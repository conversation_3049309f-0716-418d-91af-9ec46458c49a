<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>edu-service-hq-parent</artifactId>
        <groupId>cn.huanju.edu</groupId>
        <version>0.0.2</version>
    </parent>

    <groupId>cn.huanju</groupId>
    <artifactId>edu-study-client</artifactId>
    <version>1.1.23-20250609-SNAPSHOT</version>
    <name>edu-study-client</name>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
            <version>0.8.0</version>
        </dependency>
        <dependency>
            <groupId>cn.huanju.edu</groupId>
            <artifactId>edu-base</artifactId>
            <version>1.1.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.hqwx</groupId>
            <artifactId>thrift-client</artifactId>
            <version>3.0.2-RELEASE</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.11.1</version>
        </dependency>
        <dependency>
            <groupId>com.hqwx</groupId>
            <artifactId>grpc-common</artifactId>
            <version>1.6.8-RELEASE</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.huanju.edu</groupId>
            <artifactId>edu-base-model</artifactId>
            <version>1.1.1-SNAPSHOT</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.4.1.Final</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>0.6.1</version>
                <configuration>
                    <protocArtifact>com.google.protobuf:protoc:3.25.5:exe:${os.detected.classifier}</protocArtifact>
                    <pluginArtifact>io.grpc:protoc-gen-grpc-java:1.69.1:exe:${os.detected.classifier}</pluginArtifact>
                    <pluginId>grpc-java</pluginId>
                    <protoSourceRoot>src/main/java/cn/huanju/edu100/proto</protoSourceRoot>
                    <skip>false</skip>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compile-custom</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>9</source>
                    <target>9</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
            </resource>
        </resources>
    </build>
    <distributionManagement>
        <snapshotRepository>
            <id>music-public</id>
            <url>https://nexus.duowan.com/music/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>music-public</id>
            <url>https://nexus.duowan.com/music/content/repositories/releases/</url>
        </repository>
    </distributionManagement>
</project>
