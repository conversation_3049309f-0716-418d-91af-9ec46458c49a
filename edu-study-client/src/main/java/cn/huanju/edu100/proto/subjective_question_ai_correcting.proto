syntax = "proto3";

option java_multiple_files = true;
option java_package = "cn.huanju.edu100.grpc.service";
option java_outer_classname = "SubjectiveQuestionAiCorrecting";
option objc_class_prefix = "Impl";
import "head.proto";

// package grpc;
// 不需要兼容 thrift

// The greeting service definition.
service SubjectiveQuestionAiCorrectingService {


  // 获取AI批阅记录列表
  rpc getAiCorrectingLogList(GrpcRequest) returns (GrpcResponse) {}

  // 批量查询AI批阅记录列表
  rpc batchQueryAiCorrectingLogList(GrpcRequest) returns (GrpcResponse) {}

  // 触发AI批阅
  rpc triggerAiCorrecting(GrpcRequest) returns (stream GrpcResponse) {}

  // 测试流式接口
  rpc testTriggerAiCorrecting(GrpcRequest) returns (stream GrpcResponse) {}

  // 更新AI批阅记录列表
  rpc updateAiCorrectingLogList(GrpcRequest) returns (GrpcResponse) {}

  // 拍照答题AI批阅
  rpc photoAnswerAiCorrecting(GrpcRequest) returns (GrpcResponse) {}

  // 拍照答题AI批阅总评语
  rpc photoAnswerAiOverallCorrecting(GrpcRequest) returns (GrpcResponse) {}

  // 主观题AI批阅
  rpc subjectiveQuestionAiCorrecting(GrpcRequest) returns (GrpcResponse) {}

  // 删除老师批阅记录
  rpc deleteTeacherCorrecting(GrpcRequest) returns (GrpcResponse) {}

  // 根据批阅结果校准得分和小题的对错
  rpc correctSubjectiveResult(GrpcRequest) returns (GrpcResponse) {}
}
