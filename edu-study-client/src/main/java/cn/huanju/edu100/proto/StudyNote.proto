syntax = "proto3";

option java_multiple_files = true;
option java_package = "cn.huanju.edu100.grpc.service";
option java_outer_classname = "StudyNote";
option objc_class_prefix = "Impl";
import "head.proto";

// package grpc;
// 不需要兼容 thrift

// The greeting service definition.
service StudyNoteService {

  rpc getQuestionNoteList(GrpcRequest) returns (GrpcResponse) {}
  rpc getQuestionNotePage(GrpcRequest) returns (GrpcResponse) {}
  rpc getNotesByQuestionListId(GrpcRequest) returns (GrpcResponse) {}
  rpc findyQuestionNotePageByQuestionIdList(GrpcRequest) returns (GrpcResponse) {}
  rpc addQuestionNote(GrpcRequest) returns (GrpcResponse) {}
  rpc updateQuestionNote(GrpcRequest) returns (GrpcResponse) {}

  rpc getNoteQuestionList(GrpcRequest) returns (GrpcResponse) {}


  rpc getVideoNoteList(GrpcRequest) returns (GrpcResponse) {}
  rpc getVideoNotePage(GrpcRequest) returns (GrpcResponse) {}
  rpc addVideoNote(GrpcRequest) returns (GrpcResponse) {}
  rpc updateVideoNote(GrpcRequest) returns (GrpcResponse) {}

  rpc deleteNoteById(GrpcRequest) returns (GrpcResponse) {}
  rpc thumbUpNote(GrpcRequest) returns (GrpcResponse) {}
}
