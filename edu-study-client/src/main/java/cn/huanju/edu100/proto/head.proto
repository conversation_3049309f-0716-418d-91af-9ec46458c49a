syntax = "proto3";

option java_multiple_files = true;
option java_package = "cn.huanju.edu100.grpc.metadata";
option java_outer_classname = "EduHead";
option objc_class_prefix = "Impl";



// The request message containing the user's name.
message GrpcRequest {
    int64 client_ip = 1;
    int32 appid = 2;
    int32 codetype = 3;
    string msg = 4;
    int64 schId =5;
    int64 pschId =6;
    string traceId =7;
    string contextId =8;
}

// The response message containing the greetings
message GrpcResponse {
    int32 code = 1;
    string errormsg = 2;
    int32 codetype = 3;
    string msg = 4;
}
