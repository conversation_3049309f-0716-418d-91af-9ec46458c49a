syntax = "proto3";

option java_multiple_files = true;
option java_package = "cn.huanju.edu100.grpc.service";
option java_outer_classname = "Edu100Study";
option objc_class_prefix = "Impl";
import "head.proto";

//package grpc;

// The greeting service definition.
service Edu100StudyService {
  rpc grpc_ping(GrpcRequest) returns (GrpcResponse){}
  /*环球个性化学习接口定义***********************************************************************/
  rpc  sty_testInfo(GrpcRequest) returns (GrpcResponse){} 							/*测试接口	*/
  rpc  sty_getGroupsByUid(GrpcRequest) returns (GrpcResponse){} 					/*2.1.1	根据用户uid获得用户所在组信息*/
  rpc  sty_getGroupById(GrpcRequest) returns (GrpcResponse){} 					/*2.1.2	根据组id获得组信息*/
  rpc  sty_getStudyPlanListByGid(GrpcRequest) returns (GrpcResponse){} 				/*2.1.3	根据组id获得学习计划列表*/
  rpc  sty_getStudyTaskListByPidAndUid(GrpcRequest) returns (GrpcResponse){} 			/*2.1.4	根据计划id获得任务列表*/
  rpc  sty_getStudyTaskById(GrpcRequest) returns (GrpcResponse){} 					/*2.1.5	根据任务id获得任务详细信息*/
  rpc  sty_getRecordCourseListByTidList(GrpcRequest) returns (GrpcResponse){} 			/*2.1.6	根据任务idList获得录播课程列表*/
  rpc  sty_getTestTaskListByTidList(GrpcRequest) returns (GrpcResponse){} 				/*2.1.7	根据任务idList获得测试任务列表*/
  rpc  sty_getPaperStudyListByTidList(GrpcRequest) returns (GrpcResponse){} 			/*2.1.8	根据任务idList获得作业学习列表*/
  rpc  sty_getWeiCourseListByTidList(GrpcRequest) returns (GrpcResponse){} 			/*2.1.9	根据任务idList获得任务推荐资源（微课）列表*/
  rpc  sty_getStudyReportOverviewByUid(GrpcRequest) returns (GrpcResponse){} 			/*2.1.10  根据用户id获得学习报告总览*/
  rpc  sty_getStudyReportDetailByPidAndUid(GrpcRequest) returns (GrpcResponse){} 			/*2.1.11  根据计划id和用户id获得学习报告详情*/
  rpc  sty_getStudentTaskByTidAndUid(GrpcRequest) returns (GrpcResponse){}				/*2.1.15  根据任务id和用户id获得学生任务信息*/
  rpc  sty_updateProblemUserByTaskIdAndUid(GrpcRequest) returns (GrpcResponse){}			/*2.1.16  根据任务id和用户id更新问题用户*/
  rpc  sty_hasRightViewResByTidListAndUid(GrpcRequest) returns (GrpcResponse){}			/*2.1.27  根据任务idList资源id和用户id判断查看权限*/

  rpc  sty_getUserAgreementsByUid(GrpcRequest) returns (GrpcResponse){}/*2.1.12根据user_agreement的uid获得user_agreement相关信息*/
  rpc  sty_getUserAgreementInfoByIdUid(GrpcRequest) returns (GrpcResponse){}/*2.1.12根据user_agreement的uid和id获取 user_agreement详细信息*/
  rpc  sty_updateUserAgreementByUAObject(GrpcRequest) returns (GrpcResponse){}/*2.1.13根据传入的user_agreement对象更新user_agreement记录*/

  rpc  sty_getPaperAnswerById(GrpcRequest) returns (GrpcResponse){}/* 2.3.1	根据答卷概况id获取答卷概况 */
  rpc  sty_getPaperAnswerByUid(GrpcRequest) returns (GrpcResponse){}/* 2.1.8	批量获取用户做过的试卷/作业练习纪录 */
  rpc  sty_getPaperAnswerDetailByUid(GrpcRequest) returns (GrpcResponse){}/* 2.1.9	获取用户做过的试卷/作业中题目的答题详情 */
  rpc  sty_UserAnswerPaper(GrpcRequest) returns (GrpcResponse){}/* 2.1.10	用户提交试卷 */
  rpc  sty_getUserErrPaper(GrpcRequest) returns (GrpcResponse){}/* 2.1.11	获取用户的错题集 */
  rpc  sty_getUserErrAnswerQuestion(GrpcRequest) returns (GrpcResponse){}/* 2.1.12	获取用户的错题的详情 */
  rpc  sty_getUserErrQuestionList(GrpcRequest) returns (GrpcResponse){}/* 2.1.13	获取用户答题的错题列表 */
  rpc  sty_removeUserErrorQuestion(GrpcRequest) returns (GrpcResponse){}/* 云私塾2.5.0	移除用户错题接口 */
  rpc  sty_getBulletinList(GrpcRequest) returns (GrpcResponse){}/*2.1.15	获得有效网校公告列表 */
  rpc  sty_getBulletinInfoById(GrpcRequest) returns (GrpcResponse){}/*2.1.16	根据id获得网校公告详情*/
  rpc  sty_personalStudyAnswer(GrpcRequest) returns (GrpcResponse){}/*2.1.17	学习计划提交答案*/
  rpc  sty_getUserLatestLessonAnswer(GrpcRequest) returns (GrpcResponse){}/*2.1.18	获取用户讲节上的最新一次答题的详情*/
  rpc  sty_getPaperRecordPage(GrpcRequest) returns (GrpcResponse){}/*2.1.18	分页获取试卷学习记录*/

  rpc  sty_staticQuestionAnswer(GrpcRequest) returns (GrpcResponse){}/*2.1.20	根据questionId统计答题情况 */
  rpc  sty_staticPaperAnswer(GrpcRequest) returns (GrpcResponse){}/*2.1.21	根据paperId统计答卷情况*/
  rpc  sty_staticQuestionAnswerBatch(GrpcRequest) returns (GrpcResponse){}/*2.1.22	根据questionId统计答题情况*/
  rpc  sty_userAnswerHomework(GrpcRequest) returns (GrpcResponse){}/*2.1.23	用户提交作业*/
  rpc  sty_getUserHomeWorkInfo(GrpcRequest) returns (GrpcResponse){}/*2.1.24	获取用户的作业作答的情况*/
  rpc  sty_getUserHomeWorkDetail(GrpcRequest) returns (GrpcResponse){}/*2.1.25	获取用户的作业作答的详情*/
  rpc  sty_isDoHomeWorkById(GrpcRequest) returns (GrpcResponse){}/*2.1.26	批量判断用户是否做过作业*/
  rpc  sty_userAnswerErrorQuestion(GrpcRequest) returns (GrpcResponse){}/*2.1.27	错误试题作答提交*/

  rpc  sty_saveUserAgreementList(GrpcRequest) returns (GrpcResponse){}/*2.1.27	批量保存用户协议信息 */
  rpc  sty_getUserAgreementListByUidOrderId(GrpcRequest) returns (GrpcResponse){}/*2.1.28	根据uid和订单号查询用户签订协议列表 */
  rpc  sty_agreementSignFinishedByUid(GrpcRequest) returns (GrpcResponse){}/*2.1.29 查询用户的协议是否全部签约完毕 */

  rpc sty_saveUserVideoLog(GrpcRequest) returns (GrpcResponse){}/*2.1.3	保存用户最后一次观看的讲id*/
  rpc sty_queryUserVideoLogsByUidCourseId(GrpcRequest) returns (GrpcResponse){}/*2.1.4	根据学生id和课程ID返回所有的学习记录*/
  rpc sty_getLastUserVideoLogByUidCourseId(GrpcRequest) returns (GrpcResponse){}/*2.1.5根据用户uid和课程id获得最后一次观看的讲id*/
  rpc sty_queryUserVideoLogsByUidCourseIdList(GrpcRequest) returns (GrpcResponse){}/*2.1.9根据学生ID和课程IDList查询所有的学习记录*/
  rpc sty_submitTaskStateByTaskIdAndUid(GrpcRequest) returns (GrpcResponse){}/*2.1.22	根据任务id和用户id更新任务状态*/
  rpc sty_testRedis(GrpcRequest) returns (GrpcResponse){}
  rpc sty_getUnsignAgreementRelateGoodsIdListByUid(GrpcRequest) returns (GrpcResponse){}/*2.1.22	根据uid查询未签订协议的goodsIdList*/

  rpc sty_delUserAgreement(GrpcRequest) returns (GrpcResponse){}/*2.1.22	根据uid和商品ID删除协议内容*/

/*题库相关start */
  rpc sty_userCollectQuestion(GrpcRequest) returns (GrpcResponse){}/*2.2.1	用户收藏题目*/
  rpc sty_getUserQuestionCollectByBoxId(GrpcRequest) returns (GrpcResponse){}/*2.2.2	根据题库id获取用户在该题库下收藏的题目*/
  rpc sty_userIsCollectQuestion(GrpcRequest) returns (GrpcResponse){}/*2.2.3	根据题目idList判断题目是否收藏*/
  rpc sty_getUserWrongBoxQuestionInfo(GrpcRequest) returns (GrpcResponse){}/*2.2.4	获取用户做错的题目wrong*/
  rpc sty_getUserAnswerBoxQuestionInfo(GrpcRequest) returns (GrpcResponse){}/*2.2.5	获取用户做过的题目done*/
  rpc sty_getRamdonBoxQuestionList(GrpcRequest) returns (GrpcResponse){}/*2.2.8	根据策略随机抽取X道题目形成练习，并返回题目详情*/
  rpc sty_getUserBoxExerciseList(GrpcRequest) returns (GrpcResponse){}/*2.2.9	获取用户生成的练习List*/
  rpc sty_getUserIsDoExerciseById(GrpcRequest) returns (GrpcResponse){}/*2.2.11	根据练习id获取用户的练习状态*/
  rpc sty_getUserAnswerHis(GrpcRequest) returns (GrpcResponse){}/*2.2.12	获取用户某道题目的答题情况（答题次数，正确次数，最近一次作答的答案）*/
  rpc sty_getUserIsDoPaperById(GrpcRequest) returns (GrpcResponse){}/*2.2.7	根据paperId判断用户的答卷状态*/
  rpc sty_isAuthPaperByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.16	根据用户是否有权限做该份试卷*/
  rpc sty_getUserLastPractice(GrpcRequest) returns (GrpcResponse){}/*2.2.17	获取用户上次未完成的练习/试卷记录*/
  rpc sty_getUserBoxHomeworkById(GrpcRequest) returns (GrpcResponse){}/*2.2.18	根据homework_id获取练习详情*/

  rpc sty_userAnswerBoxExercise(GrpcRequest) returns (GrpcResponse){}/*2.2.19	用户提交题库练习*/
/*rpc sty_userAnswerBoxPaper(GrpcRequest) returns (GrpcResponse){}2.2.20	用户提交题库试卷*/
  rpc sty_userGenerateAndAnswerBoxExercise(GrpcRequest) returns (GrpcResponse){}/*	用户作答题库题目（此接口针对于：需要将生成作业与提交作业融合在一起的需求）*/
  rpc sty_getUserHomeWorkRecodById(GrpcRequest) returns (GrpcResponse){}/*根据作业记录id获取用户某份作业记录*/
  rpc sty_getRamdonBoxQuestion4Tourist(GrpcRequest) returns (GrpcResponse){}/*游客模式随机出题，只随机题目，不生成作业副本*/

  rpc sty_getRamdonBoxQuestions4Recite(GrpcRequest) returns (GrpcResponse){}/*背题模式抽取题目*/
  rpc sty_reportUserBoxReciteQIds(GrpcRequest) returns (GrpcResponse){}/*背题模式上报用户已背的题目*/
  rpc sty_userRemoveErrQuestionInBox(GrpcRequest) returns (GrpcResponse){}/*用户主动移除题库错题*/
  rpc sty_userRmErrQuestionInBoxBatchObj(GrpcRequest) returns (GrpcResponse){}/*用户从错题集中主动移除错题(批量obj_id+单个question_id)*/
  rpc sty_reportErrQuestionCorrect(GrpcRequest) returns (GrpcResponse){}/*用户上报纠错题目*/
  rpc sty_getErrQuestionCorrect(GrpcRequest) returns (GrpcResponse){}/*获取用户上报的纠错题目*/

  rpc sty_batchUserWrongBoxQuestionCnt(GrpcRequest) returns (GrpcResponse){}/*(批量)获取用户已做错的题目总数*/
  rpc sty_batchUserAnswerBoxQuestionCnt(GrpcRequest) returns (GrpcResponse){}/*(批量)获取用户已做过的题目总数*/

  rpc sty_getUserBrushQuestionInfo(GrpcRequest) returns (GrpcResponse){}/* 获取用户刷题挑战信息*/
  rpc sty_getUserBrushRankList(GrpcRequest) returns (GrpcResponse){}/* 获取某个题库的刷题挑战排行榜*/
  rpc sty_getUserToDayBrushRanking(GrpcRequest) returns (GrpcResponse){}/* 获取用户某个题库的今日刷题挑战排名*/
  rpc sty_getRamdonBoxQuestion4Brush(GrpcRequest) returns (GrpcResponse){}/* 刷题挑战模式，随机抽取X道题目形成练习，并返回题目详情，不生成作业副本*/
  rpc sty_userBrushQuestionBoxExercise(GrpcRequest) returns (GrpcResponse){}/* 用户提交题库刷题挑战（此接口用于刷题挑战，需要将生成作业和提交作业融合在一起，并且所有作答正误均为客户端判断的场景）*/
  rpc sty_getUserBrushRankListByWeek(GrpcRequest) returns (GrpcResponse){}/* 获取某个考试的指定周的刷题挑战排名*/

  rpc  sty_getMockExams(GrpcRequest) returns (GrpcResponse){}/* 获取万人模考列表 */
  rpc  sty_updateMockApply(GrpcRequest) returns (GrpcResponse){}/* 修改万人模考考生信息 */

  rpc  sty_getQualifyGrading(GrpcRequest) returns (GrpcResponse){}/* 获取排位赛段位信息 */
  rpc  sty_updateQualifyGrading(GrpcRequest) returns (GrpcResponse){}/* 修改排位赛段位信息 */
  rpc  sty_getQualifyGradingList(GrpcRequest) returns (GrpcResponse){}/* 获取排位赛段位排行榜 */
  rpc  sty_getUserLatestGrading(GrpcRequest) returns (GrpcResponse){}/* 获取用户排位赛最新段位信息 */
  rpc  sty_getUidsQualifyGradingList(GrpcRequest) returns (GrpcResponse){}/* 获取排位赛好友段位排行榜 */

  rpc  sty_getAnswerInfoLastWeek(GrpcRequest) returns (GrpcResponse){}/* 获取用户上周答题信息 */
  rpc  sty_getAnswerTrendWeek(GrpcRequest) returns (GrpcResponse){}/* 获取用户周答题趋势 */
  rpc  sty_batchWeekWrongBoxQuestionCnt(GrpcRequest) returns (GrpcResponse){}/* (批量)获取用户上周已做错的题目总数 */
  rpc  sty_batchWeekAnswerBoxQuestionCnt(GrpcRequest) returns (GrpcResponse){}/* (批量)获取用户上周已做过的题目总数 */
  rpc  sty_getWeekWrongBoxQuestionInfo(GrpcRequest) returns (GrpcResponse){}/* 获取用户上周做错的题目(wrong) */
  rpc  sty_batchWipeOutWrongBoxQuestionCnt(GrpcRequest) returns (GrpcResponse){}/* (批量)获取用户已消灭错的题目总数 */
  rpc  sty_getWipeOutWrongBoxQuestionInfo(GrpcRequest) returns (GrpcResponse){}/* 获取用户已消灭做错的题目(wipeOut) */
  rpc  sty_rmErrWipeOutQuestionInBoxBatchObj(GrpcRequest) returns (GrpcResponse){}/* 用户对题库已消灭错题进行主动移除(批量obj_ids+单个question_id) */
  rpc  sty_getHomeImageConfig(GrpcRequest) returns (GrpcResponse){}/* 获取首页背景图配置 */

  rpc  sty_getWrongBoxQuestionUncategorized(GrpcRequest) returns (GrpcResponse){}/* 获取用户未分类的错题列表 */
  rpc  sty_getWipeOutWrongBoxQuestionUncategorized(GrpcRequest) returns (GrpcResponse){}/* 获取用户未分类的已消灭错题列表 */
  rpc  sty_getWrongBoxQuestionAccordingToQType(GrpcRequest) returns (GrpcResponse){}/* 获取用户错题列表(按照题型分类) */
  rpc  sty_getWipeOutWrongBoxQuestionAccordingToQType(GrpcRequest) returns (GrpcResponse){}/* 获取用户已消灭错题列表(按照题型分类) */
  rpc  sty_resetChapterPractice(GrpcRequest) returns (GrpcResponse){}/* 章节练习进度重置 */

/*题库相关end */

  rpc  sty_getBulletinByIds(GrpcRequest) returns (GrpcResponse){}/*2.2.21	根据商品id列表获得有效网校公告列表 */
  rpc  sty_cancelBulletin(GrpcRequest) returns (GrpcResponse){}/*2.2.22	取消网校公告弹窗 */

/* 新版个性化服务接口 */
  rpc  sty_tutor_listSecondCategoryByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.23  根据uid查询云私塾的考试列表  */
  rpc  sty_tutor_listFeedBackByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.24        根据uid分页查询班主任回访记录列表 */
  rpc  sty_tutor_listTaskByUidStartEndTime(GrpcRequest) returns (GrpcResponse){}/*2.2.25  根据uid,考试id，开始时间，结束时间查询所有的任务（升序排序） */
  rpc  sty_tutor_getCategoryListByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.26  根据uid,考试id查询所有科目 */

  rpc  sty_tutor_listPhaseByUidAndCategoryList(GrpcRequest) returns (GrpcResponse){}/*2.2.27      根据uid,科目id列表查询所有的阶段 */
  rpc  sty_tutor_getTaskDetailByTaskId(GrpcRequest) returns (GrpcResponse){}/*2.2.28 获取任务详情*/
  rpc  sty_tutor_updateTaskStatusByUidTaskId(GrpcRequest) returns (GrpcResponse){}/*2.2.29     根据uid, task_id更新任务状态，学习中，学习完成*/
  rpc  sty_tutor_updateTaskStatusByUidLessonId(GrpcRequest) returns (GrpcResponse){}/*     根据uid, classes,lesson_id更新任务状态，学习中，学习完成*/
  rpc  sty_tutor_saveStudyVideoResult(GrpcRequest) returns (GrpcResponse){}/*2.2.30 用户是否已经看懂录播课视频 */

  rpc  sty_tutor_getLastTaskByUidAndCategoryIds(GrpcRequest) returns (GrpcResponse){}/*2.2.31  查询最近一次学而未完成的任务*/
  rpc  sty_tutor_getStudyProgressByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.32  学习进度领先多少学员*/
  rpc  sty_tutor_submitHomeworkAnswer(GrpcRequest) returns (GrpcResponse){}/*2.2.33  段落作业提交接口，并返回测评结果*/
  rpc  sty_tutor_submitPaperAnswer(GrpcRequest) returns (GrpcResponse){}/*2.2.34  试卷提交接口，并且返回得分*/
  rpc  sty_tutor_listKeyEventLogByUidType(GrpcRequest) returns (GrpcResponse){}/*2.2.35  错题汇总列表接口*/
  rpc  sty_tutor_listPushResByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.36  拉取知识点强化接口*/
  rpc  sty_tutor_getTeacherByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.27  班主任信息接口*/
  rpc  sty_tutor_getStudyOverviewByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.38  学习报告数据汇总接口(学习进度，题目正确率，完成任务数，看视频长度，答题数)*/
  rpc  sty_tutor_listPushResByChapterIdList(GrpcRequest) returns (GrpcResponse){}/*2.2.40  知识点学习详情接口 */
  rpc  sty_tutor_getStudentTaskByTidAndUid(GrpcRequest) returns (GrpcResponse){}/*2.2.41 根据任务id和用户id获得学生任务信息 */
  rpc  sty_tutor_listQuestionBylogIdAndUid(GrpcRequest) returns (GrpcResponse){}/*2.2.42 根据做题记录id和用户id获得题目信息 */
  rpc  sty_tutor_userAnswerErrorQuestion(GrpcRequest) returns (GrpcResponse){}/*2.2.43 重新提交错题 */
  rpc  sty_tutor_isAuthTaskByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.44	根据用户是否有权限做该任务*/
  rpc  sty_tutor_getUnitsList(GrpcRequest) returns (GrpcResponse){}/*2.3.3	查询单元信息 */
  rpc  sty_tutor_getStudentTaskList(GrpcRequest) returns (GrpcResponse){}/*2.3.4	查询学生任务结果*/
  rpc  sty_tutor_queryWkChapter(GrpcRequest) returns (GrpcResponse){}/*2.4.1	查询微课班章节信息*/
  rpc  sty_tutor_queryWkChapterStudy(GrpcRequest) returns (GrpcResponse){}/*2.4.2	查询微课班章节学习状态*/
  rpc  sty_tutor_getLastTask(GrpcRequest) returns (GrpcResponse){}/*2.4.4 根据单元获取最后学习的任务*/
  rpc  sty_tutor_getWkLastTask(GrpcRequest) returns (GrpcResponse){}/*2.4.5 根据微课/节获取最后学习的任务*/
  rpc  sty_tutor_queryWkTask(GrpcRequest) returns (GrpcResponse){}/*2.4.6 根据节获取微课任务*/
  rpc  sty_tutor_queryWkTaskStudy(GrpcRequest) returns (GrpcResponse){}/*2.4.7 根据微课任务id列表批量获取完成情况*/
  rpc  sty_tutor_queryCollectWkTask(GrpcRequest) returns (GrpcResponse){}/*2.4.8 获取用户收藏微课任务*/
  rpc  sty_tutor_userCollectWkKnow(GrpcRequest) returns (GrpcResponse){}/*2.4.9 收藏/取消收藏微课知识点*/
  rpc  sty_tutor_queryUserCollectWkKnow(GrpcRequest) returns (GrpcResponse){}/*2.4.10 查询用户是否收藏微课知识点*/
  rpc  sty_tutor_queryUnitStatus(GrpcRequest) returns (GrpcResponse){}/*2.4.11 查询单元完成状态*/
  rpc  sty_tutor_queryWkByIds(GrpcRequest) returns (GrpcResponse){}/*2.4.12 根据微课id列表查询微课*/

/* 面授服务接口 */
  rpc  sty_1v1_listClassesByUid(GrpcRequest) returns (GrpcResponse){}/*2.2.45	根据用户uid获取所有的班级信息*/
  rpc  sty_1v1_listLessonsByClsIds(GrpcRequest) returns (GrpcResponse){}/*2.2.46 根据班级id列表获取对应的课节信息*/
  rpc  sty_1v1_getStudyInfoByUidAndClsIds(GrpcRequest) returns (GrpcResponse){}/*2.2.47 根据用户id和班级id列表获取对应的学习信息*/
  rpc  sty_1v1_checkAccess(GrpcRequest) returns (GrpcResponse){}/*2.2.48 学员进入课程权限判断*/
  rpc  sty_1v1_addStudentPrivileg(GrpcRequest) returns (GrpcResponse){}/*2.2.49 添加学员权限*/
  rpc  sty_1v1_deleteStudentPrivileg(GrpcRequest) returns (GrpcResponse){}/*2.2.50 删除学员权限*/
  rpc  sty_1v1_getUserRole(GrpcRequest) returns (GrpcResponse){}/*2.2.51 获取用户角色*/
  rpc  sty_1v1_listLessonsBetweenTime(GrpcRequest) returns (GrpcResponse){}/*2.2.52 根据用户和时间段获取课节信息*/
  rpc  sty_1v1_listClassesByRole(GrpcRequest) returns (GrpcResponse){}/*2.2.53 根据不同用户角色获取班级信息*/
  rpc  sty_1v1_listMaterialBwtweenTime(GrpcRequest) returns (GrpcResponse){}/*2.2.54 根据时间段获取资料信息*/
  rpc  sty_1v1_listFeedback(GrpcRequest) returns (GrpcResponse){}/*2.2.55 分页查询反馈/报告*/
  rpc  sty_1v1_listStudentByClsIds(GrpcRequest) returns (GrpcResponse){}/*2.2.56 根据班级id列表获取学员信息*/
  rpc  sty_1v1_saveTeacherFeedback(GrpcRequest) returns (GrpcResponse){}/*2.2.57 提交老师反馈信息*/
  rpc  sty_1v1_listTeacherFeedback(GrpcRequest) returns (GrpcResponse){}/*2.2.58 查询老师反馈信息*/
  rpc  sty_1v1_saveResource(GrpcRequest) returns (GrpcResponse){}/*2.2.59 上传资源*/
  rpc  sty_1v1_updateResource(GrpcRequest) returns (GrpcResponse){}/*2.2.60 更新资源*/
  rpc  sty_1v1_listResource(GrpcRequest) returns (GrpcResponse){}/*2.2.61 分页查询资源*/
  rpc  sty_1v1_saveMaterial(GrpcRequest) returns (GrpcResponse){}/*2.2.62 批量保存资料*/
  rpc  sty_1v1_setTeaFeedbackMark(GrpcRequest) returns (GrpcResponse){}/*2.2.63 标记老师反馈的填写状态 */
  rpc  sty_1v1_getClassesById(GrpcRequest) returns (GrpcResponse){}/*2.2.64 根据班级id获取班级的详细信息 */
  rpc  sty_1v1_getRelationByLessonIdList(GrpcRequest) returns (GrpcResponse){}/*2.2.65 根据课节类型和课节id列表获取直播课节对应关系 */

  rpc  sty_getBulletinsByUidAndAppKey(GrpcRequest) returns (GrpcResponse){}/*2.3.1	根据用户uid和app的key获取对应的公告列表*/
  rpc  sty_saveUserCategory(GrpcRequest) returns (GrpcResponse){}/*2.3.2	题库app合并版上报用户选择的考试 */

  rpc  sty_1v1_listLessonsByLiveIdAndTime(GrpcRequest) returns (GrpcResponse){}/*2.3.3 根据直播教室id和时间段获取课节列表 */
  rpc  sty_1v1_validateEntryByClsIdAndUid(GrpcRequest) returns (GrpcResponse){}/*2.3.4 根据教室id和uid验证用户是否有权限进入频道 */
  rpc  sty_tutor_submitComment(GrpcRequest) returns (GrpcResponse){}/*2.3.5 提交录播课/讲/题目的评价 */
  rpc  sty_tutor_submitCommentNew(GrpcRequest) returns (GrpcResponse){}/*商品社区2.6  新的提交评论接口，返回评论id*/
  rpc  sty_tutor_queryCommentByCommentId(GrpcRequest)  returns (GrpcResponse){}/*商品社区2.6  通过评论id查询评论*/
  rpc  sty_tutor_queryComment(GrpcRequest) returns (GrpcResponse){}/*2.3.6查询录播课/讲/题目的评价 */
  rpc  sty_tutor_thumbUpComment(GrpcRequest) returns (GrpcResponse){}/*2.3.7点赞录播课/讲/题目的评价 */
  rpc  sty_tutor_queryCommentByGoodsGroupId(GrpcRequest) returns (GrpcResponse){}/*2.3.6查询课程下的评价 */
  rpc  sty_tutor_queryCommentByGoodsId(GrpcRequest) returns (GrpcResponse){}/*2.3.9查询商品下的评价 */
  rpc  sty_tutor_findStarByGroup(GrpcRequest)  returns (GrpcResponse){}/*查询评分分组*/

  rpc  sty_1v1_getRelationByLiveLessonIdList(GrpcRequest) returns (GrpcResponse){}/*2.3.7 根据课节类型和直播课节id列表获取课节对应关系 */
  rpc  sty_tutor_queryCommentCount(GrpcRequest) returns (GrpcResponse){}/*2.3.7查询课程/讲的评价数 */
  rpc  sty_tutor_queryCommentByUid(GrpcRequest) returns (GrpcResponse){}/*2.3.8根据uid，查询录播课/讲/题目的评价 */
  rpc  sty_tutor_queryCommentCountByGoodsId(GrpcRequest) returns (GrpcResponse){}/*2.3.9根据goodsId，查询商品的评价数 */

  rpc  sty_tutor_updateComment(GrpcRequest) returns (GrpcResponse){}/*2.3.9更新题目评价 */
  rpc  sty_tutor_getCommentNum(GrpcRequest) returns (GrpcResponse){}/*2.3.10获取用户题目评价消息数量 */

  rpc  sty_tutor_queryCommentPageByTeacherId(GrpcRequest) returns (GrpcResponse){}/*2.3.9 webv2.4.0根据老师id获取评论分页 */

  rpc  sty_solution_getQuestionList(GrpcRequest) returns (GrpcResponse){}/*2.3.11 获取用户答疑提问列表 */
  rpc  sty_solution_getRecommendQuestionList(GrpcRequest) returns (GrpcResponse){}/*2.3.11 获取热门推荐答疑列表 */
  rpc  sty_solution_getUserCollectQuestionList(GrpcRequest) returns (GrpcResponse){}/*2.3.11 用户收藏问题列表 */
  rpc  sty_solution_userCollectQuestion(GrpcRequest) returns (GrpcResponse){}/*2.3.11 收藏问题 */
  rpc  sty_solution_userCancelCollectQuestion(GrpcRequest) returns (GrpcResponse){}/*2.3.11 取消收藏问题 */
  rpc  sty_solution_getQuestionInfoById(GrpcRequest) returns (GrpcResponse){}/*2.3.11 通过问题id获取问题详情 */
  rpc  sty_solution_getQuestionListByIds(GrpcRequest) returns (GrpcResponse){}/*2.3.11 通过问题id串获取问题列表 */
  rpc  sty_solution_userAddQuestion(GrpcRequest) returns (GrpcResponse){}/*2.3.11 提问 或 追问 */
  rpc  sty_solution_userLikeAnswerToQuestion(GrpcRequest) returns (GrpcResponse){}/*2.3.11 对问题的答案点赞 */
  rpc  sty_solution_userCancelLikeAnswerToQuestion(GrpcRequest) returns (GrpcResponse){}/*2.3.11 取消对问题的答案点赞 */
  rpc  sty_solution_userAdoptAnswerToQuestion(GrpcRequest) returns (GrpcResponse){}/*2.3.11 采纳问题的答案 */
  rpc  sty_solution_isReadAnswerToQuestion(GrpcRequest) returns (GrpcResponse){}/*2.3.11 是否有未读回复 */


  rpc  sty_tutor_getStudyReportByUidAndCategoryId(GrpcRequest) returns (GrpcResponse){}/*2.4.0 云私塾学习报告,根据uid,云私塾班,科目id获取各个阶段的学习报告 */
  rpc  sty_tutor_queryCommentListByIds(GrpcRequest) returns (GrpcResponse){}/*APP5.0 根据评论ID查询评论内容*/
  rpc  sty_solution_submitComplaint(GrpcRequest) returns (GrpcResponse){}/*2.4.0 提交对答疑问题的投诉 */
  rpc  sty_solution_setQuestionRecommendTop(GrpcRequest) returns (GrpcResponse){}/*2.4.0 置顶热门推荐问题 */
  rpc  sty_solution_getManageRecommendQuestionList(GrpcRequest) returns (GrpcResponse){}/*2.4.0 获取管理后台的热门推荐答疑列表 */
  rpc  sty_solution_updateRecommendQuestionList(GrpcRequest) returns (GrpcResponse){}/*2.4.0 更新热门推荐答疑列表 */

  rpc  sty_getUserSubmitHomeWorkQuestionCount(GrpcRequest) returns (GrpcResponse){}/*user answer 相关迁移到study服务*/
  rpc  sty_getUserFinishedPaperCount(GrpcRequest) returns (GrpcResponse){}/*user answer 相关迁移到study服务*/

  rpc  sty_getUserHomeWorkAnswersForGoods(GrpcRequest) returns (GrpcResponse){}/*user answer 查询用户作业情况,汇总到每一讲*/
  rpc  sty_getUserAnswersForGoods(GrpcRequest) returns (GrpcResponse){}/*user answer 查询用户完成试卷,汇总到每一个试卷*/
  rpc  sty_getUserStudyLengthStatistics(GrpcRequest) returns (GrpcResponse){}/*获取用户学习时长统计*/

  rpc  sty_getStudyAvgCountList(GrpcRequest) returns (GrpcResponse){} 			/*admin answer 相关迁移到study服务*/
  rpc  sty_findListByStudyReportQuery(GrpcRequest) returns (GrpcResponse){} 			/*admin answer 相关迁移到study服务*/
  rpc  sty_getUserAnswerCompletionList(GrpcRequest) returns (GrpcResponse){}			/*学员试卷完成情况统计*/
  rpc  sty_getHomeworkCompletionList(GrpcRequest) returns (GrpcResponse){}		/*学员作业完成情况统计*/
  rpc  sty_getAnswerDetailList(GrpcRequest) returns (GrpcResponse){}		/*查询学员作答每题详情列表*/
  rpc  sty_adminstudyfindList(GrpcRequest) returns (GrpcResponse){} 			/*admin answer 相关迁移到study服务*/
  rpc  sty_findStudyDetailsList(GrpcRequest) returns (GrpcResponse){} 			/*admin 学员试卷具体完成情况*/
  rpc  sty_findHomeworkDetailsList(GrpcRequest) returns (GrpcResponse){} 			/*admin 学员作业具体完成情况*/
  rpc  sty_getHomeworkDetailsList(GrpcRequest) returns (GrpcResponse){} 			/*区别于上一个，这个的detail里面数据*/
  rpc  sty_adminstudygetQuestionListByAnswerId(GrpcRequest) returns (GrpcResponse){}			/*根据answerId获取题目信息*/
  rpc  sty_adminstudygetQuestionListByHomeworkAnswerId(GrpcRequest) returns (GrpcResponse){}			/*根据homeworkAnswerId获取题目信息*/
  rpc  sty_adminstudyfindAnswerSumQuestion(GrpcRequest) returns (GrpcResponse){} 			/*admin answer 相关迁移到study服务*/

  rpc  sty_1v1_getClassByProductId(GrpcRequest) returns (GrpcResponse){} /*留学app1.8 根据产品获取班次id*/
  rpc  sty_getUserCollectQuestionForAL(GrpcRequest) returns (GrpcResponse){} /*智能化学习 根据用户ID获取收藏的题目*/

  rpc sty_getUserHomeWorkCount(GrpcRequest) returns (GrpcResponse){} /* 查询作业数量*/
  rpc sty_getUserQuestionForAL(GrpcRequest) returns (GrpcResponse){} /*智能化学习 获取用户做过的题目*/

  rpc  sty_tutor_queryCommentBySecondCategory(GrpcRequest) returns (GrpcResponse){}/* 根据考试获取评论分页 */

  rpc  sty_getUserAnswerHistoryPage(GrpcRequest) returns (GrpcResponse){}/* 分页获取用户的学习记录 */

  rpc  sty_getUserAnswerDetailByIdAndUid(GrpcRequest) returns (GrpcResponse){}/* 根据答题id和用户id查询答题情况 */

  rpc  sty_getUserAnswerListByUidAndDate(GrpcRequest) returns (GrpcResponse){}/* 根据用户id时间科目查询用户答题情况 */

  rpc  sty_getUserDaysCalculate(GrpcRequest) returns (GrpcResponse){}/* 根据用户id和客户端日期获取用户登陆题库的天数统计 */
  rpc  sty_getUserQuestionCalculate(GrpcRequest) returns (GrpcResponse){}/* 根据用户id和考试id获取用户今日做题数量和累计做题数量 */
  rpc  sty_getUserLastChapterExercise(GrpcRequest) returns (GrpcResponse){}/* 根据用户id和boxId获取用户最近一次章节练习数据*/
  rpc  sty_saveUserLastChapterExercise(GrpcRequest) returns (GrpcResponse){}/* 保存用户最近一次章节练习数据*/

  rpc  sty_saveTikuForecastScore(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 保存用户预测分数据*/
  rpc  sty_getTikuForecastScore(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 获取用户预测分*/
  rpc  sty_saveTikuPracticeDuration(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 保存用户练习时长*/
  rpc  sty_getTikuPracticeDuration(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 获取用户练习时长*/
  rpc  sty_batchThisWeekDoneQuestionCnt(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 获取用户当前周做过的知识点对应的题目数据*/
  rpc  sty_batchThisWeekWrongQuestionCnt(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 获取用户当前周已做错的知识点对应的题目数据 */

  rpc  sty_getMockExamCover(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 获取万人模考封面信息 */
  rpc  sty_getMockExamByMockId(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据模考id获取模考活动 */
  rpc  sty_getMockAreaIdList(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据考试id获取有模考活动的地区id列表 */
  rpc  sty_getMockSubjectListByMockId(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据模考id获取考试科目列表 */
  rpc  sty_getMockApplyListByMockIdAndUid(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据模考活动id、uid获取用户报名信息 */
  rpc  sty_getMockRankingList(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据模考活动id获取各科目排行（前n名用户） */
  rpc  sty_getMyMockList(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据考试id、uid获取我的模考活动带分页*/
  rpc  sty_getMockApplyById(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据id获取模考报名记录*/
  rpc  sty_getLerningAdviceByScore(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据模考id、模考科目id、分数获取学习建议*/
  rpc  sty_getPaperAnswerRightAndWrong(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据答卷概况id、uid获取答卷题目正确错误数 */
  rpc  sty_getMockApplyCount(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据mockId、模考科目id获取报名人数 */
  rpc  sty_getMockApplySubmitCount(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据mockId、模考科目id获取交卷人数 */
  rpc  sty_getMockSubjectById(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 根据id获取模考科目 */
  rpc  sty_getMockUseTimeRank(GrpcRequest) returns (GrpcResponse){}/* tiku4.9 获取模考作答试卷的时间排名 */

  rpc  sty_getPaperStudyLengthByPaperId(GrpcRequest) returns (GrpcResponse){}/* 云私塾2.0 根据试卷id获取试卷的答题时长 */
  rpc  sty_getUserAnswerStudyReportByGoodsId(GrpcRequest) returns (GrpcResponse){}/* 根据uid商品id获取学员商品下试卷作业的答题报告 */

  rpc  sty_assembleEnterSchoolTestPaper(GrpcRequest) returns (GrpcResponse){}/* 题库5.0 生成入学测评试卷并出题 */
  rpc  sty_enterSchoolTestReport(GrpcRequest) returns (GrpcResponse){}/* 题库5.0 生成入学测评报告 */
  rpc  sty_checkEnterSchoolTestFlag(GrpcRequest) returns (GrpcResponse){}/* 题库5.0 检测入学测评是否已做完 */
  rpc  sty_getSaleManActivity(GrpcRequest) returns (GrpcResponse){}/*  */

  rpc  sty_getUserAnswerSumFindAllList(GrpcRequest) returns (GrpcResponse){}/* 根据uid获取学员全部作答记录 */
  rpc  sty_getUserPaperCompleteDistinct(GrpcRequest) returns (GrpcResponse){}/* 根据uid和paperIds获取学员试卷完成率,试卷中答题（非空）数量>=80%且提交则判定为完成，其余情况为未完成 */
  rpc  sty_getUserHomeworkCompleteDistinct(GrpcRequest) returns (GrpcResponse){}/* 根据uid和questionIds获取学员课后作业题目完成情况,用户选择答案后提交的非空题目（客观题选择选项，主观题输入文本）才算完成，提交时若课后作业答案为空则判定为未完成；按照单独题目数统计 */
  rpc  sty_tutor_mySecondCategoryComment(GrpcRequest) returns (GrpcResponse){}/*题库5.0 查询我的笔记，按照考试意向进行查询，降序，返回是否有点赞 */
  rpc  sty_tutor_thumbCancelComment(GrpcRequest) returns (GrpcResponse){}/*题库5.0 取消点赞 */
  rpc  sty_tutor_deleteComment(GrpcRequest) returns (GrpcResponse){}/*题库5.0 删除笔记 */
  rpc  sty_saveUserQuestionLog(GrpcRequest) returns (GrpcResponse){}/*题库5.0 保存用户问题解析反馈 */
  rpc  sty_getUserQuestionLog(GrpcRequest) returns (GrpcResponse){}/*题库5.0 查询用户问题解析反馈 */
  rpc  sty_getDailyPractice(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取每日一练题目 */
  rpc  sty_getUserQuestionCollectByUid(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取最新收藏列表 */
  rpc  sty_getCollectTreeByUidCategory(GrpcRequest) returns (GrpcResponse){}/*题库5.0 根据课程分类获取收藏树状结构 */
  rpc  sty_getNewRandomBoxQuestion4Brush(GrpcRequest) returns (GrpcResponse){}/*题库5.0 新的随机刷题抽题接口*/
  rpc  sty_getNewRandomBoxQuestionList(GrpcRequest) returns (GrpcResponse){}/*题库5.0 新的随机抽题接口*/
  rpc  sty_syncUserQuestionLog(GrpcRequest) returns (GrpcResponse){}/*题库5.0做题本记录迁移接口*/
  rpc  sty_buildChapterHomeWork(GrpcRequest) returns (GrpcResponse){}/*题库5.0 生成章节练习作业*/
  rpc  sty_batchUserAnswerQuestionCnt(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取章节知识点已做数量*/
  rpc  sty_batchUserWrongQuestionCnt(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取章节知识点错题数量*/
  rpc  sty_batchNewWipeOutWrongBoxQuestionCnt(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取已解决错题的数量*/
  rpc  sty_userQuestionLogIsSync(GrpcRequest) returns (GrpcResponse){}/*题库5.0 用户是否已经迁移做题本记录*/
  rpc  sty_getFilterQuestionNum(GrpcRequest) returns (GrpcResponse){}/*题库5.0 章节练习选题页面获取题目数量*/
  rpc  sty_rmErrWipeOutQuestionInBoxBatchObjNew(GrpcRequest) returns (GrpcResponse){}/*题库5.0 用户对题库已消灭错题进行主动移除*/
  rpc  sty_getNewWrongBoxQuestionAccordingToQType(GrpcRequest) returns (GrpcResponse){}/*题库5.0 错题列表，按题型归类*/
  rpc  sty_getNewWipeOutWrongBoxQuestionAccordingToQType(GrpcRequest) returns (GrpcResponse){}/*题库5.0 已消灭错题列表，按题型归类*/
  rpc  sty_getNewWipeOutWrongBoxQuestionInfo(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取用户已消灭做错的题目*/
  rpc  sty_getUserExerciseStatisticByCategory(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取首页章节统计*/
  rpc  sty_findUserThumbUpList(GrpcRequest) returns (GrpcResponse){}/*题库5.0 查询是否点赞评论*/
  rpc  sty_getNewUserWrongBoxQuestionInfo(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取用户错题*/
  rpc  sty_userRemoveErrQuestionInBoxNew(GrpcRequest) returns (GrpcResponse){}/*题库5.0 错题本删除错题*/
  rpc  sty_userRmErrQuestionInBoxBatchObjNew(GrpcRequest) returns (GrpcResponse){}/*题库5.0 错题本批量删除错题*/
  rpc  sty_getMockApplyByUserAnswerId(GrpcRequest) returns (GrpcResponse){}/*题库5.0 根据uid，answoerId获取模考id*/
  rpc  sty_getUserQuestionCount(GrpcRequest) returns (GrpcResponse){}/*题库5.0 获取中用户做题相关数量*/
  rpc  sty_resetNewChapterPractice(GrpcRequest) returns (GrpcResponse){}/*题库5.0 重置章节做题*/

  rpc  sty_openAutoRemoveErrorQuestion(GrpcRequest) returns (GrpcResponse){}/* 云私塾2.5.0，开启/关闭 错题自动移除 */
  rpc  sty_getIsAutoRemoveOpened(GrpcRequest) returns (GrpcResponse){}/* 云私塾2.5.0，获取 自动移除是否开启 */
  rpc  sty_getUserAnswerDetailByQuestions(GrpcRequest) returns (GrpcResponse){}/* 云私塾2.5.0，获取 根据题目获取最近一次得做题详情 */
  rpc  sty_getLastUserAnswerSumByQuestionIds(GrpcRequest) returns (GrpcResponse){}/* 云私塾2.5.0， 根据题目获取最近一次得做题记录 */
  rpc  sty_getUserAnswerMedicalList(GrpcRequest) returns (GrpcResponse){}/*学习中心2.2-医卫 获取用户答题记录（医卫类科目的全真机考的模考的电子试卷）*/
  rpc  sty_getUidCountByEPaperId(GrpcRequest) returns (GrpcResponse){}/*学习中心2.2-医卫 根据电子试卷id和试卷id获取答卷用户数*/
  rpc  sty_getUserAnswerMedicalListByIds(GrpcRequest) returns (GrpcResponse){}/*学习中心2.2-医卫 根据获取id集合获取对象(用户答题记录)集合*/
  rpc  sty_updateSleepStudyState(GrpcRequest) returns (GrpcResponse){}/*修改沉睡用户状态*/
  rpc  sty_geOtherWrongBoxQuestion(GrpcRequest) returns (GrpcResponse){}/*题库5.1 获取未分类的错题*/

  rpc sty_getUserHomeworkLastAnswerInfo(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取用户最近一次课后作业答题情况*/
  rpc sty_getUserPaperAnswerLastInfo(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取用户最近一次试卷答题情况*/
  rpc sty_getUserDoneRecordVoList(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取用户做题记录(分页)*/
  rpc sty_getUserDoneRecordObjTypeList(GrpcRequest) returns (GrpcResponse){}
  rpc sty_updateUserDoneRecord(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 更新用户做题记录*/
  rpc sty_getUserErrorProdInfo(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取学习中心错题集中产品相关信息*/
  rpc sty_getUserErrorHomeworkLessonsInfo(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取学习中心错题集中课后作业错题相关信息*/
  rpc sty_getUserHomeWorkAnswerById(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 根据id获取课后作业*/
  rpc sty_getUserErrorPaperAnswerInfo(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取学习中心错题集中模考或试卷的错题相关信息*/
  rpc sty_getUserErrorQuestionCountByCategory(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取用户某个科目下的错题数量*/
  rpc sty_delErrorSubQuestionByCategory(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 手动清除用户某科目下错题*/
  rpc sty_delCorrectQuestionByCategory(GrpcRequest) returns (GrpcResponse){}/*手动清除用户某科目下已纠错题*/
  rpc sty_studyCenterOpenAutoRemoveErrorQuestion(GrpcRequest) returns (GrpcResponse){}/* 学习中心2.3，开启/关闭 错题自动移除 */
  rpc sty_studyCenterGetIsAutoRemoveOpened(GrpcRequest) returns (GrpcResponse){}/* 学习中心2.3，获取 自动移除是否开启 */
  rpc sty_getStudyCenterUserCollectHomeworkProdInfoList(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 用户答题课后作业收藏问题列表 */
  rpc sty_getStudyCenterUserCollectHomeworkLessonsInfo(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取学习中心收藏中课后作业错题相关信息*/
  rpc sty_getStudyCenterUserCollectPaperAnswerInfo(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取学习中心收藏夹中模考或试卷的错题相关信息*/
  rpc sty_getStudyCenterUserCollectQuestionCountByCategory(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取用户某个科目下的收藏题数量*/
  rpc sty_studyCenterUserCollectQuestion(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 收藏问题 */
  rpc sty_studyCenterUserCancelCollectQuestion(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 取消收藏问题 */
  rpc sty_cancelUserCollectItems(GrpcRequest) returns (GrpcResponse){}/* 取消收藏题目 */
  rpc sty_getStudyCenterUserCollectStateByQuestionIds(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 获取题目中收藏题目状态*/
  rpc sty_getUserErrorQuestionCountByQtype(GrpcRequest) returns (GrpcResponse){}/* 学习中心2.3，根据查询条件获取错题集的题型分类的统计信息 */
  rpc sty_getUserCenterCollectQuestionCountByQtype(GrpcRequest) returns (GrpcResponse){}/* 学习中心2.3，根据查询条件获取收藏夹的题型分类的统计信息 */
  rpc sty_getUserCollectQuestionCountByQtype(GrpcRequest) returns (GrpcResponse){}/* 根据查询条件获取收藏夹的题型分类的统计信息 */

  rpc sty_syncUserSubErrorQuestionList(GrpcRequest) returns (GrpcResponse){}/* 学习中心2.3，同步错误题目列表 */
  rpc sty_removeErrorSubQuestionByQuestionId(GrpcRequest) returns (GrpcResponse){}/*学习中心2.3 手动清除用户的错题*/

  rpc  sty_getUserHomeWorkAnswerByParam(GrpcRequest) returns (GrpcResponse){}/*学习中心2.6-学习报告 根据查询条件获取作业集合*/
  rpc  sty_getUserAnswerByParam(GrpcRequest) returns (GrpcResponse){}/*根据查询条件获取试卷作答集合*/

  rpc sty_1v1_getVLessonRelationByParam(GrpcRequest) returns (GrpcResponse){}/* 查询环球直播课节和绿色直播课节关系表*/

  rpc sty_getUserCenterCollectQuestionIdList(GrpcRequest) returns (GrpcResponse){}/*学习中心3.1-web学习中心改版 根据查询条件获取收藏夹题目集合*/
  rpc sty_getUserCenterErrorQuestionIdList(GrpcRequest) returns (GrpcResponse){}/* 学习中心3.1-web学习中心改版，根据查询条件获取错题集的题目集合 */
  rpc sty_getUserCollectQuestionIdListByCategoryId(GrpcRequest) returns (GrpcResponse){}/*根据科目id和其他条件查询收藏题目id*/

  rpc sty_getUserHomeWorkUseTime(GrpcRequest) returns (GrpcResponse){} /*题库获取用户学习时间*/
  rpc sty_getPaperAnswerDetailsList(GrpcRequest) returns (GrpcResponse){} /*根据uid和试卷产品ids获取的detail里面数据*/

  rpc sty_syncHomeworkVideoCourse(GrpcRequest) returns (GrpcResponse){}/*作业系统业务版本，同步产品排课的录播产品的课后作业信息*/
  rpc sty_syncHomeworkProductSchedule(GrpcRequest) returns (GrpcResponse){}/*作业系统业务版本，同步新课程表排课的录播课节的课后作业信息*/
  rpc sty_syncHomeworkProductAdaptiveLearning(GrpcRequest) returns (GrpcResponse){}/*作业系统业务版本，同步云私塾任务的课后作业信息*/
  rpc sty_syncHomeworkIdVideoCourse(GrpcRequest) returns (GrpcResponse){}/*作业系统业务版本，补充产品排课的录播产品的课后作业关联的user_answer_homework表的homework_id信息*/
  rpc sty_syncHomeworkIdProductSchedule(GrpcRequest) returns (GrpcResponse){}/*作业系统业务版本，补充新课程表排课的录播的课后作业关联的user_answer_homework表的homework_id信息*/
  rpc sty_syncHomeworkIdProductAdaptiveLearning(GrpcRequest) returns (GrpcResponse){}/*作业系统业务版本，补充云私塾任务的课后作业关联的user_answer_homework表的homework_id信息*/

  rpc sty_getPaperAnswerCountByUid(GrpcRequest) returns (GrpcResponse){}/* 获取用户试卷作答次数 */


  rpc sty_submitHomeworkComment(GrpcRequest) returns (GrpcResponse){}/* 提交作业批改 */

  rpc sty_queryHomeworkComment(GrpcRequest) returns (GrpcResponse){}/* 查询作业批改 */

  rpc sty_getUserHomeworkTaskPage(GrpcRequest) returns (GrpcResponse){}/* 分页获取学员作业任务列表 */

  rpc sty_getUserHomeworkTaskList(GrpcRequest) returns (GrpcResponse){}/* 获取学员作业任务列表 */

  rpc sty_getHomeworkStatusByList(GrpcRequest) returns (GrpcResponse){}/* 获取作业作答和批改状态 */

  rpc sty_checkChapterQuestionCountByUid(GrpcRequest) returns (GrpcResponse){}/* 判断用户章节练习做题数量 */

  rpc sty_repairWrongQuestionByUserHomeworkId(GrpcRequest) returns (GrpcResponse){}/* 根据用户章节记录重入错题本 */

  rpc sty_getRightAndWrongRandomBoxQuestionList(GrpcRequest) returns (GrpcResponse){}/* 根据用户章节记录重入错题本 */

  rpc sty_getUserHomeworkCompleteQustionIds(GrpcRequest) returns (GrpcResponse){}/* 获取学员已完成的课后作业的题目ID集合去重 */

  rpc sty_getUserHomeWorkRecodsByIdList(GrpcRequest) returns (GrpcResponse){}/*根据作业记录id列表获取用户作业记录列表*/

  rpc sty_getUserHomeWorkDetailList(GrpcRequest) returns (GrpcResponse){}/*获取用户的作业作答的详情列表*/

  rpc sty_getHomeworkAnswerCount(GrpcRequest) returns (GrpcResponse){}/* 查询课节作业所做次数 */

  rpc sty_delHomeWorkRecord(GrpcRequest) returns (GrpcResponse){}/* 删除用户的作业作答 */

  rpc  sty_getUserHomeWorkAnswerByHomeworkId(GrpcRequest) returns (GrpcResponse){}/*2.1.24	根据作业id获取用户的作业作答的情况*/

  rpc sty_syncLessonIdUserAnswerMedical(GrpcRequest) returns (GrpcResponse){}/*产品配置优化业务版本，根据电子试卷产品信息补user_answer_medical表的lesson_id信息*/

  rpc sty_findUserHomeWorkAnswerInfosGroupByHomeworkId(GrpcRequest) returns (GrpcResponse){}/*通过课后作业ID查询是否作答过*/

  rpc sty_transferCollectQuestionId(GrpcRequest) returns (GrpcResponse){}/* 迁移商品的收藏的题目数据 */


  rpc sty_reservePaper(GrpcRequest) returns (GrpcResponse){}/*考试预约*/

  rpc sty_getUserDoneQuestionMap(GrpcRequest) returns (GrpcResponse){}/*查询用户已做题目id*/
  rpc sty_findAlSubmitQuestionList(GrpcRequest) returns (GrpcResponse){}/* 查找已作答的题目列表（适用课后作业和试卷） */

  rpc sty_findAlSubmitQuestionListByPaperAndHomework(GrpcRequest) returns (GrpcResponse){}/* 查找已作答的题目列表（适用课后作业和试卷） */

  rpc sty_getUserSubErrorQuestionList(GrpcRequest) returns (GrpcResponse){}/* 获取错题信息集合 */
  rpc sty_isHaveNewRecordForAnswerQuestion(GrpcRequest) returns (GrpcResponse){}/* 查询是否有答疑的回复记录 */


  rpc sty_tutor_queryReplyByCommentId(GrpcRequest) returns (GrpcResponse){}/*查询评论详情以及跟评*/
  rpc sty_getMaxStateUserHomeworkTaskList(GrpcRequest) returns (GrpcResponse){} /* 获取自定义作业的最新状态值 */
  rpc sty_getCountSubmitCustomHomework(GrpcRequest) returns (GrpcResponse){} /* 获取自定义作业的提交人数 */
  rpc sty_getUserCustomHomeworkCommentList(GrpcRequest) returns (GrpcResponse){} /* 获取自定义作业的老师评价列表 */
  rpc sty_getQuestionTopicStatisticList(GrpcRequest) returns (GrpcResponse){} /* 获取题目的统计信息 */
  rpc sty_queryReplyCommentByReplyId(GrpcRequest) returns (GrpcResponse){} /* 查询评论详情以及跟评v2 */

  rpc sty_saveUserConfig(GrpcRequest) returns (GrpcResponse){} /* 保存用户配置 */

  rpc sty_getUserConfigByConfigKey(GrpcRequest) returns (GrpcResponse){} /* 根据configKey获取用户配置 */

  rpc sty_getSolutionAnswerByQid(GrpcRequest) returns (GrpcResponse){} /* 获取solution_answer */

  rpc sty_getSolutionQuestionBriefById(GrpcRequest) returns (GrpcResponse){} /* 获取solution_question(不带answer) */

  rpc sty_isShowEvaluationEntryAndResult(GrpcRequest) returns (GrpcResponse){} /* 判断是否显示入学测评入口、测评结果、是否弹窗*/
  rpc sty_getEvaluationReport(GrpcRequest) returns (GrpcResponse){} /* 入学测评报告*/
  rpc sty_submitEvaluationQuestion(GrpcRequest) returns (GrpcResponse){} /*保存入学测评的作答信息*/
  rpc sty_getEvaluationUserAnswerList(GrpcRequest) returns (GrpcResponse){} /*获取入学测评的作答信息*/
  rpc sty_getEvaluationUserAnswerByAnswerId(GrpcRequest) returns (GrpcResponse){} /*根据作答id 获取入学测评的作答信息*/

  rpc sty_streamAnswerComplete(GrpcRequest) returns (GrpcResponse){} /*gpt流式回复完成*/

  rpc sty_deleteSolutionAnswerById(GrpcRequest) returns (GrpcResponse){} /*根据id删除*/

  rpc sty_getChapterQuestionNoToken(GrpcRequest) returns (GrpcResponse){} /*未登录状态抽取章节练习题目*/

  rpc sty_findUserAnswerErrorQuestionInfoList(GrpcRequest) returns (GrpcResponse){} /*获取学员作答错误的题目情况*/

  rpc sty_getSubjectiveQuestionAiCorrectingLog(GrpcRequest) returns (GrpcResponse){} /*获取主观题AI批阅结果*/

  rpc sty_addSubjectiveQuestionAiCorrectingLog(GrpcRequest) returns (GrpcResponse){} /*保存主观题AI批阅结果*/

  rpc sty_updateSubjectiveQuestionAiCorrectingLog(GrpcRequest) returns (GrpcResponse){} /*更新主观题AI批阅结果*/

  rpc sty_addSolutionQuestion(GrpcRequest) returns (GrpcResponse){} /*插入solutionQuestion*/

  rpc sty_addSolutionAnswer(GrpcRequest) returns (GrpcResponse){} /*插入solutionAnswer*/

  rpc sty_solutionQuestionToManual(GrpcRequest) returns (GrpcResponse){} /*solutionQuestion转人工*/

  rpc sty_getSolutionQuestionByMessageId(GrpcRequest) returns (GrpcResponse){} /*根据messageId获取solutionQuestion*/

  rpc sty_getPaperCorrectionSum(GrpcRequest) returns (GrpcResponse){} /*查找论文批改统计*/

  rpc sty_createAiCorrectionRecord(GrpcRequest) returns (GrpcResponse){} /*提交论文批改*/

  rpc sty_findCorrectionRecordList(GrpcRequest) returns (GrpcResponse){} /*查找论文批改记录*/
  rpc sty_findLastReadOveredSubjectivePaperAnswers(GrpcRequest) returns (GrpcResponse){} /*获取学员含有主观题且已批阅最新试卷作答记录*/

  rpc sty_findLastReadOveredSubjectiveHomeworkAnswers(GrpcRequest) returns (GrpcResponse){} /*获取学员含有主观题且已批阅最新作业作答记录*/

  rpc sty_getUserAnswersByPreClassExercise(GrpcRequest) returns (GrpcResponse){} /*获取课前做题试卷答题情况*/

  rpc sty_getUserAnswersByPaperIdsAndObjType(GrpcRequest) returns (GrpcResponse){}

  rpc sty_findUserHomeWorkAnswersByUserHomeworkIds(GrpcRequest) returns (GrpcResponse){}

  rpc sty_getRecommendedQuestionIdListByErrorQuestionId(GrpcRequest) returns (GrpcResponse){} /*根据错题id查询推荐题目idsList*/

  rpc sty_getSolutionQuestionCount(GrpcRequest) returns (GrpcResponse){}/*获取用户答疑个数 */

  rpc sty_getUserErrorAndCorrectQuestionCount(GrpcRequest) returns (GrpcResponse){}/*错题和纠错题数量*/

  rpc sty_getUserCorrectQuestion(GrpcRequest) returns (GrpcResponse){}/*纠错题*/

  rpc sty_getUserCorrectQuestionCount(GrpcRequest) returns (GrpcResponse){}/*纠错题*/

  rpc sty_getUserSubErrorQuestion(GrpcRequest) returns (GrpcResponse){}/*错题*/

  rpc sty_getUserAnswerDetailList(GrpcRequest) returns (GrpcResponse){} /*根据uid,answerid,questionid获取List-主观题批阅使用*/
}
