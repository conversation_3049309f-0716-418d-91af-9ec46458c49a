syntax = "proto3";

option java_multiple_files = true;
option java_package = "cn.huanju.edu100.grpc.service";
option java_outer_classname = "FissionActivity";
option objc_class_prefix = "Impl";
import "head.proto";

// 裂变活动服务接口
service FissionActivityService {

  // 获取裂变活动列表
  rpc getActivityList(GrpcRequest) returns (GrpcResponse) {}

  // 获取推送首页的活动
  rpc getPushedActivity(GrpcRequest) returns (GrpcResponse) {}

  // 获取单个活动配置信息
  rpc getActivitySetting(GrpcRequest) returns (GrpcResponse) {}

  // 获取某个活动的所有配置信息
  rpc getActivityAllSettings(GrpcRequest) returns (GrpcResponse) {}

  // 获取某种配置的所有活动配置信息
  rpc getAllActivitySettings(GrpcRequest) returns (GrpcResponse) {}

  // 设置活动配置
  rpc setActivitySetting(GrpcRequest) returns (GrpcResponse) {}

  // 获取活动参加人数
  rpc getActivityJoinNum(GrpcRequest) returns (GrpcResponse) {}
}
