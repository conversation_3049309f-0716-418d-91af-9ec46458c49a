package cn.huanju.edu100.study.client.impl.studynote;

import cn.huanju.edu100.grpc.client.GrpcClient;
import cn.huanju.edu100.grpc.utils.JsonParser;
import cn.huanju.edu100.study.service.studynote.StudyNoteServiceBase;
import com.fasterxml.jackson.core.JacksonException;
import com.hqwx.study.dto.Page;
import com.hqwx.study.entity.studynote.Query;
import com.hqwx.study.entity.studynote.StudyNoteBaseInfo;
import com.hqwx.study.entity.studynote.ThumbUpInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/21 19:16
 * @description 学习笔记服务客户端接口模板实现类
 */
public abstract class StudyNoteServiceImpl<T extends StudyNoteBaseInfo, Q extends Query.QueryParam>
        implements StudyNoteServiceBase<T, Q> {

    protected final GrpcClient grpcClient;
    private final String noteTypeName;

    public StudyNoteServiceImpl(GrpcClient grpcClient, String noteTypeName){
        this.grpcClient = grpcClient;
        this.noteTypeName = noteTypeName;
    }

    @Override
    public List<T> getNoteList(Q queryParam, Class<T> clazz) {
        GrpcClient.Request request = new GrpcClient.Request("get" + noteTypeName + "NoteList", queryParam);
        request.setSchId(queryParam.getSchId())
                .setPSchId(queryParam.getPSchId());
        return grpcClient.sendRequestForList(request, clazz);
    }

    @Override
    public Page<T> getNotePage(Q queryParam, Integer start, Integer size, Class<T> clazz) {
        Map<String, Object> params = Map.of("param", queryParam, "start", start, "size", size);
        GrpcClient.Request request = new GrpcClient.Request("get" + noteTypeName + "NotePage", params);
        request.setSchId(queryParam.getSchId())
                .setPSchId(queryParam.getPSchId());
        return grpcClient.sendRequest(request, new GrpcClient.ResponseParser<Page<T>>() {
            @Override
            public Page<T> parse(String s) throws JacksonException {
                JsonParser parser = grpcClient.getJsonParser();
                return parsePage(parser, s);
            }
        });
    }

    /**
     * 由于 java 模板的限制，只能由知道具体类开的派生类来解析
     * @param parser json解析器
     * @param s json字符串
     * @return 笔记分页数据
     * @throws JacksonException json解析异常
     */
    protected abstract Page<T> parsePage(JsonParser parser, String s) throws JacksonException;

    private <T extends StudyNoteBaseInfo> GrpcClient.Request createRequest(String methodName, T note) {
        GrpcClient.Request request = new GrpcClient.Request(methodName, note);
        request.setSchId(note.getSchId())
                .setPSchId(note.getPSchId());
        return  request;
    }

    @Override
    public Long addNote(T note) {
        return Long.valueOf(grpcClient.sendRequestForString(
                createRequest("add" + noteTypeName + "Note", note)));
    }

    @Override
    public Integer updateNote(T note) {
        return Integer.valueOf(grpcClient.sendRequestForString(
                createRequest("update" + noteTypeName + "Note", note)));
    }

    @Override
    public Boolean deleteNoteById(StudyNoteBaseInfo note) {
        return Boolean.valueOf(grpcClient.sendRequestForString(
                createRequest("deleteNoteById", note)));
    }

    @Override
    public Integer thumbUp(ThumbUpInfo thumbUpInfo) {
        GrpcClient.Request request = new GrpcClient.Request("thumbUpNote", thumbUpInfo);
        return Integer.valueOf(grpcClient.sendRequestForString(request));
    }
}
