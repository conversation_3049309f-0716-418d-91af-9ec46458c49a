package cn.huanju.edu100.study.client.impl;

import cn.huanju.edu100.grpc.client.GrpcClient;
import cn.huanju.edu100.study.service.ErrorQuestionService;
import com.hqwx.study.dto.UserAutoRemoveErrorQuestionConfigDTO;
import com.hqwx.study.dto.UserCategoryHistoryErrorQuestionInfo;
import com.hqwx.study.dto.UserErrorQuestionToPdfTaskDTO;
import com.hqwx.study.dto.command.UserErrorQuestionToPdfTaskCommand;
import com.hqwx.study.dto.command.UserHistoryErrorQuestionMoveCmd;
import com.hqwx.study.dto.query.UserCategoryHistoryErrorQuestionQuery;
import com.hqwx.study.dto.query.UserErrorQuestionToPdfTaskQuery;
import com.hqwx.study.dto.query.UserGoodsHistoryErrorQuestionQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/20 16:56
 * @description
 */
public class ErrorQuestionServiceClientImpl implements ErrorQuestionService {

    private final GrpcClient grpcClient;

    public ErrorQuestionServiceClientImpl(GrpcClient grpcClient) {
        this.grpcClient = grpcClient;
    }

    @Override
    public List<UserCategoryHistoryErrorQuestionInfo> haveHistoryErrorQuestion(UserGoodsHistoryErrorQuestionQuery query) {
        return grpcClient.sendRequestForList("haveHistoryErrorQuestion",query, UserCategoryHistoryErrorQuestionInfo.class);
    }

    @Override
    public Boolean moveHistoryErrorQuestion(UserHistoryErrorQuestionMoveCmd cmd) {
        return grpcClient.sendRequestForEntity("moveHistoryErrorQuestion",cmd, Boolean.class);
    }

    @Override
    public List<Long> getHistoryErrorQuestion(UserCategoryHistoryErrorQuestionQuery query) {
        return grpcClient.sendRequestForList("getHistoryErrorQuestion", query, Long.class);
    }

    @Override
    public UserErrorQuestionToPdfTaskDTO createUserErrorQuestionToPdfTask(UserErrorQuestionToPdfTaskCommand cmd) {
        return grpcClient.sendRequestForEntity("createUserErrorQuestionToPdfTask",cmd, UserErrorQuestionToPdfTaskDTO.class);
    }

    @Override
    public UserErrorQuestionToPdfTaskDTO getUserErrorQuestionToPdfTaskResult(UserErrorQuestionToPdfTaskQuery query) {
        return grpcClient.sendRequestForEntity("getUserErrorQuestionToPdfTaskResult",query, UserErrorQuestionToPdfTaskDTO.class);
    }

    @Override
    public UserAutoRemoveErrorQuestionConfigDTO getUserAutoRemoveErrorQuestionConfig(Long uid) {
        return grpcClient.sendRequestForEntity("getUserAutoRemoveErrorQuestionConfig",uid, UserAutoRemoveErrorQuestionConfigDTO.class);
    }
}
