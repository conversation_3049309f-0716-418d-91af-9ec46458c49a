package cn.huanju.edu100.study.client;

import com.hqwx.study.dto.Page;
import com.hqwx.study.entity.wxapp.*;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/20 11:11
 * @description 裂变活动服务接口
 */
public interface FissionActivityService {
    /**
     * 获取裂变活动列表
     * @param query 查询参数
     * @return 裂变活动列表
     */
    Page<FissionActivity> getActivityList(FissionActivityQuery query);

    /**
     * 获取推荐到首页的活动
     * @param secondCategoryId 考试id
     * @return 活动信息
     */
    FissionActivity getPushedActivity(@NonNull Long secondCategoryId);

    /**
     * 获取单个活动配置信息
     * @param activityId 活动id
     * @param activityType 活动类型，参考 {@link FissionActivity.ActivityType}
     * @param settingType 配置类型，参考 {@link FissionActivitySetting.Type}
     * @return 活动配置信息
     */
    FissionActivitySetting getActivitySetting(@NonNull Long activityId, @NonNull Integer activityType, @NonNull Integer settingType);

    /**
     * 获取某个活动的所有配置信息
     * @param activityId 活动id
     * @param activityType 活动类型，参考 {@link FissionActivity.ActivityType}
     * @return 活动配置信息列表
     */
    List<FissionActivitySetting> getActivityAllSettings(@NonNull Long activityId, @NonNull Long activityType);

    /**
     * 获取某种配置的所有活动配置信息
     * @param settingType 配置类型，参考 {@link FissionActivitySetting.Type}
     * @return 活动配置信息列表
     */
    List<FissionActivitySetting> getAllActivitySettings(@NonNull Integer settingType);

    /**
     * 设置活动配置
     * @param setting 活动配置信息
     * @return 是否更新成功
     */
    Boolean setActivitySetting(@NonNull FissionActivitySetting setting);

    /**
     * 获取活动参与人数
     * @param activityId 活动id
     * @param activityType 活动类型，参考 {@link FissionActivity.ActivityType}
     * @return 参与人数
     */
    Integer getActivityJoinNum(@NonNull Long activityId, @NonNull Integer activityType);

}
