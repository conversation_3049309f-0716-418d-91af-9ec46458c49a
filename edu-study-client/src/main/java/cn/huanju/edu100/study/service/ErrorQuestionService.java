package cn.huanju.edu100.study.service;

import com.hqwx.study.dto.UserAutoRemoveErrorQuestionConfigDTO;
import com.hqwx.study.dto.UserCategoryHistoryErrorQuestionInfo;
import com.hqwx.study.dto.UserErrorQuestionToPdfTaskDTO;
import com.hqwx.study.dto.command.UserErrorQuestionToPdfTaskCommand;
import com.hqwx.study.dto.command.UserHistoryErrorQuestionMoveCmd;
import com.hqwx.study.dto.query.UserCategoryHistoryErrorQuestionQuery;
import com.hqwx.study.dto.query.UserErrorQuestionToPdfTaskQuery;
import com.hqwx.study.dto.query.UserGoodsHistoryErrorQuestionQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/20 16:53
 * @description 给其他服务提供访问Study服务的接口
 */
public interface ErrorQuestionService {
    // 查询是否有历史错题
    List<UserCategoryHistoryErrorQuestionInfo> haveHistoryErrorQuestion(UserGoodsHistoryErrorQuestionQuery query);
    // 迁移历史错题
    Boolean moveHistoryErrorQuestion(UserHistoryErrorQuestionMoveCmd cmd);

    //查询历史错题
    List<Long> getHistoryErrorQuestion(UserCategoryHistoryErrorQuestionQuery query);

    // 创建用户错题pdf生成任务
    UserErrorQuestionToPdfTaskDTO createUserErrorQuestionToPdfTask(UserErrorQuestionToPdfTaskCommand cmd);
    // 查询用户错题pdf生成任务的状态
    UserErrorQuestionToPdfTaskDTO getUserErrorQuestionToPdfTaskResult(UserErrorQuestionToPdfTaskQuery query);

    // 获取用户自动移除错题配置
    UserAutoRemoveErrorQuestionConfigDTO getUserAutoRemoveErrorQuestionConfig(Long uid);
}
