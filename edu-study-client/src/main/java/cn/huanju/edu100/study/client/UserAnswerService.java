package cn.huanju.edu100.study.client;

import com.hqwx.study.dto.UserAnswerComment;
import com.hqwx.study.entity.UserAnswer;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 17:33
 * @description
 */
public interface UserAnswerService {
    /**
     * 更新用户作答记录
     * @param userAnswer 用户作答记录
     * @return 是否更新成功
     */
    Boolean updateUserAnswer(UserAnswer userAnswer);

    /**
     * 获取用户作答记录批阅信息列表
     * @param uid 用户id
     * @param userAnswerId 用户作答记录id
     * @return 用户作答记录批阅信息列表
     */
    List<UserAnswerComment> getUserAnswerCommentList(Long uid, Long userAnswerId);

    /**
     * 获取用户作答记录批阅信息列表
     */
    List<UserAnswerComment> getUserAnswerCommentListByUserAnswerIds(List<Long> userAnswerIds);

    /**
     * 获取试卷用户作答记录批阅信息列表
     * @param paperId 试卷id
     * @return 用户作答记录批阅信息列表
     */
    List<UserAnswerComment> getPaperUserAnswerCommentList(Long paperId);

    /**
     * 保存用户作答记录批阅信息列表
     * @param userAnswerCommentList 用户作答记录批阅信息列表
     * @return 是否保存成功
     */
    Boolean saveUserAnswerCommentList(List<UserAnswerComment> userAnswerCommentList);

    /**
     * 获取用户作答记录
     * @param uid 用户id
     * @param userAnswerId 用户作答记录id
     * @return 用户作答记录
     */
    UserAnswer getUserAnswer(Long uid, Long userAnswerId);

    /**
     * 获取用户作答记录，可选择是否计算批阅分数
     * @param uid 用户id
     * @param userAnswerId 用户作答记录id
     * @param withSubjectiveQuestionCorrectScore 是否计算批阅分数
     * @return 用户作答记录
     */
    UserAnswer getUserAnswer(Long uid, Long userAnswerId, boolean withSubjectiveQuestionCorrectScore);
}
