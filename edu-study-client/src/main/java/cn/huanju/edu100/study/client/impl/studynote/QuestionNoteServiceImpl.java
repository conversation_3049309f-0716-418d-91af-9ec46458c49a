package cn.huanju.edu100.study.client.impl.studynote;

import cn.huanju.edu100.grpc.client.GrpcClient;
import cn.huanju.edu100.grpc.utils.JsonParser;
import cn.huanju.edu100.study.service.studynote.QuestionNoteService;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.hqwx.study.dto.Page;
import com.hqwx.study.dto.query.UserNoteQuestionQuery;
import com.hqwx.study.entity.studynote.Query;
import com.hqwx.study.entity.studynote.QuestionNote;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/21 19:10
 * @description 问题笔记服务客户端接口实现类
 */
@Slf4j
public class QuestionNoteServiceImpl extends StudyNoteServiceImpl<QuestionNote, Query.QuestionQueryParam> implements QuestionNoteService {

    public QuestionNoteServiceImpl(GrpcClient grpcClient) {
        super(grpcClient, "Question");
    }

    @Override
    public Map<Long, List<QuestionNote>> getNotesByQuestionListId(List<Long> questionIdList, Long uid, Integer includeReply) {
        Map<String, Object> params = Map.of("questionIdList", questionIdList, "uid", uid, "includeReply", includeReply);
        Map<String, Object> response = grpcClient.sendRequestForMap("getNotesByQuestionListId", params);

        var parser = grpcClient.getJsonParser();

        Map<Long, List<QuestionNote>> result = new HashMap<>(response.size());
        response.forEach((k, v) -> {
            if (v instanceof List) {
                List<?> list = (List<LinkedHashMap>) v;
                List<QuestionNote> noteList = list.stream()
                        .map(o -> {
                            try {
                                return parser.parseEntity(parser.toJson(o), QuestionNote.class);
                            } catch (JsonProcessingException e) {
                                return null;
                            }
                        })
                        .collect(Collectors.toList());
                result.put(Long.parseLong(k), noteList);
            }
        });
        return result;
    }

    @Override
    public Map<Long, Page<QuestionNote>> findyQuestionNotePageByQuestionIdList(List<Long> questionIdList, Long uid, Integer includeReply, Integer includeOthers, Integer from, Integer rows) {
        Map<String, Object> params = Map.of("questionIdList", questionIdList, "uid", uid, "includeReply", includeReply, "includeOthers", includeOthers, "from", from, "rows", rows);
        Map<String, Object> response = grpcClient.sendRequestForMap("findyQuestionNotePageByQuestionIdList", params);

        var parser = grpcClient.getJsonParser();
        Map<Long, Page<QuestionNote>> result = new HashMap<>(response.size());

        response.forEach((k, v) -> {
            Map<String, Object> pageData = (Map<String, Object>) v;
            List<QuestionNote> records = (List<QuestionNote>) pageData.get("records");
            Long total = pageData.get("total") == null ? 0L :Long.parseLong(pageData.get("total").toString());
            Long size = pageData.get("size") == null ? 0L :Long.parseLong(pageData.get("size").toString());
            Long current = pageData.get("current") == null ? 0L :Long.parseLong(pageData.get("current").toString());
            Page<QuestionNote> page = new Page<>(records, total, size, current);
            result.put(Long.parseLong(k), page);
        });
        return result;
    }

    @Override
    protected Page<QuestionNote> parsePage(JsonParser parser, String s) throws JacksonException {
        return parser.getObjectMapper().readValue(s, new TypeReference<Page<QuestionNote> >(){});
    }


    @Override
    public List<Long> getNoteQuestionList(UserNoteQuestionQuery query) {
        return grpcClient.sendRequestForList("getNoteQuestionList",query, Long.class);
    }
}
