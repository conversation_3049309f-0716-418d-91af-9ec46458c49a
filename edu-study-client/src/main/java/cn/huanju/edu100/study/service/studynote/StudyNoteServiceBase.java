package cn.huanju.edu100.study.service.studynote;

import com.hqwx.study.dto.Page;
import com.hqwx.study.entity.studynote.Query;
import com.hqwx.study.entity.studynote.StudyNoteBaseInfo;
import com.hqwx.study.entity.studynote.ThumbUpInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/20 18:38
 * @description 学习笔记公共模板接口
 */
public interface StudyNoteServiceBase<T extends StudyNoteBaseInfo, Q extends Query.QueryParam> {

    /**
     * 查询参数
     */


    /**
     * 查询笔记列表
     * @param queryParam 查询参数
     * @param clazz 笔记类型
     * @return 笔记列表
     */
    List<T> getNoteList(Q queryParam, Class<T> clazz);

    /**
     * 分页查询笔记列表
     * @param queryParam 查询参数
     * @param start 开始位置
     * @param size 查询数量
     * @param clazz 笔记类型
     * @return 笔记分页数据
     */
    Page<T> getNotePage(Q queryParam, Integer start, Integer size, Class<T> clazz);

    /**
     * 添加笔记
     * @param note 笔记参数
     * @return 笔记id
     */
    Long addNote(T note);

    /**
     * 更新笔记
     * @param note 笔记参数
     * @return 更新的笔记数
     */
    Integer updateNote(T note);

    /**
     * 删除笔记
     * @param note 笔记参数
     * @return 删除是否成功
     */
    Boolean deleteNoteById(StudyNoteBaseInfo note);

    /**
     * 点赞
     * @param note 笔记参数
     * @return 点赞数
     */
    Integer thumbUp(ThumbUpInfo thumbUpInfo);

}
