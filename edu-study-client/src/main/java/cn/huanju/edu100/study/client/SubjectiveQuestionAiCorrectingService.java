package cn.huanju.edu100.study.client;

import com.hqwx.study.dto.SubjectiveQuestionAICorrectingLogDTO;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/7 15:28
 * @description
 */
public interface SubjectiveQuestionAiCorrectingService {
    int isPaperNeedAiCorrecting(UserAnswer userAnswer);
    int isHomeworkNeedAiCorrecting(UserHomeWorkAnswer userAnswer);

    void correctSubjectiveResult(Long uid, Long userAnswerId, Integer questionSource);

    @Getter
    public static enum ErrorCode {
        /**
         * 错误码
         */
        ERROR_NO_QUESTION_INFO(1001, "找不到题目信息"),
        ERROR_QUESTION_CONTAIN_IMAGE(1002, "题目中包含图片"),
        ERROR_ANSWER_CONTAIN_IMAGE(1002, "答案中包含图片"),
        ERROR_NO_PROMPT_TEMPLATE(1003, "科目未开启Ai批阅服务"),
        ERROR_QUESTION_SOURCE_UNSUPPORTED(1004, "题目来源暂不支持"),
        ERROR_USER_ANSWER_NOT_FOUND(1005, "未找到用户作答记录"),
        ERROR_AI_CORRECTING(1006, "批阅中"),
        ;

        private final Integer code;
        private final String message;

        private ErrorCode(Integer code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    public static interface UpdateCommand {
        /**
         * 设置AI批阅记录的质量并设置为已应用
         *  {@link SubjectiveQuestionAICorrectingLogDTO#getId()} 不能为空，
         *  {@link SubjectiveQuestionAICorrectingLogDTO#getScore()}
         *  和 {@link SubjectiveQuestionAICorrectingLogDTO#getRemarks()} ()} 可选设置
         *  其他字段不用设置
         */
        final static Integer APPLY = 1;
        /**
         * 保存AI批阅记录应用草稿状态
         * {@link SubjectiveQuestionAICorrectingLogDTO#getId()} 不能为空，
         * @link SubjectiveQuestionAICorrectingLogDTO#getPendingAnswer()} 选填
         * 其他字段不用设置
         */
        final static Integer SAVE_PENDING = 2;
    }

    @Data
    @Accessors(chain = true)
    public static class StreamData {
        int code;
        Object data;
    }

    public interface StreamObserver<T> {
        void onNext(T value);
        void onError(Throwable t);
        void onCompleted();
    }

    //ai自动批阅的话，需要同步数据到userAnswerComment
    void syncPaperAiCorrectingResultToComment(UserAnswer userAnswer);

    /**
     * 获取AI批阅记录列表
     * @param uid 用户ID
     * @param userAnswerId 作答ID
     * @param questionId 题目ID
     * @param topicId 专题ID
     * @param questionSource 题目来源，参考{@link SubjectiveQuestionAICorrectingLogDTO.QuestionSource}
     * @return AI批阅记录列表
     */
    List<SubjectiveQuestionAICorrectingLogDTO> getAiCorrectingLogList(
            @NonNull Long uid, @NonNull Long userAnswerId, Long questionId, Long topicId, @NonNull Integer questionSource);

    /**
     * 批量查询AI批阅记录
     * @param userAnswerIdList 作答ID列表，不需要查询分表，userAnswerId可确定个用户的作答记录
     * @param questionSource 题目来源，参考{@link SubjectiveQuestionAICorrectingLogDTO.QuestionSource}
     * @return AI批阅记录列表
     */
    List<SubjectiveQuestionAICorrectingLogDTO> batchQueryAiCorrectingLogList(@NonNull List<Long> userAnswerIdList, @NonNull Integer questionSource);

    /**
     * 触发AI批阅
     *
     * @param uid            用户ID
     * @param userAnswerId   作答ID
     * @param questionId     题目ID
     * @param topicId        子题ID
     * @param questionSource 题目来源，参考{@link SubjectiveQuestionAICorrectingLogDTO.QuestionSource}
     * @param streamObserver 观察者
     */
    void triggerAiCorrecting(@NonNull Long uid, @NonNull Long userAnswerId, @NonNull Long categoryId,
                             @NonNull Long questionId, @NonNull Long topicId, @NonNull Integer questionSource,
                             StreamObserver<StreamData> streamObserver);

    /**
     * 测试更新AI批阅记录的流式接口
     * @param uid 用户ID
     * @param userAnswerId 作答ID
     * @param questionId 题目ID
     * @param topicId 专题ID
     * @param questionSource 题目来源，参考{@link SubjectiveQuestionAICorrectingLogDTO.QuestionSource}
     * @param streamObserver 观察者
     */
    void testTriggerAiCorrecting(@NonNull Long uid, @NonNull Long userAnswerId, @NonNull Long questionId, @NonNull Long topicId,
                                 @NonNull Integer questionSource, StreamObserver<StreamData> streamObserver);

    /**
     * 拍照答题AI批阅
     * @param uid 用户ID
     * @param sessionId 会话ID
     * @param params 参数
     * @param content 图片内容
     * @return 批阅结果
     */
    void photoAnswerAiCorrecting(@NonNull Long uid, @NonNull String sessionId, @NonNull Map<String, Object> params, @NonNull List<Map<String, Object>> content);

    /**
     * 主观题AI批阅
     * @param uid 用户ID
     * @param params 参数
     * @return 批阅结果
     */
    void subjectiveQuestionAiCorrecting(@NonNull Long uid, @NonNull Map<String, Object> params);

    public boolean isSubjectiveQuestion(int qtype);
}
