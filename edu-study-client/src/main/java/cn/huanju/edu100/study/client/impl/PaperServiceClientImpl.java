package cn.huanju.edu100.study.client.impl;

import cn.huanju.edu100.grpc.client.GrpcClient;
import cn.huanju.edu100.study.service.PaperService;
import com.hqwx.study.dto.PaperSubmitCompareInfo;
import com.hqwx.study.dto.UserAnswerSumDTO;
import com.hqwx.study.dto.query.UserPaperAnswerQuery;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/20 16:56
 * @description
 */
public class PaperServiceClientImpl implements PaperService {

    private final GrpcClient grpcClient;

    public PaperServiceClientImpl(GrpcClient grpcClient) {
        this.grpcClient = grpcClient;
    }

    @Override
    public PaperSubmitCompareInfo getPaperSubmitCompareInfo(Long paperId, Double score, Double totalScore, Double paperAccuracy) {
        PaperSubmitCompareInfo paperSubmitCompareInfo = new PaperSubmitCompareInfo();
        paperSubmitCompareInfo.setPaperId(paperId);
        paperSubmitCompareInfo.setScore(score);
        paperSubmitCompareInfo.setTotalScore(totalScore);
        paperSubmitCompareInfo.setPaperAccuracy(paperAccuracy);
        GrpcClient.Request request = new GrpcClient.Request("styGetPaperSubmitCompareInfo", paperSubmitCompareInfo);
        return grpcClient.sendRequestForEntity(request, PaperSubmitCompareInfo.class);
    }

    @Override
    public List<UserAnswerSumDTO> getPaperAnswerSummaryList(Long uid, List<Long> paperIdList) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("uid", uid);
        params.put("paperIdList", paperIdList);
        return grpcClient.sendRequestForList("styGetPaperAnswerSummaryList", params, UserAnswerSumDTO.class);
    }

    @Override
    public List<UserAnswerSumDTO> getPaperLastAnswerSummary(Long uid, List<Long> paperIdList, Integer objType, Integer needDetail) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("uid", uid);
        params.put("paperIdList", paperIdList);
        params.put("objType", objType);
        params.put("needDetail", needDetail);
        return grpcClient.sendRequestForList("styGetPaperLastAnswerSummary", params, UserAnswerSumDTO.class);
    }

    @Override
    public List<UserAnswerSumDTO> getPaperLastAnswerSummaryNew(UserPaperAnswerQuery query) {
        return  grpcClient.sendRequestForList("styGetPaperLastAnswerSummaryNew", query, UserAnswerSumDTO.class);
    }
}
