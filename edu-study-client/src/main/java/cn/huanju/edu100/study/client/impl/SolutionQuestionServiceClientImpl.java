package cn.huanju.edu100.study.client.impl;

import cn.huanju.edu100.grpc.client.GrpcClient;
import cn.huanju.edu100.study.service.SolutionQuestionService;
import com.hqwx.study.dto.SolutionQuestionQuery;

public class SolutionQuestionServiceClientImpl implements SolutionQuestionService {

    private final GrpcClient grpcClient;

    public SolutionQuestionServiceClientImpl(GrpcClient grpcClient) {
        this.grpcClient = grpcClient;
    }

    @Override
    public Long styGetSolutionQuestionCount(SolutionQuestionQuery query) {
        return grpcClient.sendRequestForEntity("styGetSolutionQuestionCount",query, Long.class);
    }
}
