package cn.huanju.edu100.study.client.impl;

import cn.huanju.edu100.grpc.client.GrpcClient;
import cn.huanju.edu100.study.client.impl.studynote.QuestionNoteServiceImpl;
import cn.huanju.edu100.study.client.impl.studynote.VideoNoteServiceImpl;
import cn.huanju.edu100.study.service.ErrorQuestionService;
import cn.huanju.edu100.study.service.PaperService;
import cn.huanju.edu100.study.client.StudyClient;
import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.study.service.studynote.QuestionNoteService;
import cn.huanju.edu100.study.service.studynote.VideoNoteService;
import io.grpc.stub.AbstractBlockingStub;
import io.grpc.stub.AbstractStub;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/2 11:23
 * @description StudyClient的实现类，如果需要支持多种技术方案的rpc，可通过增加构造函数的方式来实现。
 * 例如如果后期需要同时支持grpc和 feign，却增加同时支持feign和grpc配置的构造函数即可。
 * 不修改历史构造函数，以保证兼容性。
 */
@Slf4j
public class StudyClientImpl implements StudyClient {

    private final GrpcClient grpcClient;

    private final Consumer<GrpcRequest.Builder> builderInitializer;

    /**
     * 只使用 grpc 接口的构造函数
     * @param stub grpc stub
     * @param builderInitializer grpc request builder 初始化器
     */
    public StudyClientImpl(AbstractBlockingStub stub, Consumer<GrpcRequest.Builder> builderInitializer) {
        this.builderInitializer = builderInitializer;
        grpcClient = new GrpcClient(stub, builderInitializer);
    }

    @Override
    public String ping() {
        GrpcClient.Request request = new GrpcClient.Request("ping", "StudyGrpcClient");
        return grpcClient.sendRequestForString(request);
    }

    private void checkGrpcClientReady() {
        if(grpcClient == null) {
            throw new RuntimeException("Not support grpc client");
        }
    }

    @Override
    public PaperService createPaperService() {
        checkGrpcClientReady();
        return new PaperServiceClientImpl(grpcClient);
    }

    @Override
    public QuestionNoteService createQuestionNoteService(AbstractStub<?> stub) {
        GrpcClient client = new GrpcClient(stub, builderInitializer);
        return new QuestionNoteServiceImpl(client);
    }

    @Override
    public VideoNoteService createVideoNoteService(AbstractStub<?> stub) {
        GrpcClient client = new GrpcClient(stub, builderInitializer);
        return new VideoNoteServiceImpl(client);
    }

    @Override
    public ErrorQuestionService createErrorQuestionService() {
        checkGrpcClientReady();
        return new ErrorQuestionServiceClientImpl(grpcClient);
    }
}
