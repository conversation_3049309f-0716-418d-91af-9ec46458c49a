package com.hqwx.thrift.client.api;

import cn.huanju.edu100.thrift.edu100_study;
import com.hqwx.study.dto.*;
import com.hqwx.study.dto.command.SolutionAnswerAddCommand;
import com.hqwx.study.dto.command.SolutionQuestionAddCommand;
import com.hqwx.study.dto.command.SolutionQuestionToManualCmd;
import com.hqwx.study.dto.query.*;
import com.hqwx.study.dto.query.sleepUser.SleepUserPushStateQuery;
import com.hqwx.study.dto.query.wxapp.UserAnswerDetailQuestionQuery;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetailDto;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import com.hqwx.study.vo.UserAnswerErrorQuestionVo;
import com.hqwx.thrift.client.annotation.ThriftClient;
import com.hqwx.thrift.client.annotation.ThriftMethod;
import com.hqwx.thrift.client.base.ThriftRequest;
import com.hqwx.thrift.client.base.ThriftResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-03-13
 * @description study服务接口
 */
@ThriftClient(iface = edu100_study.class,
        serviceId = "edu-study",
        grpcServiceId = "edu-study-grpc")
public interface HqwxStudyThriftClient {
    /**
     * 获取用户答疑提问列表
     * @param request 查询参数
     * @return
     */
    @ThriftMethod(methodName = "sty_solution_getQuestionList")
    ThriftResponse<PageModel<SolutionQuestionDTO>> getSolutionQuestionList(ThriftRequest<SolutionQuestionQuery> request);

    /**
     * 获取热门推荐答疑列表
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_solution_getRecommendQuestionList")
    ThriftResponse<PageModel<SolutionQuestionDTO>> getRecommendSolutionQuestionList(ThriftRequest<SolutionQuestionQuery> request);

    /**
     * 用户收藏问题列表
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_solution_getUserCollectQuestionList")
    ThriftResponse<PageModel<SolutionQuestionDTO>> getUserCollectionSolutionQuestionList(ThriftRequest<SolutionQuestionQuery> request);

    /**
     * 通过问题id串获取问题列表
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_solution_getQuestionListByIds")
    ThriftResponse getSolutionQuestionListByIds(ThriftRequest<List<Long>> request);

    /**
     * 根据uid商品id获取学员商品下试卷作业的答题报告
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_getUserAnswerStudyReportByGoodsId")
    ThriftResponse<UserAnswerStudyReportDTO> getUserAnswerStudyReportByGoodsId(ThriftRequest<UserAnswerStudyReportQuery> request);

    /**
     * tiku5.0 入学测评 出题
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_assembleEnterSchoolTestPaper")
    ThriftResponse assembleEnterSchoolTestPaper(ThriftRequest<EnterSchoolTestREQ> request);

    /**
     * tiku5.0 入学测评 报告
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_enterSchoolTestReport")
    ThriftResponse<EnterSchoolTestReportDTO> enterSchoolTestReport(ThriftRequest<EnterSchoolTestREQ> request);

    /**
     * tiku5.0 入学测评 是否已完成测评
     * 响应值 Y/N
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_checkEnterSchoolTestFlag")
    ThriftResponse<List<Long>> checkEnterSchoolTestFlag(ThriftRequest<EnterSchoolTestREQ> request);

    /**
     * 根据uid商品id获取学员商品下试卷作业的答题报告
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_getUserAnswerSumFindAllList")
    ThriftResponse getUserAnswerSumFindAllList(ThriftRequest<UserAnswerSumDTO> request);

    /**
     * 根据uid商品id获取学员商品下试卷作业的答题报告
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_getPaperRecordPage")
    ThriftResponse  sty_getPaperRecordPage(ThriftRequest<PaperStudyReportQuery> request);

    /**
     * 云私塾移除错题接口
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_removeUserErrorQuestion")
    ThriftResponse removeUserErrorQuestion(ThriftRequest<UserErrorQuestionDTO> request);

    /**
     * 打开/关闭 自动移除错题
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_openAutoRemoveErrorQuestion")
    ThriftResponse openAtuoRemove(ThriftRequest<OpenAutoRemoveQuery> request);

    /**
     * 打开/关闭 自动移除错题
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_getIsAutoRemoveOpened")
    ThriftResponse isAutoRemoveOpened(ThriftRequest<BaseAlQuery> request);


    /**
     * 题库5.0 获取每日一练题目
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_getDailyPractice")
    ThriftResponse  sty_getDailyPractice(ThriftRequest<DailyPracticeQuery> request);

    @ThriftMethod(methodName = "sty_getNewRandomBoxQuestion4Brush")
    ThriftResponse  sty_getNewRandomBoxQuestion4Brush(ThriftRequest<QuestionQuery> request);

    /**
     * 题库5.0同步用户做题本数据接口
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_syncUserQuestionLog")
    ThriftResponse  sty_syncUserQuestionLog(ThriftRequest<UserQuestionQuery> request);

    /**
     * 题库5.0检查是否需要同步用户做题本数据的接口
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_userQuestionLogIsSync")
    ThriftResponse  sty_userQuestionLogIsSync(ThriftRequest<UserQuestionQuery> request);

    /**
     * 获取作答详情列表
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_getAnswerDetailList")
    ThriftResponse sty_getAnswerDetailList(ThriftRequest<UserAnswerDetailDTO> request);

    /**
     * 获取作答详情列表
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_getUserAnswerDetailByQuestions")
    ThriftResponse sty_getUserAnswerDetailByQuestions(ThriftRequest<UserAnswerDetailQuery> request);

    /**
     * 根据题目id集合获取学员最近的题目作答记录
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_getLastUserAnswerSumByQuestionIds")
    ThriftResponse sty_getLastUserAnswerSumByQuestionIds(ThriftRequest<UserAnswerSumQuery> request);

    /**
     * 修改沉睡用户状态
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_updateSleepStudyState")
    ThriftResponse sty_updateSleepStudyState(ThriftRequest<SleepUserPushStateQuery> request);

    /**
     * 作业系统业务版本，同步产品排课的录播产品的课后作业信息
     */
    @ThriftMethod(methodName = "sty_syncHomeworkVideoCourse")
    ThriftResponse sty_syncHomeworkVideoCourse(ThriftRequest<SyncHomeworkDTO> request);

    /**
     * 作业系统业务版本，同步新课程表排课的录播课节的课后作业信息
     */
    @ThriftMethod(methodName = "sty_syncHomeworkProductSchedule")
    ThriftResponse sty_syncHomeworkProductSchedule(ThriftRequest<SyncHomeworkDTO> request);

    /**
     * 作业系统业务版本，同步云私塾任务的课后作业信息
     */
    @ThriftMethod(methodName = "sty_syncHomeworkProductAdaptiveLearning")
    ThriftResponse sty_syncHomeworkProductAdaptiveLearning(ThriftRequest<SyncHomeworkDTO> request);

    /**
     * 作业系统业务版本，补充产品排课的录播产品的课后作业关联的user_answer_homework表的homework_id信息
     */
    @ThriftMethod(methodName = "sty_syncHomeworkIdVideoCourse")
    ThriftResponse sty_syncHomeworkIdVideoCourse(ThriftRequest<SyncHomeworkDTO> request);

    /**
     * 作业系统业务版本，补充新课程表排课的录播的课后作业关联的user_answer_homework表的homework_id信息
     */
    @ThriftMethod(methodName = "sty_syncHomeworkIdProductSchedule")
    ThriftResponse sty_syncHomeworkIdProductSchedule(ThriftRequest<SyncHomeworkDTO> request);

    /**
     * 作业系统业务版本，补充云私塾任务的课后作业关联的user_answer_homework表的homework_id信息
     */
    @ThriftMethod(methodName = "sty_syncHomeworkIdProductAdaptiveLearning")
    ThriftResponse sty_syncHomeworkIdProductAdaptiveLearning(ThriftRequest<SyncHomeworkDTO> request);


    /**
     * 提交作业批改记录
     */
    @ThriftMethod(methodName = "sty_submitHomeworkComment")
    ThriftResponse<Boolean>  sty_submitHomeworkComment(ThriftRequest<UserHomeworkCommentDTO> request);


    /**
     * 查询作业批改记录
     */
    @ThriftMethod(methodName = "sty_queryHomeworkComment")
    ThriftResponse<List<UserHomeworkCommentDTO>> sty_queryHomeworkComment(ThriftRequest<UserHomeworkCommentQuery> request);


    @ThriftMethod(methodName = "sty_getUserHomeworkTaskList")
    ThriftResponse<List<UserHomeworkTaskDTO>> sty_getUserHomeworkTaskList(ThriftRequest<UserHomeworkTaskQuery> request);

    @ThriftMethod(methodName = "sty_getUserHomeworkTaskPage")
    ThriftResponse<Page<UserHomeworkTaskDTO>> sty_getUserHomeworkTaskPage(ThriftRequest<UserHomeworkTaskQuery> request);

    @ThriftMethod(methodName = "sty_getHomeworkStatusByList")
    ThriftResponse<Map<Long,UserHomeworkTaskDTO>> sty_getHomeworkStatusByList(ThriftRequest<List<UserHomeworkTaskQuery>> request);

    /**
     * 保存用户协议
     */
    @ThriftMethod(methodName = "sty_saveUserAgreementList")
    ThriftResponse<Boolean> sty_saveUserAgreementList(ThriftRequest<List<UserAgreementDTO>> request);

    /**
     * 产品配置优化业务版本，根据电子试卷产品信息补user_answer_medical表的lesson_id信息
     */
    @ThriftMethod(methodName = "sty_syncLessonIdUserAnswerMedical")
    ThriftResponse sty_syncLessonIdUserAnswerMedical(ThriftRequest<SyncHomeworkDTO> request);

    /**
     * 通过课后作业ID查询是否作答过
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_findUserHomeWorkAnswerInfosGroupByHomeworkId")
    ThriftResponse<List<UserHomeworkAnswerDTO>> sty_findUserHomeWorkAnswerInfosGroupByHomeworkId(ThriftRequest<UserHomeworkAnswerQuery> request);

    /**
     * 云私塾题目收藏
     */
    @ThriftMethod(methodName = "sty_userCollectQuestion")
    ThriftResponse sty_userCollectQuestion(ThriftRequest request);

    /**
     * 根据题目idList判断题目是否收藏
     */
    @ThriftMethod(methodName = "sty_userIsCollectQuestion")
    ThriftResponse sty_userIsCollectQuestion(ThriftRequest request);

    /**
     * 迁移商品的收藏的题目数据
     */
    @ThriftMethod(methodName = "sty_transferCollectQuestionId")
    ThriftResponse<Boolean> sty_transferCollectQuestionId(ThriftRequest request);


    /**
     * 试卷做题预约
     * @param request
     * @return
     */
    @ThriftMethod(methodName = "sty_reservePaper")
    ThriftResponse<Boolean> sty_reservePaper(ThriftRequest<ReservationPaperDTO> request);

    /**
     * 获取错题信息集合
     */
    @ThriftMethod(methodName = "sty_getUserSubErrorQuestionList")
    ThriftResponse<UserSubErrorQuestionDTO> sty_getUserSubErrorQuestionList(ThriftRequest<UserSubErrorQuestionQuery> request);

    /**
     * 查询是否有答疑的回复记录
     */
    @ThriftMethod(methodName = "sty_isHaveNewRecordForAnswerQuestion")
    ThriftResponse<Boolean> sty_isHaveNewRecordForAnswerQuestion(ThriftRequest<QuestionAnswerQuery> request);


    /**
     * 获取自定义作业的最新状态值
     */
    @ThriftMethod(methodName = "sty_getMaxStateUserHomeworkTaskList")
    ThriftResponse<List<UserHomeworkMaxStateTaskDTO>> sty_getMaxStateUserHomeworkTaskList(ThriftRequest<UserHomeworkMaxStateTaskQuery> request);

    /**
     * 获取自定义作业的提交人数
     */
    @ThriftMethod(methodName = "sty_getCountSubmitCustomHomework")
    ThriftResponse<Integer> sty_getCountSubmitCustomHomework(ThriftRequest<UserHomeworkTaskQuery> request);

    /**
     * 保存入学测评的作答信息
     */
    @ThriftMethod(methodName = "sty_submitEvaluationQuestion")
    ThriftResponse<EvaluationSubmitRetDTO> sty_submitEvaluationQuestion(ThriftRequest<EvaluationSubmitDTO> request);

    /**
     * 获取入学测评的作答信息
     */
    @ThriftMethod(methodName = "sty_getEvaluationUserAnswerList")
    ThriftResponse<List<EvaluationUserAnswerDTO>> sty_getEvaluationUserAnswerList(ThriftRequest<EvaluationUserAnswerQuery> request);
    /**
     * 获取题目的统计信息
     */
    @ThriftMethod(methodName = "sty_getQuestionTopicStatisticList")
    ThriftResponse<List<QuestionTopicStatisticDTO>> sty_getQuestionTopicStatisticList(ThriftRequest<QuestionTopicStatisticQuery> request);

    /**
     * 获取学员作答中错题的统计情况
     */
    @ThriftMethod(methodName = "sty_findUserAnswerErrorQuestionInfoList")
    ThriftResponse<List<UserAnswerErrorQuestionVo>> sty_findUserAnswerErrorQuestionInfoList(ThriftRequest<UserErrorAnswerQuery> request);

    /**
     * 插入solution_question
     */
    @ThriftMethod(methodName = "sty_addSolutionQuestion")
    ThriftResponse<Long> sty_addSolutionQuestion(ThriftRequest<SolutionQuestionAddCommand> request);

    /**
     * 插入solution_answer
     */
    @ThriftMethod(methodName = "sty_addSolutionAnswer")
    ThriftResponse<Long> sty_addSolutionAnswer(ThriftRequest<SolutionAnswerAddCommand> request);

    /**
     * solutionQuestion转人工
     */
    @ThriftMethod(methodName = "sty_solutionQuestionToManual")
    ThriftResponse<Boolean> sty_solutionQuestionToManual(ThriftRequest<SolutionQuestionToManualCmd> request);

    /**
     * solutionQuestion转人工
     */
    @ThriftMethod(methodName = "sty_getSolutionQuestionByMessageId")
    ThriftResponse<SolutionQuestionDTO> sty_getSolutionQuestionByMessageId(ThriftRequest<String> request);


    /**
     * 获取课前做题试卷答题情况
     */
    @ThriftMethod(methodName = "sty_getUserAnswersByPreClassExercise")
    ThriftResponse<List<UserAnswer>> sty_getUserAnswersByPreClassExercise(ThriftRequest<String> request);

    @ThriftMethod(methodName = "sty_getUserAnswersByPaperIdsAndObjType")
    ThriftResponse<List<UserAnswer>> sty_getUserAnswersByPaperIdsAndObjType(ThriftRequest<String> request);

    @ThriftMethod(methodName = "sty_findUserHomeWorkAnswersByUserHomeworkIds")
    ThriftResponse<List<UserHomeWorkAnswer>> sty_findUserHomeWorkAnswersByUserHomeworkIds(ThriftRequest<String> request);

    /**
     * 根据错题id查询推荐题目idsList
     */
    @ThriftMethod(methodName = "sty_getRecommendedQuestionIdListByErrorQuestionId")
    ThriftResponse<List<Long>> sty_getRecommendedQuestionIdListByErrorQuestionId(ThriftRequest<String> request);

    @ThriftMethod(methodName = "sty_getSolutionQuestionCount")
    ThriftResponse<Long> sty_getSolutionQuestionCount(ThriftRequest<String> request);

    /**
     * 获取课前做题试卷答题情况
     */
    @ThriftMethod(methodName = "sty_getSolutionWxDocumentsActivityList")
    ThriftResponse<List<SolutionWxDocumentsActivityDTO>> sty_getSolutionWxDocumentsActivityList(ThriftRequest<SolutionWxDocumentsActivityQuery> request);


    /**
     * 根据uid,answerid,questionid获取List-主观题批阅使用
     */
    @ThriftMethod(methodName = "sty_getUserAnswerDetailList")
    ThriftResponse<List<UserAnswerDetailDto>> sty_getUserAnswerDetailList(ThriftRequest<UserAnswerDetailQuestionQuery> request);


//    ThriftResponse  sty_solution_userCollectQuestion(1:request req),/*2.3.11 收藏问题 */
//    ThriftResponse  sty_solution_userCancelCollectQuestion(1:request req),/*2.3.11 取消收藏问题 */
//    ThriftResponse  sty_solution_getQuestionInfoById(1:request req),/*2.3.11 通过问题id获取问题详情 */
//    ThriftResponse  sty_solution_getQuestionListByIds(1:request req),/*2.3.11 通过问题id串获取问题列表 */
//    ThriftResponse  sty_solution_userAddQuestion(1:request req),/*2.3.11 提问 或 追问 */
//    ThriftResponse  sty_solution_userLikeAnswerToQuestion(1:request req),/*2.3.11 对问题的答案点赞 */
//    ThriftResponse  sty_solution_userCancelLikeAnswerToQuestion(1:request req),/*2.3.11 取消对问题的答案点赞 */
//    ThriftResponse  sty_solution_userAdoptAnswerToQuestion(1:request req),/*2.3.11 采纳问题的答案 */
//    ThriftResponse  sty_solution_isReadAnswerToQuestion(1:request req),/*2.3.11 是否有未读回复 */
//
//
//    public void sty_solution_getQuestionList() {
//        public void sty_solution_getRecommendQuestionList() {
//            public void sty_solution_getQuestionInfoById() {
//                public void sty_solution_getQuestionListByIds() {
//                    public void sty_solution_userCollectQuestion(){
//                        public void sty_solution_userCancelCollectQuestion(){
//                            public void sty_solution_getUserCollectQuestionList(){
//                                public void sty_solution_userAddQuestion() {
//                                    public void sty_solution_userLikeAnswerToQuestion() {
//                                        public void sty_solution_userCancelLikeAnswerToQuestion() {
//                                            public void sty_solution_userAdoptAnswerToQuestion() {
//                                                public void sty_solution_isReadAnswerToQuestion() {
//                                                    public void sty_solution_submitComplaint() {
//                                                        public void sty_solution_setQuestionRecommendTop() {
//                                                            public void sty_solution_updateRecommendQuestionList() {
//                                                                public void sty_solution_getManageRecommendQuestionList() {
}
