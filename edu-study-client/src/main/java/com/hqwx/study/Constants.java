package com.hqwx.study;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/10 16:45
 * @description
 */
public interface Constants {

    /**
     * 标识提交 UserAnswer/UserHomeworkAnswer 做题数据的原因
     * 因为现有功能的实现，用户提交、商品升级都会调用相应的 submit 接口提交做题数，用户正常做题提交需要更新知识点掌握程度，但商品升级是系统行为，
     * 不应该再触发知识点掌握程度的更新（商品升级实现方案是否不太合理？），增加该标识于识别该功能。
     * 后缀如有需要可扩展其他提交场景
     */
    enum UserAnswerSubmitReason {
        /**
         * 用户做题提交
         */
        UserSubmit,

        /**
         * 商品升级提交的数据
         */
        GoodsUpgrade,
    }
}
