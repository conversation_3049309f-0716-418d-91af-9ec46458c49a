package com.hqwx.study.dto;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * AI主观题批改结果
 */
@Data
@Accessors(chain = true)
public class SubjectiveQuestionAICorrectingLogDTO {
    @Getter
    public static enum QuestionSource {
        /**
         * 标识题目来源课后作业
         */
        SOURCE_HOMEWORK(0),
        /**
         * 标识题目来源于试卷
         */
        SOURCE_PAPER(1);
        private final Integer value;
        QuestionSource(Integer value) {
            this.value = value;
        }
    };

    @Getter
    public static enum State {
        /**
         * 批阅失败
         */
        STATE_FAILED(-1),
        /**
         * 批阅中
         */
        STATE_CORRECTING(1),
        /**
         * 批阅完成
         */
        STATE_CORRECTED(2),
        ;
        private final Integer value;
        State(Integer value) {
            this.value = value;
        }

    }

    private Long id;
    //考试id
    private Long secondCategory;
    //科目id
    private Long categoryId;
    //题目id
    private Long questionId;
    //子题id
    private Long topicId;
    //题目信息
    private String questionInfo;
    //用户作答
    private String userAnswer;
    //ai的回复
    private String aiAnswer;
    /**
     * ai回复质量：1-A；2-B；3-C；4-D
     */
    private Integer score;
    //备注
    private String remarks;
    //完整提示词
    private String prompt;
    //创建时间
    private Date createDate;
    //更新时间
    private Date updateDate;
    //版本号
    private Long versionId;
    //用户id
    private Long uid;
    //用户作答id
    private String userAnswerId;

    /**
     * 题目来源，定义参考 {@link QuestionSource}
     */
    private Integer questionSource;

    /**
     * 记录状态，定义参考 {@link State}
     * NOTE: restfulapi 服务中课后作业(questionSource=0)因原先实现逻辑未使用该状态，后续的扩展请注意使用
     */
    private Integer state;

    /**
     * 批改方式：1-老师批改-线上作答；2-AI批改；3-老师批改-拍照上传
     */
    private Integer correctMethod;

}