package com.hqwx.study.dto;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/16 13:45
 * @description 用户作答批阅信息
 */
@Data
@Accessors(chain = true)
public class UserAnswerComment {
    @Getter
    public static enum State {
        /**
         * 批阅中（草稿）
         */
        COMMENT_PENDING(0),
        /**
         * 已批阅
         */
        COMMENTED(1);

        private final Integer value;
        State(Integer value) {
            this.value = value;
        }
    }

    private Long id;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 作答ID，
     * {@link UserAnswerComment#questionId} 和 {@link UserAnswerComment#topicId} 为空时，记录表示整个试卷的批阅
     * {@link UserAnswerComment#questionId} 和 {@link UserAnswerComment#topicId} 不为空时，记录表示题目的批阅
     */
    private Long answerId;
    /**
     * 商品ID
     */
    private Long goodsId;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 试卷ID
     */
    private Long paperId;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * 子题目ID
     */
    private Long topicId;
    /**
     * 批阅分数
     */
    private Double score;
    /**
     * 批阅内容
     */
    private String comment;
    /**
     * 批阅时间
     */
    private Date commentTime;
    /**
     * 老师ID
     */
    private Long teacherId;
    /**
     * 老师名字
     */
    private String teacherName;
    /**
     * 状态，参考{@link State}
     */
    private Integer state;

    /**
     * 是否是AI批阅。0或null：否；1：是
     */
    private Integer isAiComment;

    /**
     * 是否为拍照答题，0-否；1-是
     */
    private Integer correctMethod;

    /**
     * 是否为试卷总评语
     * @return true：是；false：否
     */
    public boolean isPaperComment() {
        return questionId == null && topicId == null;
    }

    /**
     * 是否为题目题目
     * @return true：是；false：否
     */
    public boolean isQuestionComment() {
        return questionId != null || topicId != null;
    }
}
