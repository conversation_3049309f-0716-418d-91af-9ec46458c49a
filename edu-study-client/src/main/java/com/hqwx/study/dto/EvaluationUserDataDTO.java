package com.hqwx.study.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class EvaluationUserDataDTO implements Serializable {

    private Long uid;
    private String applyProvince;//申报省份
    private String applyCity;//申报城市
    private Long secondCategory;		//意向考试ID
    private String company;		// 工作单位
    private String position;	// 职业
    private String professional;	// 专业
    private String education;	// 学历
    private String graduationTime;	// 毕业年份
    private String industry;	// 行业
    private String yearOfEmployment;	// 从业年份
    private String workingYears;	// 工作年限
    private String studyHis;	// 学习基础
    private String examHis;	    // 参与过的考试

    private String examCategory;	    // 参与过的考试种类
    private String studyWdZone;	    // 工作日学习时间
    private String studyRdZone;	    // 周末学习时间
    private String attendExamTimes;	    // 参加考试次数
    private String weekLearnDuration;	// 每周学习时长
    private String examPurpose;	        // 考试目的
    private String studyAbility;	    // 学习能力

}
