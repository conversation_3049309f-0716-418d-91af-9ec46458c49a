package com.hqwx.study.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 学员课后作业作答情况
 * <AUTHOR>
 * @version 2022-12-12
 */
@Data
public class UserHomeworkAnswerDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 作答ID
	 */
	private Long id;

	/**
	 * 用户uid
	 */
	private Long uid;
	/**
	 * 课后作业homeworkId
	 */
	private Long homeworkId;
	/**
	 * 课后作业作答题目数量
	 */
	private Integer answerNum;

	/**
	 * 课后作业作答开始时间
	 */
	private Date startTime;
}
