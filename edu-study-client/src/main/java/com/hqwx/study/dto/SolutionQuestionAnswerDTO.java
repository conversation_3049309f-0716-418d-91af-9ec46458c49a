package com.hqwx.study.dto;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 用户答疑问题回复实体
 *
 * */
public class SolutionQuestionAnswerDTO extends DataEntity<SolutionQuestionAnswerDTO> {
    private static final long serialVersionUID = 1L;
    private Long pid;
    private Long questionId;
    private String content;
    private String contentText;
    private Integer isBest;
    private Integer isRead;
    private Long userId;
    private String userName;
    private Integer likeNum;
    private Long createdTime;
    private Long updatedTime;
    private Integer havaLiked;

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentText() {
        return contentText;
    }

    public void setContentText(String contentText) {
        this.contentText = contentText;
    }

    public Integer getIsBest() {
        return isBest;
    }

    public void setIsBest(Integer isBest) {
        this.isBest = isBest;
    }

    public Integer getIsRead() {
        return isRead;
    }

    public void setIsRead(Integer isRead) {
        this.isRead = isRead;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    public Long getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Integer getHavaLiked() {
        return havaLiked;
    }

    public void setHavaLiked(Integer havaLiked) {
        this.havaLiked = havaLiked;
    }
}
