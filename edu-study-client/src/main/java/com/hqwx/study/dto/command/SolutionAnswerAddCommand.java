package com.hqwx.study.dto.command;

import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023/6/6 17:04
 */
@Data
public class SolutionAnswerAddCommand {

    /**
     * 父问题id
     */
    private Long pid;
    /**
     * 问题id
     */
    private Long questionId;
    /**
     * 内容,json格式{images:["xxx/xxx.png"],audios:["xxx/xxx.mp3"],text:"xxx"}
     */
    private String content;
    /**
     * 内容的文本数据，主要是给搜索引擎使用的
     */
    private String contentText;
    /**
     * 是否最佳回答
     */
    private Integer isBest;
    /**
     * 是否读取状态 0是未读取 1是已经读取
     */
    private Integer isRead;
    /**
     * 回答人
     */
    private Long userId;
    /**
     * 回答人name
     */
    private String userName;
    /**
     * 点赞数
     */
    private Integer likeNum;
    /**
     * 创建时间 时间戳（单位秒）
     */
    private Long createdTime;
    /**
     * 更新时间 时间戳（单位秒）
     */
    private Long updatedTime;
}
