package com.hqwx.study.dto;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;


public class QuestionTopicDTO extends DataEntity<QuestionTopicDTO> {

    public interface ScoreRule {
        int TOTAL = 0;
        int PART = 1;
        int DEDUCTION = 2;
    }

    public interface Type {
        int SINGLE_CHOICE = 0;
        int MULTI_CHOICE = 1;
        int UNCERTAIN_CHOICE = 2;
        int DETERMINE = 3;
        int FILL = 4;
        int ESSAY = 5;
    }

    private static final long serialVersionUID = 1L;
    //	private Long id;
    private Long qId;
    private Integer seq;
    private Integer qtype;
    private Integer scoreRule;
    private Double score;
    private String content;
    private String answerOption;
    private String answerText;
//	private Long createBy;
//	private Long updateBy;
//	private Date createDate;
//	private Date updateDate;

    /*非数据库对应属性*/
    private List<QuestionOptionsDTO> optionList;

//	public Long getId() {
//		return id;
//	}
//
//	public void setId(Long id) {
//		this.id = id;
//	}

    public Long getQId() {
        return qId;
    }

    public void setQId(Long qId) {
        this.qId = qId;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getQtype() {
        return qtype;
    }

    public void setQtype(Integer qtype) {
        this.qtype = qtype;
    }

    public Integer getScoreRule() {
        return scoreRule;
    }

    public void setScoreRule(Integer scoreRule) {
        this.scoreRule = scoreRule;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAnswerOption() {
        return answerOption;
    }

    public void setAnswerOption(String answerOption) {
        this.answerOption = answerOption;
    }

    public String getAnswerText() {
        return answerText;
    }

    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }

//	public Long getCreateBy() {
//		return createBy;
//	}
//
//	public void setCreateBy(Long createBy) {
//		this.createBy = createBy;
//	}
//
//	public Long getUpdateBy() {
//		return updateBy;
//	}
//
//	public void setUpdateBy(Long updateBy) {
//		this.updateBy = updateBy;
//	}
//
//	public Date getCreateDate() {
//		return createDate;
//	}
//
//	public void setCreateDate(Date createDate) {
//		this.createDate = createDate;
//	}
//
//	public Date getUpdateDate() {
//		return updateDate;
//	}
//
//	public void setUpdateDate(Date updateDate) {
//		this.updateDate = updateDate;
//	}


    public List<QuestionOptionsDTO> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<QuestionOptionsDTO> optionList) {
        this.optionList = optionList;
    }
}
