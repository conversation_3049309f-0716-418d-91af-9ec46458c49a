package com.hqwx.study.dto;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 *
 * 用户入学测评试卷 接口响应dto
 * <AUTHOR>
 * @date 2021-04-27 11:53:01
 */
public class EnterSchoolTestPaperDTO extends DataEntity<EnterSchoolTestPaperDTO> {
    private static final long serialVersionUID = 1L;
    /**
     * uid
     */
    private Long uid;

    /**
     * 科目id
     */
    private Long categoryId;
    /**
     * 关联试卷id
     */
    private Long relationPaperId;
    /**
     * 是否为关联后台试卷 是1 否0
     */
    private Integer relationFlag;
    /**
     * 用户答题id
     */
    private Integer userAnswerId;

    private List<QuestionDTO> questions;

    public Integer getUserAnswerId() {
        return userAnswerId;
    }

    public void setUserAnswerId(Integer userAnswerId) {
        this.userAnswerId = userAnswerId;
    }

    public List<QuestionDTO> getQuestions() {
        return questions;
    }

    public void setQuestions(List<QuestionDTO> questions) {
        this.questions = questions;
    }

    public Long getUid() {
        return this.uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getRelationPaperId() {
        return relationPaperId;
    }

    public void setRelationPaperId(Long relationPaperId) {
        this.relationPaperId = relationPaperId;
    }

    public Integer getRelationFlag() {
        return relationFlag;
    }

    public void setRelationFlag(Integer relationFlag) {
        this.relationFlag = relationFlag;
    }
}
