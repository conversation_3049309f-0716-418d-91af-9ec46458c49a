package com.hqwx.study.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户错题信息
 * */
@Data
public class UserSubErrorQuestionDTO implements Serializable {
	private Long id;
	private Long uid;		// 用户id
	private Long productId;		// 产品id
	private Long goodsId;		// 商品id
	private Long paragraphId;		// 段落id
	private Long lessonId;		// 讲节id
	private Long questionId;		// 题目id
	private Long topicId;		// 子题id
	private Integer qtype;		// 题目类型 0：单选 1：多选(多个答案，全部选中得分)  2不定向选择(不少于1个答案，按选中的比例得分) 3、主观题
	private Integer sourceType;		// 题目来源 0:随堂练习 1:错题集 2:收藏夹
	private Integer productType;		// 产品类型
	private Date lastErrorTime;		// last_error_time
	private String lastErrorAnswer;		// last_error_answer
	private Long categoryId;		// 科目id
	private Long answerId;		// 作答id 可能是user_answer表id或者user_answer_homework表id
	private Date createDate;

}
