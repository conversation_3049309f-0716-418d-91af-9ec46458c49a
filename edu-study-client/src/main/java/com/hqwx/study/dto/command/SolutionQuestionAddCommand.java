package com.hqwx.study.dto.command;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2023/6/6 17:04
 */
@Data
public class SolutionQuestionAddCommand {

    //父id
    private Long pid;

    //大类+科目+考试
    private String categoryIdCode;

    //大类id
    private Long firstCategory;

    //考试id
    private Long secondCategory;

    //科目id
    private Long categoryId;

    //课程id
    private Integer courseId;

    //课时id
    private Integer lessonId;

    //教材id
    private Integer teachBookId;

    //章节id
    private Integer chapterId;

    //知识点id
    private Integer knowledgeId;

    //题目id
    private Long questionId;

    //商品id
    private Long goodsId;

    //视频位置
    private Integer position;

    //标题
    private String title;

    //内容,json格式{images:["xxx/xxx.png"],audios:["xxx/xxx.mp3"],text:"xxx"}
    private String content;

    //内容的文本数据，主要是给搜索引擎使用的
    private String contentText;

    //提问人id
    private Long userId;

    //提问人name
    private String userName;

    //状态 0 未回答，1 已回答,2 已追问，3已解决
    private Integer status;

    private Integer isFrozen;

    //问题来源'player', 'ucenter', 'tiku', 'app', 'app_yss', 'uc_yss', 'record', 'live', 'question'
    private String source;

    //使用设备
    private String device;

    //ip
    private String ip;

    //创建时间 时间戳秒
    private Long createdTime;

    //更新时间 时间戳秒
    private Long updatedTime;

    //标签值 1是非专业 2是精华
    private Integer tag;

    //问题收藏数
    private Integer collectionNum;

    //点击量、浏览量
    private Integer views;

    //是否被投诉标识 默认是0 1是被投诉
    private Integer isComplained;

    //是否是智能学习
    private Integer isAl;

    //产品id
    private Long productId;

    //问题类型 1专业问题，2非专业问题
    private Integer questionType;

    //云私塾学习任务id，云私塾课程没有视频id，使用任务id关联查询使用
    private Long pathId;

    //试卷id
    private Long paperId;

    //学员答题id，题目作答时的数据id，当source=question时才传入此字段（题目混排使用）
    private Long answerId;

    private Integer sourceType;
    //答疑查询类型，问题来源 0:全部 1:教材 2:试题 3:课程 4:云私塾

    //资源id
    private Long resourceId;

    //是否公开: 0 不公开发表  1 公开发表 默认0
    private Integer isPublish;

    //是否ai回复，0人工 1ai回复中 2ai已回复 3已人工校验
    private Integer isAiAnswer;

    //是否为流式回复
    private Integer isStream ;

    //手动转人工时间
    private Date changeAiToManualTime ;

    //机构id
    private Long schId ;

    //拓展信息json格式
    private String extendInfo;

    //会话ID
    private String conversationId;

    //消息ID
    private String messageId;

}
