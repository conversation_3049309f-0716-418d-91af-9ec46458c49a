package com.hqwx.study.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EvaluationSubmitDTO implements Serializable {

    private Long uid;
    private Long paperId;//试卷id
    private Integer usetime;//答题时间
    private Date startTime;		// 用户开始答卷时间
    private Date endTime;		// 用户提交试卷时间
    private Long secondCategoryId;		// 考试id
    private Long goodsId;//商品id
    private String baseAnswerDetails;//作答字符串(基础题目的)
    private String majorAnswerDetails;//作答字符串(专业题目的)
    private String platForm;	//所属客户终端 ios android等
    private String appid;   //所属业务终端 jianzaoshi,edu24olapp等
    private Integer isAl;   //是否云私塾 0-否 1-是
    private Long schId; //机构id
}
