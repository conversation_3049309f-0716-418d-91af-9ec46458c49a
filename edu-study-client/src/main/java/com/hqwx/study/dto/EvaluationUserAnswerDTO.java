package com.hqwx.study.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class EvaluationUserAnswerDTO implements Serializable {

    private Long id;
    private Long uid;
    private Integer evaluationType;//做题评估类型 1-基础 4-专业
    private Integer busType;//所属新旧入学测评数据0表示新，1表示迁移过来的旧数据
    private Long paperId;//试卷id
    private Integer usetime;//答题时间
    private Integer answerNum;		// 当前答题数
    private Date startTime;		// 用户开始答卷时间
    private Date endTime;		// 用户提交试卷时间
    private Long secondCategoryId;		// 考试id
    private Long categoryId;//科目id
    private Long goodsId;//商品id
    private String platForm;	//所属客户终端 ios android等
    private String appid;   //所属业务终端 jianzaoshi,edu24olapp等
    private Long orgAnswerId;//专业试卷作答id
    private Date createDate;
    private Date updateDate;

    private List<EvaluationUserBaseAnswerDetailDTO> evaluationUserBaseAnswerDetailList;//答题详情列表(基础的)
    private List<UserAnswerDetailDTO> userAnswerDetailList;//答题详情列表(专业的)
}
