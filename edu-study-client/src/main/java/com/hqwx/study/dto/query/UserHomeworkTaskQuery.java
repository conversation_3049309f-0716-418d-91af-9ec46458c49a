package com.hqwx.study.dto.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UserHomeworkTaskQuery {

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 用户uid列表
     */
    private List<Long> uidList;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 课节ID
     */
    private Long lessonId;

    /**
     * 作业ID
     */
    private Long homeworkId;

    /**
     * 状态  0 未提交  1 已提交  2 未批改  3已批改
     */
    private Integer status;


    private Integer pageSize;


    private Integer pageNo;

    private Date startDate;

    private Date endDate;
}
