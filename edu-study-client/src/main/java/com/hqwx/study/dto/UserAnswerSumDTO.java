package com.hqwx.study.dto;

import com.hqwx.study.entity.UserAnswerDetail;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户答案大题详情Entity
 * <AUTHOR>
 * @version 2015-05-12
 */
public class UserAnswerSumDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Long uid;		// uid
	private Long userAnswerId;//user_answer_id
	private Long userHomeworkId;//user_homework_id
	private Long paragraphId;		// paragraph_id
	private Long lessonId;		// lesson_id
	private Long mClassId;		//m_class_id
	private Long paperId;		// 试卷id
	private Long questionId;		// 题目id
	private Date startTime;		// 答题开始时间
	private Date endTime;		// 答题结束时间
	private Double score;		// 用户得分

	/**
	 * 参考 {@link com.hqwx.study.entity.UserAnswer.State }
	 */
	private Integer state;
	private Long tuid;		// 批改老师uid
	private String comment;		// 老师评语
	private Date commentTime;		// comment_time
	/**
	 * 是否是智能化学习
	 */
	private Integer isAl;
	private Integer isRight;
	private Long goodsId;		//商品id
	private Long productId;		//产品id
	private Integer isLastRight;//是否最新提交且正确

	private Integer homeworkAnswerObjType;

	private List<Long> questionIdList;

	@Getter
	@Setter
	private List<UserAnswerDetail> answerDetail;

	public List<Long> getQuestionIdList() {
		return questionIdList;
	}

	public void setQuestionIdList(List<Long> questionIdList) {
		this.questionIdList = questionIdList;
	}

	public Integer getHomeworkAnswerObjType() {
		return homeworkAnswerObjType;
	}

	public void setHomeworkAnswerObjType(Integer homeworkAnswerObjType) {
		this.homeworkAnswerObjType = homeworkAnswerObjType;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getIsRight() {
		return isRight;
	}

	public void setIsRight(Integer isRight) {
		this.isRight = isRight;
	}

	public Long getUserHomeworkId() {
		return userHomeworkId;
	}

	public void setUserHomeworkId(Long userHomeworkId) {
		this.userHomeworkId = userHomeworkId;
	}

	public Long getmClassId() {
		return mClassId;
	}

	public void setmClassId(Long mClassId) {
		this.mClassId = mClassId;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getParagraphId() {
		return paragraphId;
	}

	public void setParagraphId(Long paragraphId) {
		this.paragraphId = paragraphId;
	}

	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}

	public Long getPaperId() {
		return paperId;
	}

	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}

	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Long getTuid() {
		return tuid;
	}

	public void setTuid(Long tuid) {
		this.tuid = tuid;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Date getCommentTime() {
		return commentTime;
	}

	public void setCommentTime(Date commentTime) {
		this.commentTime = commentTime;
	}

	public Long getUserAnswerId() {
		return userAnswerId;
	}

	public void setUserAnswerId(Long userAnswerId) {
		this.userAnswerId = userAnswerId;
	}

	public Integer getIsAl() {
		return isAl;
	}

	public void setIsAl(Integer isAl) {
		this.isAl = isAl;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public Integer getIsLastRight() {
		return isLastRight;
	}

	public void setIsLastRight(Integer isLastRight) {
		this.isLastRight = isLastRight;
	}
}
