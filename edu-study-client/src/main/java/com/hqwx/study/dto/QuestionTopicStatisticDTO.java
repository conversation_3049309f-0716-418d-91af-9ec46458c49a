package com.hqwx.study.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QuestionTopicStatisticDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private Long topicId;//子题目id
    private String errorProneOptionName;//易错项
    private Long answerRightCount;//作答正确数
    private Long answerCount;//作答总数
    private String rightRate;//正确率
    private Integer timeId;//分区字段（日期）
    private Date createDate;//创建时间
    private Date updateDate;//更新时间

}
