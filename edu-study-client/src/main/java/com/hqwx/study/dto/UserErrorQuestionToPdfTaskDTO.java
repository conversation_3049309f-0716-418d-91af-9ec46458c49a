package com.hqwx.study.dto;

import lombok.Data;

@Data
//用户错题生成pdf任务
public class UserErrorQuestionToPdfTaskDTO {
    //任务id
    private String taskId;
    //用户id
    private Long uid;
    //商品id
    private Long goodsId;
    //产品id
    private Long productId;
    //任务状态 0-未开始 1-进行中 2-已完成 -1-执行失败
    private Integer status;
    //错误信息
    private String errorMsg;
    //pdf地址
    private String pdfUrl;
}
