package com.hqwx.study.dto;

import lombok.Data;

import java.util.List;
import java.util.Random;

@Data
public class ExemplaryAnalogousQuestionDTO {

    private Long errorQuestionId;

    private Long exemplaryAnalogousId;

    private List<Long> questionIdList;

    // 状态：0-未作答；1-已作答
    private Integer state;

    private Long exemplaryAnalogousAnswerId;

    private Double finishPeopleRate;

    public Double getFinishPeopleRate() {
        Random random = new Random();
        // 生成30到90之间的随机数
        double randomValue = 30 + (60 * random.nextDouble());
        return finishPeopleRate = Math.round(randomValue * 10.0) / 10.0;//保留一位小数;
    }
}