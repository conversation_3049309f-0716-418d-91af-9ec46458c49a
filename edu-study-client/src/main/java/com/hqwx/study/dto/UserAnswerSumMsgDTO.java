package com.hqwx.study.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserAnswerSumMsgDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	private Integer type;		// 0-试卷 1-作业
	private Long id;
	private Long uid;		// uid
	private Long userAnswerSumId;//user_answer_sum_id
	private Long goodsId;		//商品id
	private Long productId;		//产品id
	private Long lessonId;		// lesson_id
	private Long relationId;// 资源关联id（新课程表的字段）
	private Long paperId;		// 试卷id
	private Long questionId;		// 题目id
	private Integer questionType;		// 题目类型
	private Integer isAl;		//是否是智能化学习
	private Integer isRight;
	private Long objId;		// obj_id
	private Integer objType;		// obj_type （0：讲作业，1：段落作业，2：微课作业，3：个性化作业，4：新录播课作业，5：题库作业）
	private Long schId;
	private Date createDate;
	private Integer goodsSourceFlag; //商品创建方式 1-产品排课 2-课程表排课 3-云私塾类型商品
	private Long tenantId;//网校id
	private String answer;//用户回答
}
