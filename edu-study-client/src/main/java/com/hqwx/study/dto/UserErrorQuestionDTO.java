package com.hqwx.study.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户错题提交bean
 * */
public class UserErrorQuestionDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long uid;
	private Long paragraphId;
	private Long lessonId;
	private Long questionId;
	private Long topicId;
	private Date lastErrorTime;
	private String lastErrorAnswer;

	private Long objId;		// obj_id
	private Integer objType;		// obj_type
	private Integer isAl;

	private Long productId;	// 产品id
	private Long goodsId;// 商品id

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getParagraphId() {
		return paragraphId;
	}

	public void setParagraphId(Long paragraphId) {
		this.paragraphId = paragraphId;
	}

	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}

	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}

	public Long getTopicId() {
		return topicId;
	}

	public void setTopicId(Long topicId) {
		this.topicId = topicId;
	}

	public Date getLastErrorTime() {
		return lastErrorTime;
	}

	public void setLastErrorTime(Date lastErrorTime) {
		this.lastErrorTime = lastErrorTime;
	}

	public String getLastErrorAnswer() {
		return lastErrorAnswer;
	}

	public void setLastErrorAnswer(String lastErrorAnswer) {
		this.lastErrorAnswer = lastErrorAnswer;
	}

	public Long getObjId() {
		return objId;
	}

	public void setObjId(Long objId) {
		this.objId = objId;
	}

	public Integer getObjType() {
		return objType;
	}

	public void setObjType(Integer objType) {
		this.objType = objType;
	}

	public Integer getIsAl() {
		return isAl;
	}

	public void setIsAl(Integer isAl) {
		this.isAl = isAl;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
}
