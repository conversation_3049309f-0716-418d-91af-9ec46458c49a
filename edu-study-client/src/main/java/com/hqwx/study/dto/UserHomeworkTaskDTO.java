package com.hqwx.study.dto;

import lombok.Data;

@Data
public class UserHomeworkTaskDTO {

    private Long id;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 课节ID
     */
    private Long lessonId;

    /**
     * 作业ID
     */
    private Long homeworkId;

    /**
     * 状态  0 未提交  1 已提交  2 未批改  3已批改
     */
    private Integer status;

    /**
     * 答案ID
     */
    private Long answerId;

    /**
     * 评分
     */
    private Double score;

    /**
     * 答题时间
     */
    private Long answerTime;

    /**
     * 作业点评ID
     */
    private Long commentId;

    /**
     * 作业点评时间
     */
    private Long commentTime;

    /**
     * 版本号
     */
    private Integer version;
}
