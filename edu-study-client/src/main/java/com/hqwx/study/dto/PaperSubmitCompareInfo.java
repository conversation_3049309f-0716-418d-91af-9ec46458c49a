package com.hqwx.study.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/2 14:28
 * @description 试卷提交的比较信息：
 *  已知：试卷 paperId 得分 score
 *  返回信息：多少从做过该试卷；平均分是多少；得分score超过百分之多少的人
 */
@Data
public class PaperSubmitCompareInfo {
     /** -- 基础信息 -- **/

    Long paperId;
    /**
     * 用户得分
     */
    Double score;
    /**
     * 试卷总分
     */
    Double totalScore;

    Double paperAccuracy;

    /** -- 比较信息 --**/

    /**
     * 试卷提交过的人次
     */
    Long submitCount;

    /**
     * 试卷正确率提交人次
     */
    Long submitAccuracyCount;

    /**
     * 所有提交过的平均分（非精确值）
     */
    Double averageScore;

    /**
     * 例如：80，表示超过 80% 的人, 结果已乘以100
     */
    Integer greatThanPercent;

    /**
     * 试卷平均正确率
     */
    Double averageAccuracy;

    /**
     * 按照正确率返回超过人的百分比
     */
    Integer greatThanPercentByAccuracy;
}
