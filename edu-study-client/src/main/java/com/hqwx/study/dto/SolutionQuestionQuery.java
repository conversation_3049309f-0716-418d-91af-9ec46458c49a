package com.hqwx.study.dto;

import java.util.List;

/**
 * 答疑查询参数
 * <AUTHOR>
 */
public class SolutionQuestionQuery {
    /**
     * 用户uid
     */
    private Long uid;
    /**
     * 考试id
     */
    private Long secondCategory;
    /**
     * 科目id
     */
    private Long categoryId;
    /**
     * 课程id
     */
    private Integer courseId ;
    /**
     * 讲id 或直播课室id
     */
    private Integer lessonId;
    /**
     * 标签值 1是非专业 2是精华
     */
    private Integer tag;

    /**
     * 标签值列表
     */
    private List<Integer> tagList;
    /**
     * 试题题目id
     */
    private Long questionId;
    /**
     * 知识点id
     */
    private Integer knowledgeId;

    /**
     * 知识点id集合
     */
    private List<Long> knowledgeIds;
    /**
     * 是否云私塾Pro
     */
    private Integer isAl;
    private Integer from;
    private Integer rows;
    /**
     * 来源 'player','ucenter','tiku','app','app_yss','uc_yss'
     */
    private String source;
    /**
     * 科目id串[xxx,xxx]
     */
    private String categoryIds;

    private Long pathId;

    /**
     *  查询的问题来源
     */
    private Integer sourceType;

    /**
     * 查询对应的机构id
     */
    private Long schId;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 问题类型 1专业问题，2非专业问题
     */
    private Integer questionType;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Integer getCourseId() {
        return courseId;
    }

    public void setCourseId(Integer courseId) {
        this.courseId = courseId;
    }

    public Integer getLessonId() {
        return lessonId;
    }

    public void setLessonId(Integer lessonId) {
        this.lessonId = lessonId;
    }

    public Integer getTag() {
        return tag;
    }

    public void setTag(Integer tag) {
        this.tag = tag;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Integer getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(Integer knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public Integer getIsAl() {
        return isAl;
    }

    public void setIsAl(Integer isAl) {
        this.isAl = isAl;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(String categoryIds) {
        this.categoryIds = categoryIds;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getPathId() {
        return pathId;
    }

    public void setPathId(Long pathId) {
        this.pathId = pathId;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Long getSchId() {
        return schId;
    }

    public void setSchId(Long schId) {
        this.schId = schId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public List<Long> getKnowledgeIds() {
        return knowledgeIds;
    }

    public void setKnowledgeIds(List<Long> knowledgeIds) {
        this.knowledgeIds = knowledgeIds;
    }

    public List<Integer> getTagList() {
        return tagList;
    }

    public void setTagList(List<Integer> tagList) {
        this.tagList = tagList;
    }
}
