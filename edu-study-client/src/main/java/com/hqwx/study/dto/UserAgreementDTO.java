package com.hqwx.study.dto;

import lombok.Data;

import java.util.Date;

/**
 * 用户协议Entity
 * <AUTHOR>
 * @version 2015-05-13
 */
@Data
public class UserAgreementDTO {
	private Long id;
	private Long orderId;		// order_id
	private Long uid;		// uid
	private String username;		// username
	private String title;		// title
	private String phone;		// phone
	private Integer status;		// status
	private Date buyDate;		// buy_date
	private Date agreeDate;		// agree_date
	private String content;		// content
	private String ip;		// ip
	private String goodsId;		// goods_id
	private String goodsName;		// goods_name
	private String name;		// name
	private String address;		// address
	private String email;		// email
	private Integer orgIsFirstParty;		//机构是否是甲方：0否，1是
	private Long agreementId;		//协议ID

	private Long schId;
	private String delFlag = "0"; 	// 删除标记（0：正常；1：删除；2：审核）

	private String encPhone;
	private String encIdcard;
	private String encName;

	protected Long createBy; // 创建者
	protected Date createDate; // 创建日期
	protected Long updateBy; // 更新者
	protected Date updateDate; // 更新日期

}