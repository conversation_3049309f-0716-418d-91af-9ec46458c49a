package com.hqwx.study.dto.query;

import java.util.List;

public class QuestionQuery {
    private Long categoryId;

    private Long boxId;

    private Long teachBookId;

    private Integer num;

    private List<Integer> questionTypes;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getBoxId() {
        return boxId;
    }

    public void setBoxId(Long boxId) {
        this.boxId = boxId;
    }

    public Long getTeachBookId() {
        return teachBookId;
    }

    public void setTeachBookId(Long teachBookId) {
        this.teachBookId = teachBookId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public List<Integer> getQuestionTypes() {
        return questionTypes;
    }

    public void setQuestionTypes(List<Integer> questionTypes) {
        this.questionTypes = questionTypes;
    }
}