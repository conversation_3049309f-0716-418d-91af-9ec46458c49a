package com.hqwx.study.dto;

import lombok.Data;

@Data
public class SolutionWxDocumentsActivityDTO {
    private Long id; // 主键

    private Long orgId; // 机构id

    private Long type; // 广告类型 1弹窗 2悬浮图 3banner  4动态icon 5推荐广告 6微销广告图

    private String appid; //  所属wxapp

    private Integer position; // 位置

    private Long secondCategory; // 所属考试  0 为全部

    private String secondCategoryIds; //type=4/5时，所属考试id集合，逗号隔开

    private Long contentType; //内容类型

    private Long showType;  // 显示类型 1 文本 2 图片

    private String images; // 图片地址

    private String title; // 标题

    private String content; // 内容

    private String button;  // 按钮

    private Long urlType; //跳转类型 1客服消息 2小程序路径 3外部链接

    private String path; //wxapp路径

    private String extLink; //外部链接

    private String targetAppid; //目标小程序appid

    private Long openTime;  // 限时上线时间

    private Long closeTime; // 限时下线时间

    private Long sort;  // 排序

    private Long status; // 状态 0未上线 1已上线 9 删除

    private Long channel; // 频道
}
