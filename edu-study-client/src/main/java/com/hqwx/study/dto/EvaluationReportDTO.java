package com.hqwx.study.dto;

import lombok.Data;
import java.util.List;


/**
 * 测评报告视图
 *
 * <AUTHOR>
 * @version 2023-04-13
 */
@Data
public class EvaluationReportDTO {

    //是否只含测评基础题目
    private Boolean isOnlyHaveEvaluationBaseQuestion=false;
    //用时(秒)
    private Long usetime;
    //正确率
    private String accuracy;
    //总题目数
    private Integer questionNum;
    //做题数
    private Integer answerNum;
    //答对题目数
    private Integer rightNum;
    //当前水平
    private Double levelRate = 0.d;
    //能力详情列表
    private List<KnowledgeQuestionReportDTO> knowledgeQuestionReportVoList;
    //作答ID
    private Long answerId;

    //作答ID字符串
    private String answerIdStr;

    //入学测评试卷id
    private Long evaluationPaperId;


}
