package com.hqwx.study.dto.query;

public class UserAnswerSumCountQuery {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long uid;
    private Long lessonId;
    private Long userHomeworkAnswerId;
    private Long homeworkId;

    private Long objId;
    private Integer objType;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getLessonId() {
        return lessonId;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public Long getUserHomeworkAnswerId() {
        return userHomeworkAnswerId;
    }

    public void setUserHomeworkAnswerId(Long userHomeworkAnswerId) {
        this.userHomeworkAnswerId = userHomeworkAnswerId;
    }

    public Long getHomeworkId() {
        return homeworkId;
    }

    public void setHomeworkId(Long homeworkId) {
        this.homeworkId = homeworkId;
    }
}
