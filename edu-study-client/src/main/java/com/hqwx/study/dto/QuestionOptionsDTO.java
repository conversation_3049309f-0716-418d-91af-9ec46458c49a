package com.hqwx.study.dto;

import cn.huanju.edu100.persistence.model.DataEntity;


public class QuestionOptionsDTO extends DataEntity<QuestionOptionsDTO> {

    private static final long serialVersionUID = 1L;
    //	private Long id;
    private Long tId;
    private String seq;
    private String content;
    private String pic;
    private Integer scoreProp;

//	public Long getId() {
//		return id;
//	}
//
//	public void setId(Long id) {
//		this.id = id;
//	}

    public Long getTId() {
        return tId;
    }

    public void setTId(Long tId) {
        this.tId = tId;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Integer getScoreProp() {
        return scoreProp;
    }

    public void setScoreProp(Integer scoreProp) {
        this.scoreProp = scoreProp;
    }
}
