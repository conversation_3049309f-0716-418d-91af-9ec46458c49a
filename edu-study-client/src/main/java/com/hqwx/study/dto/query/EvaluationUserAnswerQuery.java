package com.hqwx.study.dto.query;

import lombok.Data;


@Data
public class EvaluationUserAnswerQuery {

    private Long uid;
    private Integer evaluationType;//做题评估类型 1-基础 4-专业
    private Long secondCategoryId;		// 考试id
    private Long goodsId;//商品id
    private Integer isGetAnswerDetail = 0;//是否获取题目的作答详情 0-否 1-是

    private Integer from;//从哪一个记录开始 0是起点
    private Integer pageSize;//页大小
    private String orderBy;//排序

    private Long answerId;  //作答id
    private Long orgAnswerId; //真实作答id
}
