package com.hqwx.study.dto;


import java.io.Serializable;
import java.util.List;

/**
 *
 * 用户入学测评试卷 接口响应dto
 * <AUTHOR>
 * @date 2021-04-27 11:53:01
 */
public class EnterSchoolTestREQ implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * uid
     */
    private Long uid;

    /**
     * 科目id
     */
    private Long categoryId;

    private Long paperId;

    private Long userAnswerId;

    private List<Long> categoryIds;

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public Long getUserAnswerId() {
        return userAnswerId;
    }

    public void setUserAnswerId(Long userAnswerId) {
        this.userAnswerId = userAnswerId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public List<Long> getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(List<Long> categoryIds) {
        this.categoryIds = categoryIds;
    }
}
