package com.hqwx.study.dto.query;

import lombok.Data;

import java.util.List;

@Data
public class UserErrorAndCorrectQuestionQuery {

    private Long uid;		// 用户uid
    private Long categoryId;		// 考试id
    private Long productId;		// 产品id
    private Long goodsId;		// 商品id
    private Integer moreThanTimes; // 大于错误次数
    private Integer lessThanTimes; // 小于错误次数
    private Integer type; // 0待练习 1:顽固错题2：已纠正
    private String startTime;
    private String endTime;
    private Integer sourceType;//0全部1当前课程配置2快题库
    private List<Long> notInQuestionList;
    private Integer from = 0;
    private Integer rows = 10;

    public interface type {
        int WAIT_PRACTICE = 0;
        int PERSISTENT_ERROR = 1;
        int HAVE_BEAN_CORRECTED = 2;
    }

}
