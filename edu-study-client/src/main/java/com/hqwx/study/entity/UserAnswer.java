package com.hqwx.study.entity;

import cn.huanju.edu100.persistence.model.DataEntity;
import com.hqwx.study.Constants;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户答题Entity
 * <AUTHOR>
 * @version 2015-05-12
 */
public class UserAnswer extends DataEntity<UserAnswer> {

    public interface State {
		/**
		 * 未开始
		 */
		Integer NOT_BEGIN = 0;
		/**
		 * 进行中
		 */
		Integer DOING = 1;
		/**
		 * 已交卷
		 */
		Integer SUBMITTED = 2;
		/**
		 * 已评卷
		 */
		Integer COMMENTED = 3;
	}
	
	public interface Source {
		/**
		 * 普通购买
		 */
		int buy = 0;
		/**
		 * 个性化服务
		 */
		int personal = 1;
		
		/**
		 * 题库试卷
		 */
		int questionBox = 2;

		/**
		 * 每日一练
		 */
		int dailyPractice = 3;

		/**
		 * 万人模考
		 */
		int mockExam = 4;

		/**
		 * 微课班试卷
		 */
		int weikeClassPaper = 101;

		/**
		 * 云私塾pro的普通试卷
		 *
		 */
		int AL_PAPER = 201;

        /**
         * 云私塾pro的巩固练习
         */
        int AL_CONSOLIDATION = 202;
        /**
         * 云私塾Pro的入学测评
         */
        int AL_ASSESSMENT = 203;
		/**
		 * 云私塾Pro的章节回顾试卷
		 */
		int AL_CHAPTER_REVIEW = 204;
		/**
		 * 题库5.0 入学测评
		 */
		int TK_ENTERSCHOOLTEST = 301;
		/**
		 * 题库5.0 月月考
		 */
		int TK_MONTH_TEST = 8;
		/**
		 * 入学测评试卷
		 */
		int EVALUATION_PAPER = 333;

		/**
		 * 课前做题提交的试卷
		 */
		int PRE_CLASS_EXERCISE = 340;

		/**
		 * 题集提交的真题和模考
		 */
		int QUESTION_COLLECTION_REAL_AND_MOCK_EXAM = 342;

		/**
		 * 集训刷题提交的试卷
		 */
		int AL_TRAIN_BRUSH_PAPER = 343;
	}
	
	public interface ObjType{
		/**
		 * 普通购买试卷记录
		 */
		int buy = 0;
		/**
		 * 个性化任务试卷记录
		 */
		int personal = 1;
		
		/**
		 * 题库试卷记录
		 */
		int questionBox = 2;

		/**
		 * 每日一练
		 */
		int dailyPractice = 3;

        /**
         * 万人模考
         */
        int mockExam = 4;

		/**
		 * 普通课的仿真机考
		 */
		int simulationExamNormal = 10;

		/**
		 * 云私塾的仿真机考
		 */
		int simulationExamYSS = 11;

		/**
		 * 题库的仿真机考
		 */
		int simulationExamTK = 12;


		/**
		 * 微课班试卷
		 */
		int weikeClassPaper = 101;

		/**
		 * 直播课前试卷
		 */
		int preClassPaper = 102;

		/**
		 * 普通录播任务 课前练习试卷
		 */
		int ORDINARY_PRE_CLASS_EXERCISE_PAPER = 103;

		/**
		 * 云私塾pro的普通试卷
		 *
		 */
		int AL_PAPER = 201;

        /**
         * 云私塾pro的巩固练习
         */
        int AL_CONSOLIDATION = 202;
        /**
         * 云私塾Pro的入学测评
         */
        int AL_ASSESSMENT = 203;

		/**
		 * 云私塾Pro的章节回顾试卷
		 */
		int AL_CHAPTER_REVIEW = 204;

		/**
		 * 题库5.0 入学测评
		 */
		int TK_ENTERSCHOOLTEST = 301;
		/**
		 * 题库5.0 月月考
		 */
		int TK_MONTH_TEST = 8;

		/**
		 * 入学测评试卷
		 */
		int EVALUATION_PAPER = 333;

		/**
		 * 课前做题提交的试卷
		 */
		int AL_PRE_CLASS_EXERCISE = 340;

		/**
		 * 章节巩固复习提交的试卷
		 */
		int CHAPTER_CONSOLIDATE_PAPER = 341;

		/**
		 * 题集提交的真题和模考
		 */
		int QUESTION_COLLECTION_REAL_AND_MOCK_EXAM = 342;

		/**
		 * 集训刷题提交的试卷
		 */
		int AL_TRAIN_BRUSH_PAPER = 343;
	}

	private List<Long> objTypes;
	private List<Integer> states;

	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long paperId;		// paper_id
	private Integer paperType;		// 试卷类型:0作业 ，1练习题，2正式考试 ，3模拟考试，4仿真机考
	private Double score;		// score
	private Long usetime;		// usetime
	private Long answerNum;		// 当前答题数
	private Date startTime;		// 用户开始答卷时间
	private Date endTime;		// 用户提交试卷时间
	private String comment;		// 老师评语
	private Integer state;		// 状态，0未开始 1进行中 2已交卷 3已评卷
	private Integer source;		// 记录来源（0：普通购买，1：个性化服务，2：题库试卷）
	
	private Long objId;			// 记录来源id（对于objType=0的，objId=0。objType=1的，objId=任务id（由于前期未考虑，会有部分数据丢失）。objType=2的，objId=题库id）
	private Integer objType;	// 试卷记录类型（0：普通购买试卷记录，1：个性化任务试卷记录，2：题库试卷记录）
	
	private List<UserAnswerDetail> answerDetail;

	private Integer isSubmit;
	private Long mClassId;		//m_class_id

	private Long productId;		// 产品id
	private Long goodsId;// 商品id
	private String appid;   //app的类型 所属终端，web、PC客户端、环球网校APP、快题库、建造师题库…、快题库小程序
	private String platForm;	// app平台ios android
	private Long lessonId;
    /**
     * 云私塾pro专用,对应的学习任务的教学方式
     */
    private Integer studyMethod;

    private Integer hasConsolidation;
	private Long categoryId;

    private List<Long> paperIds;
    private List<Long> objIds;

    //tiku4.6 练习记录新增需求(非表结构中字段)
    private Long num;// 题目数,
    private Long right_count;//答对数
    //tiku4.9
    private Long wrong_count;//答错数

    Map<Long, Long> map_qid_rw = new HashMap<Long, Long>();//key:questionid value:正确与否

	private Integer from;
	private Integer pageSize;
	private String finishRate;
	//学生中心2.2-医卫
	private Long ePaperId;// 电子试卷id
	private Integer isOnErrorList;// 是否是错题集的题目 0否 1是
	private Integer isOnCollectList;// 是否是收藏夹的题目 0否 1是
	private Integer errorListType;//错题集或收藏夹的类型 1场景归类 2题型归类
	private Integer errorType;//错题集或收藏夹的题目的类型 0试卷 1模考 2作业 3单项选择题 4多项选择题 5不定项选择题 6判断题 7填空题 8简答题 9案例题
	private Integer doneMode;// 做题模式 1练习模式 2考试模式

	private Integer oldAutoRemoveVersion = 0;

	private String idStr;	//answerId对应的String值
	private List<Long> productIdList;	//产品ids
	private List<Long> answerIds;
	private Long studyPathId;	//云私塾任务id

	private Integer eventType; //1-处理分数；2-处理正确率

	private String salesName;//推荐试卷作答的销售人员名称

	public Integer getEventType() {
		return eventType;
	}

	public void setEventType(Integer eventType) {
		this.eventType = eventType;
	}

	private Constants.UserAnswerSubmitReason submitReason = Constants.UserAnswerSubmitReason.UserSubmit;
	public Long getObjId() {
		return objId;
	}

	public void setObjId(Long objId) {
		this.objId = objId;
	}

	public Integer getObjType() {
		return objType;
	}

	public void setObjType(Integer objType) {
		this.objType = objType;
	}

	public Long getmClassId() {
		return mClassId;
	}

	public void setmClassId(Long mClassId) {
		this.mClassId = mClassId;
	}

	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	public Integer getIsSubmit() {
		return isSubmit;
	}

	public void setIsSubmit(Integer isSubmit) {
		this.isSubmit = isSubmit;
	}

	public UserAnswer() {
		super();
	}

	public UserAnswer(Long uid, Long paperId, Integer paperType, Integer state) {
		this.uid = uid;
		this.paperId = paperId;
		this.paperType = paperType;
		this.state = state;
	}

	public UserAnswer(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public Long getPaperId() {
		return paperId;
	}

	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}
	
	public Integer getPaperType() {
		return paperType;
	}

	public void setPaperType(Integer paperType) {
		this.paperType = paperType;
	}
	
	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}
	
	public Long getUsetime() {
		return usetime;
	}

	public void setUsetime(Long usetime) {
		this.usetime = usetime;
	}
	
	public Long getAnswerNum() {
		return answerNum;
	}

	public void setAnswerNum(Long answerNum) {
		this.answerNum = answerNum;
	}
	
	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	
	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public List<UserAnswerDetail> getAnswerDetail() {
		return answerDetail;
	}

	public void setAnswerDetail(List<UserAnswerDetail> answerDetail) {
		this.answerDetail = answerDetail;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getPlatForm() {
		return platForm;
	}

	public void setPlatForm(String platForm) {
		this.platForm = platForm;
	}

    public Integer getStudyMethod() {
        return studyMethod;
    }

    public void setStudyMethod(Integer studyMethod) {
        this.studyMethod = studyMethod;
    }

    public Integer getHasConsolidation() {
        return hasConsolidation;
    }

    public void setHasConsolidation(Integer hasConsolidation) {
        this.hasConsolidation = hasConsolidation;
    }

    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }

    public Long getRight_count() {
        return right_count;
    }

    public void setRight_count(Long right_count) {
        this.right_count = right_count;
    }
	public List<Long> getObjTypes() {
		return objTypes;
	}

	public void setObjTypes(List<Long> objTypes) {
		this.objTypes = objTypes;
	}

	public List<Integer> getStates() {
		return states;
	}

	public void setStates(List<Integer> states) {
		this.states = states;
	}

	public Integer getFrom() {
		return from;
	}

	public void setFrom(Integer from) {
		this.from = from;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

    public Long getWrong_count() {
        return wrong_count;
    }

    public void setWrong_count(Long wrong_count) {
        this.wrong_count = wrong_count;
    }

    public Map<Long, Long> getMap_qid_rw() {
        return map_qid_rw;
    }

    public void setMap_qid_rw(Map<Long, Long> map_qid_rw) {
        this.map_qid_rw = map_qid_rw;
    }

	public String getFinishRate() {
		return finishRate;
	}

	public void setFinishRate(String finishRate) {
		this.finishRate = finishRate;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public List<Long> getPaperIds() {
		return paperIds;
	}

	public void setPaperIds(List<Long> paperIds) {
		this.paperIds = paperIds;
	}

	public List<Long> getObjIds() {
		return objIds;
	}

	public void setObjIds(List<Long> objIds) {
		this.objIds = objIds;
	}

	public Long getEPaperId() {
		return ePaperId;
	}

	public void setEPaperId(Long ePaperId) {
		this.ePaperId = ePaperId;
	}

	public Integer getOldAutoRemoveVersion() {
		return oldAutoRemoveVersion;
	}

	public void setOldAutoRemoveVersion(Integer oldAutoRemoveVersion) {
		this.oldAutoRemoveVersion = oldAutoRemoveVersion;
	}

	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}

	public Integer getErrorListType() {
		return errorListType;
	}

	public void setErrorListType(Integer errorListType) {
		this.errorListType = errorListType;
	}

	public Integer getErrorType() {
		return errorType;
	}

	public void setErrorType(Integer errorType) {
		this.errorType = errorType;
	}

	public Integer getDoneMode() {
		return doneMode;
	}

	public void setDoneMode(Integer doneMode) {
		this.doneMode = doneMode;
	}

	public Integer getIsOnErrorList() {
		return isOnErrorList;
	}

	public void setIsOnErrorList(Integer isOnErrorList) {
		this.isOnErrorList = isOnErrorList;
	}

	public Integer getIsOnCollectList() {
		return isOnCollectList;
	}

	public void setIsOnCollectList(Integer isOnCollectList) {
		this.isOnCollectList = isOnCollectList;
	}

	public String getIdStr() {
		return idStr;
	}

	public void setIdStr(String idStr) {
		this.idStr = idStr;
	}

	public List<Long> getProductIdList() {
		return productIdList;
	}

	public void setProductIdList(List<Long> productIdList) {
		this.productIdList = productIdList;
	}

	public List<Long> getAnswerIds() {
		return answerIds;
	}

	public void setAnswerIds(List<Long> answerIds) {
		this.answerIds = answerIds;
	}

	public Long getStudyPathId() {
		return studyPathId;
	}

	public void setStudyPathId(Long studyPathId) {
		this.studyPathId = studyPathId;
	}

	public Constants.UserAnswerSubmitReason getSubmitReason() {
		return submitReason;
	}

	public void setSubmitReason(Constants.UserAnswerSubmitReason submitReason) {
		this.submitReason = submitReason;
	}

	/**
	 * 是否已评卷（批阅）
	 * @param state 状态
	 * @return 是否已评卷
	 */
	public static Boolean isCommented(Integer state) {
		return State.COMMENTED.equals(state);
	}

	public Boolean isCommented() {
		return isCommented(this.state);
	}

	public String getSalesName() {
		return salesName;
	}

	public void setSalesName(String salesName) {
		this.salesName = salesName;
	}
}