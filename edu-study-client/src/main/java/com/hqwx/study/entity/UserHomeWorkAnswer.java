package com.hqwx.study.entity;

import cn.huanju.edu100.persistence.model.DataEntity;
import com.hqwx.study.Constants;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户作业Entity
 * <AUTHOR>
 * @version 2015-05-12
 */
@Data
public class UserHomeWorkAnswer extends DataEntity<UserHomeWorkAnswer> {

	public interface HomeWorkType {
		/**
		 * 讲作业(针对老的讲)
		 */
		int LESSON = 0;
		/**
		 * 段落作业
		 */
		int PARAGRAPH = 1;
		/**
		 * 微课作业
		 */
		int M_CLASS = 2;
		/**
		 * 个性化作业
		 */
		int PERSON_TASK = 3;
		
		/**
		 * 新录播课段落作业
		 * */
		int COURSE_PARAGRAPH = 4;
		
		/**
		 * 题库练习作业
		 * */
		int BOX_HOMEWORK = 5;
		
		/**
		 * 测评作业，即课件作业（最对新的讲）
		 * */
		int RESOURCE_VIDEO = 6;
		/**
		 * 课件资源的段落作业
		 */
		int LESSON_PARAGRAPH = 10;
		/**
		 * 微课班作业
		 */
		int WEIKE_CLASS = 101;

		/**
		 * 2XX都是智能学习的
		 * 每日一练
		 */
		int DAILY_WORK = 201;

		/**
		 * 学习作业
		 */
		int STUDY_WORK = 202;

		/**
		 * 复习作业
		 */
		int REVIEW_WORK = 203;

		/**
		 * 云私塾段落作业
		 */
		int PARAGRAPH_WORK = 205;


		/**
		 * 云私塾群刷题
		 */
		int AL_TRAIN_BRUSH_QUESTION = 206;

		/**
		 * 云私塾章节练习
		 */
		int AL_CHAPTER_WORK = 207;

		/**
		 * 云私塾自定义作业（作业批改）
		 */
		int AL_CUSTOM_WORK = 208;

		/**
		 * 云私塾知识点练题
		 */
		int AL_KNOWLEDGE_WORK = 209;

		/**
		 * 云私塾摸底测评
		 */
		int AL_DIAGNOSTIC_TEST = 210;

		/**
		 * 云私塾课前做题作业
		 */
		int PRE_CLASS_HOMEWORK = 211;

		/**
		 * 自定义作业（作业批改）（学习中心）
		 */
		int CUSTOM_WORK = 400;

		/**
		 * 知识点练题（学习中心）
		 */
		int KNOWLEDGE_WORK = 401;
		/**
		 * 知识点练题（学习中心-课表排课）
		 */
		int SCHEDULE_KNOWLEDGE_WORK = 402;
		/**
		 * 自定义论文作业（作业批改）（学习中心）
		 */
		int CUSTOM_PAPER_WORK = 403;

		/**
		 * 普通课的课题对照练习类型（学习中心）
		 */
		int ORDINARY_VIDEO_QUESTION_WORK = 404;

		/**
		 * 遗忘巩固复习
		 */
		int FORGETTING_CONSOLIDATE_REVIEW = 220;

		/**
		 * 举一反三做题
		 */
		int EXEMPLARY_ANALOGOUS = 221;

		/**
		 * 学习作业（普通+增强）
		 */
		int STUDY_WORK_MIX = 222;

		/**
		 * 学习作业（增强）
		 */
		int STUDY_WORK_ENHANCE = 223;

		/**
		 * 题集-专项练习
		 */
		int SPECIAL_EXERCISE = 224;

		/**
		 * 高频易错练习
		 */
		int HIGH_FREQUENCY_ERROR_QUESTION = 225;

		/**
		 * 自定义刷题
		 */
		int CUSTOM_QUESTION = 226;

		/**
		 * 课中弹题
		 */
		int LESSON_QUESTION = 227;
	}

	public interface DoneSource {
		int AlError = 32;//错题集
		int SPECIAL_PRACTICE = 43; //题集专项练习
		int HighFrequencyErrorQuestion = 44;//高频易错练习
		int CustomQuestion = 45;//自定义刷题
		int ExemplaryAnalogous = 46;//举一反三
		int LessonQuestion = 47;//课中弹题
	}
	
	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long objId;		// obj_id
	private Integer objType;		// obj_type （0：讲作业，1：段落作业，2：微课作业，3：个性化作业，4：新录播课作业，5：题库作业）
	private Long taskId;		// task_id
	private Double score;		// score
	private Long usetime;		// usetime
	private Long answerNum;		// 当前答题数
	private Date startTime;		// 用户开始答卷时间
	private Date endTime;		// 用户提交试卷时间
	private String comment;		// 老师评语
	private Integer state;		// 状态，0未开始 1进行中 2已交卷 3已评卷

	private List<UserAnswerDetail> answerDetail;

	private Integer isSubmit;

	private Long productId;		// 产品id
	private Long goodsId;// 商品id
	private String appid;   //app的类型 所属终端，web、PC客户端、环球网校APP、快题库、建造师题库…、快题库小程序
	private String platForm;	// app平台ios android

	private List<Long> objTypes;
	private List<Integer> states;

	private Integer from;
	private Integer pageSize;
	private String finishRate;

	private Long categoryId;
	private Integer isOnErrorList;// 是否是错题集的题目 0否 1是
	private Integer isOnCollectList;// 是否是收藏夹的题目 0否 1是
	private Integer errorListType;//错题集或收藏夹的类型 1场景归类 2题型归类
	private Integer errorType;//错题集或收藏夹的题目的类型 0试卷 1模考 2作业 3单项选择题 4多项选择题 5不定项选择题 6判断题 7填空题 8简答题 9案例题
	private Long studyPathId;	//云私塾任务id
	private Integer doneSource;	//做题来源 30-云私塾作业(任务上的作业) 32-云私塾错题集 33-云私塾收藏本 34-云私塾章节练习 35-云私塾学情回顾（每日一练）

	private Integer oldAutoRemoveVersion = 0;

	private Long firstCategoryId;		// 大类id
	private Long secondCategoryId;		// 科目id
	private Long relationId;// 资源关联id（新课程表的字段）
	private List<Long> productIdList;	//产品ids
	private Long homeworkId;		// 作业id
	private List<Long> idList;	//课后作业ids
	private String tagStr;
	private Integer extendType;
	private Integer isCount; //是否计数
	private Long questionId;

	private List<Long> homeworkIdList;

	private Constants.UserAnswerSubmitReason submitReason = Constants.UserAnswerSubmitReason.UserSubmit;

	public UserHomeWorkAnswer() {
		super();
	}

	public UserHomeWorkAnswer(Long id){
		super(id);
	}


}