package com.hqwx.study.entity.wxapp;

import cn.huanju.edu100.util.JSONUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/20 15:13
 * @description 裂变活动设置实体类
 */
@ApiModel(value = "FissionActivitySetting", description = "裂变活动设置实体类")
@Data
@Accessors(chain = true)
public class FissionActivitySetting {
    @Getter
    public static enum Type {
        PUSH(1); // 推送配置

        private final int value;

        Type(int value) {
            this.value = value;
        }
    }

    @ApiModelProperty(value = "裂变活动设置id")
    private Long id;
    /**
     * 活动id
     * activityType=1时，关联solution_wx_documents_activity.id;
     * activity_type=2时，关联solution_bargain_activity.id;
     * activity_type=3时，关联solution_activity_zuli_good.id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;
    /**
     * 活动类型，参考{@link FissionActivity.ActivityType}
     */
    @ApiModelProperty(value = "活动类型，1-拼团；2-砍价；3-助力")
    private Integer activityType;

    /**
     * 活动设置类型，参考{@link Type}
     */
    @ApiModelProperty(value = "活动设置类型，1-推送配置")
    private Integer type;

    @ApiModelProperty(value = "活动设置值，具体值根据type定义")
    private String value;

    /**
     * 业务使用该接口设置活动配置
     * @param setting 活动配置
     * @return this
     */
    public FissionActivitySetting setSetting(Setting setting) {
        assert Objects.equals(setting.getType(), this.getType());
        this.value = JSONUtils.toJsonString(setting);
        return this;
    }

    public <T extends Setting> T parseSetting(Class<T> clazz) {
        T setting = JSONUtils.parseObject(value, clazz);
        assert Objects.equals(setting.getType(), this.getType());
        return setting;
    }

    //-------- 以下为活动设置类型 --------
    public interface Setting {
        Integer getType();
    }
    /**
     * 推送配置
     */
    @ApiModel(value = "PushSetting", description = "推送配置结构")
    @Data
    @Accessors(chain = true)
    public static class PushSetting implements Setting {
        @ApiModelProperty(value = "是否启用")
        private Boolean enable;
        @ApiModelProperty(value = "推送标题")
        private String title;
        @ApiModelProperty(value = "标签")
        private List<String> tags;
        @ApiModelProperty(value = "开始时间")
        private Date startTime;
        @ApiModelProperty(value = "结束时间")
        private Date endTime;

        @Override
        public Integer getType() {
            return Type.PUSH.value;
        }

        public boolean isEnable() {
            if(enable == null || !enable) {
                return false;
            }
            if(startTime != null && startTime.getTime() > System.currentTimeMillis()) {
                return false;
            }
            if(endTime != null && endTime.getTime() < System.currentTimeMillis()) {
                return false;
            }
            return true;
        }
    }
}
