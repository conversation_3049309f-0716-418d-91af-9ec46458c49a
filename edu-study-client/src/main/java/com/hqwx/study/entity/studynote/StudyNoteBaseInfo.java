package com.hqwx.study.entity.studynote;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hqwx.study.dto.Page;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/19 11:54
 * @description 学习笔记基础数据类
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class StudyNoteBaseInfo {
    /**
     * 添加笔记的来源
     */
    @Getter
    public enum Source {
        /**
         * 题库
         */
        SOURCE_TIKU(1, "题库"),
        /**
         * 普通课
         */
        SOURCE_UC(2, "普通课"),
        /**
         * 云私塾智能化学习
         */
        SOURCE_AL(3, "云私塾");

        private final Integer value;
        private final String desc;

        private Source(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        @Override
        public String toString() {
            return getValue() + "." + this.desc;
        }

        public static Source getSource(Integer value) {
            for (Source source : Source.values()) {
                if (source.getValue().equals(value)) {
                    return source;
                }
            }
            return null;
        }
    }

    /**
     * 笔记类型
     */
    @Getter
    public enum Type {
        /**
         * 题目笔记，复用原有评论表 comment_element 中的定义
         */
        TYPE_QUESTION(2, "做题笔记"),

        /**
         * 视频笔记，v3.9.1 云私塾深度优化新增笔记类型
         */
        TYPE_VIDEO(8, "看课笔记");

        private final Integer value;
        private final String desc;

        private Type(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    StudyNoteBaseInfo(Type type) {
        this.type = type;
    }

    @JsonProperty
    @Getter
    private Type type;

    private Long id;
    /**
     * 笔记内容
     */
    private String text;

    /**
     * 点赞数量
     */
    private Integer thumbUpCount;

    /**
     * 标识当前查询的用户是否对当前笔记已点赞
     */
    private Integer hasThumbUp;

    private Long goodsGroupId;
    private Long goodsId;
    private Long productId;
    private Long categoryId;

    /**
     * 添加笔记的用户id
     */
    private Long uid;
    private String nickName;
    private String avatar;

    /**
     * 笔记来源
     */
    private Source source;

    /**
     * 笔记创建时间
     */
    private Date createTime;

    /**
     * 笔记更新时间
     */
    private Date updateTime;

    /**
     * 当前笔记的回复列表
     */
    private Page<StudyNoteBaseInfo> replyList;

    private Long schId;
    private Long pSchId;

    private String platform;

    private Boolean noteEditableFlag;
}
