package com.hqwx.study.entity.studynote;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/23 18:16
 * @description 点赞信息
 */
@Data
@Accessors(chain = true)
public class ThumbUpInfo {

    @Getter
    public enum Action {
        /**
         * 点赞
         */
        ACTION_UP(1, "点赞"),
        /**
         * 取消点赞
         */
        ACTION_CANCEL(2, "取消点赞");

        private final Integer value;
        private final String desc;

        private Action(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    Long noteId;
    Long uid;
    String ip;

    @JsonProperty
    Action action;
}
