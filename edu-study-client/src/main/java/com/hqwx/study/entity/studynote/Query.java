package com.hqwx.study.entity.studynote;

import com.hqwx.study.dto.Page;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/21 19:08
 * @description
 */
public interface Query {
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    abstract class QueryParam {
        QueryParam(StudyNoteBaseInfo.Type type) {
            this.type = type;
        }

        /**
         * 笔记类型，必填
         */
        @Setter(AccessLevel.NONE)
        private StudyNoteBaseInfo.Type type;

        /**
         * 当前请求的用户id
         */
        private Long uid;

        /**
         * 商品id
         */
        private Long goodsId;

        /**
         * 产品id
         */
        private Long productId;

        /**
         * 科目id
         */
        private Long categoryId;

        /**
         * 是否包含评论
         */
        private boolean includeReply = false;

        /**
         * 是否包含其他人的笔记
         */
        private boolean includeOthers = false;

        private Long schId;
        private Long pSchId;
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper=false)
    class QuestionQueryParam extends QueryParam {
        public QuestionQueryParam() {
            super(StudyNoteBaseInfo.Type.TYPE_QUESTION);
        }
        /**
         * 题目id
         */
        private Long questionId;

        private List<Long> questionIdList;
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper=false)
    class VideoQueryParam extends QueryParam {
        public VideoQueryParam() {
            super(StudyNoteBaseInfo.Type.TYPE_VIDEO);
        }
        /**
         * 视频id
         */
        private Long videoId;

        /**
         * 0:默认普通视频笔记 1:汉王图片笔记
         */
        private Integer commentVideoType;

    }
}
