package com.hqwx.study.entity.studynote;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/19 15:03
 * @description 做题笔记实体
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Accessors(chain = true)
public class QuestionNote extends StudyNoteBaseInfo {

    public QuestionNote() {
        super(Type.TYPE_QUESTION);
    }

    /**
     * 题目id
     */
    Long questionId;

    /**
     * 题干内容
     */
    String questionTitle;
}
