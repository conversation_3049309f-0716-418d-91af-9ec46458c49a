package com.hqwx.study.entity;

import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.util.GsonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 用户答案子题详情Entity
 * <AUTHOR>
 * @version 2015-05-12
 */
public class UserAnswerDetail extends DataEntity<UserAnswerDetail> {

    public interface IsRight {
		//批阅中
		int CORRECTING = -1;
		int WRONG = 0;
		int HALF_RIGHT = 1;
		int RIGHT = 2;
		int NOT_ANSWER = 3;

	}
	
	private static final long serialVersionUID = 1L;
	private Long sumId;		// sum_id
	private Long uid;		// uid
	private Long questionId;		// 题目id
	private Long topicId;		// 子题目id
	private String optionsId;		// options_id
	private String[] answer;		// 用户文本答案
	private String answerStr;		// 用户文本答案
	private String pic;		// 用户图片答案json
	private String mp3;		// 用户MP3答案json
	private String file;		// 用户文件答案json
	private Integer isRight;		// 用户答题正确与否(0：错误，1：部分正确，2：完全正确)
	private Double score;		// 用户得分
	private Long tuid;		// 老师uid
	private String comment;		// comment
	private Date commentTime;		// comment_time

	private Long exerciseId;
	private Long userAnswerId;
	private Long userHomeworkId;
	private Integer hasConsolidation;
    private String userAnswerIdStr;
    private Date startTime;
    private Date endTime;

	public Long getUserHomeworkId() {
		return userHomeworkId;
	}

	public void setUserHomeworkId(Long userHomeworkId) {
		this.userHomeworkId = userHomeworkId;
	}

	public Long getUserAnswerId() {
		return userAnswerId;
	}

	public void setUserAnswerId(Long userAnswerId) {
		this.userAnswerId = userAnswerId;
		if (userAnswerId != null) {
            this.userAnswerIdStr = String.valueOf(userAnswerId);
        }
	}

	public UserAnswerDetail() {
		super();
	}

	public UserAnswerDetail(Long uid, Long questionId, Long topicId, String[] answer, String pic, String mp3, String file) {
		this.uid = uid;
		this.questionId = questionId;
		this.topicId = topicId;
		this.answer = answer;
		this.pic = pic;
		this.mp3 = mp3;
		this.file = file;
	}

	public UserAnswerDetail(Long id){
		super(id);
	}

	public Long getSumId() {
		return sumId;
	}

	public void setSumId(Long sumId) {
		this.sumId = sumId;
	}
	
	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}
	
	public Long getTopicId() {
		return topicId;
	}

	public void setTopicId(Long topicId) {
		this.topicId = topicId;
	}

	public String getOptionsId() {
		return optionsId;
	}

	public void setOptionsId(String optionsId) {
		this.optionsId = optionsId;
	}
	
	public String[] getAnswer() {
		return answer;
	}

	public void setAnswer(String[] answer) {
		this.answer = answer;
	}
	
	public String getAnswerStr() {
		return answerStr;
	}

	public void setAnswerStr(String answerStr) {
		if (StringUtils.isNotBlank(answerStr)) {
			setAnswer(GsonUtil.getGson().fromJson(answerStr, String[].class));
		}
		this.answerStr = answerStr;
	}

	public String getPic() {
		return pic;
	}

	public void setPic(String pic) {
		this.pic = pic;
	}
	
	public String getMp3() {
		return mp3;
	}

	public void setMp3(String mp3) {
		this.mp3 = mp3;
	}
	
	public String getFile() {
		return file;
	}

	public void setFile(String file) {
		this.file = file;
	}
	
	public Integer getIsRight() {
		return isRight;
	}

	public void setIsRight(Integer isRight) {
		this.isRight = isRight;
	}
	
	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}
	
	public Long getTuid() {
		return tuid;
	}

	public void setTuid(Long tuid) {
		this.tuid = tuid;
	}
	
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
	
	public Date getCommentTime() {
		return commentTime;
	}

	public void setCommentTime(Date commentTime) {
		this.commentTime = commentTime;
	}

    public Integer getHasConsolidation() {
        return hasConsolidation;
    }

    public void setHasConsolidation(Integer hasConsolidation) {
        this.hasConsolidation = hasConsolidation;
    }
    public String getUserAnswerIdStr() {
        return this.userAnswerIdStr;
    }

    public void setUserAnswerIdStr(String userAnswerIdStr) {
        this.userAnswerIdStr = userAnswerIdStr;
    }

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Long getExerciseId() {
		return exerciseId;
	}

	public void setExerciseId(Long exerciseId) {
		this.exerciseId = exerciseId;
	}
}