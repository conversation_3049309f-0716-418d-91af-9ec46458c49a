<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>

	<groupId>cn.huanju.edu</groupId>
	<artifactId>edu-service-hq-parent</artifactId>
	<version>0.0.2</version>
	<packaging>pom</packaging>

	<properties>
		<slf4j_version>1.7.7</slf4j_version>
		<logback_version>1.2.2</logback_version>
		<springframework_version>4.3.9.RELEASE</springframework_version>
		<mysql_version>5.1.30</mysql_version>
		<c3p0_version>0.9.1.2</c3p0_version>
		<junit_version>4.12</junit_version>
		<aspectj_version>1.8.9</aspectj_version>
		<jserverlib_version>1.7.8</jserverlib_version>
		<commons_collections_version>3.2.1</commons_collections_version>
		<sharding.jdbc.version>1.4.1</sharding.jdbc.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<jserverlib.version>0.0.1</jserverlib.version>
		<mqhq.version>0.0.4</mqhq.version>
		<serviceagentsdk.version>1.5.3.1</serviceagentsdk.version>
		<udb.edu.monitor.version>0.0.2</udb.edu.monitor.version>
		<hawk.version>2.6.9</hawk.version>
	</properties>


	<modules>
	    <module>edu-study</module>
		<module>edu-study-client</module>
	</modules>
    <distributionManagement>
        <snapshotRepository>
            <id>music-public</id>
            <url>https://nexus.duowan.com/music/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>music-public</id>
            <url>https://nexus.duowan.com/music/content/repositories/releases/</url>
        </repository>
    </distributionManagement>


	<dependencies>
		<dependency>  
    		<groupId>com.google.code.gson</groupId>  
    		<artifactId>gson</artifactId>  
    		<version>2.6.2</version>
		</dependency>
	</dependencies>
</project>