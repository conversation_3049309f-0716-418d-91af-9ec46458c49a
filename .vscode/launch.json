{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "Test",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.Test",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "VideoSnapshotTest",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.VideoSnapshotTest",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "SM4EncryptCardTypeHandler",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.config.ibatis.encrypt.SM4EncryptCardTypeHandler",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "SM4EncryptNameTypeHandler",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.config.ibatis.encrypt.SM4EncryptNameTypeHandler",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "SM4EncryptPhoneTypeHandler",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.config.ibatis.encrypt.SM4EncryptPhoneTypeHandler",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "ExpressionMemberIbatisImpl",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.dao.ibatis.impl.expression.ExpressionMemberIbatisImpl",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "ServerBootstrap",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.entry.ServerBootstrap",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "GrpcTest",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.grpc.GrpcTest",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "UserAgreement",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.model.UserAgreement",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "TutorUserVideoLog",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.model.tutor.TutorUserVideoLog",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "QuestionAnswerStaticsServiceImpl",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.service.impl.QuestionAnswerStaticsServiceImpl",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "TutorStudentOverviewServiceImpl",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.service.impl.tutor.TutorStudentOverviewServiceImpl",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "SyncUserSubErrorQuestionTask",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.task.SyncUserSubErrorQuestionTask",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "BeanCopierUtils",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.util.BeanCopierUtils",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "DateUtil",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.util.DateUtil",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "NewRedisConsts",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.util.NewRedisConsts",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "RedisConsts",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.util.RedisConsts",
            "projectName": "edu-study"
        },
        {
            "type": "java",
            "name": "PdfUtil",
            "request": "launch",
            "mainClass": "cn.huanju.edu100.study.util.pdf.PdfUtil",
            "projectName": "edu-study"
        }
    ]
}