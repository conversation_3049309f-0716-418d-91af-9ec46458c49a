package cn.huanju.edu100.study;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.PaperSubmitStatisticInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class Test {

    /**
     * @param args
     */
    public static void main(String[] args) throws BusinessException {
        //"+i+"
//        for (int i = 1; i < 64; i=i+2) {
//            String sql = "ALTER TABLE `user_done_record_"+i+"`\n" +
//                    "MODIFY COLUMN `obj_type`  tinyint(1) NULL DEFAULT NULL COMMENT '类型 0-试卷 1-模考 2-作业 3-错题 4-收藏 30-云私塾章节练习 31-云私塾试卷 32-云私塾错题集 33-云私塾收藏本' AFTER `uid`,\n" +
//                    "ADD COLUMN `is_al`  tinyint(1) NULL DEFAULT 0 COMMENT '是否云私塾 0-否 1-是' ,\n" +
//                    "ADD COLUMN `study_path_id`  bigint(20) NULL DEFAULT 0 COMMENT '任务id' ;\n" +
//                    "\n";
//            System.out.println(sql);
//        }
        for (int i = 0; i < 64; i++) {
            String sql = "DELETE from user_study_record_"+i+";";
//            System.out.println(sql);
        }

        int min = 1;
        int max = 100;
        Random random = new Random();
        for(int i = 0; i < 100; ++i) {
            double score = random.nextDouble(max - min + 1) + min;
            Integer index = PaperSubmitStatisticInfo.score2Index(score, 100.0);
            System.out.println(i + ": " + score + " --> " + index);
        }
        for(int i = 0; i <= 10; ++i) {
            double score = i * 10;
            Integer index = PaperSubmitStatisticInfo.score2Index(score, 100.0);
            System.out.println(score + " --> " + index);
        }

        Double allTotalScore = 366.63;
        Long allSubmitCount = 7L;

        Double allAverageScore = allTotalScore/allSubmitCount;
        BigDecimal average = BigDecimal.valueOf(allTotalScore).divide(BigDecimal.valueOf(allSubmitCount), 0, RoundingMode.HALF_DOWN);
        Double newAverage = average.doubleValue();

        System.out.println("double allAverageScore=" + allAverageScore + ", BigDecimal average=" + average + ", newAverage=" + newAverage);
    }

}
