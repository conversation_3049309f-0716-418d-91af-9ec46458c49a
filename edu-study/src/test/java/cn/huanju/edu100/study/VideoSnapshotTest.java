package cn.huanju.edu100.study;

import cn.huanju.edu100.exception.BusinessException;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import java.net.URL;
import java.util.Date;

public class VideoSnapshotTest {

    /**
     * @param args
     */
    public static void main(String[] args) throws BusinessException {
        String endpoint = "https://oss-cn-beijing.aliyuncs.com";
        try{
            //EnvironmentVariableCredentialsProvider credentialsProvider= CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();
            String bucketName="oss-hqwx-video";
            String objectName="fbe7cb9f80843a3d03589fdeff8202277bb81fc5";
            String accessKeyId="LTAI4GDyrSdXwKS5rrikXrxE";
            String accessKeySecret="******************************";
            OSS ossClient=new OSSClientBuilder().build(endpoint,accessKeyId,accessKeySecret);
            String style="video/snapshot,t_1700,f_jpg,w_800,h_600";
            //设置有效期10年
            Date expiration=new Date(new Date().getTime()+3600l* 1000*24*365*10);
            GeneratePresignedUrlRequest req=new GeneratePresignedUrlRequest(bucketName,objectName, HttpMethod.GET);
            req.setExpiration(expiration);
            req.setProcess(style);
            URL signedUrl=ossClient.generatePresignedUrl(req);
            System.out.println(signedUrl);
            String url = signedUrl.getProtocol() + "://" + "oss-hqwx-video.hqwx.com";
            if (signedUrl.getPort() != -1) {
                url += ":" + signedUrl.getPort() + "/";
            }
            url += signedUrl.getPath() + "?" + signedUrl.getQuery();
            System.out.println("url:"+url);
            // 关闭OSSClient。
            ossClient.shutdown();
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }
}
