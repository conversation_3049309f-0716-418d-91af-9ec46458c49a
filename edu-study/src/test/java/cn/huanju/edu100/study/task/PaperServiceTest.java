package cn.huanju.edu100.study.task;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.repository.PaperSubmitCompareInfoRepository;
import cn.huanju.edu100.study.service.PaperService;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hqwx.study.dto.PaperSubmitCompareInfo;
import com.hqwx.study.dto.UserAnswerSumDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/12 18:01
 * @description
 */
public class PaperServiceTest extends BaseTest {
    @Autowired
    SavePaperSubmitStatisticTask savePaperSubmitStatisticTask;

    @Autowired
    PaperService paperService;

    @Autowired
    PaperSubmitCompareInfoRepository paperSubmitCompareInfoRepository;

    @Test
    public void testSaveTask() throws Exception {
        savePaperSubmitStatisticTask.execute("");
    }

    @Test
    public void testGetComparableInfo() throws BusinessException {
        PaperSubmitCompareInfo paperSubmitCompareInfo = new PaperSubmitCompareInfo();
        paperSubmitCompareInfo.setPaperId(65606L);
        paperSubmitCompareInfo.setTotalScore(100.0);
        paperSubmitCompareInfo.setScore(20.00);
        paperSubmitCompareInfo.setPaperAccuracy(0.0);
        paperSubmitCompareInfo = paperService.getPaperSubmitCompareInfo(
                paperSubmitCompareInfo.getPaperId(),
                paperSubmitCompareInfo.getScore(),
                paperSubmitCompareInfo.getTotalScore(),
                paperSubmitCompareInfo.getPaperAccuracy());
        System.out.println(paperSubmitCompareInfo);
    }

    @Test
    public void incrementSubmitCount() {
        paperSubmitCompareInfoRepository.increaseSubmitCount(2148L, 66.66, 100.0);
    }

    @Test
    public void findPaperLastAnswerSum() throws Exception {
        List<UserAnswerSumDTO> list = paperService.getPaperLastAnswerSummary(
                11498706L, Arrays.asList(42653L, 42652L), 3, 1);
        {
            String json = GsonUtil.getGson().toJson(list);
            System.out.println("testFindPaperAnswerSummaryList list: " + json);
        }

        Map<Long, List<Long>> userAnswerIdGroup = Maps.newHashMap();
        for(UserAnswerSumDTO sum : list) {
            List<Long> l = userAnswerIdGroup.get(sum.getUserAnswerId());
            if(l == null) {
                l = Lists.newArrayList();
                userAnswerIdGroup.put(sum.getUserAnswerId(), l);
            }
            l.add(sum.getQuestionId());
        }

        String json = GsonUtil.getGson().toJson(userAnswerIdGroup);
        System.out.println("findPaperLastAnswerSum result: " + json);
    }
}
