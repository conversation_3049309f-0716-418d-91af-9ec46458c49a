package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100StudyServiceGrpc;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.thrift.edu100_study;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.channel.ChannelOption;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TFramedTransport;
import org.apache.thrift.transport.TSocket;
import org.apache.thrift.transport.TTransport;
import org.apache.thrift.transport.TTransportException;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class UserQuestionBoxTestCase {

	private static Gson gson = GsonUtil.getGson();

//	public static String URL = "58.215.169.173";//58.215.169.173 58.215.169.174 ************** **************
//	public static String URL = "**************";
	public static String URL = "127.0.0.1";
//	 public static String URL = "***************";
	public static int PORT = 8285;

	// public static String URL = "**************";
	// public static int PORT = 12300;

	/**
	 * 测试2.2.1 用户收藏题目接口：正常用例
	 */
	@Test
	public void sty_userCollectQuestion_withSuccess() {
		NettyChannelBuilder builder = null;
		try {
			builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
			((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
			ManagedChannel channel = builder.build();
			Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
					= Edu100StudyServiceGrpc.newBlockingStub(channel);
			GrpcRequest.Builder req = GrpcRequest.newBuilder();
			req.setAppid(6);
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", 11587537);
//			param.put("box_id", 0);
			param.put("is_al", 1);
			// 594, 596
			param.put("question_id", 111);
			param.put("is_collect", 1);
			req.setMsg(gson.toJson(param));
			GrpcResponse res = client.styUserCollectQuestion(req.build());
			System.out.println(JsonFormat.printer().print(res));
		} catch (InvalidProtocolBufferException e) {
			e.printStackTrace();
		}
	}
//
//	/**
//	 * 测试2.2.3	根据题目id List判断题目是否收藏
//	 * sty_userIsCollectQuestion=response(code:0, errormsg:null, codetype:0, msg:{"119":true,"596":false})
//	 */
//	@Test
//	public void sty_userIsCollectQuestion() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket("106.120.191.125", PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 12129111);
//			param.put("box_id", 601);
//			// 594, 596
//			List<Long> ids = new ArrayList<Long>();
//			ids.add(119L);
//			ids.add(596L);
//			param.put("question_ids", ids);
//			// req.setMsg(gson.toJson(param));
//			req.setMsg("{\"uid\":11498706,\"box_id\":0,\"question_ids\":[2807842,2062458,2083065,2696052,2519111,2062503,2074222,2071805],\"is_al\":1}");
//			response res = client.sty_userIsCollectQuestion(req);
//			System.out.println("sty_userIsCollectQuestion=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//
//	/**
//	 * 测试2.2.2	根据题库id获取用户在该题库下收藏的题目：只获取总数用例
//	 */
//	@Test
//	public void sty_getUserQuestionCollectByBoxId_onlyTotal() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 12129111);
//			param.put("box_id", 0);
//			param.put("is_total", 0);
//			param.put("from", 0);
//			param.put("rows", 10);
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserQuestionCollectByBoxId(req);
//			System.out.println("sty_getUserQuestionCollectByBoxId=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	/**
//	 * 测试2.2.2	根据题库id获取用户在该题库下收藏的题目：获取总数和题目id集合用例
//	 */
//	@Test
//	public void sty_getUserQuestionCollectByBoxId_notOnlyTotal() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
////			param.put("uid", 11065863);
////			param.put("box_id", 2821);
//
//			param.put("uid", 102361432);
//			param.put("box_id", 1946);
//			param.put("is_total", 1);
//			param.put("from", 0);
//			param.put("rows", 10);
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserQuestionCollectByBoxId(req);
//			System.out.println("sty_getUserQuestionCollectByBoxId=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	/**
//	 * 测试2.2.2	根据题库id获取用户在该题库下收藏的题目：下标越界用例
//	 */
//	@Test
//	public void sty_getUserQuestionCollectByBoxId_withInvalidParam() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 10000);
//			param.put("box_id", 282);
//			param.put("is_total", 1);
//			param.put("from", -1);
//			//param.put("rows", 2);
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserQuestionCollectByBoxId(req);
//			System.out.println("sty_getUserQuestionCollectByBoxId=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	/**
//	 * 测试2.2.17 获取用户上次未完成的练习/试卷记录接口
//	 */
//	@Test
//	public void sty_getUserLastPractice() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 10000);
//			param.put("box_id", 282);
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserLastPractice(req);
//			System.out.println("sty_getUserLastPractice=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	/**
//	 * 测试2.2.17 获取用户上次未完成的练习/试卷记录接口：缺少参数用例
//	 */
//	@Test
//	public void sty_getUserLastPractice_withInvalidParam() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			//param.put("uid", 10000);
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserLastPractice(req);
//			System.out.println("sty_getUserLastPractice=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	/**
//	 * 2.2.6	根据策略随机抽取X道题目形成练习，并返回题目详情
//	 */
//	@Test
//	public void sty_getRamdonBoxQuestionList() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
////			param.put("uid", 11065863);
////			param.put("box_id", 1862);
////			param.put("teach_book_id", 4584);
////			param.put("obj_id", 0);
////			param.put("obj_type", Consts.Question_Exercise_Type.ALL);
////			param.put("num", 25);
////			param.put("random_type", 0);
//			//正式
////			param.put("uid", 11587537);
////			param.put("box_id", 1364);
////			param.put("teach_book_id", 5608);
////			param.put("obj_id", 62781);
////			param.put("obj_type", Consts.Question_Exercise_Type.Chapter);
////			param.put("num", 5);
////			param.put("random_type", 2);
//			//测试
//			param.put("uid", 11587537);
//			param.put("box_id", 1206);
//			param.put("teach_book_id", 106);
//			param.put("obj_id", 301); //691	知识点
//			param.put("obj_type", Consts.Question_Exercise_Type.Chapter);
//			param.put("num", 5);
//			param.put("random_type", 2);
//
//			/**知识点练习*/
////			param.put("uid", 11065863);
////			param.put("box_id", 282);
////			param.put("teach_book_id", 15);
////			param.put("obj_id", 314);
////			param.put("obj_type", Consts.Question_Exercise_Type.Knowledge);
////			param.put("num", 5);
////			param.put("random_type", 2);
//
//			/**知章节练习*/
////			param.put("uid", 11065863);
////			param.put("box_id", 282);
////			param.put("teach_book_id", 15);
////			param.put("obj_id", 20);
////			param.put("obj_type", Consts.Question_Exercise_Type.Chapter);
////			param.put("num", 5);
////			param.put("random_type", 2);
//
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getRamdonBoxQuestionList(req);
//			System.out.println("sty_getRamdonBoxQuestionList=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	@Test
//	public void sty_getUserAnswerBoxQuestionInfo() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 100745147);
//			param.put("box_id", 4983);
//			param.put("teach_book_id", 7121);
//			param.put("obj_id", 63915);
//			param.put("obj_type", 1);
//			param.put("is_total", 1);
//			param.put("from", 0);
//			param.put("rows", 50);
//
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserAnswerBoxQuestionInfo(req);
//			System.out.println("sty_getUserAnswerBoxQuestionInfo=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	@Test
//	public void sty_getUserWrongBoxQuestionInfo() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket("39.107.240.91", PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			/*param.put("uid", 11065863L);
//			param.put("box_id", 1862L);
//			param.put("obj_id", 60639L);
//			param.put("obj_type", 1);
//			param.put("teach_book_id", 4584L);
//			param.put("is_total", 0);*/
//
//			/*param.put("uid", 11065863L);
//			param.put("box_id", 475L);
//			param.put("obj_id", 104L);
//			param.put("obj_type", 1);
//			param.put("from", 0);
//			param.put("rows", 5);
//			param.put("teach_book_id", 38L);
//			param.put("is_total", 1);*/
//
//			/*param.put("uid", 11587537L);
//			param.put("box_id", 1943L);
//			param.put("obj_id", 4283L);
//			param.put("obj_type", 1);
//			param.put("from", 0);
//			param.put("rows", 5);
//			param.put("teach_book_id", 1167L);
//			param.put("is_total", 1);*/
//
//			param.put("uid", 11065867);
//			param.put("box_id", 1074L);
//			param.put("obj_id", 485L);
//			param.put("obj_type", 0);
//			param.put("from", 0);
//			param.put("rows", 5);
//			param.put("teach_book_id", 84L);
//			param.put("is_total", 1);
//
//			// req.setMsg(gson.toJson(param));
//            req.setMsg("{\"uid\":101536104,\"box_id\":1941,\"obj_type\":3,\"obj_id\":0,\"is_total\":1,\"from\":10,\"rows\":10} ");
//			response res = client.sty_getUserWrongBoxQuestionInfo(req);
//			System.out.println("sty_getUserWrongBoxQuestionInfo=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	@Test
//	public void sty_getUserBoxExerciseList() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
////			param.put("uid", 11788681);
////			param.put("box_id", 282);
////			param.put("state", 2);
////			param.put("from", 0);
////			param.put("rows", 15);
//
//			param.put("uid", 100001320L);
//			param.put("box_id", 1530);
//            param.put("random_type", 3L);//0-4 ，9(查询用户的章节练习记录用)
//			param.put("state", 2);
//			param.put("from", 0);
//			param.put("rows", 10);
//
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserBoxExerciseList(req);
//			System.out.println("sty_getUserBoxExerciseList=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	@Test
//	public void sty_getUserAnswerHis() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			List<Long> questionIds = new ArrayList<Long>();
//			questionIds.add(2104409L);
////			questionIds.add(113L);
////			questionIds.add(114L);
////			questionIds.add(171L);
////			questionIds.add(142L);
////			questionIds.add(309L);
//			Map<String, Object> param = new HashMap<String, Object>();
////			param.put("uid", 50012132);
//			param.put("uid", 11065863);
//			param.put("question_ids", questionIds);
//
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserAnswerHis(req);
//			System.out.println("sty_getUserAnswerHis=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	@Test
//	public void sty_getUserBoxHomeworkById() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 11065863L);
//			param.put("homework_id", 3);
//
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserBoxHomeworkById(req);
//			System.out.println("sty_getUserBoxHomeworkById=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//	/**
//	 * 测试2.2.2	根据题库id获取用户收藏的题目：
//	 */
//	@Test
//	public void sty_getUserCollectQuestionForAL() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 11587537);
//			param.put("is_total", 1);
//			param.put("is_al", 1);
//			param.put("from", 0);
//			param.put("rows", 2);
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserCollectQuestionForAL(req);
//			System.out.println("sty_getUserCollectQuestionForAL=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	@Test
//	public void sty_getUserQuestionForAL() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket(URL, PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 11587537);
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_getUserQuestionForAL(req);
//			System.out.println("sty_getUserQuestionForAL=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}
//
//	@Test
//	public void sty_geOtherWrongBoxQuestion() {
//		TTransport transport;
//		try {
//			transport = new TFramedTransport(new TSocket("39.105.11.127", PORT));
//			TProtocol protocol = new TBinaryProtocol(transport);
//			edu100_study.Client client = new edu100_study.Client(protocol);
//			transport.open();
//
//			request req = new request();
//			req.setAppid(6);
//			Map<String, Object> param = new HashMap<String, Object>();
//			param.put("uid", 11587537);
//			param.put("boxId",1530);
//			param.put("bookId",180);
//			req.setMsg(gson.toJson(param));
//			response res = client.sty_geOtherWrongBoxQuestion(req);
//			System.out.println("sty_geOtherWrongBoxQuestion=" + res);
//
//			transport.close();
//		} catch (TTransportException e) {
//			e.printStackTrace();
//		} catch (TException e) {
//			e.printStackTrace();
//		}
//	}

}
