package cn.huanju.edu100.study.dao.df;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserAnswerDetailDao;
import com.hqwx.study.entity.UserAnswerDetail;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserAnswerDetailDaoTest extends BaseTest {
    @Autowired
    private UserAnswerDetailDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 223748451194634240L);
        param.put("uid", 101567141L);
        UserAnswerDetail ua = dao.getShardingById(param);
        Assertions.assertEquals(223748450842312704L, ua.getSumId());
    }
    @Test
    public void findStudyCenterLastHomeworkAnswerDetailList() throws Exception{
        List<Long> ua = dao.findStudyCenterLastHomeworkAnswerDetailList(1l, List.of(1l));
        System.out.println();
    }
}
