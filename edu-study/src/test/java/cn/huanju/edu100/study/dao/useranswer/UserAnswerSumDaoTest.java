package cn.huanju.edu100.study.dao.useranswer;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserAnswerSumDao;
import cn.huanju.edu100.study.entry.ServerBootstrap;
import cn.huanju.edu100.study.model.UserAnswerSum;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hqwx.study.dto.UserAnswerSumDTO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest(classes = ServerBootstrap.class)
public class UserAnswerSumDaoTest extends BaseTest {
    @Autowired
    private UserAnswerSumDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 23316L);
        param.put("uid", 11587537L);
        UserAnswerSum ua = dao.getShardingById(param);
        Assertions.assertEquals(1364L, ua.getUserHomeworkId());
    }

    @Test
    public void testFindUserAllQuestionHistory() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("from", 0);
        param.put("pageSize", 500);
        param.put("pageNo", 1);
        param.put("uid", 225078477L);
        dao.findUserAllQuestionHistory(param);
    }

    @Test
    public void findAlSubmitQuestionListByPaperOrHomework() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("uid", 100753984l);
        dao.findAlSubmitQuestionListByPaperOrHomework(param);
    }

    @Test
    public void testFindPaperAnswerSummaryList() throws Exception {
        List<UserAnswerSum> list = dao.findPaperAnswerSummaryList(151236240L, Arrays.asList(42647L));
        {
            String json = GsonUtil.getGson().toJson(list);
            System.out.println("testFindPaperAnswerSummaryList list: " + json);
        }

        Map<Long, List<Long>> userAnswerIdGroup = Maps.newHashMap();
        for(UserAnswerSum sum : list) {
            List<Long> l = userAnswerIdGroup.get(sum.getUserAnswerId());
            if(l == null) {
                l = Lists.newArrayList();
                userAnswerIdGroup.put(sum.getUserAnswerId(), l);
            }
            l.add(sum.getQuestionId());
        }

        String json = GsonUtil.getGson().toJson(userAnswerIdGroup);
        System.out.println("testFindPaperAnswerSummaryList result: " + json);
    }

    @Test
    public void testFindPaperLastAnswerSum() throws Exception {
        List<UserAnswerSum> list = dao.findPaperAnswerSumDetail(11498706L, Arrays.asList(42653L, 42652L));
        {
            String json = GsonUtil.getGson().toJson(list);
            System.out.println("testFindPaperAnswerSummaryList list: " + json);
        }

        Map<Long, List<Long>> userAnswerIdGroup = Maps.newHashMap();
        for(UserAnswerSum sum : list) {
            List<Long> l = userAnswerIdGroup.get(sum.getUserAnswerId());
            if(l == null) {
                l = Lists.newArrayList();
                userAnswerIdGroup.put(sum.getUserAnswerId(), l);
            }
            l.add(sum.getQuestionId());
        }

        String json = GsonUtil.getGson().toJson(userAnswerIdGroup);
        System.out.println("testFindPaperAnswerSummaryList result: " + json);
    }
}
