package cn.huanju.edu100.study.dao.qbox;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.questionBox.UserWrongQuestion;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class UserWrongQuestionDaoTest extends BaseTest {
    @Autowired
    private UserWrongQuestionDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 883417834003890178L);
        param.put("uid", 203699231L);
        UserWrongQuestion ua = dao.getShardingById(param);
        Assertions.assertEquals("qtype:wrong:0", ua.getKey());
    }

    @Test
    public void testGetByUidQboxIdAndKey() throws Exception{
        dao.getByUidQboxIdAndKey(123L, 456L, "abc");
    }

    @Test
    public void testGetByUidQboxIdAndKeyLike() throws Exception{
        dao.getByUidQboxIdAndKeyLike(123L, 234L, "abc");
    }

    @Test
    public void testGetByUidQboxIdListAndKeyLike() throws Exception{
        dao.getByUidQboxIdListAndKeyLike(123L, Arrays.asList(234L), "abc");
    }

    @Test
    public void testGetKeysByUidAndQboxId() throws Exception{
        dao.getKeysByUidAndQboxId(123L, 234L);
    }

    @Test
    public void testGetFromMasterDbByUidQboxIdAndKey() throws Exception{
        dao.getFromMasterDbByUidQboxIdAndKey(123L, 234L, "abc");
    }
}
