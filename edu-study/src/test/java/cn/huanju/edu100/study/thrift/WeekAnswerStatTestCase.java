package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100StudyServiceGrpc;
import cn.huanju.edu100.thrift.edu100_study;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.channel.ChannelOption;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TFramedTransport;
import org.apache.thrift.transport.TSocket;
import org.apache.thrift.transport.TTransport;
import org.apache.thrift.transport.TTransportException;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 用户周答题统计
 * Created by caijunhua on 2018/6/14.
 */
public class WeekAnswerStatTestCase {
    private static Gson gson = GsonUtil.getGson();
    // public static String URL = "221.228.86.187";// 221.228.86.187, 127.0.0.1
//    public static String URL = "127.0.0.1";
//    public static String URL = "**************";
    public static String URL = "**************"; //***************，**************，***************，**************，**************，**************，*************
    public static int PORT = 8285;

    //获取用户上周答题信息
    @Test
    public void sty_getAnswerInfoLastWeek() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();

//            param.put("box_id", 1206L);
//            param.put("teach_book_id", 106L);
//            param.put("uid", 11587537L);

//            param.put("box_id", 1348L);
//            param.put("teach_book_id", 162L);
//            param.put("uid", 101161851L);

            //正式
            param.put("box_id", 1943L);
            param.put("teach_book_id", 1167L);
            param.put("uid", 101567141L);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.styGetAnswerInfoLastWeek(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
//
//    //获取用户周答题趋势
//    @Test
//    public void sty_getAnswerTrendWeek() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//
//            param.put("box_id", 1206L);
//            param.put("teach_book_id", 106L);
//            param.put("uid", 11587537L);
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getAnswerTrendWeek(req);
//            System.out.println("sty_getAnswerTrendWeek>>>" + res.getMsg());
//            System.out.println("sty_getAnswerTrendWeek>>>gson>>>" + gson.toJson(res));
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    //批量)获取用户上周已做错的题目总数
//    @Test
//    public void sty_batchWeekWrongBoxQuestionCnt() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//
//            param.put("box_id", 1206L);
//            param.put("teach_book_id", 106L);
//            param.put("uid", 11587537L);
//            param.put("obj_type", 1); //章节1/知识点2
//            Integer[] obj_ids = {300};
//            param.put("obj_ids", obj_ids); //章节ids
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_batchWeekWrongBoxQuestionCnt(req);
//            System.out.println("sty_batchWeekWrongBoxQuestionCnt>>>res>>>" + res.toString());
//            System.out.println("sty_batchWeekWrongBoxQuestionCnt>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    //(批量)获取用户上周已做过的题目总数
//    @Test
//    public void sty_batchWeekAnswerBoxQuestionCnt() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//
////            param.put("box_id", 1206L);
////            param.put("teach_book_id", 106L);
////            param.put("uid", 11587537L);
////            param.put("obj_type", 1); //章节1/知识点2
////            Integer[] obj_ids = {300};
////            param.put("obj_ids", obj_ids); //章节ids
//
//            param.put("box_id", 1364L);
//            param.put("teach_book_id", 5608L);
//            param.put("uid", 11921827L);
//            param.put("obj_type", 1); //章节1/知识点2
//            Integer[] obj_ids = {63068,63080,63110,63116,63117,63128,63136,63146,63152,63163,63186,63206,62778,62787,62795,62812,62828,62836,62845,62857,62870,62880,62890,62907,62916,62927,62941,62950,62967,62978,62986,63028,63038,63044,63050,63055,63063,63069,63082,63112,63118,63127,63130,63138,63147,63154,63164,63188,63207,62779,62788,62799,62815,62829,62848,62858,62872,62891,62908,62917};
//            param.put("obj_ids", obj_ids); //章节ids
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_batchWeekAnswerBoxQuestionCnt(req);
//            System.out.println("sty_batchWeekAnswerBoxQuestionCnt>>>" + res.getMsg());
//            System.out.println("sty_batchWeekAnswerBoxQuestionCnt>>>gson>>>" + gson.toJson(res));
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    //获取用户上周做错的题目(wrong)
//    @Test
//    public void sty_getWeekWrongBoxQuestionInfo() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//
//            param.put("box_id", 1206L);
//            param.put("teach_book_id", 106L);
//            param.put("uid", 11587537L);
//            param.put("obj_type", 1); //章节1/知识点2
//            param.put("obj_id", 300); //章节id
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getWeekWrongBoxQuestionInfo(req);
//            System.out.println("sty_getWeekWrongBoxQuestionInfo>>>" + res.getMsg());
//            System.out.println("sty_getWeekWrongBoxQuestionInfo>>>gson>>>" + gson.toJson(res));
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    //(批量)获取用户已消灭错的题目总数
//    @Test
//    public void sty_batchWipeOutWrongBoxQuestionCnt() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//
////            param.put("box_id", 1206L);
////            param.put("teach_book_id", 106L);
////            param.put("uid", 11587537L);
////            param.put("obj_type", 1); //章节1/知识点2
////            Integer[] obj_ids = {300};
////            param.put("obj_ids", obj_ids); //章节ids
//
//            //正式
//            param.put("box_id", 1943L);
//            param.put("teach_book_id", 1167L);
//            param.put("uid", 11587537L);
//            param.put("obj_type", 1); //章节1/知识点2
//            Integer[] obj_ids = {4283};
//            param.put("obj_id", obj_ids); //章节id
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_batchWipeOutWrongBoxQuestionCnt(req);
//            System.out.println("sty_batchWipeOutWrongBoxQuestionCnt>>>" + res.getMsg());
//            System.out.println("sty_batchWipeOutWrongBoxQuestionCnt>>>gson>>>" + gson.toJson(res));
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    //获取用户已消灭做错的题目(wipeOut)
//    @Test
//    public void sty_getWipeOutWrongBoxQuestionInfo() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            //测试
////            param.put("box_id", 1206L);
////            param.put("teach_book_id", 106L);
////            param.put("uid", 11587537L);
////            param.put("obj_type", 1); //章节1/知识点2
////            param.put("obj_id", 300); //章节id
//
//            //正式
//            param.put("box_id", 1943L);
//            param.put("teach_book_id", 1167L);
//            param.put("uid", 101567141L);
//            param.put("obj_type", 1); //章节1/知识点2
//            param.put("obj_id", 4283); //章节id
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getWipeOutWrongBoxQuestionInfo(req);
//            System.out.println("sty_getWipeOutWrongBoxQuestionInfo>>>" + res.getMsg());
//            System.out.println("sty_getWipeOutWrongBoxQuestionInfo>>>gson>>>" + gson.toJson(res));
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    //用户对题库已消灭错题进行主动移除(批量obj_ids+单个question_id)
//    @Test
//    public void sty_rmErrWipeOutQuestionInBoxBatchObj() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//
//            param.put("box_id", 1206L);
//            param.put("teach_book_id", 106L);
//            param.put("uid", 11587537L);
//            param.put("obj_type", 1); //章节1/知识点2
//            Integer[] obj_ids = {300};
//            param.put("obj_ids", obj_ids); //章节ids
//            param.put("question_id", 2480255); //题目id
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_rmErrWipeOutQuestionInBoxBatchObj(req);
//            System.out.println("sty_rmErrWipeOutQuestionInBoxBatchObj>>>" + res.getMsg());
//            System.out.println("sty_rmErrWipeOutQuestionInBoxBatchObj>>>gson>>>" + gson.toJson(res));
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }

}
