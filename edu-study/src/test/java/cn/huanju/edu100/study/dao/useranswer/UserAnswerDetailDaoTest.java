package cn.huanju.edu100.study.dao.useranswer;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserAnswerDetailDao;
import cn.huanju.edu100.study.util.GsonUtils;
import com.hqwx.study.entity.UserAnswerDetail;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class UserAnswerDetailDaoTest extends BaseTest {
    @Autowired
    private UserAnswerDetailDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 223748451194634240L);
        param.put("uid", 101567141L);
        UserAnswerDetail ua = dao.getShardingById(param);
        Assertions.assertEquals(223748450842312704L, ua.getSumId());
    }

    @Test
    public void insertSharding() throws Exception{
        String json = "{ \"sumId\": 1372508983879254017, \"uid\": 187234461, \"questionId\": 3814378, \"topicId\": 2951035, \"answer\": [\"哈哈哈哈哈哈哈哈哈\"], \"answerStr\": \"[\\\"哈哈哈哈哈\\\"]\", \"isRight\": 2, \"score\": 20.0, \"userAnswerId\": 1372508983233331200, \"userAnswerIdStr\": \"1372508983233331200\", \"delFlag\": \"0\", \"id\": 1372508983918002752, \"page\": { \"from\": 0, \"pageNo\": 1, \"pageSize\": -1, \"count\": 0, \"needCount\": true, \"first\": 0, \"last\": 0, \"prev\": 0, \"next\": 0, \"firstPage\": false, \"lastPage\": false, \"length\": 8, \"slider\": 1, \"list\": [], \"orderBy\": \"\", \"funcName\": \"page\", \"funcParam\": \"\", \"message\": \"\" }, \"isNewRecord\": false }";
        UserAnswerDetail userAnswerDetail = GsonUtils.fromJson(json, UserAnswerDetail.class);
        dao.insertSharding(userAnswerDetail, userAnswerDetail.getClass());
        System.out.println(userAnswerDetail.getId());
    }
}
