package cn.huanju.edu100.study.dao.qbox;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.questionBox.UserWipeOutWrongQuestion;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class UserWipeoutWrongQuestionDaoTest extends BaseTest {
    @Autowired
    private UserWipeOutWrongQuestionDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 903354299588542486L);
        param.put("uid", 151115563L);
        UserWipeOutWrongQuestion ua = dao.getShardingById(param);
        Assertions.assertEquals("qtype:wrong:0", ua.getKey());
    }

    @Test
    public void testGetByUidQboxIdAndKey() throws Exception{
        dao.getByUidQboxIdAndKey(123L, 234L, "abc");
    }

    @Test
    public void testGetByUidQboxIdAndKeyLike() throws Exception{
        dao.getByUidQboxIdAndKeyLike(123L, 234L, "abc");
    }

    @Test
    public void testGetByUidQboxIdListAndKeyLike() throws Exception{
        dao.getByUidQboxIdListAndKeyLike(123L, Arrays.asList(234L), "abc");
    }

    @Test
    public void testGetKeysByUidAndQboxId() throws Exception{
        dao.getKeysByUidAndQboxId(123L, 234L);
    }

    @Test
    public void testGetFromMasterDbByUidQboxIdAndKey() throws Exception{
        dao.getFromMasterDbByUidQboxIdAndKey(123L, 234L, "abc");
    }
}
