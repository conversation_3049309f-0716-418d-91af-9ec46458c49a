package cn.huanju.edu100.study.dao.keys;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.qbox.SynTaskInfoDao;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class SynTaskInfoDaoTest extends BaseTest {
    @Autowired
    private SynTaskInfoDao dao;

    @Test
    public void testSharding() throws Exception{
        Map map = new HashMap<>();
        map.put("id", 123);
        map.put("uid", 234L);
        dao.getShardingById(map);
    }
}
