package cn.huanju.edu100.study.dao.qbox;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.questionBox.UserDoneQuestion;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class UserDoneQuestionDaoTest extends BaseTest {
    @Autowired
    private UserDoneQuestionDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 267250838576037891L);
        param.put("uid", 151114307L);
        UserDoneQuestion ua = dao.getShardingById(param);
        Assertions.assertEquals("qtype:done:1", ua.getKey());
    }

    @Test
    public void testGetByUidQboxIdAndKey() throws Exception{
        dao.getByUidQboxIdAndKey(123L, 234L, "abc");
    }

    @Test
    public void testGetByUidQboxIdAndKeyLike() throws Exception{
        dao.getByUidQboxIdAndKeyLike(123L, 234L, "abc");
    }

    @Test
    public void testGetByUidQboxIdListAndKeyLike() throws Exception{
        dao.getByUidQboxIdListAndKeyLike(123L, Arrays.asList(234L), "bcd");
    }

    @Test
    public void testGetKeysByUidAndQboxId() throws Exception{
        dao.getKeysByUidAndQboxId(123L, 234L);
    }

    @Test
    public void testGetFromMasterDbByUidQboxIdAndKey() throws Exception{
        dao.getFromMasterDbByUidQboxIdAndKey(123L, 234L, "abc");
    }
}
