package cn.huanju.edu100.study.dao.keys;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.UserAgreement;
import cn.huanju.edu100.study.service.UserAgreementService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserAgreementDaoTest extends BaseTest {
    @Autowired
    private UserAgreementService dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 46L);
        param.put("uid", 11173283L);
        UserAgreement ua = dao.getById((Serializable) param.get("id"));
        Assertions.assertEquals("18680179552", ua.getPhone());
    }

    @Test
    public void testListByUidOrderIdFromMaster() throws Exception{
        dao.listByUidOrderIdFromMaster(123L, 234L);
    }

    @Test
    public void testByUid() throws DataAccessException {
        dao.sty_getUserAgreementsByUid(1L,1L,"1");
    }

    @Test
    public void testByUid2() throws DataAccessException {
        List<Long> list = new ArrayList<>();
        list.add(1L);
        list.add(2L);
        dao.getUserAgreementsByUid(1L,list,"1");
        dao.getUserAgreementsByUid(1L,new ArrayList<>(),"1");
        dao.getUserAgreementsByUid(1L,null,"1");
    }
//    @Test
//    public void testListByUidAndGoodsIdFromMaster() throws Exception{
//        Long[] input = new Long[2];
//        input[0] = 233L;
//        input[1] = 3454L;
//
//        dao.listByUidAndGoodsIdFromMaster(123L, input, 345L);
//    }
}
