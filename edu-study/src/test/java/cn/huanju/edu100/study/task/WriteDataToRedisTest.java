package cn.huanju.edu100.study.task;

import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.questionBox.UserDoneQuestion;
import cn.huanju.edu100.study.model.questionBox.UserReciteQuestion;
import cn.huanju.edu100.study.model.questionBox.UserWrongQuestion;
import cn.huanju.edu100.study.service.qbox.UserDoneQuestionService;
import cn.huanju.edu100.study.service.qbox.UserReciteQuestionService;
import cn.huanju.edu100.study.service.qbox.UserWrongQuestionService;
import cn.huanju.edu100.study.util.RedisConsts;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 18/11/2
 */
public class WriteDataToRedisTest extends BaseTest {

    @Autowired
    private UserDoneQuestionService userDoneQuestionService;

    @Autowired
    private UserWrongQuestionService userWrongQuestionService;

    @Autowired
    private UserReciteQuestionService userReciteQuestionService;

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    @Test
    public void writeDataToRedis() {
        List<String> keys = Lists.newArrayList();
        keys.add("qBox_d_11587537_1242");
        keys.add("qBox_w_101308914_1198");
        keys.add("qBox_d_103067234_1356");
        keys.add("qBox_w_12234593_1206");
        keys.add("qBox_w_12234593_1205");
        keys.add("qBox_d_103067234_1352");
        keys.add("qBox_w_12234593_1203");
        keys.add("qBox_d_11587537_928");
        keys.add("qBox_d_12234593_469");
        keys.add("qBox_d_151114270_1235");
        keys.add("qBox_w_12234593_1202");
        keys.add("qBox_read_101308914_1243");
        keys.add("qBox_w_12234593_1201");
        keys.add("qBox_w_103075810_1206");
        keys.add("qBox_w_11587537_1206");
        keys.add("qBox_w_11575598_750");
        keys.add("qBox_d_100001320_1235");
        keys.add("qBox_read_151114193_1122");
        keys.add("qBox_read_12234593_1348");
        keys.add("qBox_d_100001320_1238");
        keys.add("qBox_w_11587537_1202");
        keys.add("qBox_d_12234502_1229");
        keys.add("qBox_w_103067234_1362");
        keys.add("qBox_d_12234593_1348");
        keys.add("qBox_w_100001320_1362");
        keys.add("qBox_d_151114283_1203");
        keys.add("qBox_d_11587537_1238");
        keys.add("qBox_d_12234601_1122");
        keys.add("qBox_w_11380101_1074");
        keys.add("qBox_d_151114283_1202");
        keys.add("qBox_w_11808911_725");
        keys.add("qBox_d_12234593_1238");
        keys.add("qBox_d_101318156_1198");
        keys.add("qBox_d_11587537_1255");
        keys.add("qBox_w_101161851_1160");
        keys.add("qBox_d_11587537_1150");
        keys.add("qBox_read_11587537_1348");
        keys.add("qBox_d_151114311_1238");
        keys.add("qBox_d_11587537_1157");
        keys.add("qBox_read_11587537_1206");
        keys.add("qBox_d_12234601_1206");
        keys.add("qBox_d_101308914_1198");
        keys.add("qBox_d_12234601_1212");
        keys.add("qBox_w_102306044_1348");
        keys.add("qBox_d_11587537_469");
        keys.add("qBox_w_12296377_282");
        keys.add("qBox_d_12234601_1214");
        keys.add("qBox_w_12234696_1074");
        keys.add("qBox_d_12234593_1336");
        keys.add("qBox_d_151114283_1213");
        keys.add("qBox_d_151114270_1238");
        keys.add("qBox_d_151114283_1212");
        keys.add("qBox_d_12234593_1332");
        keys.add("qBox_d_151114300_1198");
        keys.add("qBox_read_12234593_1212");
        keys.add("qBox_read_151114283_1348");
        keys.add("qBox_d_12234593_1122");
        keys.add("qBox_d_11065868_594");
        keys.add("qBox_d_12234593_1123");
        keys.add("qBox_w_151114283_1160");
        keys.add("qBox_w_100001320_1238");
        keys.add("qBox_read_11587537_1212");
        keys.add("qBox_d_100001320_1219");
        keys.add("qBox_read_11587537_1214");
        keys.add("qBox_d_11587537_1122");
        keys.add("qBox_d_11587537_1123");
        keys.add("qBox_d_100001320_1217");
        keys.add("qBox_w_12234593_1327");
        keys.add("qBox_read_11587537_1217");
        keys.add("qBox_w_101161851_1586");
        keys.add("qBox_d_100001320_1212");
        keys.add("qBox_read_11587537_1219");
        keys.add("qBox_d_12234593_1327");
        keys.add("qBox_d_12234642_1157");
        keys.add("qBox_d_101308983_1074");
        keys.add("qBox_d_12234642_1150");
        keys.add("qBox_w_101567141_1348");
        keys.add("qBox_d_12234502_1348");
        keys.add("qBox_w_102923418_1160");
        keys.add("qBox_d_100001320_1354");
        keys.add("qBox_d_101318156_1160");
        keys.add("qBox_w_11333631_475");
        keys.add("qBox_w_11333631_282");
        keys.add("qBox_d_101308914_1074");
        keys.add("qBox_w_101318156_1348");
        keys.add("qBox_d_100001320_1206");
        keys.add("qBox_read_12234593_1074");
        keys.add("qBox_read_101161851_1214");
        keys.add("qBox_d_11335119_1348");
        keys.add("qBox_d_12234593_1252");
        keys.add("qBox_w_151114311_471");
        keys.add("qBox_d_100001320_1202");
        keys.add("qBox_d_151114283_1327");
        keys.add("qBox_d_103067234_1362");
        keys.add("qBox_read_11065863_282");
        keys.add("qBox_w_103067234_1352");
        keys.add("qBox_d_11587537_1074");
        keys.add("qBox_d_11587537_1629");
        keys.add("qBox_w_12395025_475");
        keys.add("qBox_d_151114283_1332");
        keys.add("qBox_d_11587537_1625");
        keys.add("qBox_w_100001320_1255");
        keys.add("qBox_d_102306044_1348");
        keys.add("qBox_w_151114300_1206");
        keys.add("qBox_w_103067234_1356");
        keys.add("qBox_w_102923418_1255");
        keys.add("qBox_w_100001320_1392");
        keys.add("qBox_d_12247716_469");
        keys.add("qBox_w_101308914_1150");
        keys.add("qBox_d_12234593_1203");
        keys.add("qBox_d_12234593_1202");
        keys.add("qBox_d_12234593_1201");
        keys.add("qBox_w_100001320_1217");
        keys.add("qBox_w_101308983_1150");
        keys.add("qBox_w_103066595_1362");
        keys.add("qBox_d_12234593_1206");
        keys.add("qBox_d_12234593_1392");
        keys.add("qBox_d_12234593_1205");
        keys.add("qBox_w_101308914_1157");
        keys.add("qBox_w_100001320_1219");
        keys.add("qBox_w_151114300_1212");
        keys.add("qBox_w_11587537_1625");
        keys.add("qBox_w_151114300_1210");
        keys.add("qBox_w_11587537_1242");
        keys.add("qBox_w_101318156_1214");
        keys.add("qBox_d_151114218_1238");
        keys.add("qBox_d_151114788_1212");
        keys.add("qBox_w_101318156_1215");
        keys.add("qBox_w_11587537_1629");
        keys.add("qBox_w_101318156_1212");
        keys.add("qBox_w_101308914_1160");
        keys.add("qBox_w_12341395_282");
        keys.add("qBox_d_12234502_1201");
        keys.add("qBox_w_11587537_1235");
        keys.add("qBox_d_11575598_750");
        keys.add("qBox_read_12234593_1123");
        keys.add("qBox_d_11587537_1199");
        keys.add("qBox_w_102384617_1629");
        keys.add("qBox_d_151114269_1157");
        keys.add("qBox_w_11587537_1238");
        keys.add("qBox_read_12234601_1074");
        keys.add("qBox_w_151114307_1206");
        keys.add("qBox_d_151114269_1310");
        keys.add("qBox_d_151114311_1160");
        keys.add("qBox_w_151114300_1228");
        keys.add("qBox_w_100001320_1235");
        keys.add("qBox_w_151114300_1229");
        keys.add("qBox_w_151114788_1212");
        keys.add("qBox_d_151114269_1150");
        keys.add("qBox_w_101308983_1278");
        keys.add("qBox_read_101318156_1629");
        keys.add("qBox_d_151114269_1123");
        keys.add("qBox_w_11787913_282");
        keys.add("qBox_d_12234502_1214");
        keys.add("qBox_d_12234502_1215");
        keys.add("qBox_d_12234502_1212");
        keys.add("qBox_d_151114269_1122");
        keys.add("qBox_d_12234502_1213");
        keys.add("qBox_w_101308914_1278");
        keys.add("qBox_d_12234502_1210");
        keys.add("qBox_d_12234502_1211");
        keys.add("qBox_d_12234593_1221");
        keys.add("qBox_w_151114311_1160");
        keys.add("qBox_d_12234593_1220");
        keys.add("qBox_d_11380101_1074");
        keys.add("qBox_d_11587537_1160");
        keys.add("qBox_d_12234593_1228");
        keys.add("qBox_w_101161851_1348");
        keys.add("qBox_w_11587537_1123");
        keys.add("qBox_w_100001320_1202");
        keys.add("qBox_w_11587537_1122");
        keys.add("qBox_w_151114193_1122");
        keys.add("qBox_d_151114269_1629");
        keys.add("qBox_w_101308914_1074");
        keys.add("qBox_w_101161851_1238");
        keys.add("qBox_w_11065863_584");
        keys.add("qBox_w_12247716_469");
        keys.add("qBox_w_101161851_1235");
        keys.add("qBox_d_11587537_282");
        keys.add("qBox_d_101049653_1348");
        keys.add("qBox_d_12234593_1213");
        keys.add("qBox_d_12234593_1212");
        keys.add("qBox_w_11587537_1214");
        keys.add("qBox_w_11587537_1215");
        keys.add("qBox_d_12234593_1215");
        keys.add("qBox_w_11587537_1216");
        keys.add("qBox_d_12234593_1214");
        keys.add("qBox_w_11587537_1217");
        keys.add("qBox_read_12234593_1336");
        keys.add("qBox_w_11587537_1219");
        keys.add("qBox_d_12234593_1210");
        keys.add("qBox_d_12097752_282");
        keys.add("qBox_w_151114283_1198");
        keys.add("qBox_d_12247716_282");
        keys.add("qBox_w_11334665_282");
        keys.add("qBox_d_103730254_1586");
        keys.add("qBox_d_12234593_1361");
        keys.add("qBox_w_100001320_1206");
        keys.add("qBox_d_151114270_1160");
        keys.add("qBox_w_11799190_725");
        keys.add("qBox_w_100001320_1212");
        keys.add("qBox_d_12234593_471");
        keys.add("qBox_d_12234593_1352");
        keys.add("qBox_d_12234502_1123");
        keys.add("qBox_w_151114354_471");
        keys.add("qBox_d_12417560_282");
        keys.add("qBox_d_12234593_1354");
        keys.add("qBox_d_12234593_470");
        keys.add("qBox_d_12234593_1629");
        keys.add("qBox_w_11587537_1210");
        keys.add("qBox_w_100001320_1354");
        keys.add("qBox_w_101161851_1242");
        keys.add("qBox_w_11587537_1212");
        keys.add("qBox_d_11065863_1122");
        keys.add("qBox_w_11587537_1211");
        keys.add("qBox_w_101161851_1211");
        keys.add("qBox_d_151114301_1201");
        keys.add("qBox_d_101049653_1212");
        keys.add("qBox_w_101161851_1214");
        keys.add("qBox_w_101161851_1215");
        keys.add("qBox_w_101161851_1212");
        keys.add("qBox_w_151114283_1348");
        keys.add("qBox_d_151114193_1074");
        keys.add("qBox_d_11065863_469");
        keys.add("qBox_w_101161851_1219");
        keys.add("qBox_w_151114270_1238");
        keys.add("qBox_w_101161851_1217");
        keys.add("qBox_w_151114270_1235");
        keys.add("qBox_d_102306044_1160");
        keys.add("qBox_d_11065863_750");
        keys.add("qBox_w_151114345_1354");
        keys.add("qBox_d_151114276_1348");
        keys.add("qBox_w_103066595_1327");
        keys.add("qBox_d_11575598_282");
        keys.add("qBox_w_12247716_282");
        keys.add("qBox_w_101161851_1220");
        keys.add("qBox_w_151114218_1348");
        keys.add("qBox_w_11587537_282");
        keys.add("qBox_d_12234601_928");
        keys.add("qBox_w_101161851_1354");
        keys.add("qBox_w_12070563_475");
        keys.add("qBox_w_151114283_1332");
        keys.add("qBox_w_151114345_1348");
        keys.add("qBox_w_151114276_1278");
        keys.add("qBox_w_151114344_1348");
        keys.add("qBox_read_12234593_1160");
        keys.add("qBox_d_151114276_1332");
        keys.add("qBox_d_101567141_1348");
        keys.add("qBox_w_11587537_1074");
        keys.add("qBox_w_151114218_1354");
        keys.add("qBox_d_151114269_1198");
        keys.add("qBox_w_11587537_1160");
        keys.add("qBox_d_151114269_1160");
        keys.add("qBox_d_101161851_1348");
        keys.add("qBox_read_12234601_1348");
        keys.add("qBox_w_11335340_928");
        keys.add("qBox_d_151114300_1228");
        keys.add("qBox_d_151114300_1229");
        keys.add("qBox_d_12368273_725");
        keys.add("qBox_d_151114276_1221");
        keys.add("qBox_d_151114276_1220");
        keys.add("qBox_d_101161851_1242");
        keys.add("qBox_d_151114276_1229");
        keys.add("qBox_d_12234642_476");
        keys.add("qBox_w_151114344_1212");
        keys.add("qBox_d_11333631_282");
        keys.add("qBox_w_103066595_1348");
        keys.add("qBox_d_151114354_1160");
        keys.add("qBox_w_101308914_1235");
        keys.add("qBox_w_11587537_1255");
        keys.add("qBox_w_151114269_1310");
        keys.add("qBox_d_11065863_472");
        keys.add("qBox_w_151114283_1213");
        keys.add("qBox_d_11065863_475");
        keys.add("qBox_w_11587537_1157");
        keys.add("qBox_w_151114283_1212");
        keys.add("qBox_d_151114269_1074");
        keys.add("qBox_w_101161851_1206");
        keys.add("qBox_w_11587537_1150");
        keys.add("qBox_w_151114269_1202");
        keys.add("qBox_w_151114300_470");
        keys.add("qBox_w_101308914_1147");
        keys.add("qBox_d_101567141_1160");
        keys.add("qBox_w_101049653_1348");
        keys.add("qBox_w_151114300_471");
        keys.add("qBox_d_151114270_469");
        keys.add("qBox_w_103066595_1212");
        keys.add("qBox_w_101308914_1243");
        keys.add("qBox_d_101161851_1235");
        keys.add("qBox_d_151114276_1219");
        keys.add("qBox_d_151114276_1218");
        keys.add("qBox_d_151114217_1348");
        keys.add("qBox_w_103066595_1356");
        keys.add("qBox_d_151114276_1217");
        keys.add("qBox_d_101161851_1238");
        keys.add("qBox_w_101049653_1212");
        keys.add("qBox_d_151114276_1353");
        keys.add("qBox_d_103075810_1206");
        keys.add("qBox_d_151114276_1352");
        keys.add("qBox_d_11587537_1586");
        keys.add("qBox_d_11335340_928");
        keys.add("qBox_w_151114300_469");
        keys.add("qBox_w_151114283_1203");
        keys.add("qBox_d_11821050_475");
        keys.add("qBox_w_151114283_1202");
        keys.add("qBox_d_151114283_1348");
        keys.add("qBox_d_11787913_282");
        keys.add("qBox_d_151114276_1202");
        keys.add("qBox_d_151114276_1203");
        keys.add("qBox_w_101308914_1216");
        keys.add("qBox_d_12234601_1348");
        keys.add("qBox_w_151114276_1229");
        keys.add("qBox_w_101308914_1214");
        keys.add("qBox_read_11587537_1238");
        keys.add("qBox_w_12234502_1348");
        keys.add("qBox_w_101308914_1211");
        keys.add("qBox_d_151114276_1206");
        keys.add("qBox_read_12234593_1199");
        keys.add("qBox_w_101308914_1212");
        keys.add("qBox_w_12234593_1629");
        keys.add("qBox_read_11333631_282");
        keys.add("qBox_read_11587537_1235");
        keys.add("qBox_w_151114217_1348");
        keys.add("qBox_w_101308914_1210");
        keys.add("qBox_w_101318156_1160");
        keys.add("qBox_w_151114276_1221");
        keys.add("qBox_w_151114276_1220");
        keys.add("qBox_d_101161851_1220");
        keys.add("qBox_w_101318156_1354");
        keys.add("qBox_w_100451919_1160");
        keys.add("qBox_w_151114276_1326");
        keys.add("qBox_d_12368263_475");
        keys.add("qBox_w_101308914_1218");
        keys.add("qBox_w_101308914_1217");
        keys.add("qBox_d_101161851_1354");
        keys.add("qBox_d_12368263_282");
        keys.add("qBox_read_11787913_282");
        keys.add("qBox_w_101308914_1219");
        keys.add("qBox_w_101308914_1224");
        keys.add("qBox_d_101161851_1217");
        keys.add("qBox_d_101161851_1219");
        keys.add("qBox_d_101161851_1214");
        keys.add("qBox_w_101308914_1220");
        keys.add("qBox_w_101308914_1222");
        keys.add("qBox_w_151114276_1332");
        keys.add("qBox_w_12234601_928");
        keys.add("qBox_w_101308914_1223");
        keys.add("qBox_d_101161851_1215");
        keys.add("qBox_d_100451919_1160");
        keys.add("qBox_d_101161851_1212");
        keys.add("qBox_d_101161851_1211");
        keys.add("qBox_w_151114270_1211");
        keys.add("qBox_d_151114217_1212");
        keys.add("qBox_w_151114270_1210");
        keys.add("qBox_w_151114270_1212");
        keys.add("qBox_w_151114270_1215");
        keys.add("qBox_w_12648980_282");
        keys.add("qBox_w_151114270_1214");
        keys.add("qBox_d_102923418_1206");
        keys.add("qBox_d_11587537_472");
        keys.add("qBox_d_11587537_471");
        keys.add("qBox_w_151114270_1216");
        keys.add("qBox_d_11587537_478");
        keys.add("qBox_w_151114283_1327");
        keys.add("qBox_d_11587537_475");
        keys.add("qBox_w_11334665_475");
        keys.add("qBox_read_11587537_1354");
        keys.add("qBox_d_11065863_584");
        keys.add("qBox_w_151114276_1206");
        keys.add("qBox_w_12368263_475");
        keys.add("qBox_d_101161851_1206");
        keys.add("qBox_read_12234502_1074");
        keys.add("qBox_d_103213055_1348");
        keys.add("qBox_w_151114276_1203");
        keys.add("qBox_w_151114276_1202");
        keys.add("qBox_d_151114276_1326");
        keys.add("qBox_w_151114276_1348");
        keys.add("qBox_d_151114300_1160");
        keys.add("qBox_w_151114218_1238");
        keys.add("qBox_w_101567141_1160");
        keys.add("qBox_d_100745147_1348");
        keys.add("qBox_w_11587537_1199");
        keys.add("qBox_w_11065871_475");
        keys.add("qBox_d_12288123_282");
        keys.add("qBox_w_12129111_601");
        keys.add("qBox_d_102379970_1206");
        keys.add("qBox_d_103004520_1356");
        keys.add("qBox_read_11587537_1160");
        keys.add("qBox_w_151114269_1354");
        keys.add("qBox_w_151114269_1352");
        keys.add("qBox_w_151114276_1352");
        keys.add("qBox_w_151114276_1353");
        keys.add("qBox_w_101308914_1202");
        keys.add("qBox_w_151114276_1217");
        keys.add("qBox_w_151114276_1160");
        keys.add("qBox_w_12368263_726");
        keys.add("qBox_w_151114269_1348");
        keys.add("qBox_w_151114276_1219");
        keys.add("qBox_w_151114276_1218");
        keys.add("qBox_d_11065863_1598");
        keys.add("qBox_w_12417560_282");
        keys.add("qBox_w_151114269_1198");
        keys.add("qBox_d_102923418_1212");
        keys.add("qBox_read_151114350_1348");
        keys.add("qBox_w_102957334_1206");
        keys.add("qBox_d_12097752_475");
        keys.add("qBox_d_12097752_472");
        keys.add("qBox_w_11335119_1310");
        keys.add("qBox_w_102379970_1214");
        keys.add("qBox_w_12097752_492");
        keys.add("qBox_w_12234593_1074");
        keys.add("qBox_w_12234502_469");
        keys.add("qBox_w_11587537_475");
        keys.add("qBox_w_11084928_471");
        keys.add("qBox_w_11587537_472");
        keys.add("qBox_w_11587537_471");
        keys.add("qBox_w_151114269_1255");
        keys.add("qBox_w_12234502_1074");
        keys.add("qBox_w_103004520_1356");
        keys.add("qBox_d_102923418_1219");
        keys.add("qBox_d_102923418_1217");
        keys.add("qBox_d_100001320_1392");
        keys.add("qBox_w_151114269_1392");
        keys.add("qBox_w_11587537_478");
        keys.add("qBox_d_11787913_750");
        keys.add("qBox_w_102957334_1217");
        keys.add("qBox_w_12288123_282");
        keys.add("qBox_w_102379970_1206");
        keys.add("qBox_w_11335119_282");
        keys.add("qBox_d_102957334_1206");
        keys.add("qBox_d_11335330_1160");
        keys.add("qBox_w_12234642_1074");
        keys.add("qBox_d_12296377_282");
        keys.add("qBox_w_103730254_1586");
        keys.add("qBox_w_102379970_1212");
        keys.add("qBox_d_11335330_1157");
        keys.add("qBox_d_11587537_1362");
        keys.add("qBox_d_11587537_1361");
        keys.add("qBox_w_100451919_1348");
        keys.add("qBox_d_11065863_282");
        keys.add("qBox_read_11587537_1074");
        keys.add("qBox_w_102379970_1354");
        keys.add("qBox_w_11587537_469");
        keys.add("qBox_w_103453390_1352");
        keys.add("qBox_w_12234642_1150");
        keys.add("qBox_w_11065863_1122");
        keys.add("qBox_d_151114344_1348");
        keys.add("qBox_w_11335330_1160");
        keys.add("qBox_w_151114269_1074");
        keys.add("qBox_w_12234642_1157");
        keys.add("qBox_d_11808911_725");
        keys.add("qBox_w_151114276_1198");
        keys.add("qBox_d_151114193_1122");
        keys.add("qBox_d_151114307_1206");
        keys.add("qBox_d_102923418_1238");
        keys.add("qBox_w_12234593_1198");
        keys.add("qBox_w_12234593_1199");
        keys.add("qBox_d_12234601_469");
        keys.add("qBox_w_102957334_1238");
        keys.add("qBox_d_11335330_1074");
        keys.add("qBox_w_151114269_1160");
        keys.add("qBox_w_11335330_1157");
        keys.add("qBox_w_102957334_1235");
        keys.add("qBox_w_12234642_1160");
        keys.add("qBox_w_12234601_469");
        keys.add("qBox_w_101318156_1198");
        keys.add("qBox_w_151114217_1212");
        keys.add("qBox_w_12234502_478");
        keys.add("qBox_w_151114269_1157");
        keys.add("qBox_d_12234642_1160");
        keys.add("qBox_d_102379970_1212");
        keys.add("qBox_w_151114269_1362");
        keys.add("qBox_w_12234502_470");
        keys.add("qBox_d_100001320_1362");
        keys.add("qBox_w_12234502_471");
        keys.add("qBox_d_102379970_1214");
        keys.add("qBox_d_102957334_1238");
        keys.add("qBox_d_11787913_601");
        keys.add("qBox_w_100451919_1310");
        keys.add("qBox_d_12341395_282");
        keys.add("qBox_d_102923418_1255");
        keys.add("qBox_d_151114276_1278");
        keys.add("qBox_d_102957334_1235");
        keys.add("qBox_w_151114269_1150");
        keys.add("qBox_read_12234502_1160");
        keys.add("qBox_w_11575598_282");
        keys.add("qBox_w_151114307_1243");
        keys.add("qBox_w_151114270_1160");
        keys.add("qBox_d_101161851_1160");
        keys.add("qBox_read_151114276_1348");
        keys.add("qBox_w_11821050_475");
        keys.add("qBox_w_101318156_1629");
        keys.add("qBox_w_151114269_1278");
        keys.add("qBox_d_101308914_1202");
        keys.add("qBox_w_12234502_1160");
        keys.add("qBox_w_12234593_469");
        keys.add("qBox_d_102923418_1160");
        keys.add("qBox_d_12070563_475");
        keys.add("qBox_w_11335330_1074");
        keys.add("qBox_w_12234593_470");
        keys.add("qBox_d_101161851_1586");
        keys.add("qBox_w_12234593_471");
        keys.add("qBox_d_12368263_726");
        keys.add("qBox_d_11084928_471");
        keys.add("qBox_w_11335119_1348");
        keys.add("qBox_w_102957334_1354");
        keys.add("qBox_d_151114283_1160");
        keys.add("qBox_d_101308914_1210");
        keys.add("qBox_d_101308914_1211");
        keys.add("qBox_d_102384617_1629");
        keys.add("qBox_d_101308914_1212");
        keys.add("qBox_d_101318156_1348");
        keys.add("qBox_d_102957334_1217");
        keys.add("qBox_d_102821903_1212");
        keys.add("qBox_w_12234502_1199");
        keys.add("qBox_w_12234502_1198");
        keys.add("qBox_d_12097752_492");
        keys.add("qBox_d_151114307_1243");
        keys.add("qBox_d_151114300_471");
        keys.add("qBox_d_151114300_470");
        keys.add("qBox_d_151114300_1212");
        keys.add("qBox_d_12097752_593");
        keys.add("qBox_d_151114300_469");
        keys.add("qBox_d_12234502_478");
        keys.add("qBox_d_151114300_1210");
        keys.add("qBox_d_151114283_1198");
        keys.add("qBox_w_12097752_475");
        keys.add("qBox_w_11587537_601");
        keys.add("qBox_d_101308914_1219");
        keys.add("qBox_d_12234502_470");
        keys.add("qBox_d_101308914_1218");
        keys.add("qBox_d_103453390_1352");
        keys.add("qBox_w_12097752_472");
        keys.add("qBox_w_11587537_928");
        keys.add("qBox_d_101308914_1214");
        keys.add("qBox_w_151114354_1160");
        keys.add("qBox_d_12234502_471");
        keys.add("qBox_d_101308914_1217");
        keys.add("qBox_d_101308914_1216");
        keys.add("qBox_d_101308914_1223");
        keys.add("qBox_d_102379970_1354");
        keys.add("qBox_d_101308914_1224");
        keys.add("qBox_d_12161379_282");
        keys.add("qBox_d_151114344_1212");
        keys.add("qBox_d_101308914_1222");
        keys.add("qBox_w_12097752_282");
        keys.add("qBox_d_101308914_1220");
        keys.add("qBox_w_12234601_1122");
        keys.add("qBox_w_11316230_282");
        keys.add("qBox_d_102923418_1629");
        keys.add("qBox_w_151114269_1122");
        keys.add("qBox_d_100451919_1310");
        keys.add("qBox_d_101318156_1354");
        keys.add("qBox_w_151114269_1123");
        keys.add("qBox_w_11065863_1598");
        keys.add("qBox_d_100451919_1348");
        keys.add("qBox_d_12234502_469");
        keys.add("qBox_d_151114300_1206");
        keys.add("qBox_d_101308914_1225");
        keys.add("qBox_w_11587537_1354");
        keys.add("qBox_w_12234601_1348");
        keys.add("qBox_d_101308914_1238");
        keys.add("qBox_d_101308914_470");
        keys.add("qBox_w_11587537_1352");
        keys.add("qBox_d_100128321_1212");
        keys.add("qBox_w_12368273_725");
        keys.add("qBox_d_151114311_471");
        keys.add("qBox_d_11333631_475");
        keys.add("qBox_w_12368263_282");
        keys.add("qBox_read_100001320_1202");
        keys.add("qBox_d_11065871_475");
        keys.add("qBox_d_151114354_471");
        keys.add("qBox_w_12234601_1150");
        keys.add("qBox_read_12234502_1123");
        keys.add("qBox_w_12234601_1157");
        keys.add("qBox_d_11799190_725");
        keys.add("qBox_w_12234593_1278");
        keys.add("qBox_d_101308914_1235");
        keys.add("qBox_d_12234593_1199");
        keys.add("qBox_d_12368284_282");
        keys.add("qBox_d_12234593_1198");
        keys.add("qBox_w_11587537_1356");
        keys.add("qBox_read_11587537_282");
        keys.add("qBox_w_12097752_593");
        keys.add("qBox_w_11788681_282");
        keys.add("qBox_w_11587537_1361");
        keys.add("qBox_w_11587537_1362");
        keys.add("qBox_w_102923418_1206");
        keys.add("qBox_d_151114345_1348");
        keys.add("qBox_d_101308983_1150");
        keys.add("qBox_read_151114269_1074");
        keys.add("qBox_d_103066595_1327");
        keys.add("qBox_d_101318156_1629");
        keys.add("qBox_w_12234601_1254");
        keys.add("qBox_d_12395025_475");
        keys.add("qBox_d_101308914_1147");
        keys.add("qBox_w_151114270_469");
        keys.add("qBox_w_11065863_282");
        keys.add("qBox_d_151114312_1215");
        keys.add("qBox_read_151114269_1348");
        keys.add("qBox_w_12234502_1123");
        keys.add("qBox_d_101308914_1244");
        keys.add("qBox_d_11788681_282");
        keys.add("qBox_d_101308914_1243");
        keys.add("qBox_w_11787913_601");
        keys.add("qBox_d_151114345_1354");
        keys.add("qBox_d_12234502_1160");
        keys.add("qBox_d_102655231_1348");
        keys.add("qBox_d_103066595_1212");
        keys.add("qBox_w_103213055_1348");
        keys.add("qBox_d_151114276_1160");
        keys.add("qBox_w_11065863_472");
        keys.add("qBox_d_103066595_1356");
        keys.add("qBox_d_12234593_1278");
        keys.add("qBox_d_151114218_1348");
        keys.add("qBox_w_11065863_475");
        keys.add("qBox_d_101318156_1212");
        keys.add("qBox_w_11065863_750");
        keys.add("qBox_w_12234593_1392");
        keys.add("qBox_read_151114283_1160");
        keys.add("qBox_d_151114269_1354");
        keys.add("qBox_w_12234502_1150");
        keys.add("qBox_w_11587537_1335");
        keys.add("qBox_d_151114269_1352");
        keys.add("qBox_w_12234593_1252");
        keys.add("qBox_d_101318156_1215");
        keys.add("qBox_w_151114312_1215");
        keys.add("qBox_w_100128321_1212");
        keys.add("qBox_d_101318156_1214");
        keys.add("qBox_w_11065863_469");
        keys.add("qBox_d_12234502_1150");
        keys.add("qBox_w_101308914_470");
        keys.add("qBox_read_12097752_593");
        keys.add("qBox_w_12234593_1122");
        keys.add("qBox_d_101308914_469");
        keys.add("qBox_w_12234593_1123");
        keys.add("qBox_d_12234696_1074");
        keys.add("qBox_d_151114269_1202");
        keys.add("qBox_d_102957334_1354");
        keys.add("qBox_w_151114300_1160");
        keys.add("qBox_d_103066595_1348");
        keys.add("qBox_w_11587537_1348");
        keys.add("qBox_read_151114276_1074");
        keys.add("qBox_w_12234601_1160");
        keys.add("qBox_w_11587537_1349");
        keys.add("qBox_d_101308983_1278");
        keys.add("qBox_d_151114269_1348");
        keys.add("qBox_d_12234601_1157");
        keys.add("qBox_w_102923418_1238");
        keys.add("qBox_w_12234601_1206");
        keys.add("qBox_d_11334665_475");
        keys.add("qBox_w_101308914_469");
        keys.add("qBox_d_12234601_1254");
        keys.add("qBox_d_12234593_1160");
        keys.add("qBox_d_12234601_1150");
        keys.add("qBox_w_11787913_750");
        keys.add("qBox_w_151114301_1201");
        keys.add("qBox_d_11587537_1235");
        keys.add("qBox_w_12234593_1336");
        keys.add("qBox_d_101308914_1278");
        keys.add("qBox_d_11316230_282");
        keys.add("qBox_d_151114269_1278");
        keys.add("qBox_w_12234593_1332");
        keys.add("qBox_w_12234593_1238");
        keys.add("qBox_w_151114300_1198");
        keys.add("qBox_d_11587537_1335");
        keys.add("qBox_d_103066595_1362");
        keys.add("qBox_w_12368284_282");
        keys.add("qBox_d_12234502_1074");
        keys.add("qBox_w_12234502_1229");
        keys.add("qBox_w_12234601_1214");
        keys.add("qBox_d_12234593_1074");
        keys.add("qBox_d_100001320_1255");
        keys.add("qBox_d_11334665_282");
        keys.add("qBox_d_12234601_1160");
        keys.add("qBox_w_151114311_1216");
        keys.add("qBox_d_11587537_1219");
        keys.add("qBox_w_102923418_1629");
        keys.add("qBox_d_11587537_1216");
        keys.add("qBox_w_151114311_1214");
        keys.add("qBox_d_151114269_1362");
        keys.add("qBox_d_11587537_1217");
        keys.add("qBox_w_151114311_1212");
        keys.add("qBox_d_11587537_1221");
        keys.add("qBox_w_12234593_1348");
        keys.add("qBox_w_12234601_1212");
        keys.add("qBox_w_101308983_1074");
        keys.add("qBox_d_151114218_1354");
        keys.add("qBox_d_151114270_1216");
        keys.add("qBox_w_100745147_1348");
        keys.add("qBox_d_11587537_1348");
        keys.add("qBox_read_151114269_1214");
        keys.add("qBox_d_151114270_1215");
        keys.add("qBox_d_11587537_1349");
        keys.add("qBox_w_102923418_1212");
        keys.add("qBox_w_12234502_1215");
        keys.add("qBox_w_12234502_1213");
        keys.add("qBox_w_151114193_1074");
        keys.add("qBox_w_102923418_1217");
        keys.add("qBox_w_12234502_1214");
        keys.add("qBox_d_11335119_1310");
        keys.add("qBox_read_12234502_1348");
        keys.add("qBox_d_11587537_1206");
        keys.add("qBox_d_151114269_1392");
        keys.add("qBox_read_151114269_1212");
        keys.add("qBox_w_12234593_1212");
        keys.add("qBox_d_11587537_601");
        keys.add("qBox_d_11587537_1212");
        keys.add("qBox_d_11587537_1215");
        keys.add("qBox_w_12234601_1074");
        keys.add("qBox_d_11587537_1214");
        keys.add("qBox_w_12234593_1210");
        keys.add("qBox_d_101308914_1150");
        keys.add("qBox_w_151114269_1629");
        keys.add("qBox_d_11587537_1211");
        keys.add("qBox_d_11587537_1210");
        keys.add("qBox_d_11587537_1352");
        keys.add("qBox_w_12234502_1212");
        keys.add("qBox_w_12234502_1211");
        keys.add("qBox_d_12234601_1074");
        keys.add("qBox_d_11587537_1354");
        keys.add("qBox_w_12234502_1210");
        keys.add("qBox_d_101308914_1157");
        keys.add("qBox_d_11587537_1356");
        keys.add("qBox_w_12234593_1354");
        keys.add("qBox_w_12234593_1215");
        keys.add("qBox_d_151114269_1255");
        keys.add("qBox_w_12234593_1213");
        keys.add("qBox_w_12234593_1352");
        keys.add("qBox_w_12234593_1214");
        keys.add("qBox_w_11065868_594");
        keys.add("qBox_d_12234502_1198");
        keys.add("qBox_w_12161379_282");
        keys.add("qBox_d_11335119_282");
        keys.add("qBox_w_102821903_1212");
        keys.add("qBox_d_12234502_1199");
        keys.add("qBox_d_12234642_1074");
        keys.add("qBox_d_12648980_282");
        keys.add("qBox_w_12234593_1220");
        keys.add("qBox_w_12234593_1221");
        keys.add("qBox_d_12129111_601");
        keys.add("qBox_d_11587537_1202");
        keys.add("qBox_w_102306044_1160");
        keys.add("qBox_d_151114311_1216");
        keys.add("qBox_d_101308914_1160");
        keys.add("qBox_w_11587537_1586");
        keys.add("qBox_w_12234593_1228");
        keys.add("qBox_d_151114276_1198");
        keys.add("qBox_d_151114311_1212");
        keys.add("qBox_d_151114270_1210");
        keys.add("qBox_w_12234593_1361");
        keys.add("qBox_w_102923418_1219");
        keys.add("qBox_d_151114311_1214");
        keys.add("qBox_w_12234502_1201");
        keys.add("qBox_read_12234502_1214");
        keys.add("qBox_d_151114270_1214");
        keys.add("qBox_read_12234502_1212");
        keys.add("qBox_d_151114270_1211");
        keys.add("qBox_d_151114270_1212");
        keys.add("qBox_w_12234593_1160");

        for (String key : keys){
            try {
                System.out.println("exec " + key);
                String[] split = StringUtils.split(key, "_");
                if (null == split || split.length != 4) {
                    return;
                }
                Long uid = Long.valueOf(split[2]);
                Long qboxId = Long.valueOf(split[3]);
                if ("d".equals(split[1])) {
                    List<UserDoneQuestion> doneList = userDoneQuestionService.getByUidAndQboxId(uid, qboxId);
                    if (CollectionUtils.isEmpty(doneList)) {
                        System.out.println("key:"+key+" has no data");
                        continue;
                    }
                    Map<String,String> doneMap = Maps.newHashMap();
                    for (UserDoneQuestion userDoneQuestion : doneList) {
                        doneMap.put(userDoneQuestion.getKey(),userDoneQuestion.getQuestionIdList());
                    }
                    compatableRedisClusterClient.hmset(RedisConsts.getUserQuestionboxDone(uid,qboxId),doneMap);
                } else if ("w".equals(split[1])) {
                    List<UserWrongQuestion> wrongList = userWrongQuestionService.getByUidAndQboxId(uid, qboxId);
                    if (CollectionUtils.isEmpty(wrongList)) {
                        System.out.println("key:"+key+" has no data");
                        continue;
                    }
                    Map<String,String> wrongMap = Maps.newHashMap();
                    for (UserWrongQuestion userWrongQuestion : wrongList) {
                        wrongMap.put(userWrongQuestion.getKey(),userWrongQuestion.getQuestionIdList());
                    }
                    compatableRedisClusterClient.hmset(RedisConsts.getUserQuestionboxWrong(uid,qboxId),wrongMap);
                } else if ("read".equals(split[1])) {
                    List<UserReciteQuestion> reciteList = userReciteQuestionService.getByUidAndQboxId(uid, qboxId);
                    if (CollectionUtils.isEmpty(reciteList)) {
                        System.out.println("key:"+key+" has no data");
                        continue;
                    }
                    Map<String,String> reciteMap = Maps.newHashMap();
                    for (UserReciteQuestion userReciteQuestion : reciteList) {
                        reciteMap.put(userReciteQuestion.getKey(),userReciteQuestion.getQuestionIdList());
                    }
                    compatableRedisClusterClient.hmset(RedisConsts.getUserQuestionboxRecite(uid,qboxId),reciteMap);
                }
                System.out.println("done " + key);
            } catch (Exception e) {
              e.printStackTrace();
            }
        }
    }
}
