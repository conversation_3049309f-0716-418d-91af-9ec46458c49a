package cn.huanju.edu100.study.dao.useranswer;

import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.QuestionAnswerStaticsDao;
import cn.huanju.edu100.study.dao.UserAnswerDao;
import cn.huanju.edu100.study.dao.ibatis.impl.UserVirtualHomeworkIbatisImpl;
import cn.huanju.edu100.study.entry.ServerBootstrap;
import cn.huanju.edu100.study.model.QuestionAnswerStatics;
import cn.huanju.edu100.study.model.QuestionAnswerStaticsCount;
import cn.huanju.edu100.study.model.questionBox.VirtualHomework;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.DateUtils;
import com.google.common.collect.Maps;
import com.hqwx.study.entity.UserAnswer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

@SpringBootTest(classes = ServerBootstrap.class)
public class UserAnswerDaoTest extends BaseTest {
    @Autowired
    private UserAnswerDao dao;
    @Autowired
    private UserVirtualHomeworkIbatisImpl virtualHomeworkIbatis;
    @Autowired
    private QuestionAnswerStaticsDao questionAnswerStaticsDao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 269510130674958336L);
        param.put("uid", 11718457L);
        UserAnswer ua = dao.getShardingById(param);
        dao.update(ua);
        Page<UserAnswer> page = new Page<>();
        page.setPageNo(1);
        page.setPageSize(10);
        UserAnswer userAnswer = new UserAnswer();
        userAnswer.setUid(1l);
        dao.findList(page, userAnswer);
        Assertions.assertEquals(13.1, ua.getScore());
    }

    @Test
    public void countRightAnswerByIds() throws Exception{
        long start2 = System.currentTimeMillis();
        long num = questionAnswerStaticsDao.countRightAnswer(2175232l);
        System.out.println(System.currentTimeMillis() - start2);
        List<Long> ids = Arrays.asList(1088l,1280l,1344l,2002688l,2175232l,3813495l,3813496l,3813521l,3813524l,3813526l,3813531l,3813534l,3813539l,3813542l,3813544l,3813545l,3813546l,3813547l,3813548l,3813549l,3813550l,3813551l,3813552l,3813913l);
        long start = System.currentTimeMillis();
        List<QuestionAnswerStaticsCount> result = questionAnswerStaticsDao.countRightAnswerByIds(ids);
        System.out.println(System.currentTimeMillis() - start);
        System.out.println(result);
    }

    @Test
    public void testFindAnswerGroupByPaperId() throws Exception {
        List<UserAnswer> userAnswerList = dao.findAnswerGroupByPaperId(151236240L, Arrays.asList(42647L), null, null, null);
        System.out.println(userAnswerList);
    }

    @Test
    public void findList() throws Exception {
        Map<String,Object> query = Maps.newHashMap();
        query.put("uid", 151114848L);
        query.put("bookIds", Arrays.asList(180L));
        query.put("sourceType", Consts.Homework_Source_Type.BOX);
//		query.put("homeWorkType", Consts.Question_Exercise_Type.Chapter);//查询章节练习
        query.put("randomType", 9); //此处特殊处理，用户查询章节练习记录时，random_type的值包括0，1，2
        Date todayBegin = DateUtils.getBeginOfDate(new Date());
        query.put("startTime", todayBegin); //查询今天开始的
        List<VirtualHomework> lists= virtualHomeworkIbatis.findList(query);
        System.out.println(lists);
    }
}
