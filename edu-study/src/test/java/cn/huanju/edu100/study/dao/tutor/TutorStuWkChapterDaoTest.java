package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.tutor.TutorStuWkChapter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class TutorStuWkChapterDaoTest extends BaseTest {
    @Autowired
    private TutorStuWkChapterDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 18L);
        param.put("uid", 11335330L);
        TutorStuWkChapter ua = dao.getShardingById(param);
        Assertions.assertEquals(98, ua.getTaskId());
    }

    @Test
    public void testSelect() throws Exception{
        TutorStuWkChapter ua = dao.get(18L);
        Assertions.assertEquals(98, ua.getTaskId());
    }
}
