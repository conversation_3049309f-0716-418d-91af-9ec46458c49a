package cn.huanju.edu100.study.service;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.tutor.TutorTeacher;
import cn.huanju.edu100.study.service.tutor.TutorTeacherService;
import cn.huanju.edu100.util.GsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 *
 */
public class RuleEvalTest extends BaseTest {

	@Autowired
	TutorTeacherService teacherService;

	@Test
	public void getExpDefine() throws Exception {
		TutorTeacher teacher = teacherService.get(1l);
		String s = GsonUtil.toJson(teacher);
		System.out.println(s);
	}
}
