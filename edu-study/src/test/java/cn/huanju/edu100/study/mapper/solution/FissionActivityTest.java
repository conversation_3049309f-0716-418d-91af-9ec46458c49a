package cn.huanju.edu100.study.mapper.solution;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.client.FissionActivityService;
import cn.huanju.edu100.study.repository.wxapp.FissionActivityRepository;
import cn.huanju.edu100.util.JSONUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hqwx.study.dto.Page;
import com.hqwx.study.entity.wxapp.FissionActivity;
import com.hqwx.study.entity.wxapp.FissionActivityQuery;
import com.hqwx.study.entity.wxapp.FissionActivitySetting;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/22 15:56
 * @description
 */
public class FissionActivityTest extends BaseTest {
    @Autowired
    private FissionActivityRepository fissionActivityRepository;

    @Autowired
    private FissionActivityService fissionActivityService;

    @Test
    public void testFindActivity(){
        FissionActivityQuery query = new FissionActivityQuery();
//        query.setSecondCategoryId(5583L);
        query.setSecondCategoryId(775L).setIsPushed(true);
        IPage<FissionActivity> page = fissionActivityRepository.findActivityPage(query, null);
        System.out.println(JSONUtils.toJsonString(page));
    }

    @Test
    public void testFindActivityList(){
        FissionActivityQuery query = new FissionActivityQuery();
//        query.setSecondCategoryId(5583L);
        query.setSecondCategoryId(775L).setIsPushed(true);
        Page<FissionActivity> page = fissionActivityService.getActivityList(query);
        System.out.println(JSONUtils.toJsonString(page));
    }

    @Test
    public void testFindSetPushActivityList(){
        System.out.println(JSONUtils.toJsonString(fissionActivityRepository.findSettingActivityList(5583L,
                FissionActivitySetting.Type.PUSH.getValue())));
    }
}
