package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.entry.ServerBootstrap;
import cn.huanju.edu100.study.model.QuestionAnswerStatics;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.JSONUtils;
import com.google.gson.Gson;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/9 15:25
 * @description
 */

@SpringBootTest(classes = ServerBootstrap.class)
@Slf4j
public class UserAnswerStaticsServiceTest {

    @Autowired
    QuestionAnswerStaticsService questionAnswerStaticsService;

    @Test
    public void staticQuestionAnswerBatch() throws DataAccessException {
        long start2 = System.currentTimeMillis();
        Map<Long, QuestionAnswerStatics> result2 = questionAnswerStaticsService.staticQuestionAnswerBatchNew(Arrays.asList(1088l, 1280l, 1344l, 2002688l, 2175232l, 3813495l, 3813496l, 3813521l, 3813524l, 3813526l, 3813531l, 3813534l, 3813539l, 3813542l, 3813544l, 3813545l, 3813546l, 3813547l, 3813548l, 3813549l, 3813550l, 3813551l, 3813552l, 3813913l));
        log.info("time:{}", System.currentTimeMillis() - start2);
        log.info("result:{}", GsonUtil.toJson(result2));
        long start = System.currentTimeMillis();
        Map<Long, QuestionAnswerStatics> result = questionAnswerStaticsService.staticQuestionAnswerBatch(Arrays.asList(1088l, 1280l, 1344l, 2002688l, 2175232l, 3813495l, 3813496l, 3813521l, 3813524l, 3813526l, 3813531l, 3813534l, 3813539l, 3813542l, 3813544l, 3813545l, 3813546l, 3813547l, 3813548l, 3813549l, 3813550l, 3813551l, 3813552l, 3813913l));
        log.info("time:{}", System.currentTimeMillis() - start);
        log.info("result:{}", GsonUtil.toJson(result));
    }
}
