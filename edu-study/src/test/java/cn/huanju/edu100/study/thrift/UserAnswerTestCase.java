package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100StudyServiceGrpc;
import cn.huanju.edu100.grpc.service.SubjectiveQuestionAiCorrectingServiceGrpc;
import cn.huanju.edu100.study.client.SubjectiveQuestionAiCorrectingService;
import cn.huanju.edu100.study.client.impl.SubjectiveQuestionAiCorrectingServiceClientImpl;
import cn.huanju.edu100.stustamp.constant.Consts;
import cn.huanju.edu100.stustamp.util.DateUtil;
import cn.huanju.edu100.stustamp.util.IpConvert;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.hqwx.study.dto.PaperStudyRecordDTO;
import com.hqwx.study.dto.SyncHomeworkDTO;
import com.hqwx.study.dto.UserAnswerSumDTO;
import com.hqwx.study.dto.query.PaperStudyReportQuery;
import com.hqwx.study.dto.query.UserErrorAndCorrectQuestionQuery;
import com.hqwx.study.dto.query.UserErrorAnswerQuery;
import com.hqwx.study.dto.query.wxapp.UserAnswerDetailQuestionQuery;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.channel.ChannelOption;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class UserAnswerTestCase {
    private static Gson gson = GsonUtil.getGson();
//     public static String URL = "**************";// 221.228.86.187, 127.0.0.1
//    public static String URL = "58.215.169.173";//103.227.123.195 58.215.169.173 ************** ************** **************
   public static String URL = "*************";
//    public static String URL = "*************";
//     public static String URL = "**************";
    public static int PORT = 58285;

    // public static String URL = "**************";
    // public static int PORT = 12300;

    @Test
    public void sty_userAnswerErrorQuestion() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("user_error_id", 5191L);
            param.put("uid", 11587537L);
            param.put("question_id", 960L);
            param.put("topic_id", 307752L);
            param.put("answer", new String[] { "B" });

            List<Map<String, Object>> paramList = new ArrayList<Map<String,Object>>();
            paramList.add(param);


            req.setMsg(gson.toJson(paramList));
            GrpcResponse res = client.styUserAnswerErrorQuestion(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
//
    @Test
    public void sty_getPaperAnswerById() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 151179750);
            param.put("id", 1384600248934666240L);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.styGetPaperAnswerById(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sty_getPaperAnswerDetailByUid() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);

            req.setMsg("{\"uid\":186666732,\"user_answer_id\":1372605422713061376,\"question_ids\":[]}");
            GrpcResponse res = client.styGetPaperAnswerDetailByUid(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
//
//    @Test
//    public void sty_adminstudygetQuestionListByAnswerId() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("answerId", 886041087716753408L);
//            param.put("uid", 11498706L);
//
//            req.setMsg(gson.toJson(param));
//            // req.setMsg("{\"id\":639538151827832832,\"uid\":11498706} ");
//            response res = client.sty_adminstudygetQuestionListByAnswerId(req);
//            System.out.println("sty_getPaperAnswerById>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
    @Test
    public void sty_getPaperAnswerByUid() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 201782443);
            param.put("paper_id", 131525);
            param.put("source",10);
            param.put("obj_type",10);
            param.put("is_latest", 1);
            req.setMsg(GsonUtil.toJson(param));
            GrpcResponse response = client.styGetPaperAnswerByUid(req.build());
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void sty_getUserPaperAnswerLastInfo() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("127.0.0.1",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 103453390L);
            param.put("paperIds", List.of(66252L));
            param.put("paperType",2);
            req.setMsg(GsonUtil.toJson(param));
            GrpcResponse response = client.styGetUserPaperAnswerLastInfo(req.build());
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void sty_getPaperRecordPage() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            PaperStudyReportQuery paperStudyReportQuery = new PaperStudyReportQuery();
            paperStudyReportQuery.setUid(11498706L);
            paperStudyReportQuery.setGoodsId(5578L);
            paperStudyReportQuery.setProductId(36113L);
            paperStudyReportQuery.setPageNo(1);
            paperStudyReportQuery.setPageSize(20);
            req.setMsg(GsonUtil.getGenericGson().toJson(paperStudyReportQuery));
            GrpcResponse response = client.styGetPaperRecordPage(req.build());
            Type type = new com.google.gson.reflect.TypeToken<com.hqwx.study.dto.PageModel<PaperStudyRecordDTO>>() {
            }.getType();
            com.hqwx.study.dto.PageModel<PaperStudyRecordDTO> rs = GsonUtil.getGenericGson().fromJson(response.getMsg() == null ? null : (String)response.getMsg(), type);
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
//
//    @Test
//    public void sty_getPaperAnswerDetailByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("*************", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            List<Long> question_ids = new ArrayList<Long>();
////            question_ids.add(25l);
////            question_ids.add(39l);
////            param.put("uid", 11065863l);
////            param.put("user_answer_id", 210l);
////            param.put("question_ids", question_ids);
//
//          /*  Long[] ids = {2556620L,2556621L,2556622L,2556628L,2556629L,2556638L};
//            for(Long id : ids) {
//                question_ids.add(id);
//            }*/
//            param.put("uid", 102171867L);
//            param.put("user_answer_id", 240890124651462657L);
//            //param.put("question_ids", question_ids);
//
//            //req.setMsg(gson.toJson(param));
////            req.setMsg(" {\"uid\":11498706,\"user_answer_id\":891369836725272576,\"question_ids\":[]}");
//            req.setMsg(" {\"uid\":151236240,\"user_answer_id\":897892485387386880,\"question_ids\":[]}");
//            response res = client.sty_getPaperAnswerDetailByUid(req);
//            System.out.println("sty_getPaperAnswerDetailByUid>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_userAnswerPaper() {
//
//        long userId = 11498706L;
//        long ePaperId = 21847L;
//        long paperId = 2085L;
//        long goodsId = 8956L;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("paper_id", paperId);
//        param.put("paper_type", 4);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("source",0);
//        param.put("is_submit", 1);
//        param.put("obj_type", 0);
//        param.put("comment", "{\"idCard\":\"*****************\",\"username\":\"李8四2021\"}");
//        param.put("e_paper_id", ePaperId);
//        param.put("product_id", ePaperId);
//        param.put("done_record_type", 2);
//        param.put("goods_id", goodsId);
//        param.put("is_on_error_list", 1);
//        param.put("error_list_type", 1);
//        param.put("error_type", 1);
//        param.put("done_mode", 1);//做题模式 1练习模式 2考试模式
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 2484380L, 1539841L, new String[] { "A" }, "", "", "");
//        answerList.add(detail1);
//        UserAnswerDetail detail2 = new UserAnswerDetail(userId, 2484381L, 1539842L, new String[] { "C" }, "", "", "");
//        answerList.add(detail2);
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 2484382L, 1539843L, new String[] { "C" }, "", "", "");
//        answerList.add(detail3);
//
//        UserAnswerDetail detail4 = new UserAnswerDetail(userId, 2484383L, 1539844L, new String[] { "C" }, "", "", "");
//        answerList.add(detail4);
//        UserAnswerDetail detail5 = new UserAnswerDetail(userId, 2484385L, 1539846L, new String[] { }, "", "", "");
//        answerList.add(detail5);
//        UserAnswerDetail detail6 = new UserAnswerDetail(userId, 2484384L, 1539845L, new String[] { }, "", "", "");
//        answerList.add(detail6);
//
//        UserAnswerDetail detail7 = new UserAnswerDetail(userId, 2484460L, 1539930L, new String[] { }, "", "", "");
//        answerList.add(detail7);
//        UserAnswerDetail detail8 = new UserAnswerDetail(userId, 2484461L, 1539931L, new String[] { }, "", "", "");
//        answerList.add(detail8);
//        UserAnswerDetail detail9 = new UserAnswerDetail(userId, 2484462L, 1539932L, new String[] { }, "", "", "");
//        answerList.add(detail9);
//        UserAnswerDetail detail10 = new UserAnswerDetail(userId, 2484463L, 1539933L, new String[] { }, "", "", "");
//        answerList.add(detail10);
//        UserAnswerDetail detail11 = new UserAnswerDetail(userId, 2484465L, 1539935L, new String[] { }, "", "", "");
//        answerList.add(detail11);
//
//        UserAnswerDetail detail12 = new UserAnswerDetail(userId, 2484466L, 1539936L, new String[] { }, "", "", "");
//        answerList.add(detail12);
//        UserAnswerDetail detail13 = new UserAnswerDetail(userId, 2484467L, 1539937L, new String[] { }, "", "", "");
//        answerList.add(detail13);
//        UserAnswerDetail detail14 = new UserAnswerDetail(userId, 2484468L, 1539938L, new String[] { }, "", "", "");
//        answerList.add(detail14);
//        UserAnswerDetail detail15 = new UserAnswerDetail(userId, 2484469L, 1539939L, new String[] { }, "", "", "");
//        answerList.add(detail15);
//
//        UserAnswerDetail detail16 = new UserAnswerDetail(userId, 2484471L, 1539941L, new String[] { }, "", "", "");
//        answerList.add(detail16);
//        UserAnswerDetail detail17 = new UserAnswerDetail(userId, 2484470L, 1539940L, new String[] { }, "", "", "");
//        answerList.add(detail17);
//        UserAnswerDetail detail18 = new UserAnswerDetail(userId, 2484472L, 1539942L, new String[] { }, "", "", "");
//        answerList.add(detail18);
//        UserAnswerDetail detail19 = new UserAnswerDetail(userId, 2484473L, 1539943L, new String[] { }, "", "", "");
//        answerList.add(detail19);
//        UserAnswerDetail detail20 = new UserAnswerDetail(userId, 2484474L, 1539944L, new String[] { }, "", "", "");
//        answerList.add(detail20);
//        UserAnswerDetail detail21 = new UserAnswerDetail(userId, 2484474L, 1539945L, new String[] { }, "", "", "");
//        answerList.add(detail21);
//        UserAnswerDetail detail22 = new UserAnswerDetail(userId, 2484474L, 1539946L, new String[] { }, "", "", "");
//        answerList.add(detail22);
//        UserAnswerDetail detail23 = new UserAnswerDetail(userId, 2484474L, 1539947L, new String[] { }, "", "", "");
//        answerList.add(detail23);
//
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_UserAnswerPaper(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                for (UserAnswerDetail detail : details) {
//                    double score = detail.getScore();
//                    long topicId = detail.getTopicId();
//                    int isRight = detail.getIsRight();
//                    System.out.println(topicId + "|" + score + "|" + isRight);
//                }
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//    @Test
//    public void sty_userAnswerPaper2() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setMsg("{\"uid\":151236240,\"e_paper_id\":22641,\"paper_id\":2120,\"is_submit\":1,\"product_id\":22641,\"goods_id\":9398,\"start_time\":1634175289438,\"end_time\":1634175334284," +
//                    "\"answer_detail\":[{\"answer\":[\"B\"],\"question_id\":2484380,\"topic_id\":1539841},{\"answer\":[\"B\",\"C\"],\"question_id\":2484384,\"topic_id\":1539845},{\"answer\":[\"B\",\"C\"],\"question_id\":2484465,\"topic_id\":1539935},{\"answer\":[\"A\"],\"question_id\":2484470,\"topic_id\":1539940},{\"answer\":[\"B\"],\"question_id\":2484382,\"topic_id\":1539843},{\"answer\":[\"C\"],\"question_id\":2484460,\"topic_id\":1539930},{\"answer\":[\"hchfhfj\"],\"question_id\":2484459,\"topic_id\":1539929},{\"answer\":[\"dhhfh\",\"fhfh\",\"fjfj\"],\"question_id\":2484468,\"topic_id\":1539938},{\"answer\":[\"hxhchc\"],\"question_id\":2484472,\"topic_id\":1539942},{\"answer\":[\"B\"],\"question_id\":2484474,\"topic_id\":1539944},{\"answer\":[\"A\"],\"question_id\":2484474,\"topic_id\":1539945},{\"answer\":[\"jcjf\",\"chfj\"],\"question_id\":2484474,\"topic_id\":1539946},{\"answer\":[\"hdhfjf\"],\"question_id\":2484474,\"topic_id\":1539947}]," +
//                    "\"obj_type\":0,\"category_id\":23184,\"done_mode\":2,\"is_on_error_list\":0,\"source\":0}");
//            response res = client.sty_UserAnswerPaper(req);
//            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//            }.getType();
//            List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//            System.out.println("sty_userAnswerPaper2>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    @Test
//    public void sty_userAnswerPaper3() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setMsg("{\"uid\":151236240,\"e_paper_id\":22641,\"paper_id\":2120,\"is_submit\":1,\"product_id\":22641,\"goods_id\":9398,\"start_time\":1634175289438,\"end_time\":1634175334284," +
//                    "\"answer_detail\":[{\"answer\":[\"B\"],\"question_id\":2484380,\"topic_id\":1539841},{\"answer\":[],\"question_id\":2484384,\"topic_id\":1539845},{\"answer\":[],\"question_id\":2484465,\"topic_id\":1539935},{\"answer\":[],\"question_id\":2484470,\"topic_id\":1539940},{\"answer\":[],\"question_id\":2484382,\"topic_id\":1539843},{\"answer\":[],\"question_id\":2484460,\"topic_id\":1539930},{\"answer\":[],\"question_id\":2484459,\"topic_id\":1539929},{\"answer\":[\"你好\",\"世界\",\"和平\"],\"question_id\":2484468,\"topic_id\":1539938},{\"answer\":[],\"question_id\":2484472,\"topic_id\":1539942},{\"answer\":[],\"question_id\":2484474,\"topic_id\":1539944},{\"answer\":[],\"question_id\":2484474,\"topic_id\":1539945},{\"answer\":[],\"question_id\":2484474,\"topic_id\":1539946},{\"answer\":[],\"question_id\":2484474,\"topic_id\":1539947}]," +
//                    "\"obj_type\":0,\"category_id\":23184,\"done_mode\":2,\"is_on_error_list\":0,\"source\":0}");
//            response res = client.sty_UserAnswerPaper(req);
//            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//            }.getType();
//            List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//            System.out.println("sty_userAnswerPaper2>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    @Test
//    public void sty_userAnswerPaper1() {
//
//        long userId = 11587537L;
//        long paperId = 27058L;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("paper_id", paperId);
//        param.put("paper_type", 3);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("source",0);
//        param.put("product_id",25806L);
//        param.put("goods_id",32521L);
//        param.put("is_submit", 1);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 2695024L, 1761395L, new String[] { "A" }, "", "", "");
//        answerList.add(detail1);
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 2695023L, 1761394L, new String[] { "A" }, "", "", "");
//        answerList.add(detail3);
//        UserAnswerDetail detail4 = new UserAnswerDetail(userId, 2695022L, 1761393L, new String[] { "A" }, "", "", "");
//        answerList.add(detail4);
//        param.put("answer_detail", answerList);
//
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("*************", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
////            req.setMsg(gson.toJson(param));
//            req.setMsg("{\"answer_detail\":[{\"uid\":184229518,\"question_id\":3087260,\"topic_id\":2192841,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2922146,\"topic_id\":2011668,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087268,\"topic_id\":2192849,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2908316,\"topic_id\":1996258,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2908318,\"topic_id\":1996260,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087278,\"topic_id\":2192859,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087286,\"topic_id\":2192867,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2922181,\"topic_id\":2011703,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2922187,\"topic_id\":2011712,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087320,\"topic_id\":2192901,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3063566,\"topic_id\":2167332,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087352,\"topic_id\":2192933,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087358,\"topic_id\":2192939,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2553945,\"topic_id\":1614772,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087395,\"topic_id\":2192976,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087400,\"topic_id\":2192981,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087406,\"topic_id\":2192987,\"answer\":[\"B\"]},{\"uid\":184229518,\"question_id\":2429240,\"topic_id\":1477370,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2743677,\"topic_id\":1813144,\"answer\":[\"B\"]},{\"uid\":184229518,\"question_id\":3087421,\"topic_id\":2193002,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3032207,\"topic_id\":2133685,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087453,\"topic_id\":2193034,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087468,\"topic_id\":2193049,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087477,\"topic_id\":2193058,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3078183,\"topic_id\":2182993,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2553959,\"topic_id\":1614791,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087493,\"topic_id\":2193074,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3087503,\"topic_id\":2193084,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":2922314,\"topic_id\":2011852,\"answer\":[\"A\"]},{\"uid\":184229518,\"question_id\":3075703,\"topic_id\":2180374,\"answer\":[\"A\"]}],\"uid\":184229518,\"paper_id\":89912,\"paper_type\":1,\"start_time\":1634647986358,\"end_time\":1634648049686,\"is_submit\":1,\"plat_form\":\"android\",\"appid\":\"examtrain_wxapp\",\"obj_id\":1130327,\"source\":0,\"product_id\":82332,\"goods_id\":101851} ");
//            response res = client.sty_UserAnswerPaper(req);
//            System.out.println(GsonUtil.toJson(res));
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_userAnswerHomework() {
//
//        long userId = 5001124l;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("id", 6L);
//        param.put("obj_id", 22222);
//        param.put("obj_type", 0);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 1);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 25l, 21l, new String[] { "A" }, "", "", "");
//        answerList.add(detail1);
//
//        UserAnswerDetail detail2 = new UserAnswerDetail(userId, 32l, 38l, new String[] { "A" }, "", "", "");
//        answerList.add(detail2);
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 39l, 30l, new String[] { "B" }, "", "", "");
//        answerList.add(detail3);
//        // UserAnswerDetail detail4 = new UserAnswerDetail(userId, 47l, 55l, new
//        // String[]{"B,C"},
//        // "", "", "");
//        // answerList.add(detail4);
//        // UserAnswerDetail detail5 = new UserAnswerDetail(userId, 31l, 44l, new
//        // String[]{"C"},
//        // "", "", "");
//        // answerList.add(detail5);
//        // UserAnswerDetail detail6 = new UserAnswerDetail(userId, 14l, 11l, new
//        // String[]{"A"},
//        // "", "", "");
//        // answerList.add(detail6);
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_userAnswerHomework(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//
//    @Test
//    public void sty_userAnswerHomework3() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("127.0.0.1", PORT));
////            transport = new TFramedTransport(new TSocket("*************", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            //"error_list_type":2,"error_type":3,
//            req.setMsg("{\"uid\":151236240,\"is_on_error_list\":0,\"start_time\":1634349289429,\"end_time\":1634349362593,\"is_submit\":1,\"plat_form\":\"ios\",\"appid\":\"edu24olapp\",\"product_id\":21522,\"goods_id\":9285," +
//                    "\"obj_id\":158110,\"obj_type\":0," +
//                    "\"answer_detail\":[{\"answer\":[\"B\"],\"question_id\":2484382,\"topic_id\":1539843},{\"answer\":[],\"question_id\":2484381,\"topic_id\":1539842},{\"answer\":[],\"question_id\":2484380,\"topic_id\":1539841},{\"answer\":[],\"question_id\":2484460,\"topic_id\":1539930},{\"answer\":[],\"question_id\":2484461,\"topic_id\":1539931},{\"answer\":[],\"question_id\":2484463,\"topic_id\":1539933},{\"answer\":[],\"question_id\":2484465,\"topic_id\":1539935},{\"answer\":[],\"question_id\":2484466,\"topic_id\":1539936},{\"answer\":[],\"question_id\":2484468,\"topic_id\":1539938},{\"answer\":[],\"question_id\":2484469,\"topic_id\":1539939},{\"answer\":[],\"question_id\":2484470,\"topic_id\":1539940},{\"answer\":[],\"question_id\":2484471,\"topic_id\":1539941},{\"answer\":[],\"question_id\":2484473,\"topic_id\":1539943},{\"answer\":[],\"question_id\":2484472,\"topic_id\":1539942},{\"answer\":[\"A\"],\"question_id\":2484474,\"topic_id\":1539944},{\"answer\":[\"A\"],\"question_id\":2484474,\"topic_id\":1539945},{\"answer\":[\"春花秋月何时了\",\"往日知多少\"],\"question_id\":2484474,\"topic_id\":1539946},{\"answer\":[测试11],\"question_id\":2484474,\"topic_id\":1539947}]" +
//                    "}");
//            req.setAppid(7);
//            response res = client.sty_userAnswerHomework(req);
//            Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//            }.getType();
//            System.out.println(res);
//            List<UserAnswerDetail> result = GsonUtil.getGenericGson().fromJson(res.getMsg(), type);
//            String str1 = GsonUtil.getGson().toJson(result);
//            Type typeTmp = new com.google.gson.reflect.TypeToken<List<Map>>() {
//            }.getType();
//            List<Map> mapList = GsonUtil.getGenericGson().fromJson(str1, typeTmp);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_personalStudyAnswerNoPaper() {
//
//        long userId = 5001124l;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("group_id", 19l);
////        param.put("id", 3l);
//        param.put("task_id", 166l);
//        param.put("type", 0);
//        param.put("obj_id", 206);
//        param.put("obj_type", 1);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 1);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 25l, 21l, new String[] { "A" }, "", "", "");
//        answerList.add(detail1);
//
//        UserAnswerDetail detail2 = new UserAnswerDetail(userId, 32l, 38l, new String[] { "A" }, "", "", "");
//        answerList.add(detail2);
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 39l, 30l, new String[] { "B" }, "", "", "");
//        answerList.add(detail3);
//        // UserAnswerDetail detail4 = new UserAnswerDetail(userId, 47l, 55l, new
//        // String[]{"B,C"},
//        // "", "", "");
//        // answerList.add(detail4);
//        // UserAnswerDetail detail5 = new UserAnswerDetail(userId, 31l, 44l, new
//        // String[]{"C"},
//        // "", "", "");
//        // answerList.add(detail5);
//        // UserAnswerDetail detail6 = new UserAnswerDetail(userId, 14l, 11l, new
//        // String[]{"A"},
//        // "", "", "");
//        // answerList.add(detail6);
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_personalStudyAnswer(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//    @Test
//    public void sty_personalStudyAnswerWithPaper() {
//
//        long userId = 11065863l;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        // param.put("paper_id", 23l);
//        param.put("group_id", 19l);
//        param.put("task_id", 30l);
//        param.put("type", 0);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 1);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 25l, 21l, new String[] { "B" }, "", "", "");
//        answerList.add(detail1);
//
//        UserAnswerDetail detail2 = new UserAnswerDetail(userId, 32l, 38l, new String[] { "萨范德萨地方" }, "", "", "");
//        answerList.add(detail2);
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 39l, 30l, new String[] { "B" }, "", "", "");
//        answerList.add(detail3);
//        // UserAnswerDetail detail4 = new UserAnswerDetail(userId, 47l, 55l, new
//        // String[]{"B,C"},
//        // "", "", "");
//        // answerList.add(detail4);
//        // UserAnswerDetail detail5 = new UserAnswerDetail(userId, 31l, 44l, new
//        // String[]{"C"},
//        // "", "", "");
//        // answerList.add(detail5);
//        // UserAnswerDetail detail6 = new UserAnswerDetail(userId, 14l, 11l, new
//        // String[]{"A"},
//        // "", "", "");
//        // answerList.add(detail6);
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_personalStudyAnswer(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//    @Test
//    public void sty_getUserErrPaper() {
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", 50002144l);
//        param.put("from", 0);
//        param.put("rows", 10);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                response res = client.sty_getUserErrPaper(req);
//                System.out.println(res);
//            }
//        });
//    }
//
//    @Test
//    public void sty_getUserErrAnswerQuestion() {
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", 50002144l);
//        param.put("p_id", 1);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                response res = client.sty_getUserErrAnswerQuestion(req);
//                System.out.println(res);
//            }
//        });
//    }
//
//    private void testThirftApi(Map<String, Object> param, TestThriftApi action) {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setMsg(gson.toJson(param));
//            action.test(client, req);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private interface TestThriftApi {
//        public void test(Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client, GrpcRequest req) throws TException;
//    }
//
//
//    @Test
//    public void sty_getUserErrQuestionList() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11065863l);
//            param.put("lessonId", 164l);
//            param.put("from", 0);
//            param.put("rows", 10);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserErrQuestionList(req);
//            System.out.println("sty_getUserErrQuestionList.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserLatestLessonAnswer() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(6);
//            param.put("uid", 11065863l);
//            param.put("obj_id", 164l);
//            param.put("obj_type", 0);
//            param.put("from", 0);
//            param.put("rows", 10);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserLatestLessonAnswer(req);
//            System.out.println("sty_getUserLatestLessonAnswer.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserHomeWorkInfo() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
////            transport = new TFramedTransport(new TSocket("*************", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
//            param.put("uid", 151236240l);
//            param.put("obj_id", 158103);
//            param.put("product_id", 21520);
//            param.put("obj_type", 0);
//            param.put("is_latest", 1);
//            param.put("from", 0);
//            param.put("rows", 10);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserHomeWorkInfo(req);
//            Type type = new com.google.gson.reflect.TypeToken<PageModel<UserHomeWorkAnswer>>() {
//            }.getType();
//            PageModel<UserHomeWorkAnswer> result = GsonUtil.getGenericGson().fromJson(res.getMsg(), type);
//            System.out.println("sty_getUserHomeWorkInfo.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
    @Test
    public void sty_getUserHomeWorkDetail() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 11498706L);
            param.put("user_homework_id", "1351556159711875072");

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.styGetUserHomeWorkDetail(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

//    @Test
//    public void sty_isDoHomeWorkById() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
//            param.put("uid", 11065863l);
//            param.put("obj_type", 0);
//            param.put("obj_id", new Long[] { 164L, 188L, 18839L });
//            param.put("from", 0);
//            param.put("rows", 30);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_isDoHomeWorkById(req);
//            System.out.println("sty_isDoHomeWorkById.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserHomeWorkRecodById() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
////            param.put("uid", 11065863l);
////            param.put("user_homework_id", 80L);
//
////            param.put("uid", 102847348L);
////            param.put("user_homework_id", 232167600308629505L);
//
////            param.put("uid", 100451919L);
////            param.put("user_homework_id", 232183277346095104L);
//
////            param.put("uid", 151114283L);
////            param.put("user_homework_id", 204157237357182976L);
//
////            param.put("uid", 103079915L);
////            param.put("user_homework_id", 238302435246870529L);
//
//            param.put("uid", 100128321L);
//            param.put("user_homework_id", 238966733145124860L);
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserHomeWorkRecodById(req);
//            System.out.println("sty_getUserHomeWorkRecodById.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void sty_staticQuestionAnswerBatch() {
//
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//
//            transport.open();
//            request req = new request();
//            List<Long> param = new ArrayList<Long>();
//            param.add(223L);
//            param.add(10L);
//            param.add(11L);
//            param.add(12L);
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_staticQuestionAnswerBatch(req);
//            System.out.println("sty_staticQuestionAnswerBatch=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_staticQuestionAnswer() {
//
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("question_id", 261L);
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_staticQuestionAnswer(req);
//            System.err.println(res.getErrormsg());
//            System.err.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_staticPaperAnswer() {
//
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("paper_id", 17L);
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_staticPaperAnswer(req);
//            System.err.println(res.getErrormsg());
//            System.err.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void sty_userAnswerBoxExercise4KnowledgeTest() {
//
//        long userId = 11065863l;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("teach_book_id", 15L);
//        param.put("box_id", 282L);
//        param.put("obj_id", 4L);
//        param.put("obj_type", 5);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 1);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 171l, 185l, new String[] { "A" }, "", "", "");
//        answerList.add(detail1);
//
//        UserAnswerDetail detail2_1 = new UserAnswerDetail(userId, 142l, 147l, new String[] { "B" }, "", "", "");
//        UserAnswerDetail detail2_2 = new UserAnswerDetail(userId, 142l, 148l, new String[] { "B" }, "", "", "");
//        UserAnswerDetail detail2_3 = new UserAnswerDetail(userId, 142l, 149l, new String[] { "A" }, "", "", "");
//        answerList.add(detail2_1);
//        answerList.add(detail2_2);
//        answerList.add(detail2_3);
//
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 309l, 307053l, new String[] { "B" }, "", "", "");
//        answerList.add(detail3);
//        UserAnswerDetail detail4 = new UserAnswerDetail(userId, 310l, 307054l, new String[] { "E" }, "", "", "");
//        answerList.add(detail4);
//        // UserAnswerDetail detail4 = new UserAnswerDetail(userId, 47l, 55l, new
//        // String[]{"B,C"},
//        // "", "", "");
//        // answerList.add(detail4);
//        // UserAnswerDetail detail5 = new UserAnswerDetail(userId, 31l, 44l, new
//        // String[]{"C"},
//        // "", "", "");
//        // answerList.add(detail5);
//        // UserAnswerDetail detail6 = new UserAnswerDetail(userId, 14l, 11l, new
//        // String[]{"A"},
//        // "", "", "");
//        // answerList.add(detail6);
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_userAnswerBoxExercise(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//    @Test
//    public void sty_userAnswerBoxExercise4ChapterTest() {
//
////        long userId = 11065863l;
////
////        Map<String, Object> param = new HashMap<String, Object>();
////        param.put("uid", userId);
////        param.put("teach_book_id", 15L);
////        param.put("box_id", 282L);
////        param.put("obj_id", 3L);
////        param.put("obj_type", 5);
////        param.put("start_time", new Date());
////        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
////        param.put("is_submit", 1);
////
////        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
////        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 169l, 181l, new String[] { "B","C" }, "", "", "");
////        answerList.add(detail1);
////
////        UserAnswerDetail detail2_1 = new UserAnswerDetail(userId, 79l, 70l, new String[] { "A" }, "", "", "");
////        UserAnswerDetail detail2_2 = new UserAnswerDetail(userId, 79l, 71l, new String[] { "A" }, "", "", "");
////        UserAnswerDetail detail2_3 = new UserAnswerDetail(userId, 79l, 72l, new String[] { "A" }, "", "", "");
////        answerList.add(detail2_1);
////        answerList.add(detail2_2);
////        answerList.add(detail2_3);
////
////        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 313l, 307057l, new String[] { "B" }, "", "", "");
////        answerList.add(detail3);
////
////        UserAnswerDetail detail4_1 = new UserAnswerDetail(userId, 104l, 103l, new String[] { "A","C" }, "", "", "");
////        UserAnswerDetail detail4_2 = new UserAnswerDetail(userId, 104l, 104l, new String[] { "A" }, "", "", "");
////        UserAnswerDetail detail4_3 = new UserAnswerDetail(userId, 104l, 105l, new String[] { "D" }, "", "", "");
////        UserAnswerDetail detail4_4 = new UserAnswerDetail(userId, 104l, 105l, new String[] { "A","B" }, "", "", "");
////        answerList.add(detail4_1);
////        answerList.add(detail4_2);
////        answerList.add(detail4_3);
////        answerList.add(detail4_4);
////
////        UserAnswerDetail detail5_1 = new UserAnswerDetail(userId, 166l, 171l, new String[] { "A" }, "", "", "");
////        UserAnswerDetail detail5_2 = new UserAnswerDetail(userId, 166l, 172l, new String[] { "B" }, "", "", "");
////        UserAnswerDetail detail5_3 = new UserAnswerDetail(userId, 166l, 173l, new String[] { "D" }, "", "", "");
////        answerList.add(detail5_1);
////        answerList.add(detail5_2);
////        answerList.add(detail5_3);
////
////        param.put("answer_detail", answerList);
//
//        //测试
////        long userId = 11587537;
////
////        Map<String, Object> param = new HashMap<String, Object>();
////        param.put("uid", userId);
////        param.put("teach_book_id", 106L);
////        param.put("box_id", 1206L);
////        param.put("obj_id", 1240L);
////        param.put("obj_type", 5);
////        param.put("start_time", new Date());
////        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
////        param.put("is_submit", 1);
////
////        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
////        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 2480257L, 1535664L, new String[] { "C" }, "", "", "");
//////        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 2480257L, 1535664L, new String[] { "A" }, "", "", "");
////        answerList.add(detail1);
////
////        UserAnswerDetail detail2_1 = new UserAnswerDetail(userId, 2480256L, 1535663L, new String[] { "A" }, "", "", "");
////        answerList.add(detail2_1);
////        //A
//////        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 2480255L, 1535662L, new String[] { "B" }, "", "", "");
////        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 2480255L, 1535662L, new String[] { "A" }, "", "", "");
////        answerList.add(detail3);
////        //A
////        UserAnswerDetail detail4_1 = new UserAnswerDetail(userId, 2480254L, 1535661L, new String[] { "C" }, "", "", "");
////        answerList.add(detail4_1);
////        //A
////        UserAnswerDetail detail5_1 = new UserAnswerDetail(userId, 2480253L, 1535660L, new String[] { "A" }, "", "", "");
////        answerList.add(detail5_1);
////
////        param.put("answer_detail", answerList);
//
//        //正式
//       /* long userId = 11587537;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("teach_book_id", 1167L);
//        param.put("box_id", 1943L);
//        param.put("obj_id", 4283L);
//        param.put("obj_type", 5);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 1);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        //D
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 1024065L, 24065L, new String[] { "D" }, "", "", "");
//        answerList.add(detail1);
//        //D
//        UserAnswerDetail detail2_1 = new UserAnswerDetail(userId, 1024070L, 24070L, new String[] { "A" }, "", "", "");
//        answerList.add(detail2_1);
//        //B
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 1024254L, 24254L, new String[] { "A" }, "", "", "");
//        answerList.add(detail3);
//
//        param.put("answer_detail", answerList);*/
//
//
//       //wusiyue
//        long userId = 11065867;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("teach_book_id", 84L);
//        param.put("box_id",1074L);
//        param.put("obj_id", 271L);
//        param.put("obj_type", 5);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 1);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        //D
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 1136L, 1536179L, new String[] { "D" }, "", "", "");
//        answerList.add(detail1);
//        //D
//        UserAnswerDetail detail2 = new UserAnswerDetail(userId, 1137L, 1536182L, new String[] { "D" }, "", "", "");
//        answerList.add(detail2);
//        //B
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 1138L, 1536177L, new String[] { "D" }, "", "", "");
//        answerList.add(detail3);
//        //
//        UserAnswerDetail detail4 = new UserAnswerDetail(userId, 1134L, 1536159L, new String[] { "D" }, "", "", "");
//        answerList.add(detail4);
//
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_userAnswerBoxExercise(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//
//    @Test
//    public void sty_userAnswerBoxExercise4ChapterTest22() {
//
//        long userId = 11065863l;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("teach_book_id", 15L);
//        param.put("box_id", 282L);
//        param.put("obj_id", 35L);
//        param.put("obj_type", 5);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 2);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 106l, 107l, new String[] { "B" }, "", "", "");
//        answerList.add(detail1);
//
//        UserAnswerDetail detail2_1 = new UserAnswerDetail(userId, 109l, 111l, new String[] { "A" }, "", "", "");
//        answerList.add(detail2_1);
//
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 120l, 123l, new String[] { "B" }, "", "", "");
//        answerList.add(detail3);
//
//        UserAnswerDetail detail4_1 = new UserAnswerDetail(userId, 121l, 124l, new String[] { "A" }, "", "", "");
//        answerList.add(detail4_1);
//
//        UserAnswerDetail detail5_1 = new UserAnswerDetail(userId, 231l, 253l, new String[] { "A" }, "", "", "");
//        UserAnswerDetail detail5_2 = new UserAnswerDetail(userId, 231l, 254l, new String[] { "B" }, "", "", "");
//        answerList.add(detail5_1);
//        answerList.add(detail5_2);
//
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_userAnswerBoxExercise(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//    @Test
//    public void sty_userAnswerPaper_In_QuestionBox_Submit() {
//
////        long userId = 11065863l;
////        long paperId = 76l;
////
////        Map<String, Object> param = new HashMap<String, Object>();
////        param.put("uid", userId);
////        param.put("source", 2);
////        param.put("obj_id", 282);
////        param.put("obj_type", 2);
////        param.put("paper_id", paperId);
////        param.put("paper_type", 2);
////        param.put("start_time", new Date());
////        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
////        param.put("is_submit", 1);
////
////        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
////        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 309l, 307053l, new String[] { "A" }, "", "", "");
////        answerList.add(detail1);
////
////        UserAnswerDetail detail2 = new UserAnswerDetail(userId, 310l, 307054l, new String[] { "A" }, "", "", "");
////        answerList.add(detail2);
////
////        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 311l, 307055l, new String[] { "A" }, "", "", "");
////        answerList.add(detail3);
////
////        UserAnswerDetail detail4 = new UserAnswerDetail(userId, 312l, 307056l, new String[] { "B" }, "", "", "");
////        answerList.add(detail4);
////
////        UserAnswerDetail detail5 = new UserAnswerDetail(userId, 313l, 307057l, new String[] { "A" }, "", "", "");
////        answerList.add(detail5);
////
////        param.put("answer_detail", answerList);
//
//        long userId = 11065863l;
//        long paperId = 363l;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("source", 2);
//        param.put("obj_id", 1598);
//        param.put("obj_type", 2);
//        param.put("paper_id", paperId);
//        param.put("paper_type", 2);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 1);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 2480450L, 1535860L, new String[] { "C" }, "", "", "");
//        answerList.add(detail1);
//
//        UserAnswerDetail detail2 = new UserAnswerDetail(userId, 2480451L, 1535861L, new String[] { "B" }, "", "", "");
//        answerList.add(detail2);
//
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 2480452L, 1535862L, new String[] { "A" }, "", "", "");
//        answerList.add(detail3);
//
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_UserAnswerPaper(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                for (UserAnswerDetail detail : details) {
//                    double score = detail.getScore();
//                    long topicId = detail.getTopicId();
//                    int isRight = detail.getIsRight();
//                    System.out.println(topicId + "|" + score + "|" + isRight);
//                }
//
//                System.out.println(res);
//            }
//        });
//    }
//
//
//    @Test
//    public void sty_userAnswerPaper_In_QuestionBox_Save() {
//
//        long userId = 11065863l;
//        long paperId = 71l;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("source", 2);
//        param.put("obj_id", 282);
//        param.put("obj_type", 2);
//        param.put("paper_id", paperId);
//        param.put("paper_type", 2);
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 0);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 309l, 307053l, new String[] { "A" }, "", "", "");
//        answerList.add(detail1);
//
//        UserAnswerDetail detail2 = new UserAnswerDetail(userId, 310l, 307054l, new String[] { "A" }, "", "", "");
//        answerList.add(detail2);
//
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 311l, 307055l, new String[] { "A" }, "", "", "");
//        answerList.add(detail3);
//
//        UserAnswerDetail detail4 = new UserAnswerDetail(userId, 312l, 307056l, new String[] { "B" }, "", "", "");
//        answerList.add(detail4);
//
//        UserAnswerDetail detail5 = new UserAnswerDetail(userId, 313l, 307057l, new String[] { "A" }, "", "", "");
//        answerList.add(detail5);
//
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_UserAnswerPaper(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                for (UserAnswerDetail detail : details) {
//                    double score = detail.getScore();
//                    long topicId = detail.getTopicId();
//                    int isRight = detail.getIsRight();
//                    System.out.println(topicId + "|" + score + "|" + isRight);
//                }
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//    @Test
//    public void sty_userGenerateAndAnswerBoxExerciseTest() {
//
//        long userId = 11065863l;
//
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("uid", userId);
//        param.put("teach_book_id", 15L);
//        param.put("box_id", 282L);
//        param.put("homeworkTypeId", 35L);//作业类型id(章节id/知识点id)
//        param.put("homeworkType", 1);//作业类型（0：所有，1：章节，2：知识点）
//        param.put("homeworkModel", 5);//作业模式（0：未做试题，1：错误试题，2：全部试题,3：智能练习【未做+错误】）
//        param.put("start_time", new Date());
//        param.put("end_time", DateUtils.addMinutes(new Date(), 3));
//        param.put("is_submit", 2);
//
//        List<UserAnswerDetail> answerList = new ArrayList<UserAnswerDetail>();
//        UserAnswerDetail detail1 = new UserAnswerDetail(userId, 106l, 107l, new String[] { "B" }, "", "", "");
//        answerList.add(detail1);
//
//        UserAnswerDetail detail2_1 = new UserAnswerDetail(userId, 109l, 111l, new String[] { "A" }, "", "", "");
//        answerList.add(detail2_1);
//
//        UserAnswerDetail detail3 = new UserAnswerDetail(userId, 120l, 123l, new String[] { "B" }, "", "", "");
//        answerList.add(detail3);
//
//        UserAnswerDetail detail4_1 = new UserAnswerDetail(userId, 121l, 124l, new String[] { "A" }, "", "", "");
//        answerList.add(detail4_1);
//
//        UserAnswerDetail detail5_1 = new UserAnswerDetail(userId, 231l, 253l, new String[] { "A" }, "", "", "");
//        UserAnswerDetail detail5_2 = new UserAnswerDetail(userId, 231l, 254l, new String[] { "B" }, "", "", "");
//        answerList.add(detail5_1);
//        answerList.add(detail5_2);
//
//        param.put("answer_detail", answerList);
//
//        this.testThirftApi(param, new TestThriftApi() {
//            @Override
//            public void test(edu100_study.Client client, request req) throws TException {
//                req.setAppid(6);
//                response res = client.sty_userAnswerBoxExercise(req);
//                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerDetail>>() {
//                }.getType();
//                List<UserAnswerDetail> details = GsonUtil.getGson().fromJson(res.getMsg(), type);
//
//                System.out.println(res);
//            }
//        });
//
//    }
//
//    @Test
//    public void sty_userAnswerBoxExercise(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("39.105.150.133", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
//            req.setMsg(" {\"uid\":11498706,\"teach_book_id\":95,\"box_id\":471,\"obj_id\":2094,\"obj_type\":5,\"start_time\":1635387006890,\"end_time\":1635387016890,\"answer_detail\":[{\"uid\":11498706,\"question_id\":2484378,\"topic_id\":1539914,\"answer\":[\"B\"]}],\"is_submit\":1,\"appid\":\"wwwedu24ol\",\"plat_form\":\"web\"} ");
//            response res = client.sty_userAnswerBoxExercise(req);
//            System.out.println("sty_userAnswerBoxExercise.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//
//    @Test
//    public void sty_getUserFinishedPaperCount(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
//            param.put("uid", 11335340l);
//            param.put("goods_id", 99l);
//            param.put("paper_ids", new Long[]{144l, 184l, 182l, 199l});
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserFinishedPaperCount(req);
//            System.out.println("sty_getUserFinishedPaperCount.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    /**
//     * 获取完成的试卷数量
//     */
//    @Test
//    public void sty_getUserSubmitHomeWorkQuestionCount(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
//            param.put("uid", 11335340l);
//            param.put("goods_id", 99);
//            param.put("lesson_ids", new Long[] { 2357l,2844l,2933l,3565l,3488l });
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserSubmitHomeWorkQuestionCount(req);
//            System.out.println("sty_getUserSubmitHomeWorkQuestionCount.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    /**
//     * 获取完成的试卷数量
//     */
//    @Test
//    public void sty_getStudyAvgCountList(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(6);
//            param.put("uid", 11335340l);
//
//            List list = new ArrayList();
//            list.add(183l);
//            list.add(184l);
//
//            param.put("paperIdList", list);
//            //param.put("paperId", 183);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getStudyAvgCountList(req);
//            System.out.println("sty_getStudyAvgCountList.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void sty_adminstudyfindList(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            UserAnswer userAnswer = new UserAnswer();
//
//            userAnswer.setUid(11335340l);
//            userAnswer.setPaperId(183l);
//
//            req.setMsg(gson.toJson(userAnswer));
//            response res = client.sty_adminstudyfindList(req);
//            System.out.println("sty_adminstudyfindList.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_adminstudyfindAnswerSumQuestion(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//
//
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(6);
//
//            param.put("uid", 11335340l);
//            param.put("uAnswerIdList", new Long[]{4260l, 4261l, 4262l});
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_adminstudyfindAnswerSumQuestion(req);
//            System.out.println("sty_adminstudyfindAnswerSumQuestion.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void sty_findListByStudyReportQuery(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            StudyReportQuery studyReportQuery = new StudyReportQuery();
//            studyReportQuery.setStudyLogType(StudyReportQuery.StudyLogType.HOME_WORK);
//            studyReportQuery.setUid(11335340L);
//            studyReportQuery.setGoodsId(22L);
//            studyReportQuery.setFrom(0);
//            studyReportQuery.setPageSize(10);
//            // List<Long> objIdList = Lists.newArrayList();
//            // objIdList.add(2357L);
//            // studyReportQuery.setLessonIdList(objIdList);
//
//            req.setMsg(gson.toJson(studyReportQuery));
//            response res = client.sty_findListByStudyReportQuery(req);
//            System.out.println("sty_findListByStudyReportQuery.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
    @Test
    public void sty_getUserAnswersForGoods(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(7);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 11498706);
            param.put("goods_id", 9282);
            param.put("paper_ids", new Long[]{2096L,2119L});
            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.styGetUserAnswersForGoods(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }

    }
//
//    /**
//     * 获取完成的试卷数量
//     */
//    @Test
//    public void sty_getUserHomeWorkAnswersForGoods(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
//            param.put("uid", 11335340l);
//            param.put("goods_id", 99);
//            param.put("lesson_ids", new Long[] { 2357l,2844l,2933l,3565l,3488l });
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserHomeWorkAnswersForGoods(req);
//            System.out.println("sty_getUserHomeWorkAnswersForGoods.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 获取用户学习时长统计
//     */
//    @Test
//    public void sty_getUserStudyLengthStatistics(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("103.227.123.24", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
//            param.put("uid", 11587537L);
//            param.put("obj_id", 5589L);
//            param.put("date","2020-02-22");
//            // req.setMsg(gson.toJson(param));
//            req.setMsg("{\"date\":\"2020-09-03\",\"uid\":177418772,\"obj_id\":5627,\"productId\":32172}");
//            response res = client.sty_getUserStudyLengthStatistics(req);
//            System.out.println("sty_getUserStudyLengthStatistics.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 获取完成的试卷数量
//     */
//    @Test
//    public void sty_getUserHomeWorkCount(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            req.setAppid(7);
//            param.put("uid", 172646490);
//            param.put("obj_id", 5589);
//            param.put("obj_type", 201);
//            Date now = new Date();
//            param.put("start_time", DateUtils.setHours(now,0));
//            param.put("end_time", DateUtils.setHours(now,23));
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserHomeWorkCount(req);
//            System.out.println("sty_getUserHomeWorkCount.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 获取试卷完成的情况
//     */
//    @Test
//    public void sty_getUserAnswerCompletionList() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//
//            StudyCompletionInfoQuery param = new StudyCompletionInfoQuery();
//            req.setAppid(7);
//            param.setUid(11084928L);
//            param.setCategoryId(298L);
//            param.setFrom(0);
//            param.setPageSize(10);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserAnswerCompletionList(req);
//            System.out.println("sty_getUserAnswerCompletionList.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//     /**
//     * 获取作业完成的情况
//     */
//    @Test
//    public void sty_getHomeworkCompletionList(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//
//            StudyCompletionInfoQuery param=new StudyCompletionInfoQuery();
//            req.setAppid(7);
//            param.setUid(11498699L);
//            param.setFrom(0);
//            param.setPageSize(10);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getHomeworkCompletionList(req);
//            System.out.println("sty_getHomeworkCompletionList.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//    /**
//     * 获取作业完成的具体情况
//     */
//    @Test
//    public void sty_findHomeworkDetailsList(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("103.227.123.24", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//
//            UserHomeWorkAnswer param=new UserHomeWorkAnswer();
//            req.setAppid(7);
//            param.setUid(11498699L);
//            param.setObjId(2974L);
//            param.setObjTypes(Arrays.asList(202L, 203L));
//            param.setStates(Arrays.asList(2,3));
////            param.setFrom(0);
////            param.setPageSize(10);
////             req.setMsg(gson.toJson(param));
//            req.setMsg("{\"uid\":101893639,\"start_time\":1598495276000,\"end_time\":1598509198000,\"product_id\":32173,\"obj_types\":[202,203],\"states\":[2,3],\"del_flag\":\"0\",\"is_new_record\":false} ");
//            response res = client.sty_findHomeworkDetailsList(req);
//            System.out.println("sty_findHomeworkDetailsList.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 获取作业完成的具体情况
//     */
//    @Test
//    public void sty_getAnswerDetailList(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("103.227.123.24", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//
//            req.setAppid(7);
//            req.setMsg("{\"uid\":101893639,\"start_time\":1598495276000,\"end_time\":1598509198000,\"product_id\":32173,\"obj_types\":[202,203],\"states\":[2,3],\"del_flag\":\"0\",\"is_new_record\":false} ");
//            response res = client.sty_getHomeworkDetailsList(req);
//            System.out.println("sty_getHomeworkDetailsList.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserAnswerStudyReportByGoodsId(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//
//            req.setAppid(7);
//            req.setMsg("{\"uid\":194269966,\"goodsId\":8566}");
//            response res = client.sty_getUserAnswerStudyReportByGoodsId(req);
//            System.out.println("sty_getUserAnswerStudyReportByGoodsId.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
    @Test
    public void sty_getUserAnswerSumFindAllList(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 11498706L);
            param.put("is_al", 1);
            param.put("goods_id", 103563L);
            req.setMsg(gson.toJson(param));
            GrpcResponse response = client.styGetUserAnswerSumFindAllList(req.build());
            Type type = new com.google.gson.reflect.TypeToken<List<UserAnswerSumDTO>>() {
            }.getType();
            List<UserAnswerSumDTO> rs = GsonUtil.getGson().fromJson(response.getMsg() == null ? null : (String)response.getMsg(), type);
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
//
//    @Test
//    public void sty_getUserAnswerMedicalList(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("127.0.0.1", 8285));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setMsg("{\"ePaperId\":21847,\"paperId\":2085,\"from\":"+0*30+",\"rows\":30,\"isPass\":1}");//"uid":11498706,
//            response res = client.sty_getUserAnswerMedicalList(req);
//            System.out.println("sty_getUserAnswerMedicalList.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUidCountByEPaperId(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("127.0.0.1", 8285));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setMsg("{\"ePaperId\":21847,\"paperId\":2085}");//"uid":11498706,
//            response res = client.sty_getUidCountByEPaperId(req);
//            System.out.println("sty_getUidCountByEPaperId.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserAnswerMedicalListByIds(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("127.0.0.1", 8285));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            Map<String, Object> param = new HashMap<String, Object>();
//            List<Long> paperIds = new ArrayList<>();
//            Long paperId = 4455L;
//            Long paperId2 = 4454L;
//            Long paperId3 = 4453L;
//            paperIds.add(paperId);
//            paperIds.add(paperId2);
//            paperIds.add(paperId3);
//            param.put("ids", paperIds);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserAnswerMedicalListByIds(req);
//            System.out.println("sty_getUserAnswerMedicalListByIds.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
    @Test
    public void sty_getUserHomeworkLastAnswerInfo(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            req.setTraceId("ooooooookkkkkkkkkkkkk");
            req.setMsg("{\"uid\":243506173,\"productId\":235882,\"goodsId\":191080,\"lessonIds\":[2790817,2790821,2790844,2790850,2790853,2790859,2790862,2790865,2790867,2790870,2790873,2790980,2790884,2790890,2790895,2790898,2790901,2790905,2790907,2790909,2790912,2790916,2790919,2790923,2790926,2790929,2790933,2790935,2790942,2790946,2790949,2790951,2790956,2790960,2790966,2790970,2790976]}");
            GrpcResponse res = client.styGetUserHomeworkLastAnswerInfo(req.build());
            System.out.println(res.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
//
    @Test
    public void sty_getUserDoneRecordVoList(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 103453390);
            param.put("from", 0);
            param.put("rows", 20);
            param.put("isHaveError",1);
            param.put("isAl", 1);
            param.put("goodsId", 155459);
            param.put("categoryId", 5847);
            param.put("sourceType", 0);
            req.setMsg("{\"uid\":235403718,\"isHaveError\":1,\"sourceType\":1,\"goodsId\":174404,\"from\":20,\"rows\":10,\"categoryId\":5608}");
            GrpcResponse res = client.styGetUserDoneRecordVoList(req.build());
            System.out.println(res.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }




    @Test
    public void sty_findUserAnswerErrorQuestionInfoList(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            UserErrorAnswerQuery param = new UserErrorAnswerQuery();
            param.setUid(196726689L);
            param.setAnswerType(1);
            param.setAnswerIdList(List.of(1284194320854171648L));
            req.setMsg(GsonUtil.getGenericGson().toJson(param));
            GrpcResponse res = client.styFindUserAnswerErrorQuestionInfoList(req.build());
            System.out.println(res.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
//
//    @Test
//    public void sty_getUserHomeWorkAnswerById(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("*************", 8285));
////            transport = new TFramedTransport(new TSocket("127.0.0.1", 8285));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            Map<String, Object> param = new HashMap<String, Object>();
////            param.put("uid", 11498706L);
////            param.put("id", 885530417535516672L);
//            param.put("uid", 151236095L);
//            param.put("id", 892059903261278208L);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserHomeWorkAnswerById(req);
//            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserHomeWorkAnswer>() {
//            }.getType();
//            UserHomeWorkAnswer userHomeWorkAnswer = GsonUtil.getGson(req.getAppid()).fromJson(res.getMsg(), type);
//            System.out.println("sty_getUserHomeWorkAnswerById.response=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_adminstudygetQuestionListByHomeworkAnswerId() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("homeworkAnswerId", 886263850201120768L);
//            param.put("uid", 11498706L);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_adminstudygetQuestionListByHomeworkAnswerId(req);
//            System.out.println("sty_adminstudygetQuestionListByHomeworkAnswerId>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_updateUserDoneRecord() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
////            param.put("categoryId", 23184L);
////            param.put("objType", 0);
//            param.put("uid", 11498706L);
//            param.put("delFlag", "1");
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_updateUserDoneRecord(req);
//            System.out.println("sty_updateUserDoneRecord>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserErrorProdInfo() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("127.0.0.1", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setMsg("{\"uid\":151236240,\"productIds\":[21376,1472,21520,21469,33357],\"categoryId\":5588}");
////            req.setMsg("{\"uid\":151237705,\"productIds\":[21520,21376],\"goodsId\": 9206}");
//            response res = client.sty_getUserErrorProdInfo(req);
//            System.out.println("sty_getUserErrorProdInfo>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserErrorQuestionCountByQtype() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("*************", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setSchId(1L);
//            req.setPschId(1L);
////            req.setMsg("{\"uid\":11498706,\"categoryId\":23184,\"goodsId\":8956}");
//            req.setMsg("{\"uid\":11498706,\"categoryId\":23184}");
//            response res = client.sty_getUserErrorQuestionCountByQtype(req);
//            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserQuestionCountVo>>() {
//            }.getType();
//            List<UserQuestionCountVo> userErrorEpaperQuestionCountVoList= GsonUtil.getGson(req.getAppid()).fromJson(res.getMsg(), type);
//            System.out.println("sty_getUserErrorQuestionCountByQtype>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserCenterCollectQuestionCountByQtype() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setSchId(1L);
//            req.setPschId(1L);
////            req.setMsg("{\"uid\":11498706,\"categoryId\":23184,\"goodsId\":8956}");
//            req.setMsg("{\"uid\":11498706}");
//            response res = client.sty_getUserCenterCollectQuestionCountByQtype(req);
//            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserQuestionCountVo>>() {
//            }.getType();
//            List<UserQuestionCountVo> userErrorEpaperQuestionCountVoList= GsonUtil.getGson(req.getAppid()).fromJson(res.getMsg(), type);
//            System.out.println("sty_getUserCenterCollectQuestionCountByQtype>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getStudyCenterUserCollectQuestionCountByCategory() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setSchId(1L);
//            req.setPschId(1L);
//            req.setMsg("{\"uid\":11498706,\"categoryId\":23184}");//,"goodsId":8956
//            response res = client.sty_getStudyCenterUserCollectQuestionCountByCategory(req);
//            System.out.println("sty_getStudyCenterUserCollectQuestionCountByCategory>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
    @Test
    public void sty_studyCenterOpenAutoRemoveErrorQuestion() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("127.0.0.1",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setMsg("{\"uid\":11498699,\"state\":1,\"rightTimes\":3}");
            GrpcResponse res = client.styStudyCenterOpenAutoRemoveErrorQuestion(req.build());
            System.out.println("sty_studyCenterOpenAutoRemoveErrorQuestion>>>" + res.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
//
//    @Test
//    public void sty_studyCenterGetIsAutoRemoveOpened() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setSchId(1L);
//            req.setPschId(1L);
//            req.setMsg("{\"uid\":151236095}");
//            response res = client.sty_studyCenterGetIsAutoRemoveOpened(req);
//            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Boolean>() {
//            }.getType();
//            Boolean result = GsonUtil.getGson(req.getAppid()).fromJson(res.getMsg(), type);
//            System.out.println("sty_studyCenterGetIsAutoRemoveOpened>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_delErrorSubQuestionByCategory() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setSchId(2L);
//            req.setPschId(14L);
////            req.setMsg("{\"uid\":151237705,\"categoryId\":5588,\"goodsId\":9206}");
//            req.setMsg("{\"uid\":151236240,\"categoryId\":5588,\"questionId\":2480270}");
//            response res = client.sty_delErrorSubQuestionByCategory(req);
//            System.out.println("sty_studyCenterGetIsAutoRemoveOpened>>>" + res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//

    @Test
    public void createTableSql(){
        for (int i = 1; i < 64; i=i+2) {
            String sql = "ALTER TABLE `user_answer_homework_"+i+"`\n" +
                    "ADD INDEX `homework_id_idx` (`homework_id`) USING BTREE ;";
            System.out.println(sql);
        }
    }

    @Test
    public void test() {
        List<Integer> rsList = Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13);
        Integer zoneTotal = 16;
        int zoneIndex = 13;
        int size = rsList.size();
        int zoneSize = size / zoneTotal;
        int startIndex = 0;
        int endIndex = 0;
        if (zoneTotal > size && zoneIndex < size) {//当分区数大于记录数时
            startIndex = zoneIndex;
            endIndex = zoneIndex + 1;
        } else {
            for (int i = 1; i <= zoneTotal; i++) {
                startIndex = endIndex;
                if (i == zoneTotal) {
                    endIndex = size;
                } else {
                    endIndex = zoneSize * i;
                }
                if (zoneIndex == (i - 1)) {
                    break;
                }
            }
        }

        System.out.println("当前分区：" + zoneIndex + "  开始下标：" + startIndex + "  结束下标：" + endIndex);
        List<Integer> subList = rsList.subList(startIndex, endIndex);
        System.out.println(subList);

        List<Integer> resIdList = rsList;
        int threadNum = 3;//线程数
        if (rsList.size() > threadNum) {
            int threadExeRows = rsList.size() / threadNum;
            List<Integer> subList1 = rsList.subList(0, threadExeRows);
            List<Integer> subList2 = rsList.subList(threadExeRows, threadExeRows * 2);
            List<Integer> subList3 = rsList.subList(threadExeRows * 2, rsList.size());
            System.out.println(subList3);
        } else {
            System.out.println(rsList);
        }

        String endTimeStr = " 05:00:00";
        String curYmd = DateUtil.dateToString(new Date(), DateUtil.FORMAT_YMD);
        Date endDate = DateUtil.StringToDate(curYmd+endTimeStr, DateUtil.FORMAT_YMDHMS);
        System.out.println(DateUtil.dateToString(endDate, DateUtil.FORMAT_YMDHMS));
        if (new Date(1651093163000L).getTime() > endDate.getTime()) {
            System.out.println(11);
        }
        List<Long> threadResIdList = Lists.newArrayList(80L, 99L);
        Long resId = 88L;
        if(threadResIdList.contains(resId)){
            System.out.println("之前已经进行了同步！resId="+resId);
        }
        System.out.println();
    }

    @Test
    public void sty_syncHomeworkVideoCourse(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("resIdList", Lists.newArrayList(999999L));
            param.put("isReadRedis", 0);
//            param.put("zoneIndex", 0);
//            param.put("zoneTotal", 2);
//            param.put("isClearRedis", 1);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.stySyncHomeworkVideoCourse(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void sty_syncHomeworkProductSchedule(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);//"*************"
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("resIdList", Lists.newArrayList(60972,62128,66931,67294,46931,57424,13317,62132,62733,67333,52628,60970,66928,15276,51732,52580,52704,56399,60968,63344,52626,56401,59822,65537,67951,69455,44404,46791,52573,59775,67954,69458,46806,52578,65810,67273,44632,52689,59747,69436,17028,44634,52523,56390,59750,65766,67948,69439,15173,44398));
            param.put("isReadRedis", 0);
//            param.put("zoneIndex", 0);
//            param.put("zoneTotal", 2);
//            param.put("isClearRedis", 1);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.stySyncHomeworkProductSchedule(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sty_syncHomeworkProductAdaptiveLearning(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);//"*************"
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("resIdList", Lists.newArrayList(68451,68635,68638,68648,68444,68492,68503,68629,68538,68512,68518,68643,68480,68530,68504,68637,68608,68450,68476,68471,68470,68519,68628,68571,68522,68619,68623,68606,68501,68624,68532,68610,68452,68443,68553,68445,68523,68604,68514,68515,68541,68505,68617,68500,68615,68521,68540,68616,68516,68517));
            param.put("isReadRedis", 0);
//            param.put("zoneIndex", 0);
//            param.put("zoneTotal", 2);
//            param.put("isClearRedis", 1);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.stySyncHomeworkProductAdaptiveLearning(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sty_syncHomeworkIdVideoCourse(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
//            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("resIdList", Lists.newArrayList(7883));
//            param.put("resIdList", Lists.newArrayList(80235));
            param.put("isReadRedis", 0);
//            param.put("zoneIndex", 0);
//            param.put("zoneTotal", 2);
//            param.put("isClearRedis", 1);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.stySyncHomeworkIdVideoCourse(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sty_syncHomeworkIdProductSchedule(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
//            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("resIdList", Lists.newArrayList(7645));
//            param.put("resIdList", Lists.newArrayList(529499));
            param.put("isReadRedis", 0);
//            param.put("zoneIndex", 0);
//            param.put("zoneTotal", 2);
//            param.put("isClearRedis", 1);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.stySyncHomeworkIdProductSchedule(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sty_syncHomeworkIdProductAdaptiveLearning(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
//            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);//"*************"
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("resIdList", Lists.newArrayList(22763));
//            param.put("resIdList", Lists.newArrayList(48703));
            param.put("isReadRedis", 0);
//            param.put("zoneIndex", 0);
//            param.put("zoneTotal", 2);
//            param.put("isClearRedis", 1);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.stySyncHomeworkIdProductAdaptiveLearning(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sty_syncLessonIdUserAnswerMedical(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            SyncHomeworkDTO param = new SyncHomeworkDTO();
            List<String> resIdStrList = Lists.newArrayList();
            resIdStrList.add("21910_64927");//产品id_试卷id
            param.setResIdStrList(resIdStrList);
            req.setMsg(GsonUtil.getGenericGson().toJson(param));
            GrpcResponse res = client.stySyncLessonIdUserAnswerMedical(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }

    }
    @Test
    public void styGetUserPaperAnswerLastInfo(){
        NettyChannelBuilder builder = null;
        try {
//            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            req.setMsg("{\"uid\":11498699,\"goodsId\":13018,\"objTypeList\":[0,10],\"paperIds\":[65312,64770,65429,65430]}");
            GrpcResponse res = client.styGetUserPaperAnswerLastInfo(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void sty_getUserAnswerDetailList(){
        NettyChannelBuilder builder = null;
        try {
//            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);

            UserAnswerDetailQuestionQuery info = new UserAnswerDetailQuestionQuery();
            info.setUid(241233677L);
            info.setAnswerId(1376838148434563072L);
            req.setMsg(GsonUtil.getGenericGson().toJson(info));
            GrpcResponse res = client.styGetUserAnswerDetailList(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
    @Test
    public void sty_getUserHomeWorkDetailList(){
        NettyChannelBuilder builder = null;
        try {
//            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);

            Map<String, Object> params = Maps.newHashMap();
            params.put("uid", 241233677L);
            params.put("user_homework_ids", List.of(1376838148434563072L));
            req.setMsg(GsonUtil.getGenericGson().toJson(params));
            GrpcResponse res = client.styGetUserHomeWorkDetailList(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testTriggerAiCorrecting(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            SubjectiveQuestionAiCorrectingServiceGrpc.SubjectiveQuestionAiCorrectingServiceBlockingStub client = SubjectiveQuestionAiCorrectingServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Long categoryId=21513L;
            Long questionId=2907188L;
            int questionSource =0;
            Long topicId=1995093L;
            Long uid=232519284L;
            Long userAnswerId=1376692688688754688L;
            SubjectiveQuestionAiCorrectingService s = new SubjectiveQuestionAiCorrectingServiceClientImpl(client, b -> {
                b.setAppid(7)
                        .setCodetype(1)
                        .setClientIp(IpConvert.ipToLong(Consts.Code.CLIENT_IP));
            });
            s.triggerAiCorrecting(uid, userAnswerId, categoryId, questionId, topicId, questionSource, new SubjectiveQuestionAiCorrectingService.StreamObserver<SubjectiveQuestionAiCorrectingService.StreamData>() {
                    @Override
                    public void onNext(SubjectiveQuestionAiCorrectingService.StreamData streamData) {
                        System.out.println("onNext:"+streamData);
                    }

                    @Override
                    public void onError(Throwable t) {
                        t.printStackTrace();
                    }

                    @Override
                    public void onCompleted() {
                        System.out.println("onCompleted");
                    }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void sty_getUserCorrectQuestion(){
        NettyChannelBuilder builder = null;
        try {
//            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("127.0.0.1",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);

            UserErrorAndCorrectQuestionQuery info = new UserErrorAndCorrectQuestionQuery();
            info.setUid(103453390L);
            info.setCategoryId(5847L);
            info.setGoodsId(153275L);
            info.setProductId(48278L);
            info.setSourceType(0);
            info.setType(2);
            req.setMsg(GsonUtil.getGenericGson().toJson(info));
            GrpcResponse res = client.styGetUserCorrectQuestion(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void correctSubjectiveResult(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            SubjectiveQuestionAiCorrectingServiceGrpc.SubjectiveQuestionAiCorrectingServiceBlockingStub client = SubjectiveQuestionAiCorrectingServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            int questionSource =1;
            Long uid=103453390L;
            Long userAnswerId=1390329081988792320L;
            SubjectiveQuestionAiCorrectingService s = new SubjectiveQuestionAiCorrectingServiceClientImpl(client, b -> {
                b.setAppid(7)
                        .setCodetype(1)
                        .setClientIp(IpConvert.ipToLong(Consts.Code.CLIENT_IP));
            });
            s.correctSubjectiveResult(uid, userAnswerId,  questionSource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Test
    public void sty_findLastReadOveredSubjectivePaperAnswers(){
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(**********).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setMsg("{\"uid\":103075810,\"productId\":45869,\"goodsId\":13512,\"paperIds\":[65675,66253,66262,66245,66263,66265]}");
            GrpcResponse res = client.styFindLastReadOveredSubjectivePaperAnswers(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
