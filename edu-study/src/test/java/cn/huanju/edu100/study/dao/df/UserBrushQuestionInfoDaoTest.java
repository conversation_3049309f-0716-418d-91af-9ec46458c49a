package cn.huanju.edu100.study.dao.df;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserBrushQuestionInfoDao;
import cn.huanju.edu100.study.model.questionBox.UserBrushQuestionInfo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class UserBrushQuestionInfoDaoTest extends BaseTest {
    @Autowired
    private UserBrushQuestionInfoDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 143378408422768640L);
        param.put("uid", 151114283L);
        UserBrushQuestionInfo ua = dao.getShardingById(param);
        Assertions.assertEquals(54547, ua.getBestAnswerId());
    }
}
