package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100StudyServiceGrpc;
import cn.huanju.edu100.thrift.edu100_study;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.channel.ChannelOption;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TFramedTransport;
import org.apache.thrift.transport.TSocket;
import org.apache.thrift.transport.TTransport;
import org.apache.thrift.transport.TTransportException;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class UserQuestionBoxTestCase3 {

	private static Gson gson = GsonUtil.getGson();

	//	public static String URL = "***************";//***************，************** ************** ************** **************
	public static String URL = "127.0.0.1";
	//	 public static String URL = "**************";
//	public static String URL = "**************";
	public static int PORT = 8285;


    @Test
    public void sty_getWrongBoxQuestionUncategorized() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(6);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 151116810);
            param.put("categoryId", 1348);
            param.put("is_total", 1);//IsTotal 是否只获取总数（0：是，1：否）
            param.put("from", 0);
            param.put("rows", 10);
            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.styGetWrongBoxQuestionUncategorized(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
//
//    @Test
//    public void sty_getWipeOutWrongBoxQuestionUncategorized() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 151169135);
//            param.put("categoryId", 469);
//            param.put("is_total", 1);//IsTotal 是否只获取总数（0：是，1：否）
//            param.put("from", 0);
//            param.put("rows", 10);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getWipeOutWrongBoxQuestionUncategorized(req);
//            System.out.println("sty_getWipeOutWrongBoxQuestionUncategorized=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getWrongBoxQuestionAccordingToQType() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 151169135);
//            param.put("categoryId", 469);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getWrongBoxQuestionAccordingToQType(req);
//            System.out.println("sty_getWrongBoxQuestionAccordingToQType=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getWipeOutWrongBoxQuestionAccordingToQType() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 151169135);
//            param.put("categoryId", 469);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getWipeOutWrongBoxQuestionAccordingToQType(req);
//            System.out.println("sty_getWipeOutWrongBoxQuestionAccordingToQType=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void sty_resetChapterPractice() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 151169135);
//            param.put("categoryId", 469);
//            param.put("teach_book_id",97);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_resetChapterPractice(req);
//            System.out.println("sty_resetChapterPractice=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserDaysCalculate() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 151169136);
//            param.put("datetime", 20200512);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserDaysCalculate(req);
//            System.out.println("sty_getUserDaysCalculate=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getUserQuestionCalculate() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 151169136);
//            param.put("secondCategory", 775);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserQuestionCalculate(req);
//            System.out.println("sty_getUserDaysCalculate=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    //sty_getUserLastChapterExercise
//    @Test
//    public void sty_getUserLastChapterExercise() {
//        TTransport transport;
//        try {
//
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 151169136);
//            param.put("box_id", 775);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getUserLastChapterExercise(req);
//            System.out.println("sty_getUserLastChapterExercise=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//
//
//        }
//    }
//    //sty_saveUserLastChapterExercise
//    @Test
//    public void sty_saveUserLastChapterExercise() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 99L);
//            param.put("box_id", 775L);
//            param.put("book_id", 777L);
//            param.put("obj_type", 1);
//            param.put("obj_id", 999L);
//            param.put("chapter_id", 999L);
//            param.put("chapter_name", "第一章");
//            param.put("section_id", 444L);
//            param.put("section_name", "第2节");
//            param.put("knowledge_id", 444);
//            param.put("knowledge_name", "知识点");
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_saveUserLastChapterExercise(req);
//            System.out.println("sty_saveUserLastChapterExercise=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//
//
//        }
//    }
//
//    //
//    @Test
//    public void sty_saveTikuForecastScore() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 9999L);
//            param.put("box_id", 776L);
//            param.put("category_id", 9949L);
//            param.put("user_answer_id", 444L);
//            param.put("score", 70);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_saveTikuForecastScore(req);
//            System.out.println("sty_saveUserLastChapterExercise=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getTikuForecastScore() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 99L);
//            param.put("box_id", 775L);
//            param.put("category_id", 999L);
//
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getTikuForecastScore(req);
//            System.out.println("sty_getTikuForecastScore=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_saveTikuPracticeDuration() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 7777L);
//            param.put("box_id", 776L);
//            param.put("category_id", 9946L);
//            param.put("use_time", 10);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_saveTikuPracticeDuration(req);
//            System.out.println("sty_saveTikuPracticeDuration=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getTikuPracticeDuration() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 8888L);
//            param.put("box_id", 776L);
//            param.put("category_id", 9949L);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getTikuPracticeDuration(req);
//            System.out.println("sty_getTikuPracticeDuration=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void sty_userQuestionLogIsSync(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 8888L);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_userQuestionLogIsSync(req);
//            System.out.println("sty_userQuestionLogIsSync=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//    @Test
//    public void sty_syncUserQuestionLog(){
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("39.105.150.133", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11084928L);
//            req.setMsg(gson.toJson(param));
//            req.setTraceId("ghj-xxxxxx-999");
//            response res = client.sty_syncUserQuestionLog(req);
//            System.out.println("sty_syncUserQuestionLog=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getNewWrongBoxQuestionAccordingToQType() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("120.24.223.80", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(6);
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 225040675);
//            param.put("boxId", 94374);
//            param.put("bookId", 19722);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_getNewWrongBoxQuestionAccordingToQType(req);
//            System.out.println("sty_getNewWrongBoxQuestionAccordingToQType=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_getNewRandomBoxQuestionList() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("47.94.236.243", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            req.setAppid(7);
//            req.setMsg("{\"uid\":225040675,\"category_id\":23670,\"box_id\":94374,\"teach_book_id\":19722,\"num\":29,\"random_type\":1,\"obj_id\":0,\"obj_type\":0,\"question_types\":[0]}");
//            response res = client.sty_getNewRandomBoxQuestionList(req);
//            System.out.println("sty_getNewRandomBoxQuestionList=" + res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
}
