package cn.huanju.edu100.study.dao.df;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserVideoLogDao;
import cn.huanju.edu100.study.model.UserVideoLog;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class UserVideoLogDaoTest extends BaseTest {
    @Autowired
    private UserVideoLogDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 11493L);
        param.put("uid", 151114869L);
        UserVideoLog ua = dao.getShardingById(param);
        Assertions.assertEquals(56L, ua.getLength());
    }
}
