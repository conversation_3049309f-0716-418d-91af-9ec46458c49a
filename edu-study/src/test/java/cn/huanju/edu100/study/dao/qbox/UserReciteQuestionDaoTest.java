package cn.huanju.edu100.study.dao.qbox;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.questionBox.UserReciteQuestion;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserReciteQuestionDaoTest extends BaseTest {
    @Autowired
    private UserReciteQuestionDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 268680644186341376L);
        param.put("uid", 151114283L);
        UserReciteQuestion ua = dao.getShardingById(param);
        Assertions.assertEquals("[1375]", ua.getQuestionIdList());
    }

    @Test
    public void testGetKeysByUidAndQboxId() throws Exception{
        dao.getKeysByUidAndQboxId(123L, 456L);
    }

    @Test
    public void testGetByUidQboxIdAndKey() throws Exception{
        dao.getByUidQboxIdAndKey(123L, 234L, "abc");
    }

    @Test
    public void testGetByUidQboxIdAndKeyLike() throws Exception{
        dao.getByUidQboxIdAndKeyLike(123L, 456L, "abc");
    }

    @Test
    public void testGetFromMasterDbByUidQboxIdAndKey() throws Exception{
        dao.getFromMasterDbByUidQboxIdAndKey(123L, 234L, "abc");
    }
}
