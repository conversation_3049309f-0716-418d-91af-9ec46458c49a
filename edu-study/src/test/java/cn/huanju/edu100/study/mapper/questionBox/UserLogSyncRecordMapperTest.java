package cn.huanju.edu100.study.mapper.questionBox;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.questionBox.UserLogSyncRecord;
import cn.huanju.edu100.study.service.qbox.UserLogSyncRecordService;
import cn.huanju.edu100.util.JSONUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.checkerframework.checker.units.qual.A;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.UUID;

public class UserLogSyncRecordMapperTest extends BaseTest{

    @Autowired
    private UserLogSyncRecordMapper userLogSyncRecordMapper;

    @Autowired
    private UserLogSyncRecordService userLogSyncRecordService;

    @Test
    public void findListTest(){
        UserLogSyncRecord userLogSyncRecord = new UserLogSyncRecord();
        userLogSyncRecord.setUid(186876969L);
        List<UserLogSyncRecord> list = null;
        try {
            list = userLogSyncRecordService.findList(userLogSyncRecord);
        } catch (DataAccessException e) {
            e.printStackTrace();
        }
        System.out.println(JSONUtils.toJsonString(list));
    }

    @Test
    public void insertTest() {
        UserLogSyncRecord userLogSyncRecord = new UserLogSyncRecord();
        userLogSyncRecord.setUid(186876969L);
        userLogSyncRecord.setCategoryId(8407L);
        userLogSyncRecord.setQuestionBoxId(1354L);
        userLogSyncRecordService.insert(userLogSyncRecord);
    }

    @Test
    public void updateTest() {
        UserLogSyncRecord userLogSyncRecord = new UserLogSyncRecord();
        userLogSyncRecord.setId(4013L);
        userLogSyncRecord.setStatus(1);
        userLogSyncRecordService.update(userLogSyncRecord);
    }
}


