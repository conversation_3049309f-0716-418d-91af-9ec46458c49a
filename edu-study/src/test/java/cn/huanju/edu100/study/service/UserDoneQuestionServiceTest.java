package cn.huanju.edu100.study.service;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.questionBox.UserDoneQuestion;
import cn.huanju.edu100.study.service.qbox.UserDoneQuestionService;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019-03-26
 */
public class UserDoneQuestionServiceTest extends BaseTest {

    @Autowired
    UserDoneQuestionService userDoneQuestionService;

    @Test
    public void batchSave(){
        Long uid = 11335340L;
        Long questionBoxId = 1732L;
        List<UserDoneQuestion> list = Lists.newArrayList();
        UserDoneQuestion u1 = new UserDoneQuestion();
        u1.setUid(uid);
        u1.setQuestionBoxId(questionBoxId);
        u1.setKey("cp:done:14:125");
        u1.setQuestionIdList("[102,999]");
        list.add(u1);

        UserDoneQuestion u2 = new UserDoneQuestion();
        u2.setUid(uid);
        u2.setQuestionBoxId(questionBoxId);
        u2.setKey("cp:done:14:1251");
        u2.setQuestionIdList("[102,85,999]");
        list.add(u2);
        try {
            userDoneQuestionService.batchSave(uid, questionBoxId, list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
