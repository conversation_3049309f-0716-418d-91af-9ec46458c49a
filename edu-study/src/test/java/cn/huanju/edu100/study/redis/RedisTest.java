package cn.huanju.edu100.study.redis;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.repository.PaperSubmitCompareInfoRepository;
import cn.huanju.edu100.study.service.PaperService;
import com.hqwx.study.dto.PaperSubmitCompareInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/5 11:01
 * @description
 */
public class RedisTest extends BaseTest {

    @Autowired
    JedisPool alMasterPool;

    @Autowired
    PaperSubmitCompareInfoRepository paperSubmitCompareInfoRepository;

    @Autowired
    PaperService paperService;

    @Test
    public void testLock() {
        try(Jedis jedis = alMasterPool.getResource()) {
            SetParams setParams = new SetParams();
            setParams.nx().px(2_000);
            String lockKey = "lock_test_111";
            String result = jedis.set(lockKey, "", setParams);
            if(result == null) {
                System.out.println("locker is locked!");
            } else {
                System.out.println("lock success!");
            }
            System.out.println("lock1 result=" + result);

            result = jedis.set(lockKey, "", setParams);
            if(result == null) {
                System.out.println("locker is locked!");
            } else {
                System.out.println("lock success!");
            }
            System.out.println("lock2 result=" + result);

            jedis.del(lockKey);
        }
    }

    @Test
    public void testExpireSubscribe() throws InterruptedException, BusinessException {
        Long paperId = 1088L;
        Double score = 81.0;

        paperSubmitCompareInfoRepository.increaseSubmitCount(paperId, score, 100.0);
        PaperSubmitCompareInfo paperSubmitCompareInfo = paperService.getPaperSubmitCompareInfo(
                paperId, score, 100.0, 0.0);

        System.out.println(paperSubmitCompareInfo);
    }


    @Test
    public void testPaperSubmitStatistic() throws InterruptedException {
        paperSubmitCompareInfoRepository.increaseSubmitCount(2200L, 70.0, 100.0);
        var result = paperSubmitCompareInfoRepository.getPaperScoreIndex2StatisticInfo(2200L);
        System.out.println(result);
        Thread.sleep(5*1000*60);
    }
}
