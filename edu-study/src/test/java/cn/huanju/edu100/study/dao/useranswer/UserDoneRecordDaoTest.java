package cn.huanju.edu100.study.dao.useranswer;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserDoneRecordDao;
import cn.huanju.edu100.study.model.UserDoneRecord;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class UserDoneRecordDaoTest extends BaseTest {
    @Autowired
    private UserDoneRecordDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 897072386720006144L);
        param.put("uid", 151115432L);
        UserDoneRecord ua = dao.getShardingById(param);
        Assertions.assertEquals(897072385822425088L, ua.getRelAnswerId());
    }
}
