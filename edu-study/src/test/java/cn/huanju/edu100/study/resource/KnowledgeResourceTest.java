package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 18/11/8
 */
public class KnowledgeResourceTest extends BaseTest {
    @Autowired
    private KnowledgeResource knowledgeResource;

    @Test
    public void getQuestionByIds() {
        List<Long> ids= Lists.newArrayList();

        ids.add(2480438L);
        ids.add(2480496L);
        ids.add(2480495L);
        ids.add(2480494L);
        ids.add(2480493L);
        ids.add(2480470L);
        ids.add(2480490L);
        ids.add(2480491L);
        ids.add(1366L);
        ids.add(2480489L);
        ids.add(2480488L);
        ids.add(2480487L);
        ids.add(2480486L);
        ids.add(2480485L);
        ids.add(2480484L);
        ids.add(2480483L);
        ids.add(2480482L);

        List<Question> questions  = knowledgeResource.getQuestionByIds(ids);
        System.out.println(GsonUtil.toJson(questions));


    }
}
