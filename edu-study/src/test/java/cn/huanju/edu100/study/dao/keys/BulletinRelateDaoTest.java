package cn.huanju.edu100.study.dao.keys;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.BulletinRelateDao;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class BulletinRelateDaoTest extends BaseTest {
    @Autowired
    private BulletinRelateDao dao;

    @Test
    public void testSharding() throws Exception{
        Map map = new HashMap<>();
        map.put("id", 123);
        map.put("uid", 234L);
        dao.getShardingById(map);
    }
}
