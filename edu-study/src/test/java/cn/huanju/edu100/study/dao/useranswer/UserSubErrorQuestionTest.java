package cn.huanju.edu100.study.dao.useranswer;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserSubErrorQuestionDao;
import cn.huanju.edu100.study.model.UserSubErrorQuestion;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class UserSubErrorQuestionTest extends BaseTest {
    @Autowired
    private UserSubErrorQuestionDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 913435245792591872L);
        param.put("uid", 151237173L);
        UserSubErrorQuestion ua = dao.getShardingById(param);
        Assertions.assertEquals(1024224L, ua.getQuestionId());
    }
}
