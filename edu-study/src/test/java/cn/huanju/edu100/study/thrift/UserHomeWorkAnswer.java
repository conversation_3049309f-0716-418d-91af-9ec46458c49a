package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.persistence.model.DataEntity;
import com.hqwx.study.entity.UserAnswerDetail;

import java.util.Date;
import java.util.List;

/**
 * 用户作业Entity
 * <AUTHOR>
 * @version 2015-05-12
 */
public class UserHomeWorkAnswer extends DataEntity<UserHomeWorkAnswer> {

	public interface HomeWorkType {
		/**
		 * 讲作业(针对老的讲)
		 */
		int LESSON = 0;
		/**
		 * 段落作业
		 */
		int PARAGRAPH = 1;
		/**
		 * 微课作业
		 */
		int M_CLASS = 2;
		/**
		 * 个性化作业
		 */
		int PERSON_TASK = 3;
		
		/**
		 * 新录播课段落作业
		 * */
		int COURSE_PARAGRAPH = 4;
		
		/**
		 * 题库练习作业
		 * */
		int BOX_HOMEWORK = 5;
		
		/**
		 * 测评作业，即课件作业（最对新的讲）
		 * */
		int RESOURCE_VIDEO = 6;

		/**
		 * 微课班作业
		 */
		int WEIKE_CLASS = 101;

		/**
		 * 2XX都是智能学习的
		 * 每日一练
		 */
		int DAILY_WORK = 201;

		/**
		 * 学习作业
		 */
		int STUDY_WORK = 202;

		/**
		 * 复习作业
		 */
		int REVIEW_WORK = 203;
	}
	
	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long objId;		// obj_id
	private Integer objType;		// obj_type （0：讲作业，1：段落作业，2：微课作业，3：个性化作业，4：新录播课作业，5：题库作业）
	private Long taskId;		// task_id
	private Double score;		// score
	private Long usetime;		// usetime
	private Long answerNum;		// 当前答题数
	private Date startTime;		// 用户开始答卷时间
	private Date endTime;		// 用户提交试卷时间
	private String comment;		// 老师评语
	private Integer state;		// 状态，0未开始 1进行中 2已交卷 3已评卷

	private List<UserAnswerDetail> answerDetail;

	private Integer isSubmit;

	private Long productId;		// 产品id
	private Long goodsId;// 商品id
	private String appid;   //app的类型 所属终端，web、PC客户端、环球网校APP、快题库、建造师题库…、快题库小程序
	private String platForm;	// app平台ios android

	private List<Long> objTypes;
	private List<Integer> states;

	private Integer from;
	private Integer pageSize;

	public Integer getIsSubmit() {
		return isSubmit;
	}

	public void setIsSubmit(Integer isSubmit) {
		this.isSubmit = isSubmit;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public Long getObjId() {
		return objId;
	}

	public void setObjId(Long objId) {
		this.objId = objId;
	}

	public Integer getObjType() {
		return objType;
	}

	public void setObjType(Integer objType) {
		this.objType = objType;
	}

	public UserHomeWorkAnswer() {
		super();
	}

	public UserHomeWorkAnswer(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}
	
	public Long getUsetime() {
		return usetime;
	}

	public void setUsetime(Long usetime) {
		this.usetime = usetime;
	}
	
	public Long getAnswerNum() {
		return answerNum;
	}

	public void setAnswerNum(Long answerNum) {
		this.answerNum = answerNum;
	}
	
	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	
	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public List<UserAnswerDetail> getAnswerDetail() {
		return answerDetail;
	}

	public void setAnswerDetail(List<UserAnswerDetail> answerDetail) {
		this.answerDetail = answerDetail;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getPlatForm() {
		return platForm;
	}

	public void setPlatForm(String platForm) {
		this.platForm = platForm;
	}

	public List<Long> getObjTypes() {
		return objTypes;
	}

	public void setObjTypes(List<Long> objTypes) {
		this.objTypes = objTypes;
	}

	public List<Integer> getStates() {
		return states;
	}

	public void setStates(List<Integer> states) {
		this.states = states;
	}

	public Integer getFrom() {
		return from;
	}

	public void setFrom(Integer from) {
		this.from = from;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}