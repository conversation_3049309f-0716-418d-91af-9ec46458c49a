package cn.huanju.edu100.study.event;

import cn.huanju.edu100.study.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/2 16:54
 * @description
 */
public class ApplicationEventTest extends BaseTest {
    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void testPaperSubmittedEvent() {
        applicationContext.publishEvent(new PaperSubmittedEvent(this, null));
    }

    @Test
    public void testHomeworkSubmittedEvent() {
        applicationContext.publishEvent(new HomeworkSubmittedEvent(this, null));
    }
}
