package cn.huanju.edu100.study.task;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.BaseTest;
import org.junit.jupiter.api.Test;
import javax.annotation.Resource;



class DelSensitiveCommentJobHandlerTest extends BaseTest {

    @Resource
    private DelSensitiveCommentJobHandler delSensitiveCommentJobHandler;

    @Test
    void delSensitiveComment() throws DataAccessException {
        delSensitiveCommentJobHandler.delSensitiveComment(3, 4, "2020-01-01 00:00:00,2021-01-02 00:00:00");
    }
}