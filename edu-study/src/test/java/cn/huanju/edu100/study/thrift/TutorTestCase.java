package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100StudyServiceGrpc;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.channel.ChannelOption;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class TutorTestCase {
    private static Gson gson = GsonUtil.getGson();
    // public static String URL = "**************";// 221.228.86.187, 127.0.0.1
    //public static String URL = "58.215.169.94";// 58.215.169.173 ************** **************

    public static String URL = "127.0.0.1";// 58.215.169.173 ************** **************
                                           // **************
//    public static String URL = "**************";
//    public static String URL = "**************";
    public static int PORT = 58285;

    // public static String URL = "**************";
    // public static int PORT = 12300;

    @Test
    public void sty_tutor_listSecondCategoryByUid() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 11335340);
            req.setMsg(gson.toJson(param));
            System.out.println(req.getMsg());
            GrpcResponse res = client.styTutorListSecondCategoryByUid(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
//
//    @Test
//    public void sty_tutor_getStudyOverviewByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorStudentOverview tutorStudentOverview = new TutorStudentOverview();
//            tutorStudentOverview.setUid(9744268l);
//            tutorStudentOverview.setClasses("2017一建云私塾");
//            tutorStudentOverview.setSecondCategory(775l);
//            req.setMsg(gson.toJson(tutorStudentOverview));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getStudyOverviewByUid(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getTeacherByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11498710);
//            param.put("classes", "2016");
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_tutor_getTeacherByUid(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_listTaskByUidStartEndTime() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11498699);
//            param.put("classes", "2016");
//            param.put("second_category", 81);
//            param.put("need_detail", 1);
//
///*            Calendar calendar = Calendar.getInstance();
//            calendar.set(Calendar.DAY_OF_MONTH, 1);
//            calendar.set(Calendar.HOUR_OF_DAY, 0);
//            calendar.set(Calendar.SECOND, 0);
//            calendar.set(Calendar.MINUTE, 0);
//            calendar.set(Calendar.MILLISECOND, 0);
//            param.put("start_time", calendar.getTime());
//            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
//            calendar.set(Calendar.DAY_OF_MONTH, 8);
//            param.put("end_time", calendar.getTime());*/
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_listTaskByUidStartEndTime(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getCategoryListByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11498710);
//            param.put("classes", "2016");
//            param.put("second_category", 80);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getCategoryListByUid(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_listPhaseByUidAndCategoryList() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11498699);
//            param.put("classes", "2016");
//            param.put("categoryIds", "93");
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_listPhaseByUidAndCategoryList(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getLastTaskByUidAndCategoryIds() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11498699);
//            param.put("classes", "2016");
//            param.put("categoryIds", "93");
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getLastTaskByUidAndCategoryIds(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_listFeedBackByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11319186);
//            param.put("classes", "2016");
//            param.put("from", 0);
//            param.put("pageSize", 10);
//            param.put("isShow", 1);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_listFeedBackByUid(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_updateTaskStatusByUidTaskId() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorStudentTask tutorStudentTask = new TutorStudentTask();
//            tutorStudentTask.setUid(10902279l);
//            tutorStudentTask.setTaskId(19l);
//            tutorStudentTask.setStatus(2);
//            req.setMsg(gson.toJson(tutorStudentTask));
//            System.out.println(req.getMsg());
//            for (int i=0; i<5; i++) {
//                response res = client.sty_tutor_updateTaskStatusByUidTaskId(req);
//                System.out.println(res.getMsg());
//            }
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_updateTaskStatusByUidLessonId() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("127.0.0.1", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid",11335119);
//            param.put("classes","Reise中级经济师");
//            param.put("lessonId",4418);
//            param.put("status",2);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_updateTaskStatusByUidLessonId(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//
//    @Test
//    public void sty_tutor_getStudyProgressByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorStudentTaskCount tutorStudentTaskCount = new TutorStudentTaskCount();
//            tutorStudentTaskCount.setUid(10902273l);
//            tutorStudentTaskCount.setPhaseId(3l);
//            tutorStudentTaskCount.setClasses("2016");
//            req.setMsg(gson.toJson(tutorStudentTaskCount));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getStudyProgressByUid(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getTaskDetailByTaskId() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorTask tutorTask = new TutorTask();
//            tutorTask.setId(89l);
//            req.setMsg(gson.toJson(tutorTask));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getTaskDetailByTaskId(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_saveStudyVideoResult() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorUserVideoLog tutorUserVideoLog = new TutorUserVideoLog();
//            tutorUserVideoLog.setLessonId(2933l);
//            tutorUserVideoLog.setClsId(33l);
//            tutorUserVideoLog.setCourseId(61l);
//            tutorUserVideoLog.setUid(11498699l);
//            tutorUserVideoLog.setLastTime(new Date());
//            tutorUserVideoLog.setPosition(0);
//            tutorUserVideoLog.setResult(1);
//            tutorUserVideoLog.setClasses("2016");
//            req.setMsg(gson.toJson(tutorUserVideoLog));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_saveStudyVideoResult(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    public static void main(String args[]){
//        String field = "dfd_111_ss";
//        System.out.println(field.substring(field.indexOf("_") + 1));
//
//        long lt = 1462328880444l;
//        Date dt = new Date(lt);
//        System.out.println(dt);
//    }
//    @Test
//    public void sty_tutor_listPushResByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorStudentPushRes tutorStudentPushRes = new TutorStudentPushRes();
//            tutorStudentPushRes.setClasses("33");
//            tutorStudentPushRes.setUid(11498699l);
//            //tutorStudentPushRes.setCategoryId(82l);
//            //tutorStudentPushRes.setTaskIdList(Arrays.asList(new Long[] { 14l,15l,16l,17l,18l,19l,20l,23l,24l,27l,28l,29l,30l }));
//            req.setMsg(gson.toJson(tutorStudentPushRes));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_listPushResByUid(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getStudentTaskByTidAndUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorStudentTask task = new TutorStudentTask();
//            task.setUid(10902272L);
//            task.setTaskId(18L);
//            req.setMsg(gson.toJson(task));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getStudentTaskByTidAndUid(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_listKeyEventLogByUidType() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 10902273);
//            param.put("classes", "2016");
//            param.put("categoryId", 82);
//            param.put("from", 0);
//            param.put("row", 10);
//            param.put("type", 0);
//            // param.put("total", 1);
//
//            req.setMsg("{\"uid\":11498682,\"secondCategory\":775,\"type\":0,\"from\":0,\"row\":10}");
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_listKeyEventLogByUidType(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_listQuestionBylogIdAndUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("58.215.170.244", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 102666310);
//            param.put("logId", 5629642);
//            param.put("result", 0);
//            param.put("pageNo", 1);
//            param.put("pageSize", 10);
//            // param.put("total", 1);
//
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_listQuestionBylogIdAndUid(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_submitPaperAnswer() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 10902273);
//            param.put("logId", 1);
//            param.put("result", 0);
//            param.put("pageNo", 1);
//            param.put("pageSize", 10);
//            // param.put("total", 1);
//
//            // req.setMsg(gson.toJson(param));
//            // req.setMsg("{\"uid\":11065863,\"start_time\":1453889468089,\"end_time\":1453889480907,\"is_submit\":1,\"group_id\":19,\"task_id\":166,\"type\":1,\"answer_detail\":[{\"uid\":11065863,\"question_id\":324,\"topic_id\":307069,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11065863,\"question_id\":324,\"topic_id\":307070,\"answer\":[\"A\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11065863,\"question_id\":25,\"topic_id\":21,\"answer\":[\"B\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"}],\"obj_id\":206,\"obj_type\":1}");
//            req.setMsg("{\"uid\":11587537,\"start_time\":1519899212000,\"end_time\":1519899226221,\"is_submit\":1,\"group_id\":59,\"task_id\":372,\"type\":6,\"paper_id\":290,\"paper_type\":3,\"answer_detail\":[{\"uid\":11587537,\"question_id\":2480052,\"topic_id\":1535452,\"answer\":[\"D\"]},{\"uid\":11587537,\"question_id\":2480053,\"topic_id\":1535453,\"answer\":[\"B\"]},{\"uid\":11587537,\"question_id\":2480051,\"topic_id\":1535446,\"answer\":[]},{\"uid\":11587537,\"question_id\":2480051,\"topic_id\":1535447,\"answer\":[]},{\"uid\":11587537,\"question_id\":2480051,\"topic_id\":1535448,\"answer\":[]},{\"uid\":11587537,\"question_id\":2480051,\"topic_id\":1535449,\"answer\":[]},{\"uid\":11587537,\"question_id\":2480051,\"topic_id\":1535450,\"answer\":[]},{\"uid\":11587537,\"question_id\":2480051,\"topic_id\":1535451,\"answer\":[]}]}");
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_submitPaperAnswer(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_submitHomeworkAnswer() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 10902273);
//            param.put("logId", 1);
//            param.put("result", 0);
//            param.put("pageNo", 1);
//            param.put("pageSize", 10);
//            // param.put("total", 1);
//
//            // req.setMsg(gson.toJson(param));
//            req.setMsg("{\"uid\":11335330,\"start_time\":1,\"end_time\":1,\"is_submit\":1,\"group_id\":\"34\",\"task_id\":\"225\",\"type\":\"1\",\"answer_detail\":[{\"uid\":11335330,\"question_id\":1129,\"topic_id\":307927,\"answer\":[\"B\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1130,\"topic_id\":307928,\"answer\":[\"A\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1126,\"topic_id\":307924,\"answer\":[\"B\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1131,\"topic_id\":307929,\"answer\":[\"Sdf\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1127,\"topic_id\":307925,\"answer\":[\"B\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1132,\"topic_id\":307930,\"answer\":[\"Sdfsdf\",\"Sdfsdf\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1128,\"topic_id\":307926,\"answer\":[\"B\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1124,\"topic_id\":307922,\"answer\":[\"A\"],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1125,\"topic_id\":307923,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1123,\"topic_id\":307921,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1120,\"topic_id\":307918,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1122,\"topic_id\":307920,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1121,\"topic_id\":307919,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1119,\"topic_id\":307917,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1118,\"topic_id\":307916,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1117,\"topic_id\":307915,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1116,\"topic_id\":307914,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1112,\"topic_id\":307910,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1109,\"topic_id\":307907,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1111,\"topic_id\":307909,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1110,\"topic_id\":307908,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1108,\"topic_id\":307906,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1107,\"topic_id\":307905,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1102,\"topic_id\":307903,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1102,\"topic_id\":307904,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1102,\"topic_id\":307903,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1102,\"topic_id\":307904,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1101,\"topic_id\":307898,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1100,\"topic_id\":307897,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1099,\"topic_id\":307896,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1098,\"topic_id\":307895,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1094,\"topic_id\":307891,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"},{\"uid\":11335330,\"question_id\":1097,\"topic_id\":307894,\"answer\":[],\"pic\":\"\",\"mp3\":\"\",\"file\":\"\"}],\"obj_id\":1,\"obj_type\":1}");
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_submitHomeworkAnswer(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_listPushResByChapterIdList() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11065863l);
//            param.put("firstCategory", 79);
//            param.put("secondCategory", 80);
//            param.put("categoryId", 82);
//            param.put("classes", "2016");
//            param.put("chapterIds", Arrays.asList(new Long[]{59l}));
//            // param.put("total", 1);
//
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_listPushResByChapterIdList(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_isAuthTaskByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("103.227.123.24", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//
//            param.put("uid", 11335330L);
//            param.put("weikeId", 1l);
//            // param.put("total", 1);
//            // param.put("total", 1);
//
//            // req.setMsg(gson.toJson(param));
//            req.setMsg("{\"uid\":174041187,\"taskId\":13223}");
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_isAuthTaskByUid(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getUnitsList() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorUnit tutorUnit = new TutorUnit();
//            //tutorUnit.setPlanId(1l);
//            tutorUnit.setPhaseId(119l);
//            // param.put("total", 1);
//
//            req.setMsg(gson.toJson(tutorUnit));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getUnitsList(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getStudentTaskList() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("103.227.123.24", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            TutorStudentTask tutorStudentTask = new TutorStudentTask();
///*            tutorStudentTask.setPlanId(6l);
//            tutorStudentTask.setPhaseId(11l);*/
//            tutorStudentTask.setUid(11819131L);
//            tutorStudentTask.setUnitId(825L);
////            List<Long> taskIdList = Lists.newArrayList();
////            taskIdList.add(262l);
////            tutorStudentTask.setTaskIdList(taskIdList);
//            // param.put("total", 1);
//
//            req.setMsg(gson.toJson(tutorStudentTask));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getStudentTaskList(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_submitComment() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("*************", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            CommentElement pa = new CommentElement();
//            pa.setUid(11335119l);
////            pa.setGoodsGroupId(127L);
//            pa.setGoodsId(2070L);
//            pa.setProductId(1049L);
//            pa.setObjId(15663L);
//            pa.setObjType(1);
//            pa.setObjName("测试课节9");
//            pa.setContent("this is ocntent 福尔马林");//福尔马林为敏感词
//            pa.setStar(5f);
//            // req.setMsg(gson.toJson(pa));
//            req.setMsg(" {\"uid\":11498706,\"obj_id\":680144,\"obj_name\":\"\\u5f55\\u64ad\\u8bfe\\u8bfe\\u8282\\u53ef\\u4ee5\\u8bd5\\u542c\",\"obj_type\":0,\"teacher_star\":5,\"goods_id\":101865,\"product_id\":82335,\"content\":\"\\u8001\\u5e08\\u8bb2\\u8bfe\\u662f\\u5728\\u592a\\u68d2\\u4e86\\uff01\\u597d\\u559c\\u6b22\\u8001\\u5e08\\u7684\\u8bb2\\u8bfe\\u98ce\\u683c\\uff01\",\"star\":5} ");
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_submitComment(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
    @Test
    public void sty_tutor_queryComment() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setMsg("{\"uid\":100001320,\"obj_id\":824977,\"from\":0,\"obj_type\":3,\"query_type\":1,\"rows\":12}");
            System.out.println(req.getMsg());
            GrpcResponse res = client.styTutorQueryComment(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void styTutorQueryCommentCount() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setMsg("{\"second_category\":5583,\"from\":0,\"obj_type\":5,\"query_type\":2,\"rows\":20,\"status\":2}");
            System.out.println(req.getMsg());
            GrpcResponse res = client.styTutorQueryCommentCount(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void styTutorQueryCommentBySecondCategory() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setMsg("{\"secondCategory\":775,\"from\":0,\"rows\":20}");
            System.out.println(req.getMsg());
            GrpcResponse res = client.styTutorQueryCommentBySecondCategory (req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
//
//    @Test
//    public void sty_tutor_queryCommentByGoodsGroupId() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            CommentElement pa = new CommentElement();
//
//            pa.setGoodsGroupId(201L);
//          //  pa.setObjId(3819L);
//            pa.setObjType(0);
//               pa.setFrom(0);
//               pa.setRows(12);
//               pa.setQueryType(2);
//            req.setMsg(gson.toJson(pa));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_queryCommentByGoodsGroupId(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_queryCommentCount() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            CommentElement pa = new CommentElement();
//
//            pa.setUid(11587537l);
//            pa.setObjId(15618l);
//            pa.setObjType(1);
//            pa.setQueryType(2);
//            req.setMsg(gson.toJson(pa));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_queryCommentCount(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_thumbUpComment() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            CommentElement pa = new CommentElement();
//          pa.setId(70L);
//          pa.setUid(11065863l);
//            req.setMsg(gson.toJson(pa));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_thumbUpComment(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_queryCommentByUid() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 102569453L);
//            param.put("objType", 0);
//
//            List<Long> idList = new ArrayList<Long>();
//            idList.add(150161L);
//            idList.add(3834L);
//            idList.add(2460L);
//            idList.add(2896L);
//            param.put("idList", idList);
//            req.setMsg(gson.toJson(param));
//            response res = client.sty_tutor_queryCommentByUid(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_updateComment() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            List<Long> ids = new ArrayList<Long>();
//            ids.add(1L);
//            ids.add(80L);
//            ids.add(81L);
//            param.put("ids", ids);
//            param.put("isRead", 1);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_updateComment(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getCommentNum() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11335123L);
////            param.put("isRead", 1);
////            param.put("objType", 3);
//
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getCommentNum(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getStudyReportByUidAndCategoryId() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket("103.227.123.17", PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11335340L);
//            param.put("classes", "Reise中级经济师");
//            param.put("secondCategory", 5607L);
//            param.put("categoryId", 5608L);
//
//            // req.setMsg(gson.toJson(param));
//            req.setMsg(" {\"uid\":175255399,\"classes\":\"2020\\u4e0a\\u6559\\u5e08\\u8d44\\u683c\",\"secondCategory\":7211,\"categoryId\":7217}");
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getStudyReportByUidAndCategoryId(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_mySecondCategoryComment() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            CommentElement pa = new CommentElement();
////            pa.setUid(11587537l);
////            pa.setObjId(3819L);
////            pa.setObjType(0);
////            pa.setFrom(0);
////            pa.setRows(12);
////            pa.setQueryType(2);
//            //查询题目评价
////            pa.setUid(11335123L);
////            pa.setObjType(2);
////            pa.setQueryType(1);
////            pa.setFrom(0);
////            pa.setRows(3);
//
//            pa.setUid(151115554l);
////            pa.setObjId(15618l);
//            pa.setObjType(2);
//            pa.setQueryType(1);
//            pa.setFrom(0);
//            pa.setRows(10);
////            pa.setSecondCategory(775l);
//            req.setMsg(gson.toJson(pa));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_mySecondCategoryComment(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
}
