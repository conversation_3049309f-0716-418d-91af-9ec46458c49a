package cn.huanju.edu100.study.mapper.solution;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.solution.SolutionQuestionDao;
import cn.huanju.edu100.study.model.mock.MockExamMybatisPlusDemo;
import cn.huanju.edu100.study.model.solution.SolutionQuestion;
import cn.huanju.edu100.study.model.solution.SolutionQuestionAiAnswerLog;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @program: edu-study
 * @ClassName SolutionQuestionAiAnswerLogMapperTest
 * @description:
 * @author: gaohaijing
 * @create: 2023-03-28 18:24
 * @Version 1.0
 **/
public class SolutionQuestionAiAnswerLogMapperTest extends BaseTest {

    @Autowired
    private SolutionQuestionAiAnswerLogMapper solutionQuestionAiAnswerLogMapper;

    @Autowired
    private SolutionQuestionDao ssolutionQuestionDao;

    @Test
    public void testAdd(){
        SolutionQuestionAiAnswerLog entity = new SolutionQuestionAiAnswerLog();
        entity.setQuestionId(1L);
        entity.setIsAiAnswer(-1);
        entity.setCreatedTime(System.currentTimeMillis()/1000);
        entity.setUpdatedTime(System.currentTimeMillis()/1000);
        int result = solutionQuestionAiAnswerLogMapper.insert(entity);
        Assertions.assertEquals(result, 1);
    }
    @Test
    public void findListCount() throws DataAccessException {
        SolutionQuestion solutionQuestion = new SolutionQuestion();
        solutionQuestion.setTag(2);
        solutionQuestion.setSource("record");
        ssolutionQuestionDao.findListCount(new Page<>(), solutionQuestion);
        System.out.println();
    }

    @Test
    public void findList() throws DataAccessException {
        SolutionQuestion solutionQuestion = new SolutionQuestion();
        solutionQuestion.setTagList(Lists.newArrayList(2,3));
        solutionQuestion.setSource("live");
        solutionQuestion.setSecondCategory(5632l);
//        solutionQuestion.setCategoryId(5584l);
        solutionQuestion.setIsFrozen(0);
        solutionQuestion.setQuestionType(1);
        Page page = new Page<>(1,12);
        page.setOrderBy("updatedTime desc");
        ssolutionQuestionDao.findList(page, solutionQuestion);
        System.out.println();
    }
}
