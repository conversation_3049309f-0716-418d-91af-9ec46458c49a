package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100StudyServiceGrpc;
import cn.huanju.edu100.study.model.UserAgreement;
import cn.huanju.edu100.thrift.edu100_study;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.channel.ChannelOption;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TFramedTransport;
import org.apache.thrift.transport.TSocket;
import org.apache.thrift.transport.TTransport;
import org.apache.thrift.transport.TTransportException;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class UserAgreementTestCase {
    private static Gson gson = GsonUtil.getGson();
    // public static String URL = "221.228.86.187";// 221.228.86.187, 127.0.0.1
//     public static String URL = "39.105.11.127";
    public static String URL = "127.0.0.1";
    public static int PORT = 58285;

    // public static String URL = "106.38.200.203";
    // public static int PORT = 12300;

    // @Test
    // public void sty_getUserAgreementsByUidOrderId() {
    //
    // TTransport transport;
    // try {
    // transport = new TFramedTransport(new TSocket(URL, PORT));
    // TProtocol protocol = new TBinaryProtocol(transport);
    // edu100_study.Client client = new edu100_study.Client(protocol);
    // transport.open();
    // request req = new request();
    // Map<String, Object> param = new HashMap<String, Object>();
    // param.put("uid", 10902273l);
    // param.put("order_id", 45445l);
    //
    // req.setMsg(gson.toJson(param));
    // response res = client.sty_getUserAgreementsByUidOrderId(req);
    // System.out.println("sty_getUserAgreementsByUidOrderId.response="
    // + res);
    // transport.close();
    // } catch (TTransportException e) {
    // e.printStackTrace();
    // } catch (TException e) {
    // e.printStackTrace();
    // }
    // }

    @Test
    public void sty_saveUserAgreementList() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress("*************",PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            req.setAppid(7);

            req.setMsg("[{\"order_id\":50740353,\"uid\":228281085,\"title\":\"2023中级注安vip专享班协议\",\"status\":0,\"content\":\"https://oss-hqwx-edu24ol.hqwx.com/a100eb484865fb5c92acce4f192efd861caa14cd.js\",\"goods_id\":124025,\"name\":\"2023中级注册安全工程师vip专享班-建筑（全科）\",\"agreement_id\":18457,\"create_date\":1748999414084,\"update_date\":1748999414084,\"sch_id\":2}]");
            GrpcResponse res = client.stySaveUserAgreementList(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sty_getUserAgreementListByUidOrderId() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 11406518);
            param.put("order_id", 1001836);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.styGetUserAgreementListByUidOrderId(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    // 2.1.13 根据传入的user_agreement对象更新user_agreement记录
    @Test
    public void sty_updateUserAgreementByUAObject() {
        System.out.println(">>>2.1.13	根据传入的user_agreement对象更新user_agreement记录");
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            // UserAgreement userAgreement=new UserAgreement();
            // userAgreement.setAgreeDate(new Date());
            // userAgreement.setBuyDate(new Date());
            // userAgreement.setContent("123content");
            // userAgreement.setCreateBy(123l);
            // userAgreement.setCreateDate(new Date());
            // userAgreement.setDelFlag("delFlag");
            // userAgreement.setId(123456l);
            // userAgreement.setIp("127.0.0.1");
            // userAgreement.setIsNewRecord(true);
            // userAgreement.setOrderId(5678l);
            // userAgreement.setPhone("18911112222");
            // // userAgreement.setQq("54545454");
            // userAgreement.setRemarks("remarks");
            // userAgreement.setStatus(44);
            // userAgreement.setUid(123l);
            // userAgreement.setUpdateBy(123l);
            // userAgreement.setUpdateDate(new Date());
            // userAgreement.setUsername("sy12345687");
            // System.out.println("userAgreement="+ gson.toJson(userAgreement));

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 151236095);
            param.put("id", 13992);
            param.put("name", "王丽美");
            param.put("idcard", "44010619960728780X");
            param.put("phone", "13221000666");
            param.put("ip", "128.3.3.333");
            param.put("address", "广州天河岑村");
            param.put("email", "<EMAIL>");

            req.setMsg(gson.toJson(param));
            // req.setMsg(gson.toJson(userAgreement));
            GrpcResponse res = client.styUpdateUserAgreementByUAObject(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
//
    @Test
    public void sty_agreementSignFinishedByUid() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 11406518);

            req.setMsg(gson.toJson(param));
            GrpcResponse res = client.styAgreementSignFinishedByUid(req.build());
            System.out.println(res);
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }

        // TTransport transport;
        // try {
        // transport = new TFramedTransport(new TSocket(URL, PORT));
        // TProtocol protocol = new TBinaryProtocol(transport);
        // edu100_study.Client client = new edu100_study.Client(protocol);
        // transport.open();
        // request req = new request();
        // Map<String, Object> param = new HashMap<String, Object>();
        // param.put("uid", 10978381l);
        //
        // req.setMsg(gson.toJson(param));
        // response res = client.sty_agreementSignFinishedByUid(req);
        // System.out.println("sty_agreementSignFinishedByUid.response="
        // + res);
        // transport.close();
        // } catch (TTransportException e) {
        // e.printStackTrace();
        // } catch (TException e) {
        // e.printStackTrace();
        // }
    }

    @Test
    public void sty_getAgreementRelateGoodsIdListByUid() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
                UserAgreement userAgreement = new UserAgreement();
                userAgreement.setUid(11067955L);
                req.setMsg(GsonUtil.getGson(req.getAppid()).toJson(userAgreement));
                GrpcResponse res = client.styGetUnsignAgreementRelateGoodsIdListByUid(req.build());
                System.err.println(res.getMsg());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test_sty_agreementSignFinishedByUid() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
                UserAgreement userAgreement = new UserAgreement();
                userAgreement.setUid(11084916L);
                req.setMsg(GsonUtil.getGson(req.getAppid()).toJson(userAgreement));
                GrpcResponse res = client.styAgreementSignFinishedByUid(req.build());
                System.err.println(res.getMsg());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

//    @Test
//    public void sty_delAgreement() {
//
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 10902273l);
//            List<Long> goodsIds = new ArrayList<Long>();
//            goodsIds.add(45778l);
//            goodsIds.add(4567l);
//            param.put("goodsIds", goodsIds.toArray());
//
//            req.setMsg(GsonUtil.toJson(param));
//            response res = client.sty_delUserAgreement(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//

    @Test
    public void sty_getUserAgreementInfoByIdUid() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", 10902273l);
            param.put("order_id", 45445l);

            // req.setMsg(gson.toJson(param));
            req.setMsg("{\"id\":\"927795\",\"uid\":100001320}");
            GrpcResponse res = client.styGetUserAgreementInfoByIdUid(req.build());
            System.out.println("sty_getUserAgreementInfoByIdUid.response=" + res);
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
}
