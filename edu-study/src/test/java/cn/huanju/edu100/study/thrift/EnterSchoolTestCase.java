package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100StudyServiceGrpc;
import cn.huanju.edu100.thrift.edu100_study;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.hqwx.study.dto.EnterSchoolTestREQ;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.channel.ChannelOption;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TFramedTransport;
import org.apache.thrift.transport.TSocket;
import org.apache.thrift.transport.TTransport;
import org.apache.thrift.transport.TTransportException;
import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

public class EnterSchoolTestCase {
    private static Gson gson = GsonUtil.getGson();
    // public static String URL = "**************";// **************, 127.0.0.1
//   public static String URL = "**************";
   public static String URL = "127.0.0.1";
//    public static String URL = "**************";
    public static int PORT = 8285;

    /**
     * 插入ok
     */
    @Test
    public void sty_checkEnterSchoolTestFlag() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();

            EnterSchoolTestREQ testREQ = new EnterSchoolTestREQ();
            testREQ.setUid(111111111L);
            testREQ.setCategoryIds(Lists.newArrayList(7235L));
            req.setMsg(GsonUtil.getGenericGson().toJson(testREQ));
            GrpcResponse res = client.styCheckEnterSchoolTestFlag(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }

    }
//
//    @Test
//    public void sty_assembleEnterSchoolTestPaper() {
//
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//
//            EnterSchoolTestREQ testREQ = new EnterSchoolTestREQ();
//            testREQ.setUid(151235421L);
//            testREQ.setCategoryId(7235L);
//            req.setMsg(GsonUtil.getGenericGson().toJson(testREQ));
//            response res = client.sty_assembleEnterSchoolTestPaper(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    @Test
//    public void sty_enterSchoolTestReport() {
//
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//
//            EnterSchoolTestREQ param = new EnterSchoolTestREQ();
//            param.setUid(100001320L);
//            param.setPaperId(274L);
//            param.setUserAnswerId(870601435795030016L);
//            req.setMsg(GsonUtil.getGenericGson().toJson(param));
//            response res = client.sty_enterSchoolTestReport(req);
//            System.out.println(GsonUtil.getGenericGson().toJson(res));
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//
//    }
}
