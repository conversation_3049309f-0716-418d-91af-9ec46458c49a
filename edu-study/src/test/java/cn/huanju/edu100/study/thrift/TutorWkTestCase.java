package cn.huanju.edu100.study.thrift;

import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100StudyServiceGrpc;
import cn.huanju.edu100.thrift.edu100_study;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.channel.ChannelOption;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TFramedTransport;
import org.apache.thrift.transport.TSocket;
import org.apache.thrift.transport.TTransport;
import org.apache.thrift.transport.TTransportException;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class TutorWkTestCase {
    private static Gson gson = GsonUtil.getGson();
    // public static String URL = "**************";// 221.228.86.187, 127.0.0.1
    //public static String URL = "58.215.169.94";// ************** ************** **************
    public static String URL = "127.0.0.1";// ************** ************** **************
                                           // **************
    public static int PORT = 8285;

    // public static String URL = "**************";
    // public static int PORT = 12300;

    @Test
    public void sty_tutor_queryWkChapter() {
        NettyChannelBuilder builder = null;
        try {
            builder = (NettyChannelBuilder)NettyChannelBuilder.forAddress(URL,PORT);
            ((NettyChannelBuilder)builder.usePlaintext().keepAliveWithoutCalls(true).maxInboundMessageSize(2147483647).idleTimeout(5000, TimeUnit.MILLISECONDS)).keepAliveTimeout(5000, TimeUnit.MILLISECONDS).withOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000);
            ManagedChannel channel = builder.build();
            Edu100StudyServiceGrpc.Edu100StudyServiceBlockingStub client
                    = Edu100StudyServiceGrpc.newBlockingStub(channel);
            GrpcRequest.Builder req = GrpcRequest.newBuilder();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("wk_class_id", 4L);
            List<Long> chapterIdList = Lists.newArrayList();
            chapterIdList.add(1L);
//            param.put("section_id_list", chapterIdList);
            req.setMsg(gson.toJson(param));
            System.out.println(req.getMsg());
            GrpcResponse res = client.styTutorQueryWkChapter(req.build());
            System.out.println(JsonFormat.printer().print(res));
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
//
//    @Test
//    public void sty_tutor_queryWkChapterStudy() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//          //  param.put("wk_class_id", 1L);
//            List<Long> chapterIdList = Lists.newArrayList();
//            chapterIdList.add(1L);
//            param.put("section_id_list", chapterIdList);
//            param.put("uid", 1234L);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_queryWkChapterStudy(req);
//            System.out.println(res.getMsg());
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_getLastTask() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//          //  param.put("wk_class_id", 1L);
//            List<Long> chapterIdList = Lists.newArrayList();
//            chapterIdList.add(1L);
////            param.put("section_id_list", chapterIdList);
//            param.put("uid", 11587537L);
////            param.put("unit_id", 41L);
////            param.put("phase_id", 59L);
////            param.put("task_id", 1L);
////            param.put("category_id", 7488L);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getLastTask(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//     @Test
//    public void sty_tutor_getWkLastTask() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//          //  param.put("wk_class_id", 1L);
//            List<Long> chapterIdList = Lists.newArrayList();
//            chapterIdList.add(1L);
////            param.put("section_id_list", chapterIdList);
//            param.put("uid", 1234L);
////            param.put("unit_id", 12L);
////            param.put("weike_id", 1L);
////            param.put("section_id", 1L);
//            param.put("category_id", 234L);
////            param.put("task_id", 1L);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_getWkLastTask(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_userCollectWkKnow() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 1234L);
//            param.put("weike_id", 4L);
//            List<Long> knowIdList = Lists.newArrayList();
//            knowIdList.add(538L);
//            knowIdList.add(551L);
//            knowIdList.add(552L);
//            knowIdList.add(553L);
//            knowIdList.add(554L);
//            param.put("know_id_list", knowIdList);
//            param.put("status", 1);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_userCollectWkKnow(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_queryCollectWkTask() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11335330L);
//            param.put("weike_id", 4L);
//            param.put("type", 1);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_queryCollectWkTask(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_queryWkTask() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("section_id", 23L);
//            //param.put("weike_id", 4L);
//            //param.put("type", 1);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_queryWkTask(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void sty_tutor_queryUserCollectWkKnow() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 1234L);
//            param.put("weike_id", 4L);
//            List<Long> kIdList = Lists.newArrayList();
//            kIdList.add(551L);
//            kIdList.add(552L);
//            kIdList.add(553L);
//            param.put("knowledge_id_list", kIdList);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_queryUserCollectWkKnow(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_queryUnitStatus() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("uid", 11335330L);
//
//            List<Long> idList = Lists.newArrayList();
//            idList.add(60L);
//            idList.add(2L);
//            idList.add(16L);
//            idList.add(21L);
//            idList.add(22L);
//            idList.add(23L);
//            param.put("unit_id_list", idList);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_queryUnitStatus(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void sty_tutor_queryWkByIds() {
//        TTransport transport;
//        try {
//            transport = new TFramedTransport(new TSocket(URL, PORT));
//            TProtocol protocol = new TBinaryProtocol(transport);
//            edu100_study.Client client = new edu100_study.Client(protocol);
//            transport.open();
//            request req = new request();
//            Map<String, Object> param = new HashMap<String, Object>();
//
//            List<Long> idList = Lists.newArrayList();
//            idList.add(1L);
//            idList.add(2L);
//            idList.add(3L);
//            idList.add(4L);
//            idList.add(5L);
//            idList.add(6L);
//            idList.add(7L);
//            idList.add(8L);
//            idList.add(9L);
//            param.put("id_list", idList);
//            req.setMsg(gson.toJson(param));
//            System.out.println(req.getMsg());
//            response res = client.sty_tutor_queryWkByIds(req);
//            System.out.println(res);
//            transport.close();
//        } catch (TTransportException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//

}
