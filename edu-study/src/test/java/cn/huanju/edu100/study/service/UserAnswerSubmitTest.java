package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.entry.ServerBootstrap;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.JSONUtils;
import com.google.gson.Gson;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/9 15:25
 * @description
 */

@SpringBootTest(classes = ServerBootstrap.class)
@Slf4j
public class UserAnswerSubmitTest {

    @Autowired
    UserAnswerService userAnswerService;

    @Autowired
    private UserAnswerSumService answerSumService;

    @Test
    public void testSubmitHomework() throws DataAccessException, InterruptedException {
        String json = "{\"uid\":11498706,\"obj_id\":201000,\"obj_type\":202,\"usetime\":16,\"start_time\":1683615794078,\"end_time\":1683615810425,\"answer_detail\":[{\"question_id\":2899253,\"topic_id\":1986706,\"answer\":[\"A\"],\"del_flag\":\"0\",\"is_new_record\":false},{\"question_id\":2899257,\"topic_id\":1986710,\"answer\":[\"A\"],\"del_flag\":\"0\",\"is_new_record\":false},{\"question_id\":2899256,\"topic_id\":1986709,\"answer\":[\"D\"],\"del_flag\":\"0\",\"is_new_record\":false},{\"question_id\":2899255,\"topic_id\":1986708,\"answer\":[\"C\"],\"del_flag\":\"0\",\"is_new_record\":false}],\"is_submit\":1,\"product_id\":41311,\"goods_id\":12045,\"appid\":\"wwwedu24ol\",\"plat_form\":\"web\",\"category_id\":5584,\"old_auto_remove_version\":0,\"homework_id\":74827,\"study_path_id\":55008,\"done_source\":35,\"del_flag\":\"0\",\"is_new_record\":false}";

        Gson gson = GsonUtil.getGson();
        // 参照 cn.huanju.edu100.study.entry.thrift.UserAnswerThriftImpl.sty_userAnswerHomework
        UserHomeWorkAnswer userHomeWorkAnswer = gson.fromJson(json, UserHomeWorkAnswer.class);

        if (userHomeWorkAnswer == null
                || userHomeWorkAnswer.getUid() == null || userHomeWorkAnswer.getUid() <= 0L
                || userHomeWorkAnswer.getAnswerDetail() == null
                || ((userHomeWorkAnswer.getEndTime() == null || userHomeWorkAnswer.getStartTime() == null) && userHomeWorkAnswer
                .getUsetime() == null) || userHomeWorkAnswer.getObjId() == null
                || userHomeWorkAnswer.getObjType() == null) {
            log.error(" sty_userAnswerHomework parameter lose. uid or start_time or end_time or answer_detail or obj_id or obj_type is null");
        } else {
            userHomeWorkAnswer.setSchId(1L);
            if (userHomeWorkAnswer.getIsSubmit() == null) {
                userHomeWorkAnswer.setIsSubmit(UserAnswer.State.SUBMITTED);
            }
            /*对不正常的startTime、endTime值进行处理*/
            Date minDate = new Date(cn.huanju.edu100.study.util.Constants.USER_ANSWER_MIN_TIME);
            if (userHomeWorkAnswer.getStartTime().before(minDate)) {
                userHomeWorkAnswer.setStartTime(null);
            }
            if (userHomeWorkAnswer.getEndTime().before(minDate)) {
                userHomeWorkAnswer.setEndTime(null);
            }
            /*对不正常的usetime值进行处理*/
            if (userHomeWorkAnswer.getUsetime() != null && userHomeWorkAnswer.getUsetime().compareTo(0L) < 0) {
                userHomeWorkAnswer.setUsetime(0L);
            }
            List<UserAnswerDetail> details = userAnswerService.submitHomeWork(userHomeWorkAnswer,
                    userHomeWorkAnswer.getObjType());
        }
    }

    @Test
    public void testFindAnswerSumAndDetail() throws DataAccessException {
        int uid=100001320;
        Long userAnswerId=1215011063153233920L;
        var list = answerSumService.findAnswerSumAndDetail((long)uid, userAnswerId, null);
        System.out.println(JSONUtils.toJsonString(list));
    }

    @Test
    public void testGetUserAnswer() throws DataAccessException {
        int uid=100001320;
        long userAnswerId=1215011063153233920L;
        UserAnswer userAnswer = userAnswerService.getUserAnswer((long)uid, userAnswerId, true);
        System.out.println("score:"+userAnswer.getScore());
    }
}
