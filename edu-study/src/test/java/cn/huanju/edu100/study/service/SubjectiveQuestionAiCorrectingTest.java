package cn.huanju.edu100.study.service;

import cn.huanju.edu100.study.client.SubjectiveQuestionAiCorrectingService;
import cn.huanju.edu100.study.entry.ServerBootstrap;
import com.hqwx.study.entity.UserAnswer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/14 14:43
 * @description
 */
@SpringBootTest(classes = ServerBootstrap.class)
@Slf4j
public class SubjectiveQuestionAiCorrectingTest {
    @Autowired
    private SubjectiveQuestionAiCorrectingService subjectiveQuestionAiCorrectingService;

    @Test
    public void testTriggerAiCorrecting() {
        int categoryId=5584;
        int questionId=28996800;
        int questionSource =0;
        int topicId=1987173;
        int uid=11498706;
        Long userAnswerId=1342176754220797952L;
        subjectiveQuestionAiCorrectingService.triggerAiCorrecting(
                (long) uid, userAnswerId, (long)categoryId, (long) questionId, (long) topicId, questionSource,
                new SubjectiveQuestionAiCorrectingService.StreamObserver<SubjectiveQuestionAiCorrectingService.StreamData>() {
                    @Override
                    public void onNext(SubjectiveQuestionAiCorrectingService.StreamData streamData) {
                        log.info("onNext:{}", streamData);
                    }

                    @Override
                    public void onError(Throwable t) {
                        log.warn("onError", t);
                    }

                    @Override
                    public void onCompleted() {
                        log.info("onCompleted");
                    }
                });
    }

    @Test
    public void testPhotoAnswerAiCorrecting() {
        // 构建请求参数
        Long uid = 110L;
        String sessionId = "";
        
        // 构建params
        Map<String, Object> params = new HashMap<>();
        params.put("questionId", 29025512L);
        params.put("topicId", 2729501L);
        params.put("userAnswerId", 110110110L);
        params.put("questionSource", 1);
        params.put("secondCategory", 5632L);
        params.put("category", 5677L);

        // 构建content
        List<Map<String, Object>> content = new ArrayList<>();
        
        // 第一张图片
        Map<String, Object> image1 = new HashMap<>();
        image1.put("type", "image_url");
        Map<String, Object> imageUrl1 = new HashMap<>();
        imageUrl1.put("url", "https://oss-hqwx-edu24ol.oss-cn-beijing.aliyuncs.com/test/陈修海/1.jpg");
        image1.put("imageUrl", imageUrl1);
        content.add(image1);

        // 第二张图片
        Map<String, Object> image2 = new HashMap<>();
        image2.put("type", "image_url");
        Map<String, Object> imageUrl2 = new HashMap<>();
        imageUrl2.put("url", "https://oss-hqwx-edu24ol.oss-cn-beijing.aliyuncs.com/test/陈修海/2.jpg");
        image2.put("imageUrl", imageUrl2);
        content.add(image2);

        // 第三张图片
        Map<String, Object> image3 = new HashMap<>();
        image3.put("type", "image_url");
        Map<String, Object> imageUrl3 = new HashMap<>();
        imageUrl3.put("url", "https://oss-hqwx-edu24ol.oss-cn-beijing.aliyuncs.com/test/陈修海/3.jpg");
        image3.put("imageUrl", imageUrl3);
        content.add(image3);

        // 调用服务
        subjectiveQuestionAiCorrectingService.photoAnswerAiCorrecting(uid, sessionId, params, content);
    }


    @Test
    public void subjectiveQuestionAiCorrecting() {
        // 构建请求参数
        Long uid = 110L;

        // 构建params
        Map<String, Object> params = new HashMap<>();
        params.put("questionId", 28996800L);
        params.put("topicId", 1987173L);
        params.put("userAnswerId", 1342176754220797952L);
        params.put("questionSource", 1);
        params.put("secondCategory", 5583L);
        params.put("category", 5584L);
        subjectiveQuestionAiCorrectingService.subjectiveQuestionAiCorrecting(uid, params);
    }

    @Test
    public void testCorrectSubjectiveResult() {
        Long uid = 11498706L;
        Long userAnswerId = 1383419912938786816L;
        Integer questionSource = 1;
        subjectiveQuestionAiCorrectingService.correctSubjectiveResult(uid, userAnswerId, questionSource);
    }

    @Test
    public void syncPaperAiCorrectingResultToComment() {
        UserAnswer userAnswer = new UserAnswer();
        userAnswer.setId(1383476747813195776L);
        userAnswer.setUid(11498706L);
        userAnswer.setPaperId(65675L);
        userAnswer.setGoodsId(13512L);
        userAnswer.setProductId(45869L);
        subjectiveQuestionAiCorrectingService.syncPaperAiCorrectingResultToComment(userAnswer);
    }
}
