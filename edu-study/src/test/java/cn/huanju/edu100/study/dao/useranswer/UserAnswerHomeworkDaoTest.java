package cn.huanju.edu100.study.dao.useranswer;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserHomeWorkAnswerDao;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class UserAnswerHomeworkDaoTest extends BaseTest {
    @Autowired
    private UserHomeWorkAnswerDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 223749307424047104L);
        param.put("uid", 101567141L);
        UserHomeWorkAnswer ua = dao.getShardingById(param);
        Assertions.assertEquals(1531275591L, ua.getUsetime());
    }
}
