package cn.huanju.edu100.study.dao.useranswer;

import cn.huanju.edu100.study.BaseTest;
import cn.huanju.edu100.study.dao.UserCenterCollectQuestionDao;
import cn.huanju.edu100.study.model.UserCenterCollectQuestion;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class UserCenterCollectQuestionDaoTest extends BaseTest {
    @Autowired
    private UserCenterCollectQuestionDao dao;

    @Test
    public void testSelectSharding() throws Exception{
        Map<String, Object> param = new HashMap<>();
        param.put("id", 920273122082029568L);
        param.put("uid", 100001320L);
        UserCenterCollectQuestion ua = dao.getShardingById(param);
        Assertions.assertEquals(2482592L, ua.getQuestionId());
    }
}
