#debug: true
logging:
  config: classpath:log4j2-dev.xml

grpc:
  server:
    port: 58285
    service-id: edu-study-grpc

spring:
  config:
    activate:
      on-profile: dev
  application:
    name: edu-study
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    openfeign:
      okhttp:
        enabled: true
    loadbalancer:
      nacos:
        enabled: true
    nacos:
      discovery:
        namespace: 0322cc9b-b9a4-4529-b996-1466f2a8c500
        server-addr: ***********:8848
        properties:
          - namespace: 0ad9485c-a5ce-41a8-b1f6-3a3b0f5108c9


  thrift:
#    server:
#      service-id: edu-study
#      port: 8285
    client:
      config:
        edu-analyse-grpc:
          useNacos: false
          useGrpc: true
          host: **************
          port: 58289
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100AnalyseServiceGrpc$Edu100AnalyseServiceBlockingStub
          soTimeout: 5000
        edu-goods-grpc :
          useNacos: false
          useGrpc: true
          host: ************
          port: 58282
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100GoodsServiceGrpc$Edu100GoodsServiceBlockingStub
          soTimeout: 15000
        edu-user-grpc:
          useNacos: false
          useGrpc: true
          host: *************
          port: 58287
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100UserServiceGrpc$Edu100UserServiceBlockingStub
          soTimeout: 5000
        edu-knowledge-grpc:
          useNacos: false
          useGrpc: true
#          host: **************
          host: *************
          port: 58281
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100KnowledgeServiceGrpc$Edu100KnowledgeServiceBlockingStub
          soTimeout: 50000
        edu-leaf-grpc:
          useNacos: false
          useGrpc: true
          host: **************
          port: 58298
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100LeafServiceGrpc$Edu100LeafServiceBlockingStub
          soTimeout: 5000
        edu-member-grpc:
          useNacos: false
          useGrpc: true
          host: **************
          port: 58286
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100MemberServiceGrpc$Edu100MemberServiceBlockingStub
          soTimeout: 5000
        edu-search-grpc:
          useNacos: false
          useGrpc: true
          host: **************
          port: 58290
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100HqSearchServiceGrpc$Edu100HqSearchServiceBlockingStub
          soTimeout: 5000
        edu-stustamp-grpc:
          useNacos: false
          useGrpc: true
          host: ************
          port: 58293
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100StustampServiceGrpc$Edu100StustampServiceBlockingStub
          soTimeout: 5000
        hqwx-fund-service:
          useNacos: false
          useGrpc: true
          host: **************
          port: 9192
          grpcDefaultStub: cn.huanju.edu100.grpc.service.HqwxFundServiceGrpc$HqwxFundServiceBlockingStub
          soTimeout: 5000
        push-grpc:
          useGrpc: true
          useNacos: false
          testOnCreate: true
          host: *************
          port: 52303
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100MsgServiceGrpc$Edu100MsgServiceBlockingStub
          grpcClientClass: cn.huanju.edu100.grpc.client.impl.DefaultMsgGrpcClient
          soTimeout: 5000
          pool:
            maxActive: 100
            minIdle: 2
            maxIdle: 50
            timeBetweenEvictionRunsMillis: 60000
            testWhileIdle: true
            testOnBorrow: false
        edu-prompt-grpc:
          useNacos: false
          useGrpc: true
          host: *************
          port: 58304
          grpcDefaultStub: cn.huanju.edu100.grpc.service.Edu100PromptServiceGrpc$Edu100PromptServiceBlockingStub
          soTimeout: 5000

  datasource:
    hikari:
      maximum-pool-size: 5
    dynamic:
      primary: default-ds #避免不容易发现的错误 此处默认配一个不存在的数据源
#      strict: false
      hikari:
        validation-timeout: 3000
        min-idle: 2
        maximum-pool-size: ${spring.datasource.hikari.maximum-pool-size}
        max-lifetime: 200000
        max-pool-size: 10
        idle-timeout: 60000
        connection-timeout: 60000
        initialization-fail-timeout: 1000
      datasource:
        his_masterDS:
          url: ***************************************************************************************************************************************************************************************************************************************
          username: hq_test_rw
          password: zp5zoUkD0MdLVAOxDHwTaWMF
          driver-class-name: com.mysql.cj.jdbc.Driver
        his_slave1DS:
          url: ***************************************************************************************************************************************************************************************************************************************
          username: hq_test_rw
          password: zp5zoUkD0MdLVAOxDHwTaWMF
          driver-class-name: com.mysql.cj.jdbc.Driver
        his_slave2DS:
          url: ***************************************************************************************************************************************************************************************************************************************
          username: hq_test_rw
          password: zp5zoUkD0MdLVAOxDHwTaWMF
          driver-class-name: com.mysql.cj.jdbc.Driver
        study-item-data-slave-ds:
          url: ****************************************************************************************************************************************************************************************************************************************
          username: hq_test_rw
          password: zp5zoUkD0MdLVAOxDHwTaWMF
          driver-class-name: com.mysql.cj.jdbc.Driver

    shardingsphere:
      datasource:
        ds: default-ds,qbox-ds,tutor-ds,ua-ds,ua-even-ds,ua-odd-ds,al-default-ds
        default-ds:
          names: master-ds
          master-ds:
            type: com.zaxxer.hikari.HikariDataSource
            jdbcUrl: ****************************************************************************************************************************************************************************************************************************************
            username: hq_test_rw
            password: zp5zoUkD0MdLVAOxDHwTaWMF
            driver-class-name: com.mysql.cj.jdbc.Driver
            minimumIdle: 2
            maximumPoolSize: ${spring.datasource.hikari.maximum-pool-size}
            validationTimeout: 5000
            initializationFailTimeout: 1
            connectionTimeout: 60000
            ## maxLifetime 必须不小于 idleTimeout
            maxLifetime: 30000
            idleTimeout: 20000
          rules:
            sharding:
              tables:
                user_video_log:
                  actual-data-nodes: master-ds.user_video_log_${0..31}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: default-user-video-log-inline
                tutor_user_video_log:
                  actual-data-nodes: master-ds.tutor_user_video_log_${0..31}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: default-tutor-user-video-log-table-inline
                question_answer_detail:
                  actual-data-nodes: master-ds.question_answer_detail_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: default-question-answer-detail-table-inline
                user_brush_question_info:
                  actual-data-nodes: master-ds.user_brush_question_info_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: default-user-brush-question-info-table-inline
                user_study_prop:
                  actual-data-nodes: master-ds.user_study_prop_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: default-user-study-prop-table-inline
              sharding-algorithms:
                default-user-video-log-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_video_log_$->{uid % 32}
                default-tutor-user-video-log-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: tutor_user_video_log_$->{uid % 32}
                default-question-answer-detail-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: question_answer_detail_$->{uid % 64}
                default-user-brush-question-info-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_brush_question_info_$->{uid % 64}
                default-user-study-prop-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_study_prop_$->{uid % 64}

        al-default-ds:
          names: master-ds
          master-ds:
            type: com.zaxxer.hikari.HikariDataSource
            jdbcUrl: ***************************************************************************************************************************************************************************************************************************************
            username: hq_test_rw
            password: zp5zoUkD0MdLVAOxDHwTaWMF
            driver-class-name: com.mysql.cj.jdbc.Driver
            minimumIdle: 2
            maximumPoolSize: ${spring.datasource.hikari.maximum-pool-size}
            validationTimeout: 5000
            initializationFailTimeout: 1
            connectionTimeout: 60000
            ## maxLifetime 必须不小于 idleTimeout
            maxLifetime: 30000
            idleTimeout: 20000

        qbox-ds:
          names: qbox-master-ds
          qbox-master-ds:
            type: com.zaxxer.hikari.HikariDataSource
            jdbcUrl: ****************************************************************************************************************************************************************************************************************************************
            username: hq_test_rw
            password: zp5zoUkD0MdLVAOxDHwTaWMF
            driver-class-name: com.mysql.cj.jdbc.Driver
            minimumIdle: 2
            maxLifetime: 30000
            maximumPoolSize: ${spring.datasource.hikari.maximum-pool-size}
            validationTimeout: 5000
            initializationFailTimeout: 1
            connectionTimeout: 60000
            idleTimeout: 20000
          rules:
            sharding:
              tables:
                user_done_question:
                  actual-data-nodes: qbox-master-ds.user_done_question_${0..127}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: qbox-user-done-question-inline
                user_wrong_question:
                  actual-data-nodes: qbox-master-ds.user_wrong_question_${0..127}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: qbox-user-wrong-question-table-inline
                user_recite_question:
                  actual-data-nodes: qbox-master-ds.user_recite_question_${0..127}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: qbox-user-recite-question-table-inline
                user_wipe_out_wrong_question:
                  actual-data-nodes: qbox-master-ds.user_wipe_out_wrong_question_${0..127}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: qbox-user-wipe-out-wrong-question-table-inline
              sharding-algorithms:
                qbox-user-done-question-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_done_question_$->{uid % 128}
                qbox-user-wrong-question-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_wrong_question_$->{uid % 128}
                qbox-user-recite-question-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_recite_question_$->{uid % 128}
                qbox-user-wipe-out-wrong-question-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_wipe_out_wrong_question_$->{uid % 128}

        tutor-ds:
          names: tutor-master-ds
          tutor-master-ds:
            type: com.zaxxer.hikari.HikariDataSource
            jdbcUrl: ****************************************************************************************************************************************************************************************************************************************
            username: hq_test_rw
            password: zp5zoUkD0MdLVAOxDHwTaWMF
            driver-class-name: com.mysql.cj.jdbc.Driver
            minimumIdle: 2
            maximumPoolSize: ${spring.datasource.hikari.maximum-pool-size}
            validationTimeout: 5000
            initializationFailTimeout: 1
            connectionTimeout: 60000
            ## maxLifetime 必须不小于 idleTimeout
            maxLifetime: 30000
            idleTimeout: 20000
          rules:
            sharding:
              tables:
                tutor_user_video_log:
                  actual-data-nodes: tutor-master-ds.tutor_user_video_log_${0..31}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: tutor-tutor-user-video-log-table-inline
              sharding-algorithms:
                tutor-tutor-user-video-log-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: tutor_user_video_log_$->{uid % 32}
            props:
              sql-show: false

        ua-ds:
          names: master-ds0,master-ds1
          master-ds0:
            type: com.zaxxer.hikari.HikariDataSource
            jdbcUrl: ****************************************************************************************************************************************************************************************************************************************
            username: hq_test_rw
            password: zp5zoUkD0MdLVAOxDHwTaWMF
            driver-class-name: com.mysql.cj.jdbc.Driver
            minimumIdle: 2
            maxLifetime: 30000
            maximumPoolSize: ${spring.datasource.hikari.maximum-pool-size}
            validationTimeout: 5000
            initializationFailTimeout: 1
            connectionTimeout: 60000
            idleTimeout: 20000
          master-ds1:
            type: com.zaxxer.hikari.HikariDataSource
            jdbcUrl: ****************************************************************************************************************************************************************************************************************************************
            username: hq_test_rw
            password: zp5zoUkD0MdLVAOxDHwTaWMF
            driver-class-name: com.mysql.cj.jdbc.Driver
            minimumIdle: 2
            maximumPoolSize: ${spring.datasource.hikari.maximum-pool-size}
            validationTimeout: 5000
            initializationFailTimeout: 1
            connectionTimeout: 60000
            ## maxLifetime 必须不小于 idleTimeout
            maxLifetime: 30000
            idleTimeout: 20000
          rules:
            sharding:
              tables:
                bindingTables:
                  - master-ds0.user_answer_sum_count
                  - master-ds0.user_answer_sum_extend
#                user_answer_sum_count:
#                  actual-data-nodes: ua0-m-s.user_answer_sum_count
#                user_answer_sum_extend:
#                  actual-data-nodes: ua0-m-s.user_answer_sum_extend
                user_answer:
                  actual-data-nodes: master-ds0.user_answer_${0..63},master-ds1.user_answer_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-user-answer-table-inline
                user_answer_homework:
                  actual-data-nodes: master-ds0.user_answer_homework_${0..63},master-ds1.user_answer_homework_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-user-answer-homework-table-inline
                user_answer_sum:
                  actual-data-nodes: master-ds0.user_answer_sum_${0..63},master-ds1.user_answer_sum_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-user-answer-sum-table-inline
                user_answer_detail:
                  actual-data-nodes: master-ds0.user_answer_detail_${0..63},master-ds1.user_answer_detail_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-user-answer-detail-table-inline
                user_done_record:
                  actual-data-nodes: master-ds0.user_done_record_${0..63},master-ds1.user_done_record_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-user-done-record-table-inline
                user_sub_error_question:
                  actual-data-nodes: master-ds0.user_sub_error_question_${0..63},master-ds1.user_sub_error_question_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-user-sub-error-question-table-inline
                user_correct_question:
                  actual-data-nodes: master-ds0.user_correct_question_${0..63},master-ds1.user_correct_question_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-user-correct-question-table-inline
                user_center_collect_question:
                  actual-data-nodes: master-ds0.user_center_collect_question_${0..63},master-ds1.user_center_collect_question_${0..63}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-user-center-collect-question-table-inline
                evaluation_user_answer:
                  actual-data-nodes: master-ds0.evaluation_user_answer_${0..31},master-ds1.evaluation_user_answer_${0..31}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-evaluation-user-answer-table-inline
                evaluation_user_base_answer_detail:
                  actual-data-nodes: master-ds0.evaluation_user_base_answer_detail_${0..31},master-ds1.evaluation_user_base_answer_detail_${0..31}
                  table-strategy:
                    standard:
                      sharding-column: uid
                      sharding-algorithm-name: ua-evaluation-user-base-answer-detail-table-inline
              default-database-strategy:
                standard:
                  sharding-column: uid
                  sharding-algorithm-name: ua-user-answer-database-inline
              sharding-algorithms:
                ua-user-answer-database-inline:
                  type: INLINE
                  props:
                    algorithm-expression: master-ds${uid.longValue() % 2}
                ua-user-answer-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_answer_$->{uid % 64}
                ua-user-answer-homework-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_answer_homework_$->{uid % 64}
                ua-user-answer-sum-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_answer_sum_$->{uid % 64}
                ua-user-answer-detail-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_answer_detail_$->{uid % 64}
                ua-user-done-record-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_done_record_$->{uid % 64}
                ua-user-sub-error-question-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_sub_error_question_$->{uid % 64}
                ua-user-correct-question-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_correct_question_$->{uid % 64}
                ua-user-center-collect-question-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: user_center_collect_question_$->{uid % 64}
                ua-evaluation-user-answer-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: evaluation_user_answer_$->{uid % 32}
                ua-evaluation-user-base-answer-detail-table-inline:
                  type: INLINE
                  props:
                    algorithm-expression: evaluation_user_base_answer_detail_$->{uid % 32}

        ua-even-ds:
          names: ua-even-master-ds
          ua-even-master-ds:
            type: com.zaxxer.hikari.HikariDataSource
            jdbcUrl: ****************************************************************************************************************************************************************************************************************************************
            username: hq_test_rw
            password: zp5zoUkD0MdLVAOxDHwTaWMF
            driver-class-name: com.mysql.cj.jdbc.Driver
            minimumIdle: 2
            maximumPoolSize: ${spring.datasource.hikari.maximum-pool-size}
            validationTimeout: 5000
            initializationFailTimeout: 1
            connectionTimeout: 60000
            ## maxLifetime 必须不小于 idleTimeout
            maxLifetime: 30000
            idleTimeout: 20000


        ua-odd-ds:
          names: ua-odd-master-ds
          ua-odd-master-ds:
            type: com.zaxxer.hikari.HikariDataSource
            jdbcUrl: ****************************************************************************************************************************************************************************************************************************************
            username: hq_test_rw
            password: zp5zoUkD0MdLVAOxDHwTaWMF
            driver-class-name: com.mysql.cj.jdbc.Driver
            minimumIdle: 2
            maximumPoolSize: ${spring.datasource.hikari.maximum-pool-size}
            validationTimeout: 5000
            initializationFailTimeout: 1
            connectionTimeout: 60000
            ## maxLifetime 必须不小于 idleTimeout
            maxLifetime: 30000
            idleTimeout: 20000
        props:
          sql-show: true
          max-connections-size-per-query: 50

mybatis-plus:
  mapper-locations: classpath*:mybatis/**/*.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: 4
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    cache-enabled: false
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

rediscluster:
  password: 6oH7JLhfqyqdsEzbKBqfaLQb
  local:
    - r-2ze03be28c0df024-pn.redis.rds.aliyuncs.com:6379
  far:
    - r-2ze03be28c0df024-pn.redis.rds.aliyuncs.com:6379
  lock:
    - r-2ze03be28c0df024-pn.redis.rds.aliyuncs.com:6379
answer:
  rediscluster:
    password: 6oH7JLhfqyqdsEzbKBqfaLQb
    local:
      - r-2ze03be28c0df024-pn.redis.rds.aliyuncs.com:6379
    far:
      - r-2ze03be28c0df024-pn.redis.rds.aliyuncs.com:6379
al:
  master:
    redis:
      host: r-2ze0b77af282c724-pn.redis.rds.aliyuncs.com
      password: piZ2YspJLE3rXSMvqqYY7Bdm
      port: 6379
      timeout: 5000

kafka:
  servers: bigdata-service-202:9092,bigdata-service-203:9092,bigdata-service-204:9092
  secret:
    servers: bigdata-service-202:9092,bigdata-service-203:9092,bigdata-service-204:9092
  questionanswerdetail:
    topic: QuestionAnswerDetail
    ifPartition: 0
    partitionNum: 3
    role: roleQestionad
  useranswerhomework:
    topic: UserAnswerHomework
  useranswerpaper:
    topic: UserAnswerPaper
  useragreement:
    topic: study_userAgreement
    ifPartition: 0
    partitionNum: 3
    role: roleUserAgreement
  userhomeworktask:
    topic: UserBuyGoodsProp
    group: userHomeworkTaskGroup-dev
  studycollect:
    topic: study_collect_dev
  studyerror:
    topic: study_error_info_dev
  fund:
    topic: fund_event_test
  studyquestion:
    topic: study_question_answer_dev
  goodsstudystatus:
    topic: stustamp_goods_study_status_dev
  consumer:
    studycollectreverse:
      topic: study_collect_reverse_dev
      groupId: groupId001
      enableAutoCommit: false
      sessionTimeoutMs: 30000
      autoOffsetReset: latest
      maxPollRecords: 10
    studyerrorinforeverse:
      topic: study_error_info_reverse_dev
      groupId: groupId001
      enableAutoCommit: false
      sessionTimeoutMs: 30000
      autoOffsetReset: latest
      maxPollRecords: 10

security:
  protocol: SASL_PLAINTEXT
sasl:
  mechanism: SCRAM-SHA-256
  jaas:
    config: "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"appId_000047_cn\" password=\"GYDeitpC9Zp4\";"

schedulerContainer:
  use: false

studyDataListKey: studyDataList
isTest: true
adminapiUrl: http://hqwx-gateway-common/goods-common #http://***********:8100

xxl:
  job:
    admin:
      addresses: http://**************:9093/xxl-job-admin
    executor:
      appname: edu-study
      #对应在xxl-job控制条新增执行器时，填写的APPNAME，执行器心跳注册分组依据
      ip: 127.0.0.1
      #执行器IP默认为空表示自动获取ip，
      port: 12022
      # 执行器的默认端口为9999，port是一个内置的jetty服务端口
      logpath: /tmp/log/edu100/edu-study
      # 执行器运行日志文件存储的磁盘位置，需要对该路径拥有读写权限
      logretentiondays: 5
    accessToken:

feign:
  okhttp:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 10000
    disable-ssl-validation: true
    max-connections: 20480
    max-connections-per-route: 1024

wx:
  miniapp:
    configs:
      - appid: wxb7340ab1fe9bb3ee
        secret: 5b8be3ea45a59946cb937f80be630692
        token: hqwxmall_wxapp
        aesKey: szRZQv2G7NtYXmdjFM8AEwBmFyQ0iK2GCObPjsO3bbA
        msgDataFormat: XML
        hqAppid: hqwxmall_wxapp
        name: 环球网校教育培训旗舰店
      - appid: wx890b8e807c46f703
        secret: 15bfa7ff79659b8b3107a18f39f3a16b
        token: hqwxlive_wxapp
        aesKey: bXS7BXpW96rkV9xp73XkLiWcyP7or5jJ18GPifhG2Z6
        msgDataFormat: XML
        hqAppid: hqwxlive_wxapp
        name: 环球网校在线课堂
      - appid: wxae3a9748da999008
        secret: 816e9e03058c70f2b0d5e9feac940016
        token: kuaitiku_wxapp_LqxUmQmeMVpQY
        aesKey: DCO6DLSyN7LqxUmQmeMVpQYpFlA8Ua0kHBkBLlPu24J
        msgDataFormat: XML
        hqAppid: kuaitiku_wxapp
        name: 快题库pro
      - appid: wxa82c51e3653555fe
        secret: f0db3f7c4f2a4956066a827a5d22f6ff
        token: yianapp
        aesKey: DCO6DLSyN7LqxUmQmeMVpQYpFlA8Ua0kHBkBLlPu24J
        msgDataFormat: XML
        hqAppid: yianapp
        name: 益安书画院
      - appid: wxa67ff4ae920050fb
        secret: b57ab4403bd201bf3aa344687953f97a
        token: mba_wxapp
        aesKey: MfCRiVDxGW3gFFWxgu5u7t4xbQz1qAjnw4lScdXFLDt
        msgDataFormat: XML
        hqAppid: mba_wxapp
        name: MBA考试课程
  mp:
    configs:
      - appid: wx2e0e3fc8de6211a4
        secret: f9051a65accfd46d942d7c11dbf4d7c1
        token: weixin
        aesKey: inRX85TO5Q3g0OL7e0MqGFAd2cnHyBEoZQbyOTossI3
        hqAppid: yian_service
        schId: 1308
        name: 益安书画公众号
      - appid: wxc29727e3989a47d4
        secret: e5a857829e7dc4ce5a4cedee95bd3208
        token: weixin
        aesKey: 9eW9dOYwC7QL6BS9YEJ7M3QBidMo7C2WoL965YCaOwn
        hqAppid: hqwxedu_mp
        schId: 2
        userKey: doHCs8PbtIZ2SJFg
        name: 环球网校职业教育(微信公众号)
bytedance:
  miniapp:
    configs:
      - appid: tt177961c488538dd4
        secret: 9424aa61e9c22f5ee0f0ed4c80cea6cb20def8da
        hqAppid: hqwx_tt
        name: 环球网校(头条小程序)
        developerFlag: hqwxtt0223

# 阿里云OSS文件上传配置, 可以配置多个bucket 20210201
file-upload-oss:
  bucketList:
      bucketKey: video
      endpoint: oss-cn-beijing.aliyuncs.com
      accessKeyId: LTAI4GDyrSdXwKS5rrikXrxE
      accessKeySecret: ******************************
      roleArn: 1
      roleSessionName: 1
      bucket: oss-hqwx-video
      domain: oss-hqwx-video.hqwx.com
      addPath: 0

data-cache:
  ## 开发环境禁用初始化题库与教材的关系数据（影响启动速度）
  enableInitQbox2TeachBook: false

gpt:
  chatUrl:
    openai: http://47.88.26.104:10049/api/v2/stream
assistant:
  streamUrl: http://**************:8080/api/v2/assistant/stream/question
hq-ai-agent:
    url: http://**************:8080

jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      uri: redis://<EMAIL>:6379/
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: r-2ze03be28c0df024-pn.redis.rds.aliyuncs.com
      port: 6379
      password: 6oH7JLhfqyqdsEzbKBqfaLQb