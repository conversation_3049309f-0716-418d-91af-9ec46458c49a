<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VSysUserClasses">

    <typeAlias alias="VSysUserClasses" type="cn.huanju.edu100.study.model.onetoone.VSysUserClasses" />

	<sql id="vSysUserClassesColumns">
			a.user_id AS "userId",
			a.v_cls_id AS "vClsId",
			a.name AS "name"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="VSysUserClasses">
	   select
	    	<include refid="vSysUserClassesColumns"/>
		FROM v_sys_user_classes a
		WHERE a.id = #id#
	</select>



	<select id="findList" parameterClass="java.util.Map" resultClass="VSysUserClasses">
		SELECT
	   		<include refid="vSysUserClassesColumns"/>
		FROM v_sys_user_classes a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="vClsId">
						a.v_cls_id = #vClsId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="clsIds">
				 	a.v_cls_id IN
					<iterate conjunction="," open="(" close=")" property="clsIds">
						#clsIds[]#
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>



</sqlMap>
