<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VLessonOtherInfo">

    <typeAlias alias="VLessonOtherInfo" type="cn.huanju.edu100.study.model.onetoone.VLessonOtherInfo" />

	<sql id="vLessonOtherInfoColumns">
			a.v_lesson_id AS "vLessonId",
			a.feedback_mark AS "feedbackMark",
			a.report_mark AS "reportMark",
			a.update_date AS "updateDate"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="VLessonOtherInfo">
	   select
	    	<include refid="vLessonOtherInfoColumns"/>
		FROM v_lesson_other_info a
		WHERE a.v_lesson_id = #id#
	</select>

	<insert id="insert" parameterClass="VLessonOtherInfo">
		INSERT INTO v_lesson_other_info(
			v_lesson_id,
			feedback_mark,
			report_mark,
			update_date
		) VALUES (
			#vLessonId#,
			#feedbackMark#,
			#reportMark#,
			NOW()
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="VLessonOtherInfo">
		UPDATE v_lesson_other_info SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="feedbackMark">
							feedback_mark = #feedbackMark#
						</isNotEmpty>
						<isNotEmpty prepend="," property="reportMark">
							report_mark = #reportMark#
						</isNotEmpty>
			</dynamic>
		WHERE v_lesson_id = #vLessonId#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_lesson_other_info where v_lesson_id = #id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="VLessonOtherInfo">
		SELECT
	   		<include refid="vLessonOtherInfoColumns"/>
		FROM v_lesson_other_info a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="vLessonId">
						a.v_lesson_id = #vLessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonIds">
				 	a.v_lesson_id IN
					<iterate conjunction="," open="(" close=")" property="lessonIds">
						#lessonIds[]#
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM v_lesson_other_info a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="vLessonId">
						a.v_lesson_id = #vLessonId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="VLessonOtherInfo">
		SELECT
	   		<include refid="vLessonOtherInfoColumns"/>
		FROM v_lesson_other_info a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
