<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VTeacherTeach">

    <typeAlias alias="VTeacherTeach" type="cn.huanju.edu100.study.model.onetoone.VTeacherTeach" />

	<sql id="vTeacherTeachColumns">
			a.id AS "id",
			a.teacher_id AS "teacherId",
			a.teach_id AS "teachId"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="VTeacherTeach">
	   select
	    	<include refid="vTeacherTeachColumns"/>
		FROM v_teacher_teach a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="VTeacherTeach">
		INSERT INTO v_teacher_teach(
			teacher_id,
			teach_id
		) VALUES (
			#teacherId#,
			#teachId#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="VTeacherTeach">
		UPDATE v_teacher_teach SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="teacherId">
							teacher_id = #teacherId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="teachId">
							teach_id = #teachId#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_teacher_teach where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="VTeacherTeach">
		SELECT
	   		<include refid="vTeacherTeachColumns"/>
		FROM v_teacher_teach a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teacherId">
						a.teacher_id = #teacherId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teachId">
						a.teach_id = #teachId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM v_teacher_teach a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teacherId">
						a.teacher_id = #teacherId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teachId">
						a.teach_id = #teachId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="VTeacherTeach">
		SELECT
	   		<include refid="vTeacherTeachColumns"/>
		FROM v_teacher_teach a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
