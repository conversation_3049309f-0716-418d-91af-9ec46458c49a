<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VMaterial">

    <typeAlias alias="VMaterial" type="cn.huanju.edu100.study.model.onetoone.VMaterial" />

	<sql id="vMaterialColumns">
			a.id AS "id",
			a.type AS "type",
			a.v_cls_id AS "vClsId",
			a.v_lesson_id AS "vLessonId",
			a.v_res_id AS "vResId",
			a.remarks AS "remarks",
			a.del_flag AS "delFlag",
			a.user_type AS "userType",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="VMaterial">
	   select
	    	<include refid="vMaterialColumns"/>
		FROM v_material a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="VMaterial">
		INSERT INTO v_material(
			type,
			v_cls_id,
			v_lesson_id,
			v_res_id,
			remarks,
			del_flag,
			user_type,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#type#,
			#vClsId#,
			#vLessonId#,
			#vResId#,
			#remarks#,
			#delFlag#,
			#userType#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="VMaterial">
		UPDATE v_material SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="vClsId">
							v_cls_id = #vClsId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="vLessonId">
							v_lesson_id = #vLessonId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="vResId">
							v_res_id = #vResId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="remarks">
							remarks = #remarks#
						</isNotEmpty>
						<isNotEmpty prepend="," property="userType">
							user_type = #userType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_material where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="VMaterial">
		SELECT
	   		<include refid="vMaterialColumns"/>
		FROM v_material a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="vClsId">
						a.v_cls_id = #vClsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="vLessonId">
						a.v_lesson_id = #vLessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="userType">
						a.user_type = #userType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="startTime">
						a.create_date >= #startTime#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="endTime">
						a.create_date &lt;= #endTime#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="clsIds">
				 	a.v_cls_id IN
					<iterate conjunction="," open="(" close=")" property="clsIds">
						#clsIds[]#
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM v_material a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="vClsId">
						a.v_cls_id = #vClsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="vLessonId">
						a.v_lesson_id = #vLessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="userType">
						a.user_type = #userType#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="VMaterial">
		SELECT
	   		<include refid="vMaterialColumns"/>
		FROM v_material a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>


	<insert id="insertBatch" parameterClass="java.util.List">
		INSERT INTO v_material(
			type,
			v_cls_id,
			v_lesson_id,
			v_res_id,
			remarks,
			del_flag,
			user_type,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES
		<iterate conjunction =",">
            (#list[].type#,
            #list[].vClsId#,
            #list[].vLessonId#,
            #list[].vResId#,
            #list[].remarks#,
            #list[].delFlag#,
            #list[].userType#,
            #list[].createBy#,
            now(),
            #list[].updateBy#,
            now())
    	</iterate>
	</insert>

</sqlMap>
