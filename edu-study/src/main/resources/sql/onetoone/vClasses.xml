<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VClasses">


    <sql id="vClassesColumns">
			a.id AS "id",
				a.classes AS "classes",
				a.name AS "name",
				a.service_type AS "serviceType",
				a.lesson_type AS "lessonType",
				a.product_id AS "productId",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.num AS "num",
				a.lesson_count AS "lessonCount",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.sch_id AS "schId",
				a.address_id AS "addressId",
				a.max_student AS "maxStudent",
				a.open_time AS "openTime",
				a.close_time AS "closeTime",
				a.create_by AS "createBy",
				a.create_date AS "createDate",
				a.update_by AS "updateBy",
				a.update_date AS "updateDate",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClasses">
	   select
				<include refid="vClassesColumns"/>
		FROM v_classes a
		WHERE a.id = #id#
	</select>

	 <insert id="insert" parameterClass="cn.huanju.edu100.study.model.onetoone.VClasses">
		INSERT INTO v_classes(
			classes,
			name,
			service_type,
			lesson_type,
			product_id,
			address_id,
			start_time,
			end_time,
			lesson_count,
			first_category,
			second_category,
			category_id,
			sch_id,
			open_time,
			close_time,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			max_student,
			num,
			del_flag
		) VALUES (
			#classes#,
			#name#,
			#serviceType#,
			#lessonType#,
			#productId#,
			#addressId#,
			#startTime#,
			#endTime#,
			#lessonCount#,
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#schId#,
			#openTime#,
			#closeTime#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#maxStudent#,
			#num#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.onetoone.VClasses">
		UPDATE v_classes SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="serviceType">
							service_type = #serviceType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="lessonType">
							lesson_type = #lessonType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="productId">
							product_id = #productId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="startTime">
							start_time = #startTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="endTime">
							end_time = #endTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="lessonCount">
							lesson_count = #lessonCount#
						</isNotEmpty>
						<isNotEmpty prepend="," property="firstCategory">
							first_category = #firstCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="schId">
							sch_id = #schId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="openTime">
							open_time = #openTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="closeTime">
							close_time = #closeTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_classes where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClasses">
		SELECT
				<include refid="vClassesColumns"/>
		FROM v_classes a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="num">
						a.num LIKE concat('%',#num#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="serviceType">
						a.service_type = #serviceType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonType">
						a.lesson_type = #lessonType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="productId">
						a.product_id = #productId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="startTime">
						a.start_time = #startTime#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="endTime">
						a.end_time = #endTime#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
				AND a.del_flag = 0
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClasses">
		SELECT
			<include refid="vClassesColumns"/>
		FROM v_classes a
		WHERE  a.id IN
		<iterate conjunction="," open="(" close=")" property="ids">
			#ids[]#
		</iterate>
		AND a.del_flag = '0'
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="schId">
				  	  a.sch_id = #schId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="schIds">
					a.sch_id IN
				<iterate conjunction="," open="(" close=")" property="schIds">
				  #schIds[]#
				</iterate>
			</isNotEmpty>
		</dynamic>
	</select>

	<select id="findListByGoodsAndProduct" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClasses">
	SELECT
			<include refid="vClassesColumns"/>
		FROM v_classes a, v_classes_goods b
		<dynamic prepend="WHERE">
			a.id = b.classes_id
			AND a.del_flag = '0'
			AND a.close_time IS NULL
			AND	a.product_id = #productId#
			AND	b.goods_id = #goodsId#
		</dynamic>
		<dynamic prepend="ORDER BY">
			a.start_time, a.id
		</dynamic>
	</select>


	<select id="findListByTeacherUid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClasses">
		SELECT
			DISTINCT(a.id) AS "id",
				a.classes AS "classes",
				a.name AS "name",
				a.service_type AS "serviceType",
				a.lesson_type AS "lessonType",
				a.product_id AS "productId",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.num AS "num",
				a.lesson_count AS "lessonCount",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.sch_id AS "schId",
				a.address_id AS "addressId",
				a.open_time AS "openTime",
				a.close_time AS "closeTime",
				a.create_by AS "createBy",
				a.create_date AS "createDate",
				a.update_by AS "updateBy",
				a.update_date AS "updateDate",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
		FROM v_classes a join v_lesson b on a.id = b.cls_id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="schId">
				  	  a.sch_id = #schId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="schIds">
					a.sch_id IN
				<iterate conjunction="," open="(" close=")" property="schIds">
				  #schIds[]#
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="teacherUid">
				  	  b.teacher_uid = #teacherUid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="lessonType">
				  	  b.type = #lessonType#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="classStatus">
				  	<isEqual property="classStatus" compareValue="0">
						a.start_time > NOW()
					</isEqual>
					<isEqual property="classStatus" compareValue="1">
						a.start_time &lt;= NOW() AND NOW() &lt;= a.end_time
					</isEqual>
					<isEqual property="classStatus" compareValue="2">
						a.close_time is not null
					</isEqual>
			</isNotEmpty>
			AND a.del_flag = '0'
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM v_classes a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="serviceType">
						a.service_type = #serviceType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonType">
						a.lesson_type = #lessonType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="productId">
						a.product_id = #productId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="startTime">
						a.start_time = #startTime#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="endTime">
						a.end_time = #endTime#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
				AND a.del_flag = '0'
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClasses">
		SELECT
				<include refid="vClassesColumns"/>
		FROM v_classes a
		WHERE a.del_flag = '0'
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
