<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VClassesGoods">

    <typeAlias alias="VClassesGoods" type="cn.huanju.edu100.study.model.onetoone.VClassesGoods" />

	<sql id="vClassesGoodsColumns">
			a.id AS "id",
			a.classes_id AS "classesId",
			a.goods_id AS "goodsId",
			a.del_flag AS "delFlag"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="VClassesGoods">
	   select
	    	<include refid="vClassesGoodsColumns"/>
		FROM v_classes_goods a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="VClassesGoods">
		INSERT INTO v_classes_goods(
			classes_id,
			goods_id,
			del_flag
		) VALUES (
			#classesId#,
			#goodsId#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="VClassesGoods">
		UPDATE v_classes_goods SET
		del_flag = '0'
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classesId">
							classes_id = #classesId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="goodsId">
							goods_id = #goodsId#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_classes_goods where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="VClassesGoods">
		SELECT
	   		<include refid="vClassesGoodsColumns"/>
		FROM v_classes_goods a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="classesId">
						a.classes_id = #classesId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="goodsId">
						a.goods_id = #goodsId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM v_classes_goods a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="classesId">
						a.classes_id = #classesId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="goodsId">
						a.goods_id = #goodsId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="VClassesGoods">
		SELECT
	   		<include refid="vClassesGoodsColumns"/>
		FROM v_classes_goods a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
