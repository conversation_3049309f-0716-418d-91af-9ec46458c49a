<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VTeacher">

    <typeAlias alias="VTeacher" type="cn.huanju.edu100.study.model.onetoone.VTeacher" />

	<sql id="vTeacherColumns">
			a.id AS "id",
			a.uid AS "uid",
			a.name AS "name",
			a.number AS "number",
			a.phone AS "phone",
			a.is_full_time AS "isFullTime",
			a.is_chinese AS "isChinese",
			a.min_lesson AS "minLesson",
			a.description AS "description",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate",
			a.sch_id AS "schId",
			a.ip AS "ip",
			a.del_flag AS "delFlag"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="VTeacher">
	   select
	    	<include refid="vTeacherColumns"/>
		FROM v_teacher a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="VTeacher">
		INSERT INTO v_teacher(
			name,
			uid,
			number,
			phone,
			is_full_time,
			is_chinese,
			min_lesson,
			description,
			create_by,
			create_date,
			update_by,
			update_date,
			sch_id,
			ip,
			del_flag
		) VALUES (
			#name#,
			#uid#,
			#number#,
			#phone#,
			#isFullTime#,
			#isChinese#,
			#minLesson#,
			#description#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#schId#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="VTeacher">
		UPDATE v_teacher SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="number">
							number = #number#
						</isNotEmpty>
						<isNotEmpty prepend="," property="phone">
							phone = #phone#
						</isNotEmpty>
						<isNotEmpty prepend="," property="isFullTime">
							is_full_time = #isFullTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="isChinese">
							is_chinese = #isChinese#
						</isNotEmpty>
						<isNotEmpty prepend="," property="minLesson">
							min_lesson = #minLesson#
						</isNotEmpty>
						<isNotEmpty prepend="," property="description">
							description = #description#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="schId">
							sch_id = #schId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_teacher where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="VTeacher">
		SELECT
	   		<include refid="vTeacherColumns"/>
		FROM v_teacher a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByParam" parameterClass="java.util.Map" resultClass="VTeacher">
		SELECT
				<include refid="vTeacherColumns"/>
		FROM v_teacher a
		<dynamic prepend="WHERE">
			<isPropertyAvailable prepend="AND" property="ids">
			 a.id IN
				<iterate conjunction="," open="(" close=")" property="ids">
					#ids[]#
				</iterate>
			</isPropertyAvailable>
			<isPropertyAvailable prepend="AND" property="uids">
			 a.uid IN
				<iterate conjunction="," open="(" close=")" property="uids">
					#uids[]#
				</iterate>
			</isPropertyAvailable>
		AND a.del_flag = '0'
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM v_teacher a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE CONCAT('%',#name#,'%')
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="VTeacher">
		SELECT
	   		<include refid="vTeacherColumns"/>
		FROM v_teacher a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
