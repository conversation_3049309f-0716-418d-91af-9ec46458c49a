<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VTeacherPay">

    <typeAlias alias="VTeacherPay" type="cn.huanju.edu100.study.model.onetoone.VTeacherPay" />

	<sql id="vTeacherPayColumns">
			a.id AS "id",
			a.teacher_id AS "teacherId",
			a.pay_id AS "payId"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="VTeacherPay">
	   select
	    	<include refid="vTeacherPayColumns"/>
		FROM v_teacher_pay a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="VTeacherPay">
		INSERT INTO v_teacher_pay(
			teacher_id,
			pay_id
		) VALUES (
			#teacherId#,
			#payId#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="VTeacherPay">
		UPDATE v_teacher_pay SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="teacherId">
							teacher_id = #teacherId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="payId">
							pay_id = #payId#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_teacher_pay where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="VTeacherPay">
		SELECT
	   		<include refid="vTeacherPayColumns"/>
		FROM v_teacher_pay a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teacherId">
						a.teacher_id = #teacherId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="payId">
						a.pay_id = #payId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM v_teacher_pay a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teacherId">
						a.teacher_id = #teacherId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="payId">
						a.pay_id = #payId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="VTeacherPay">
		SELECT
	   		<include refid="vTeacherPayColumns"/>
		FROM v_teacher_pay a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
