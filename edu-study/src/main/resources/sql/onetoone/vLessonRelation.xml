<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VLessonRelation">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLessonRelation">
	   select
				a.id AS "id",
				a.live_lesson_id AS "liveLessonId",
				a.hq_lesson_id AS "hqLessonId",
				a.live_cls_id AS "liveClsId",
				a.hq_obj_id AS "hqObjId",
				a.hq_room_id AS "hqRoomId",
				a.type AS "type",
				a.create_by AS "createBy.id",
				a.create_date AS "createDate",
				a.update_by AS "updateBy.id",
				a.update_date AS "updateDate",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
		FROM v_lesson_relation a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.onetoone.VLessonRelation">
		INSERT INTO v_lesson_relation(
			live_lesson_id,
			hq_lesson_id,
			live_cls_id,
			hq_obj_id,
			hq_room_id,
			type,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#liveLessonId#,
			#hqLessonId#,
			#liveClsId#,
			#hqObjId#,
			#hqRoomId#,
			#type#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
<!-- 		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey> -->
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.onetoone.VLessonRelation">
		UPDATE v_lesson_relation SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="liveLessonId">
							live_lesson_id = #liveLessonId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="hqLessonId">
							hq_lesson_id = #hqLessonId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="liveClsId">
							live_cls_id = #liveClsId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="hqObjId">
							hq_obj_id = #hqObjId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="hqRoomId">
							hq_room_id = #hqRoomId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_lesson_relation where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLessonRelation">
		SELECT
								a.id AS "id",
					a.live_lesson_id AS "liveLessonId",
					a.hq_lesson_id AS "hqLessonId",
					a.live_cls_id AS "liveClsId",
					a.hq_obj_id AS "hqObjId",
					a.hq_room_id AS "hqRoomId",
					a.type AS "type",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM v_lesson_relation a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="idList">
					a.hq_lesson_id in
					<iterate conjunction="," open="(" close=")" property="idList">
						<![CDATA[
							#idList[]#
						]]>
					</iterate>
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="liveLessonId">
						a.live_lesson_id = #liveLessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="hqLessonId">
						a.hq_lesson_id = #hqLessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="liveClsId">
						a.live_cls_id = #liveClsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="hqObjId">
						a.hq_obj_id = #hqObjId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="hqRoomId">
						a.hq_room_id = #hqRoomId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="delFlag">
						a.del_flag = #delFlag#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM v_lesson_relation a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="liveLessonId">
						a.live_lesson_id = #liveLessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="hqLessonId">
						a.hq_lesson_id = #hqLessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="liveClsId">
						a.live_cls_id = #liveClsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="hqObjId">
						a.hq_obj_id = #hqObjId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="hqRoomId">
						a.hq_room_id = #hqRoomId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="delFlag">
						a.del_flag = #delFlag#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLessonRelation">
		SELECT
								a.id AS "id",
					a.live_lesson_id AS "liveLessonId",
					a.hq_lesson_id AS "hqLessonId",
					a.live_cls_id AS "liveClsId",
					a.hq_obj_id AS "hqObjId",
					a.hq_room_id AS "hqRoomId",
					a.type AS "type",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM v_lesson_relation a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByLiveLessonIdList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLessonRelation">
		SELECT
		a.id AS "id",
		a.live_lesson_id AS "liveLessonId",
		a.hq_lesson_id AS "hqLessonId",
		a.live_cls_id AS "liveClsId",
		a.hq_obj_id AS "hqObjId",
		a.hq_room_id AS "hqRoomId",
		a.type AS "type",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.ip AS "ip",
		a.del_flag AS "delFlag"
		FROM v_lesson_relation a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="idList">
				a.live_lesson_id in
				<iterate conjunction="," open="(" close=")" property="idList">
					<![CDATA[
							#idList[]#
						]]>
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="type">
				a.type = #type#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="delFlag">
				a.del_flag = #delFlag#
			</isNotEmpty>
		</dynamic>
	</select>
</sqlMap>
