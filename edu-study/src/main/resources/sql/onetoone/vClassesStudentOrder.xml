<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VClassesStudentOrder">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder">
	   select
				a.id AS "id",
				a.uid AS "uid",
				a.cls_id AS "clsId",
				a.order_id AS "orderId",
				a.goods_id AS "goodsId",
				a.product_id AS "productId",
				a.status AS "status",
				a.lesson_count AS "lessonCount",
				a.complete_count AS "completeCount",
				a.update_date AS "updateDate"
		FROM v_classes_student_order a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder">
		INSERT INTO v_classes_student_order(
			uid,
			cls_id,
			order_id,
			goods_id,
			product_id,
			status,
			lesson_count,
			complete_count,
			update_date
		) VALUES (
			#uid#,
			#clsId#,
			#orderId#,
			#goodsId#,
			#productId#,
			#status#,
			#lessonCount#,
			#completeCount#,
			#updateDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>


	<update id="update"
		parameterClass="cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder">
		UPDATE v_classes_student_order
		<dynamic prepend="set">
			<isNotEmpty prepend="," property="uid">
				uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="," property="status">
				status = #status#
			</isNotEmpty>
			<isNotEmpty prepend="," property="lessonCount">
				lesson_count = #lessonCount#
			</isNotEmpty>
			<isNotEmpty prepend="," property="completeCount">
				complete_count = #completeCount#
			</isNotEmpty>
			<isNotEmpty prepend="," property="updateDate">
				update_date = #updateDate#
			</isNotEmpty>
		</dynamic>
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="id">
				id = #id#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="orderId">
				order_id = #orderId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="goodsId">
				goods_id = #goodsId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productId">
				product_id = #productId#
			</isNotEmpty>
		</dynamic>
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_classes_student_order where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.cls_id AS "clsId",
					a.order_id AS "orderId",
					a.goods_id AS "goodsId",
					a.product_id AS "productId",
					a.status AS "status",
					a.lesson_count AS "lessonCount",
					a.complete_count AS "completeCount",
					a.update_date AS "updateDate"
		FROM v_classes_student_order a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="clsId">
						a.cls_id = #clsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="orderId">
						a.order_id = #orderId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="goodsId">
						a.goods_id = #goodsId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="productId">
						a.product_id = #productId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="status">
					<isEqual property="status" compareValue="-1">
						a.status != 0
					</isEqual>
					<isNotEqual property="status" compareValue="-1">
						a.status = #status#
					</isNotEqual>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="listStudentCountByClsIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.CountModel">

		SELECT cls_id AS "id", COUNT(DISTINCT(uid)) AS "num"
		FROM v_classes_student_order
		<dynamic prepend="where">
			<isPropertyAvailable prepend="AND" property="clsIds">
			 cls_id IN
				<iterate conjunction="," open="(" close=")" property="clsIds">
					#clsIds[]#
				</iterate>
			</isPropertyAvailable>
			<isNotEmpty prepend="AND" property="validStatus">
						status IN (0, 2)
			</isNotEmpty>
		</dynamic>
		GROUP BY cls_id
	</select>

	<select id="findListByQueryParam" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder">
		SELECT
					a.id AS "id",
					a.uid AS "uid",
					a.cls_id AS "clsId",
					a.order_id AS "orderId",
					a.goods_id AS "goodsId",
					a.product_id AS "productId",
					a.status AS "status",
					a.lesson_count AS "lessonCount",
					a.complete_count AS "completeCount",
					a.update_date AS "updateDate"
		FROM v_classes_student_order a
		<dynamic prepend="where">
				<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="validStatus">
						a.status IN (0, 2)
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="unFinish">
						(a.lesson_count > a.complete_count OR a.complete_count IS NULL)
				</isNotEmpty>
				<isPropertyAvailable prepend="AND" property="clsIds">
						a.cls_id IN
					<iterate conjunction="," open="(" close=")" property="clsIds">
							#clsIds[]#
					</iterate>
				</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM v_classes_student_order a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="clsId">
						a.cls_id = #clsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="orderId">
						a.order_id = #orderId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.cls_id AS "clsId",
					a.order_id AS "orderId",
					a.goods_id AS "goodsId",
					a.product_id AS "productId",
					a.status AS "status",
					a.lesson_count AS "lessonCount",
					a.complete_count AS "completeCount",
					a.update_date AS "updateDate"
		FROM v_classes_student_order a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
