<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VLesson">

    <sql id="vLessonColumns">
			a.id AS "id",
				a.classes AS "classes",
				a.cls_id AS "clsId",
				a.room_id AS "roomId",
				a.teacher_uid AS "teacherUid",
				a.lesson_type AS "lessonType",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.name AS "name",
				a.status AS "status",
				a.type AS "type",
				a.pay_type AS "payType",
				a.teach_type AS "teachType",
				a.s_lesson AS "sLesson",
				a.t_lesson AS "tLesson",
				a.serial_num AS "serialNum",
				a.address AS "address",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.create_by AS "createBy",
				a.create_date AS "createDate",
				a.update_by AS "updateBy",
				a.update_date AS "updateDate",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLesson">
	   select
				<include refid="vLessonColumns"/>
		FROM v_lesson a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.onetoone.VLesson">
		INSERT INTO v_lesson(
			classes,
			cls_id,
			room_id,
			teacher_uid,
			lesson_type,
			first_category,
			second_category,
			category_id,
			name,
			status,
			start_time,
			end_time,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#classes#,
			#clsId#,
			#roomId#,
			#teacherUid#,
			#lessonType#,
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#name#,
			#status#,
			#startTime#,
			#endTime#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.onetoone.VLesson">
		UPDATE v_lesson SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="clsId">
							cls_id = #clsId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="roomId">
							room_id = #roomId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="teacherUid">
							teacher_uid = #teacherUid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="lessonType">
							lesson_type = #lessonType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="firstCategory">
							first_category = #firstCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="status">
							status = #status#
						</isNotEmpty>
						<isNotEmpty prepend="," property="startTime">
							start_time = #startTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="endTime">
							end_time = #endTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_lesson where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLesson">
		SELECT
				<include refid="vLessonColumns"/>
		FROM v_lesson a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="clsId">
						a.cls_id = #clsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="roomIdList">
						a.room_id IN
					<iterate conjunction="," open="(" close=")" property="roomIdList">
						#roomIdList[]#
					</iterate>
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="roomId">
						a.room_id = #roomId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teacherUid">
						a.teacher_uid = #teacherUid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonType">
						a.lesson_type = #lessonType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
	<!-- 				<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty> -->
					<isNotEmpty prepend="AND" property="startTime">
						a.start_time = #startTime#
				</isNotEmpty>
				AND a.del_flag = '0'
				AND a.status &lt; 2
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByParam" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLesson">
		SELECT
				<include refid="vLessonColumns"/>
		FROM v_lesson a
		<dynamic prepend="where">
		<isPropertyAvailable prepend="AND" property="clsIds">
			a.cls_id IN
			<iterate conjunction="," open="(" close=")" property="clsIds">
			#clsIds[]#
			</iterate>
		</isPropertyAvailable>
		<isNotNull prepend="AND" property="startTime">
			a.start_time &gt;= #startTime#
		</isNotNull>
		<isNotNull prepend="AND" property="endTime">
			a.end_time &lt;= #endTime#
		</isNotNull>
		<isNotNull prepend="AND" property="teacherUid">
			a.teacher_uid = #teacherUid#
		</isNotNull>
		<isPropertyAvailable prepend="AND" property="schIds">
		 	a.sch_id IN
			<iterate conjunction="," open="(" close=")" property="schIds">
			#schIds[]#
			</iterate>
		</isPropertyAvailable>
		<isPropertyAvailable prepend="AND" property="roomIdList">
		 	a.room_id IN
			<iterate conjunction="," open="(" close=")" property="roomIdList">
			#roomIdList[]#
			</iterate>
		</isPropertyAvailable>
		<isPropertyAvailable prepend="AND" property="ids">
		 	a.id IN
			<iterate conjunction="," open="(" close=")" property="ids">
			#ids[]#
			</iterate>
		</isPropertyAvailable>
		AND a.del_flag = '0'
		AND a.status &lt; 2
		</dynamic>
		ORDER BY a.start_time desc
	</select>

	<select id="newFindListByParam" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLesson">
		SELECT
				<include refid="vLessonColumns"/>
		FROM v_lesson a
		<dynamic prepend="where">
		<isPropertyAvailable prepend="AND" property="startTime">
			<![CDATA[((a.start_time>=#startTime# and a.start_time<=#endTime#) or (a.end_time>=#startTime# and a.end_time<=#endTime#) or (a.start_time<=#startTime# and a.end_time>=#endTime#))]]>
		</isPropertyAvailable>
		<isPropertyAvailable prepend="AND" property="roomIdList">
		 	a.room_id IN
			<iterate conjunction="," open="(" close=")" property="roomIdList">
			#roomIdList[]#
			</iterate>
		</isPropertyAvailable>
		<isPropertyAvailable prepend="AND" property="ids">
		 	a.id IN
			<iterate conjunction="," open="(" close=")" property="ids">
			#ids[]#
			</iterate>
		</isPropertyAvailable>
		AND a.del_flag = '0'
		AND a.status &lt; 2
		</dynamic>
		ORDER BY a.start_time desc
	</select>

	<select id="getCompleteCountByClsId" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(0)
		FROM v_lesson
		WHERE del_flag = 0 AND cls_id = #clsId# AND <![CDATA[end_time < now()]]>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM v_lesson a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="clsId">
						a.cls_id = #clsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="roomId">
						a.room_id = #roomId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teacherUid">
						a.teacher_uid = #teacherUid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonType">
						a.lesson_type = #lessonType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="startTime">
						a.start_time = #startTime#
				</isNotEmpty>
				AND a.del_flag = '0'
				AND a.status &lt; 2
		</dynamic>
	</select>

	<select id="getPeriodCount" parameterClass="java.util.Map" resultClass="java.lang.Double">
		SELECT SUM(a.s_lesson)
		FROM v_lesson a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="clsId">
						a.cls_id = #clsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="roomId">
						a.room_id = #roomId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teacherUid">
						a.teacher_uid = #teacherUid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonType">
						a.lesson_type = #lessonType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="startTime">
						a.start_time = #startTime#
				</isNotEmpty>
				AND a.del_flag = '0'
				AND a.status &lt; 2
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.onetoone.VLesson">
		SELECT
			<include refid="vLessonColumns"/>
		FROM v_lesson a
		WHERE a.del_flag = '0'
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
