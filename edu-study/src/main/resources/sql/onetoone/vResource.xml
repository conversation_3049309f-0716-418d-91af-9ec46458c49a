<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VResource">

    <typeAlias alias="VResource" type="cn.huanju.edu100.study.model.onetoone.VResource" />

	<sql id="vResourceColumns">
			a.id AS "id",
			a.name AS "name",
			a.filename AS "filename",
			a.status AS "status",
			a.url AS "url",
			a.type AS "type",
			a.remarks AS "remarks",
			a.user_type AS "userType",
			a.sch_id AS "schId",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="VResource">
	   select
	    	<include refid="vResourceColumns"/>
		FROM v_resource a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="VResource">
		INSERT INTO v_resource(
			name,
			filename,
			status,
			url,
			type,
			remarks,
			user_type,
			sch_id,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#name#,
			#filename#,
			#status#,
			#url#,
			#type#,
			#remarks#,
			#userType#,
			#schId#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="VResource">
		UPDATE v_resource SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="filename">
							filename = #filename#
						</isNotEmpty>
						<isNotEmpty prepend="," property="status">
							status = #status#
						</isNotEmpty>
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="remarks">
							remarks = #remarks#
						</isNotEmpty>
						<isNotEmpty prepend="," property="url">
							url = #url#
						</isNotEmpty>
						<isNotEmpty prepend="," property="userType">
							user_type = #userType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="schId">
							sch_id = #schId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM v_resource where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="VResource">
		SELECT
	   		<include refid="vResourceColumns"/>
		FROM v_resource a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="userType">
						a.user_type = #userType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="schId">
						a.sch_id = #schId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="createBy">
						a.create_by = #createBy#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="ids">
				 	a.id IN
					<iterate conjunction="," open="(" close=")" property="ids">
						#ids[]#
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM v_resource a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE CONCAT('%',#name#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="userType">
						a.user_type = #userType#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="schId">
						a.sch_id = #schId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="createBy">
						a.create_by = #createBy#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="ids">
				 	a.id IN
					<iterate conjunction="," open="(" close=")" property="ids">
						#ids[]#
					</iterate>
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="VResource">
		SELECT
	   		<include refid="vResourceColumns"/>
		FROM v_resource a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
