<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="StudyTaskPushRes">

	<sql id="studyTaskPushResColumns">
		a.id AS "id",
		a.res_id AS "resId",
		a.task_id AS "taskId",
		a.res_type AS "resType",
		a.knowledge_id AS "knowledgeId",
		a.uid
	</sql>

	<sql id="studyTaskPushResJoins">
	</sql>

    <select id="findListByTaskIdList" parameterClass="java.util.Map"
            resultClass="cn.huanju.edu100.study.model.StudyTaskPushRes">
        SELECT
			<include refid="studyTaskPushResColumns"/>
		FROM study_task_push_res a
		where a.uid = #uid#
		and a.task_id IN ($taskIds$)
    </select>

    <insert id="insertPushResList" parameterClass="java.util.List">
    	replace into study_task_push_res (
		  res_id
		  ,knowledge_id
		  ,task_id
		  ,uid
		)
		values
		<iterate conjunction =",">
            (#list[].resId#,
            #list[].knowledgeId#,
            #list[].taskId#,
            #list[].uid#)
    	</iterate>
    </insert>

</sqlMap>
