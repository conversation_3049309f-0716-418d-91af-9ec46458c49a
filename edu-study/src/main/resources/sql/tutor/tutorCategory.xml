<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorCategory">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorCategory">
	   select
				a.id AS "id",
				a.category_id AS "categoryId",
				a.classes AS "classes",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.summary AS "summary",
				a.create_by AS "createBy.id",
				a.create_date AS "createDate",
				a.update_by AS "updateBy.id",
				a.update_date AS "updateDate",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
		FROM tutor_category a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorCategory">
		INSERT INTO tutor_category(
			category_id,
			classes,
			start_time,
			end_time,
			summary,
			create_by,
			create_date,
			update_by,
			ip
		) VALUES (
			#categoryId#,
			#classes#,
			#startTime#,
			#endTime#,
			#summary#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#ip#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorCategory">
		UPDATE tutor_category SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="startTime">
							start_time = #startTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="endTime">
							end_time = #endTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="summary">
							summary = #summary#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_category where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorCategory">
		SELECT
								a.id AS "id",
					a.category_id AS "categoryId",
					a.classes AS "classes",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.summary AS "summary",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_category a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="delFlag">
						a.del_flag = #delFlag#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="endTime">
						a.end_time >= #endTime#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_category a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
			</dynamic>
	</select>

	<select id="getCategoryByIdList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorCategory">
		SELECT
		a.id AS "id",
		a.category_id AS "categoryId",
		a.classes AS "classes",
		a.start_time AS "startTime",
		a.end_time AS "endTime",
		a.summary AS "summary",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.ip AS "ip",
		a.del_flag AS "delFlag"
		FROM tutor_category a
		where a.del_flag = 0 and a.category_id in
		<iterate conjunction="," open="(" close=")" property="categoryIdList">
			#categoryIdList[]#
		</iterate>
		order by start_time asc
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorCategory">
		SELECT
								a.id AS "id",
					a.category_id AS "categoryId",
					a.classes AS "classes",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.summary AS "summary",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_category a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
