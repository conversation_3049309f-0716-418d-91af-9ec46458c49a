<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorStudentCategory">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentCategory">
	   select
				a.id AS "id",
				a.uid AS "uid",
				a.classes AS "classes",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.goods_id AS "goodsId",
				a.order_id AS "orderId",
				a.type AS "type",
				a.goods_name AS "goodsName",
				a.create_by AS "createBy",
				a.create_date AS "createDate",
				a.update_by AS "updateBy",
				a.update_date AS "updateDate"
		FROM tutor_student_category a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentCategory">
		INSERT INTO tutor_student_category(
			uid,
			classes,
			start_time,
			end_time,
			first_category,
			second_category,
			category_id,
			goods_id,
			order_id,
			sch_id,
			type,
			status,
			goods_name,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#uid#,
			#classes#,
			#startTime#,
			#endTime#,
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#goodsId#,
			#orderId#,
			#schId#,
			#type#,
			#status#,
			#goodsName#,
			#createBy#,
			NOW(),
			#updateBy#,
			NOW()
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentCategory">
		UPDATE tutor_student_category SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="startTime">
							start_time = #startTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="endTime">
							end_time = #endTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="goodsId">
							goods_id = #goodsId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="goodsName">
							goods_name = #goodsName#
						</isNotEmpty>
						<isNotEmpty prepend="," property="status">
							status = #status#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
			</dynamic>
			<dynamic prepend="where">
				<isNotEmpty prepend="AND" property="id">
						id = #id#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="orderId">
						order_id = #orderId#
				</isNotEmpty>
			</dynamic>
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_student_category where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentCategory">
		SELECT
					a.id AS "id",
					a.uid AS "uid",
					a.classes AS "classes",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.goods_id AS "goodsId",
					a.order_id AS "orderId",
					a.type AS "type",
					a.goods_name AS "goodsName",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate"
		FROM tutor_student_category a
			<dynamic prepend="where">
				a.status > 1 and
				<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="schId">
						a.sch_id = #schId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="orderId">
						a.order_id = #orderId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="goodsId">
						a.goods_id = #goodsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByParam" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentCategory">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.classes AS "classes",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.goods_id AS "goodsId",
					a.order_id AS "orderId",
					a.type AS "type",
					a.goods_name AS "goodsName",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate"
		FROM tutor_student_category a
			<dynamic prepend="where">
				<isNotEmpty prepend="AND" property="orderId">
						a.order_id = #orderId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="goodsId">
						a.goods_id = #goodsId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_student_category a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="schId">
						a.sch_id = #schId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentCategory">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.classes AS "classes",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.goods_id AS "goodsId",
					a.order_id AS "orderId",
					a.type AS "type",
					a.goods_name AS "goodsName",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate"
		FROM tutor_student_category a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
