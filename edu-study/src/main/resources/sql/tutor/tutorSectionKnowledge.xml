<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorSectionKnowledge">

    <typeAlias alias="TutorSectionKnowledge" type="cn.huanju.edu100.study.model.tutor.TutorSectionKnowledge" />

	<sql id="tutorSectionKnowledgeColumns">
			a.id AS "id",
			a.weike_id AS "weikeId",
			a.chapter_id AS "chapterId",
			a.section_id AS "sectionId",
			a.knowledge_id AS "knowledgeId",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate",
			a.ip AS "ip"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="TutorSectionKnowledge">
	   select
	    	<include refid="tutorSectionKnowledgeColumns"/>
		FROM tutor_section_knowledge a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="TutorSectionKnowledge">
		INSERT INTO tutor_section_knowledge(
			weike_id,
			chapter_id,
			section_id,
			knowledge_id,
			create_by,
			create_date,
			update_by,
			update_date,
			ip
		) VALUES (
			#weikeId#,
			#chapterId#,
			#sectionId#,
			#knowledgeId#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="TutorSectionKnowledge">
		UPDATE tutor_section_knowledge SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="weikeId">
							weike_id = #weikeId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="chapterId">
							chapter_id = #chapterId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="sectionId">
							section_id = #sectionId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="knowledgeId">
							knowledge_id = #knowledgeId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_section_knowledge where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="TutorSectionKnowledge">
		SELECT
	   		<include refid="tutorSectionKnowledgeColumns"/>
		FROM tutor_section_knowledge a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="weikeId">
						a.weike_id = #weikeId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="chapterId">
						a.chapter_id = #chapterId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="sectionId">
						a.section_id = #sectionId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="knowledgeId">
						a.knowledge_id = #knowledgeId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="listSectionKnowNums" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.CountModel">
		SELECT
	   		a.section_id as id,
		    count(distinct(knowledge_id)) as num
		FROM tutor_section_knowledge a
			<dynamic prepend="WHERE">

				<isNotEmpty prepend="AND" property="sectionIdList">
					a.section_id in
					<iterate conjunction="," open="(" close=")" property="sectionIdList">
						<![CDATA[
							#sectionIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		group by a.section_id
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM tutor_section_knowledge a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="weikeId">
						a.weike_id = #weikeId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="chapterId">
						a.chapter_id = #chapterId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="sectionId">
						a.section_id = #sectionId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="knowledgeId">
						a.knowledge_id = #knowledgeId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="TutorSectionKnowledge">
		SELECT
	   		<include refid="tutorSectionKnowledgeColumns"/>
		FROM tutor_section_knowledge a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
