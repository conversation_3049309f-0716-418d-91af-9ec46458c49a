<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorTaskPaper">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTaskPaper">
	   select
				a.id AS "id",
				a.type AS "type",
				a.flag AS "flag",
				a.detail_id AS "detailId",
				a.is_send AS "isSend",
				a.bak1 AS "bak1",
				a.bak2 AS "bak2"
		FROM tutor_task_paper a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorTaskPaper">
		INSERT INTO tutor_task_paper(
			type,
			detail_id,
			is_send,
			bak1,
			bak2
		) VALUES (
			#type#,
			#detailId#,
			#isSend#,
			#bak1#,
			#bak2#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorTaskPaper">
		UPDATE tutor_task_paper SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="detailId">
							detail_id = #detailId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="isSend">
							is_send = #isSend#
						</isNotEmpty>
						<isNotEmpty prepend="," property="bak1">
							bak1 = #bak1#
						</isNotEmpty>
						<isNotEmpty prepend="," property="bak2">
							bak2 = #bak2#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_task_paper where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTaskPaper">
		SELECT
								a.id AS "id",
					a.type AS "type",
					a.flag AS "flag",
					a.detail_id AS "detailId",
					a.is_send AS "isSend",
					a.bak1 AS "bak1",
					a.bak2 AS "bak2"
		FROM tutor_task_paper a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="detailId">
						a.detail_id = #detailId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="isSend">
						a.is_send = #isSend#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="bak1">
						a.bak1 = #bak1#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="bak2">
						a.bak2 = #bak2#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="taskIdList">
						a.id in
					<iterate conjunction="," open="(" close=")" property="taskIdList">
						<![CDATA[
							#taskIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_task_paper a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="detailId">
						a.detail_id = #detailId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="isSend">
						a.is_send = #isSend#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="bak1">
						a.bak1 = #bak1#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="bak2">
						a.bak2 = #bak2#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTaskPaper">
		SELECT
								a.id AS "id",
					a.type AS "type",
					a.flag AS "flag",
					a.detail_id AS "detailId",
					a.is_send AS "isSend",
					a.bak1 AS "bak1",
					a.bak2 AS "bak2"
		FROM tutor_task_paper a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
