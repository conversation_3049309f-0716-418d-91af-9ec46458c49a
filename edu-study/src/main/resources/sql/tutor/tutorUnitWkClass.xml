<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorUnitWkClass">

    <typeAlias alias="TutorUnitWkClass" type="cn.huanju.edu100.study.model.tutor.TutorUnitWkClass" />

	<sql id="tutorUnitWkClassColumns">
			a.id AS "id",
			a.unit_id AS "unitId",
			a.wk_class_id AS "wkClassId",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="TutorUnitWkClass">
	   select
	    	<include refid="tutorUnitWkClassColumns"/>
		FROM tutor_unit_wk_class a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="TutorUnitWkClass">
		INSERT INTO tutor_unit_wk_class(
			unit_id,
			wk_class_id,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#unitId#,
			#wkClassId#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="TutorUnitWkClass">
		UPDATE tutor_unit_wk_class SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="unitId">
							unit_id = #unitId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="wkClassId">
							wk_class_id = #wkClassId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_unit_wk_class where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="TutorUnitWkClass">
		SELECT
	   		<include refid="tutorUnitWkClassColumns"/>
		FROM tutor_unit_wk_class a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="unitId">
						a.unit_id = #unitId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="wkClassId">
						a.wk_class_id = #wkClassId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="unitIdList">
						a.unit_id in
					<iterate conjunction="," open="(" close=")" property="unitIdList">
						<![CDATA[
					#unitIdList[]#
				]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM tutor_unit_wk_class a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="unitId">
						a.unit_id = #unitId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="wkClassId">
						a.wk_class_id = #wkClassId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="TutorUnitWkClass">
		SELECT
	   		<include refid="tutorUnitWkClassColumns"/>
		FROM tutor_unit_wk_class a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
