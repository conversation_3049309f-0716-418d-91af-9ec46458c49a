<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorWeikeClass">

    <typeAlias alias="TutorWeikeClass" type="cn.huanju.edu100.study.model.tutor.TutorWeikeClass" />

	<sql id="tutorWeikeClassColumns">
			a.id AS "id",
			a.first_category AS "firstCategory",
			a.second_category AS "secondCategory",
			a.category_id AS "categoryId",
			a.classes AS "classes",
			a.name AS "name",
			a.status AS "status",
			a.teach_book_id AS "teachBookId",
			a.teach_book_name AS "teachBookName",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate",
			a.ip AS "ip",
			a.del_flag AS "delFlag"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="TutorWeikeClass">
	   select
	    	<include refid="tutorWeikeClassColumns"/>
		FROM tutor_weike_class a
		WHERE a.id = #id#
	</select>

	<select id="getByTaskId" parameterClass="java.util.Map" resultClass="TutorWeikeClass">
	   select
	    	<include refid="tutorWeikeClassColumns"/>
		FROM
		tutor_section_task b join
		tutor_weike_class a on b.weike_id = a.id
		WHERE
		b.id = #taskId#
	</select>

	<select id="getBySectionId" parameterClass="java.util.Map" resultClass="TutorWeikeClass">
	   select
	    	<include refid="tutorWeikeClassColumns"/>
		FROM
		tutor_wk_class_chapter b join
		tutor_weike_class a on b.wk_class_id = a.id
		WHERE
		b.id = #sectionId#
	</select>

	<insert id="insert" parameterClass="TutorWeikeClass">
		INSERT INTO tutor_weike_class(
			first_category,
			second_category,
			category_id,
			classes,
			name,
			status,
			teach_book_id,
			teach_book_name,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#classes#,
			#name#,
			#status#,
			#teachBookId#,
			#teachBookName#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="TutorWeikeClass">
		UPDATE tutor_weike_class SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="firstCategory">
							first_category = #firstCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="status">
							status = #status#
						</isNotEmpty>
						<isNotEmpty prepend="," property="teachBookId">
							teach_book_id = #teachBookId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="teachBookName">
							teach_book_name = #teachBookName#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_weike_class where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="TutorWeikeClass">
		SELECT
	   		<include refid="tutorWeikeClassColumns"/>
		FROM tutor_weike_class a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teachBookId">
						a.teach_book_id = #teachBookId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="listByIds" parameterClass="java.util.Map" resultClass="TutorWeikeClass">
		SELECT
	   		<include refid="tutorWeikeClassColumns"/>
		FROM tutor_weike_class a
			<dynamic prepend="WHERE">
				<isNotEmpty prepend="AND" property="idList">
					a.id IN
				<iterate conjunction="," open="(" close=")" property="idList">
					#idList[]#
				</iterate>
				</isNotEmpty>
				AND a.del_flag = '0'
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM tutor_weike_class a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="teachBookId">
						a.teach_book_id = #teachBookId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="TutorWeikeClass">
		SELECT
	   		<include refid="tutorWeikeClassColumns"/>
		FROM tutor_weike_class a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
