<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorStudentOverview">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentOverview">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.uid AS "uid",
				a.plan_task AS "planTask",
				a.complete_task AS "completeTask",
				a.plan_knowledge AS "planKnowledge",
				a.complete_knowledge AS "completeKnowledge",
				a.wrong_knowledge AS "wrongKnowledge",
				a.plan_video AS "planVideo",
				a.actual_video AS "actualVideo",
				a.actual_length AS "actualLength",
				a.plan_length AS "planLength",
				a.push_count AS "pushCount",
				a.plan_question AS "planQuestion",
				a.wrong_question AS "wrongQuestion",
				a.actual_question AS "actualQuestion",
				a.news_count AS "newsCount"
		FROM tutor_student_overview a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentOverview">
		INSERT INTO tutor_student_overview(
			classes,
			first_category,
			second_category,
			category_id,
			uid,
			plan_task,
			complete_task,
			plan_knowledge,
			complete_knowledge,
			wrong_knowledge,
			plan_video,
			actual_video,
			actual_length,
			plan_length,
			push_count,
			plan_question,
			wrong_question,
			actual_question,
			news_count
		) VALUES (
			#classes#,
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#uid#,
			#planTask#,
			#completeTask#,
			#planKnowledge#,
			#completeKnowledge#,
			#wrongKnowledge#,
			#planVideo#,
			#actualVideo#,
			#actualLength#,
			#planLength#,
			#pushCount#,
			#planQuestion#,
			#wrongQuestion#,
			#actualQuestion#,
			#newsCount#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentOverview">
		UPDATE tutor_student_overview SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="firstCategory">
							first_category = #firstCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planTask">
							plan_task = #planTask#
						</isNotEmpty>
						<isNotEmpty prepend="," property="completeTask">
							complete_task = #completeTask#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planKnowledge">
							plan_knowledge = #planKnowledge#
						</isNotEmpty>
						<isNotEmpty prepend="," property="completeKnowledge">
							complete_knowledge = #completeKnowledge#
						</isNotEmpty>
						<isNotEmpty prepend="," property="wrongKnowledge">
							wrong_knowledge = #wrongKnowledge#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planVideo">
							plan_video = #planVideo#
						</isNotEmpty>
						<isNotEmpty prepend="," property="actualVideo">
							actual_video = #actualVideo#
						</isNotEmpty>
						<isNotEmpty prepend="," property="actualLength">
							actual_length = #actualLength#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planLength">
							plan_length = #planLength#
						</isNotEmpty>
						<isNotEmpty prepend="," property="pushCount">
							push_count = #pushCount#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planQuestion">
							plan_question = #planQuestion#
						</isNotEmpty>
						<isNotEmpty prepend="," property="wrongQuestion">
							wrong_question = #wrongQuestion#
						</isNotEmpty>
						<isNotEmpty prepend="," property="actualQuestion">
							actual_question = #actualQuestion#
						</isNotEmpty>
						<isNotEmpty prepend="," property="newsCount">
							news_count = #newsCount#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<update id="increaseCompleteCount" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentOverview">
		UPDATE tutor_student_overview SET complete_task = complete_task+1
		WHERE classes = #classes# and first_category = #firstCategory#
		and second_category = #secondCategory# and category_id = #categoryId# and uid = #uid#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_student_overview where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentOverview">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.uid AS "uid",
					a.plan_task AS "planTask",
					a.complete_task AS "completeTask",
					a.plan_knowledge AS "planKnowledge",
					a.complete_knowledge AS "completeKnowledge",
					a.wrong_knowledge AS "wrongKnowledge",
					a.plan_video AS "planVideo",
					a.actual_video AS "actualVideo",
					a.actual_length AS "actualLength",
					a.plan_length AS "planLength",
					a.push_count AS "pushCount",
					a.plan_question AS "planQuestion",
					a.wrong_question AS "wrongQuestion",
					a.actual_question AS "actualQuestion",
					a.news_count AS "newsCount"
		FROM tutor_student_overview a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="categoryIdList">
					a.category_id in
					<iterate conjunction="," open="(" close=")" property="categoryIdList">
						<![CDATA[
							#categoryIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="uidList">
					a.uid in
					<iterate conjunction="," open="(" close=")" property="uidList">
						<![CDATA[
							#uidList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="classesList">
					a.classes in
					<iterate conjunction="," open="(" close=")" property="classesList">
						<![CDATA[
							#classesList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_student_overview a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentOverview">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.uid AS "uid",
					a.plan_task AS "planTask",
					a.complete_task AS "completeTask",
					a.plan_knowledge AS "planKnowledge",
					a.complete_knowledge AS "completeKnowledge",
					a.wrong_knowledge AS "wrongKnowledge",
					a.plan_video AS "planVideo",
					a.actual_video AS "actualVideo",
					a.actual_length AS "actualLength",
					a.plan_length AS "planLength",
					a.push_count AS "pushCount",
					a.plan_question AS "planQuestion",
					a.wrong_question AS "wrongQuestion",
					a.actual_question AS "actualQuestion",
					a.news_count AS "newsCount"
		FROM tutor_student_overview a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
