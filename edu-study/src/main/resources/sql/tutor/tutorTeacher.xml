<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorTeacher">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTeacher">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.tuid AS "tuid",
				a.teaching_course AS "teachingCourse",
				a.name AS "name",
				a.create_by AS "createBy.id",
				a.create_date AS "createDate",
				a.update_by AS "updateBy.id",
				a.update_date AS "updateDate",
				a.phone AS "phone",
				a.type AS "type",
				a.sch_id AS "schId",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
		FROM tutor_teacher a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorTeacher">
		INSERT INTO tutor_teacher(
			classes,
			tuid,
			teaching_course,
			name,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#classes#,
			#tuid#,
			#teachingCourse#,
			#name#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorTeacher">
		UPDATE tutor_teacher SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="tuid">
							tuid = #tuid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="teachingCourse">
							teaching_course = #teachingCourse#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_teacher where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTeacher">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.tuid AS "tuid",
					a.teaching_course AS "teachingCourse",
					a.name AS "name",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.phone AS "phone",
					a.type AS "type",
					a.sch_id AS "schId",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_teacher a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="tuid">
						a.tuid = #tuid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="type">
					a.type = #type#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="schId">
					a.sch_id = #schId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByTuids" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTeacher">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.tuid AS "tuid",
					a.teaching_course AS "teachingCourse",
					a.name AS "name",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_teacher a
			WHERE a.tuid IN
		<iterate conjunction="," open="(" close=")" property="tuids">
			#tuids[]#
		</iterate>
		AND a.del_flag = '0'
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_teacher a
		<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="tuid">
						a.tuid = #tuid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="type">
					a.type = #type#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="schId">
					a.sch_id = #schId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTeacher">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.tuid AS "tuid",
					a.teaching_course AS "teachingCourse",
					a.name AS "name",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.phone AS "phone",
					a.type AS "type",
					a.sch_id AS "schId",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_teacher a
		<dynamic prepend="where">
		<isNotEmpty prepend="AND" property="id">
					a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="tuid">
						a.tuid = #tuid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="type">
					a.type = #type#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="schId">
					a.sch_id = #schId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
