<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorGroupStudent">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorGroupStudent">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.uid AS "uid",
				a.group_id AS "groupId",
				a.create_by AS "createBy",
				a.create_date AS "createDate",
				a.update_by AS "updateBy",
				a.update_date AS "updateDate"
		FROM tutor_group_student a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorGroupStudent">
		INSERT INTO tutor_group_student(
			classes,
			uid,
			group_id,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#classes#,
			#uid#,
			#groupId#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorGroupStudent">
		UPDATE tutor_group_student SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="groupId">
							group_id = #groupId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_group_student where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorGroupStudent">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.uid AS "uid",
					a.group_id AS "groupId",
					a.create_by AS "createBy",
					a.create_date AS "createDate",
					a.update_by AS "updateBy",
					a.update_date AS "updateDate"
		FROM tutor_group_student a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="groupId">
						a.group_id = #groupId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="groupIdList">
					a.group_id in
					<iterate conjunction="," open="(" close=")" property="groupIdList">
						<![CDATA[
							#groupIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_group_student a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="groupId">
						a.group_id = #groupId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorGroupStudent">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.uid AS "uid",
					a.group_id AS "groupId",
					a.create_by AS "createBy",
					a.create_date AS "createDate",
					a.update_by AS "updateBy",
					a.update_date AS "updateDate"
		FROM tutor_group_student a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

	<delete id="deleteByGroupId" parameterClass="java.util.Map">
		DELETE FROM tutor_group_student where uid=#uid# and classes=#classes# and group_id=#groupId#
	</delete>

</sqlMap>
