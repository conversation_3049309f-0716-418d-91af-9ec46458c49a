<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorPhase">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorPhase">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.plan_id AS "planId",
				a.name AS "name",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.create_by AS "createBy.id",
				a.create_date AS "createDate",
				a.update_by AS "updateBy.id",
				a.update_date AS "updateDate",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
		FROM tutor_phase a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorPhase">
		INSERT INTO tutor_phase(
			classes,
			plan_id,
			name,
			start_time,
			end_time,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#classes#,
			#planId#,
			#name#,
			#startTime#,
			#endTime#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorPhase">
		UPDATE tutor_phase SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planId">
							plan_id = #planId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="startTime">
							start_time = #startTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="endTime">
							end_time = #endTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_phase where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorPhase">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.plan_id AS "planId",
					a.name AS "name",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.description AS "description",
					a.del_flag AS "delFlag"
		FROM tutor_phase a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="planId">
						a.plan_id = #planId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="planIdList">
					a.plan_id in
					<iterate conjunction="," open="(" close=")" property="planIdList">
						<![CDATA[
							#planIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="idList">
					a.id in
					<iterate conjunction="," open="(" close=")" property="idList">
						<![CDATA[
							#idList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_phase a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="planId">
						a.plan_id = #planId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorPhase">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.plan_id AS "planId",
					a.name AS "name",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.description AS "description",
					a.del_flag AS "delFlag"
		FROM tutor_phase a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
