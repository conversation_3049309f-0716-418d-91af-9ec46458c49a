<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorPlan">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorPlan">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.group_id AS "groupId",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.name AS "name",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.create_by AS "createBy.id",
				a.create_date AS "createDate",
				a.update_by AS "updateBy.id",
				a.update_date AS "updateDate",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
		FROM tutor_plan a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorPlan">
		INSERT INTO tutor_plan(
			classes,
			group_id,
			first_category,
			second_category,
			category_id,
			name,
			start_time,
			end_time,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#classes#,
			#groupId#,
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#name#,
			#startTime#,
			#endTime#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorPlan">
		UPDATE tutor_plan SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="groupId">
							group_id = #groupId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="firstCategory">
							first_category = #firstCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="startTime">
							start_time = #startTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="endTime">
							end_time = #endTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_plan where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorPlan">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.group_id AS "groupId",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.name AS "name",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_plan a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="groupId">
						a.group_id = #groupId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="categoryIdList">
					a.category_id in
					<iterate conjunction="," open="(" close=")" property="categoryIdList">
						<![CDATA[
							#categoryIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_plan a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="groupId">
						a.group_id = #groupId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorPlan">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.group_id AS "groupId",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.name AS "name",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_plan a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
