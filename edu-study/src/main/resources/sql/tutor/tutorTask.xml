<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorTask">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTask">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.plan_id AS "planId",
				a.phase_id AS "phaseId",
				a.unit_id AS "unitId",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.title AS "title",
				a.number AS "number",
				a.remark AS "remark",
				a.type AS "type",
				a.detail_id AS "detailId",
				a.is_public AS "isPublic",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.state AS "state",
				a.hours AS "hours",
				a.sort AS "sort",
				a.create_by AS "createBy.id",
				a.update_by AS "updateBy.id",
				a.create_date AS "createDate",
				a.update_date AS "updateDate",
				a.short_title AS "shortTitle",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
		FROM tutor_task a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorTask">
		INSERT INTO tutor_task(
			classes,
			plan_id,
			phase_id,
			first_category,
			second_category,
			category_id,
			title,
			remark,
			type,
			detail_id,
			is_public,
			start_time,
			end_time,
			state,
			hours,
			sort,
			create_by,
			update_by,
			create_date,
			update_date,
			short_title,
			ip,
			del_flag
		) VALUES (
			#classes#,
			#planId#,
			#phaseId#,
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#title#,
			#remark#,
			#type#,
			#detailId#,
			#isPublic#,
			#startTime#,
			#endTime#,
			#state#,
			#hours#,
			#sort#,
			#createBy#,
			#updateBy#,
			#createDate#,
			#updateDate#,
			#shortTitle#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorTask">
		UPDATE tutor_task SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planId">
							plan_id = #planId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="phaseId">
							phase_id = #phaseId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="firstCategory">
							first_category = #firstCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="title">
							title = #title#
						</isNotEmpty>
						<isNotEmpty prepend="," property="remark">
							remark = #remark#
						</isNotEmpty>
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="detailId">
							detail_id = #detailId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="isPublic">
							is_public = #isPublic#
						</isNotEmpty>
						<isNotEmpty prepend="," property="startTime">
							start_time = #startTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="endTime">
							end_time = #endTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="state">
							state = #state#
						</isNotEmpty>
						<isNotEmpty prepend="," property="hours">
							hours = #hours#
						</isNotEmpty>
						<isNotEmpty prepend="," property="sort">
							sort = #sort#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="shortTitle">
							short_title = #shortTitle#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_task where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTaskDto">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.plan_id AS "planId",
					a.unit_id AS "unitId",
					a.phase_id AS "phaseId",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.title AS "title",
					a.remark AS "remark",
					a.number AS "number",
					a.type AS "type",
					a.detail_id AS "detailId",
					a.is_public AS "isPublic",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.state AS "state",
					a.hours AS "hours",
					a.sort AS "sort",
					a.create_by AS "createBy.id",
					a.update_by AS "updateBy.id",
					a.create_date AS "createDate",
					a.update_date AS "updateDate",
					a.short_title AS "shortTitle",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_task a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="planId">
						a.plan_id = #planId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="phaseId">
						a.phase_id = #phaseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="startTime">
						<![CDATA[a.end_time >= #startTime#]]>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="endTime">
						<![CDATA[a.end_time <= #endTime#]]>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="idList">
					a.id in
					<iterate conjunction="," open="(" close=")" property="idList">
						<![CDATA[
							#idList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="unitIdList">
					a.unit_id in
					<iterate conjunction="," open="(" close=")" property="unitIdList">
						<![CDATA[
							#unitIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="categoryIdList">
					a.category_id in
					<iterate conjunction="," open="(" close=")" property="categoryIdList">
						<![CDATA[
							#categoryIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="phaseIdList">
					a.phase_id in
					<iterate conjunction="," open="(" close=")" property="phaseIdList">
						<![CDATA[
							#phaseIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_task a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="planId">
						a.plan_id = #planId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="phaseId">
						a.phase_id = #phaseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="startTime">
						a.start_time = #startTime#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="endTime">
						a.end_time = #endTime#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="offTime">
						<![CDATA[a.end_time <= #offTime#]]>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="categoryIdList">
					a.category_id in
					<iterate conjunction="," open="(" close=")" property="categoryIdList">
						<![CDATA[
							#categoryIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				and a.plan_id is not null
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTask">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.plan_id AS "planId",
					a.unit_id AS "unitId",
					a.phase_id AS "phaseId",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.title AS "title",
					a.number AS "number",
					a.remark AS "remark",
					a.type AS "type",
					a.detail_id AS "detailId",
					a.is_public AS "isPublic",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.state AS "state",
					a.hours AS "hours",
					a.sort AS "sort",
					a.create_by AS "createBy.id",
					a.update_by AS "updateBy.id",
					a.create_date AS "createDate",
					a.update_date AS "updateDate",
					a.short_title AS "shortTitle",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_task a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
