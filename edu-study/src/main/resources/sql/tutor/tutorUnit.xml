<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorUnit">

	<sql id="tutorUnitColumns">
		a.id AS "id",
		a.classes AS "classes",
		a.sort AS "sort",
		a.plan_id AS "planId",
		a.phase_id AS "phaseId",
		a.name AS "name",
		a.type AS "type",
		a.is_lock AS "isLock",
		a.open_time AS "openTime",
		a.short_name AS "shortName",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.ip AS "ip",
		a.del_flag AS "delFlag"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorUnit">
	   select
				<include refid="tutorUnitColumns" />
		FROM tutor_unit a
		WHERE a.id = #id#
	</select>

	<select id="getByWkId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorUnit">
	   select
			<include refid="tutorUnitColumns" />
		FROM
		tutor_unit_wk_class b join
		tutor_unit a on b.unit_id = a.id
		WHERE b.wk_class_id = #wkId#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorUnit">
		INSERT INTO tutor_unit(
			classes,
			sort,
			plan_id,
			phase_id,
			name,
			type,
			is_lock,
			open_time,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#classes#,
			#sort#,
			#planId#,
			#phaseId#,
			#name#,
			#type#,
			#isLock#,
			#openTime#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorUnit">
		UPDATE tutor_unit SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="sort">
							sort = #sort#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planId">
							plan_id = #planId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="phaseId">
							phase_id = #phaseId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_unit where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorUnit">
		SELECT
			<include refid="tutorUnitColumns" />,
			b.wk_class_id AS "wkClassId"
		FROM tutor_unit a left join tutor_unit_wk_class b on a.id = b.unit_id
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="delFlag">
						a.del_flag = #delFlag#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="planId">
						a.plan_id = #planId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="phaseId">
						a.phase_id = #phaseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="idList">
					a.id in
					<iterate conjunction="," open="(" close=")" property="idList">
						<![CDATA[
							#idList[]#
						]]>
					</iterate>
				</isNotEmpty>
				AND a.del_flag = '0'
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
			<isNotPropertyAvailable property="orderBy">
				a.sort asc
			</isNotPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="listUnitTaskNum" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.CountModel">
		select a.unit_id as id,
		count(1) as num
		from tutor_task a
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="unitIdList">
				a.unit_id in
				<iterate conjunction="," open="(" close=")" property="unitIdList">
					<![CDATA[
							#unitIdList[]#
						]]>
				</iterate>
			</isNotEmpty>
		</dynamic>
		group by a.unit_id
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_unit a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="delFlag">
						a.del_flag = #delFlag#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="planId">
						a.plan_id = #planId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="phaseId">
						a.phase_id = #phaseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorUnit">
		SELECT
			<include refid="tutorUnitColumns" />
		FROM tutor_unit a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
