<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorStudentKnowledge">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentKnowledge">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.knowledge_id AS "knowledgeId",
				a.uid AS "uid",
				a.question_id AS "questionId",
				a.log_id AS "logId",
				a.status AS "status",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.create_date AS "createDate"
		FROM tutor_student_knowledge a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentKnowledge">
		INSERT INTO tutor_student_knowledge(
			classes,
			knowledge_id,
			uid,
			question_id,
			log_id,
			status,
			category_id,
			create_date
		) VALUES (
			#classes#,
			#knowledgeId#,
			#uid#,
			#questionId#,
			#logId#,
			#status#,
			#categoryId#,
			#createDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentKnowledge">
		UPDATE tutor_student_knowledge SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="knowledgeId">
							knowledge_id = #knowledgeId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="questionId">
							question_id = #questionId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="logId">
							log_id = #logId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="status">
							status = #status#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_student_knowledge where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentKnowledge">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.knowledge_id AS "knowledgeId",
					a.uid AS "uid",
					a.question_id AS "questionId",
					a.log_id AS "logId",
					a.status AS "status",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.create_date AS "createDate"
		    FROM tutor_student_knowledge a
            <dynamic>
                <isNotEmpty property="uid">
                    force index(uid_log_question_idx)
                </isNotEmpty>
            </dynamic>
			<dynamic prepend="where">
				<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="firstCategory">
						a.first_category = #firstCategory#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="knowledgeId">
						a.knowledge_id = #knowledgeId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="knowledgeIdList">
						a.knowledge_id in
					<iterate conjunction="," open="(" close=")" property="knowledgeIdList">
						<![CDATA[
							#knowledgeIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_student_knowledge a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentKnowledge">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.knowledge_id AS "knowledgeId",
					a.uid AS "uid",
					a.question_id AS "questionId",
					a.log_id AS "logId",
					a.status AS "status",
					a.category_id AS "categoryId",
					a.create_date AS "createDate"
		FROM tutor_student_knowledge a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
