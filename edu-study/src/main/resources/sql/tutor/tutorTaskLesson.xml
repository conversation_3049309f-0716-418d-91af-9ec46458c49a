<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorTaskLesson">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTaskLesson">
	   select
				a.id AS "id",
				a.course_id AS "courseId",
				a.classes_id AS "classesId",
				a.lesson_id AS "lessonId",
				a.is_send AS "isSend",
				a.bak1 AS "bak1",
				a.bak2 AS "bak2"
		FROM tutor_task_lesson a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorTaskLesson">
		INSERT INTO tutor_task_lesson(
			course_id,
			classes_id,
			lesson_id,
			is_send,
			bak1,
			bak2
		) VALUES (
			#courseId#,
			#classesId#,
			#lessonId#,
			#isSend#,
			#bak1#,
			#bak2#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorTaskLesson">
		UPDATE tutor_task_lesson SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="courseId">
							course_id = #courseId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="classesId">
							classes_id = #classesId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="lessonId">
							lesson_id = #lessonId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="isSend">
							is_send = #isSend#
						</isNotEmpty>
						<isNotEmpty prepend="," property="bak1">
							bak1 = #bak1#
						</isNotEmpty>
						<isNotEmpty prepend="," property="bak2">
							bak2 = #bak2#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_task_lesson where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTaskLesson">
		SELECT
								a.id AS "id",
					a.course_id AS "courseId",
					a.classes_id AS "classesId",
					a.lesson_id AS "lessonId",
					a.is_send AS "isSend",
					a.bak1 AS "bak1",
					a.bak2 AS "bak2"
		FROM tutor_task_lesson a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="courseId">
						a.course_id = #courseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classesId">
						a.classes_id = #classesId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonId">
						a.lesson_id = #lessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="isSend">
						a.is_send = #isSend#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="taskIdList">
						a.id in
					<iterate conjunction="," open="(" close=")" property="taskIdList">
						<![CDATA[
							#taskIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findByClassesAndLessonId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTaskLesson">
		SELECT
		a.id AS "id",
		a.course_id AS "courseId",
		a.classes_id AS "classesId",
		a.lesson_id AS "lessonId",
		a.is_send AS "isSend",
		a.bak1 AS "bak1",
		a.bak2 AS "bak2"
		FROM tutor_task_lesson a
		inner join tutor_task b on a.id = b.id
		where a.lesson_id = #lessonId# and b.classes = #classes#
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_task_lesson a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="courseId">
						a.course_id = #courseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classesId">
						a.classes_id = #classesId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonId">
						a.lesson_id = #lessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="isSend">
						a.is_send = #isSend#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorTaskLesson">
		SELECT
								a.id AS "id",
					a.course_id AS "courseId",
					a.classes_id AS "classesId",
					a.lesson_id AS "lessonId",
					a.is_send AS "isSend",
					a.bak1 AS "bak1",
					a.bak2 AS "bak2"
		FROM tutor_task_lesson a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
