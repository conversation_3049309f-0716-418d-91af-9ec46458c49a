<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorStudentAnswer">

	<sql id="tutorStudentAnswerColumns">
		a.id AS "id",
		a.classes AS "classes",
		a.task_id AS "taskId",
		a.group_id AS "groupId",
		a.plane_id AS "planeId",
		a.phase_id AS "phaseId",
		a.unit_id AS "unitId",
		a.first_category AS "firstCategory",
		a.second_category AS "secondCategory",
		a.category_id AS "categoryId",
		a.uid AS "uid",
		a.type AS "type",
		a.source AS "source",
		a.study_duration AS "studyDuration",
		a.obj_type AS "objType",
		a.obj_id AS "objId",
		a.parent_obj_id AS "parentObjId",
		a.answer_num AS "answerNum",
		a.wrong_num AS "wrongNum",
		a.create_date AS "createDate",
		a.plat_form AS "platForm",
		a.appid AS "appid"
	</sql>
    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswer">
	   select
			<include refid="tutorStudentAnswerColumns" />
		FROM tutor_student_answer a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswer">
		INSERT INTO tutor_student_answer(
			classes,
			task_id,
			group_id,
			plane_id,
			phase_id,
			unit_id,
			first_category,
			second_category,
			category_id,
			uid,
			type,
			source,
			study_duration,
			obj_type,
			obj_id,
			parent_obj_id,
			answer_num,
			wrong_num,
			create_date,
			plat_form,
			appid
		) VALUES (
			#classes#,
			#taskId#,
			#groupId#,
			#planeId#,
			#phaseId#,
			#unitId#,
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#uid#,
			#type#,
			#source#,
			#studyDuration#,
			#objType#,
			#objId#,
			#parentObjId#,
			#answerNum#,
			#wrongNum#,
			#createDate#,
			#platForm#,
			#appid#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswer">
		UPDATE tutor_student_answer SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="taskId">
							task_id = #taskId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="groupId">
							group_id = #groupId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planeId">
							plane_id = #planeId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="phaseId">
							phase_id = #phaseId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="unitId">
							unit_id = #uinitId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="firstCategory">
							first_category = #firstCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="studyDuration">
							study_duration = #studyDuration#
						</isNotEmpty>
						<isNotEmpty prepend="," property="objType">
							obj_type = #objType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="objId">
							obj_id = #objId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="parentObjId">
							parent_obj_id = #parentObjId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="answerNum">
							answer_num = #answerNum#
						</isNotEmpty>
						<isNotEmpty prepend="," property="wrongNum">
							wrong_num = #wrongNum#
						</isNotEmpty>
						<isNotEmpty prepend="," property="platForm">
							plat_form = #platForm#
						</isNotEmpty>
						<isNotEmpty prepend="," property="appid">
							appid = #appid#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_student_answer where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswer">
		SELECT
			<include refid="tutorStudentAnswerColumns" />
		FROM tutor_student_answer a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="taskId">
						a.task_id = #taskId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="source">
						a.source = #source#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="groupId">
						a.group_id = #groupId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="platForm">
					a.plat_form = #platForm#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="appid">
					a.appid = #appid#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_student_answer a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="taskId">
						a.task_id = #taskId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="source">
					a.source = #source#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="groupId">
						a.group_id = #groupId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="platForm">
					a.plat_form = #platForm#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="appid">
					a.appid = #appid#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswer">
		SELECT
			<include refid="tutorStudentAnswerColumns" />
		FROM tutor_student_answer a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>
	<select id="getAnswerNumberByUidAndTaskIdList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswer">
		SELECT
		    a.task_id AS "taskId" ,
			a.obj_type AS "objType",
			a.obj_id AS "objId",
		    max(a.answer_num) AS "answerNum"
		FROM tutor_student_answer a
		WHERE a.uid=#uid#
		    and a.task_id in
		    <iterate conjunction="," open="(" close=")" property="taskIdList">
				<![CDATA[ #taskIdList[]# ]]>
			</iterate>
		GROUP BY a.task_id,a.obj_type,a.obj_id
	</select>

	<select id="findByUidAndTaskIdList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswer">
		SELECT
		<include refid="tutorStudentAnswerColumns"/>
		FROM tutor_student_answer a
		WHERE a.uid=#uid#
		and a.task_id in
		<iterate conjunction="," open="(" close=")" property="taskIdList">
			<![CDATA[ #taskIdList[]# ]]>
		</iterate>
	</select>
</sqlMap>
