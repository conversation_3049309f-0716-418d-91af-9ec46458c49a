<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorStudentTask">

	<sql id="tutorStudentTaskColumns">
		a.id AS "id",
		a.uid AS "uid",
		a.status AS "status",
		a.task_id AS "taskId",
		a.plan_id AS "planId",
		a.phase_id AS "phaseId",
		a.unit_id AS "unitId",
		a.type AS "type",
		a.score AS "score",
		a.total AS "total",
		a.source AS "source",
		a.q_complete_flag AS "qCompleteFlag",
		a.create_by AS "createBy",
		a.create_date AS "createDate",
		a.update_by AS "updateBy",
		a.update_date AS "updateDate",
		a.ip AS "ip",
		a.del_flag AS "delFlag"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentTask">
	   select
				<include refid="tutorStudentTaskColumns" />
		FROM tutor_student_task a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentTask">
		INSERT INTO tutor_student_task(
			uid,
			status,
			task_id,
			phase_id,
			plan_id,
			unit_id,
			type,
			score,
			total,
			source,
			q_complete_flag,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#uid#,
			#status#,
			#taskId#,
			#phaseId#,
			#planId#,
			#unitId#,
			#type#,
			#score#,
			#total#,
			#source#,
			#qCompleteFlag#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentTask">
		UPDATE tutor_student_task SET update_date = current_timestamp
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="status">
							status = #status#
						</isNotEmpty>
						<isNotEmpty prepend="," property="taskId">
							task_id = #taskId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="phaseId">
							phase_id = #phaseId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planId">
							plan_id = #planId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="unitId">
							unit_id = #unitId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="score">
							score = #score#
						</isNotEmpty>
						<isNotEmpty prepend="," property="total">
							total = #total#
						</isNotEmpty>
						<isNotEmpty prepend="," property="qCompleteFlag">
							q_complete_flag = #qCompleteFlag#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<update id="updateByUidAndTaskId" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentTask">
		UPDATE tutor_student_task set update_date = current_timestamp
		<dynamic prepend=",">
			<isNotEmpty prepend="," property="status">
				status = #status#
			</isNotEmpty>
			<isNotEmpty prepend="," property="qCompleteFlag">
				q_complete_flag = #qCompleteFlag#
			</isNotEmpty>
		</dynamic>
		WHERE uid = #uid# and task_id = #taskId# and source = #source# and <![CDATA[status <= #status#]]>
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_student_task where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentTask">
		SELECT
			<include refid="tutorStudentTaskColumns" />
		FROM tutor_student_task a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="taskId">
						a.task_id = #taskId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="phaseId">
						a.phase_id = #phaseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="source">
						a.source = #source#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="unitId">
						a.unit_id = #unitId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="taskIdList">
					a.task_id in
					<iterate conjunction="," open="(" close=")" property="taskIdList">
						<![CDATA[
							#taskIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="uidList">
					a.uid in
					<iterate conjunction="," open="(" close=")" property="uidList">
						<![CDATA[
							#uidList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="listUnitTaskNum" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.CountModel">
		select a.unit_id as id,
		count(1) as num
		from tutor_student_task a
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="unitIdList">
				a.unit_id in
				<iterate conjunction="," open="(" close=")" property="unitIdList">
					<![CDATA[
							#unitIdList[]#
						]]>
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="status">
				a.status = #status#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="source">
				a.source = #source#
			</isNotEmpty>
		</dynamic>
		group by a.unit_id
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_student_task a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="taskId">
						a.task_id = #taskId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentTask">
		SELECT
			<include refid="tutorStudentTaskColumns" />
		FROM tutor_student_task a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="qryLimitOneByTidAndUid" parameterClass="java.util.Map"
            resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentTask">
        SELECT
			<include refid="tutorStudentTaskColumns" />
		FROM tutor_student_task a
		WHERE a.task_id=#tid# and a.uid=#uid# and a.source = #source# limit 1
    </select>

	<select id="getLastTask" parameterClass="java.util.Map"
            resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentTask">
        SELECT
		<include refid="tutorStudentTaskColumns" />,
			b.id AS "plan.id",
			b.classes AS "plan.classes",
			b.group_id AS "plan.groupId",
			b.first_category AS "plan.firstCategory",
			b.second_category AS "plan.secondCategory",
			b.category_id AS "plan.categoryId",
			b.name AS "plan.name",
			b.start_time AS "plan.startTime",
			b.end_time AS "plan.endTime",
			b.create_by AS "plan.createBy.id",
			b.create_date AS "plan.createDate",
			b.update_by AS "plan.updateBy.id",
			b.update_date AS "plan.updateDate",
			b.ip AS "plan.ip",
			b.del_flag AS "plan.delFlag"
		FROM tutor_student_task a join tutor_plan b on a.plan_id = b.id

		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="unitId">
				a.unit_id = #unitId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="phaseId">
				a.phase_id = #phaseId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="categoryId">
				b.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="source">
				a.source = #source#
			</isNotEmpty>
		</dynamic>
		order by a.update_date desc
		limit 1
    </select>

</sqlMap>
