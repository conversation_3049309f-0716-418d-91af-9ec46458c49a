<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorStudentAnswerDetail">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.log_id AS "logId",
				a.question_id AS "questionId",
				a.uid AS "uid",
				a.result AS "result",
				a.create_date AS "createDate"
		FROM tutor_student_answer_detail a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail">
		INSERT INTO tutor_student_answer_detail(
			classes,
			log_id,
			question_id,
			uid,
			result,
			create_date
		) VALUES (
			#classes#,
			#logId#,
			#questionId#,
			#uid#,
			#result#,
			#createDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail">
		UPDATE tutor_student_answer_detail SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="logId">
							log_id = #logId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="questionId">
							question_id = #questionId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="result">
							result = #result#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_student_answer_detail where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.log_id AS "logId",
					a.question_id AS "questionId",
					a.uid AS "uid",
					a.result AS "result",
					a.create_date AS "createDate"
		FROM tutor_student_answer_detail a
		<dynamic prepend="where">
				<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="logId">
						a.log_id = #logId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="result">
						a.result = #result#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="resultList">
					a.result in
					<iterate conjunction="," open="(" close=")" property="resultList">
						<![CDATA[
							#resultList[]#
						]]>
					</iterate>
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_student_answer_detail a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="id">
				a.id = #id#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="logId">
				a.log_id = #logId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="result">
				a.result = #result#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="resultList">
				a.result in
				<iterate conjunction="," open="(" close=")" property="resultList">
					<![CDATA[
							#resultList[]#
						]]>
				</iterate>
			</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.log_id AS "logId",
					a.question_id AS "questionId",
					a.uid AS "uid",
					a.result AS "result",
					a.create_date AS "createDate"
		FROM tutor_student_answer_detail a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>
	<select id="getDoneQuestionIdByUidAndTaskIdList" parameterClass="java.util.Map" resultClass="java.lang.Long">
		SELECT b.question_id
		FROM tutor_student_answer a
		INNER JOIN tutor_student_answer_detail b ON b.log_id=a.id
		WHERE b.uid=#uid#
			and a.task_id in
			<iterate conjunction="," open="(" close=")" property="taskIdList">
				<![CDATA[ #taskIdList[]# ]]>
			</iterate>

	</select>
</sqlMap>
