<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorStudentTaskCount">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentTaskCount">
	   select
				a.id AS "id",
				a.classes AS "classes",
				a.uid AS "uid",
				a.plan_id AS "planId",
				a.phase_id AS "phaseId",
				a.unit_id AS "unitId",
				a.count AS "count",
				a.complete AS "complete",
				a.uncomplete AS "uncomplete",
				a.create_by AS "createBy.id",
				a.create_date AS "createDate",
				a.update_by AS "updateBy.id",
				a.update_date AS "updateDate",
				a.ip AS "ip",
				a.del_flag AS "delFlag"
		FROM tutor_student_task_count a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentTaskCount">
		INSERT INTO tutor_student_task_count(
			classes,
			uid,
			plan_id,
			phase_id,
			unit_id,
			count,
			complete,
			uncomplete,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#classes#,
			#uid#,
			#planId#,
			#phaseId#,
			#unitId#,
			#count#,
			#complete#,
			#uncomplete#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentTaskCount">
		UPDATE tutor_student_task_count SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="classes">
							classes = #classes#
						</isNotEmpty>
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="planId">
							plan_id = #planId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="phaseId">
							phase_id = #phaseId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="unitId">
							unit_id = #unitId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="count">
							count = #count#
						</isNotEmpty>
						<isNotEmpty prepend="," property="complete">
							complete = #complete#
						</isNotEmpty>
						<isNotEmpty prepend="," property="uncomplete">
							uncomplete = #uncomplete#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<update id="increaseCompleteCount" parameterClass="cn.huanju.edu100.study.model.tutor.TutorStudentTaskCount">
		UPDATE tutor_student_task_count SET complete = complete+1, uncomplete = uncomplete-1
		WHERE uid = #uid# and classes = #classes# and plan_id = #planId# and phase_id = #phaseId#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_student_task_count where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentTaskCount">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.uid AS "uid",
					a.plan_id AS "planId",
					a.phase_id AS "phaseId",
					a.unit_id AS "unitId",
					a.count AS "count",
					a.complete AS "complete",
					a.uncomplete AS "uncomplete",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_student_task_count a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="phaseId">
						a.phase_id = #phaseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="complete">
						a.complete = #complete#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uncomplete">
						a.uncomplete = #uncomplete#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="phaseIdList">
					phase_id in
				<iterate conjunction="," open="(" close=")" property="phaseIdList">
					#phaseIdList[]#
				</iterate>
			</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_student_task_count a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="classes">
						a.classes = #classes#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="phaseId">
						a.phase_id = #phaseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="complete">
						<![CDATA[a.complete < #complete#]]>
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uncomplete">
						<![CDATA[a.uncomplete < #uncomplete#]]>
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorStudentTaskCount">
		SELECT
								a.id AS "id",
					a.classes AS "classes",
					a.uid AS "uid",
					a.plan_id AS "planId",
					a.phase_id AS "phaseId",
					a.unit_id AS "unitId",
					a.count AS "count",
					a.complete AS "complete",
					a.uncomplete AS "uncomplete",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate",
					a.ip AS "ip",
					a.del_flag AS "delFlag"
		FROM tutor_student_task_count a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
