<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorStuKnowledgeFavorite">

    <typeAlias alias="TutorStuKnowledgeFavorite" type="cn.huanju.edu100.study.model.tutor.TutorStuKnowledgeFavorite" />

	<sql id="tutorStuKnowledgeFavoriteColumns">
			a.id AS "id",
			a.uid AS "uid",
			a.weike_id AS "weikeId",
			a.knowledge_id AS "knowledgeId",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate",
			a.ip AS "ip",
			a.del_flag AS "delFlag"
	</sql>

	<sql id="tutorSectionTaskColumns">
		distinct(a.id) AS "id",
		a.weike_id AS "weikeId",
		a.chapter_id AS "chapterId",
		a.section_id AS "sectionId",
		a.knowledge_id AS "knowledgeId",
		a.type AS "type",
		a.obj_id AS "objId",
		a.create_by AS "createBy",
		a.create_date AS "createDate",
		a.update_by AS "updateBy",
		a.update_date AS "updateDate",
		a.ip AS "ip"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="TutorStuKnowledgeFavorite">
	   select
	    	<include refid="tutorStuKnowledgeFavoriteColumns"/>
		FROM tutor_stu_knowledge_favorite a
		WHERE a.id = #id#
	</select>

	<select id="getByUidAndKIds" parameterClass="java.util.Map" resultClass="TutorStuKnowledgeFavorite">
	   select
	    	<include refid="tutorStuKnowledgeFavoriteColumns"/>
		FROM tutor_stu_knowledge_favorite a
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="weikeId">
				a.weike_id = #weikeId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="knowledgeId">
				a.knowledge_id = #knowledgeId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="knowledgeIds">
				a.knowledge_id in
				<iterate conjunction="," open="(" close=")" property="knowledgeIds">
					<![CDATA[
							#knowledgeIds[]#
						]]>
				</iterate>
			</isNotEmpty>
		</dynamic>
	</select>

	<select id="listCollectWkTask" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorSectionTask">
		select
		<include refid="tutorSectionTaskColumns" />
		from tutor_stu_knowledge_favorite b join tutor_section_task a
		on b.weike_id = a.weike_id and b.knowledge_id = a.knowledge_id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="type">
					a.type = #type#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="uid">
					b.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="weikeId">
					b.weike_id = #weikeId#
			</isNotEmpty>
			and b.del_flag = '0'
		</dynamic>
	</select>

	<insert id="insert" parameterClass="TutorStuKnowledgeFavorite">
		INSERT INTO tutor_stu_knowledge_favorite(
			uid,
			weike_id,
			knowledge_id,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES (
			#uid#,
			#weikeId#,
			#knowledgeId#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#,
			#delFlag#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<insert id="insertBatch" parameterClass="java.util.List">
		INSERT INTO tutor_stu_knowledge_favorite(
			uid,
			weike_id,
			knowledge_id,
			create_by,
			create_date,
			update_by,
			update_date,
			ip,
			del_flag
		) VALUES
		<iterate conjunction =",">
		(
			#list[].uid#,
			#list[].weikeId#,
			#list[].knowledgeId#,
			#list[].createBy#,
			#list[].createDate#,
			#list[].updateBy#,
			#list[].updateDate#,
			#list[].ip#,
			#list[].delFlag#
		)
		</iterate>
	</insert>

	<update id="update" parameterClass="TutorStuKnowledgeFavorite">
		UPDATE tutor_stu_knowledge_favorite SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="weikeId">
							weike_id = #weikeId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="knowledgeId">
							knowledge_id = #knowledgeId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_stu_knowledge_favorite where id=#id#
	</delete>

	<delete id="updateStatus" parameterClass="java.util.Map">
		update tutor_stu_knowledge_favorite
		set update_date = now(),
		del_flag = #delFlag#
		where id = #id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="TutorStuKnowledgeFavorite">
		SELECT
	   		<include refid="tutorStuKnowledgeFavoriteColumns"/>
		FROM tutor_stu_knowledge_favorite a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="weikeId">
						a.weike_id = #weikeId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="knowledgeId">
						a.knowledge_id = #knowledgeId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="knowIdList">
					a.knowledge_id in
					<iterate conjunction="," open="(" close=")" property="knowIdList">
						<![CDATA[
							#knowIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				AND a.del_flag = '0'
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM tutor_stu_knowledge_favorite a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="weikeId">
						a.weike_id = #weikeId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="knowledgeId">
						a.knowledge_id = #knowledgeId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="TutorStuKnowledgeFavorite">
		SELECT
	   		<include refid="tutorStuKnowledgeFavoriteColumns"/>
		FROM tutor_stu_knowledge_favorite a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
