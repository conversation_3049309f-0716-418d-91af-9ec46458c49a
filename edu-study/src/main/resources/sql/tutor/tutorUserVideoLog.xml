<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorUserVideoLog">

	<sql id="columns">
		a.id AS "id",
		a.uid AS "uid",
		a.course_id AS "courseId",
		a.cls_id AS "clsId",
		a.lesson_id AS "lessonId",
		a.status AS "status",
		a.last_time AS "lastTime",
		a.position AS "position",
		a.result AS "result",
		a.classes AS "classes",
		a.ip AS "ip",
		a.plat_form AS "platForm",
		a.appid AS "appid",
		a.start_position AS "startPosition"
	</sql>
    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorUserVideoLog">
	   select
			<include refid="columns"/>
		FROM tutor_user_video_log a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.tutor.TutorUserVideoLog">
		INSERT INTO tutor_user_video_log(
			uid,
			course_id,
			cls_id,
			lesson_id,
			status,
			last_time,
			position,
			result,
			classes,
			ip,
		    plat_form,
		    appid,
		    start_position
		) VALUES (
			#uid#,
			#courseId#,
			#clsId#,
			#lessonId#,
			#status#,
			#lastTime#,
			#position#,
			#result#,
			#classes#,
			#ip#,
			#platForm#,
			#appid#,
			#startPosition#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>


	<insert id="insertSharding" parameterClass="cn.huanju.edu100.study.model.tutor.TutorUserVideoLog">
		INSERT INTO tutor_user_video_log(
		id,
		uid,
		course_id,
		cls_id,
		lesson_id,
		status,
		last_time,
		position,
		result,
		classes,
		ip,
		plat_form,
		appid,
		start_position
		) VALUES (
		#id#,
		#uid#,
		#courseId#,
		#clsId#,
		#lessonId#,
		#status#,
		#lastTime#,
		#position#,
		#result#,
		#classes#,
		#ip#,
		#platForm#,
		#appid#,
		#startPosition#
		)
		<!--<selectKey resultClass="long" type="post" keyProperty="id" >-->
		<!--select LAST_INSERT_ID() as value-->
		<!--</selectKey>-->
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.tutor.TutorUserVideoLog">
		UPDATE tutor_user_video_log
			<dynamic prepend="set">
				<isNotEmpty prepend="," property="status">
					status = #status#
				</isNotEmpty>
				<isNotEmpty prepend="," property="lastTime">
					last_time = #lastTime#
				</isNotEmpty>
				<isNotEmpty prepend="," property="position">
					position = #position#
				</isNotEmpty>
				<isNotEmpty prepend="," property="result">
					result = #result#
				</isNotEmpty>
				<isNotEmpty prepend="," property="ip">
					ip = #ip#
				</isNotEmpty>
				<isNotEmpty prepend="," property="platForm">
					plat_form = #platForm#
				</isNotEmpty>
				<isNotEmpty prepend="," property="appid">
					appid = #appid#
				</isNotEmpty>
				<isNotEmpty prepend="," property="startPosition">
					start_position = #startPosition#
				</isNotEmpty>
			</dynamic>
		<dynamic prepend="where">
			<isNotEmpty prepend="and" property="id">
				id = #id#
			</isNotEmpty>
			<isNotEmpty prepend="and" property="uid">
				uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="and" property="lessonId">
				lesson_id = #lessonId#
			</isNotEmpty>
			<isNotEmpty prepend="and" property="classes">
				classes = #classes#
			</isNotEmpty>
		</dynamic>
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_user_video_log where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorUserVideoLog">
		SELECT
			<include refid="columns"/>
		FROM tutor_user_video_log a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="courseId">
						a.course_id = #courseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="clsId">
						a.cls_id = #clsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonId">
						a.lesson_id = #lessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="lessonIdList">
						a.lesson_id in
					<iterate conjunction="," open="(" close=")" property="lessonIdList">
						<![CDATA[
							#lessonIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="platForm">
					a.plat_form = #platForm#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="appid">
					a.appid = #appid#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="startPosition">
					a.start_position = #startPosition#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM tutor_user_video_log a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="courseId">
						a.course_id = #courseId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="clsId">
						a.cls_id = #clsId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="lessonId">
						a.lesson_id = #lessonId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="lessonIdList">
					a.lesson_id in
					<iterate conjunction="," open="(" close=")" property="lessonIdList">
						<![CDATA[
							#lessonIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="platForm">
					a.plat_form = #platForm#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="appid">
					a.appid = #appid#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="startPosition">
					a.start_position = #startPosition#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorUserVideoLog">
		SELECT
			<include refid="columns" />
		FROM tutor_user_video_log a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>
	<select id="findByTaskListAndUid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.tutor.TutorUserVideoLog">
		SELECT
            a.uid AS "uid",
            a.course_id AS "courseId",
            a.cls_id AS "clsId",
            a.lesson_id AS "lessonId",
            max(a.status) AS "status",
            sum(a.position) AS "position",
            a.classes AS "classes"

		FROM tutor_user_video_log a
		inner join tutor_task_lesson b on a.course_id=b.course_id and a.cls_id=b.classes_id and a.lesson_id=b.lesson_id
		where
			a.classes=#classes#
			and a.uid=#uid#
			and b.id in
			<iterate conjunction="," open="(" close=")" property="taskIdList">
				<![CDATA[ #taskIdList[]# ]]>
			</iterate>
        group by a.lesson_id
	</select>
</sqlMap>
