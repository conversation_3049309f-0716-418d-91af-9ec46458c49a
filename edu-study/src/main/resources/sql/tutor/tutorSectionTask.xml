<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorSectionTask">

    <typeAlias alias="TutorSectionTask" type="cn.huanju.edu100.study.model.tutor.TutorSectionTask" />

	<sql id="tutorSectionTaskColumns">
			a.id AS "id",
			a.weike_id AS "weikeId",
			a.chapter_id AS "chapterId",
			a.section_id AS "sectionId",
			a.knowledge_id AS "knowledgeId",
			a.type AS "type",
			a.obj_id AS "objId",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate",
			a.ip AS "ip"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="TutorSectionTask">
	   select
	    	<include refid="tutorSectionTaskColumns"/>
		FROM tutor_section_task a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="TutorSectionTask">
		INSERT INTO tutor_section_task(
			weike_id,
			chapter_id,
			section_id,
			knowledge_id,
			type,
			obj_id,
			create_by,
			create_date,
			update_by,
			update_date,
			ip
		) VALUES (
			#weikeId#,
			#chapterId#,
			#sectionId#,
			#knowledgeId#,
			#type#,
			#objId#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="TutorSectionTask">
		UPDATE tutor_section_task SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="weikeId">
							weike_id = #weikeId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="chapterId">
							chapter_id = #chapterId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="sectionId">
							section_id = #sectionId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="knowledgeId">
							knowledge_id = #knowledgeId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="objId">
							obj_id = #objId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_section_task where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="TutorSectionTask">
		SELECT
	   		<include refid="tutorSectionTaskColumns"/>
		FROM tutor_section_task a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="weikeId">
						a.weike_id = #weikeId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="chapterId">
						a.chapter_id = #chapterId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="sectionId">
						a.section_id = #sectionId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="idList">
					a.id in
					<iterate conjunction="," open="(" close=")" property="idList">
						<![CDATA[
							#idList[]#
						]]>
					</iterate>
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="knowledgeId">
						a.knowledge_id = #knowledgeId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="objId">
						a.obj_id = #objId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByParam" parameterClass="java.util.Map" resultClass="TutorSectionTask">
		SELECT
		<include refid="tutorSectionTaskColumns"/>,
		b.sort AS "sort"
		FROM tutor_section_task a left join tutor_section_knowledge b on a.section_id = b.section_id and a.knowledge_id = b.knowledge_id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="weikeId">
				a.weike_id = #weikeId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="chapterId">
				a.chapter_id = #chapterId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="sectionId">
				a.section_id = #sectionId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="idList">
				a.id in
				<iterate conjunction="," open="(" close=")" property="idList">
					<![CDATA[
							#idList[]#
						]]>
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="knowledgeId">
				a.knowledge_id = #knowledgeId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="type">
				a.type = #type#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="objId">
				a.obj_id = #objId#
			</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
			<isNotPropertyAvailable prepend="" property="orderBy">
				b.sort
			</isNotPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="listTaskNum" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.CountModel">
		select a.section_id as id,
		count(1) as num
		from tutor_section_task a
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="sectionIdList">
				a.section_id in
				<iterate conjunction="," open="(" close=")" property="sectionIdList">
					<![CDATA[
							#sectionIdList[]#
						]]>
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="type">
				a.type = #type#
			</isNotEmpty>
		</dynamic>
		group by a.section_id
	</select>

	<select id="listWKTaskNum" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.CountModel">
		select a.weike_id as id,
		count(1) as num
		from tutor_section_task a
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="weikeIdList">
				a.weike_id in
				<iterate conjunction="," open="(" close=")" property="weikeIdList">
					<![CDATA[
							#weikeIdList[]#
						]]>
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="type">
				a.type = #type#
			</isNotEmpty>
		</dynamic>
		group by a.weike_id
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM tutor_section_task a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="weikeId">
						a.weike_id = #weikeId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="chapterId">
						a.chapter_id = #chapterId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="sectionId">
						a.section_id = #sectionId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="knowledgeId">
						a.knowledge_id = #knowledgeId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="objId">
						a.obj_id = #objId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="TutorSectionTask">
		SELECT
	   		<include refid="tutorSectionTaskColumns"/>
		FROM tutor_section_task a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
