<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TutorWkClassChapter">

    <typeAlias alias="TutorWkClassChapter" type="cn.huanju.edu100.study.model.tutor.TutorWkClassChapter" />

	<sql id="tutorWkClassChapterColumns">
			a.id AS "id",
			a.parent_id AS "parentId",
			a.parent_ids AS "parentIds",
			a.wk_class_id AS "wkClassId",
			a.name AS "name",
			a.chapter_desc AS "chapterDesc",
			a.sort AS "sort",
			a.klevel AS "klevel",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate",
			a.ip AS "ip"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="TutorWkClassChapter">
	   select
	    	<include refid="tutorWkClassChapterColumns"/>
		FROM tutor_wk_class_chapter a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="TutorWkClassChapter">
		INSERT INTO tutor_wk_class_chapter(
			parent_id,
			parent_ids,
			wk_class_id,
			name,
			chapter_desc,
			sort,
			klevel,
			create_by,
			create_date,
			update_by,
			update_date,
			ip
		) VALUES (
			#parentId#,
			#parentIds#,
			#wkClassId#,
			#name#,
			#chapterDesc#,
			#sort#,
			#klevel#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#ip#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="TutorWkClassChapter">
		UPDATE tutor_wk_class_chapter SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="parentId">
							parent_id = #parentId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="parentIds">
							parent_ids = #parentIds#
						</isNotEmpty>
						<isNotEmpty prepend="," property="wkClassId">
							wk_class_id = #wkClassId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="chapterDesc">
							chapter_desc = #chapterDesc#
						</isNotEmpty>
						<isNotEmpty prepend="," property="sort">
							sort = #sort#
						</isNotEmpty>
						<isNotEmpty prepend="," property="klevel">
							klevel = #klevel#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM tutor_wk_class_chapter where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="TutorWkClassChapter">
		SELECT
	   		<include refid="tutorWkClassChapterColumns"/>
		FROM tutor_wk_class_chapter a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="parentId">
						a.parent_id = #parentId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="wkClassId">
						a.wk_class_id = #wkClassId# and parent_id = 0
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="parentIdList">
					a.parent_id in
					<iterate conjunction="," open="(" close=")" property="parentIdList">
						<![CDATA[
							#parentIdList[]#
						]]>
					</iterate>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="idList">
					a.id in
					<iterate conjunction="," open="(" close=")" property="idList">
						<![CDATA[
							#idList[]#
						]]>
					</iterate>
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="parentIds">
						a.parent_ids = #parentIds#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE concat('%',#name#,'%')
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM tutor_wk_class_chapter a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="parentId">
						a.parent_id = #parentId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="parentIds">
						a.parent_ids = #parentIds#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="name">
						a.name LIKE CONCAT('%',#name#,'%')
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="TutorWkClassChapter">
		SELECT
	   		<include refid="tutorWkClassChapterColumns"/>
		FROM tutor_wk_class_chapter a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
