<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="HomeworkSyncdataResid">
    <sql id="homeworkSyncdataResidColumns">
        a.id AS "id",
        a.res_id AS "resId",
        a.type AS "type",
        a.create_date AS "createDate",
        a.update_date AS "updateDate"
    </sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.HomeworkSyncdataResid">
	    select
        <include refid="homeworkSyncdataResidColumns"/>
        FROM homework_syncdata_resid a
		WHERE a.id = #id#
	</select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.HomeworkSyncdataResid">
        INSERT INTO homework_syncdata_resid(
        res_id,
        type,
        create_date,
        update_date
        ) VALUES (
        #resId#,
        #type#,
        #createDate#,
        #updateDate#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.HomeworkSyncdataResid">
        SELECT
        <include refid="homeworkSyncdataResidColumns"/>
        FROM homework_syncdata_resid a where 1 = 1
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="resId">
                a.res_id = #resId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="type">
                a.type = #type#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT COUNT(1)
        FROM homework_syncdata_resid a where 1 = 1
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="resId">
                a.res_id = #resId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="type">
                a.type = #type#
            </isNotEmpty>
        </dynamic>
    </select>

    <update id="update" parameterClass="cn.huanju.edu100.study.model.HomeworkSyncdataResid">
        update homework_syncdata_resid set update_date=now()
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="resId">
                res_id = #resId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="type">
                type = #type#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id#
    </update>

    <select id="getDifferenceResid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.HomeworkSyncdataResid">
        SELECT <include refid="homeworkSyncdataResidColumns"/>
        FROM homework_syncdata_resid a
        where a.type=#type#
        and a.res_id not in(SELECT b.res_id FROM homework_syncdata_result b where b.type=0 and b.product_type=#type#)
    </select>

</sqlMap>
