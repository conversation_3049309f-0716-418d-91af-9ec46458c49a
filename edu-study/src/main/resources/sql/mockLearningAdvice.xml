<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="MockLearningAdvice">

	<sql id="columns">
		a.id AS "id",
		a.mock_exam_id AS "mockExamId",
		a.mock_subject_id AS "mockSubjectId",
		a.low_score AS "lowScore",
		a.hight_score AS "hightScore",
		a.advice AS "advice",
		a.goods_group_id AS "goodsGroupId",
		a.goods_group_name AS "goodsGroupName",
		a.create_by AS "createBy",
		a.update_by AS "updateBy",
		a.create_date AS "createDate",
		a.update_date AS "updateDate"
	</sql>

    <select id="findListByExamIdAndSubjectId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.TikuMockLearningAdvice">
        select
        <include refid="columns" />
        FROM tiku_mock_learning_advice a
        WHERE  mock_exam_id=#mockExamId# and mock_subject_id = #mockSubjectId#
    </select>

</sqlMap>
