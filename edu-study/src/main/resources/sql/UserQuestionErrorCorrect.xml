<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserQuestionErrorCorrect">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionErrorCorrect">
	   select
			a.id AS "id",
			a.uid AS "uid",
			a.first_category AS "firstCategory",
			a.second_category AS "secondCategory",
			a.category_id AS "categoryId",
			a.question_id AS "questionId",
			a.source AS "source",
			a.source_id AS "sourceId",
			a.error_types AS "errorTypes",
			a.error_detail AS "errorDetail",
			a.state AS "state",
			a.teacher_id AS "teacherId",
			a.teacher_name AS "teacherName",
			a.create_date AS "createDate",
			a.update_date AS "updateDate"
		FROM user_question_error_correct a
		WHERE a.id = #id#
	</select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.UserQuestionErrorCorrect">
        INSERT INTO user_question_error_correct(
			uid,
			first_category ,
			second_category,
			category_id,
			question_id,
			source,
			source_id,
			error_types,
			error_detail,
			state,
			create_date,
			update_date
		) VALUES (
			#uid#,
			#firstCategory#,
	        #secondCategory#,
	        #categoryId#,
			#questionId#,
			#source#,
			#sourceId#,
			#errorTypes#,
			#errorDetail#,
			0,
			now(),
			now()
		)
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <update id="update" parameterClass="cn.huanju.edu100.study.model.UserQuestionErrorCorrect">
        UPDATE user_question_error_correct SET update_date=now()
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="errorTypes">
                error_types = #errorTypes#
            </isNotEmpty>
            <isNotEmpty prepend="," property="errorDetail">
                error_detail = #errorDetail#
            </isNotEmpty>
            <isNotEmpty prepend="," property="state">
                state = #state#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id#
    </update>

    <delete id="delete" parameterClass="java.util.Map">
        DELETE FROM user_question_error_correct where id=#id#
    </delete>

    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionErrorCorrect">
        select
			a.id AS "id",
			a.uid AS "uid",
			a.first_category AS "firstCategory",
			a.second_category AS "secondCategory",
			a.category_id AS "categoryId",
			a.question_id AS "questionId",
			a.source AS "source",
			a.source_id AS "sourceId",
			a.error_types AS "errorTypes",
			a.error_detail AS "errorDetail",
			a.state AS "state",
			a.teacher_id AS "teacherId",
			a.teacher_name AS "teacherName",
			a.create_date AS "createDate",
			a.update_date AS "updateDate"
		FROM user_question_error_correct a
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="uid">
                a.uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="source">
                a.source = #source#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="errorType">
            	a.error_types LIKE concat('%',#errorType#,'%')
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>


    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT count(1)
        FROM user_question_error_correct a
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="uid">
                a.uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="source">
                a.source = #source#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="errorType">
                a.error_types LIKE concat('%',#errorType#,'%')
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionErrorCorrect">
        select
			a.id AS "id",
			a.uid AS "uid",
			a.first_category AS "firstCategory",
			a.second_category AS "secondCategory",
			a.category_id AS "categoryId",
			a.question_id AS "questionId",
			a.source AS "source",
			a.source_id AS "sourceId",
			a.error_type AS "errorType",
			a.error_detail AS "errorDetail",
			a.state AS "state",
			a.teacher_id AS "teacherId",
			a.teacher_name AS "teacherName",
			a.create_date AS "createDate",
			a.update_date AS "updateDate"
		FROM user_question_error_correct a
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
    </select>

</sqlMap>
