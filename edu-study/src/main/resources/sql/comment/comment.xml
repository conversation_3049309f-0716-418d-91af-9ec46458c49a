<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="Comment">

    <typeAlias alias="Comment" type="cn.huanju.edu100.study.model.tutor.Comment" />
    <typeAlias alias="CommentElement" type="cn.huanju.edu100.study.model.tutor.CommentElement" />

	<sql id="commentColumns">
			a.id AS "id",
			a.uid AS "uid",
			a.nick_name AS "nickName",
			a.comment_element_id AS "commentElementId",
			a.content AS "content",
			a.star AS "star",
			a.thumb_up_num AS "thumbUpNum",
			a.status AS "status",
			a.is_stick AS "isStick",
			a.create_date AS "createDate",
			a.create_by AS "createBy",
			a.update_date AS "updateDate",
			a.update_by AS "updateBy",
			a.del_flag AS "delFlag",
		    a.reply_content AS "replyContent",
			a.is_read AS "isRead",
		    a.platform AS "platform",
		    a.level AS "level"
	</sql>
	
	<sql id="commentContentFilter">
		and a.content is not null  and a.content != '用户没有填写评价内容' and content != '此用户没有填写评价' and content != ''
	</sql>
	
	<sql id="queryTypeSql">
		<isEqual prepend="AND" property="queryType" compareValue="2">
			(a.status = 2
			<include refid="commentContentFilter"/>
			<isNotEmpty prepend="OR" property="uid">
				(a.uid = #uid# and a.status != 2)
			</isNotEmpty>
			)
		</isEqual>
		<isEqual prepend="AND" property="queryType" compareValue="1">
			a.uid = #uid#
		</isEqual>
		<isEqual prepend="AND" property="queryType" compareValue="3">
			a.create_date >= curdate() and a.create_date &lt;= date_add(curdate(),interval 1 day) and a.status = 2
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
		</isEqual>
		<isEqual prepend="AND" property="queryType" compareValue="4">
			a.status = 2
			<include refid="commentContentFilter"/>
			<isNotEmpty prepend="AND" property="uid">
				a.uid != #uid#
			</isNotEmpty>
		</isEqual>
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="Comment">
	   select
	    	<include refid="commentColumns"/>
		FROM comment a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="Comment">
		INSERT INTO comment(
			uid,
			nick_name,
			comment_element_id,
			content,
			star,
			status,
			thumb_up_num,
			is_stick,
			teacher_star,
			create_date,
			create_by,
			update_date,
			update_by,
			del_flag,
			is_read,
			platform,
		    level,
			source
		) VALUES (
			#uid#,
			#nickName#,
			#commentElementId#,
			#content#,
			#star#,
			#status#,
			#thumbUpNum#,
			#isStick#,
			#teacherStar#,
			#createDate#,
			#createBy#,
			#updateDate#,
			#updateBy#,
			#delFlag#,
			#isRead#,
			#platform#,
		    #level#,
			#source#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="Comment">
		UPDATE comment SET
		update_date = NOW()
			<dynamic prepend=",">
				<isNotEmpty prepend="," property="uid">
					uid = #uid#
				</isNotEmpty>
				<isNotEmpty prepend="," property="commentElementId">
					comment_element_id = #commentElementId#
				</isNotEmpty>
				<isNotEmpty prepend="," property="content">
					content = #content#
				</isNotEmpty>
				<isNotEmpty prepend="," property="star">
					star = #star#
				</isNotEmpty>
				<isNotEmpty prepend="," property="status">
					status = #status#
				</isNotEmpty>
				<isNotEmpty prepend="," property="thumbUpNum">
					thumb_up_num = #thumbUpNum#
				</isNotEmpty>
				<isNotEmpty prepend="," property="updateDate">
					update_date = #updateDate#
				</isNotEmpty>
				<isNotEmpty prepend="," property="updateBy">
					update_by = #updateBy#
				</isNotEmpty>
				<isNotEmpty prepend="," property="isRead">
					is_read = #isRead#
				</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<update id="updateBatch" parameterClass="java.util.Map">
		UPDATE comment SET
		update_date = NOW()
		<dynamic prepend=",">
			<isNotEmpty prepend="," property="uid">
				uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="," property="commentElementId">
				comment_element_id = #commentElementId#
			</isNotEmpty>
			<isNotEmpty prepend="," property="content">
				content = #content#
			</isNotEmpty>
			<isNotEmpty prepend="," property="star">
				star = #star#
			</isNotEmpty>
			<isNotEmpty prepend="," property="status">
				status = #status#
			</isNotEmpty>
			<isNotEmpty prepend="," property="thumbUpNum">
				thumb_up_num = #thumbUpNum#
			</isNotEmpty>
			<isNotEmpty prepend="," property="updateDate">
				update_date = #updateDate#
			</isNotEmpty>
			<isNotEmpty prepend="," property="updateBy">
				update_by = #updateBy#
			</isNotEmpty>
			<isNotEmpty prepend="," property="isRead">
				is_read = #isRead#
			</isNotEmpty>
		</dynamic>
		WHERE id IN
		<iterate conjunction="," open="(" close=")" property="ids">
			#ids[]#
		</iterate>
	</update>

	<update id="delete" parameterClass="java.util.Map">
		update  comment set del_flag = '1' where id=#id#
	</update>

	<update id="inCreThumbUpNum" parameterClass="java.util.Map">
		update comment set thumb_up_num = thumb_up_num + 1 where id = #id#
	</update>

	<select id="findList" parameterClass="java.util.Map" resultClass="Comment">
		SELECT
	   		<include refid="commentColumns"/>
		FROM comment a
			<dynamic prepend="WHERE">
                <isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
                <isNotEmpty prepend="AND" property="commentElementId">
						a.comment_element_id = #commentElementId#
				</isNotEmpty>
                <isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="level">
					a.level = #level#
				</isNotEmpty>
                <isNotEmpty prepend="AND" property="isRead">
                    a.is_read = #isRead#
                </isNotEmpty>
				AND a.del_flag = '0'
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
			<isNotPropertyAvailable prepend="" property="orderBy">
				a.create_date desc
			</isNotPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<!-- comment:a comment_element:b 不要弄混-->
	<sql id="commentDetailColumns">
		a.id AS "id",
		a.uid AS "uid",
		a.nick_name AS "nickName",
		a.comment_element_id AS "commentElementId",
		a.content AS "content",
		a.star AS "star",
		a.teacher_star AS "teacherStar",
		a.thumb_up_num AS "thumbUpNum",
		a.status AS "status",
		a.is_stick AS "isStick",
		a.create_date AS "createDate",
		a.create_by AS "createBy",
		a.update_date AS "updateDate",
		a.update_by AS "updateBy",
		a.del_flag AS "delFlag",
		a.reply_content AS "replyContent",
		a.is_read AS "isRead",
		a.platform AS "platform",
		a.level AS "level",
		b.obj_id AS "objId",
		b.obj_type AS "objType",
		b.obj_name AS "objName"
	</sql>
	
	<sql id="commonQueryParams">
		<isNotEmpty prepend="AND" property="commentIdList">
			a.id in
			<iterate conjunction="," open="(" close=")" property="commentIdList">
				<![CDATA[
							#commentIdList[]#
						]]>
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="commentId">
			a.id = #commentId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="level">
			a.level = #level#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="objId">
			b.obj_id = #objId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="objIdList">
			b.obj_id IN
			<iterate conjunction="," open="(" close=")" property="objIdList">
				#objIdList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="objType">
			b.obj_type = #objType#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="objTypeList">
			b.obj_type in
			<iterate conjunction="," open="(" close=")" property="objTypeList">
				<![CDATA[
							#objTypeList[]#
						]]>
			</iterate>
		</isNotEmpty>
		<include refid="queryTypeSql"/>
		<isNotEmpty prepend="AND" property="commentElementId">
			a.comment_element_id = #commentElementId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="goodsGroupId">
			b.goods_group_id = #goodsGroupId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="status">
			a.status = #status#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="isRead">
			a.is_read = #isRead#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="startDate">
			a.create_date >= #startDate# AND a.create_date &lt;= #endDate#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="goodsIdList">
			b.goods_id IN
			<iterate conjunction="," open="(" close=")" property="goodsIdList">
				#goodsIdList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="teacherId">
			b.teacher_id = #teacherId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="schId">
			b.sch_id = #schId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="secondCategory">
			b.second_category = #secondCategory#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="categoryId">
			b.category_id = #categoryId#
		</isNotEmpty>
		AND a.del_flag = '0'
	</sql>


	<select id="findListByObj" parameterClass="java.util.Map" resultClass="Comment">
		SELECT
			<include refid="commentDetailColumns"/>
			<isNotEmpty prepend="" property="queryThumb">
			,c.id AS "commentThumbId"
			</isNotEmpty>
				FROM comment a join comment_element b on a.comment_element_id = b.id
			<isNotEmpty prepend="" property="queryThumb">
				left join comment_thumb c on a.id = c.comment_id and c.uid = a.uid
			</isNotEmpty>
			<dynamic prepend="WHERE">
				<include refid="commonQueryParams"/>
			</dynamic>
			<dynamic prepend="ORDER BY">
				<isPropertyAvailable prepend="" property="orderBy">
					$orderBy$
				</isPropertyAvailable>
				<isNotPropertyAvailable prepend="" property="orderBy">
					a.is_stick desc,a.thumb_up_num desc, a.create_date desc
				</isNotPropertyAvailable>
			</dynamic>
			<dynamic prepend="limit">
				<isPropertyAvailable prepend="" property="pageSize">
					#from#,#pageSize#
				</isPropertyAvailable>
			</dynamic>
	</select>

	<select id="findListByObjGroupByContent" parameterClass="java.util.Map" resultClass="Comment">
		SELECT
		<include refid="commentDetailColumns"/>
		<isNotEmpty prepend="" property="queryThumb">
			,c.id AS "commentThumbId"
		</isNotEmpty>
 			FROM comment a join comment_element b on a.comment_element_id = b.id
		<isNotEmpty prepend="" property="queryThumb">
			left join comment_thumb c on a.id = c.comment_id and c.uid = a.uid
		</isNotEmpty>
		<dynamic prepend="WHERE">
			<include refid="commonQueryParams"/>
		</dynamic>
		group by a.uid,a.content
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
			<isNotPropertyAvailable prepend="" property="orderBy">
				a.is_stick desc, a.create_date desc
			</isNotPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByUid" parameterClass="java.util.Map" resultClass="CommentElement">
        SELECT
            a.uid AS "uid",
            b.id AS "id",
            b.obj_id AS "objId",
            b.obj_type AS "objType"
        FROM comment a
        INNER JOIN comment_element b
            ON a.comment_element_id = b.id
        <dynamic prepend="WHERE">
            <isNotEmpty prepend="AND" property="objType">
                b.obj_type = #objType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="idList">
                b.obj_id IN
                <iterate conjunction="," open="(" close=")" property="idList">
                    #idList[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="uid">
                    a.uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="schId">
                    b.sch_id = #schId#
            </isNotEmpty>
            AND a.del_flag = '0'
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>
	<select id="queryCommentCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT
		count(*)
		FROM comment a
		<!--考试不为空的性能太差，不为空先强制走idx_comm_ele_id索引，性能提升30%，仍有优化空间-->
		<isNotEmpty property="secondCategory">
			force index (idx_comm_ele_id)
		</isNotEmpty>
		join comment_element b on a.comment_element_id = b.id
		<dynamic prepend="WHERE">
			<include refid="commonQueryParams"/>
		</dynamic>
	</select>

	<select id="queryCommentCountGroupByContent" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		select count(h.id) from
		(SELECT
		a.id
		FROM comment a join comment_element b on a.comment_element_id = b.id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="objId">
				b.obj_id = #objId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="objType">
				b.obj_type = #objType#
			</isNotEmpty>
			<isEqual prepend="AND" property="queryType" compareValue="2">
				a.status = 2
			</isEqual>
			<isEqual prepend="AND" property="queryType" compareValue="1">
				a.uid = #uid#
			</isEqual>
			<isNotEmpty prepend="AND" property="commentElementId">
				a.comment_element_id = #commentElementId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="goodsGroupId">
				b.goods_group_id = #goodsGroupId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="status">
				a.status = #status#
			</isNotEmpty>
            <isNotEmpty prepend="AND" property="isRead">
                a.is_read = #isRead#
            </isNotEmpty>
			<isNotEmpty prepend="AND" property="schId">
				b.sch_id = #schId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="goodsIdList">
				b.goods_id IN
				<iterate conjunction="," open="(" close=")" property="goodsIdList">
					#goodsIdList[]#
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="teacherId">
				b.teacher_id = #teacherId#
			</isNotEmpty>
		</dynamic>
		group by a.uid,a.content) h
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM comment a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="commentElementId">
						a.comment_element_id = #commentElementId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="status">
						a.status = #status#
				</isNotEmpty>
				AND a.del_flag = '0'
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="Comment">
		SELECT
	   		<include refid="commentColumns"/>
		FROM comment a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

	<sql id="commentElementColumns">
		a.id AS "id",
		a.uid AS "uid",
		a.comment_element_id AS "commentElementId",
		a.content AS "content",
		a.star AS "star",
		a.status AS "status",
		a.is_stick AS "isStick",
		a.create_date AS "createDate",
		a.create_by AS "createBy",
		a.update_date AS "updateDate",
		a.update_by AS "updateBy",
		a.platform AS "platform"
	</sql>
	<select id="findCommentListByIdList" parameterClass="java.util.Map" resultClass="CommentElement">
		SELECT
		<include refid="commentElementColumns"/>
		FROM comment a join comment_element b on a.comment_element_id = b.id
		left join comment_thumb c on a.id = c.comment_id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="objType">
				b.obj_type = #objType#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="idList">
				a.id IN
				<iterate conjunction="," open="(" close=")" property="idList">
					#idList[]#
				</iterate>
			</isNotEmpty>
		</dynamic>
	</select>

	<update id="minusThumbUpNum" parameterClass="java.util.Map">
		update comment set thumb_up_num = thumb_up_num -1 where id = #id#
	</update>


	<select id="findMyComments" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.dto.MyComment">
		SELECT
		<include refid="commentDetailColumns"/>
		,c.id AS "commentThumbId"
		FROM comment a join comment_element b on a.comment_element_id = b.id
			left join comment_thumb c on a.id = c.comment_id and c.uid = a.uid
		<dynamic prepend="WHERE">
			<include refid="commonQueryParams"/>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
			<isNotPropertyAvailable prepend="" property="orderBy">
				a.is_stick desc, a.create_date desc
			</isNotPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findStarByGroup" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.dto.CommentStarGroup">
		select FLOOR(star) as star,
		count(1) as count
		from comment a join comment_element b on a.comment_element_id = b.id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="objId">
				b.obj_id = #objId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="objType">
				b.obj_type = #objType#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="goodsGroupId">
				b.goods_group_id = #goodsGroupId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="status">
				a.status = #status#
			</isNotEmpty>
			and a.star is not null and a.star > 3
			group by FLOOR(a.star)
		</dynamic>
	</select>

	<select id="findListByObjGroupByMyContent" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.dto.MyComment">
		SELECT
		<include refid="commentDetailColumns"/>
		,c.id AS "commentThumbId"
		FROM comment a join comment_element b on a.comment_element_id = b.id
			left join comment_thumb c on a.id = c.comment_id and c.uid = a.uid
		<dynamic prepend="WHERE">
			<include refid="commonQueryParams"/>
		</dynamic>
		group by a.uid,a.content
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
			<isNotPropertyAvailable prepend="" property="orderBy">
				a.is_stick desc, a.create_date desc
			</isNotPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>




</sqlMap>
