<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TeacherEvaluate">

    <typeAlias alias="TeacherEvaluate" type="cn.huanju.edu100.study.model.tutor.TeacherEvaluate" />

	<sql id="commentColumns">
			a.id AS "id",
			a.comment_id AS "commentId",
			a.goods_group_id AS "goodsGroupId",
			a.star AS "star",
			a.teacher_id AS "teacherId",
			a.teacher_name AS "teacherName",
			a.create_date AS "createDate",
			a.sch_id AS "schId"
	</sql>

	<insert id="insertBatch" parameterClass="java.util.List">
		INSERT INTO teacher_evaluate (
		`comment_id`,
		`goods_group_id`,
		`star`,
		`teacher_id`,
		`teacher_name`,
		`create_date`,
		`sch_id`
		) VALUES
		<iterate conjunction=",">
			<![CDATA[
			( #insertList[].commentId#,
			  #insertList[].goodsGroupId#,
			  #insertList[].star#,
			  #insertList[].teacherId#,
			  #insertList[].teacherName#,
			  #insertList[].createDate#,
			  #insertList[].schId#)
		]]>
		</iterate>
	</insert>






</sqlMap>
