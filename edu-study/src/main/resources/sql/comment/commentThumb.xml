<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="CommentThumb">

    <typeAlias alias="CommentThumb" type="cn.huanju.edu100.study.model.tutor.CommentThumb" />

	<sql id="commentThumbColumns">
			a.id AS "id",
			a.uid AS "uid",
			a.comment_id AS "commentId",
			a.ip AS "ip",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="CommentThumb">
	   select
	    	<include refid="commentThumbColumns"/>
		FROM comment_thumb a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="CommentThumb">
		INSERT INTO comment_thumb(
			uid,
			comment_id,
			ip,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#uid#,
			#commentId#,
			#ip#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="CommentThumb">
		UPDATE comment_thumb SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="commentId">
							comment_id = #commentId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="ip">
							ip = #ip#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM comment_thumb where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="CommentThumb">
		SELECT
	   		<include refid="commentThumbColumns"/>
		FROM comment_thumb a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="commentId">
						a.comment_id = #commentId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findUserThumbUpList" parameterClass="java.util.Map" resultClass="CommentThumb">
		SELECT
		<include refid="commentThumbColumns"/>
		FROM comment_thumb a
		<dynamic prepend="WHERE">
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="commentIdList">
				a.comment_id in
				<iterate conjunction="," open="(" close=")" property="commentIdList">
					<![CDATA[
							#commentIdList[]#
						]]>
				</iterate>
			</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM comment_thumb a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="commentId">
						a.comment_id = #commentId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="CommentThumb">
		SELECT
	   		<include refid="commentThumbColumns"/>
		FROM comment_thumb a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
