<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserSubErrorQuestion">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
	   select
				a.id AS "id",
				a.uid AS "uid",
				a.product_id AS "productId",
				a.goods_id AS "goodsId",
				a.paragraph_id AS "paragraphId",
				a.lesson_id AS "lessonId",
				a.question_id AS "questionId",
				a.topic_id AS "topicId",
				a.qtype AS "qtype",
				a.source_type AS "sourceType",
				a.product_type AS "productType",
				a.last_error_time AS "lastErrorTime",
				a.last_error_answer AS "lastErrorAnswer",
				a.create_date AS "createDate",
				a.del_flag AS "delFlag",
				a.category_id AS "categoryId",
				a.answer_id AS "answerId"
		FROM user_sub_error_question a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		INSERT INTO user_sub_error_question(
			id,
			uid,
			product_id,
			goods_id,
			paragraph_id,
			lesson_id,
			question_id,
			topic_id,
			qtype,
			source_type,
			product_type,
			last_error_time,
			last_error_answer,
			create_date,
			del_flag,
			category_id,
			answer_id
		) VALUES (
			#id#,
			#uid#,
			#productId#,
			#goodsId#,
			#paragraphId#,
			#lessonId#,
			#questionId#,
			#topicId#,
			#qtype#,
			#sourceType#,
			#productType#,
			#lastErrorTime#,
			#lastErrorAnswer#,
			#createDate#,
			#delFlag#,
			#categoryId#,
			#answerId#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<insert id="insertSharding" parameterClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		INSERT INTO user_sub_error_question(
		id,
		uid,
		product_id,
		goods_id,
		paragraph_id,
		lesson_id,
		question_id,
		topic_id,
		qtype,
		source_type,
		product_type,
		last_error_time,
		last_error_answer,
		create_date,
		del_flag,
		category_id,
		answer_id
		) VALUES (
		#id#,
		#uid#,
		#productId#,
		#goodsId#,
		#paragraphId#,
		#lessonId#,
		#questionId#,
		#topicId#,
		#qtype#,
		#sourceType#,
		#productType#,
		#lastErrorTime#,
		#lastErrorAnswer#,
		#createDate#,
		#delFlag#,
		#categoryId#,
		#answerId#
		)
	</insert>

	<insert id="insertBatch" parameterClass="java.util.Map">
		INSERT INTO user_sub_error_question(
		id,
		uid,
		product_id,
		goods_id,
		paragraph_id,
		lesson_id,
		question_id,
		topic_id,
		qtype,
		source_type,
		product_type,
		last_error_time,
		last_error_answer,
		create_date,
		del_flag,
		category_id,
		answer_id
		) VALUES
		<iterate property="list" conjunction =",">
			(
			#list[].id#,
			#list[].uid#,
			#list[].productId#,
			#list[].goodsId#,
			#list[].paragraphId#,
			#list[].lessonId#,
			#list[].questionId#,
			#list[].topicId#,
			#list[].qtype#,
			#list[].sourceType#,
			#list[].productType#,
			#list[].lastErrorTime#,
			#list[].lastErrorAnswer#,
			#list[].createDate#,
			#list[].delFlag#,
			#list[].categoryId#,
			#list[].answerId#
			)
		</iterate>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		UPDATE user_sub_error_question SET last_error_time=now()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="productId">
							product_id = #productId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="goodsId">
							goods_id = #goodsId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="paragraphId">
							paragraph_id = #paragraphId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="lessonId">
							lesson_id = #lessonId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="questionId">
							question_id = #questionId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="topicId">
							topic_id = #topicId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="qtype">
							qtype = #qtype#
						</isNotEmpty>
						<isNotEmpty prepend="," property="sourceType">
							source_type = #sourceType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="productType">
							product_type = #productType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="lastErrorAnswer">
							last_error_answer = #lastErrorAnswer#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="answerId">
							answer_id = #answerId#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id# and uid = #uid#
	</update>


	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM user_sub_error_question where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.product_id AS "productId",
					a.goods_id AS "goodsId",
					a.paragraph_id AS "paragraphId",
					a.lesson_id AS "lessonId",
					a.question_id AS "questionId",
					a.topic_id AS "topicId",
					a.qtype AS "qtype",
					a.source_type AS "sourceType",
					a.product_type AS "productType",
					a.last_error_time AS "lastErrorTime",
					a.last_error_answer AS "lastErrorAnswer",
					a.create_date AS "createDate",
					a.del_flag AS "delFlag",
					a.category_id AS "categoryId",
					a.answer_id AS "answerId"
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="goodsId">
				a.goods_id = #goodsId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productId">
				a.product_id = #productId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="startTime">
				<![CDATA[a.create_date >= #startTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="endTime">
				<![CDATA[a.create_date <= #endTime#]]>
			</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="goodsId">
				a.goods_id = #goodsId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productId">
				a.product_id = #productId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="startTime">
				<![CDATA[a.create_date >= #startTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="endTime">
				<![CDATA[a.create_date <= #endTime#]]>
			</isNotEmpty>
		</dynamic>
	</select>

	<select id="findGroupByList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		SELECT
			id ,
			uid ,
			product_id AS productId,
			goods_id AS goodsId,
			category_id AS categoryId,
			paragraph_id AS paragraphId,
			lesson_id AS lessonId,
			question_id AS questionId,
			topic_id AS topicId,
			qtype AS qtype,
			source_type AS sourceType,
			product_type AS productType,
			MAX(last_error_time) AS lastErrorTime,
			MAX(last_error_answer) AS lastErrorAnswer,
			MAX(create_date) AS createDate,
			del_flag AS delFlag,
			COUNT(*) AS errorTimes
		FROM user_sub_error_question
		where uid = #uid# and del_flag = 0
		<isNotEmpty prepend="AND" property="notInQuestionList">
			question_id not in
			<iterate conjunction="," open="(" close=")" property="notInQuestionList">
				<![CDATA[
                #notInQuestionList[]#
            ]]>
			</iterate>
		</isNotEmpty>
		<dynamic prepend="AND">
			<isNotNull property="sourceType">
				<!-- 全部，不区分商品-->
				<isEqual property="sourceType" compareValue="0">
				</isEqual>
				<!-- 课程配置，当前商品-->
				<isEqual property="sourceType" compareValue="1">
					<isNotEmpty prepend="AND" property="goodsId">
						goods_id = #goodsId#
					</isNotEmpty>
				</isEqual>
				<!-- 快题库，非当前商品-->
				<isEqual property="sourceType" compareValue="2">
					<isNotEmpty prepend="AND" property="goodsId">
						goods_id is null
					</isNotEmpty>
				</isEqual>
			</isNotNull>
			<isNotEmpty prepend="AND" property="categoryId">
				category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="startTime">
				<![CDATA[create_date >= #startTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="endTime">
				<![CDATA[create_date <= #endTime#]]>
			</isNotEmpty>
		</dynamic>
		GROUP BY
		uid, question_id
		<dynamic prepend="HAVING">
			<isNotNull property="moreThanTimes">
				<![CDATA[errorTimes >= #moreThanTimes#]]>
			</isNotNull>
			<isNotNull  property="lessThanTimes">
				<![CDATA[errorTimes = 1]]>
			</isNotNull>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="groupByCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		select count(*) from
		(
			SELECT COUNT(*) AS errorTimes
			FROM user_sub_error_question a
			where a.uid = #uid# and a.del_flag = 0
			<isNotEmpty prepend="AND" property="notInQuestionList">
				question_id not in
				<iterate conjunction="," open="(" close=")" property="notInQuestionList">
					<![CDATA[
					#notInQuestionList[]#
				]]>
				</iterate>
			</isNotEmpty>
			<dynamic prepend="AND">
				<isNotEmpty prepend="AND" property="categoryId">
					category_id = #categoryId#
				</isNotEmpty>
				<isNotNull property="sourceType">
					<!-- 全部，不区分商品-->
					<isEqual property="sourceType" compareValue="0">
					</isEqual>
					<!-- 课程配置，当前商品-->
					<isEqual property="sourceType" compareValue="1">
						<isNotEmpty prepend="AND" property="goodsId">
							goods_id = #goodsId#
						</isNotEmpty>
						<isNotEmpty prepend="AND" property="productId">
							product_id = #productId#
						</isNotEmpty>
					</isEqual>
					<!-- 快题库，非当前商品-->
					<isEqual property="sourceType" compareValue="2">
						<isNotEmpty prepend="AND" property="goodsId">
							goods_id is null
						</isNotEmpty>
					</isEqual>
				</isNotNull>

				<isNotEmpty prepend="AND" property="startTime">
					<![CDATA[create_date >= #startTime#]]>
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="endTime">
					<![CDATA[create_date <= #endTime#]]>
				</isNotEmpty>
			</dynamic>
			GROUP BY
			uid, question_id
			<dynamic prepend="HAVING">
				<isNotNull property="moreThanTimes">
					<![CDATA[errorTimes >= #moreThanTimes#]]>
				</isNotNull>
				<isNotNull  property="lessThanTimes">
					<![CDATA[errorTimes = 1]]>
				</isNotNull>
			</dynamic>
		) subquery
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.product_id AS "productId",
					a.goods_id AS "goodsId",
					a.paragraph_id AS "paragraphId",
					a.lesson_id AS "lessonId",
					a.question_id AS "questionId",
					a.topic_id AS "topicId",
					a.qtype AS "qtype",
					a.source_type AS "sourceType",
					a.product_type AS "productType",
					a.last_error_time AS "lastErrorTime",
					a.last_error_answer AS "lastErrorAnswer",
					a.create_date AS "createDate",
					a.del_flag AS "delFlag",
					a.category_id AS "categoryId",
					a.answer_id AS "answerId"
		FROM user_sub_error_question a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>


	<select id="findUserErrorHomeWorkLessonIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserErrorHomeworkLessonQuestionBean">
		SELECT
		a.product_id AS "productId",
		a.lesson_id AS "lessonId",
		a.question_id AS "questionId"
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productIds">
				a.product_id IN
				<iterate conjunction="," open="(" close=")" property="productIds">
					#productIds[]#
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="goodsId">
				a.goods_id = #goodsId#
			</isNotEmpty>
		</dynamic>
	</select>

	<select id="findUserErrorHomeWorkQuestionIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserErrorHomeworkLessonQuestionBean">
		SELECT
		a.product_id AS "productId",
		a.lesson_id AS "lessonId",
		a.question_id AS "questionId"
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productId">
				a.product_id = #productId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="lessonIds">
				a.lesson_id IN
				<iterate conjunction="," open="(" close=")" property="lessonIds">
					#lessonIds[]#
				</iterate>
			</isNotEmpty>
		</dynamic>
	</select>

	<select id="findUserErrorEpaperQuestionIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserErrorEpaperQuestion">
		SELECT
		a.product_id AS "productId",
		a.question_id AS "questionId"
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productIds">
				a.product_id IN
				<iterate conjunction="," open="(" close=")" property="productIds">
					#productIds[]#
				</iterate>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="goodsId">
				a.goods_id = #goodsId#
			</isNotEmpty>
		</dynamic>
	</select>


	<select id="getUserErrorQuestionCountByUidAndCategory" parameterClass="java.util.Map" resultClass="java.lang.Long">
		select count(DISTINCT question_id) as questionCount from user_sub_error_question a where a.uid = #uid# and a.del_flag = 0
		<isNotEmpty prepend="AND" property="categoryId">
			a.category_id = #categoryId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="goodsId">
			a.goods_id = #goodsId#
		</isNotEmpty>
	</select>

	<delete id="removeErrorQuestionByCategory" parameterClass="java.util.Map">
		DELETE FROM user_sub_error_question
		where uid=#uid#
		and category_id = #categoryId#
		<isNotNull property="sourceType">
			<!-- 全部，不区分商品-->
			<isEqual property="sourceType" compareValue="0">
			</isEqual>
			<!-- 课程配置，当前商品-->
			<isEqual property="sourceType" compareValue="1">
				<isNotEmpty prepend="AND" property="goodsId">
					goods_id = #goodsId#
				</isNotEmpty>
			</isEqual>
			<!-- 快题库，非当前商品-->
			<isEqual property="sourceType" compareValue="2">
				<isNotEmpty prepend="AND" property="goodsId">
					goods_id is null
				</isNotEmpty>
			</isEqual>
		</isNotNull>
		<isNotEmpty prepend="AND" property="questionId">
			question_id = #questionId#
		</isNotEmpty>
<!--		<isNotNull prepend="AND" property="moreThanTimes">-->
<!--			<![CDATA[error_time >= #moreThanTimes#]]>-->
<!--		</isNotNull>-->
<!--		<isNotNull prepend="AND" property="lessThanTimes">-->
<!--			<![CDATA[error_time <= 1]]>-->
<!--		</isNotNull>-->
		<isNotEmpty prepend="AND" property="startDate">
			<![CDATA[create_date >= #startDate#]]>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="endDate">
			<![CDATA[create_date <= #endDate#]]>
		</isNotEmpty>
	</delete>

	<delete id="removeErrorQuestionByCategoryGroupByQuestion" parameterClass="java.util.Map">
		DELETE FROM user_sub_error_question
		WHERE uid = #uid#
		AND category_id = #categoryId#
		<isNotEmpty prepend="AND" property="goodsId">
			goods_id = #goodsId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="questionId">
			question_id = #questionId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="startDate">
			<![CDATA[ create_date >= #startDate# ]]>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="endDate">
			<![CDATA[ create_date <= #endDate# ]]>
		</isNotEmpty>
		AND question_id IN (
		SELECT question_id
		FROM (
		SELECT question_id
		FROM user_sub_error_question
		WHERE uid = #uid#
		AND category_id = #categoryId#
		<isNotNull property="sourceType">
			<!-- 全部，不区分商品-->
			<isEqual property="sourceType" compareValue="0">
			</isEqual>
			<!-- 课程配置，当前商品-->
			<isEqual property="sourceType" compareValue="1">
				<isNotEmpty prepend="AND" property="goodsId">
					goods_id = #goodsId#
				</isNotEmpty>
			</isEqual>
			<!-- 快题库，非当前商品-->
			<isEqual property="sourceType" compareValue="2">
				<isNotEmpty prepend="AND" property="goodsId">
					goods_id is null
				</isNotEmpty>
			</isEqual>
		</isNotNull>
		<isNotEmpty prepend="AND" property="questionId">
			question_id = #questionId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="startDate">
			<![CDATA[ create_date >= #startDate# ]]>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="endDate">
			<![CDATA[ create_date <= #endDate# ]]>
		</isNotEmpty>
		GROUP BY question_id
		<isNotEmpty prepend="HAVING">
			<isNotNull property="moreThanTimes">
				<![CDATA[ COUNT(*) >= #moreThanTimes# ]]>
			</isNotNull>
			<isNotNull property="lessThanTimes">
				<![CDATA[ COUNT(*) = 1 ]]>
			</isNotNull>
		</isNotEmpty>
		) AS subquery
		)
	</delete>

	<select id="getOne" parameterClass="cn.huanju.edu100.study.model.UserSubErrorQuestion"
			resultClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		SELECT
		a.id AS "id",
		a.uid AS "uid",
		a.product_id AS "productId",
		a.goods_id AS "goodsId",
		a.paragraph_id AS "paragraphId",
		a.lesson_id AS "lessonId",
		a.question_id AS "questionId",
		a.topic_id AS "topicId",
		a.qtype AS "qtype",
		a.source_type AS "sourceType",
		a.product_type AS "productType",
		a.last_error_time AS "lastErrorTime",
		a.last_error_answer AS "lastErrorAnswer",
		a.create_date AS "createDate",
		a.del_flag AS "delFlag",
		a.category_id AS "categoryId",
		a.answer_id AS "answerId"
		FROM user_sub_error_question a
		<dynamic prepend="where">
			<isNotNull prepend="AND" property="uid">
				a.uid = #uid#
			</isNotNull>
			<isNotEmpty prepend="AND" property="goodsId">
				a.goods_id = #goodsId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productId">
				a.product_id = #productId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotNull prepend="AND" property="lessonId">
				a.lesson_id = #lessonId#
			</isNotNull>
			<isNotNull prepend="AND" property="questionId">
				a.question_id = #questionId#
			</isNotNull>
			<isNotNull prepend="AND" property="topicId">
				a.topic_id = #topicId#
			</isNotNull>
		</dynamic>
		limit 1
	</select>


	<delete id="removeUserSubErrorQuestion" parameterClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		DELETE FROM user_sub_error_question
		where uid=#uid# and question_id = #questionId#
		<isNotNull prepend="AND" property="topicId">
			topic_id = #topicId#
		</isNotNull>
	</delete>
	<delete id="removeUserSubErrorQuestionSoft" parameterClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		update user_sub_error_question
		set del_flag = 2
		where uid=#uid# and question_id = #questionId#
		<isNotNull prepend="AND" property="topicId">
			topic_id = #topicId#
		</isNotNull>
	</delete>

	<select id="getUserErrorQuestionCountByQtype" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionCountVo">
		select bb.qtype,count(1) count from (
		select DISTINCT a.question_id,a.qtype from user_sub_error_question a where a.uid = #uid# and a.del_flag = 0
		<isNotEmpty prepend="AND" property="categoryId">
			a.category_id = #categoryId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="goodsId">
			a.goods_id = #goodsId#
		</isNotEmpty>
		) bb
		group by bb.qtype
	</select>

	<select id="getUserErrorQuestionListGroupByQtype" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserErrorEpaperQuestion">
		select a.question_id as questionId,a.qtype from user_sub_error_question a where a.uid = #uid# and a.del_flag = 0
		<isNotEmpty prepend="AND" property="categoryId">
			a.category_id = #categoryId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="goodsId">
			a.goods_id = #goodsId#
		</isNotEmpty>
		group by a.question_id,a.qtype
	</select>

	<select id="findUserErrorQuestionIdsByCategoryAndGoodsId" parameterClass="java.util.Map" resultClass="java.lang.Long">
		SELECT
		distinct(a.question_id) AS "questionId"
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<isNotNull property="sourceType">
			<!-- 全部，不区分商品-->
			<isEqual property="sourceType" compareValue="0">
			</isEqual>
			<!-- 课程配置，当前商品-->
			<isEqual property="sourceType" compareValue="1">
				<isNotEmpty prepend="AND" property="goodsId">
					goods_id = #goodsId#
				</isNotEmpty>
			</isEqual>
			<!-- 快题库，非当前商品-->
			<isEqual property="sourceType" compareValue="2">
				<isNotEmpty prepend="AND" property="goodsId">
					goods_id is null
				</isNotEmpty>
			</isEqual>
		</isNotNull>
		<isNotEmpty prepend="AND" property="categoryId">
			a.category_id = #categoryId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="startDate">
			<![CDATA[create_date >= #startDate#]]>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="endDate">
			<![CDATA[create_date <= #endDate#]]>
		</isNotEmpty>
	</select>

	<select id="findAnswerIdByCategoryAndGoodsId" parameterClass="java.util.Map" resultClass="java.lang.Long">
		SELECT
		distinct(a.answer_id) AS "answerId"
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<isNotNull property="sourceType">
			<!-- 全部，不区分商品-->
			<isEqual property="sourceType" compareValue="0">
			</isEqual>
			<!-- 课程配置，当前商品-->
			<isEqual property="sourceType" compareValue="1">
				<isNotEmpty prepend="AND" property="goodsId">
					goods_id = #goodsId#
				</isNotEmpty>
			</isEqual>
			<!-- 快题库，非当前商品-->
			<isEqual property="sourceType" compareValue="2">
				<isNotEmpty prepend="AND" property="goodsId">
					goods_id is null
				</isNotEmpty>
			</isEqual>
		</isNotNull>
		<isNotEmpty prepend="AND" property="categoryId">
			a.category_id = #categoryId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="startTime">
			<![CDATA[a.create_date >= #startTime#]]>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="endTime">
			<![CDATA[a.create_date <= #endTime#]]>
		</isNotEmpty>
	</select>

	<select id="getUserSubErrorQuestionList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		SELECT
		a.id AS "id",
		a.uid AS "uid",
		a.product_id AS "productId",
		a.goods_id AS "goodsId",
		a.paragraph_id AS "paragraphId",
		a.lesson_id AS "lessonId",
		a.question_id AS "questionId",
		a.topic_id AS "topicId",
		a.qtype AS "qtype",
		a.source_type AS "sourceType",
		a.product_type AS "productType",
		a.last_error_time AS "lastErrorTime",
		a.last_error_answer AS "lastErrorAnswer",
		a.create_date AS "createDate",
		a.del_flag AS "delFlag",
		a.category_id AS "categoryId",
		a.answer_id AS "answerId"
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="goodsId">
				a.goods_id = #goodsId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productId">
				a.product_id = #productId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="startTime">
				<![CDATA[a.create_date >= #startTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="endTime">
				<![CDATA[a.create_date <= #endTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="questionIdList">
				a.question_id IN
				<iterate conjunction="," open="(" close=")" property="questionIdList">
					#questionIdList[]#
				</iterate>
			</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<isEmpty property="noNeedLimit">
			limit 500
		</isEmpty>
		<isEqual property="noNeedLimit" compareValue="0">
			limit 500
		</isEqual>
	</select>

	<select id="countUserSubErrorQuestionList" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT
		count(*)
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="goodsId">
				a.goods_id = #goodsId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productId">
				a.product_id = #productId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="startTime">
				<![CDATA[a.create_date >= #startTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="endTime">
				<![CDATA[a.create_date <= #endTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="questionIdList">
				a.question_id IN
				<iterate conjunction="," open="(" close=")" property="questionIdList">
					#questionIdList[]#
				</iterate>
			</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		limit 500
	</select>

	<select id="getUserErrorQuestionCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM user_sub_error_question a
		where a.uid = #uid# and a.del_flag = 0
		<dynamic prepend="AND">
			<isNotEmpty prepend="AND" property="goodsId">
				a.goods_id = #goodsId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="productId">
				a.product_id = #productId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="categoryId">
				a.category_id = #categoryId#
			</isNotEmpty>
<!--			<isNotNull prepend="AND" property="moreThanTimes">-->
<!--				a.error_time >= #moreThanTimes#-->
<!--			</isNotNull>-->
<!--			<isNull prepend="AND" property="moreThanTimes">-->
<!--				a.error_time = 1-->
<!--			</isNull>-->
			<isNotEmpty prepend="AND" property="startTime">
				<![CDATA[a.create_date >= #startTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="endTime">
				<![CDATA[a.create_date <= #endTime#]]>
			</isNotEmpty>
			and a.del_flag = 0
		</dynamic>
	</select>

	<select id="getUserQuestionErrorTimes" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserSubErrorQuestion">
		SELECT
		uid ,
		question_id AS questionId,
		COUNT(*) AS errorTimes
		FROM user_sub_error_question
		where uid = #uid# and del_flag = 0
		<isNotEmpty prepend="AND" property="questionIdList">
			question_id  in
			<iterate conjunction="," open="(" close=")" property="questionIdList">
				<![CDATA[
                #questionIdList[]#
            ]]>
			</iterate>
		</isNotEmpty>
		<dynamic prepend="AND">
			<isNotNull property="sourceType">
				<!-- 全部，不区分商品-->
				<isEqual property="sourceType" compareValue="0">
				</isEqual>
				<!-- 课程配置，当前商品-->
				<isEqual property="sourceType" compareValue="1">
					<isNotEmpty prepend="AND" property="goodsId">
						goods_id = #goodsId#
					</isNotEmpty>
				</isEqual>
				<!-- 快题库，非当前商品-->
				<isEqual property="sourceType" compareValue="2">
					<isNotEmpty prepend="AND" property="goodsId">
						goods_id is null
					</isNotEmpty>
				</isEqual>
			</isNotNull>
			<isNotEmpty prepend="AND" property="categoryId">
				category_id = #categoryId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="startTime">
				<![CDATA[create_date >= #startTime#]]>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="endTime">
				<![CDATA[create_date <= #endTime#]]>
			</isNotEmpty>
		</dynamic>
		GROUP BY
		uid, question_id
	</select>

</sqlMap>
