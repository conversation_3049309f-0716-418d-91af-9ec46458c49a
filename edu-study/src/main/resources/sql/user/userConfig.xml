<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserConfig">
    <sql id="userConfigColumns">
        a.id AS "id",
        a.uid AS "uid",
        a.config_key AS "configKey",
        a.config_val AS "configVal",
        a.update_date AS "updateDate"
    </sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.user.UserConfig">
	   select
        <include refid="userConfigColumns"/>
        FROM user_config a
		WHERE a.id = #id#
	</select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.user.UserConfig">
        INSERT INTO user_config(
        uid,
        config_key,
        config_val,
        update_date
        ) VALUES (
        #uid#,
        #configKey#,
        #configVal#,
        #updateDate#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <update id="update" parameterClass="cn.huanju.edu100.study.model.user.UserConfig">
        UPDATE user_config SET update_date=now()
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="configKey">
                config_key = #configKey#
            </isNotEmpty>
            <isNotEmpty prepend="," property="configVal">
                config_val = #configVal#
            </isNotEmpty>
            <isNotEmpty prepend="," property="uid">
                uid = #uid#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id#
    </update>


    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.user.UserConfig">
        SELECT
        <include refid="userConfigColumns"/>
        FROM user_config a where 1=1
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="configKey">
                config_key = #configKey#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="configVal">
                config_val = #configVal#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="uid">
                uid = #uid#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>


    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT COUNT(1)
        FROM user_config a where 1=1
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="configKey">
                config_key = #configKey#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="configVal">
                config_val = #configVal#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="uid">
                uid = #uid#
            </isNotEmpty>
        </dynamic>
    </select>

</sqlMap>
