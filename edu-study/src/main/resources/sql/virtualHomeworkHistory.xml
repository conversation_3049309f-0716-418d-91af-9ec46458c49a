<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VirtualHomeworkHistory">

	<select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.VirtualHomework">
	   select
			a.id AS "id",
			a.uid AS "uid",
			a.name AS "name",
			a.num AS "num",
			a.homework_type_id AS "homeworkTypeId",
			a.homework_type AS "homeworkType",
			a.source_id AS "sourceId",
			a.source_type AS "sourceType",
			a.book_id AS "bookId",
			a.random_type AS "randomType",
			a.create_by AS "createBy",
			a.update_by AS "updateBy",
			a.create_date AS "createDate",
			a.update_date AS "updateDate"
		FROM virtual_homework_$tbidx$ a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.questionBox.VirtualHomework">
		INSERT INTO virtual_homework_$tbidx$(
            id,
			uid,
			name,
			num,
			homework_type_id,
			homework_type,
			source_id,
			source_type,
			book_id,
			random_type,
			create_by,
			update_by,
			create_date,
			update_date
		) VALUES (
            #id#,
			#uid#,
			#name#,
			#num#,
			#homeworkTypeId#,
			#homeworkType#,
			#sourceId#,
			#sourceType#,
			#bookId#,
			#randomType#,
			#createBy#,
			#updateBy#,
            #createDate#,
            #updateDate#
		)
	</insert>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.VirtualHomework">
		SELECT
			a.id AS "id",
			a.uid AS "uid",
			a.name AS "name",
			a.num AS "num",
			a.homework_type_id AS "homeworkTypeId",
			a.homework_type AS "homeworkType",
			a.source_id AS "sourceId",
			a.source_type AS "sourceType",
			a.book_id AS "bookId",
			a.random_type AS "randomType",
			a.create_by AS "createBy",
			a.update_by AS "updateBy",
			a.create_date AS "createDate",
			a.update_date AS "updateDate"
		FROM virtual_homework_$tbidx$ a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="sourceId">
				a.source_id = #sourceId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="sourceType">
				a.source_type = #sourceType#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="randomType">
                <isEqual property="randomType" compareValue="9">
                    a.random_type IN (0, 1, 2)
                </isEqual>
                <isEqual property="randomType" compareValue="0">
                    a.random_type = 0
                </isEqual>
                <isEqual property="randomType" compareValue="1">
                    a.random_type = 1
                </isEqual>
                <isEqual property="randomType" compareValue="2">
                    a.random_type = 2
                </isEqual>
                <isEqual property="randomType" compareValue="3">
                    a.random_type = 3
                </isEqual>
                <isEqual property="randomType" compareValue="4">
                    a.random_type = 4
                </isEqual>
			</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM virtual_homework_$tbidx$ a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="sourceId">
				a.source_id = #sourceId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="sourceType">
				a.source_type = #sourceType#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="randomType">
                <isEqual property="randomType" compareValue="9">
                    a.random_type IN (0, 1, 2)
                </isEqual>
                <isEqual property="randomType" compareValue="0">
                    a.random_type = 0
                </isEqual>
                <isEqual property="randomType" compareValue="1">
                    a.random_type = 1
                </isEqual>
                <isEqual property="randomType" compareValue="2">
                    a.random_type = 2
                </isEqual>
                <isEqual property="randomType" compareValue="3">
                    a.random_type = 3
                </isEqual>
                <isEqual property="randomType" compareValue="4">
                    a.random_type = 4
                </isEqual>
			</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.VirtualHomework">
		SELECT
			a.id AS "id",
			a.uid AS "uid",
			a.name AS "name",
			a.num AS "num",
			a.homework_type_id AS "homeworkTypeId",
			a.homework_type AS "homeworkType",
			a.source_id AS "sourceId",
			a.source_type AS "sourceType",
			a.book_id AS "bookId",
			a.random_type AS "randomType",
			a.create_by AS "createBy",
			a.update_by AS "updateBy",
			a.create_date AS "createDate",
			a.update_date AS "updateDate"
		FROM virtual_homework_$tbidx$ a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>


    <insert id="insertDetailBatchBatch" parameterClass="java.util.Map">
		insert into virtual_homework_detail_$tbidx$
			(
            id,
            homework_id,
			element_id,
			element_type,
			create_by,
			update_by,
			create_date,
			update_date)
		values
		<iterate property="list" conjunction =",">
            (
            #list[].id#,
            #list[].homeworkId#,
            #list[].elementId#,
            #list[].elementType#,
            #list[].createBy#,
            #list[].createBy#,
            #list[].createDate#,
            #list[].updateDate#
            )
    	</iterate>
	</insert>

    <select id="getHomeWorkDetails" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.VirtualHomeworkDetail">
		SELECT
			  a.id AS "id",
			  a.homework_id AS "homeworkId",
			  a.element_id AS "elementId",
			  a.element_type AS "elementType",
              a.create_by AS "createBy",
              a.update_by AS "updateBy",
			  a.create_date AS "createDate",
			  a.update_date AS "updateDate"
		FROM virtual_homework_detail_$tbidx$ a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="homeworkId">
				a.homework_id = #homeworkId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="elementId">
				a.element_id = #elementId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="elementType">
				a.element_type = #elementType#
			</isNotEmpty>
		</dynamic>
	</select>

	<delete id="deleteDetailByHomeworkId" parameterClass="java.util.HashMap">
		DELETE FROM virtual_homework_detail_$tbidx$ where homework_id = #homeworkId#
	</delete>

</sqlMap>
