<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserAnswerDetail">

    <select id="get" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserAnswerDetail">
	   select
				a.id AS "id",
				a.sum_id AS "sumId",
				a.uid AS "uid",
				a.question_id AS "questionId",
				a.topic_id AS "topicId",
				a.options_id AS "optionsId",
				a.answer AS "answerStr",
				a.pic AS "pic",
				a.mp3 AS "mp3",
				a.file AS "file",
				a.is_right AS "isRight",
				a.score AS "score",
				a.tuid AS "tuid",
				a.comment AS "comment",
				a.comment_time AS "commentTime"
		FROM user_answer_detail a
		WHERE a.id = #id# and a.uid = #uid#
	</select>

    <insert id="insert" parameterClass="com.hqwx.study.entity.UserAnswerDetail">
        INSERT INTO user_answer_detail(
        sum_id,
        uid,
        question_id,
        topic_id,
        options_id,
        answer,
        pic,
        mp3,
        file,
        is_right,
        create_date,
        update_date,
        score
        ) VALUES (
        #sumId#,
        #uid#,
        #questionId#,
        #topicId#,
        #optionsId#,
        #answerStr#,
        #pic#,
        #mp3#,
        #file#,
        #isRight#,
        now(),
        now(),
        #score#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <insert id="insertSharding" parameterClass="com.hqwx.study.entity.UserAnswerDetail">
        INSERT INTO user_answer_detail(
        id,
        sum_id,
        uid,
        question_id,
        topic_id,
        options_id,
        answer,
        pic,
        mp3,
        file,
        is_right,
        create_date,
        update_date,
        score
        ) VALUES (
        #id#,
        #sumId#,
        #uid#,
        #questionId#,
        #topicId#,
        #optionsId#,
        #answerStr#,
        #pic#,
        #mp3#,
        #file#,
        #isRight#,
        now(),
        now(),
        #score#
        )
    </insert>

    <update id="update" parameterClass="com.hqwx.study.entity.UserAnswerDetail">
        UPDATE user_answer_detail SET update_date=now()
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="sumId">
                sum_id = #sumId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="uid">
                uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="," property="questionId">
                question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="topicId">
                topic_id = #topicId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="optionsId">
                options_id = #optionsId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="answerStr">
                answer = #answerStr#
            </isNotEmpty>
            <isNotEmpty prepend="," property="pic">
                pic = #pic#
            </isNotEmpty>
            <isNotEmpty prepend="," property="mp3">
                mp3 = #mp3#
            </isNotEmpty>
            <isNotEmpty prepend="," property="file">
                file = #file#
            </isNotEmpty>
            <isNotEmpty prepend="," property="isRight">
                is_right = #isRight#
            </isNotEmpty>
            <isNotEmpty prepend="," property="score">
                score = #score#
            </isNotEmpty>
            <isNotEmpty prepend="," property="tuid">
                tuid = #tuid#
            </isNotEmpty>
            <isNotEmpty prepend="," property="comment">
                comment = #comment#
            </isNotEmpty>
            <isNotEmpty prepend="," property="commentTime">
                comment_time = #commentTime#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id# and uid = #uid#
    </update>

    <delete id="delete" parameterClass="java.util.Map">
        DELETE FROM user_answer_detail where id=#id# and uid = #uid#
    </delete>

    <select id="findList" parameterClass="java.util.Map"
            resultClass="com.hqwx.study.entity.UserAnswerDetail">
        SELECT
        a.id AS "id",
        a.sum_id AS "sumId",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.topic_id AS "topicId",
        a.options_id AS "optionsId",
        a.answer AS "answerStr",
        a.pic AS "pic",
        a.mp3 AS "mp3",
        a.file AS "file",
        a.is_right AS "isRight",
        a.score AS "score",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime"
        FROM user_answer_detail a
        <dynamic prepend="where">
            a.uid = #uid#
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isNotEmpty prepend="" property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="limit">
            <isNotEmpty prepend="" property="pageSize">
                #from#,#pageSize#
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT count(1)
        FROM user_answer_detail a
        <dynamic prepend="where">
            a.uid = #uid#
        </dynamic>
    </select>

    <select id="findAllList" parameterClass="java.util.Map"
            resultClass="com.hqwx.study.entity.UserAnswerDetail">
        SELECT
        a.id AS "id",
        a.sum_id AS "sumId",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.topic_id AS "topicId",
        a.options_id AS "optionsId",
        a.answer AS "answerStr",
        a.pic AS "pic",
        a.mp3 AS "mp3",
        a.file AS "file",
        a.is_right AS "isRight",
        a.score AS "score",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime"
        FROM user_answer_detail a
        where a.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="sumId">
                a.sum_id = #sumId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                <![CDATA[ a.create_date >= #startTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ a.create_date <= #endTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="isRight">
                a.is_right = #isRight#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="getByPaperId" parameterClass="java.util.Map"
            resultClass="com.hqwx.study.entity.UserAnswerDetail">
		select
			a.id AS "id",
			a.sum_id AS "sumId",
			a.uid AS "uid",
			a.question_id AS "questionId",
			a.topic_id AS "topicId",
			a.options_id AS "optionsId",
			a.answer AS "answerStr",
			a.pic AS "pic",
			a.mp3 AS "mp3",
			a.file AS "file",
			a.is_right AS "isRight",
			a.score AS "score",
			a.tuid AS "tuid",
			a.comment AS "comment",
			a.comment_time AS "commentTime"
		FROM user_answer_detail a
		WHERE a.uid = #uid# and a.question_id = #questionId#
	</select>

	<select id="findByUserHomeworkId" parameterClass="java.util.Map"
            resultClass="com.hqwx.study.entity.UserAnswerDetail">
		select
			a.id AS "id",
			a.sum_id AS "sumId",
			a.uid AS "uid",
			a.question_id AS "questionId",
			a.topic_id AS "topicId",
			a.options_id AS "optionsId",
			a.answer AS "answerStr",
			a.pic AS "pic",
			a.mp3 AS "mp3",
			a.file AS "file",
			a.is_right AS "isRight",
			a.score AS "score",
			a.tuid AS "tuid",
			a.comment AS "comment",
			a.comment_time AS "commentTime",
			b.user_homework_id AS "userHomeworkId"
		FROM user_answer_detail a right join user_answer_sum b on a.sum_id = b.id
		WHERE a.uid = #uid# and b.uid = #uid# and b.user_homework_id = #userHomeworkId#
	</select>

    <select id="findByUserHomeworkIdList" parameterClass="java.util.Map"
            resultClass="com.hqwx.study.entity.UserAnswerDetail">
        select
        a.id AS "id",
        a.sum_id AS "sumId",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.topic_id AS "topicId",
        a.options_id AS "optionsId",
        a.answer AS "answerStr",
        a.pic AS "pic",
        a.mp3 AS "mp3",
        a.file AS "file",
        a.is_right AS "isRight",
        a.score AS "score",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime",
        b.user_homework_id AS "userHomeworkId"
        FROM user_answer_detail a right join user_answer_sum b on a.sum_id = b.id
        WHERE a.uid = #uid# and b.uid = #uid#
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="userHomeworkIdList">
                b.user_homework_id in
                <iterate open="(" close=")" conjunction="," property="userHomeworkIdList" >
                    #userHomeworkIdList[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
    </select>

	<select id="findLastQIdDetails" parameterClass="com.hqwx.study.entity.UserAnswerDetail"
            resultClass="com.hqwx.study.entity.UserAnswerDetail">
		select
			a.id AS "id",
			a.sum_id AS "sumId",
			a.uid AS "uid",
			a.question_id AS "questionId",
			a.topic_id AS "topicId",
			a.options_id AS "optionsId",
			a.answer AS "answerStr",
			a.pic AS "pic",
			a.mp3 AS "mp3",
			a.file AS "file",
			a.is_right AS "isRight",
			a.score AS "score",
			a.tuid AS "tuid",
			a.comment AS "comment",
			a.comment_time AS "commentTime"
		FROM user_answer_detail a
		WHERE
			a.uid = #uid#
		AND a.question_id = #questionId#
		AND EXISTS (
			SELECT 1 FROM
				(SELECT topic_id, id, max(update_date)
					FROM user_answer_detail
					WHERE
						uid = #uid# AND question_id = #questionId#
					GROUP BY topic_id ) b
			WHERE b.id = a.id
		)
	</select>

    <select id="getUserAnswerDetailByQuestions" resultClass="com.hqwx.study.entity.UserAnswerDetail">
        select *
        from (
            select
                id AS "id",
                sum_id AS "sumId",
                uid AS "uid",
                question_id AS "questionId",
                topic_id AS "topicId",
                options_id AS "optionsId",
                answer AS "answerStr",
                pic AS "pic",
                mp3 AS "mp3",
                file AS "file",
                is_right AS "isRight",
                score AS "score",
                tuid AS "tuid",
                comment AS "comment",
                comment_time AS "commentTime"
            from user_answer_detail
            where uid = #uid#
            <dynamic prepend="and">
                <isNotEmpty prepend="AND" property="questionIdList">
                    question_id in
                    <iterate open="(" close=")" conjunction="," property="questionIdList" >
                        #questionIdList[]#
                    </iterate>
                </isNotEmpty>
            </dynamic>
            order by create_date desc limit 50000)
        a group by a.topicId
    </select>


    <select id="findStudyCenterLastHomeworkAnswerDetailList" resultClass="java.lang.Long">
        select sumId
        from (
        select
        sum_id AS "sumId"
        from user_answer_detail
        where uid = #uid# and is_right !=3
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="answerSumIdList">
                sum_id in
                <iterate open="(" close=")" conjunction="," property="answerSumIdList" >
                    #answerSumIdList[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
        )
        a group by a.sumId;
    </select>
    <select id="getUserAnswerDetailByQuestionAndAnswer" resultClass="com.hqwx.study.entity.UserAnswerDetailDto">
        select  a.id           as id,
                a.uid          as uid,
                a.sum_id       as sumId,
                a.question_id  as questionId,
                a.topic_id     as topicId,
                a.options_id   as optionsId,
                a.answer       as answer,
                a.pic          as pic,
                a.mp3          as mp3,
                a.file         as file,
                a.is_right     as isRight,
                a.score        as score,
                a.tuid         as tuid,
                a.comment      as comment,
                a.comment_time as commentTime,
                a.create_date  as createDate,
                a.update_date  as updateDate,
                null           as paperId
        from user_answer_detail a
        where   a.uid = #uid#
        and a.question_id = #questionId#
        <isNotEmpty prepend="AND" property="answerSumIdList">
            a.sum_id in
            <iterate open="(" close=")" conjunction="," property="answerSumIdList" >
                #answerSumIdList[]#
            </iterate>
        </isNotEmpty>
    </select>

    <select id="getUserAnswerDetailBySumIdList" resultClass="com.hqwx.study.entity.UserAnswerDetail">
        select  a.id AS "id",
        a.sum_id AS "sumId",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.topic_id AS "topicId",
        a.options_id AS "optionsId",
        a.answer AS "answerStr",
        a.pic AS "pic",
        a.mp3 AS "mp3",
        a.file AS "file",
        a.is_right AS "isRight",
        a.score AS "score",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime"
        from user_answer_detail a
        where   a.uid = #uid#
            and a.sum_id in
            <iterate open="(" close=")" conjunction="," property="answerSumIdList" >
                #answerSumIdList[]#
            </iterate>
    </select>
</sqlMap>
