<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserAnswerHomeworkOdd">

    <update id="updateByParam" parameterClass="java.util.Map">
        update user_answer_homework_$tbIndex$ set homework_id=#homeworkId#,update_date=update_date
        where id in
        <iterate conjunction="," open="(" close=")" property="idList">
            #idList[]#
        </iterate>
    </update>

    <select id="selectByParam" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserHomeWorkAnswer">
        SELECT id AS "id", obj_id AS "objId", obj_type AS "objType", product_id as "productId", homework_id as "homeworkId"
        FROM user_answer_homework_$tbIndex$
        where obj_id=#objId# and obj_type=0 and product_id=#productId#
    </select>

    <update id="updateHomeworkIdByUid" parameterClass="java.util.Map">
        update user_answer_homework a set a.homework_id=#homeworkId#
        where a.uid=#uid# and a.id=#id#
    </update>

    <select id="getMaxIdByTbIndex" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserHomeWorkAnswer">
        SELECT id AS "id", obj_id AS "objId", obj_type AS "objType", product_id as "productId"
        FROM user_answer_homework_$tbIndex$ order by id desc limit 1
    </select>

    <select id="selectCountByMaxId" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT count(1) as total
        FROM user_answer_homework_$tbIndex$ where id>#id# and obj_type=0 and obj_id is not null and product_id is not null order by id desc
    </select>

    <select id="selectListByMaxId" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserHomeWorkAnswer">
        SELECT id AS "id", obj_id AS "objId", obj_type AS "objType", product_id as "productId", homework_id as "homeworkId", uid AS "uid"
        FROM user_answer_homework_$tbIndex$ where id>#id# and obj_type=0 and obj_id is not null and product_id is not null order by id desc limit #from#,#rows#
    </select>

</sqlMap>
