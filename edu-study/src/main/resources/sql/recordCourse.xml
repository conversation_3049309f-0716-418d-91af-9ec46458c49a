<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="Recordcourse">
    
	<sql id="recordcourseColumns">
		a.id AS "id",
		a.task_id AS "taskId",
		a.course_id AS "courseId",
		a.cls_id AS "clsId",
		a.lesson_id AS "lessonId"
	</sql>
	
	<sql id="recordcourseJoins">
	</sql>
	
    <select id="findListByTaskIdList" parameterClass="java.lang.String"
            resultClass="cn.huanju.edu100.study.model.Recordcourse">
        SELECT 
			<include refid="recordcourseColumns"/>
		FROM record_course a
		WHERE a.task_id IN ($value$)
    </select>
    
</sqlMap>