<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserQuestionLog">


    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionLog">
        select
        a.id AS "id",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.status AS "status",
        a.last_time AS "lastTime",
        a.ip AS "ip"
        FROM user_question_log a
        WHERE a.id = #id#
    </select>
    <select id="queryByUidQuestionId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionLog">
        select
        a.id AS "id",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.last_time AS "lastTime",
        a.status AS "status",
        a.ip AS "ip"
        FROM user_question_log a

        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="uid">
                a.uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>

        </dynamic>

    </select>


    <select id="queryLastByUidQuestionId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionLog">
        select * from
        (select  id AS id,
        uid AS uid,
        question_id AS questionId,
        last_time AS lastTime,
        status AS status,
        ip AS ip,
        update_date as updateDate
        FROM user_question_log
        where uid = #uid#
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="questionIds">
                question_id in
                <iterate open="(" close=")" conjunction="," property="questionIds" >
                    #questionIds[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
        order by update_date desc limit 50000)
        as a group by a.uid,a.questionId;
    </select>

    <insert id="insertBatch" parameterClass="java.util.List">
        INSERT INTO user_question_log(
        uid,
        question_id,
        status,
        last_time,
        ip
        ) VALUES
        <iterate conjunction=",">
            <![CDATA[
			(#insertList[].uid#, #insertList[].questionId#, #insertList[].status#, #insertList[].lastTime#, #insertList[].ip#)
		]]>
        </iterate>

    </insert>
    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.UserQuestionLog">
        INSERT INTO user_question_log(
        uid,
        question_id,
        status,
        last_time,
        ip
        ) VALUES (
        #uid#,
        #questionId#,
        #status#,
        #lastTime#,
        #ip#
        )
        <selectKey resultClass="long" type="post" keyProperty="id" >
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>


    <update id="update" parameterClass="cn.huanju.edu100.study.model.UserQuestionLog">
        UPDATE user_question_log
        <dynamic prepend="set">
            <isNotEmpty prepend="," property="uid">
                uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="," property="questionId">
                question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="status">
                status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="," property="ip">
                ip = #ip#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lastTime">
                last_time = #lastTime#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id#
    </update>
    <delete id="delete" parameterClass="java.util.Map">
        DELETE FROM user_question_log where id=#id#
    </delete>

    <select id="findOne" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionLog">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.last_time AS "lastTime",
        a.status AS "status",
        a.ip AS "ip"
        FROM user_question_log a
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="uid">
                a.uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="status">
                a.status = #status#
            </isNotEmpty>
            order by a.update_date desc  limit 1
        </dynamic>
    </select>
    <select id="queryOne" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionLog">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.last_time AS "lastTime",
        a.status AS "status",
        a.ip AS "ip"
        FROM user_question_log a
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="uid">
                a.uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="status">
                a.status = #status#
            </isNotEmpty>
            order by a.update_date desc  limit 1
        </dynamic>
    </select>
    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionLog">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.last_time AS "lastTime",
        a.status AS "status",
        a.ip AS "ip"
        FROM user_question_log a
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="uid">
                a.uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="status">
                a.status = #status#
            </isNotEmpty>

            <isNotEmpty prepend="AND" property="ip">
                a.ip = #ip#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT count(1)
        FROM user_question_log a
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="uid">
                a.uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="status">
                a.status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="ip">
                a.ip = #ip#
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserQuestionLog">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.question_id AS "questionId",
        a.status AS "status",
        a.ip AS "ip"
        FROM user_question_log a
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
    </select>

</sqlMap>
