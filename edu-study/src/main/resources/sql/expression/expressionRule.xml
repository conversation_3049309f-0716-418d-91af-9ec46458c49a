<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="ExpressionRule">

    <typeAlias alias="ExpressionRule" type="cn.huanju.edu100.study.model.expression.ExpressionRule" />

	<sql id="expressionRuleColumns">
			a.id AS "id",
			a.title AS "title",
			a.t_name AS "tName",
			a.obj_name AS "objName",
			a.right_compare_type AS "rightCompareType",
			a.right_compare_val AS "rightCompareVal",
			a.left_compare_type AS "leftCompareType",
			a.left_compare_val AS "leftCompareVal",
			a.description AS "description",
	    	a.create_by AS "createBy",
			a.create_date AS "createDate",
	    	a.update_by AS "updateBy",
			a.update_date AS "updateDate",
			a.is_opposite AS "isOpposite",
			a.user_type AS "userType",
			a.sch_id AS "schId"
	</sql>

    <select id="get" parameterClass="java.util.Map" resultClass="ExpressionRule">
	   select
	    	<include refid="expressionRuleColumns"/>
		FROM expression_rule a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="ExpressionRule">
		INSERT INTO expression_rule(
			title,
			t_name,
			obj_name,
			right_compare_type,
			right_compare_val,
			left_compare_type,
			left_compare_val,
			description,
			create_by,
			create_date,
			update_by,
			update_date,
			is_opposite,
			sch_id
		) VALUES (
			#title#,
			#tName#,
			#objName#,
			#rightCompareType#,
			#rightCompareVal#,
			#leftCompareType#,
			#leftCompareVal#,
			#description#,
			#createBy#,
			#createDate#,
			#updateBy#,
			#updateDate#,
			#isOpposite#,
			#schId#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="ExpressionRule">
		UPDATE expression_rule SET
		update_date = NOW()
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="title">
							title = #title#
						</isNotEmpty>
						<isNotEmpty prepend="," property="tName">
							t_name = #tName#
						</isNotEmpty>
						<isNotEmpty prepend="," property="objName">
							obj_name = #objName#
						</isNotEmpty>
						<isNotEmpty prepend="," property="rightCompareType">
							right_compare_type = #rightCompareType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="rightCompareVal">
							right_compare_val = #rightCompareVal#
						</isNotEmpty>
						<isNotEmpty prepend="," property="leftCompareType">
							left_compare_type = #leftCompareType#
						</isNotEmpty>
						<isNotEmpty prepend="," property="leftCompareVal">
							left_compare_val = #leftCompareVal#
						</isNotEmpty>
						<isNotEmpty prepend="," property="description">
							description = #description#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy">
							update_by = #updateBy#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="schId">
							sch_id = #schId#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM expression_rule where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="ExpressionRule">
		SELECT
			a.id AS "id",
			a.is_opposite AS "isOpposite"
		FROM expression_rule a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="title">
						a.title LIKE concat('%',#title#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="tName">
						a.t_name = #tName#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="objName">
						a.obj_name = #objName#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="schId">
						a.sch_id = #schId#
				</isNotEmpty>
				<isPropertyAvailable prepend="AND" property="rightCompareValList">
					a.right_compare_val IN
					<iterate open="(" close=")" conjunction="," property="rightCompareValList">
						#rightCompareValList[]#
					</iterate>
				</isPropertyAvailable>
				<isNotEmpty prepend="AND" property="rightCompareVal">
					a.right_compare_val = #rightCompareVal#
						<!--(a.right_compare_val LIKE concat('%',#rightCompareVal#,'%')-->
						<!--<isNotEmpty prepend="OR" property="source">-->
							<!--a.right_compare_val = #source# or a.right_compare_val = 'terminal_all'-->
						<!--</isNotEmpty>-->
						<!--)-->
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="time">
						a.update_date >= #time#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListByParam" parameterClass="java.util.Map" resultClass="ExpressionRule">
		SELECT
			a.id AS "id",
			a.is_opposite AS "isOpposite"
		FROM expression_rule a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="title">
						a.title LIKE concat('%',#title#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="tName">
						a.t_name = #tName#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="objName">
						a.obj_name = #objName#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="schId">
						a.sch_id = #schId#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="rightCompareVal">
						(a.right_compare_val LIKE concat('%',#rightCompareVal#,'%')
						or a.right_compare_val = 'web_all' or a.right_compare_val = 'app_all')
				</isNotEmpty>
				<isPropertyAvailable property="idList" prepend="AND">
				a.id IN
					<iterate conjunction="," open="(" close=")" property="idList">
						#idList[]#
					</iterate>
				</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT COUNT(1)
		FROM expression_rule a
			<dynamic prepend="WHERE">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="title">
						a.title LIKE CONCAT('%',#title#,'%')
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="tName">
						a.t_name = #tName#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="objName">
						a.obj_name = #objName#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="schId">
						a.sch_id = #schId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="ExpressionRule">
		SELECT
	   		<include refid="expressionRuleColumns"/>
		FROM expression_rule a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
