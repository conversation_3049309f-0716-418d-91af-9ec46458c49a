<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="SynTaskInfo">

	<sql id="columns" >
		a.id AS "id",
		a.name AS "name",
		a.syn_time AS "synTime",
		a.syn_key AS "synKey",
		a.last_exe_time AS "lastExeTime",
		a.is_on AS "isOn",
		a.filter AS "filter"
	</sql>

	<select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.SynTaskInfo">
		select
		<include refid="columns"></include>
		FROM syn_task_info a
		WHERE a.id = #id#
	</select>
	<select id="getByName" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.SynTaskInfo">
		select
		<include refid="columns"></include>
		FROM syn_task_info a
		WHERE a.name = #name#
	</select>
	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.SynTaskInfo">
		select
		<include refid="columns"></include>
		FROM syn_task_info a

		<dynamic prepend="where">
			<isNotEmpty prepend="and" property="id">
				AND a.id = #id#
			</isNotEmpty>
			<isNotEmpty prepend="and" property="name">
				AND a.name = #name#
			</isNotEmpty>
		</dynamic>

	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.questionBox.SynTaskInfo">
		INSERT INTO syn_task_info(
			name,
			syn_time,
			syn_key,
			last_exe_time,
			is_on,
			filter,
		) VALUES (
			#name#,
			#synTime#,
			#synKey#,
			#lastExeTime#,
			#isOn#,
			#filter#
		)
	</insert>

	<update id="updateByName" parameterClass="cn.huanju.edu100.study.model.questionBox.SynTaskInfo">
		UPDATE syn_task_info
        <dynamic prepend="SET">
            <isNotEmpty prepend="," property="synTime">
                syn_time = #synTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="synKey">
                syn_key = #synKey#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lastExeTime">
                last_exe_time = #lastExeTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="isOn">
                is_on = #isOn#
            </isNotEmpty>
            <isNotEmpty prepend="," property="filter">
                filter = #filter#
            </isNotEmpty>
        </dynamic>
		WHERE name = #name#
	</update>
	<update id="update" parameterClass="cn.huanju.edu100.study.model.questionBox.SynTaskInfo">
		UPDATE syn_task_info
        <dynamic prepend="SET">
            <isNotEmpty prepend="," property="id">
                id = #id#
            </isNotEmpty>
            <isNotEmpty prepend="," property="name">
                name = #name#
            </isNotEmpty>
            <isNotEmpty prepend="," property="synTime">
                syn_time = #synTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="synKey">
                syn_key = #synKey#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lastExeTime">
                last_exe_time = #lastExeTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="isOn">
                is_on = #isOn#
            </isNotEmpty>
            <isNotEmpty prepend="," property="filter">
                filter = #filter#
            </isNotEmpty>
        </dynamic>

		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM syn_task_info where id=#id#
	</delete>


	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.SynTaskInfo">
		select
		<include refid="columns"></include>
		FROM syn_task_info a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>
</sqlMap>
