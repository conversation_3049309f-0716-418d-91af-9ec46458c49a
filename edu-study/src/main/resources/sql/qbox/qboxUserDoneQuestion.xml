<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserDoneQuestion">

	<sql id="columns" >
		a.id AS "id",
		a.uid AS "uid",
		a.question_box_id AS "questionBoxId",
		a.`key` AS "key",
		a.question_id_list AS "questionIdList",
		a.update_date AS "updateDate"
	</sql>

	<select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		select
		<include refid="columns" />
		FROM user_done_question a
		WHERE a.id = #id# and a.uid=#uid#
	</select>
	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		INSERT INTO user_done_question (
		`id`,
		`uid`,
		`question_box_id`,
		`key`,
		`question_id_list`
		) VALUES (
		#id#,
		#uid#,
		#questionBoxId#,
		#key#,
		#questionIdList#
		)
	</insert>
	<update id="update" parameterClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		UPDATE user_done_question SET
		question_id_list = #questionIdList#
		WHERE id = #id# and uid=#uid#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM user_done_question where id=#id# and uid=#uid#
	</delete>

	<select id="getByUidQboxIdAndKey" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		SELECT
		<include refid="columns" />
		FROM user_done_question a
		where
		a.uid = #uid#
		and a.question_box_id = #questionBoxId#
		and a.`key` = #key#
		limit 1
	</select>

	<select id="getByUidQboxId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		SELECT
		<include refid="columns" />
		FROM user_done_question a
		where
		a.uid = #uid#
		and a.question_box_id = #questionBoxId#
	</select>

	<select id="getByUidQboxIdAndKeyLike" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		SELECT
		<include refid="columns" />
		FROM user_done_question a
		where
		a.uid = #uid#
		and a.question_box_id = #questionBoxId#
		and a.`key` like concat(#key#,'%')
	</select>

	<select id="getByUidQboxIdListAndKeyLike" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		SELECT
		<include refid="columns" />
		FROM user_done_question a
		where
		a.uid = #uid#
		<isNotEmpty prepend="AND" property="boxIdList">
			a.question_box_id in
			<iterate conjunction="," open="(" close=")" property="boxIdList">
				<![CDATA[
							#boxIdList[]#
						]]>
			</iterate>
		</isNotEmpty>
		and a.`key` like concat(#key#,'%')
	</select>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		SELECT
		<include refid="columns" />
		FROM user_done_question a
		where
		a.uid = #uid#
		<isNotEmpty prepend="and" property="questionBoxId">
			a.question_box_id = #questionBoxId#
		</isNotEmpty>
	</select>

	<select id="getByUidAndQboxId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		SELECT
		<include refid="columns" />
		FROM user_done_question a
		where
		a.uid = #uid#
		and	a.question_box_id = #questionBoxId#
	</select>

    <select id="getByUidAndBoxIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
        SELECT
        <include refid="columns" />
        FROM user_done_question a
        where
        a.uid = #uid#
        and	a.question_box_id in
        <iterate open="(" close=")" conjunction="," property="boxIds">
            #boxIds[]#
        </iterate>

    </select>

	<select id="getKeysByUidAndQboxId" parameterClass="java.util.Map" resultClass="java.lang.String">
		SELECT
		a.`key`
		FROM user_done_question a
		where
		a.uid = #uid#
		and	a.question_box_id = #questionBoxId#
	</select>

	<delete id="deleteByUidAndQboxId" parameterClass="java.util.Map">
		DELETE FROM user_done_question where uid=#uid# and question_box_id = #questionBoxId#
	</delete>
	<delete id="deleteByUidAndQboxIdAndIdList" parameterClass="java.util.Map">
		DELETE FROM user_done_question
		where uid=#uid# and question_box_id = #questionBoxId#
		and id in
		<iterate open="(" close=")" conjunction="," property="idList">
			#idList[]#
		</iterate>
	</delete>
	<insert id="insertBatch" parameterClass="java.util.List">
		INSERT INTO user_done_question (
		`id`,
		`uid`,
		`question_box_id`,
		`key`,
		`question_id_list`
		) VALUES
		<iterate conjunction=",">
			<![CDATA[
			(#insertList[].id#, #insertList[].uid#, #insertList[].questionBoxId#, #insertList[].key#, #insertList[].questionIdList#)
		]]>
		</iterate>

	</insert>

	<insert id="insertBatchNew" parameterClass="java.util.List">
		INSERT INTO user_done_question (
		`id`,
		`uid`,
		`question_box_id`,
		`key`,
		`question_id_list`
		) VALUES
		<iterate conjunction=",">
			<![CDATA[
			(#insertList[].id#, #insertList[].uid#, #insertList[].questionBoxId#, #insertList[].key#, #insertList[].questionIdList#)
		]]>
		</iterate>
		on duplicate key update  `question_id_list` =  values(`question_id_list`)
	</insert>

	<select id="getAllBoxIdByUid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		select
		a.id AS "id",
		a.uid AS "uid",
		a.question_box_id AS "questionBoxId",
		a.`key` AS "key"
		from user_done_question a
		where
		a.uid = #uid#
		group by a.question_box_id
	</select>

	<select id="getUserRecordByQType" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.questionBox.UserDoneQuestion">
		select
		a.id AS "id",
		a.uid AS "uid",
		a.question_box_id AS "questionBoxId",
		a.`key` AS "key"
		from user_done_question a
		where
		a.uid = #uid#
		and a.key like concat(#new:qtype#,'%')
	</select>
</sqlMap>
