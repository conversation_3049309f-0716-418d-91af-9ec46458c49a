<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="StudyPlan">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.StudyPlan">
	   select
				a.id AS "id",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.group_id AS "groupId",
				a.prod_id AS "prodId",
				a.start_time AS "startTime",
				a.name AS "name",
				a.end_time AS "endTime",
				a.create_by AS "createBy.id",
				a.create_date AS "createDate",
				a.update_by AS "updateBy.id",
				a.update_date AS "updateDate"
		FROM study_plan a
		WHERE a.id = #id#
	</select>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.StudyPlan">
		UPDATE study_plan SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="firstCategory">
							first_category = #firstCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="groupId">
							group_id = #groupId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="prodId">
							prod_id = #prodId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="startTime">
							start_time = #startTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="name">
							name = #name#
						</isNotEmpty>
						<isNotEmpty prepend="," property="endTime">
							end_time = #endTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateBy.id">
							update_by = #updateBy.id#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM study_plan where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.StudyPlan">
		SELECT
								a.id AS "id",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.group_id AS "groupId",
					a.prod_id AS "prodId",
					a.start_time AS "startTime",
					a.name AS "name",
					a.end_time AS "endTime",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate"
		FROM study_plan a
			<dynamic prepend="where">
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM study_plan a
			<dynamic prepend="where">
		</dynamic>
	</select>

	<select id="qryStudyPlansByGid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.StudyPlan">
		SELECT
					a.id AS "id",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.group_id AS "groupId",
					a.prod_id AS "prodId",
					a.start_time AS "startTime",
					a.name AS "name",
					a.end_time AS "endTime",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate"
		FROM study_plan a
		where a.group_id = #gid#
	</select>

	<select id="qryPlanPhasesByPids" parameterClass="java.lang.String" resultClass="cn.huanju.edu100.study.model.PlanPhase">
		select
		 	a.id AS "id",
		 	a.plan_id AS "planId",
		 	a.name AS "name",
		 	a.start_time AS "startTime",
		 	a.end_time AS "endTime"
		from plan_phase a where a.plan_id in ($value$);
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.StudyPlan">
		SELECT
								a.id AS "id",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.group_id AS "groupId",
					a.prod_id AS "prodId",
					a.start_time AS "startTime",
					a.name AS "name",
					a.end_time AS "endTime",
					a.create_by AS "createBy.id",
					a.create_date AS "createDate",
					a.update_by AS "updateBy.id",
					a.update_date AS "updateDate"
		FROM study_plan a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
