<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="WhiteList">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.WhiteList">
	   select
				a.id AS "id",
				a.type AS "type",
				a.obj_id AS "objId",
				a.ext_obj_id AS "extObjId"
		FROM white_list a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.WhiteList">
		INSERT INTO white_list(
			type,
			obj_id,
			ext_obj_id
		) VALUES (
			#type#,
			#objId#,
			#extObjId#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<insert id="insertBatch" parameterClass="java.util.List">
		INSERT INTO white_list(
			type,
			obj_id,
			ext_obj_id
		) VALUES
		<iterate conjunction=",">
		<![CDATA[
			(#insertList[].type#, #insertList[].objId#, #insertList[].extObjId#)
		]]>
		</iterate>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.WhiteList">
		UPDATE white_list SET
			<dynamic prepend=",">
						<isNotEmpty prepend="," property="type">
							type = #type#
						</isNotEmpty>
						<isNotEmpty prepend="," property="objId">
							obj_id = #objId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="extObjId">
							ext_obj_id = #extObjId#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM white_list where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.WhiteList">
		SELECT
								a.id AS "id",
					a.type AS "type",
					a.obj_id AS "objId",
					a.ext_obj_id AS "extObjId"
		FROM white_list a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="objId">
						a.obj_id = #objId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM white_list a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="type">
						a.type = #type#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.WhiteList">
		SELECT
								a.id AS "id",
					a.type AS "type",
					a.obj_id AS "objId",
					a.ext_obj_id AS "extObjId"
		FROM white_list a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
