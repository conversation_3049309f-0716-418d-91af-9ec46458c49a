<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="GroupStudent">

	<!-- 更新状态 -->
	<update id="updateType" parameterClass="java.util.Map">
		UPDATE group_student SET
			type = #type#
		WHERE group_id = #groupId# AND suid = #suid# and type = 0
	</update>

	<select id="qryGroupStudentByGroupIdAndUid" parameterClass="java.util.Map"
            resultClass="cn.huanju.edu100.study.model.GroupStudent">
        SELECT
			a.id AS "id",
			a.group_id AS "groupId",
			a.suid AS "suid",
			a.memo AS "memo"
		FROM group_student a
		WHERE a.group_id=#groupId# AND a.suid = #uid# limit 1
    </select>
</sqlMap>
