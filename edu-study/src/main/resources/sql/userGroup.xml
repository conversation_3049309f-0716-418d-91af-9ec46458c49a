<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserGroup">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserGroup">
	   select
				a.id AS "id",
				a.first_category AS "firstCategory",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.user_group_id AS "userGroupId",
				a.name AS "name",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.freeze AS "freeze"
		FROM user_group a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.UserGroup">
		INSERT INTO user_group(
			first_category,
			second_category,
			category_id,
			user_group_id,
			name,
			start_time,
			end_time
		) VALUES (
			#firstCategory#,
			#secondCategory#,
			#categoryId#,
			#userGroupId#,
			#name#,
			#startTime#,
			#endTime#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.UserGroup">
		UPDATE user_group SET
			<dynamic prepend=",">
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM user_group where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserGroup">
		SELECT
								a.id AS "id",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.user_group_id AS "userGroupId",
					a.name AS "name",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.freeze AS "freeze"
		FROM user_group a
			<dynamic prepend="where">
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="qryGroupByUid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserGroup">
		SELECT
					a.id AS "id",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.user_group_id AS "userGroupId",
					a.name AS "name",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.freeze AS "freeze"
		FROM user_group a, group_student b
		where a.id = b.group_id and b.suid = #suid#
	</select>

	<select id="qryTeachersByGid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.GroupTeacher">
		SELECT
			a.id AS "id",
			a.group_id AS "groupId",
			a.type AS "type",
			a.tuid AS "tuid"
		FROM group_teacher a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="groupId">
				a.group_id = #groupId#
			</isNotEmpty>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM user_group a
			<dynamic prepend="where">
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserGroup">
		SELECT
								a.id AS "id",
					a.first_category AS "firstCategory",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.user_group_id AS "userGroupId",
					a.name AS "name",
					a.start_time AS "startTime",
					a.end_time AS "endTime",
					a.freeze AS "freeze"
		FROM user_group a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
