<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="PaperAssessmentScore">

    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.evaluation.PaperAssessmentScore">
        SELECT
            a.id AS "id",
            a.user_answer_id AS "userAnswerId",
            a.paper_id AS "paperId",
            a.score AS "score",
            a.right_rate AS "rightRate",
            a.time_id AS "timeId",
            a.create_date AS "createDate",
            a.update_date AS "updateDate"
        FROM paper_assessment_score a
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="id">
                a.id = #id#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userAnswerId">
                a.user_answer_id = #userAnswerId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="paperId">
                a.paper_id = #paperId#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="getAssessmentAnswerCountByPaperId" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT
           count(1)
        FROM paper_assessment_score a where a.paper_id=#paperId#
    </select>

    <select id="getAssessmentAnswerCountByPaperIdAndRightRate" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT
           count(1)
        FROM paper_assessment_score a where a.paper_id=#paperId# and <![CDATA[ a.right_rate<#rightRate# ]]>
    </select>

</sqlMap>
