<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="StudyReportDetail">

    <select id="qryAllStudyTaskCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1) FROM study_task a WHERE a.plan_id=#pid#
	</select>

	<select id="qryFinishStudyTaskCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		select count(1) from student_task b
		where
		b.task_id in ($tids$)
		and b.state = 2
		and b.uid = #uid#
	</select>

    <select id="qryStudyLogList" parameterClass="java.util.Map"
            resultClass="cn.huanju.edu100.study.model.StudyReportDetail">
		SELECT
			c.knowledge_id AS "knowledgeId",
			c.answer_num AS "answerNum",
			c.wrong_num AS "wrongNum",
			c.wrong_ids AS "wrongIds",
		    d.task_id AS "taskId"
		FROM
			study_log c,
			( SELECT * FROM student_task b
			  WHERE b.task_id IN (SELECT a.id AS "id" FROM study_task a WHERE a.plan_id = #pid#)
			) d
		WHERE
			c.uid = #uid#
		AND c.stu_task_id = d.id
    </select>

</sqlMap>
