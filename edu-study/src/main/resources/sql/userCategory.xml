<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserCategory">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserCategory">
	   select
				a.id AS "id",
				a.uid AS "uid",
				a.appid AS "appid",
				a.second_category AS "secondCategory"
		FROM user_category a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.UserCategory">
		INSERT INTO user_category(
			uid,
			appid,
			second_category
		) VALUES (
			#uid#,
			#appid#,
			#secondCategory#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.UserCategory">
		UPDATE user_category
			<dynamic prepend="SET">
						<isNotEmpty prepend="," property="uid">
							uid = #uid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="appid">
							appid = #appid#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM user_category where id=#id#
	</delete>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserCategory">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.appid AS "appid",
					a.second_category AS "secondCategory"
		FROM user_category a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="appid">
						a.appid = #appid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM user_category a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="appid">
						a.appid = #appid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserCategory">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.appid AS "appid",
					a.second_category AS "secondCategory"
		FROM user_category a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

</sqlMap>
