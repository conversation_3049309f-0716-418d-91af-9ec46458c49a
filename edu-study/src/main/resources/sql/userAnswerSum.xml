<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserAnswerSum">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
	   select
				a.id AS "id",
				a.uid AS "uid",
				a.user_answer_id AS "userAnswerId",
				a.user_homework_id AS "userHomeworkId",
				a.paragraph_id AS "paragraphId",
				a.lesson_id AS "lessonId",
				a.m_class_id AS "mClassId",
				a.paper_id AS "paperId",
				a.question_id AS "questionId",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.score AS "score",
				a.state AS "state",
				a.tuid AS "tuid",
				a.comment AS "comment",
				a.comment_time AS "commentTime",
                a.create_date AS "createDate"
		FROM user_answer_sum a
		WHERE a.id = #id#  and a.uid = #uid#
	</select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.UserAnswerSum">
        INSERT INTO user_answer_sum(
        uid,
        user_answer_id,
        user_homework_id,
        paragraph_id,
        lesson_id,
        m_class_id,
        paper_id,
        question_id,
        start_time,
        end_time,
        score,
        create_date,
        update_date,
        state,
        is_al
        ) VALUES (
        #uid#,
        #userAnswerId#,
        #userHomeworkId#,
        #paragraphId#,
        #lessonId#,
        #mClassId#,
        #paperId#,
        #questionId#,
        #startTime#,
        #endTime#,
        #score#,
        now(),
        now(),
        #state#,
        #isAl#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <insert id="insertSharding" parameterClass="cn.huanju.edu100.study.model.UserAnswerSum">
        INSERT INTO user_answer_sum(
        id,
        uid,
        user_answer_id,
        user_homework_id,
        paragraph_id,
        lesson_id,
        m_class_id,
        paper_id,
        question_id,
        start_time,
        end_time,
        score,
        create_date,
        update_date,
        state,
        is_al
        ) VALUES (
        #id#,
        #uid#,
        #userAnswerId#,
        #userHomeworkId#,
        #paragraphId#,
        #lessonId#,
        #mClassId#,
        #paperId#,
        #questionId#,
        #startTime#,
        #endTime#,
        #score#,
        now(),
        now(),
        #state#,
        #isAl#
        )
    </insert>

    <update id="update" parameterClass="cn.huanju.edu100.study.model.UserAnswerSum">
        UPDATE user_answer_sum SET update_date=now()
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="uid">
                uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="," property="paragraphId">
                paragraph_id = #paragraphId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lessonId">
                lesson_id = #lessonId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="paperId">
                paper_id = #paperId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="questionId">
                question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="startTime">
                start_time = #startTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="endTime">
                end_time = #endTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="score">
                score = #score#
            </isNotEmpty>
            <isNotEmpty prepend="," property="state">
                state = #state#
            </isNotEmpty>
            <isNotEmpty prepend="," property="tuid">
                tuid = #tuid#
            </isNotEmpty>
            <isNotEmpty prepend="," property="comment">
                comment = #comment#
            </isNotEmpty>
            <isNotEmpty prepend="," property="commentTime">
                comment_time = #commentTime#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id# and uid = #uid#
    </update>

    <delete id="delete" parameterClass="java.util.Map">
        DELETE FROM user_answer_sum where id=#id# and uid = #uid#
    </delete>

    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.user_answer_id AS "userAnswerId",
        a.user_homework_id AS "userHomeworkId",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.m_class_id AS "mClassId",
        a.paper_id AS "paperId",
        a.question_id AS "questionId",
        a.start_time AS "startTime",
        a.end_time AS "endTime",
        a.score AS "score",
        a.state AS "state",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime"
        FROM user_answer_sum a
        <dynamic prepend="where">
            <!--<isNotEmpty prepend="AND" property="uid">-->
                a.uid = #uid#
            <!--</isNotEmpty>-->
            <isNotEmpty prepend="AND" property="userAnswerId">
                a.user_answer_id = #userAnswerId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userHomeworkId">
            	a.user_homework_id = #userHomeworkId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="findAnswerSum" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.user_answer_id AS "userAnswerId",
        a.user_homework_id AS "userHomeworkId",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.m_class_id AS "mClassId",
        a.paper_id AS "paperId",
        a.question_id AS "questionId",
        a.start_time AS "startTime",
        a.end_time AS "endTime",
        a.score AS "score",
        a.state AS "state",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime",
        a.is_al AS "isAl"
        FROM user_answer_sum a
        where a.uid = #uid#
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="userAnswerId">
                a.user_answer_id = #userAnswerId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userHomeworkIds">
                a.user_homework_id in
                <iterate open="(" close=")" conjunction="," property="userHomeworkIds" >
                    #userHomeworkIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionIdList">
                a.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findAnswerSumAnswerOrHomework" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.user_answer_id AS "userAnswerId",
        a.user_homework_id AS "userHomeworkId",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.m_class_id AS "mClassId",
        a.paper_id AS "paperId",
        a.question_id AS "questionId",
        a.start_time AS "startTime",
        a.end_time AS "endTime",
        a.score AS "score",
        a.state AS "state",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime",
        a.is_al AS "isAl",
        a.create_date AS "createDate",
        a.update_date AS "updateDate"
        FROM user_answer_sum a
        where a.uid = #uid#
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="userAnswerId">
                ( a.user_answer_id = #userAnswerId#  or a.user_homework_id = #userAnswerId# )
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionIdList">
                a.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findHomeworkSum" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.user_answer_id AS "userAnswerId",
        a.user_homework_id AS "userHomeworkId",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.m_class_id AS "mClassId",
        a.paper_id AS "paperId",
        a.question_id AS "questionId",
        a.start_time AS "startTime",
        a.end_time AS "endTime",
        a.score AS "score",
        a.state AS "state",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.update_date AS "updateDate",
        a.comment_time AS "commentTime",
        a.create_date AS "createDate"
        FROM user_answer_sum a
        where a.uid = #uid#
        and a.user_homework_id = #userHomeworkId#
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="questionIdList">
                a.question_id in
		        <iterate open="(" close=")" conjunction="," property="questionIdList" >
		                #questionIdList[]#
		        </iterate>
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findAnswerSumByMClass" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.user_answer_id AS "userAnswerId",
        a.user_homework_id AS "userHomeworkId",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.m_class_id AS "mClassId",
        a.paper_id AS "paperId",
        a.question_id AS "questionId",
        a.start_time AS "startTime",
        a.end_time AS "endTime",
        a.score AS "score",
        a.state AS "state",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime",
        a.create_date AS "createDate"
        FROM user_answer_sum a
        where a.uid = #uid#
        and a.m_class_id = #mClassId#
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="questionIdList">
                a.question_id in
		        <iterate open="(" close=")" conjunction="," property="questionIdList" >
		                #questionIdList[]#
		        </iterate>
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT count(1)
        FROM user_answer_sum a
        where a.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="userAnswerId">
                a.user_answer_id = #userAnswerId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userHomeworkId">
                a.user_homework_id = #userHomeworkId#
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findAllList" parameterClass="java.util.Map"
            resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
            SELECT
            a.id AS "id",
            a.uid AS "uid",
            a.user_answer_id as "userAnswerId",
            a.user_homework_id AS "userHomeworkId",
            a.paragraph_id AS "paragraphId",
            a.lesson_id AS "lessonId",
            a.m_class_id AS "mClassId",
            a.paper_id AS "paperId",
            a.question_id AS "questionId",
            a.start_time AS "startTime",
            a.end_time AS "endTime",
            a.score AS "score",
            a.state AS "state",
            a.tuid AS "tuid",
            a.comment AS "comment",
            a.comment_time AS "commentTime",
            a.create_date AS "createDate",
            a.is_al AS "isAl"
            FROM user_answer_sum a
            where a.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="userAnswerId">
                a.user_answer_id = #userAnswerId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userHomeworkId">
                a.user_homework_id = #userHomeworkId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="isAl">
                a.is_al = #isAl#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="getOne" parameterClass="cn.huanju.edu100.study.model.UserAnswerSum"
            resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.user_answer_id as "userAnswerId",
        a.user_homework_id AS "userHomeworkId",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.m_class_id AS "mClassId",
        a.paper_id AS "paperId",
        a.question_id AS "questionId",
        a.start_time AS "startTime",
        a.end_time AS "endTime",
        a.score AS "score",
        a.state AS "state",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime"
        FROM user_answer_sum a
        WHERE a.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="userAnswerId">
                a.user_answer_id = #userAnswerId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotEmpty>
        </dynamic>
        limit 1
    </select>

    <select id="getByPaperId" parameterClass="java.util.Map"
            resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
		select
			a.id AS "id",
			a.uid AS "uid",
			a.user_answer_id AS "userAnswerId",
			a.user_homework_id AS "userHomeworkId",
			a.paragraph_id AS "paragraphId",
			a.lesson_id AS "lessonId",
			a.m_class_id AS "mClassId",
			a.paper_id AS "paperId",
			a.question_id AS "questionId",
			a.start_time AS "startTime",
			a.end_time AS "endTime",
			a.score AS "score",
			a.state AS "state",
			a.tuid AS "tuid",
			a.comment AS "comment",
			a.comment_time AS "commentTime"
		FROM user_answer_sum a
		WHERE a.uid = #uid# and a.paper_id = #paperId#
	</select>


    <select id="findAnswerSumQuestion" resultClass="com.hqwx.study.entity.UserAnswerDetail">
        SELECT
        s.user_answer_id, d.sum_id, d.question_id,d.topic_id,d.is_right
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.user_answer_id IN

        <iterate open="(" close=")" conjunction="," property="uAnswerIdList" >
            #uAnswerIdList[]#
        </iterate>

        AND s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
    </select>

    <select id="getQuestionListByAnswerId" resultClass="com.hqwx.study.entity.UserAnswerDetail">
        SELECT
            s.user_answer_id as userAnswerId,
            d.sum_id as sumId,
            d.question_id as questionId,
            d.topic_id as topicId,
            d.is_right as isRight,
            d.create_date as createDate
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.user_answer_id = #answerId#
        AND s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="questionId">
                d.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionIdList">
                d.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                <![CDATA[ d.create_date >= #startTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ d.create_date <= #endTime# ]]>
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="getQuestionListByHomeworkAnswerId" resultClass="com.hqwx.study.entity.UserAnswerDetail">
        SELECT
            s.user_homework_id as homeworkAnswerId,
            d.sum_id as sumId,
            d.question_id as questionId,
            d.topic_id as topicId,
            d.is_right as isRight,
            d.create_date as createDate,
            d.score as score
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.user_homework_id = #homeworkAnswerId#
        AND s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="questionId">
                d.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionIdList">
                d.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                <![CDATA[ d.create_date >= #startTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ d.create_date <= #endTime# ]]>
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="getAnswerDetailsByHomeworkAnswerIds" resultClass="com.hqwx.study.entity.UserAnswerDetail">
        SELECT
        s.user_homework_id as userHomeworkId,
        d.sum_id as sumId,
        d.question_id as questionId,
        d.topic_id as topicId,
        d.is_right as isRight,
        d.answer as answerStr,
        d.create_date as createDate,
        d.score as score
        FROM user_answer_detail d, user_answer_sum s
        WHERE
        s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="homeworkAnswerIds">
                s.user_homework_id in
                <iterate open="(" close=")" conjunction="," property="homeworkAnswerIds" >
                    #homeworkAnswerIds[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="getAnswerDetailsByAnswerIds" resultClass="com.hqwx.study.entity.UserAnswerDetail">
        SELECT
        s.user_homework_id as userHomeworkId,
        s.user_answer_id as userAnswerId,
        d.sum_id as sumId,
        d.question_id as questionId,
        d.topic_id as topicId,
        d.is_right as isRight,
        d.create_date as createDate,
        d.score as score
        FROM user_answer_detail d, user_answer_sum s
        WHERE
        s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="userAnswerIds">
                s.user_answer_id in
                <iterate open="(" close=")" conjunction="," property="userAnswerIds" >
                    #userAnswerIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="homeworkAnswerIds">
                s.user_homework_id in
                <iterate open="(" close=")" conjunction="," property="homeworkAnswerIds" >
                    #homeworkAnswerIds[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="countQuestionListByHomeworkAnswerId" resultClass="java.lang.Integer">
        SELECT
            count(1)
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.user_homework_id = #homeworkAnswerId#
        AND s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="questionId">
                d.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionIdList">
                d.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                <![CDATA[ d.create_date >= #startTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ d.create_date <= #endTime# ]]>
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="countQuestionListByAnswerId" resultClass="java.lang.Integer">
        SELECT
            count(1)
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.user_answer_id = #answerId#
        AND s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="questionId">
                d.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionIdList">
                d.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                <![CDATA[ d.create_date >= #startTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ d.create_date <= #endTime# ]]>
            </isNotEmpty>
        </dynamic>
    </select>
    <select id="findUserAnswerPaperCompleteCount" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        select
            id as answerId,
            paper_id as paperId,
            max(answer_num) as answerNum
        from user_answer a
        where a.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="paperIds">
                a.paper_id in
                <iterate open="(" close=")" conjunction="," property="paperIds" >
                    #paperIds[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
        group by id,paper_id
        limit 5000

    </select>
    <select id="findUserAnswerSumTotalCount" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        select
            user_answer_id answerId,
            count(id) as answerTotalNum
        from user_answer_sum a
        where a.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="answerIds">
                a.user_answer_id in
                <iterate open="(" close=")" conjunction="," property="answerIds" >
                    #answerIds[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
        group by user_answer_id
    </select>
    <select id="findUserHomeworkCompleteCount" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        select count(*) as finishHomeworkCount from (
            select a.question_id
            from user_answer_detail a
            RIGHT JOIN user_answer_sum b ON a.sum_id = b.id
            RIGHT JOIN user_answer_homework c ON b.user_homework_id = c.id
            WHERE a.uid = #uid# AND b.uid = #uid# AND c.uid = #uid#
            and  a.is_right != 3
            <dynamic prepend="AND">
                <isNotEmpty prepend="AND" property="questionIds">
                    a.question_id in
                    <iterate open="(" close=")" conjunction="," property="questionIds" >
                        #questionIds[]#
                    </iterate>
                </isNotEmpty>
                <isNotEmpty prepend="AND" property="startTime">
                    <![CDATA[ a.create_date >= #startTime# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="AND" property="endTime">
                    <![CDATA[ a.create_date <= #endTime# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="AND" property="goodsId">
                    c.goods_id = #goodsId#
                </isNotEmpty>
            </dynamic>
            group by question_id
        ) b
    </select>

    <select id="findUserHomeworkCompleteQuestionIds" parameterClass="java.util.Map" resultClass="java.lang.Long">
        select a.question_id
        from user_answer_detail a
        RIGHT JOIN user_answer_sum b ON a.sum_id = b.id
        RIGHT JOIN user_answer_homework c ON b.user_homework_id = c.id
        WHERE a.uid = #uid# AND b.uid = #uid# AND c.uid = #uid#
        and  a.is_right != 3
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="questionIds">
                a.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIds" >
                    #questionIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                <![CDATA[ a.create_date >= #startTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ a.create_date <= #endTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="goodsId">
                c.goods_id = #goodsId#
            </isNotEmpty>
        </dynamic>
        group by question_id
    </select>


    <select id="countWrongNumByAnswerId" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT
        count(1)
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.user_answer_id = #answerId#
        AND s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        AND d.is_right in (0,1)
    </select>

    <select id="countWrongNumByHomeworkId" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT
        count(1)
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.user_homework_id = #homeworkId#
        AND s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        AND d.is_right in (0,1)
    </select>

    <select id="findUserAllQuestion" parameterClass="cn.huanju.edu100.study.model.UserAnswerSum" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        a.question_id AS "questionId"
        FROM user_answer_sum a
        WHERE a.uid = #uid#
        AND a.is_al = 1
        group by question_id
    </select>

    <select id="findUserAllQuestionHistoryPaper" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerHistory">
        select
        a.id as answer_id,a.uid as uid,a.paper_id as paper_id,1 as answer_type,a.score as score,a.update_date as update_date,b.question_id as question_id,b.score as sub_score,b.id as sum_id,
        c.is_right
        from user_answer a
        inner join user_answer_sum b
        on a.id=b.user_answer_id
        inner join user_answer_detail c
        on b.id = c.sum_id
        where a.uid = #uid# and b.uid = #uid# and c.uid = #uid#
    </select>

    <select id="findUserAllQuestionHistoryHomeWork" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerHistory">
        select
        a.id as answer_id,a.uid as uid,a.obj_id as paper_id,2 as answer_type,a.score as score,a.update_date as update_date,b.question_id as question_id,b.score as sub_score,b.id as sum_id,
        c.is_right
        from user_answer_homework a
        INNER JOIN user_answer_sum b
        ON a.id=b.user_homework_id
        inner join user_answer_detail c
        on b.id = c.sum_id
        where a.uid = #uid# and b.uid = #uid# and c.uid = #uid#

        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>


    <select id="findUserAnswerDetailByIdAndUid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.PaperUserAnswerDetail">
        select
        a.id as id,
        a.paper_id,
        a.score as totalScore,
        b.question_id as qid,
        b.score ,
        c.is_right as isRight
        from user_answer a
        LEFT JOIN user_answer_sum b
        ON a.id = b.user_answer_id and b.uid = #uid#
        LEFT JOIN user_answer_detail c
        ON b.id = c.sum_id and c.uid =#uid#
        where a.id = #id# and a.uid = #uid#;
    </select>

    <select id="findUserAnswerHomeworkDetailByIdAndUid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.PaperUserAnswerDetail">
        select
        a.id as userAnswerId,
        a.obj_id,
        a.score as totalScore,
        b.question_id as qid,
        b.score ,
        c.is_right as isRight
        from user_answer_homework a
        LEFT JOIN user_answer_sum b
        ON a.id = b.user_homework_id and b.uid = #uid#
        LEFT JOIN user_answer_detail c
        ON b.id = c.sum_id and c.uid =#uid#
        where a.id = #id# and a.uid = #uid#;
    </select>

    <select id="getUserAnswerListByUidAndDate" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.AlPaperReport">
        select
        a.id as userAnswerId,
        a.uid,
        a.obj_id as categoryId,
        a.paper_id as resourceId,
        a.start_time as lastStudyTime,
        (case WHEN b.first_time > 0 THEN 1 ELSE 0 END) as isFirst

        from (
        select id,uid,obj_id,paper_id,start_time from user_answer
        where uid = #uid# and obj_type = 201 and obj_id = #categoryId# and start_time BETWEEN #startTime# and #endTime# ) a
        LEFT JOIN

        (SELECT paper_id,MIN(start_time) as first_time from user_answer
        where uid = #uid# and obj_type = 201 and obj_id = #categoryId# ) b
        ON a.paper_id = b.paper_id and b.first_time  <![CDATA[< a.start_time ]]>

    </select>

    <select id="getUserAnswerHomeworkListByUidAndDate" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.AlPaperReport">
        select
        a.id as userAnswerId,
        a.uid,
        a.obj_id as resourceId,
        a.start_time as lastStudyTime,
        (case WHEN b.first_time > 0 THEN 1 ELSE 0 END) as isFirst

        from (
        select id,uid,obj_id,start_time from user_answer_homework
        where uid = #uid# and obj_type = 202
        and start_time BETWEEN #startTime# and #endTime# and obj_id in
            <iterate conjunction="," open="(" close=")" property="resIds">
            #resIds[]#
            </iterate>) a
        LEFT JOIN

        (SELECT obj_id, min(start_time) as first_time from user_answer_homework
        where uid = #uid# and obj_type = 202  and obj_id in
            <iterate conjunction="," open="(" close=")" property="resIds">
            #resIds[]#
            </iterate>
        GROUP BY uid,obj_id
        ) b
        ON a.obj_id = b.obj_id and b.first_time <![CDATA[< a.start_time ]]>

    </select>

    <select id="getLastUserAnswerSumByQuestionIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        select *
        from (
            select
            id AS "id",
            uid AS "uid",
            user_answer_id AS "userAnswerId",
            user_homework_id AS "userHomeworkId",
            paragraph_id AS "paragraphId",
            lesson_id AS "lessonId",
            m_class_id AS "mClassId",
            paper_id AS "paperId",
            question_id AS "questionId",
            start_time AS "startTime",
            end_time AS "endTime",
            score AS "score",
            state AS "state",
            tuid AS "tuid",
            comment AS "comment",
            comment_time AS "commentTime"
            FROM user_answer_sum
            where uid = #uid#
            <dynamic prepend="and">
                <isNotEmpty prepend="AND" property="questionIdList">
                    question_id in
                    <iterate open="(" close=")" conjunction="," property="questionIdList" >
                        #questionIdList[]#
                    </iterate>
                </isNotEmpty>
            </dynamic>
            order by create_date desc limit 50000)
        a group by a.questionId order by a.id desc
    </select>

    <select id="getLastUserAnswerSumAndDetailByQuestionIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        select *
        from (
        SELECT
        s.id AS "id",
        s.question_id as questionId,
        d.is_right as isRight
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        AND s.is_al=1
        AND s.user_homework_id is not null
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="questionIdList">
                s.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
        order by s.create_date desc limit 50000)
        a group by a.questionId order by a.id desc
    </select>

    <select id="getLastUserAnswerSumAndDetailByQuestionIdsV2" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        select *
        from (
        SELECT
        s.id AS "id",
        s.question_id as questionId,
        d.is_right as isRight
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        AND s.is_al=1
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="questionIdList">
                s.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
        order by s.create_date desc limit 50000)
        a group by a.questionId order by a.id desc
    </select>



    <select id="findStudyCenterLastHomeworkCompleteCount" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        SELECT us.user_homework_id as userHomeworkId, count(1) as answerTotalNum
        FROM user_answer_sum us, user_answer_detail ud
        where
        ud.sum_id = us.id and ud.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="userHomeworkIds">
                us.user_homework_id in
                <iterate open="(" close=")" conjunction="," property="userHomeworkIds" >
                    #userHomeworkIds[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
        AND ud.is_right !=3
        GROUP BY us.user_homework_id
    </select>


    <select id="findStudyCenterLastHomeworkSumList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        us.id AS "id",
        us.user_homework_id AS "userHomeworkId"
        FROM user_answer_sum us
        where
        us.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="userHomeworkIds">
                us.user_homework_id in
                <iterate open="(" close=")" conjunction="," property="userHomeworkIds" >
                    #userHomeworkIds[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
    </select>


    <select id="getQuestionListByAnswerIds" resultClass="com.hqwx.study.entity.UserAnswerDetail">
        SELECT
        s.user_answer_id as userAnswerId,
        d.sum_id as sumId,
        d.question_id as questionId,
        d.topic_id as topicId,
        d.options_id AS "optionsId",
        d.answer AS "answerStr",
        d.pic AS "pic",
        d.mp3 AS "mp3",
        d.file AS "file",
        d.is_right AS "isRight",
        d.score AS "score",
        d.tuid AS "tuid",
        d.comment AS "comment",
        d.comment_time AS "commentTime",
        d.create_date as createDate
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="questionId">
                d.question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionIdList">
                d.question_id in
                <iterate open="(" close=")" conjunction="," property="questionIdList" >
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="answerIds">
                s.user_answer_id in
                <iterate open="(" close=")" conjunction="," property="answerIds" >
                    #answerIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                <![CDATA[ d.create_date >= #startTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ d.create_date <= #endTime# ]]>
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="findAlSubmitQuestionList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        s.id AS "id",
        d.question_id as questionId,
        d.is_right as isRight
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        AND s.is_al=1
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="userAnswerIds">
                s.user_answer_id in
                <iterate open="(" close=")" conjunction="," property="userAnswerIds" >
                    #userAnswerIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userHomeworkIds">
                s.user_homework_id in
                <iterate open="(" close=")" conjunction="," property="userHomeworkIds" >
                    #userHomeworkIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionIdList">
                d.question_id IN
                <iterate open="(" close=")" conjunction="," property="questionIdList">
                    #questionIdList[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findAlSubmitQuestionListByPaperOrHomework" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        s.id AS "id",
        d.question_id as questionId,
        d.is_right as isRight
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        AND s.is_al=1
        <dynamic prepend="AND">
            <!-- 处理 userAnswerIds 不为空且 userHomeworkIds 不为空的情况 -->
            <isNotEmpty property="userAnswerIds">
                <isNotEmpty property="userHomeworkIds">
                    (s.user_answer_id IN
                    <iterate open="(" close=")" conjunction="," property="userAnswerIds">
                        #userAnswerIds[]#
                    </iterate>
                    OR s.user_homework_id IN
                    <iterate open="(" close=")" conjunction="," property="userHomeworkIds">
                        #userHomeworkIds[]#
                    </iterate>
                    )
                </isNotEmpty>
                <!-- 处理 userAnswerIds 不为空且 userHomeworkIds 为空的情况 -->
                <isEmpty property="userHomeworkIds">
                    s.user_answer_id IN
                    <iterate open="(" close=")" conjunction="," property="userAnswerIds">
                        #userAnswerIds[]#
                    </iterate>
                </isEmpty>
            </isNotEmpty>
            <!-- 处理 userAnswerIds 为空且 userHomeworkIds 不为空的情况 -->
            <isEmpty property="userAnswerIds">
                <isNotEmpty property="userHomeworkIds">
                    s.user_homework_id IN
                    <iterate open="(" close=")" conjunction="," property="userHomeworkIds">
                        #userHomeworkIds[]#
                    </iterate>
                </isNotEmpty>
            </isEmpty>
        </dynamic>
    </select>

    <select id="findPaperAnswerSummaryList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        s.user_answer_id as userAnswerId,
        s.paper_id as paperId,
        s.question_id as questionId,
        d.is_right as isRight
        FROM user_answer_detail d, user_answer_sum s
        WHERE s.id=d.sum_id
        AND d.uid = #uid#
        AND s.uid = #uid#
        AND s.paper_id in
        <iterate open="(" close=")" conjunction="," property="paperIdList" >
            #paperIdList[]#
        </iterate>
        order by s.create_date desc
    </select>

    <select id="findUserPaperLastAnswerId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
        a.paper_id as paperId, max(a.user_answer_id) userAnswerId
        FROM user_answer_sum a inner join user_answer b on a.uid=b.uid and a.user_answer_id = b.id
        WHERE a.uid=#uid# and b.uid=#uid#
        AND a.paper_id in
        <iterate open="(" close=")" conjunction="," property="paperIdList" >
            #paperIdList[]#
        </iterate>
        <isNotEmpty prepend="AND" property="objTypeList">
            b.obj_type IN
            <iterate conjunction="," open="(" close=")" property="objTypeList">
                #objTypeList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="goodsId">
            b.goods_id=#goodsId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="productId">
            b.product_id=#productId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="paperType">
            b.paper_type=#paperType#
        </isNotEmpty>
        GROUP BY a.paper_id
    </select>

    <select id="findPaperAnswerSumDetail" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserAnswerSum">
        SELECT
            s.user_answer_id as userAnswerId,
            s.paper_id as paperId,
            s.question_id as questionId,
            d.is_right as isRight
        FROM
            user_answer_detail d,
            user_answer_sum s
        WHERE
            d.uid=#uid#
            AND s.uid=#uid#
            AND s.id=d.sum_id
            AND s.user_answer_id IN
            <iterate open="(" close=")" conjunction="," property="userAnswerIdList" >
                #userAnswerIdList[]#
            </iterate>
    </select>

    <select id="findErrorPaperAnswerSumDetailGroupByAnswerId" parameterClass="java.util.Map" resultClass="com.hqwx.study.vo.UserAnswerErrorQuestionVo">
        select dd.user_answer_id as answerId,count(*) as errorQuestionCount,group_concat(dd.question_id) as errorQuestionIdsStr
        from (select s.user_answer_id as user_answer_id,d.question_id as question_id
        from user_answer_sum s,user_answer_detail d
        where  d.uid=#uid#
        AND s.uid=#uid#
        AND s.id=d.sum_id
        AND d.is_right!=2
        AND d.answer!='[]'
        AND s.user_answer_id in
        <iterate open="(" close=")" conjunction="," property="answerIdList" >
            #answerIdList[]#
        </iterate>
        group by s.user_answer_id,d.question_id)dd
        group by dd.user_answer_id
    </select>

    <select id="findErrorHomeworkAnswerSumDetailGroupByAnswerId" parameterClass="java.util.Map" resultClass="com.hqwx.study.vo.UserAnswerErrorQuestionVo">
        select dd.user_homework_id as answerId,count(*) as errorQuestionCount,group_concat(dd.question_id) as errorQuestionIdsStr
        from (select s.user_homework_id as user_homework_id,d.question_id as question_id
        from user_answer_sum s,user_answer_detail d
        where  d.uid=#uid#
        AND s.uid=#uid#
        AND s.id=d.sum_id
        AND d.is_right!=2
        AND d.answer!='[]'
        AND s.user_homework_id in
        <iterate open="(" close=")" conjunction="," property="answerIdList" >
            #answerIdList[]#
        </iterate>
        group by s.user_homework_id,d.question_id)dd
        group by dd.user_homework_id
    </select>
</sqlMap>
