<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tikuForecastScore">
  <resultMap class="cn.huanju.edu100.study.model.report.TikuForecastScore" id="BaseResultMap">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="box_id" jdbcType="BIGINT" property="boxId" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="user_answer_id" jdbcType="BIGINT" property="userAnswerId" />
    <result column="score" jdbcType="DOUBLE" property="score" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, uid, box_id, category_id, user_answer_id, score, create_date, update_date
  </sql>
  <select id="selectByPrimaryKey" parameterClass="cn.huanju.edu100.study.model.report.TikuForecastScore" resultMap="BaseResultMap">
    select
    <include refid="tikuForecastScore.Base_Column_List" />
    from tiku_forecast_score
    where id = #id:BIGINT#
  </select>
  <delete id="deleteByPrimaryKey" parameterClass="cn.huanju.edu100.study.model.report.TikuForecastScore">
    delete from tiku_forecast_score
    where id = #id:BIGINT#
  </delete>
  <insert id="insert" parameterClass="cn.huanju.edu100.study.model.report.TikuForecastScore">
    insert into tiku_forecast_score (id, uid, box_id, category_id, user_answer_id,
      score, create_date, update_date)
    values (#id:BIGINT#, #uid:BIGINT#, #boxId:BIGINT#, #categoryId:BIGINT#, #userAnswerId:BIGINT#,
      #score:DOUBLE#, #createDate:TIMESTAMP#, #updateDate:TIMESTAMP#)
  </insert>
  <insert id="insertSelective" parameterClass="cn.huanju.edu100.study.model.report.TikuForecastScore">
    insert into tiku_forecast_score
    <dynamic prepend="(">
      <isNotNull prepend="," property="id">
        id
      </isNotNull>
      <isNotNull prepend="," property="uid">
        uid
      </isNotNull>
      <isNotNull prepend="," property="boxId">
        box_id
      </isNotNull>
      <isNotNull prepend="," property="categoryId">
        category_id
      </isNotNull>
      <isNotNull prepend="," property="userAnswerId">
        user_answer_id
      </isNotNull>
      <isNotNull prepend="," property="score">
        score
      </isNotNull>
      <isNotNull prepend="," property="createDate">
        create_date
      </isNotNull>
      <isNotNull prepend="," property="updateDate">
        update_date
      </isNotNull>
      )
    </dynamic>
    values
    <dynamic prepend="(">
      <isNotNull prepend="," property="id">
        #id:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="uid">
        #uid:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="boxId">
        #boxId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="categoryId">
        #categoryId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="userAnswerId">
        #userAnswerId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="score">
        #score:DOUBLE#
      </isNotNull>
      <isNotNull prepend="," property="createDate">
        #createDate:TIMESTAMP#
      </isNotNull>
      <isNotNull prepend="," property="updateDate">
        #updateDate:TIMESTAMP#
      </isNotNull>
      )
    </dynamic>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterClass="cn.huanju.edu100.study.model.report.TikuForecastScore">
    update tiku_forecast_score
    <dynamic prepend="set">
      <isNotNull prepend="," property="uid">
        uid = #uid:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="boxId">
        box_id = #boxId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="categoryId">
        category_id = #categoryId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="userAnswerId">
        user_answer_id = #userAnswerId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="score">
        score = #score:DOUBLE#
      </isNotNull>
      <isNotNull prepend="," property="createDate">
        create_date = #createDate:TIMESTAMP#
      </isNotNull>
      <isNotNull prepend="," property="updateDate">
        update_date = #updateDate:TIMESTAMP#
      </isNotNull>
    </dynamic>
    where id = #id:BIGINT#
  </update>
  <update id="updateByPrimaryKey" parameterClass="cn.huanju.edu100.study.model.report.TikuForecastScore">
    update tiku_forecast_score
    set uid = #uid:BIGINT#,
      box_id = #boxId:BIGINT#,
      category_id = #categoryId:BIGINT#,
      user_answer_id = #userAnswerId:BIGINT#,
      score = #score:DOUBLE#,
      create_date = #createDate:TIMESTAMP#,
      update_date = #updateDate:TIMESTAMP#
    where id = #id:BIGINT#
  </update>
    <select id="calculateForecastScore" parameterClass="java.util.Map" resultClass="java.lang.Double" >
        select avg(score) from tiku_forecast_score
        where uid = #uid:BIGINT# and category_id = #categoryId:BIGINT# and box_id = #boxId:BIGINT# order by create_date limit 5;
    </select>
</sqlMap>
