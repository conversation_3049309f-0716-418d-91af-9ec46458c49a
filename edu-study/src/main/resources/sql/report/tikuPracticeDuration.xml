<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tikuPracticeDuration">
  <resultMap class="cn.huanju.edu100.study.model.report.TikuPracticeDuration" id="BaseResultMap">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="box_id" jdbcType="BIGINT" property="boxId" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="total_time" jdbcType="BIGINT" property="totalTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, box_id, category_id, uid, total_time, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterClass="cn.huanju.edu100.study.model.report.TikuPracticeDuration" resultMap="BaseResultMap">
    select
    <include refid="tikuPracticeDuration.Base_Column_List" />
    from tiku_practice_duration
    where id = #id:BIGINT#
  </select>
  <delete id="deleteByPrimaryKey" parameterClass="cn.huanju.edu100.study.model.report.TikuPracticeDuration">
    delete from tiku_practice_duration
    where id = #id:BIGINT#
  </delete>
  <insert id="insert" parameterClass="cn.huanju.edu100.study.model.report.TikuPracticeDuration">
    insert into tiku_practice_duration (id, box_id, category_id, uid, total_time,
      create_time, update_time)
    values (#id:BIGINT#, #boxId:BIGINT#, #categoryId:BIGINT#, #uid:BIGINT#, #totalTime:BIGINT#,
      #createTime:TIMESTAMP#, #updateTime:TIMESTAMP#)
  </insert>
  <insert id="insertSelective" parameterClass="cn.huanju.edu100.study.model.report.TikuPracticeDuration">
    insert into tiku_practice_duration
    <dynamic prepend="(">
      <isNotNull prepend="," property="id">
        id
      </isNotNull>
      <isNotNull prepend="," property="boxId">
        box_id
      </isNotNull>
      <isNotNull prepend="," property="categoryId">
        category_id
      </isNotNull>
      <isNotNull prepend="," property="uid">
        uid
      </isNotNull>
      <isNotNull prepend="," property="totalTime">
        total_time
      </isNotNull>
      <isNotNull prepend="," property="createTime">
        create_time
      </isNotNull>
      <isNotNull prepend="," property="updateTime">
        update_time
      </isNotNull>
      )
    </dynamic>
    values
    <dynamic prepend="(">
      <isNotNull prepend="," property="id">
        #id:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="boxId">
        #boxId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="categoryId">
        #categoryId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="uid">
        #uid:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="totalTime">
        #totalTime:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="createTime">
        #createTime:TIMESTAMP#
      </isNotNull>
      <isNotNull prepend="," property="updateTime">
        #updateTime:TIMESTAMP#
      </isNotNull>
      )
    </dynamic>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterClass="cn.huanju.edu100.study.model.report.TikuPracticeDuration">
    update tiku_practice_duration
    <dynamic prepend="set">
      <isNotNull prepend="," property="boxId">
        box_id = #boxId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="categoryId">
        category_id = #categoryId:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="uid">
        uid = #uid:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="totalTime">
        total_time = #totalTime:BIGINT#
      </isNotNull>
      <isNotNull prepend="," property="createTime">
        create_time = #createTime:TIMESTAMP#
      </isNotNull>
      <isNotNull prepend="," property="updateTime">
        update_time = #updateTime:TIMESTAMP#
      </isNotNull>
    </dynamic>
    where id = #id:BIGINT#
  </update>
  <update id="updateByPrimaryKey" parameterClass="cn.huanju.edu100.study.model.report.TikuPracticeDuration">
    update tiku_practice_duration
    set box_id = #boxId:BIGINT#,
      category_id = #categoryId:BIGINT#,
      uid = #uid:BIGINT#,
      total_time = #totalTime:BIGINT#,
      create_time = #createTime:TIMESTAMP#,
      update_time = #updateTime:TIMESTAMP#
    where id = #id:BIGINT#
  </update>

    <select id="findListByParam" parameterClass="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="tikuPracticeDuration.Base_Column_List"/>
        FROM tiku_practice_duration a
        <dynamic prepend="WHERE">
            <isNotEmpty prepend="AND" property="boxId">
                a.box_id = #boxId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="categoryId">
                a.category_id = #categoryId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="uid">
                a.uid = #uid#
            </isNotEmpty>
        </dynamic>

    </select>

    <update id="update" parameterClass="cn.huanju.edu100.study.model.report.TikuPracticeDuration">
        update tiku_practice_duration
        set box_id = #boxId:BIGINT#,
        category_id = #categoryId:BIGINT#,
        uid = #uid:BIGINT#,
        total_time = #totalTime:BIGINT#,
        create_time = #createTime:TIMESTAMP#,
        update_time = #updateTime:TIMESTAMP#
        where id = #id:BIGINT#
    </update>

</sqlMap>
