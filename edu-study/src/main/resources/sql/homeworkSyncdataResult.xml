<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="HomeworkSyncdataResult">
    <sql id="homeworkSyncdataResultColumns">
        a.id AS "id",
        a.res_id AS "resId",
        a.product_type AS "productType",
        a.type AS "type",
        a.status AS "status",
        a.fail_table_index AS "failTableIndex",
        a.remark AS "remark",
        a.create_date AS "createDate",
        a.update_date AS "updateDate"
    </sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.HomeworkSyncdataResult">
	    select
        <include refid="homeworkSyncdataResultColumns"/>
        FROM homework_syncdata_result a
		WHERE a.id = #id#
	</select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.HomeworkSyncdataResult">
        INSERT INTO homework_syncdata_result(
        res_id,
        product_type,
        type,
        status,
        fail_table_index,
        remark,
        create_date,
        update_date
        ) VALUES (
        #resId#,
        #productType#,
        #type#,
        #status#,
        #failTableIndex#,
        #remark#,
        #createDate#,
        #updateDate#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.HomeworkSyncdataResult">
        SELECT
        <include refid="homeworkSyncdataResultColumns"/>
        FROM homework_syncdata_result a where 1 = 1
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="resId">
                a.res_id = #resId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="productType">
                a.product_type = #productType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="type">
                a.type = #type#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="status">
                a.status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="failTableIndex">
                a.fail_table_index = #failTableIndex#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT COUNT(1)
        FROM homework_syncdata_result a where 1 = 1
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="resId">
                a.res_id = #resId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="productType">
                a.product_type = #productType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="type">
                a.type = #type#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="status">
                a.status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="failTableIndex">
                a.fail_table_index = #failTableIndex#
            </isNotEmpty>
        </dynamic>
    </select>

    <update id="update" parameterClass="cn.huanju.edu100.study.model.HomeworkSyncdataResult">
        update homework_syncdata_result set update_date=now()
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="resId">
                res_id = #resId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="productType">
                product_type = #productType#
            </isNotEmpty>
            <isNotEmpty prepend="," property="type">
                type = #type#
            </isNotEmpty>
            <isNotEmpty prepend="," property="status">
                status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="," property="failTableIndex">
                fail_table_index = #failTableIndex#
            </isNotEmpty>
            <isNotEmpty prepend="," property="remark">
                remark = #remark#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id#
    </update>
</sqlMap>
