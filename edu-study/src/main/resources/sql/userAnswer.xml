<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserAnswer">

    <select id="get" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserAnswer">
	   select a.id AS "id",
				a.uid AS "uid",
				a.paper_id AS "paperId",
				a.paper_type AS "paperType",
				a.obj_id AS "objId",
				a.obj_type AS "objType",
				a.score AS "score",
				a.source AS "source",
				a.usetime AS "usetime",
				a.answer_num AS "answerNum",
				a.start_time AS "startTime",
				a.end_time AS "endTime",
				a.comment AS "comment",
				a.create_date AS "createDate",
        		a.update_date AS "updateDate",
				a.state AS "state",
                a.product_id as "productId",
                a.goods_id as "goodsId",
                a.appid as "appid",
                a.plat_form as "platForm",
                a.has_consolidation as "hasConsolidation",
                a.done_mode as "doneMode",
                a.lesson_id AS "lessonId"
        FROM user_answer a
		WHERE a.id = #id# and a.uid = #uid#
	</select>

    <insert id="insert" parameterClass="com.hqwx.study.entity.UserAnswer">
        INSERT INTO user_answer(
            uid,
            paper_id,
            paper_type,
            obj_id,
            obj_type,
            lesson_id,
            score,
            source,
            usetime,
            answer_num,
            start_time,
            end_time,
            create_date,
            update_date,
            state,
            product_id,
            goods_id,
            appid,
            plat_form,
            done_mode
        ) VALUES (
            #uid#,
            #paperId#,
            #paperType#,
            #objId#,
            #objType#,
            #lessonId#,
            #score#,
            #source#,
            #usetime#,
            #answerNum#,
            #startTime#,
            #endTime#,
            now(),
            now(),
            #state#,
            #productId#,
            #goodsId#,
            #appid#,
            #platForm#,
            #doneMode#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <insert id="insertSharding" parameterClass="com.hqwx.study.entity.UserAnswer">
        INSERT INTO user_answer(
            id,
            uid,
            paper_id,
            paper_type,
            obj_id,
            obj_type,
            lesson_id,
            score,
            source,
            usetime,
            answer_num,
            start_time,
            end_time,
            create_date,
            update_date,
            state,
            product_id,
            goods_id,
            appid,
            plat_form,
            done_mode
        ) VALUES (
            #id#,
            #uid#,
            #paperId#,
            #paperType#,
            #objId#,
            #objType#,
            #lessonId#,
            #score#,
            #source#,
            #usetime#,
            #answerNum#,
            #startTime#,
            #endTime#,
            now(),
            now(),
            #state#,
            #productId#,
            #goodsId#,
            #appid#,
            #platForm#,
            #doneMode#
        )
    </insert>

    <update id="update" parameterClass="com.hqwx.study.entity.UserAnswer">
        UPDATE user_answer SET update_date=now()
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="uid">
                uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="," property="paperId">
                paper_id = #paperId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="paperType">
                paper_type = #paperType#
            </isNotEmpty>
            <isNotEmpty prepend="," property="score">
                score = #score#
            </isNotEmpty>
            <isNotEmpty prepend="," property="usetime">
                usetime = #usetime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="answerNum">
                answer_num = #answerNum#
            </isNotEmpty>
            <isNotEmpty prepend="," property="startTime">
                start_time = #startTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="endTime">
                end_time = #endTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="comment">
                comment = #comment#
            </isNotEmpty>
            <isNotEmpty prepend="," property="state">
                state = #state#
            </isNotEmpty>
            <isNotEmpty prepend="," property="productId">
                product_id = #productId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="goodsId">
                goods_id = #goodsId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="appid">
                appid = #appid#
            </isNotEmpty>
            <isNotEmpty prepend="," property="platForm">
                plat_form = #platForm#
            </isNotEmpty>
            <isNotEmpty prepend="," property="hasConsolidation">
                has_consolidation = #hasConsolidation#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id# and uid = #uid#
    </update>

    <delete id="delete" parameterClass="java.util.Map">
        DELETE FROM user_answer where id=#id# and uid = #uid#
    </delete>

    <select id="findList" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserAnswer">
        SELECT
            a.id AS "id",
            a.uid AS "uid",
            a.paper_id AS "paperId",
            a.paper_type AS "paperType",
            a.obj_id AS "objId",
            a.obj_type AS "objType",
            a.score AS "score",
            a.source AS "source",
            a.usetime AS "usetime",
            a.answer_num AS "answerNum",
            a.start_time AS "startTime",
            a.end_time AS "endTime",
            a.comment AS "comment",
            a.create_date AS "createDate",
            a.update_date AS "updateDate",
            a.state AS "state",
            a.product_id as "productId",
            a.goods_id as "goodsId",
            a.appid as "appid",
            a.plat_form as "platForm",
            a.has_consolidation as "hasConsolidation",
            a.done_mode as "doneMode",
            a.lesson_id AS "lessonId"
        FROM user_answer a where  a.uid = #uid#
        <![CDATA[
        	and a.paper_id <>0
        ]]>
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="id">
                a.id = #id#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="answerIds">
                a.id in
                <iterate conjunction="," open="(" close=")" property="answerIds">
                    <![CDATA[
							#answerIds[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="paperId">
                a.paper_id = #paperId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="paperIds">
                a.paper_id in
                <iterate conjunction="," open="(" close=")" property="paperIds">
                    <![CDATA[
							#paperIds[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="paperType">
                a.paper_type = #paperType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objId">
                a.obj_id = #objId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objIds">
                a.obj_id in
                <iterate conjunction="," open="(" close=")" property="objIds">
                    <![CDATA[
							#objIds[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objType">
                a.obj_type = #objType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="source">
                a.source = #source#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="state">
                a.state = #state#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="goodsId">
                a.goods_id = #goodsId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objTypes">
                a.obj_type in
                <iterate conjunction="," open="(" close=")" property="objTypes">
                    <![CDATA[
							#objTypes[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="states">
                a.state in
                <iterate conjunction="," open="(" close=")" property="states">
                    <![CDATA[
							#states[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                a.create_date >= #startTime#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ a.create_date <= #endTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="productId">
                a.product_id = #productId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="productIdList">
                a.product_id in
                <iterate conjunction="," open="(" close=")" property="productIdList">
                    <![CDATA[
							#productIdList[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="lessonId">
                a.lesson_id = #lessonId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="doneMode">
                a.done_mode = #doneMode#
            </isNotEmpty>
        </dynamic>

        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT count(1)
        FROM user_answer a where a.uid = #uid#
        <![CDATA[
        	and a.paper_id <>0
        ]]>
        <dynamic prepend="and">
            <isNotEmpty prepend="AND" property="paperId">
                a.paper_id = #paperId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="paperType">
                a.paper_type = #paperType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objId">
                a.obj_id = #objId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objType">
                a.obj_type = #objType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="source">
                a.source = #source#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="state">
                a.state = #state#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objTypes">
                a.obj_type in
                <iterate conjunction="," open="(" close=")" property="objTypes">
                    <![CDATA[
							#objTypes[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="states">
                a.state in
                <iterate conjunction="," open="(" close=")" property="states">
                    <![CDATA[
							#states[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                a.create_date >= #startTime#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ a.create_date <= #endTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="productId">
                a.product_id = #productId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="productIdList">
                a.product_id in
                <iterate conjunction="," open="(" close=")" property="productIdList">
                    <![CDATA[
							#productIdList[]#
						]]>
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="goodsId">
                a.goods_id = #goodsId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="lessonId">
                a.lesson_id = #lessonId#
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findAllList" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserAnswer">
        SELECT
            a.id AS "id",
            a.uid AS "uid",
            a.paper_id AS "paperId",
            a.paper_type AS "paperType",
            a.obj_id AS "objId",
            a.obj_type AS "objType",
            a.score AS "score",
            a.source AS "source",
            a.usetime AS "usetime",
            a.answer_num AS "answerNum",
            a.start_time AS "startTime",
            a.end_time AS "endTime",
            a.comment AS "comment",
            a.create_date AS "createDate",
            a.update_date AS "updateDate",
            a.state AS "state",
            a.product_id as "productId",
            a.goods_id as "goodsId",
            a.appid as "appid",
            a.plat_form as "platForm",
            a.has_consolidation as "hasConsolidation",
            a.lesson_id AS "lessonId"
            FROM user_answer a
        where a.uid = #uid#
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="getOne" parameterClass="com.hqwx.study.entity.UserAnswer" resultClass="com.hqwx.study.entity.UserAnswer">
        select
            a.id AS "id",
            a.uid AS "uid",
            a.paper_id AS "paperId",
            a.paper_type AS "paperType",
            a.obj_id AS "objId",
            a.obj_type AS "objType",
            a.score AS "score",
            a.source AS "source",
            a.usetime AS "usetime",
            a.answer_num AS "answerNum",
            a.start_time AS "startTime",
            a.end_time AS "endTime",
            a.comment AS "comment",
            a.create_date AS "createDate",
            a.update_date AS "updateDate",
            a.state AS "state",
            a.product_id as "productId",
            a.goods_id as "goodsId",
            a.appid as "appid",
            a.plat_form as "platForm",
            a.has_consolidation as "hasConsolidation",
            a.lesson_id AS "lessonId"
            FROM user_answer a
        where a.uid = #uid#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="paperId">
                a.paper_id = #paperId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objId">
                a.obj_id = #objId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objType">
                a.obj_type = #objType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="source">
                a.source = #source#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="paperType">
                a.paper_type = #paperType#
            </isNotEmpty>
        </dynamic>
        limit 1
    </select>

    <select id="queryUserFinishedPaperByGoodsIdAndPaperIds" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserAnswer">
        SELECT
        a.uid as "uid",
        a.goods_id as "goodsId",
        a.product_id as "productId",
        a.paper_id AS "paperId",
        a.lesson_id AS "lessonId"
        FROM user_answer a
        where a.uid = #uid#
        and a.state in (2,3)
        and a.obj_type = 0
        and <![CDATA[ ((a.goods_id = #goodsId#) or (a.end_time < #startTime# and a.goods_id is null)) ]]>
        <isNotEmpty prepend="AND" property="paperIds">
            a.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIds">
                <![CDATA[
							#paperIds[]#
						]]>
            </iterate>
        </isNotEmpty>
        group by a.goods_id,a.product_id,a.paper_id
    </select>


    <select id="getUserAnswersByPreClassExercise" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserAnswer">
        SELECT
        max(a.id) as "id",
        a.uid as "uid",
        a.goods_id as "goodsId",
        a.product_id as "productId",
        a.obj_id as "objId",
        a.lesson_id as "lessonId",
        a.paper_id AS "paperId"
        FROM user_answer a
        where a.uid = #uid#
        and a.state in (2,3)
        and a.obj_type in(340,103)
        <isNotEmpty prepend="AND" property="paperIds">
            a.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIds">
                <![CDATA[
							#paperIds[]#
						]]>
            </iterate>
        </isNotEmpty>
        group by a.goods_id, a.product_id, a.lesson_id, a.paper_id
    </select>

    <select id="getUserAnswersByPaperIdsAndObjType" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserAnswer">
        SELECT
        a.id as "id",
        a.uid as "uid",
        a.goods_id as "goodsId",
        a.product_id as "productId",
        a.score as "score",
        a.obj_id as "objId",
        a.lesson_id as "lessonId",
        a.paper_id AS "paperId"
        FROM user_answer a
        where a.uid = #uid#
        <isNotEmpty prepend="AND" property="objType">
            a.obj_type = #objType#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="paperIds">
            a.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIds">
                <![CDATA[
							#paperIds[]#
						]]>
            </iterate>
        </isNotEmpty>
    </select>

    <select id="countUserFinishedPaperByGoodsIdAndPaperIds" parameterClass="java.util.Map" resultClass="java.lang.Long">
        SELECT count(distinct a.paper_id)
        FROM user_answer a
        where a.uid = #uid#
        and a.state in (2,3)
        and a.obj_type = 0
        <isNotEmpty prepend="AND" property="goodsId">
            <![CDATA[ ((a.goods_id = #goodsId#) or (a.end_time < #startTime# and a.goods_id is null)) ]]>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="paperIds">
            a.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIds">
                <![CDATA[
							#paperIds[]#
						]]>
            </iterate>
        </isNotEmpty>

    </select>

    <select id="queryStudyStatisticsByParam" parameterClass="java.util.Map" resultClass="java.lang.Long">
        SELECT
        IFNULL(sum(t.usetime),0) AS "usetime"
        FROM user_answer t
        where
        t.uid = #uid#
        and t.obj_id = #objId#
        and <![CDATA[ t.usetime>0 and t.usetime< 21600 ]]>
        <isNotEmpty prepend="AND" property="startTime">
            <![CDATA[ t.create_date >= #startTime# ]]>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime">
            <![CDATA[ t.create_date <= #endTime# ]]>
        </isNotEmpty>
        and <![CDATA[ t.start_time > '2019-05-01 23:59:59' ]]>
        and t.obj_type in (201,202,203)
        <isNotEmpty prepend="AND" property="productId">
            t.product_id = #productId#
        </isNotEmpty>
    </select>

    <select id="getPaperUseTimeByPaperId" parameterClass="java.util.Map" resultClass="java.lang.Long">
        SELECT
        IFNULL(sum(t.usetime),0) AS "usetime"
        FROM user_answer t
        where
        t.uid = #uid#
        and <![CDATA[ t.usetime>0 and t.usetime< 21600 ]]>
        and <![CDATA[ t.start_time > '2019-05-01 23:59:59' ]]>
        and t.obj_type in (201,202,203)
        <isNotEmpty prepend="AND" property="paperId">
            t.paper_id = #paperId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="productId">
            t.product_id = #productId#
        </isNotEmpty>
        group by t.paper_id
    </select>

    <select id="getUserUseTime" parameterClass="java.util.Map" resultClass="java.lang.Long">
        SELECT
        IFNULL(sum(t.usetime),0) AS "usetime"
        FROM user_answer t
        where
        t.uid = #uid#
    </select>

    <select id="avgCountList" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        SELECT
        a.paper_id as paperId, count(1) as count, count(distinct(uid)) as ucount,round(avg(a.score),2) as avgScore
        FROM user_answer a
        where a.uid = #uid#

        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="id">
                a.id = #id#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="paperId">
                a.paper_id = #paperId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="objType">
                a.obj_type = #objType#
            </isNotEmpty>

            <isNotEmpty prepend="AND" property="startTime">
                a.start_time >= #startTime#
            </isNotEmpty>

            <isNotEmpty prepend="AND" property="endTime">
                <![CDATA[ a.end_time <= #endTime# ]]>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="paperIdList">
                a.paper_id in
                <iterate conjunction="," open="(" close=")" property="paperIdList">
                    <![CDATA[
							#paperIdList[]#
						]]>
                </iterate>
            </isNotEmpty>
        </dynamic>
        group by a.paper_id
    </select>


    <sql id="userAnswerColumns">
        a.id AS "id",
        a.uid AS "uid",
        a.paper_id AS "paperId",
        a.obj_id AS "objId",
        a.obj_type AS "objType",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.paper_type AS "paperType",
        a.source AS "source",
        a.score AS "score",
        a.usetime AS "usetime",
        a.answer_num AS "answerNum",
        a.start_time AS "startTime",
        a.end_time AS "endTime",
        a.state AS "state",
        a.tuid AS "tuid",
        a.comment AS "comment",
        a.comment_time AS "commentTime",
        a.create_date AS "createDate",
        a.update_date AS "updateDate",
        a.product_id as "productId",
        a.goods_id as "goodsId",
        a.plat_form as "platForm",
        a.appid as "appid"
    </sql>

    <sql id="userAnswerJoins">
    </sql>

    <select id="countByStudyReportQuery" parameterClass="cn.huanju.edu100.study.model.adminstudy.StudyReportQuery" resultClass="java.lang.Integer">
        SELECT
        count(*)
        FROM user_answer a
        <include refid="userAnswerJoins"/>
        where
        a.uid = #uid#
        and a.obj_type = 0
        and a.state in (2,3)
        <![CDATA[ and (a.goods_id = #goodsId# or (a.create_date < #newRuleStartTime# and a.goods_id is null)) ]]>

        <isNotEmpty prepend="AND" property="paperIdList">
            a.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIdList">
                <![CDATA[
							#paperIdList[]#
						]]>
            </iterate>
        </isNotEmpty>

        <isNotEmpty prepend="AND" property="startTime">
            a.create_date >= #startTime#
        </isNotEmpty>

        <isNotEmpty prepend="AND" property="endTime">
            <![CDATA[ a.create_date <= #endTime# ]]>
        </isNotEmpty>
    </select>


    <select id="findListByStudyReportQuery" parameterClass="cn.huanju.edu100.study.model.adminstudy.StudyReportQuery" resultClass="com.hqwx.study.entity.UserAnswer">
        SELECT
        <include refid="userAnswerColumns"/>
        FROM user_answer a
        <include refid="userAnswerJoins"/>
        where
        a.uid = #uid#
        and a.obj_type = 0
        and a.state in (2,3)
        <![CDATA[ and (a.goods_id = #goodsId# or (a.create_date < #newRuleStartTime# and a.goods_id is null)) ]]>

        <isNotEmpty prepend="AND" property="paperIdList">
            a.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIdList">
                <![CDATA[
							#paperIdList[]#
						]]>
            </iterate>
        </isNotEmpty>

        <isNotEmpty prepend="AND" property="startTime">
            a.create_date >= #startTime#
        </isNotEmpty>

        <isNotEmpty prepend="AND" property="endTime">
            <![CDATA[ a.create_date <= #endTime# ]]>
        </isNotEmpty>
        order by a.create_date desc
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="getUserAnswerCompletionList" parameterClass="cn.huanju.edu100.study.model.adminstudy.StudyCompletionInfoQuery" resultClass="cn.huanju.edu100.study.model.adminstudy.UserAnswerCompletion">
        SELECT
        a.paper_id as paperId, count(1) as paperCompletionNum,round(avg(a.score),2) as avgScore,MAX(start_time) as lastAnswerTime
        FROM user_answer a
        where a.uid = #uid#
        and a.obj_id=#categoryId#
        and a.obj_type in (201,203)
        and a.state in (2,3)
        <isNotEmpty prepend="AND" property="paperId">
            a.paper_id = #paperId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime">
            a.create_date >= #startTime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime">
            <![CDATA[ a.create_date <= #endTime# ]]>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="paperIdList">
            a.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIdList">
                <![CDATA[
							#paperIdList[]#
						]]>
            </iterate>
        </isNotEmpty>
        group by a.paper_id
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="countUserAnswerCompletion" parameterClass="cn.huanju.edu100.study.model.adminstudy.StudyCompletionInfoQuery" resultClass="java.lang.Integer">
        SELECT count(1) from
        (SELECT
        a.paper_id as paperId, count(1) as paperCompletionNum,round(avg(a.score),2) as avgScore,MAX(start_time) as lastAnswerTime
        FROM user_answer a
        where
        a.uid = #uid#
        and a.obj_id=#categoryId#
        and a.obj_type in (201,203)
        and a.state in (2,3)
        <isNotEmpty prepend="AND" property="paperId">
            a.paper_id = #paperId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime">
            a.create_date >= #startTime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime">
            <![CDATA[ a.create_date <= #endTime# ]]>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="paperIdList">
            a.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIdList">
                <![CDATA[
							#paperIdList[]#
						]]>
            </iterate>
        </isNotEmpty>
        group by a.paper_id) as result
    </select>

    <!-- 根据试卷ids获取对应的课节最近一次的答题记录 -->
    <select id="findAnswerGroupByPaperId" parameterClass="java.util.Map" resultClass="com.hqwx.study.entity.UserAnswer">
        SELECT
        <include refid="userAnswerColumns"/>
        FROM
        (SELECT distinct(id) as uas_id, t1.* from user_answer as t1
        where
        uid = #uid#
        <isNotEmpty prepend="AND" property="objTypeList">
            t1.obj_type in
            <iterate conjunction="," open="(" close=")" property="objTypeList">
                <![CDATA[
							#objTypeList[]#
						]]>
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="goodsId">
            t1.goods_id = #goodsId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="paperType">
            t1.paper_type = #paperType#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="productId">
            t1.product_id = #productId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="paperIds">
            t1.paper_id in
            <iterate conjunction="," open="(" close=")" property="paperIds">
                <![CDATA[
							#paperIds[]#
						]]>
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="stateList">
            t1.state in
            <iterate conjunction="," open="(" close=")" property="stateList">
                <![CDATA[
							#stateList[]#
						]]>
            </iterate>
        </isNotEmpty>
        order by create_date desc) as a
        group by a.paper_id
    </select>

</sqlMap>
