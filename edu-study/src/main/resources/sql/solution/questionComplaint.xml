<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="QuestionComplaint">
    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.QuestionComplaint">
	   select
        id, questionId, userId, userName, reason, createdTime
        FROM solution_question_complain
		WHERE id = #id# LIMIT 1
	</select>

    <select id="findQuestionComplaintInfoByQuestionId" parameterClass="java.lang.Long" resultClass="cn.huanju.edu100.study.model.solution.QuestionComplaint">
        select
        id, questionId, userId, userName, reason, createdTime
        FROM solution_question_complain
        WHERE questionId = #questionId# LIMIT 1
    </select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.solution.QuestionComplaint">
        INSERT INTO solution_question_complain(
        questionId,
        userId,
        userName,
        reason,
        createdTime
        ) VALUES (
        #questionId#,
        #userId#,
        #userName#,
        #reason#,
        #createdTime#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>
</sqlMap>
