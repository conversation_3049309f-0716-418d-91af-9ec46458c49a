<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="Question">
    <sql id="questionColumns">
        a.id AS "id",
        a.pid AS "pid",
        a.categoryIdCode AS "categoryIdCode",
        a.firstCategory AS "firstCategory",
        a.secondCategory AS "secondCategory",
        a.categoryId AS "categoryId",
        a.courseId AS "courseId",
        a.lessonId AS "lessonId",
        a.teachBookId AS "teachBookId",
        a.chapterId AS "chapterId",
        a.knowledgeId AS "knowledgeId",
        a.questionId AS "questionId",
        a.goodsId AS "goodsId",
        a.title AS "title",
        a.content AS "content",
        a.contentText AS "contentText",
        a.userId AS "userId",
        a.username AS "userName",
        a.source AS "source",
        a.status AS "status",
        a.isFrozen AS "isFrozen",
        a.tag AS "tag",
        a.collectionNum AS "collectionNum",
        a.views AS "views",
        a.isComplained AS "isComplained",
        a.isAl AS "isAl",
        a.is_publish AS "isPublish",
        a.createdTime AS "createdTime",
        a.updatedTime AS "updatedTime",
        a.source_type AS "sourceType",
        a.sch_id AS "schId",
        a.extend_info AS "extendInfo",
        a.resource_id AS "resourceId",
        a.question_type AS "questionType",
        a.isAiAnswer as "isAiAnswer",
        a.is_stream as "isStream"
    </sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.SolutionQuestion">
	   select
        <include refid="questionColumns"/>
        FROM solution_question a
		WHERE a.id = #id#
	</select>

    <select id="getSolutionQuestionByMessageId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.SolutionQuestion">
	   select
        <include refid="questionColumns"/>
        FROM solution_question a
		WHERE a.message_id = #messageId#
	</select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.solution.SolutionQuestion">
        INSERT INTO solution_question(
        pid,
        categoryIdCode,
        firstCategory,
        secondCategory,
        categoryId,
        courseId,
        lessonId,
        teachBookId,
        chapterId,
        knowledgeId,
        questionId,
        goodsId,
        userId,
        username,
        title,
        content,
        contentText,
        source,
        status,
        device,
        ip,
        isAl,
        product_id,
        path_id,
        paper_id,
        answer_id,
        position,
        question_type,
        sch_id,
        createdTime,
        updatedTime,
        source_type,
        is_publish,
        extend_info,
        resource_id,
        isAiAnswer,
        is_stream,
        conversation_id,
        message_id,
        change_ai_to_manual_time
        ) VALUES (
        #pid#,
        #categoryIdCode#,
        #firstCategory#,
        #secondCategory#,
		#categoryId#,
        #courseId#,
        #lessonId#,
        #teachBookId#,
        #chapterId#,
        #knowledgeId#,
        #questionId#,
        #goodsId#,
        #userId#,
        #userName#,
        #title#,
        #content#,
        #contentText#,
        #source#,
        #status#,
        #device#,
        #ip#,
        #isAl#,
        #productId#,
        #pathId#,
        #paperId#,
        #answerId#,
        #position#,
        #questionType#,
        #schId#,
        #createdTime#,
        #updatedTime#,
        #sourceType#,
        #isPublish#,
        #extendInfo#,
        #resourceId#,
        #isAiAnswer#,
        #isStream#,
        #conversationId#,
        #messageId#,
        #changeAiToManualTime#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <update id="update" parameterClass="cn.huanju.edu100.study.model.solution.SolutionQuestion">
        UPDATE solution_question SET updatedTime=#updatedTime#
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="status">
                status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="," property="isAiAnswer">
                isAiAnswer = #isAiAnswer#
            </isNotEmpty>
            <isNotEmpty prepend="," property="changeAiToManualTime">
                change_ai_to_manual_time = #changeAiToManualTime#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id# and userId = #userId#
    </update>

    <update id="updateCollectionNum" parameterClass="java.util.Map">
        UPDATE solution_question SET collectionNum = collectionNum $collectionNum$
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="updatedTime">
                updatedTime = #updatedTime#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id#
    </update>

    <update id="updateViews" parameterClass="java.util.Map">
        UPDATE solution_question SET views = views + 1
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="updatedTime">
                updatedTime = #updatedTime#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id#
    </update>

    <update id="updateComplained" parameterClass="java.util.Map">
        UPDATE solution_question SET isComplained = #isComplained#
        WHERE id = #id#
    </update>

    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.SolutionQuestion">
        SELECT
        <include refid="questionColumns"/>
        FROM solution_question a
        <isNotEmpty property="categoryId">
            force index (I_category)
        </isNotEmpty>
<!--        <isNotEmpty property="orderBy">-->
<!--            <isEmpty property="categoryId">-->
<!--                <isEqual property="orderBy" compareValue="updatedTime desc">-->
<!--                    force index (I_updatedTime)-->
<!--                </isEqual>-->
<!--            </isEmpty>-->
<!--        </isNotEmpty>-->
        where a.pid = 0
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="secondCategory">
                a.secondCategory = #secondCategory#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="categoryId">
                a.categoryId = #categoryId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="categoryIds">
                a.categoryId IN ($categoryIds$)
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userId">
                a.userId = #userId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="goodsId">
                a.goodsId = #goodsId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="tag">
                a.tag = #tag#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="tagList">
                a.tag IN
                <iterate conjunction="," open="(" close=")" property="tagList">
                    #tagList[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="status">
                a.status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="courseId">
                a.courseId = #courseId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="productId">
                a.product_id = #productId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="lessonId">
                a.lessonId = #lessonId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.questionId = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="source">
                a.source = #source#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="isFrozen">
                a.isFrozen = #isFrozen#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="isAl">
                a.isAl = #isAl#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="isPublish">
                a.is_publish = #isPublish#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="knowledgeId">
                a.knowledgeId = #knowledgeId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="knowledgeIds">
                a.knowledgeId IN
                <iterate conjunction="," open="(" close=")" property="knowledgeIds">
                    #knowledgeIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="sourceType">
                a.source_type = #sourceType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="schId">
                a.sch_id = #schId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="resourceId">
                a.resource_id = #resourceId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionType">
                a.question_type = #questionType#
            </isNotEmpty>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="findUserQuestionListByIds" parameterClass="java.lang.String" resultClass="cn.huanju.edu100.study.model.solution.SolutionQuestion" >
        SELECT
        <include refid="questionColumns"/>
        ,(a.collectionNum*0.6 + sa.likeNum*0.3 + a.views*0.1) AS hotOrderValue
        FROM solution_question a LEFT JOIN solution_answer sa
        ON sa.questionId = a.id
        where a.id IN ($ids$)
        ORDER BY hotOrderValue DESC, a.updatedTime DESC
    </select>

    <select id="findUserQuestionListByIdsOrderById" parameterClass="java.lang.String" resultClass="cn.huanju.edu100.study.model.solution.SolutionQuestion" >
        SELECT
        <include refid="questionColumns"/>
        FROM solution_question a
        where a.id IN ($ids$)
        ORDER BY field(id,$ids$)
    </select>

    <select id="findUserQuestionInfoById" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.SolutionQuestion" >
        SELECT
        <include refid="questionColumns"/>
        FROM solution_question a where a.pid = 0
        AND a.id = #questionId#
        LIMIT 1
    </select>

    <select id="findUserQuestionAnswerListByPid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.SolutionQuestion" >
        SELECT
        <include refid="questionColumns"/>
        FROM solution_question a where a.pid = #pid#
        ORDER BY a.createdTime DESC
    </select>

    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT COUNT(1)
        FROM solution_question a
        <isNotEmpty property="categoryId">
            force index (I_category)
        </isNotEmpty>
<!--        <isEmpty property="secondCategory and categoryId and categoryIds and userId and productId and lessonId and questionId">-->
<!--            force index (I_updatedTime)-->
<!--        </isEmpty>-->
        where a.pid = 0
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="secondCategory">
                a.secondCategory = #secondCategory#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="categoryId">
                a.categoryId = #categoryId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="categoryIds">
                a.categoryId IN ($categoryIds$)
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userId">
                a.userId = #userId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="tag">
                a.tag = #tag#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="tagList">
                a.tag IN
                <iterate conjunction="," open="(" close=")" property="tagList">
                    #tagList[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="status">
                a.status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="courseId">
                a.courseId = #courseId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="productId">
                a.product_id = #productId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="lessonId">
                a.lessonId = #lessonId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionId">
                a.questionId = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="source">
                a.source = #source#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="isFrozen">
                a.isFrozen = #isFrozen#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="isAl">
                a.isAl = #isAl#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="isPublish">
                a.is_publish = #isPublish#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="knowledgeId">
                a.knowledgeId = #knowledgeId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="knowledgeIds">
                a.knowledgeId IN
                <iterate conjunction="," open="(" close=")" property="knowledgeIds">
                    #knowledgeIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="sourceType">
                a.source_type = #sourceType#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="schId">
                a.sch_id = #schId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="resourceId">
                a.resource_id = #resourceId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="questionType">
                a.question_type = #questionType#
            </isNotEmpty>
        </dynamic>
    </select>
    
    <select id="countSoFar" parameterClass="java.util.Map" resultClass="java.lang.Integer" >
        select count(1) from solution_question a
        where a.userId = #userId#
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="categoryId">
                a.categoryId = #categoryId#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="startTime">
                a.createdTime >= #startTime#
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="findUserHotQuestionList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.SolutionQuestion" >
        SELECT
        <include refid="questionColumns"/>
        ,(a.collectionNum*0.6 + sa.likeNum*0.3 + a.views*0.1) AS hotOrderValue, sa.isBest AS isBest, sa.likeNum AS likeNum, sa.userId AS teacherId, sa.username AS teacherName
        FROM solution_answer sa LEFT JOIN solution_question a
        ON sa.questionId = a.id
        where a.pid = 0
        AND a.isFrozen = 0
        AND a.tag = 2
        AND (a.status IN (1,2) OR sa.isBest = 1)
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="secondCategory">
                a.secondCategory = #secondCategory#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userId">
                a.userId != #userId#
            </isNotEmpty>
        </dynamic>
        ORDER BY hotOrderValue DESC, a.updatedTime DESC
        LIMIT #from#,#rows#
    </select>

    <select id="findUserHotQuestionListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer" >
        SELECT COUNT(1) FROM solution_answer sa LEFT JOIN solution_question a ON sa.questionId = a.id where a.pid = 0
        AND a.isFrozen = 0
        AND a.tag = 2
        AND (a.status IN (1,2) OR sa.isBest = 1)
        <dynamic prepend="AND">
            <isNotEmpty prepend="AND" property="secondCategory">
                a.secondCategory = #secondCategory#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="userId">
                a.userId != #userId#
            </isNotEmpty>
        </dynamic>
    </select>
</sqlMap>
