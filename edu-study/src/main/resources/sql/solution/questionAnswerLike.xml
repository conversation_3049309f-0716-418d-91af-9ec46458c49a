<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="QuestionAnswerLike">
    <sql id="answerLikeColumns">
        a.id AS "id",
        a.answerId AS "answerId",
        a.userId AS "userId",
        a.createdTime AS "createdTime"
    </sql>

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.QuestionAnswerLike">
	   select
        <include refid="answerLikeColumns"/>
        FROM solution_answer_like a
		WHERE a.id = #id#
	</select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.solution.QuestionAnswerLike">
        INSERT INTO solution_answer_like(
        answerId,
        userId,
        createdTime
        ) VALUES (
        #answerId#,
        #userId#,
        #createdTime#
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <delete id="deleteAnswerLike" parameterClass="java.util.Map">
        DELETE FROM solution_answer_like where answerId=#answerId# and userId = #uid#
    </delete>

    <select id="findUserAnswerLikeByAnswerIdAndUid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.solution.QuestionAnswerLike">
        select
        <include refid="answerLikeColumns"/>
        FROM solution_answer_like a
        WHERE a.answerId = #answerId#
        AND userId = #uid#
        LIMIT 1
    </select>
</sqlMap>
