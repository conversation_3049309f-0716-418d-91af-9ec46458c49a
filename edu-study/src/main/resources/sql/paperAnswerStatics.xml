<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="PaperAnswerStatics">

    <select id="countPaperAnswer" parameterClass="java.lang.Long" resultClass="java.lang.Long">
        SELECT total
            FROM paper_answer t
        WHERE t.p_id =#paperId#
    </select>

    <select id="countFinishPaper" parameterClass="java.lang.Long" resultClass="java.lang.Long">
        SELECT count(1)
            FROM paper_answer_detail t
        WHERE
            t.p_id = #paperId#
            AND t.state IN (2,3)
    </select>

</sqlMap>