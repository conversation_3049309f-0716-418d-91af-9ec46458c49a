<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserErrorQuestion">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserErrorQuestion">
	   select
				a.id AS "id",
				a.uid AS "uid",
				a.paragraph_id AS "paragraphId",
				a.lesson_id AS "lessonId",
				a.question_id AS "questionId",
				a.topic_id AS "topicId",
				a.last_error_time AS "lastErrorTime",
				a.last_error_answer AS "lastErrorAnswer"
		FROM user_error_question a
		WHERE a.id = #id#
	</select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        INSERT INTO user_error_question(
	        uid,
	        paragraph_id,
	        lesson_id,
	        question_id,
	        topic_id,
	        last_error_time,
	        last_error_answer,
            goods_id,
            product_id,
	        create_date
        ) VALUES (
	        #uid#,
	        #paragraphId#,
	        #lessonId#,
	        #questionId#,
	        #topicId#,
	        #lastErrorTime#,
	        #lastErrorAnswer#,
            #goodsId#,
            #productId#,
	        now()
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <update id="update" parameterClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        UPDATE user_error_question SET id=id
        <dynamic prepend=",">
            <isNotEmpty prepend="," property="uid">
                uid = #uid#
            </isNotEmpty>
            <isNotEmpty prepend="," property="goodsId">
                goods_id = #goodsId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="productId">
                product_id = #productId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="paragraphId">
                paragraph_id = #paragraphId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lessonId">
                lesson_id = #lessonId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="questionId">
                question_id = #questionId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="topicId">
                topic_id = #topicId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lastErrorTime">
                last_error_time = #lastErrorTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lastErrorAnswer">
                last_error_answer = #lastErrorAnswer#
            </isNotEmpty>
        </dynamic>
        WHERE id = #id#
    </update>

    <delete id="delete" parameterClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        DELETE FROM user_error_question where id=#id#
    </delete>

    <delete id="removeErrQuestion" parameterClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        DELETE FROM user_error_question
        where uid=#uid#
        and question_id = #questionId#
        and topic_id = #topicId#
        <dynamic prepend="AND">
            <isNotNull prepend="AND" property="goodsId">
                goods_id = #goodsId#
            </isNotNull>
            <isNotNull prepend="AND" property="productId">
                product_id = #productId#
            </isNotNull>
        </dynamic>
    </delete>

    <delete id="removeErrQuestionWithlesson" parameterClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        DELETE FROM user_error_question
        where uid=#uid# and question_id = #questionId#
        and topic_id = #topicId#
        <dynamic prepend="AND">
            <isNotNull prepend="AND" property="paragraphId">
                paragraph_id = #paragraphId#
            </isNotNull>
            <isNotNull prepend="AND" property="lessonId">
                lesson_id = #lessonId#
            </isNotNull>
            <isNotNull prepend="AND" property="goodsId">
                goods_id = #goodsId#
            </isNotNull>
            <isNotNull prepend="AND" property="productId">
                product_id = #productId#
            </isNotNull>
        </dynamic>
    </delete>

    <select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        SELECT
        	a.id AS "id",
			a.uid AS "uid",
			a.paragraph_id AS "paragraphId",
			a.lesson_id AS "lessonId",
			a.question_id AS "questionId",
			a.topic_id AS "topicId",
			a.last_error_time AS "lastErrorTime",
			a.last_error_answer AS "lastErrorAnswer"
        FROM user_error_question a where 1=1 and del_flag = '0'
        <isEmpty property="isAl">
            <![CDATA[
        and (a.paragraph_id <> 0 or a.lesson_id <> 0)
         ]]>
        </isEmpty>

        <dynamic prepend="AND">
            <isNotNull prepend="AND" property="uid">
                a.uid = #uid#
            </isNotNull>
            <isNotNull prepend="AND" property="goodsId">
                a.goods_id = #goodsId#
            </isNotNull>
            <isNotNull prepend="AND" property="productId">
                a.product_id = #productId#
            </isNotNull>
            <isNotNull prepend="AND" property="paragraphId">
                a.paragraph_id = #paragraphId#
            </isNotNull>
            <isNotNull prepend="AND" property="lessonId">
                a.lesson_id = #lessonId#
            </isNotNull>
            <isNotNull prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotNull>
            <isNotNull prepend="AND" property="topicId">
                a.topic_id = #topicId#
            </isNotNull>
        </dynamic>
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
        SELECT count(1)
        FROM user_error_question a where 1=1 and del_flag = '0'
        <isEmpty property="isAl">
            <![CDATA[
           and (a.paragraph_id <> 0 or a.lesson_id <> 0)
           ]]>
        </isEmpty>
        <dynamic prepend="AND">
            <isNotNull prepend="AND" property="uid">
                a.uid = #uid#
            </isNotNull>
            <isNotNull prepend="AND" property="goodsId">
                a.goods_id = #goodsId#
            </isNotNull>
            <isNotNull prepend="AND" property="productId">
                a.product_id = #productId#
            </isNotNull>
            <isNotNull prepend="AND" property="paragraphId">
                a.paragraph_id = #paragraphId#
            </isNotNull>
            <isNotNull prepend="AND" property="lessonId">
                a.lesson_id = #lessonId#
            </isNotNull>
            <isNotNull prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotNull>
        </dynamic>
    </select>

    <select id="findAllList" parameterClass="java.util.Map"
            resultClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        SELECT
        	a.id AS "id",
			a.uid AS "uid",
			a.paragraph_id AS "paragraphId",
			a.lesson_id AS "lessonId",
			a.question_id AS "questionId",
			a.topic_id AS "topicId",
			a.last_error_time AS "lastErrorTime",
			a.last_error_answer AS "lastErrorAnswer"
        FROM user_error_question a
        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
    </select>

    <select id="getOne" parameterClass="cn.huanju.edu100.study.model.UserErrorQuestion"
            resultClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.question_id AS "questionId",
        a.topic_id AS "topicId",
        a.last_error_time AS "lastErrorTime",
        a.last_error_answer AS "lastErrorAnswer",
        a.goods_id AS "goodsId",
        a.product_id AS "productId"
        FROM user_error_question a
        <dynamic prepend="where">
            <isNotNull prepend="AND" property="uid">
                a.uid = #uid#
            </isNotNull>
            <isNotNull prepend="AND" property="goodsId">
                a.goods_id = #goodsId#
            </isNotNull>
            <isNotNull prepend="AND" property="productId">
                a.product_id = #productId#
            </isNotNull>
            <isNotNull prepend="AND" property="paragraphId">
                a.paragraph_id = #paragraphId#
            </isNotNull>
            <isNotNull prepend="AND" property="lessonId">
                a.lesson_id = #lessonId#
            </isNotNull>
            <isNotNull prepend="AND" property="questionId">
                a.question_id = #questionId#
            </isNotNull>
            <isNotNull prepend="AND" property="topicId">
                a.topic_id = #topicId#
            </isNotNull>
        </dynamic>
        limit 1
    </select>


    <select id="findHomeworkErrorQuestionList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.UserErrorQuestion">
        SELECT
        a.id AS "id",
        a.uid AS "uid",
        a.paragraph_id AS "paragraphId",
        a.lesson_id AS "lessonId",
        a.question_id AS "questionId",
        a.topic_id AS "topicId",
        a.last_error_time AS "lastErrorTime",
        a.last_error_answer AS "lastErrorAnswer",
        a.create_date AS "createDate",
        a.goods_id AS "goodsId",
        a.product_id AS "productId"
        FROM user_error_question a
        where goods_id > 0 and del_flag = '0'

        <isNotNull prepend="AND" property="uid">
            a.uid = #uid#
        </isNotNull>
        <isNotEmpty prepend="AND" property="startId">
            <![CDATA[ a.id > #startId# ]]>
        </isNotEmpty>

        <isNotEmpty prepend="AND" property="endId">
            <![CDATA[ a.id <= #endId# ]]>
        </isNotEmpty>

        <dynamic prepend="ORDER BY">
            <isPropertyAvailable prepend="" property="orderBy">
                $orderBy$
            </isPropertyAvailable>
        </dynamic>
        <dynamic prepend="limit">
            <isPropertyAvailable prepend="" property="pageSize">
                #from#,#pageSize#
            </isPropertyAvailable>
        </dynamic>
    </select>

</sqlMap>
