<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="QuestionAnswerStatics">

    <select id="countQuestionAnswer" parameterClass="java.lang.Long" resultClass="java.lang.Long">
        SELECT total
        FROM question_answer t
        WHERE t.question_id = #questionId#
    </select>

    <select id="countRightAnswer" parameterClass="java.lang.Long" resultClass="java.lang.Long">
        SELECT count(1)
            FROM question_answer_detail t
        WHERE
            t.question_id = #questionId#
            AND t.state = 2
    </select>

    <select id="countWrongAnswer" parameterClass="java.lang.Long" resultClass="java.lang.Long">
        SELECT count(1)
            FROM question_answer_detail t
        WHERE
            t.question_id = #questionId#
            AND t.state != 2
    </select>


    <update id="updateQuestionAnswerDetail" parameterClass="cn.huanju.edu100.study.model.QuestionAnswerDetail">
        UPDATE question_answer_detail SET answer_time=#answerTime#,state=#state#,update_time=now()
        WHERE question_id = #questionId#
            AND uid = #uid#
    </update>

    <insert id="addQuestionAnswerDetail" parameterClass="cn.huanju.edu100.study.model.QuestionAnswerDetail">
        insert into question_answer_detail
			(sch_id,
			uid,
			question_id,
			answer_time,
			state,
			create_time,
			update_time)
        ) VALUES (
        #schId#,
        #uid#,
        #questionId#,
        #answerTime#,
        #state#,
        now(),
        now()
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

    <insert id="addQuestionAnswerDetailBatch" parameterClass="java.util.List">
		insert into question_answer_detail
			(sch_id,
			uid,
			question_id,
			answer_time,
			state,
			create_time,
			update_time)
		values
		<iterate conjunction =",">
            (#list[].schId#,
            #list[].uid#,
            #list[].questionId#,
            #list[].answerTime#,
            #list[].state#,
            now(),
            now())
    	</iterate>
	</insert>

	<select id="getByQuestionId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.QuestionAnswer">
	   select
				a.id AS "id",
				a.sch_id AS "schId",
				a.question_id AS "questionId",
				a.total AS "total",
				a.update_time AS "updateTime",
				a.create_time AS "createTime"
		FROM question_answer a
		WHERE a.question_id = #questionId#
	</select>

	<insert id="addQuestionAnswer" parameterClass="cn.huanju.edu100.study.model.QuestionAnswer">
        insert into question_answer
        (
        	sch_id,
		 	question_id,
		 	total,
		 	update_time,
		 	create_time
        ) VALUES (
        	#schId#,
        	#questionId#,
        	#total#,
        	now(),
        	now()
        )
        <selectKey resultClass="long" type="post" keyProperty="id">
            select LAST_INSERT_ID() as value
        </selectKey>
    </insert>

	<update id="refashQuestionAnswerTotal" parameterClass="java.util.Map">
        UPDATE question_answer SET total=#num#,update_time=now()
        WHERE question_id = #questionId#
    </update>

    <select id="isExsitByUidAndQuestionId" parameterClass="cn.huanju.edu100.study.model.QuestionAnswerDetail" resultClass="java.lang.Integer">
        SELECT count(1)
            FROM question_answer_detail t
        WHERE
            t.question_id = #questionId#
            AND t.uid = #uid#
    </select>


</sqlMap>
