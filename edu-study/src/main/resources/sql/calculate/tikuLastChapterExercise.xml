<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TikuLastChapterExercise">
    <sql id="columns" >
        a.id AS "id",
        a.uid AS "uid",
        a.box_id AS "boxId",
        a.book_id AS "bookId",
        a.obj_type AS "objType",
        a.obj_id AS "objId",
        a.chapter_id AS "chapterId",
        a.section_id AS "sectionId",
        a.knowledge_id AS "knowledgeId",
        a.chapter_name AS "chapterName",
        a.section_name AS "sectionName",
        a.knowledge_name AS "knowledgeName",
        a.update_date AS "updateDate",
        a.create_date AS "createDate"
    </sql>

    <select id="getByUidBoxId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise">
        select
        <include refid="columns" />
        FROM tiku_last_chapter_exercise a
        WHERE  uid=#uid# and box_id=#boxId# limit 1
    </select>
    <select id="getByUidBoxIds" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise">
        select
        <include refid="columns" />
        FROM tiku_last_chapter_exercise a
        WHERE  uid=#uid#
        <isNotEmpty prepend="AND" property="boxIds">
            box_id IN
            <iterate conjunction="," open="(" close=")" property="boxIds">
                #boxIds[]#
            </iterate>
        </isNotEmpty>
        order by a.update_date desc
        limit 0,1
    </select>
    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise">
        INSERT INTO tiku_last_chapter_exercise(
        id,
        uid,
        box_id,
        book_id,
        obj_type,
        obj_id,
        chapter_id,
        section_id,
        knowledge_id,
        chapter_name,
        section_name,
        knowledge_name,
        update_date,
        create_date
        ) VALUES (
        #id#,
        #uid#,
        #boxId#,
        #bookId#,
        #objType#,
        #objId#,
        #chapterId#,
        #sectionId#,
        #knowledgeId#,
        #chapterName#,
        #sectionName#,
        #knowledgeName#,
        #updateDate#,
        #createDate#
        )
    </insert>
    <update id="update" parameterClass="cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise">
        UPDATE tiku_last_chapter_exercise SET
        uid = #uid#,
        box_id = #boxId#,
        book_id = #bookId#,
        obj_type = #objType#,
        obj_id = #objId#,
        chapter_id = #chapterId#,
        section_id = #sectionId#,
        knowledge_id = #knowledgeId#,
        chapter_name = #chapterName#,
        section_name = #sectionName#,
        knowledge_name = #knowledgeName#,
        update_date = #updateDate#
        WHERE id = #id#
    </update>

    <delete id="delete" parameterClass="java.util.Map">
        DELETE FROM tiku_last_chapter_exercise where id=#id#
    </delete>
</sqlMap>







