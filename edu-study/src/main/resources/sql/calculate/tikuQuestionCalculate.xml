<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TikuQuestionCalculate">

    <sql id="columns">
        a.id AS "id",
        a.second_category AS "secondCategory",
        a.uid AS "uid",
        a.today_count AS "todayCount",
        a.total_count AS "totalCount",
        a.today_date AS "todayDate",
        a.create_date AS "createDate",
        a.update_date AS "updateDate"
    </sql>
    <select id="getByUidSecondCategoryId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.calculate.TikuQuestionCalculate">
        select
        <include refid="columns" />
        FROM tiku_question_calculate a
        WHERE  uid=#uid# and second_category=#secondCategory# limit 1
    </select>

    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.calculate.TikuQuestionCalculate">
        INSERT INTO tiku_question_calculate(
        id,
        second_category,
        uid,
        today_count,
        total_count,
        today_date,
        create_date,
        update_date
        ) VALUES (
        #id#,
        #secondCategory#,
        #uid#,
        #todayCount#,
        #totalCount#,
        #todayDate#,
        #createDate#,
        #updateDate#
        )
    </insert>
    <update id="update" parameterClass="cn.huanju.edu100.study.model.calculate.TikuQuestionCalculate">
        UPDATE tiku_question_calculate SET
        second_category = #secondCategory#,
        uid = #uid#,
        today_count = #todayCount#,
        total_count = #totalCount#,
        today_date = #todayDate#,
        update_date = #updateDate#
        WHERE id = #id#
    </update>

</sqlMap>
