<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TikuDaysCalculate">

    <sql id="columns" >
        a.id AS "id",
        a.uid AS "uid",
        a.day_total AS "dayTotal",
        a.create_date AS "createDate",
        a.update_date AS "updateDate"
    </sql>
    <select id="getByUid" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.calculate.TikuDaysCalculate">
        select
        <include refid="columns" />
        FROM tiku_days_calculate a
        WHERE  uid=#uid#
    </select>
    <insert id="insert" parameterClass="cn.huanju.edu100.study.model.calculate.TikuDaysCalculate">
        INSERT INTO tiku_days_calculate(
        id,
        uid,
        day_total,
        create_date,
        update_date
        ) VALUES (
        #id#,
        #uid#,
        #dayTotal#,
        #createDate#,
        #updateDate#
        )
    </insert>
    <update id="update" parameterClass="cn.huanju.edu100.study.model.calculate.TikuDaysCalculate">
        UPDATE tiku_days_calculate SET
        day_total = #dayTotal#,
        update_date = #updateDate#
        WHERE  uid = #uid#
    </update>

</sqlMap>
