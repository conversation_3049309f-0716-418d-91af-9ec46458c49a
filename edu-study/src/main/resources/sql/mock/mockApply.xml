<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="MockApply">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockApply">
	   select
				a.id AS "id",
				a.uid AS "uid",
				a.mobile AS "mobile",
				a.apply_time AS "applyTime",
				a.apply_status AS "applyStatus",
				a.exam_status AS "examStatus",
				a.score AS "score",
                a.user_answer_id AS "userAnswerId",
                a.is_resit AS "isResit",
				a.mock_category_id AS "mockCategoryId",
				a.mock_exam_id AS "mockExamId",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.create_date AS "createDate",
				a.update_date AS "updateDate",
                a.submit_rank AS "submitRank",
                a.use_time AS "useTime"
		FROM mock_apply a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.mock.MockApply">
		INSERT INTO mock_apply(
			uid,
			mobile,
			apply_time,
			apply_status,
			exam_status,
			score,
            user_answer_id,
            is_resit,
			mock_category_id,
			mock_exam_id,
			second_category,
			category_id,
			create_date,
			update_date,
            submit_rank,
            use_time,
			app_id
		) VALUES (
			#uid#,
			#mobile#,
			#applyTime#,
			#applyStatus#,
			#examStatus#,
			#score#,
            #userAnswerId#,
            #isResit#,
			#mockCategoryId#,
			#mockExamId#,
			#secondCategory#,
			#categoryId#,
			#createDate#,
			#updateDate#,
            #submitRank#,
            #useTime#,
			#appId#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.mock.MockApply">
		UPDATE mock_apply
			<dynamic prepend="SET">
                <isNotEmpty prepend="," property="uid">
                    uid = #uid#
                </isNotEmpty>
                <isNotEmpty prepend="," property="mobile">
                    mobile = #mobile#
                </isNotEmpty>
                <isNotEmpty prepend="," property="applyTime">
                    apply_time = #applyTime#
                </isNotEmpty>
                <isNotEmpty prepend="," property="applyStatus">
                    apply_status = #applyStatus#
                </isNotEmpty>
                <isNotEmpty prepend="," property="examStatus">
                    exam_status = #examStatus#
                </isNotEmpty>
                <isNotEmpty prepend="," property="score">
                    score = #score#
                </isNotEmpty>
                <isNotEmpty prepend="," property="userAnswerId">
                    user_answer_id = #userAnswerId#
                </isNotEmpty>
                <isNotEmpty prepend="," property="isResit">
                    is_resit = #isResit#
                </isNotEmpty>
                <isNotEmpty prepend="," property="mockCategoryId">
                    mock_category_id = #mockCategoryId#
                </isNotEmpty>
                <isNotEmpty prepend="," property="mockExamId">
                    mock_exam_id = #mockExamId#
                </isNotEmpty>
                <isNotEmpty prepend="," property="secondCategory">
                    second_category = #secondCategory#
                </isNotEmpty>
                <isNotEmpty prepend="," property="categoryId">
                    category_id = #categoryId#
                </isNotEmpty>
                <isNotEmpty prepend="," property="createDate">
                    create_date = #createDate#
                </isNotEmpty>
                <isNotEmpty prepend="," property="updateDate">
                    update_date = #updateDate#
                </isNotEmpty>
                <isNotEmpty prepend="," property="submitRank">
                    submit_rank = #submitRank#
                </isNotEmpty>
                <isNotEmpty prepend="," property="useTime">
                    use_time = #useTime#
                </isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM mock_apply where id=#id#
	</delete>

	<select id="qryByMockExamId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockApply">
		select
			a.id AS "id",
			a.uid AS "uid",
			a.mobile AS "mobile",
			a.apply_time AS "applyTime",
			a.apply_status AS "applyStatus",
			a.exam_status AS "examStatus",
			a.score AS "score",
			a.mock_category_id AS "mockCategoryId",
			a.mock_exam_id AS "mockExamId",
			a.second_category AS "secondCategory",
			a.category_id AS "categoryId",
			a.create_date AS "createDate",
			a.update_date AS "updateDate",
            a.submit_rank AS "submitRank",
            a.use_time AS "useTime"
		FROM mock_apply a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="mockExamId">
				a.mock_exam_id = #mockExamId#
			</isNotEmpty>
		</dynamic>
		order by update_date desc
	</select>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockApply">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.mobile AS "mobile",
					a.apply_time AS "applyTime",
					a.apply_status AS "applyStatus",
					a.exam_status AS "examStatus",
					a.score AS "score",
					a.mock_category_id AS "mockCategoryId",
					a.mock_exam_id AS "mockExamId",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.create_date AS "createDate",
					a.update_date AS "updateDate"
		FROM mock_apply a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="mobile">
						a.mobile = #mobile#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="applyTime">
						a.apply_time = #applyTime#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="applyStatus">
						a.apply_status = #applyStatus#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="examStatus">
					a.exam_status = #examStatus#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="mockCategoryId">
						a.mock_category_id = #mockCategoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="mockExamId">
						a.mock_exam_id = #mockExamId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM mock_apply a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="uid">
						a.uid = #uid#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="mobile">
						a.mobile = #mobile#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="applyTime">
						a.apply_time = #applyTime#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="applyStatus">
						a.apply_status = #applyStatus#
				</isNotEmpty>
				<isNotEmpty prepend="AND" property="examStatus">
					a.exam_status = #examStatus#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="mockCategoryId">
						a.mock_category_id = #mockCategoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="mockExamId">
						a.mock_exam_id = #mockExamId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockApply">
		SELECT
								a.id AS "id",
					a.uid AS "uid",
					a.mobile AS "mobile",
					a.apply_time AS "applyTime",
					a.apply_status AS "applyStatus",
					a.exam_status AS "examStatus",
					a.score AS "score",
					a.mock_category_id AS "mockCategoryId",
					a.mock_exam_id AS "mockExamId",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.create_date AS "createDate",
					a.update_date AS "updateDate"
		FROM mock_apply a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

    <select id="findMockApplyListByMockId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockApply">
        SELECT
        uid,
        use_time as useTime,
        category_id as categoryId,
        score,
        submit_rank as submitRank,
        v_sort as vSort
        from (
        select uid,use_time,
        category_id,
        score,
        submit_rank,
        update_date,
        if(@category_id=category_id,@rowno:=@rowno+1,@rowno:=1) as v_sort,
        @category_id:=category_id as v_km
        from (
        select uid,use_time,category_id,score,submit_rank,update_date
        from mock_apply where mock_exam_id = #mockExamId# and score is not null and exam_status = 1
        order by category_id,score desc ,update_date asc
        )s ,(select @rowno:=0,@category_id:=null)s1
        ) s
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="number">
                v_sort &lt;=  #number#
            </isNotEmpty>
            <isNotEmpty prepend="OR" property="uid">
                uid =  #uid#
            </isNotEmpty>
        </dynamic>
    </select>


	<select id="countMockApplyByParam" parameterClass="java.util.Map" resultClass="java.lang.Long">
		SELECT count(*) from mock_apply
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="mockExamId">
				mock_exam_id = #mockExamId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="mockSubjectId">
				mock_category_id =  #mockSubjectId#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="isResit">
				<isEqual property="isResit" compareValue="1">
					is_resit =  #isResit#
				</isEqual>
				<isEqual property="isResit" compareValue="0">
					(is_resit is null or is_resit = 0)
				</isEqual>
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="examStatus">
				exam_status =  #examStatus#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="subjectIdList">
				mock_category_id in
				<iterate conjunction="," open="(" close=")" property="subjectIdList">
					<![CDATA[
							#subjectIdList[]#
						]]>
				</iterate>
			</isNotEmpty>
		</dynamic>
	</select>

    <select id="findMockUseTimeRankList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockApply">
        SELECT
        uid,
        category_id as categoryId,
        use_time,
        v_sort as vSort
        from (
        select uid,
        category_id,
        use_time,
        update_date,
        if(@category_id=category_id,@rowno:=@rowno+1,@rowno:=1) as v_sort,
        @category_id:=category_id as v_km
        from (
        select uid,category_id,use_time,update_date
        from mock_apply where mock_exam_id = #mockExamId# and use_time is not null and exam_status = 1
        order by category_id,use_time asc ,update_date asc
        )s ,(select @rowno:=0,@category_id:=null)s1
        ) s
        <dynamic prepend="where">
            <isNotEmpty prepend="AND" property="uid">
                uid = #uid#
            </isNotEmpty>
        </dynamic>
    </select>

	<select id="qryByUserAnswerId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockApply">
		select
		a.id AS "id",
		a.uid AS "uid",
		a.mobile AS "mobile",
		a.apply_time AS "applyTime",
		a.apply_status AS "applyStatus",
		a.exam_status AS "examStatus",
		a.score AS "score",
		a.mock_category_id AS "mockCategoryId",
		a.mock_exam_id AS "mockExamId",
		a.second_category AS "secondCategory",
		a.category_id AS "categoryId",
		a.create_date AS "createDate",
		a.user_answer_id AS "userAnswerId",
		a.update_date AS "updateDate",
		a.submit_rank AS "submitRank",
		a.use_time AS "useTime"
		FROM mock_apply a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="uid">
				a.uid = #uid#
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="userAnswerId">
				a.user_answer_id = #userAnswerId#
			</isNotEmpty>
		</dynamic>
		order by update_date desc
		limit 0,1
	</select>

</sqlMap>
