<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="MockLiveLesson">

	<sql id="columns">
		a.id AS "id",
		a.mock_exam_id AS "mockExamId",
		a.product_id AS "productId",
		a.second_category AS "secondCategory",
		a.category_id AS "categoryId",
		a.create_date AS "createDate",
		a.update_date AS "updateDate",
		a.title AS "title",
		a.sort_num AS "sortNum"
	</sql>


	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM mock_live_lesson where id=#id#
	</delete>

	<select id="getByMockId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockLiveLesson">
		select
		<include refid="columns"/>
		FROM mock_live_lesson a
		<dynamic prepend="where">
			<isNotEmpty prepend="AND" property="mockExamId">
				a.mock_exam_id = #mockExamId#
			</isNotEmpty>
		</dynamic>
		ORDER BY a.sort_num,a.update_date desc
	</select>

</sqlMap>
