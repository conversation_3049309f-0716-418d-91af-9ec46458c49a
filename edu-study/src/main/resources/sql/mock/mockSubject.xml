<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="MockSubject">

    <select id="get" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockSubject">
	   select
				a.id AS "id",
				a.mock_exam_id AS "mockExamId",
				a.second_category AS "secondCategory",
				a.category_id AS "categoryId",
				a.paper_id AS "paperId",
				a.course_id AS "courseId",
				a.intro AS "intro",
				a.mock_start_time AS "mockStartTime",
				a.mock_end_time AS "mockEndTime",
				a.create_date AS "createDate",
				a.update_date AS "updateDate"
		FROM mock_subject a
		WHERE a.id = #id#
	</select>

	<insert id="insert" parameterClass="cn.huanju.edu100.study.model.mock.MockSubject">
		INSERT INTO mock_subject(
			mock_exam_id,
			second_category,
			category_id,
			paper_id,
			course_id,
			intro,
			mock_start_time,
			mock_end_time,
			create_date,
			update_date
		) VALUES (
			#mockExamId#,
			#secondCategory#,
			#categoryId#,
			#paperId#,
			#courseId#,
			#intro#,
			#mockStartTime#,
			#mockEndTime#,
			#createDate#,
			#updateDate#
		)
		<selectKey resultClass="long" type="post" keyProperty="id" >
        	select LAST_INSERT_ID() as value
    	</selectKey>
	</insert>

	<update id="update" parameterClass="cn.huanju.edu100.study.model.mock.MockSubject">
		UPDATE mock_subject
			<dynamic prepend="SET">
						<isNotEmpty prepend="," property="mockExamId">
							mock_exam_id = #mockExamId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="secondCategory">
							second_category = #secondCategory#
						</isNotEmpty>
						<isNotEmpty prepend="," property="categoryId">
							category_id = #categoryId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="paperId">
							paper_id = #paperId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="courseId">
							course_id = #courseId#
						</isNotEmpty>
						<isNotEmpty prepend="," property="intro">
							intro = #intro#
						</isNotEmpty>
						<isNotEmpty prepend="," property="mockStartTime">
							mock_start_time = #mockStartTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="mockEndTime">
							mock_end_time = #mockEndTime#
						</isNotEmpty>
						<isNotEmpty prepend="," property="createDate">
							create_date = #createDate#
						</isNotEmpty>
						<isNotEmpty prepend="," property="updateDate">
							update_date = #updateDate#
						</isNotEmpty>
			</dynamic>
		WHERE id = #id#
	</update>

	<delete id="delete" parameterClass="java.util.Map">
		DELETE FROM mock_subject where id=#id#
	</delete>

	<select id="qryByMockExamId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockSubject">
		select
            a.id AS "id",
            a.mock_exam_id AS "mockExamId",
            a.second_category AS "secondCategory",
            a.category_id AS "categoryId",
            a.paper_id AS "paperId",
            a.course_id AS "courseId",
            a.intro AS "intro",
            a.mock_start_time AS "mockStartTime",
            a.mock_end_time AS "mockEndTime",
            a.create_date AS "createDate",
            a.update_date AS "updateDate",
			a.sort_num AS "sortNum"
		FROM mock_subject a
		where a.mock_end_time > DATE_SUB(SYSDATE(),INTERVAL 12 HOUR)
		<isNotEmpty prepend="AND" property="mockExamId">
			a.mock_exam_id = #mockExamId#
		</isNotEmpty>
        ORDER BY sort_num desc, mock_start_time asc
	</select>

	<select id="findList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockSubject">
		SELECT
								a.id AS "id",
					a.mock_exam_id AS "mockExamId",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.paper_id AS "paperId",
					a.course_id AS "courseId",
					a.intro AS "intro",
					a.mock_start_time AS "mockStartTime",
					a.mock_end_time AS "mockEndTime",
					a.create_date AS "createDate",
					a.update_date AS "updateDate"
		FROM mock_subject a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="mockExamId">
						a.mock_exam_id = #mockExamId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="paperId">
						a.paper_id = #paperId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="courseId">
						a.course_id = #courseId#
				</isNotEmpty>
		</dynamic>
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
		<dynamic prepend="limit">
			<isPropertyAvailable prepend="" property="pageSize">
				#from#,#pageSize#
			</isPropertyAvailable>
		</dynamic>
	</select>

	<select id="findListCount" parameterClass="java.util.Map" resultClass="java.lang.Integer">
		SELECT count(1)
		FROM mock_subject a
			<dynamic prepend="where">
					<isNotEmpty prepend="AND" property="id">
						a.id = #id#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="mockExamId">
						a.mock_exam_id = #mockExamId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="secondCategory">
						a.second_category = #secondCategory#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="categoryId">
						a.category_id = #categoryId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="paperId">
						a.paper_id = #paperId#
				</isNotEmpty>
					<isNotEmpty prepend="AND" property="courseId">
						a.course_id = #courseId#
				</isNotEmpty>
		</dynamic>
	</select>

	<select id="findAllList" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockSubject">
		SELECT
								a.id AS "id",
					a.mock_exam_id AS "mockExamId",
					a.second_category AS "secondCategory",
					a.category_id AS "categoryId",
					a.paper_id AS "paperId",
					a.course_id AS "courseId",
					a.intro AS "intro",
					a.mock_start_time AS "mockStartTime",
					a.mock_end_time AS "mockEndTime",
					a.create_date AS "createDate",
					a.update_date AS "updateDate"
		FROM mock_subject a
		<dynamic prepend="ORDER BY">
			<isPropertyAvailable prepend="" property="orderBy">
				$orderBy$
			</isPropertyAvailable>
		</dynamic>
	</select>

    <select id="findMockSubjectListByMockId" parameterClass="java.util.Map" resultClass="cn.huanju.edu100.study.model.mock.MockSubject">
        select
        a.id AS "id",
        a.mock_exam_id AS "mockExamId",
        a.second_category AS "secondCategory",
        a.category_id AS "categoryId",
        a.paper_id AS "paperId",
        a.course_id AS "courseId",
        a.intro AS "intro",
        a.mock_start_time AS "mockStartTime",
        a.mock_end_time AS "mockEndTime",
        a.create_date AS "createDate",
        a.update_date AS "updateDate",
        a.sort_num AS "sortNum"
        FROM mock_subject a
        where 1=1

            <isNotEmpty prepend="AND" property="mockExamId">
                a.mock_exam_id = #mockExamId#
            </isNotEmpty>


        ORDER BY sort_num desc, mock_start_time asc
    </select>
</sqlMap>
