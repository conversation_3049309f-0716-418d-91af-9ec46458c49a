<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TestTask">
    
	<sql id="testTaskColumns">
		a.id AS "id",
		a.q_id AS "qId",
		a.task_id AS "taskId",
		a.title AS "title",
		a.purpose AS "purpose"
	</sql>
	
	<sql id="testTaskJoins">
	</sql>
	
	<select id="findListByTaskIdList" parameterClass="java.lang.String"
            resultClass="cn.huanju.edu100.study.model.TestTask">
        SELECT 
			<include refid="testTaskColumns"/>
		FROM test_task a
		WHERE a.task_id IN ($value$)
    </select>
	
</sqlMap>