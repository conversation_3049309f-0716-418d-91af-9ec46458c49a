#########################################
# \u53c2\u6570\u914d\u7f6e
#########################################
#\u6d4b\u8bd5\u5f00\u5173\uff0ctrue\u662f\u6d4b\u8bd5\uff0cfalse \u975e\u6d4b\u8bd5
test_switch = true
#\u6253\u5370\u4e1a\u52a1\u8017\u65f6\u5f00\u5173\uff0ctrue\u662f\u6d4b\u8bd5\uff0cfalse \u975e\u6d4b\u8bd5
log_busi_spend_time = true

#\u4ece\u5e93\u5e73\u6ed1\u5207\u6362\u7b56\u7565
#\u5206\u503c-\u8d1f\u8f7d\u6743\u91cd
mysql_slave1_switch_policy = 0-10,10-8,30-6,40-4,50-2,60-0
mysql_slave2_switch_policy = 0-10,10-8,30-6,40-4,50-2,60-1
mysql_master_notice_point = 10
mysql_slave_notice_point = 10