<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMapConfig
PUBLIC "-//iBATIS.com//DTD SQL Map Config 2.0//EN"
"http://www.ibatis.com/dtd/sql-map-config-2.dtd">
<!-- Always ensure to use the correct XML header as above! -->
<sqlMapConfig>

	<!-- The properties (name=value) in the file specified here can be used 
		placeholders in this config file (e.g. “${driver}”. The file is relative 
		to the classpath and is completely optional. -->
	<!-- <properties resource="SqlMapConfigMaster.properties" /> -->
	<!-- These settings control SqlMap configuration details, primarily to do 
		with transaction management. They are all optional (see the Developer Guide 
		for more). -->

	<settings cacheModelsEnabled="false" enhancementEnabled="false"
		lazyLoadingEnabled="false" errorTracingEnabled="true" maxRequests="128"
		maxSessions="64" maxTransactions="64" useStatementNamespaces="true"
		defaultStatementTimeout="100" statementCachingEnabled="false"
		classInfoCacheEnabled="true" />

	<sqlMap resource="sql/virtualHomework.xml" />
<!--	<sqlMap resource="sql/virtualHomeworkHistory.xml" />-->


</sqlMapConfig>

