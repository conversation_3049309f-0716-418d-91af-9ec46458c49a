<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMapConfig
PUBLIC "-//iBATIS.com//DTD SQL Map Config 2.0//EN"
"http://www.ibatis.com/dtd/sql-map-config-2.dtd">
<!-- Always ensure to use the correct XML header as above! -->
<sqlMapConfig>

	<!-- The properties (name=value) in the file specified here can be used 
		placeholders in this config file (e.g. “${driver}”. The file is relative 
		to the classpath and is completely optional. -->
	<!-- <properties resource="SqlMapConfigMaster.properties" /> -->
	<!-- These settings control SqlMap configuration details, primarily to do 
		with transaction management. They are all optional (see the Developer Guide 
		for more). -->

	<settings cacheModelsEnabled="false" enhancementEnabled="false"
		lazyLoadingEnabled="false" errorTracingEnabled="true" maxRequests="128"
		maxSessions="64" maxTransactions="64" useStatementNamespaces="true"
		defaultStatementTimeout="100" statementCachingEnabled="false"
		classInfoCacheEnabled="true" />

	<!-- Type aliases allow you to use a shorter name for long fully qualified 
		class names. -->
	<!-- Configure a datasource to use with this SQL Map using SimpleDataSource.
		Notice the use of the properties from the above resource -->

	<!-- Identify all SQL Map XML files to be loaded by this SQL map. Notice 
		the paths are relative to the classpath. For now, we only have one… -->
	<sqlMap resource="sql/qbox/qboxUserDoneQuestion.xml" />
	<sqlMap resource="sql/qbox/qboxUserWrongQuestion.xml" />
	<sqlMap resource="sql/qbox/qboxUserWipeOutWrongQuestion.xml" />
	<sqlMap resource="sql/qbox/qboxUserReciteQuestion.xml" />
	<sqlMap resource="sql/qbox/synTaskInfo.xml" />

</sqlMapConfig>

