<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:tx="http://www.springframework.org/schema/tx"
	   xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-4.0.xsd"
	   default-lazy-init="true">

	<bean id="his_master" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
		<property name="configLocation" value="classpath:sqlMapConfig.xml" />
		<property name="dataSource" ref="his_masterDS" />
	</bean>
	<bean id="his_slave1" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
		<property name="configLocation" value="classpath:sqlMapConfig.xml" />
		<property name="dataSource" ref="his_slave1DS" />
	</bean>
	<bean id="his_slave2" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
		<property name="configLocation" value="classpath:sqlMapConfig.xml" />
		<property name="dataSource" ref="his_slave2DS" />
	</bean>

	<bean id="his_datasourceMap" class="java.util.HashMap">
	   <constructor-arg>
	      <map key-type="java.lang.String"  value-type="com.ibatis.sqlmap.client.SqlMapClient">
	         <entry key="master" value-ref="his_master"/>
			  <entry key="slave1" value-ref="his_slave1"/>
              <entry key="slave2" value-ref="his_slave2"/>
			  <entry key="slave" value-ref="his_slave1"/>
          </map>
	   </constructor-arg>
	</bean>

	<bean id="his_slaveDbRoundRobin" class="cn.huanju.edu100.dao.ibatis.robin.WeightedRoundRobin">
		<constructor-arg>
			<list>
				<bean class="cn.huanju.edu100.dao.ibatis.robin.Robin">
				    <constructor-arg name="sname" value="slave1"/>
				    <constructor-arg name="weight" value="1"/>
				</bean>
				<bean class="cn.huanju.edu100.dao.ibatis.robin.Robin">
					<constructor-arg name="sname" value="slave2"/>
				    <constructor-arg name="weight" value="1"/>
				</bean>
			</list>
		</constructor-arg>
	</bean>

	<bean id="his_dataSourceInit" abstract="true" class="cn.huanju.edu100.dao.ibatis.SqlMapClientBase">
		<property name="clientMaps" ref="his_datasourceMap" />
		<property name="slaveDbRoundRobin" ref="his_slaveDbRoundRobin"/>
	</bean>


	<bean id="his_transactionManager"
		  class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource">
			<ref bean="his_masterDS" />
		</property>
	</bean>


	<tx:advice id="his_transactionManagerAdivice"
			   transaction-manager="his_transactionManager">
		<tx:attributes>
			<tx:method name="*" isolation="READ_COMMITTED" propagation="REQUIRED"
					   rollback-for="java.lang.Exception" />
		</tx:attributes>
	</tx:advice>


	<aop:config>
		<aop:pointcut expression="execution(* cn.huanju.edu100.study.service.impl.*.delete*(..))
		|| execution(* cn.huanju.edu100.study.service.impl.*.insert*(..))
		|| execution(* cn.huanju.edu100.study.service.impl.*.update*(..))
		|| execution(* cn.huanju.edu100.study.service.impl.*.modify*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.insert*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.update*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.modify*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.save*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.delete*(..))

		 " id="his_auditTransaction"/>

		<aop:advisor advice-ref="his_transactionManagerAdivice"
					 pointcut-ref="his_auditTransaction" />
	</aop:config>



	<tx:annotation-driven transaction-manager="his_transactionManager" order="0"/>
	<context:annotation-config />
	<context:annotation-config />
</beans>
