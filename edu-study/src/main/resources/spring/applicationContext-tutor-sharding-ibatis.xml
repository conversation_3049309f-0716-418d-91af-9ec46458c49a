<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd"
       default-lazy-init="true">

    <bean id="tutormaster" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:sqlMapConfig.xml" />
        <property name="dataSource" ref="tutorDs" />
    </bean>
    <bean id="tutorshardingMaster" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:sqlMapConfig.xml" />
        <property name="dataSource" ref="tutorDs" />
    </bean>
    <bean id="tutorslave1" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:sqlMapConfig.xml" />
        <property name="dataSource" ref="tutorDs" />
    </bean>
    <bean id="tutorslave2" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:sqlMapConfig.xml" />
        <property name="dataSource" ref="tutorDs" />
    </bean>

    <bean id="tutordatasourceMap" class="java.util.HashMap">
        <constructor-arg>
            <map key-type="java.lang.String" value-type="com.ibatis.sqlmap.client.SqlMapClient">
                <entry key="master" value-ref="tutormaster"/>
                <entry key="shardingmaster" value-ref="tutorshardingMaster"/>
                <entry key="slave1" value-ref="tutorslave1"/>
                <entry key="slave2" value-ref="tutorslave2"/>
                <entry key="slave" value-ref="tutorslave1"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="tutorslaveDbRoundRobin" class="cn.huanju.edu100.dao.ibatis.robin.WeightedRoundRobin">
        <constructor-arg>
            <list>
                <bean class="cn.huanju.edu100.dao.ibatis.robin.Robin">
                    <constructor-arg name="sname" value="slave1"/>
                    <constructor-arg name="weight" value="1"/>
                </bean>
                <bean class="cn.huanju.edu100.dao.ibatis.robin.Robin">
                    <constructor-arg name="sname" value="slave2"/>
                    <constructor-arg name="weight" value="1"/>
                </bean>
            </list>
        </constructor-arg>
    </bean>

    <bean id="tutorDataSourceInit" abstract="true" class="cn.huanju.edu100.dao.ibatis.SqlMapClientBase">
        <property name="clientMaps" ref="tutordatasourceMap" />
        <property name="slaveDbRoundRobin" ref="tutorslaveDbRoundRobin"/>
    </bean>
</beans>
