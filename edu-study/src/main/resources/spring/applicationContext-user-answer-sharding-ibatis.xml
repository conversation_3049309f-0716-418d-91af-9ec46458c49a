<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-4.0.xsd"
       default-lazy-init="true">

    <bean id="ua_shardingMaster" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:userAnswer_sqlMapConfig.xml" />
        <property name="dataSource" ref="userAnswerDs" />
    </bean>
    <bean id="ua_slave1" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:userAnswer_sqlMapConfig.xml" />
        <property name="dataSource" ref="userAnswerDs" />
    </bean>
    <bean id="ua_slave2" class="cn.huanju.edu100.study.config.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:userAnswer_sqlMapConfig.xml" />
        <property name="dataSource" ref="userAnswerDs" />
    </bean>

    <bean id="ua_datasourceMap" class="java.util.HashMap">
        <constructor-arg>
            <map key-type="java.lang.String" value-type="com.ibatis.sqlmap.client.SqlMapClient">
                <entry key="master" value-ref="ua_shardingMaster"/>
                <entry key="shardingmaster" value-ref="ua_shardingMaster"/>
                <entry key="slave1" value-ref="ua_slave1"/>
                <entry key="slave2" value-ref="ua_slave2"/>
                <entry key="slave" value-ref="ua_slave1"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="ua_slaveDbRoundRobin" class="cn.huanju.edu100.dao.ibatis.robin.WeightedRoundRobin">
        <constructor-arg>
            <list>
                <bean class="cn.huanju.edu100.dao.ibatis.robin.Robin">
                    <constructor-arg name="sname" value="slave1"/>
                    <constructor-arg name="weight" value="1"/>
                </bean>
                <bean class="cn.huanju.edu100.dao.ibatis.robin.Robin">
                    <constructor-arg name="sname" value="slave2"/>
                    <constructor-arg name="weight" value="1"/>
                </bean>
            </list>
        </constructor-arg>
    </bean>

    <bean id="ua_dataSourceInit" abstract="true" class="cn.huanju.edu100.dao.ibatis.SqlMapClientBase">
        <property name="clientMaps" ref="ua_datasourceMap" />
        <property name="slaveDbRoundRobin" ref="ua_slaveDbRoundRobin"/>
    </bean>


    <bean id="ua_transactionShardingManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="userAnswerDs" />
    </bean>


    <tx:advice id="ua_transactionShardingManagerAdivice"
               transaction-manager="ua_transactionShardingManager">
        <tx:attributes>
            <tx:method name="*" isolation="READ_COMMITTED" propagation="REQUIRED"
                       rollback-for="java.lang.Exception" />
        </tx:attributes>
    </tx:advice>

    <aop:config>
        <aop:pointcut expression="execution(* cn.huanju.edu100.study.service.impl.*.delete*(..))
		|| execution(* cn.huanju.edu100.study.service.impl.*.insert*(..))
		|| execution(* cn.huanju.edu100.study.service.impl.*.update*(..))
		|| execution(* cn.huanju.edu100.study.service.impl.*.modify*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.insert*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.update*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.modify*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.save*(..))
		|| execution(* cn.huanju.edu100.persistence.service.impl.*.delete*(..))

		 " id="ua_auditShardingTransaction"/>

        <aop:advisor advice-ref="ua_transactionShardingManagerAdivice"
                     pointcut-ref="ua_auditShardingTransaction" />
    </aop:config>

    <tx:annotation-driven transaction-manager="ua_transactionShardingManager" order="0"/>
    <context:annotation-config />
</beans>
