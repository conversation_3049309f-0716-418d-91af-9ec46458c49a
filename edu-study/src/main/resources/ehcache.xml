<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd"
 updateCheck="false">
 <diskStore path="java.io.tmpdir" />
 <defaultCache
        maxElementsInMemory="100"
        eternal="true"
        overflowToDisk="false"
        diskPersistent="false"
        diskExpiryThreadIntervalSeconds="120"/>
        
    <cache name="knowledgeCache"
            maxElementsInMemory="1000"
            maxElementsOnDisk="100000"
            eternal="false"
            overflowToDisk="false"
			timeToLiveSeconds="1200"
           timeToIdleSeconds="1000"
            memoryStoreEvictionPolicy="LRU"
            />    
    
    <cache name="resourceCache"
    		maxElementsInMemory="10000"
    		maxElementsOnDisk="1000000"
    		eternal="false"
    		overflowToDisk="false"
			timeToLiveSeconds="1200"
           timeToIdleSeconds="1000"
            memoryStoreEvictionPolicy="LRU"
    		/>
    <cache name="questionCache"
           maxElementsInMemory="10000"
           maxElementsOnDisk="1000000"
           eternal="false"
           overflowToDisk="false"
           timeToLiveSeconds="600"
           timeToIdleSeconds="400"
           memoryStoreEvictionPolicy="LRU"
            />
    <cache name="taskCache"
           maxElementsInMemory="10000"
           maxElementsOnDisk="1000000"
           eternal="false"
           overflowToDisk="false"
           timeToLiveSeconds="600"
           timeToIdleSeconds="400"
           memoryStoreEvictionPolicy="LRU"
            />
    <cache name="paperCache"
           maxElementsInMemory="10000"
           maxElementsOnDisk="1000000"
           eternal="false"
           overflowToDisk="false"
           timeToLiveSeconds="600"
           timeToIdleSeconds="400"
           memoryStoreEvictionPolicy="LRU"
            />
    <cache name="bulletinCache"
           maxElementsInMemory="10000"
           maxElementsOnDisk="1000000"
           eternal="false"
           overflowToDisk="false"
           timeToLiveSeconds="600"
           timeToIdleSeconds="400"
           memoryStoreEvictionPolicy="LRU"
            />
    <cache name="goodsCache"
           maxElementsInMemory="10000"
           maxElementsOnDisk="1000000"
           eternal="false"
           overflowToDisk="false"
           timeToLiveSeconds="600"
           timeToIdleSeconds="400"
           memoryStoreEvictionPolicy="LRU"
    />
</ehcache>

