<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd">

       <!-- 定义producer的参数 -->
       <bean id="producerProperties" class="java.util.HashMap">
              <constructor-arg>
                     <map>
                            <entry key="bootstrap.servers" value="${kafa.servers}" />
                            <entry key="group.id" value="0" />
                            <entry key="retries" value="1" />
                            <entry key="batch.size" value="16384" />
                            <entry key="linger.ms" value="1" />
                            <entry key="buffer.memory" value="33554432" />
                            <entry key="key.serializer"
                                   value="org.apache.kafka.common.serialization.StringSerializer" />
                            <entry key="value.serializer"
                                   value="org.apache.kafka.common.serialization.StringSerializer" />
                     </map>
              </constructor-arg>
       </bean>

       <!-- 创建kafkatemplate需要使用的producerfactory bean -->
       <bean id="producerFactory"
             class="org.springframework.kafka.core.DefaultKafkaProducerFactory">
              <constructor-arg>
                     <ref bean="producerProperties" />
              </constructor-arg>
       </bean>

       <!-- 创建kafkatemplate bean，使用的时候，只需要注入这个bean，即可使用template的send消息方法 -->
       <bean id="KafkaTemplate" class="org.springframework.kafka.core.KafkaTemplate">
              <constructor-arg ref="producerFactory" />
              <constructor-arg name="autoFlush" value="true" />
              <property name="defaultTopic" value="replicated-topic" />
              <property name="producerListener" ref="producerListener"/>
       </bean>

       <bean id="producerListener" class="cn.huanju.edu100.study.kafka.KafkaProducerListener" />

       <bean id="questionStaticProducerService" class="cn.huanju.edu100.study.kafka.question.KafkaMsgProducerService" >
              <property name="kafkaTemplate" ref="KafkaTemplate"/>
       </bean>

       <bean id="fundProducerService" class="cn.huanju.edu100.study.kafka.question.KafkaMsgProducerService" >
              <property name="kafkaTemplate" ref="KafkaTemplate"/>
       </bean>


       <!-- 定义producer的参数 -->
       <bean id="secretProducerProperties" class="java.util.HashMap">
              <constructor-arg>
                     <map>
                            <entry key="bootstrap.servers" value="${kafka.secret.servers}" />
                            <entry key="group.id" value="0" />
                            <entry key="retries" value="1" />
                            <entry key="batch.size" value="16384" />
                            <entry key="linger.ms" value="1" />
                            <entry key="buffer.memory" value="33554432" />
                            <entry key="key.serializer"
                                   value="org.apache.kafka.common.serialization.StringSerializer" />
                            <entry key="value.serializer"
                                   value="org.apache.kafka.common.serialization.StringSerializer" />

                     </map>
              </constructor-arg>
       </bean>

       <!-- 加密集群配置 -->
       <!-- 创建kafkatemplate需要使用的producerfactory bean -->
       <bean id="secretProducerFactory"
             class="org.springframework.kafka.core.DefaultKafkaProducerFactory">
              <constructor-arg>
                     <ref bean="secretProducerProperties" />
              </constructor-arg>
       </bean>
       <bean id="secretProducerListener" class="cn.huanju.edu100.study.kafka.KafkaProducerListener" />

       <!-- 创建kafkatemplate bean，使用的时候，只需要注入这个bean，即可使用template的send消息方法 -->
       <bean id="secretKafkaTemplate" class="org.springframework.kafka.core.KafkaTemplate">
              <constructor-arg ref="secretProducerFactory" />
              <constructor-arg name="autoFlush" value="true" />
              <property name="defaultTopic" value="replicated-topic" />
              <property name="producerListener" ref="secretProducerListener"/>
       </bean>
       <bean id="secretStaticProducerService" class="cn.huanju.edu100.study.kafka.question.KafkaMsgProducerService" >
              <property name="kafkaTemplate" ref="secretKafkaTemplate"/>
       </bean>

</beans>
