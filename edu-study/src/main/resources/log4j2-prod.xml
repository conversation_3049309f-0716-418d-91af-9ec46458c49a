<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">

    <!-- 控制台输出 -->
    <Properties>
        <Property name="Log_Pattern" value="%d{yyyy-MM-dd HH:mm:ss,SSS} [%X{ip}] [%thread] [%X{traceId}] %-5level %logger{50}:%L - %msg%n" />
        <Property name="Log_STUOUT" value="%style{%d{yyyy-MM-dd HH:mm:ss.SSS}}{green} [%X{ip}] [%thread] [%highlight{%X{traceId}}] %style{%-5level}{cyan} %style{%c{1.}.%M(%L)}{yellow}: - %msg%n" />
        <Property name="log_file_path">/data/weblog/edu100/edu-study/${sys:subLogPath:-}</Property>
    </Properties>

    <!-- 控制台输出 -->
    <Appenders>
        <Console name="CONSOLE" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${Log_STUOUT}" disableAnsi="false" noConsoleNoAnsi="false"/>
        </Console>

        <!-- 按照每天生成日志文件 -->
        <RollingRandomAccessFile name="ROLL_FILE_ROOT" fileName="${log_file_path}/study_service.log"
                      filePattern="${log_file_path}/study_service.%d{yyyy-MM-dd}-%i.log"
                      append="true">
            <PatternLayout pattern="${Log_Pattern}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="500000KB" />
                <TimeBasedTriggeringPolicy modulate="true" />
            </Policies>
            <DefaultRolloverStrategy max="1000"/>
        </RollingRandomAccessFile>
    </Appenders>
    <Loggers>
        <AsyncLogger name="com.ibatis" level="debug">
            <AppenderRef ref="ROLL_FILE_ROOT" />
            <AppenderRef ref="STDOUT" />
        </AsyncLogger>

        <AsyncLogger name="com.mysql" level="WARN" additivity="false">
            <AppenderRef ref="ROLL_FILE_ROOT" />
        </AsyncLogger>
        <AsyncLogger name="com.duowan.udb" level="INFO" >
            <AppenderRef ref="ROLL_FILE_ROOT" />
        </AsyncLogger>
        <AsyncLogger name="com.hqwx" level="INFO" />
        <AsyncLogger name="cn.huanju" level="INFO"  />
        <AsyncLogger name="cn.huanju.edu100.study.service.impl.solution" level="DEBUG"  />
        <AsyncLogger name="com.duowan.udb" level="INFO" />
        <Root level="INFO">
            <AppenderRef ref="ROLL_FILE_ROOT" />
        </Root>
    </Loggers>
</Configuration>
