package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * <AUTHOR>
 * @version 2015-05-14
 */

public class QuestionAnswerStatics extends DataEntity<QuestionAnswerStatics> {
    //答题数
    private long answerAmount;
    //答题正确率
    private String rightAnswerPercent;
    // 答题错误率
    private String wrongAnswerPercent;

    public long getAnswerAmount() {
        return answerAmount;
    }

    public void setAnswerAmount(long answerAmount) {
        this.answerAmount = answerAmount;
    }

    public String getRightAnswerPercent() {
        return rightAnswerPercent;
    }

    public void setRightAnswerPercent(String rightAnswerPercent) {
        this.rightAnswerPercent = rightAnswerPercent;
    }

    public String getWrongAnswerPercent() {
        return wrongAnswerPercent;
    }

    public void setWrongAnswerPercent(String wrongAnswerPercent) {
        this.wrongAnswerPercent = wrongAnswerPercent;
    }
}
