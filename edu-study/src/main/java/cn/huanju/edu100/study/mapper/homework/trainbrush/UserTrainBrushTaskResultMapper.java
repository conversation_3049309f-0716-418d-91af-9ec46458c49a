package cn.huanju.edu100.study.mapper.homework.trainbrush;

import cn.huanju.edu100.study.model.homework.trainbrush.UserTrainBrushTaskResult;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Repository
@Mapper
@DS("al-default-ds")
public interface UserTrainBrushTaskResultMapper extends BaseMapper<UserTrainBrushTaskResult> {
}
