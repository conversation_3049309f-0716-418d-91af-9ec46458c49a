package cn.huanju.edu100.study.dao.ibatis.impl.evaluation;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.evaluation.EvaluationUserBaseAnswerDetailDao;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.model.evaluation.EvaluationUserBaseAnswerDetail;
import com.hqwx.study.dto.EvaluationUserBaseAnswerDetailDTO;
import com.hqwx.study.dto.query.EvaluationUserBaseAnswerDetailQuery;
import com.ibatis.sqlmap.client.SqlMapClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;


@Slf4j
public class EvaluationUserBaseAnswerDetailIbatisImpl extends CrudIbatisImpl2<EvaluationUserBaseAnswerDetail> implements EvaluationUserBaseAnswerDetailDao {

	public EvaluationUserBaseAnswerDetailIbatisImpl() {
		super("EvaluationUserBaseAnswerDetail");
	}

	@Override
	public void insertBatch(List<EvaluationUserBaseAnswerDetail> list) throws DataAccessException {
		try {
			if (CollectionUtils.isEmpty(list)) {
				logger.error("insertBatch error, param is empty");
				throw new DataAccessException("insertBatch error, param is empty");
			}
			SqlMapClient sqlMap = super.getMaster();
			sqlMap.insert(namespace.concat(".insertBatch"), list);
		} catch (SQLException e) {
			logger.error("insertBatch SQLException. list:{}", list, e);
			throw new DataAccessException("insertBatch SQLException error");
		}
	}

	@Override
	public Integer getEvaluationUserBaseAnswerDetailCount(EvaluationUserBaseAnswerDetailQuery query) throws DataAccessException {
		if (query == null || query.getUid() == null) {
			String tip = "getEvaluationUserBaseAnswerDetailCount error, param is empty";
			logger.error(tip);
			throw new DataAccessException(tip);
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Integer ret = (Integer) sqlMap.queryForObject(namespace.concat(".getEvaluationUserBaseAnswerDetailCount"), query);
			if (ret == null) {
				ret = 0;
			}
			return ret;
		} catch (SQLException e) {
			logger.error("getEvaluationUserBaseAnswerDetailCount SQLException. query:{}", query, e);
			throw new DataAccessException("getEvaluationUserBaseAnswerDetailCount SQLException error");
		}
	}

	@Override
	public List<EvaluationUserBaseAnswerDetailDTO> getEvaluationUserBaseAnswerDetailList(EvaluationUserBaseAnswerDetailQuery query) throws DataAccessException {
		if (query == null || query.getUid() == null) {
			String tip = "getEvaluationUserBaseAnswerDetailList error, param is empty";
			logger.error(tip);
			throw new DataAccessException(tip);
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			List<EvaluationUserBaseAnswerDetailDTO> ret = (List<EvaluationUserBaseAnswerDetailDTO>) sqlMap.queryForList(namespace.concat(".getEvaluationUserBaseAnswerDetailList"), query);
			return ret;
		} catch (SQLException e) {
			logger.error("getEvaluationUserBaseAnswerDetailList SQLException. query:{}", query, e);
			throw new DataAccessException("getEvaluationUserBaseAnswerDetailList SQLException error");
		}
	}

}
