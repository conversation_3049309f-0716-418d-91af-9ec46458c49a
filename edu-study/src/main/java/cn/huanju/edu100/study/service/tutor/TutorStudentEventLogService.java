package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorStudentEventLog;
import cn.huanju.edu100.study.model.tutor.TutorStudentEventLogListWrap;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Map;

/**
 * 用户关键事件记录Service
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentEventLogService extends BaseService<TutorStudentEventLog> {

    /**
     * 展示用户关键事件列表
     *
     * @param params
     * @return
     * @throws DataAccessException
     */
    TutorStudentEventLogListWrap listKeyEventLogByUidType(Map<String, Object> params) throws DataAccessException;
}
