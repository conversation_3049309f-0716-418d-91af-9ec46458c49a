package cn.huanju.edu100.study.model.expression;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 规则关联Entity
 * <AUTHOR>
 * @version 2016-05-23
 */
public class ExpressionMember extends DataEntity<ExpressionMember> {
	
	private static final long serialVersionUID = 1L;
	private Integer type;		// 规则类型：0规则，1规则组
	private Long ruleId;		// 规则id
	private Long groupId;		// 规则组id
	private Long seq;		// seq
	
	public ExpressionMember() {
		super();
	}

	public ExpressionMember(Long id){
		super(id);
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public Long getRuleId() {
		return ruleId;
	}

	public void setRuleId(Long ruleId) {
		this.ruleId = ruleId;
	}
	
	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	
	public Long getSeq() {
		return seq;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}
	
}