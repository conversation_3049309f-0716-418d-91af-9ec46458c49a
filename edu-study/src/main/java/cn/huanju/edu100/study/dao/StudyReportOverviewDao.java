/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.StudyReportOverview;
import cn.huanju.edu100.exception.DataAccessException;

/**
 * 作业学习DAO接口
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface StudyReportOverviewDao extends CrudDao<StudyReportOverview> {
	/**根据任务ID集查找到对应的Homework结果集*/
	public StudyReportOverview findObjectByUid(Long uid) throws DataAccessException;
}
