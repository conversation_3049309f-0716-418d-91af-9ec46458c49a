/**
 * Copyright (c) 2011 duowan.com. 
 * All Rights Reserved.
 * This program is the confidential and proprietary information of 
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.model;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class StudyTaskPushResResponse {

    private long taskId;
    private List<WeiCourse> weiCourses = new ArrayList<WeiCourse>();

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public List<WeiCourse> getWeiCourses() {
        return weiCourses;
    }

    public void setWeiCourses(List<WeiCourse> weiCourses) {
        this.weiCourses = weiCourses;
    }

}
