package cn.huanju.edu100.study.service.impl.mock;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.mock.MockExamDao;
import cn.huanju.edu100.study.model.mock.MockExam;
import cn.huanju.edu100.study.service.mock.MockExamService;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 模考活动Service
 * <AUTHOR>
 * @version 2018-04-08
 */
@Service
public class MockExamServiceImpl extends BaseServiceImpl<MockExamDao, MockExam> implements MockExamService {
    private static Logger logger = LoggerFactory.getLogger(MockExamServiceImpl.class);
    private static Gson gson = GsonUtil.getGson();

    @Autowired
    private MockExamDao mockExamDao;

    @Override
    public List<MockExam> qryBySecCategoryAndStatus(Long secondCategory, Integer status) throws DataAccessException {
        return mockExamDao.qryBySecCategoryAndStatus(secondCategory, status);
    }

    @Override
    public List<MockExam> qryBySecCategoryAndStatusAndAreaId(Long secondCategory, Integer status, Long areaId) throws DataAccessException {

        return mockExamDao.findMockExamListBySecCategoryAndStatusAndAreaId(secondCategory, status, areaId);
    }

    @Override
    public List<Long> findMockAreaIdListBySecondCategoryId(Long secondCategory) throws DataAccessException{

        return mockExamDao.findMockAreaIdListBySecondCategoryId(secondCategory);
    }

    @Override
    public List<MockExam> findMyMockList(Long secondCategory, Long uid, int from, int rows) throws DataAccessException {
        return mockExamDao.findMyMockList(secondCategory,uid,from, rows );
    }

}
