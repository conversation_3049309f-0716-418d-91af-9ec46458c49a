/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorTaskResourceDao;
import cn.huanju.edu100.study.model.tutor.TutorTaskResource;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资源类任务DAO接口
 * <AUTHOR>
 * @version 2016-01-18
 */
public class TutorTaskResourceIbatisImpl extends CrudIbatisImpl2<TutorTaskResource> implements
		TutorTaskResourceDao {

	public TutorTaskResourceIbatisImpl() {
		super("TutorTaskResource");
	}

    @Override
    public List<TutorTaskResource> getByIdList(List<Long> taskIdList) throws DataAccessException {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return null;
        }

        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("taskIdList", taskIdList);

            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorTaskResource>) sqlMap
                    .queryForList("TutorTaskResource.findList", params);
        } catch (SQLException e) {
            logger.error("getByIdList SQLException.", e);
            throw new DataAccessException("getByIdList SQLException error");
        }
    }

}
