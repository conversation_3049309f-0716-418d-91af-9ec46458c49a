package cn.huanju.edu100.study.repository.question;


import cn.huanju.edu100.study.mapper.question.UserHistoryErrorQuestionAddLogMapper;
import cn.huanju.edu100.study.model.UserHistoryErrorQuestionAddLog;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class UserHistoryErrorQuestionAddLogRepository {
    @Autowired
    private UserHistoryErrorQuestionAddLogMapper userHistoryErrorQuestionAddLogMapper;

    public List<UserHistoryErrorQuestionAddLog> selectByUidAndGoodsIdAndCategoryId(Long uid, Long goodsId, Long categoryId) {
        LambdaQueryWrapper<UserHistoryErrorQuestionAddLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserHistoryErrorQuestionAddLog::getUid, uid);
        queryWrapper.eq(UserHistoryErrorQuestionAddLog::getGoodsId, goodsId);
        queryWrapper.eq(Objects.nonNull(categoryId), UserHistoryErrorQuestionAddLog::getCategoryId, categoryId);
        return userHistoryErrorQuestionAddLogMapper.selectList(queryWrapper);
    }

    public int insert(UserHistoryErrorQuestionAddLog userHistoryErrorQuestionAddLog) {
        userHistoryErrorQuestionAddLog.setCreateDate(new Date());
        return userHistoryErrorQuestionAddLogMapper.insert(userHistoryErrorQuestionAddLog);
    }

    public int updateById(UserHistoryErrorQuestionAddLog userHistoryErrorQuestionAddLog) {
        userHistoryErrorQuestionAddLog.setUpdateDate(new Date());
        return userHistoryErrorQuestionAddLogMapper.updateById(userHistoryErrorQuestionAddLog);
    }
}
