package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.homework.papercorrection.CorrectionRecordCmd;
import cn.huanju.edu100.study.model.homework.papercorrection.CorrectionRecordQuery;
import cn.huanju.edu100.study.model.homework.papercorrection.PaperCorrectionRecord;
import cn.huanju.edu100.study.model.homework.papercorrection.PaperCorrectionSum;
import cn.huanju.edu100.study.service.homework.papercorrection.PaperCorrectionRecordService;
import cn.huanju.edu100.study.service.homework.papercorrection.PaperCorrectionSumService;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class PaperCorrectionThriftImpl extends AbstractServiceThrift{
    private static Logger logger = LoggerFactory.getLogger(PaperCorrectionThriftImpl.class);

    private static Gson gson = GsonUtil.getGenericGson();

    @Autowired
    private PaperCorrectionSumService paperCorrectionSumService;

    @Autowired
     private PaperCorrectionRecordService paperCorrectionRecordService;

    /**
     * 获取论文批改次数
    * */
    response sty_getPaperCorrectionSum(request req) throws BusinessException{

        String entry = "sty_getPaperCorrectionSum";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            CorrectionRecordQuery query = gson.fromJson(req.getMsg(), CorrectionRecordQuery.class);
            if (query == null ||  query.getUid() == null
                    || query.getGoodsId() == null || query.getOrderId() == null || query.getServiceId() == null) {
                logger.error("{} fail.parameter serviceId or uid or goodsId or orderId  is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter serviceId or uid or goodsId or orderId  is null is null.");
            }
            PaperCorrectionSum paperCorrectionSum = paperCorrectionSumService.findPaperCorrectionSum(query);
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.toJson(paperCorrectionSum, req.getAppid()));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 提交论文批改
    * */
    response sty_createAiCorrectionRecord(request req) throws BusinessException{
        String entry = "sty_createAiCorrectionRecord";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            CorrectionRecordCmd cmd = gson.fromJson(req.getMsg(), CorrectionRecordCmd.class);
            if (cmd == null ||  cmd.getUid() == null
                    || cmd.getGoodsId() == null || cmd.getOrderId() == null || cmd.getServiceId() == null) {
                logger.error("{} fail.parameter serviceId or uid or goodsId or orderId  is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter serviceId or uid or goodsId or orderId  is null is null.");
            }
            PaperCorrectionRecord data = paperCorrectionRecordService.createAiCorrectionRecord(cmd);
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.toJson(data, req.getAppid()));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 查找论文批改记录
     * */
    response sty_findCorrectionRecordList(request req) throws BusinessException{
        String entry = "sty_findCorrectionRecordList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            CorrectionRecordQuery query = gson.fromJson(req.getMsg(), CorrectionRecordQuery.class);
            if (query == null ||  query.getUid() == null
                    || query.getGoodsId() == null || query.getOrderId() == null || query.getServiceId() == null) {
                logger.error("{} fail.parameter serviceId or uid or goodsId or orderId  is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter serviceId or uid or goodsId or orderId  is null is null.");
            }
            List<PaperCorrectionRecord> data = paperCorrectionRecordService.findCorrectionRecordList(query);
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.toJson(data, req.getAppid()));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

}
