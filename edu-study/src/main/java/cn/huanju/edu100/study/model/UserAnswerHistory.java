package cn.huanju.edu100.study.model;

/**
 * @description: 我是干啥的
 * @author: wusiyue
 * @create: 2020-02-18 15:16
 **/

public class UserAnswerHistory {
    private Long uid;
    private Long answer_id;
    private Integer answer_type;
    private Long category_id;
    private Long paper_id;
    private Integer score;
    private String update_date;

    private Long question_id;
    private Integer is_right;
    private Integer sub_score;

    //条件
    private Long sum_id;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getAnswer_id() {
        return answer_id;
    }

    public void setAnswer_id(Long answer_id) {
        this.answer_id = answer_id;
    }

    public Integer getAnswer_type() {
        return answer_type;
    }

    public void setAnswer_type(Integer answer_type) {
        this.answer_type = answer_type;
    }

    public Long getCategory_id() {
        return category_id;
    }

    public void setCategory_id(Long category_id) {
        this.category_id = category_id;
    }

    public Long getPaper_id() {
        return paper_id;
    }

    public void setPaper_id(Long paper_id) {
        this.paper_id = paper_id;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getUpdate_date() {
        return update_date;
    }

    public void setUpdate_date(String update_date) {
        this.update_date = update_date;
    }


    public Long getQuestion_id() {
        return question_id;
    }

    public void setQuestion_id(Long question_id) {
        this.question_id = question_id;
    }

    public Integer getIs_right() {
        return is_right;
    }

    public void setIs_right(Integer is_right) {
        this.is_right = is_right;
    }

    public Integer getSub_score() {
        return sub_score;
    }

    public void setSub_score(Integer sub_score) {
        this.sub_score = sub_score;
    }

    public Long getSum_id() {
        return sum_id;
    }

    public void setSum_id(Long sum_id) {
        this.sum_id = sum_id;
    }
}
