package cn.huanju.edu100.study.service.impl.answer;

import cn.huanju.edu100.study.model.QuestionGroupRelation;
import cn.huanju.edu100.study.model.QuestionOptions;
import cn.huanju.edu100.study.model.QuestionTopic;
import com.hqwx.study.entity.UserAnswerDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/5/15.
 */
public class FillAnswerJudger extends AbstractAnswerJudger {


    @Override
    public int judge(QuestionTopic topic, String[] answer) {
    	//主观题判断修改，只要作答就认为对。update by linyl 20160301
        if (answer ==null ||answer.length==0) {
        	return UserAnswerDetail.IsRight.NOT_ANSWER;
        }
        int empCnt = 0;
        for (String answerStr : answer) {
			if ("".equals(answerStr)) {
				empCnt++;
			}
		}
        if (empCnt == answer.length) {//作答全部都是空的
        	return UserAnswerDetail.IsRight.NOT_ANSWER;
		}

        List<QuestionOptions> options = topic.getOptionList();

        if (CollectionUtils.isEmpty(options)) {
        	return UserAnswerDetail.IsRight.WRONG;
        }

        for(QuestionOptions questionOption:options){
            if(StringUtils.isNotBlank(questionOption.getContent())){
                Document document = Jsoup.parse(StringEscapeUtils.unescapeHtml(questionOption.getContent()));
                questionOption.setContent(document.text());
            }
        }

        String[] userAnswers = answer;

        int rightCount = 0;
        for (int i = 0; i < userAnswers.length; i++) {
            QuestionOptions option = options.get(i);
            String userAnswer = userAnswers[i];
            if (StringUtils.isBlank(option.getContent()) || StringUtils.isBlank(userAnswer)) {
                continue;
            }
            String[] rightAnswers = option.getContent().split(",");

            if (ArrayUtils.contains(rightAnswers,userAnswer)) {
                rightCount++;
            }
        }
        if (rightCount == 0) {
            return UserAnswerDetail.IsRight.WRONG;
        }
        if (rightCount == options.size()) {
            return UserAnswerDetail.IsRight.RIGHT;
        }
        return UserAnswerDetail.IsRight.HALF_RIGHT;
    }

    @Override
    public double calculateScore(QuestionTopic topic, String[] answer,Long paperId) {
    	//主观题得分修改，不论对错都不计分。update by linyl 20160301
//    	  Double score = getScore(topic,paperId);
//
//        int isRight = judge(topic,answer);//获得是否正确
//        //update by linyl 20160301
//        if (isRight == UserAnswerDetail.IsRight.WRONG || isRight == UserAnswerDetail.IsRight.NOT_ANSWER) {//如果答错或未作答
//        	return 0;
//        }
//        if (isRight == UserAnswerDetail.IsRight.RIGHT) {//如果答对
//        	return score;
//        }
//
//        //如果部分正确（TODO：暂未实现）
//        List<QuestionOptions> options = topic.getOptionList();
//        String[] userAnswers = answer;
//
//        double deserveScore = 0;
//        for (int i = 0; i < userAnswers.length; i++) {
//            QuestionOptions option = options.get(i);
//            String userAnswer = userAnswers[i];
//            if (StringUtils.isBlank(option.getContent()) || StringUtils.isBlank(userAnswer)) continue;
//            String[] rightAnswers = option.getContent().split(",");
//            if (ArrayUtils.contains(rightAnswers,userAnswer)) {
//                deserveScore += score * option.getScoreProp() / 100;
//            }
//        }
//        return toFixed(deserveScore,1);填空题默认不算分
//        return 0D;
    	// 主观题，只要作答，就给分。update by linyl 20160401
    	Double score = getScore(topic,paperId);

        return judge(topic,answer) ==  UserAnswerDetail.IsRight.RIGHT ? score : 0;
    }

    @Override
    public double calculateScores(QuestionTopic topic, String[] answer,Long paperId,Map<Long,QuestionGroupRelation> map) {
        //主观题得分修改，不论对错都不计分。update by linyl 20160301

        // 主观题，只要作答，就给分。update by linyl 20160401
        Double score = getScores(topic,paperId,map);

        return judge(topic,answer) ==  UserAnswerDetail.IsRight.RIGHT ? score : 0;
    }
}
