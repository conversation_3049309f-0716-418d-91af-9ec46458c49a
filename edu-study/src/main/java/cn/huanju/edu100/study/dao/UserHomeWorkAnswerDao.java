/**
 * 
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import cn.huanju.edu100.study.model.adminstudy.HomeworkCompletion;
import cn.huanju.edu100.study.model.adminstudy.StudyCompletionInfoQuery;
import cn.huanju.edu100.study.model.adminstudy.StudyReportQuery;
import cn.huanju.edu100.study.model.dto.UserAnswerCount;
import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.dto.UserHomeworkAnswerDTO;
import com.hqwx.study.dto.query.UserHomeworkAnswerQuery;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户作业作答DAO接口
 * <AUTHOR>
 * @version 2015-05-12
 */
public interface UserHomeWorkAnswerDao extends CrudDao<UserHomeWorkAnswer> {

	List<UserHomeWorkAnswer> findUserHomeWorkInfo(
			HashMap<String, Object> queryParam) throws DataAccessException;

	int findUserHomeWorkInfoCount(HashMap<String, Object> queryParam)throws DataAccessException;

	UserHomeWorkAnswer getLatestLessonHomeWork(HashMap<String, Object> queryParam) throws DataAccessException;

	List<UserHomeWorkAnswer> getLatestLessonHomeWorkByObjIds(HashMap<String, Object> queryParam) throws DataAccessException;

	UserHomeWorkAnswer getUserHomeWorkAnswerById(Long uid, Long user_homework_id)throws DataAccessException;

	List<UserHomeWorkAnswer> getUserHomeWorkAnswersByIdList(Long uid, List<Long> user_homework_ids)throws DataAccessException;

	List<UserHomeWorkAnswer> getUserHomeWorkAnswersByUserHomeworkIds(Long uid, List<Long> homeworkIdList)throws DataAccessException;

	List<Long> findNeedDealUser(HashMap<String, Object> queryParam)throws DataAccessException;

	List<UserAnswerDetail> findHomeWorkDetailsByObjType(Map<String, Object> queryPara) throws DataAccessException;

	List<UserHomeWorkAnswer> queryUserSubmitHomeworkQuestionsByGoodsIdAndLessonIds(Long uid, Long goodsId, List<Long> lessonIds) throws DataAccessException;

	List<UserHomeWorkAnswer> queryStudyStatisticsByParam(Long uid,Long productId,String endTime) throws DataAccessException;

    Long queryStudyLengthByParam(Long uid, Long productId,String startTime, String endTime) throws DataAccessException;

    Long countUserSubmitHomeworkQuestionsByGoodsIdAndLessonIds(Long uid, Long goodsId, List<Long> lessonIds) throws DataAccessException;

	Integer countByStudyReportQueryHomeWork(StudyReportQuery params) throws DataAccessException;

	List<UserHomeWorkAnswer> findListByStudyReportQueryHomeWork(StudyReportQuery params) throws DataAccessException;

    Integer countHomeWork(UserHomeWorkAnswer params) throws DataAccessException;

    List<HomeworkCompletion> getHomeworkCompletionList(StudyCompletionInfoQuery params) throws DataAccessException;

    List<UserAnswerCount> findUserHomeWorkInfoCountBatch(Map<String, Object> param) throws DataAccessException;
	List<UserHomeWorkAnswer> findUserHomeWorkInfoNotPage(HashMap<String, Object> queryParam) throws DataAccessException;

	List<UserHomeWorkAnswer> findAnswerHomeworkListGroupByLessonId(Long uid,List<Long> lessonIdList, Long productId, Long goodsId) throws DataAccessException;

	Long getUserUseTime(Long uid) throws DataAccessException;;

	int updateHomeworkIdByUid(Map<String, Object> param) throws DataAccessException;

	void deleteByParam(UserHomeWorkAnswer userHomeWorkAnswer) throws DataAccessException;

	List<UserHomeworkAnswerDTO> findUserHomeWorkAnswerInfosGroupByHomeworkId(UserHomeworkAnswerQuery query) throws DataAccessException;

    List<UserHomeWorkAnswer> findLastReadOveredSubjectiveHomeworkAnswers(Long uid, List<Long> homeworkIds, Long goodsId, Long productId, List<Integer> stateList) throws DataAccessException;
}