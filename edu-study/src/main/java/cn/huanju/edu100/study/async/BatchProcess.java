package cn.huanju.edu100.study.async;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.LinkedList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 
 * 
 */
public final class BatchProcess<T> {
	private static final Logger LOG = LoggerFactory
			.getLogger("HeartBeatLogger");
	Logger logger = LoggerFactory.getLogger(this.getClass());

	private ExecutorService executorService;
	private SyncThreadFactory<T> syncFactory;
	private int queueLength = 200000;// 内存缓存队列最大长度
	private int takeBatchNum = 1000; // 批量读取最大数量
	private long waitTimeBatch = 500;// 最大等待时间，若到了500毫秒，不管多少条数据都刷走
	private LinkedBlockingQueue<T> queue = null;
	// private CachedQueue<T> cachedQueue = new CachedQueue<T>();
	private int cpuNums = Runtime.getRuntime().availableProcessors();
	private int threadNum = 1 * cpuNums;

	public BatchProcess(SyncThreadFactory<T> syncFactory) {
		queue = new LinkedBlockingQueue<T>(queueLength);
		this.syncFactory = syncFactory;
		init();
	}

	private void init() {
		executorService = Executors.newFixedThreadPool(threadNum);
		process();
		LOG.info("AbstractBatchProcess init complete.");
	}

	public void destroy() {
		executorService.shutdown();
	}

	/**
	 * 存储，50毫秒超时
	 * 
	 * @param t
	 * @return
	 */
	public boolean offer(T t) {
		try {
			return queue.offer(t, 50, TimeUnit.MILLISECONDS);
		} catch (InterruptedException e) {
			return false;
		}
	}

	/**
	 * 批量读取,每次最多读取50
	 * 
	 * @return
	 */
	Collection<T> takeBatch() {
		try {
			Collection<T> resp = new LinkedList<T>();
			int getNum = 0;
			T tmp = null;
			long start = System.currentTimeMillis();
			while (true) {
				tmp = queue.poll(10, TimeUnit.MILLISECONDS);
				if (tmp != null) {
					resp.add(tmp);
					getNum++;
				}
				if (getNum >= takeBatchNum
						|| (System.currentTimeMillis() - start >= waitTimeBatch)) {
					break;
				}
			}

			return resp;
		} catch (InterruptedException e) {
			return null;
		} catch (Exception e) {
			LOG.error("takeBatch error", e);
			return null;
		}
	}

	// public CachedQueue<T> getCachedQueue() {
	// return cachedQueue;
	// }

	private void process() {
		new Thread(new Runnable() {
			@Override
			public void run() {
				int count = 0;
				while (true) {
					// 取数据的策略，1是队列中数量够了，2是时间到了
					final Collection<T> list = takeBatch();
					boolean hadData = (list != null && list.size() > 0);
					if (hadData) {
						// 刷新到持久化介质
						try {
							executorService.submit(syncFactory
									.newSyncThread(list));
						} catch (Exception ex) {
							logger.error("flush fail.", ex);
						}
						// 休眠策略
						// 若当前队列有数据，则跑勤快点，但是超过池大小时，需要休眠一下
						count++;
						if (count >= threadNum) {
							// 连续取threadNum个数的次数，然后再休眠3秒
							count = 0;
							try {
								TimeUnit.SECONDS.sleep(3);
							} catch (InterruptedException e) {
							}
						}
					} else {
						// 没有取到数据时，休眠
						try {
							TimeUnit.SECONDS.sleep(1);
						} catch (InterruptedException e) {
						}
						count = 0;
					}
				}
			}
		}).start();
	}
}
