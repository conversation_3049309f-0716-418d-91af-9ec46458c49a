package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.dao.UserAnswerSumDao;
import cn.huanju.edu100.study.mapper.homework.trainbrush.UserTrainBrushQuestionResultMapper;
import cn.huanju.edu100.study.model.PaperUserAnswerDetail;
import cn.huanju.edu100.study.model.UserAnswerSum;
import cn.huanju.edu100.study.model.homework.trainbrush.UserTrainBrushQuestionResult;
import cn.huanju.edu100.study.model.questionBox.UserLogSyncRecord;
import cn.huanju.edu100.study.service.UserAnswerSumService;
import cn.huanju.edu100.study.service.UserBrushQuestionInfoService;
import cn.huanju.edu100.study.service.homework.trainbrush.UserTrainBrushQuestionResultService;
import cn.huanju.edu100.util.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqwx.study.entity.UserAnswerDetail;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 获取answerId中detail里面的答题状态为right的questionId 并写入user_train_brush_question_result表当中
 * Created by zhanghong on 2023/5/8.
 */
@Service
public class SyncTrainBrushQuestionResultJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SyncTrainBrushQuestionResultJobHandler.class);

    private static final Integer BATCH = 100;

    private static final String TRAIN_BRUSH_LAST_ID = "train_brush_last_id";

    @Autowired
    private UserAnswerSumService userAnswerSumService;

    @Autowired
    private UserTrainBrushQuestionResultService userTrainBrushQuestionResultService;

    @Autowired
    private UserTrainBrushQuestionResultMapper userTrainBrushQuestionResultMapper;

    @Autowired
    private JedisCluster lockJedisCluster;

    @XxlJob("SyncTrainBrushQuestionResultJobHandler")
    public ReturnT<String> execute(String param) throws Exception{
        logger.info("------SyncTrainBrushQuestionResultJobHandler start------");

        //遍历user_train_brush_question_result表
        // 根据uid和user_answer_id 获取作答正确题目数
        // 并回写入user_train_brush_question_result中的rightQuestionIds字段
        while(true){
            Long lastId = 0L;
            String value = lockJedisCluster.get(TRAIN_BRUSH_LAST_ID);
            logger.info("train_brush_last_id is {}", value);
            if (StringUtils.isNotBlank(value)) {
                lastId = Long.parseLong(value);
            }

            LambdaQueryWrapper<UserTrainBrushQuestionResult> questionResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
            questionResultLambdaQueryWrapper.gt(UserTrainBrushQuestionResult::getId, lastId);
            questionResultLambdaQueryWrapper.last(" limit " + BATCH);

            List<UserTrainBrushQuestionResult> list = userTrainBrushQuestionResultMapper.selectList(questionResultLambdaQueryWrapper);
            if(CollectionUtils.isEmpty(list)){
                logger.info("SyncTrainBrushQuestionResultJobHandler finished .... break");
                break;
            }
            Long maxId = list.stream().mapToLong(UserTrainBrushQuestionResult::getId).max().getAsLong();
            lockJedisCluster.set(TRAIN_BRUSH_LAST_ID, String.valueOf(maxId));

            list.stream().forEach(new Consumer<UserTrainBrushQuestionResult>() {
                @Override
                public void accept(UserTrainBrushQuestionResult userTrainBrushQuestionResult) {
                    Long uid = userTrainBrushQuestionResult.getUid();
                    Long userHomeworkAnswerId = userTrainBrushQuestionResult.getUserAnswerHomeworkId();
                    if(!IdUtils.isValid(uid) || !IdUtils.isValid(userHomeworkAnswerId)){
                        return;
                    }
                    if(StringUtils.isNotEmpty(userTrainBrushQuestionResult.getRightQuestionIds())){
                        return;
                    }
                    try {
                        List<UserAnswerSum>  userAnswerSumList = userAnswerSumService.findHomeworkSumAndDetail(uid,userHomeworkAnswerId,null);
                        if(CollectionUtils.isEmpty(userAnswerSumList)){
                            return;
                        }
                        userAnswerSumList = userAnswerSumList.stream()
                                .filter(x -> (x.getIsRight()!=null && x.getIsRight() == UserAnswerDetail.IsRight.RIGHT)).collect(Collectors.toList());
                        if(CollectionUtils.isEmpty(userAnswerSumList)){
                            return;
                        }
                        String rightQuestionIds = userAnswerSumList.stream().map(a->String.valueOf(a.getQuestionId())).collect(Collectors.joining(","));

                        UserTrainBrushQuestionResult updateResult = new UserTrainBrushQuestionResult();
                        updateResult.setId(userTrainBrushQuestionResult.getId());
                        updateResult.setRightQuestionIds(rightQuestionIds);
                        updateResult.setUpdateDate(userTrainBrushQuestionResult.getUpdateDate());
                        userTrainBrushQuestionResultMapper.updateById(updateResult);

                    }catch (Exception ex){
                        logger.error("findUserAnswerHomeworkDetailByIdAndUid error:{}",ex);
                    }

                }
            });

        }

        logger.info("------SyncTrainBrushQuestionResultJobHandler end------");
        return ReturnT.SUCCESS;
    }
}
