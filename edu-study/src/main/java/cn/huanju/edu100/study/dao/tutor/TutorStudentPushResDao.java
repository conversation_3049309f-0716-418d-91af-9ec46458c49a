/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentPushRes;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 推送记录DAO接口
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentPushResDao extends CrudDao<TutorStudentPushRes> {

    List<TutorStudentPushRes> findListByParams(Map<String, Object> params) throws DataAccessException;

}
