package cn.huanju.edu100.study.model.questionBox;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 用户题库练习DTO
 * 
 * */
public class UserBoxHomework extends DataEntity<UserBoxHomework>{
	
	private static final long serialVersionUID = 1L;
	
	private Long uid;
	private Long boxId;
	private Long userHomeworkId;
	private Long homeworkId;
	private String homeworkName;
	private Integer num;
	private Integer rightCount;
	private Integer state;
	private Integer randomType;
	private Integer homeworkType;
	private Long homeWorkTypeId;
	private Long bookId;
	
	public UserBoxHomework() {
		super();
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getBoxId() {
		return boxId;
	}

	public void setBoxId(Long boxId) {
		this.boxId = boxId;
	}

	public Long getUserHomeworkId() {
		return userHomeworkId;
	}

	public void setUserHomeworkId(Long userHomeworkId) {
		this.userHomeworkId = userHomeworkId;
	}

	public Long getHomeworkId() {
		return homeworkId;
	}

	public void setHomeworkId(Long homeworkId) {
		this.homeworkId = homeworkId;
	}

	public String getHomeworkName() {
		return homeworkName;
	}

	public void setHomeworkName(String homeworkName) {
		this.homeworkName = homeworkName;
	}

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	public Integer getRightCount() {
		return rightCount;
	}

	public void setRightCount(Integer rightCount) {
		this.rightCount = rightCount;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

    public Integer getRandomType() {
        return randomType;
    }

    public void setRandomType(Integer randomType) {
        this.randomType = randomType;
    }

	public Long getHomeWorkTypeId()
	{
		return homeWorkTypeId;
	}

	public void setHomeWorkTypeId(Long homeWorkTypeId)
	{
		this.homeWorkTypeId = homeWorkTypeId;
	}

	public Integer getHomeworkType()
	{
		return homeworkType;
	}

	public void setHomeworkType(Integer homeworkType)
	{
		this.homeworkType = homeworkType;
	}

	public Long getBookId()
	{
		return bookId;
	}

	public void setBookId(Long bookId)
	{
		this.bookId = bookId;
	}
}
