package cn.huanju.edu100.study.model.lesson;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel("根据商品ID查询课节列表请求报文")
@Data
public class LessonForStatsQuery implements Serializable {


	@ApiModelProperty("新课程表商品SkuId")
	private Long goodsSkuId;

	@ApiModelProperty("环球商品ID")
	private Long hqGoodsId;

	@ApiModelProperty("课节类型")
	private String relationType;

	@ApiModelProperty("资源id")
	private Long relationId;

	@ApiModelProperty("是否新课程表课节")
	private Integer lessonType;

	@ApiModelProperty("资源id列表")
	private List<Long> relationIdList;

	@ApiModelProperty("id")
	private Long id;

	@ApiModelProperty("idList")
	private List<Long> idList;

	@ApiModelProperty("schId")
	private Long schId;

}