package cn.huanju.edu100.study.service.solution;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.PageModel;
import cn.huanju.edu100.study.model.solution.QuestionCollect;
import cn.huanju.edu100.exception.DataAccessException;


public interface QuestionCollectService extends BaseService<QuestionCollect> {
    boolean collectQuestion(Long uid, Long questionId,Integer isAl) throws DataAccessException,BusinessException;
    boolean cancelCollectQuestion(Long uid, Long questionId) throws DataAccessException,BusinessException;
    PageModel getUserCollectQuestionList(Long uid, Long secondCategory, Integer from, Integer rows, Integer isAl,Integer sourceType) throws DataAccessException,BusinessException;

}
