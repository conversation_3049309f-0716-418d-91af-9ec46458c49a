package cn.huanju.edu100.study.model.mock;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 模考考生报名Entity
 * <AUTHOR>
 * @version 2018-04-08
 */
public class MockApply extends DataEntity<MockApply> {

	private Long uid;		// 用户uid
	private String mobile;		// 手机号
	private Date applyTime;		// 报名时间
	private Integer applyStatus;		// 报名状态（0未报名，1已报名）
	private Double score;		// 分数
	private Long mockCategoryId;		// 模考科目表id
	private Long mockExamId;		// 模考活动id
	private Long secondCategory;		// 所属考试id
	private Long categoryId;		// 科目id
//	private Date createDate;		// 创建时间
//	private Date updateDate;		// 更新时间

	private Integer examStatus;		// 考试状态（0未考试，1已考试）
    private Long userAnswerId;     //作答记录id
    private Integer isResit;       //是否补考（0不是补考，1是补考）
    private Long submitRank;     //交卷名次
    private Long useTime;     //作答所用时间 单位分钟

    //一下非本表字段
    private Long vSort;//排名用字段
	private String appId;

	public MockApply() {
		super();
	}

	public MockApply(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}

	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}

	public Long getMockCategoryId() {
		return mockCategoryId;
	}

	public void setMockCategoryId(Long mockCategoryId) {
		this.mockCategoryId = mockCategoryId;
	}

	public Long getMockExamId() {
		return mockExamId;
	}

	public void setMockExamId(Long mockExamId) {
		this.mockExamId = mockExamId;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

//	public Date getCreateDate() {
//		return createDate;
//	}
//
//	public void setCreateDate(Date createDate) {
//		this.createDate = createDate;
//	}
//
//	public Date getUpdateDate() {
//		return updateDate;
//	}
//
//	public void setUpdateDate(Date updateDate) {
//		this.updateDate = updateDate;
//	}

	public Integer getExamStatus() {
		return examStatus;
	}

	public void setExamStatus(Integer examStatus) {
		this.examStatus = examStatus;
	}

    public Long getvSort() {
        return vSort;
    }

    public void setvSort(Long vSort) {
        this.vSort = vSort;
    }

    public Long getUserAnswerId() {
        return userAnswerId;
    }

    public void setUserAnswerId(Long userAnswerId) {
        this.userAnswerId = userAnswerId;
    }

    public Integer getIsResit() {
        return isResit;
    }

    public void setIsResit(Integer isResit) {
        this.isResit = isResit;
    }

    public Long getSubmitRank() {
        return submitRank;
    }

    public void setSubmitRank(Long submitRank) {
        this.submitRank = submitRank;
    }

    public Long getUseTime() {
        return useTime;
    }

    public void setUseTime(Long useTime) {
        this.useTime = useTime;
    }

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}
}
