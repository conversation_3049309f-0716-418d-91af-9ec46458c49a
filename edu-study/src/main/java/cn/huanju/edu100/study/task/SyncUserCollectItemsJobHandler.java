package cn.huanju.edu100.study.task;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.UserCollectItems;
import cn.huanju.edu100.study.model.UserCenterCollectQuestion;
import cn.huanju.edu100.study.model.goods.Product;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserCenterCollectQuestionService;
import cn.huanju.edu100.study.service.UserCollectItemsService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 同步云私塾的收藏题目记录至user_center_collect_question表
 */
@Service
public class SyncUserCollectItemsJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SyncUserCollectItemsJobHandler.class);

    @Autowired
    private UserCollectItemsService userCollectItemsService;
    @Autowired
    private UserCenterCollectQuestionService userCenterCollectQuestionService;
    @Autowired
    private KnowledgeResource knowledgeResource;
    @Autowired
    private GoodsResource goodsResource;
    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    @XxlJob("SyncUserCollectItemsJobHandler")
    public ReturnT<String> execute(String paramStr) throws Exception {
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("SyncUserCollectItemsJobHandler 分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);
        if (zoneIndex == 0) {
            logger.info("------SyncUserCollectItemsJobHandler start------");
            int pageSize = getPageSize(paramStr);
            String startIdKey = "UserCollectItemsStartId";
            String startIdStr = compatableRedisClusterClient.get(startIdKey);
            if (StringUtils.isBlank(startIdStr)) {
                logger.error("startIdStr is null. key:{}", startIdKey);
                return ReturnT.SUCCESS;
            }
            Map<String, String> startIdMap = null;
            try {
                startIdMap = GsonUtil.getGenericGson().fromJson(startIdStr, Map.class);
            } catch (Exception e) {
                logger.error("转换startIdStr失败，startIdStr：{}", startIdStr);
                return ReturnT.SUCCESS;
            }
            if (startIdMap == null) {
                logger.error("startIdMap is null. ");
                return ReturnT.SUCCESS;
            }
            for (int tbidx = 0; tbidx < 32; tbidx++) {//32个表
                String startIdTmp = startIdMap.get(tbidx + "");
                if (StringUtils.isBlank(startIdTmp)) {
                    continue;
                }
                Long startId = Long.parseLong(startIdTmp);
                if (startId == null) {
                    continue;
                }
                int size = 1;//标识，是否可以继续遍历获取userCollectItems数据
                while (size > 0) {
                    List<UserCollectItems> userCollectItemsList = getUserCollectItems(tbidx, pageSize, startId);
                    if (CollectionUtils.isNotEmpty(userCollectItemsList)) {
                        startId = userCollectItemsList.get(userCollectItemsList.size() - 1).getId();
                        Set<Long> questionIds = Sets.newHashSet();
                        Set<Long> productIds = Sets.newHashSet();
                        for (UserCollectItems userCollectItems : userCollectItemsList) {
                            if (userCollectItems.getItemId() != null && userCollectItems.getItemId().longValue() > 0) {
                                questionIds.add(userCollectItems.getItemId());
                            }
                            if (userCollectItems.getProductId() != null && userCollectItems.getProductId().longValue() > 0) {
                                productIds.add(userCollectItems.getProductId());
                            }
                        }
                        List<Question> questionList = knowledgeResource.getQuestionByIds(Lists.newArrayList(questionIds));
                        Map<Long, Question> questionMap = Maps.newHashMap();
                        if (CollectionUtils.isNotEmpty(questionList)) {
                            questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Question -> Question, (key1, key2) -> key2));
                        }
                        Map<Long, Product> productIdMap = goodsResource.getProductsByIdList(Lists.newArrayList(productIds));

                        List<UserCenterCollectQuestion> userCenterCollectQuestionList = getUserCenterCollectQuestions(userCollectItemsList, questionMap, productIdMap);

                        if (CollectionUtils.isNotEmpty(userCenterCollectQuestionList)) {
                            for (UserCenterCollectQuestion userCenterCollectQuestion : userCenterCollectQuestionList) {
                                try {
                                    List<Long> existQuestionIds = userCenterCollectQuestionService.findUserCenterCollectIdByQuestionIds(userCenterCollectQuestion.getUid(), userCenterCollectQuestion.getGoodsId(),
                                            Lists.newArrayList(userCenterCollectQuestion.getQuestionId()));
                                    if (CollectionUtils.isEmpty(existQuestionIds)) {
                                        logger.info("insertUserCenterCollectQuestion entity:{}", GsonUtil.getGenericGson().toJson(userCenterCollectQuestion));
                                        userCenterCollectQuestionService.insertObj(userCenterCollectQuestion);
                                    }
                                } catch (Exception ex) {
                                    logger.info("insertUserCenterCollectQuestion {} error", userCenterCollectQuestion, ex);
                                }
                            }
                        }
                        startIdMap.put(tbidx + "", startId.toString());
                        compatableRedisClusterClient.set(startIdKey, GsonUtil.getGenericGson().toJson(startIdMap));
                    } else {
                        size = 0;
                    }

                    Thread.sleep(1000);
                }

            }
            logger.info("------SyncUserCollectItemsJobHandler end------");
        }
        return ReturnT.SUCCESS;
    }

    @NotNull
    private List<UserCenterCollectQuestion> getUserCenterCollectQuestions(List<UserCollectItems> userCollectItemsList, Map<Long, Question> questionMap, Map<Long, Product> productIdMap) {
        List<UserCenterCollectQuestion> userCenterCollectQuestionList = Lists.newArrayList();
        for (UserCollectItems userCollectItems : userCollectItemsList) {
            try {
                Long goodsId = userCollectItems.getGoodsId();
                if (goodsId == null || goodsId.longValue() <= 0) {
                    continue;
                }
                UserCenterCollectQuestion userCenterCollectQuestion = new UserCenterCollectQuestion();
                if (userCollectItems.getProductId() != null && userCollectItems.getProductId().longValue() > 0) {
                    Product product = productIdMap.get(userCollectItems.getProductId());
                    if (product == null) {
                        continue;
                    }
                    if (product.getType() != null && product.getType() == Consts.ProductType.PRODUCT_ADAPTIVE_LEARNING) {
                        userCenterCollectQuestion.setProductType(Consts.ProductType.PRODUCT_ADAPTIVE_LEARNING);
                    } else {
                        continue;
                    }
                }
                userCenterCollectQuestion.setUid(userCollectItems.getUid());
                userCenterCollectQuestion.setProductId(userCollectItems.getProductId());
                userCenterCollectQuestion.setGoodsId(userCollectItems.getGoodsId());
                userCenterCollectQuestion.setQuestionId(userCollectItems.getItemId());
                userCenterCollectQuestion.setCreateDate(userCollectItems.getCreateDate());
                Integer productType = userCenterCollectQuestion.getProductType();
                if (productType == null) {
                    userCenterCollectQuestion.setProductType(Consts.ProductType.PRODUCT_ADAPTIVE_LEARNING);
                }
                Question question = questionMap.get(userCollectItems.getItemId());
                if (question != null) {
                    userCenterCollectQuestion.setCategoryId(question.getCategoryId());
                    userCenterCollectQuestion.setQtype(question.getQtype());
                }
                userCenterCollectQuestionList.add(userCenterCollectQuestion);
            } catch (Exception e) {
                logger.error("生成userCenterCollectQuestion失败！{}", GsonUtil.getGenericGson().toJson(userCollectItems));
                logger.error("Exception: ", e);
            }
        }
        return userCenterCollectQuestionList;
    }

    private List<UserCollectItems> getUserCollectItems(int tbidx, int pageSize, Long startId) {
        Map<String, Object> params = new HashMap<>();
        params.put("tbidx", tbidx);
        params.put("startId", startId);
        params.put("sourceType", Consts.User_Collect_Source_Type.AL);
        params.put("from", 0);
        params.put("pageSize", pageSize);
        params.put("orderBy", "id asc");
        List<UserCollectItems> userCollectItemss = Lists.newArrayList();
        try {
            logger.info("查询userCollectItems，startId；{}", startId);
            userCollectItemss = userCollectItemsService.getListInTbIdx(params);
        } catch (DataAccessException e) {
            e.printStackTrace();
        }
        return userCollectItemss;
    }

    private int getPageSize(String paramStr) {
        int pageSize = 1000;
        if (StringUtils.isBlank(paramStr)) {
            paramStr = XxlJobHelper.getJobParam();//格式："{"pageSize":100}"
        }
        if (StringUtils.isNotBlank(paramStr)) {
            try {
                Map<String, Object> param = GsonUtil.getGenericGson().fromJson(paramStr, Map.class);
                Integer pageSizeTmp = MapUtils.getInteger(param, "pageSize", null);
                if (pageSizeTmp != null) {
                    pageSize = pageSizeTmp;
                }
            } catch (Exception e) {
                logger.error("转换paramStr失败，paramStr：{}", paramStr);
            }
        }
        return pageSize;
    }

}
