package cn.huanju.edu100.study.service.calculate;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

public interface TikuLastChapterExerciseService extends BaseService<TikuLastChapterExercise> {

    TikuLastChapterExercise getByUidBoxId(Long uid, Long boxId) throws DataAccessException;
    TikuLastChapterExercise getByUidBoxIds(Long uid, List<Long> boxIds) throws DataAccessException;

}
