/**
 *
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.BulletinRule;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 公告规则DAO接口
 *
 * <AUTHOR>
 * @version 2016-05-23
 */
public interface BulletinRuleDao extends CrudDao<BulletinRule> {

    List<BulletinRule> findListByParam(List<Long> terminalRuleIdList, BulletinRule bulletinRule) throws DataAccessException;

    List<BulletinRule> getBulletinRules(Map<String, Object> params) throws DataAccessException;
}
