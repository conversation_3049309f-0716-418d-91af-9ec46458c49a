package cn.huanju.edu100.study.dao.ibatis.impl.solution;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.solution.QuestionComplaintDao;
import cn.huanju.edu100.study.model.solution.QuestionComplaint;
import cn.huanju.edu100.study.util.ValidateUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;


public class QuestionComplaintIbatisImpl extends CrudIbatisImpl2<QuestionComplaint> implements QuestionComplaintDao {
    public QuestionComplaintIbatisImpl() {super("QuestionComplaint");}

    @Override
    public QuestionComplaint findQuestionComplaintInfoByQuestionId(Long questionId) throws DataAccessException {
        if (ValidateUtils.isEmpty(questionId)) {
            logger.error("findQuestionComplaintInfoByQuestionId {} error, questionId is null", namespace);
            throw new DataAccessException("findQuestionComplaintInfoByQuestionId {} error,questionId is null", namespace);
        }
        try {
            SqlMapClient sqlMap = super.getSlave();

            return (QuestionComplaint) sqlMap.queryForObject(namespace + ".findQuestionComplaintInfoByQuestionId", questionId);
        } catch (SQLException e) {
            logger.error("findQuestionComplaintInfoByQuestionId {} error, questionId is " + questionId, namespace,questionId,e);
            throw new DataAccessException("findQuestionComplaintInfoByQuestionId SQLException error" + e.getMessage());
        }
    }
}
