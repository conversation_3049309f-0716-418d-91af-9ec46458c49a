/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorStudentFeedbackDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentFeedback;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 老师回访记录DAO接口
 * <AUTHOR>
 * @version 2016-01-19
 */
public class TutorStudentFeedbackIbatisImpl extends CrudIbatisImpl2<TutorStudentFeedback> implements
		TutorStudentFeedbackDao {

	public TutorStudentFeedbackIbatisImpl() {
		super("TutorStudentFeedback");
	}

    @Override
    public List<TutorStudentFeedback> listFeedBackByUid(Map<String, Object> paramsMap)
            throws DataAccessException {
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorStudentFeedback>) sqlMap
                    .queryForList("TutorStudentFeedback.findList", paramsMap);
        } catch (SQLException e) {
            logger.error("listFeedBackByUid SQLException.", e);
            throw new DataAccessException("listFeedBackByUid SQLException error");
        }
    }

    @Override
    public Integer getFeedBackCount(Map<String, Object> params) throws DataAccessException {
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (Integer) sqlMap
                    .queryForObject("TutorStudentFeedback.findListCount", params);
        } catch (SQLException e) {
            logger.error("getFeedBackCount SQLException.", e);
            throw new DataAccessException("getFeedBackCount SQLException error");
        }
    }

}
