package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.service.evaluation.EvaluationUserAnswerService;
import cn.huanju.edu100.study.service.evaluation.EvaluationUserBaseAnswerDetailService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 同步入学测评的相关信息
 */
@Service
public class SyncEvaluationDataJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SyncEvaluationDataJobHandler.class);
    @Autowired
    private EvaluationUserAnswerService evaluationUserAnswerService;
    @Autowired
    private EvaluationUserBaseAnswerDetailService evaluationUserBaseAnswerDetailService;

    @XxlJob("SyncEvaluationDataJobHandler")
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("------SyncEvaluationDataJobHandler start------");
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);
        logger.info("------SyncEvaluationDataJobHandler transferEvaluationUserAnswerList start------");
        evaluationUserAnswerService.transferEvaluationUserAnswerList();
        logger.info("------SyncEvaluationDataJobHandler transferEvaluationUserAnswerList end------");
        evaluationUserBaseAnswerDetailService.transferEvaluationUserBaseAnswerDetailList();
        logger.info("------SyncEvaluationDataJobHandler transferEvaluationUserBaseAnswerDetailList end------");
        logger.info("------SyncEvaluationDataJobHandler end------");
        return ReturnT.SUCCESS;
    }

}
