package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 答题总记录表Entity
 * <AUTHOR>
 * @version 2015-05-14
 */
public class QuestionAnswer extends DataEntity<QuestionAnswer> {

	private static final long serialVersionUID = 1L;
	private Long questionId;		// question_id
	private Long total;		// 学生参与数
	private Date updateTime;
	private Date createTime;

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public QuestionAnswer() {
		super();
	}

	public QuestionAnswer(Long id){
		super(id);
	}

	public Long getQuestionId() {
		return questionId;
	}
	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}
	public Long getTotal() {
		return total;
	}
	public void setTotal(Long total) {
		this.total = total;
	}
}
