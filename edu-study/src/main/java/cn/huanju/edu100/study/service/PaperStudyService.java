/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.PaperStudy;
import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.dto.PaperSubmitCompareInfo;

import java.util.List;


/**
 * 作业学习Service
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface PaperStudyService extends BaseService<PaperStudy> {

	public List<PaperStudy> findListByTaskIdList(List<Long> taskIdList) throws DataAccessException;
}
