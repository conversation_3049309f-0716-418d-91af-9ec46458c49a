package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.IpConvert;
import com.hqwx.thrift.client.base.ThriftRequest;
import org.springframework.stereotype.Component;

public class ThriftClientRequestReady {

    public static <T> ThriftRequest<T> creatThriftRequest(T t) {
        ThriftRequest<T> thriftRequest = new ThriftRequest<T>();
        thriftRequest.setAppid(7);
        thriftRequest.setClient_ip(IpConvert.ipToLong(Consts.Code.CLIENT_IP));
        thriftRequest.setMsg(t);
        thriftRequest.setCodetype(1);
        thriftRequest.setPschId(14);
        thriftRequest.setSchId(2);
        return thriftRequest;
    }

    public static <T> ThriftRequest<T> creatThriftRequest(T t, Integer appid, Integer codeType, Long schId, Long pschId) {
        ThriftRequest<T> thriftRequest = new ThriftRequest<T>();
        thriftRequest.setAppid(appid);
        thriftRequest.setClient_ip(IpConvert.ipToLong(Consts.Code.CLIENT_IP));
        thriftRequest.setMsg(t);
        thriftRequest.setCodetype(codeType);
        thriftRequest.setPschId(pschId);
        thriftRequest.setSchId(schId);
        return thriftRequest;
    }

}
