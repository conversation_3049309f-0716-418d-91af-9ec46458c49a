package cn.huanju.edu100.study.model.questionBox;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * <AUTHOR>
 * @description
 * @date 18/10/29
 */
public class UserWipeOutWrongQuestion extends DataEntity<UserWipeOutWrongQuestion> {

    private Long uid;
    private Long questionBoxId;
    private String key;
    private String questionIdList;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getQuestionBoxId() {
        return questionBoxId;
    }

    public void setQuestionBoxId(Long questionBoxId) {
        this.questionBoxId = questionBoxId;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getQuestionIdList() {
        return questionIdList;
    }

    public void setQuestionIdList(String questionIdList) {
        this.questionIdList = questionIdList;
    }
}
