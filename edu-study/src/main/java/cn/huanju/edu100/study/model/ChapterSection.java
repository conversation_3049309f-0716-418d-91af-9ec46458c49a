package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 章节Entity
 * <AUTHOR>
 * @version 2015-05-11
 */
public class ChapterSection extends DataEntity<ChapterSection> {
	
	private static final long serialVersionUID = 1L;
	private Long parentId;		// parent_id
	private String parentIds;		// parent_ids
	private Long bookId;		// 教材ID
	private String name;		// 名称
	private String description;		// 描述
	private Integer sort;		// 排序
	private Integer klevel;		// 难度
	
	public ChapterSection() {
		super();
	}

	public ChapterSection(Long id){
		super(id);
	}
	
	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getParentIds() {
		return parentIds;
	}
	public void setParentIds(String parentIds) {
		this.parentIds = parentIds;
	}
	public Long getBookId() {
		return bookId;
	}
	public void setBookId(Long bookId) {
		this.bookId = bookId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public Integer getKlevel() {
		return klevel;
	}
	public void setKlevel(Integer klevel) {
		this.klevel = klevel;
	}
	
}