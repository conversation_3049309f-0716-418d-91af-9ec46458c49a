/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorStudentKnowledgeCountDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentKnowledgeCount;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 知识点通过率DAO接口
 * <AUTHOR>
 * @version 2016-01-20
 */
public class TutorStudentKnowledgeCountIbatisImpl extends CrudIbatisImpl2<TutorStudentKnowledgeCount> implements
		TutorStudentKnowledgeCountDao {

	public TutorStudentKnowledgeCountIbatisImpl() {
		super("TutorStudentKnowledgeCount");
	}

    @Override
    public List<TutorStudentKnowledgeCount> getTutorStudentKnowledge(Map<String, Object> params)
            throws DataAccessException {
        if (params == null) {
            return null;
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorStudentKnowledgeCount>) sqlMap
                    .queryForList("TutorStudentKnowledgeCount.findList", params);
        } catch (SQLException e) {
            logger.error("getTutorStudentKnowledge SQLException.", e);
            throw new DataAccessException("getTutorStudentKnowledge SQLException error");
        }
    }

}
