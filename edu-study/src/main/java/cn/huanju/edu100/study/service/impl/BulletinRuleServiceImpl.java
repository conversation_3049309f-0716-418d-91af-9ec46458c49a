package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.cache.DataCache;
import cn.huanju.edu100.study.dao.BulletinRuleDao;
import cn.huanju.edu100.study.model.BulletinRule;
import cn.huanju.edu100.study.service.BulletinRuleService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 公告规则Service
 * <AUTHOR>
 * @version 2016-05-23
 */
@Service
public class BulletinRuleServiceImpl extends BaseServiceImpl<BulletinRuleDao, BulletinRule> implements BulletinRuleService {

    @Autowired
    private DataCache dataCache;

    @Override
    public List<BulletinRule> findListByParam(Set<Long> tRuleIdSet, BulletinRule bulletinRule)
            throws DataAccessException {

        if (CollectionUtils.isEmpty(tRuleIdSet) && null == bulletinRule) {
            logger.error("findListByParam fail, all param is null");
            throw new DataAccessException("findListByParam fail, all param is null");
        }

        List<Long> tRuleIdList = null;
        if (CollectionUtils.isNotEmpty(tRuleIdSet)) {
            tRuleIdList = Lists.newArrayList();
            tRuleIdList.addAll(tRuleIdSet);
        }

        List<BulletinRule> bulletinRules = dataCache.getBulletinRuleByTerminalIdAnType(
                tRuleIdList, bulletinRule.getTerminalRuleType());
        return bulletinRules;
/*        if (!CollectionUtils.isEmpty(bulletinRules)) {
            return bulletinRules;
        }
        return dao.findListByParam(tRuleIdList, bulletinRule);*/
    }

}
