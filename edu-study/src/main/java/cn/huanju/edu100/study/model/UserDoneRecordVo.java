package cn.huanju.edu100.study.model;

import com.hqwx.goods.dto.EPaperDTO;
import lombok.Data;

/**
 * 用户做题记录Vo
 * <AUTHOR>
 * @version 2021-09-08
 */
@Data
public class UserDoneRecordVo extends UserDoneRecord {

	private Integer productType;	// 产品类型 0录播 2电子试卷 13直播 15内部资料 24音频课 25第三方产品 27课程表产品
	private String productName;		// 产品名称
	private String lessonName;		// 课节名称
	private EPaperDTO ePaper;		// 电子试卷

	private Long chapterConsolidatePaperId;	//章节练习对应巩固试卷id
	private Long chapterConsolidatePaperAnswerId; //章节练习对应巩固试卷作答id

}