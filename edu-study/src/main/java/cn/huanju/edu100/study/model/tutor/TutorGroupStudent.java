package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 学员跟分组关联Entity
 * <AUTHOR>
 * @version 2016-01-26
 */
public class TutorGroupStudent extends DataEntity<TutorGroupStudent> {
	
	private static final long serialVersionUID = 1L;
	private String classes;		// classes
	private Long uid;		// uid
	private Long groupId;		// group_id
	
	public TutorGroupStudent() {
		super();
	}

	public TutorGroupStudent(Long id){
		super(id);
	}

	public String getClasses() {
		return classes;
	}

	public void setClasses(String classes) {
		this.classes = classes;
	}
	
	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	
}