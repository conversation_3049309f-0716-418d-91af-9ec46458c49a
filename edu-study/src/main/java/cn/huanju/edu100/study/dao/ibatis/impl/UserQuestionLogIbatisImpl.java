/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserQuestionLogDao;
import cn.huanju.edu100.study.model.UserQuestionLog;
import com.google.common.collect.Lists;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.*;

/**
 * 记录学生在学习录播课程过程中的信息DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-18
 */
public class UserQuestionLogIbatisImpl
        extends CrudIbatisImpl2<UserQuestionLog>
		implements UserQuestionLogDao {

	public UserQuestionLogIbatisImpl() {
		super("UserQuestionLog");
	}

	@Override
	public UserQuestionLog query(final Long uid, final Long questionId) throws DataAccessException{
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("uid", uid);
		param.put("questionId", questionId);
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (UserQuestionLog) sqlMap.queryForObject(
					"UserQuestionLog.queryOne", param);
		} catch (SQLException e) {
			logger.error("findOne SQLException.param:{}", param, e);
			throw new DataAccessException("findOne SQLException error");
		}
	}
	@Override
	public Collection<UserQuestionLog> queryUserQuestionLog(Map<String, Object> param)
			throws DataAccessException {

		try {
			SqlMapClient sqlMap = super.getSlave();
			return (Collection<UserQuestionLog>) sqlMap.queryForList(
					"UserQuestionLog.findList", param);
		} catch (SQLException e) {
			logger.error("findList SQLException.uid:{}", param, e);
			throw new DataAccessException("findList SQLException error");
		}
	}
	@Override
	public UserQuestionLog findOne(Map<String, Object> param)
			throws DataAccessException {

		try {
			SqlMapClient sqlMap = super.getSlave();
			return (UserQuestionLog) sqlMap.queryForObject(
					"UserQuestionLog.findOne", param);
		} catch (SQLException e) {
			logger.error("findOne SQLException.param:{}", param, e);
			throw new DataAccessException("findOne SQLException error");
		}
	}
	@Override
	public long insertUserQuestionLog(UserQuestionLog userVideoLog)
			throws DataAccessException {

		try {
			SqlMapClient sqlMap = super.getMaster();
			userVideoLog.setLastTime(new Date());
			if (userVideoLog.getStatus() == null) {
				userVideoLog.setStatus(0);
			}
			Object id = sqlMap.insert("UserQuestionLog.insert", userVideoLog);
			if (id != null && id instanceof Long) {
				return (Long) id;
			} else {
				return 0l;
			}
		} catch (SQLException e) {
			logger.error("insert SQLException.uid:{}", userVideoLog, e);
			throw new DataAccessException("insert SQLException error");
		}
	}

	@Override
	public int updateUserQuestionLog(UserQuestionLog userVideoLog)
			throws DataAccessException {
		if(null == userVideoLog.getUid()){
			logger.error("updateLastRec SQLException.uid null", userVideoLog);
			return -1;
		}
		try {
			SqlMapClient sqlMap = super.getShardingMaster();
			userVideoLog.setLastTime(new Date());
			if (userVideoLog.getStatus() == null) {
				userVideoLog.setStatus(0);
			}
//			SqlMapClient mainSqlMap = super.getMaster();
//			mainSqlMap.update("UserQuestionLog.updateLastRec", userVideoLog);

			return sqlMap.update("UserQuestionLog.update", userVideoLog);
		} catch (SQLException e) {
			logger.error("update SQLException.uid:{}", userVideoLog, e);
			throw new DataAccessException("update SQLException error");
		}
	}

	@Override
	public Collection<UserQuestionLog> queryByUidQuestionId(Long uid, Long questionId)
			throws DataAccessException {
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("questionId", questionId);
			return (Collection<UserQuestionLog>) sqlMap.queryForList(
					"UserQuestionLog.queryByUidCourseId", param);
		} catch (SQLException e) {
			logger.error("queryByUidCourseId SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"queryByUidCourseId SQLException error");
		}
	}

	@Override
	public List<UserQuestionLog> getLastUserQuestionLog(long uid, List<Long> questionIds)
			throws DataAccessException {
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();

			param.put("uid", uid);
			param.put("questionIds", questionIds);

			return (List<UserQuestionLog>) sqlMap.queryForList(
					"UserQuestionLog.queryLastByUidQuestionId", param);

		} catch (SQLException e) {
			logger.error("getLastUserVideoLog SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"getLastUserVideoLog SQLException:"+e.getMessage());
		}
	}
	@Override
	public List<UserQuestionLog> split_getLastUserQuestionLog(long uid, List<Long> questionIds)
			throws DataAccessException {
		try {
			//TODO 和getLastUserQuestionLog比较，如果耗时明显更长，换成直接调用getLastUserQuestionLog
			long begTime = System.currentTimeMillis();
			List<UserQuestionLog> result = Lists.newArrayList();
			List<List<Long>> part = Lists.partition(questionIds, 20);
			for (List<Long> partIds : part) {
				List<UserQuestionLog> list = this.getLastUserQuestionLog(uid, partIds);
				if(CollectionUtils.isNotEmpty(list)){
					result.addAll(list);
				}
			}

			logger.info("split_getLastUserQuestionLog questionIds.size:{} part.size:{} cost:{}ms", questionIds.size(), part.size(), (System.currentTimeMillis() - begTime));
			return result;
		} catch (DataAccessException e) {
			logger.error("split_getLastUserQuestionLog Exception.uid:{}", uid, e);
			throw new DataAccessException(
					"split_getLastUserQuestionLog Exception:"+e.getMessage());
		}
	}
	@Override
	public void insertBatch(List<UserQuestionLog> userQuestionLogs) throws DataAccessException {
		if (CollectionUtils.isEmpty(userQuestionLogs)) {
			logger.error("batchInsert get error, parameter is empty");
			throw new DataAccessException("insertBatch get error, parameter is empty");
		}
		try {
			SqlMapClient sqlMap = super.getMaster();
			sqlMap.insert(namespace.concat(".insertBatch"), userQuestionLogs);
		} catch (SQLException e) {
			logger.error("batchInsert SQLException.", e);
			throw new DataAccessException("insertBatch get SQLException error" + e.getMessage());
		} catch (Exception e) {
			logger.error("get SException.", e);
			throw new DataAccessException("insertBatch get Exception error" + e.getMessage());
		}
	}

}
