package cn.huanju.edu100.study.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.*;
import java.util.Enumeration;
import java.util.regex.Pattern;

public class NetworkUtil {
    private static final Logger log = LoggerFactory.getLogger(NetworkUtil.class);
    private static final String LOCALHOST_IP = "127.0.0.1";
    private static final String EMPTY_IP = "0.0.0.0";
    private static final Pattern IP_PATTERN = Pattern.compile("[0-9]{1,3}(\\.[0-9]{1,3}){3,}");

    public NetworkUtil() {
    }

    public static int findUnusedLocalPort(String host, int port) {
        if (port < 65535) {
            Socket socket = null;

            label91: {
                int var4;
                try {
                    socket = new Socket(host, port);
                    break label91;
                } catch (ConnectException var16) {
                    var4 = port;
                } catch (IOException var17) {
                    log.debug("Socket: {} io exception", socket, var17);
                    break label91;
                } finally {
                    if (socket != null) {
                        try {
                            socket.close();
                        } catch (IOException var15) {
                            log.warn("Failed to close socket: {}", socket);
                        }
                    }

                }

                return var4;
            }

            ++port;
            return findUnusedLocalPort(host, port);
        } else {
            throw new IllegalArgumentException("Can not found avalible port");
        }
    }

    public static InetSocketAddress getScoketAddress() {
        InetAddress addr = getHostAddress();
        int port = findUnusedLocalPort(addr.getHostAddress(), 56789);
        return new InetSocketAddress(addr, port);
    }

    public static InetAddress getHostAddress() {
        InetAddress localAddress = null;

        try {
            localAddress = InetAddress.getLocalHost();
            if (isValidHostAddress(localAddress)) {
                return localAddress;
            }
        } catch (Throwable var6) {
            log.warn("Failed to retriving local host ip address, try scan network card ip address. cause: " + var6.getMessage());
        }

        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            if (interfaces != null) {
                while(interfaces.hasMoreElements()) {
                    try {
                        NetworkInterface network = (NetworkInterface)interfaces.nextElement();
                        Enumeration<InetAddress> addresses = network.getInetAddresses();
                        if (addresses != null) {
                            while(addresses.hasMoreElements()) {
                                try {
                                    InetAddress address = (InetAddress)addresses.nextElement();
                                    if (isValidHostAddress(address)) {
                                        return address;
                                    }
                                } catch (Throwable var5) {
                                    log.warn("Failed to retriving network card ip address. cause:" + var5.getMessage());
                                }
                            }
                        }
                    } catch (Throwable var7) {
                        log.warn("Failed to retriving network card ip address. cause:" + var7.getMessage());
                    }
                }
            }
        } catch (Throwable var8) {
            log.warn("Failed to retriving network card ip address. cause:" + var8.getMessage());
        }

        log.error("Could not get local host ip address, will use 127.0.0.1 instead.");
        return localAddress;
    }

    private static boolean isValidHostAddress(InetAddress address) {
        if (address != null && !address.isLoopbackAddress()) {
            String name = address.getHostAddress();
            return name != null && !"0.0.0.0".equals(name) && !"127.0.0.1".equals(name) && IP_PATTERN.matcher(name).matches();
        } else {
            return false;
        }
    }
}
