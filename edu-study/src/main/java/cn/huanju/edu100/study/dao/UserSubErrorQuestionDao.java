/**
 *
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.study.model.UserErrorEpaperQuestion;
import cn.huanju.edu100.study.model.UserErrorHomeworkLessonQuestionBean;
import cn.huanju.edu100.study.model.UserQuestionCountVo;
import cn.huanju.edu100.study.model.UserSubErrorQuestion;
import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.dto.query.UserErrorAndCorrectQuestionQuery;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户分表错题记录DAO接口
 *
 * <AUTHOR>
 * @version 2021-09-08
 */
public interface UserSubErrorQuestionDao extends CrudDao<UserSubErrorQuestion> {

    public List<UserErrorHomeworkLessonQuestionBean> findUserErrorHomeWorkLessonIds(Map<String, Object> params) throws DataAccessException;

    public List<UserErrorHomeworkLessonQuestionBean> findUserErrorHomeWorkQuestionIds(Map<String, Object> queryParam) throws DataAccessException;

    public List<UserErrorEpaperQuestion> findUserErrorEpaperQuestionIds(Map<String, Object> queryParam) throws DataAccessException;

    public Long getUserErrorQuestionCountByUidAndCategory(Map<String, Object> queryParam) throws DataAccessException;

    public boolean removeErrorQuestionByCategory(Map<String, Object> queryParam) throws DataAccessException;

    public boolean removeErrorQuestionByCategoryGroupByQuestion(Map<String, Object> queryParam) throws DataAccessException;

    public boolean removeUserSubErrorQuestion(UserSubErrorQuestion userSubErrorQuestion) throws DataAccessException;

    boolean removeUserSubErrorQuestionSoft(UserSubErrorQuestion userSubErrorQuestion) throws DataAccessException;

    public List<UserQuestionCountVo> getUserErrorQuestionCountByQtype(Map<String, Object> queryParam) throws DataAccessException;

    public List<UserErrorEpaperQuestion> getUserErrorQuestionListGroupByQtype(Map<String, Object> queryParam) throws DataAccessException;

    public List<Long> findUserErrorQuestionIdsByCategoryAndGoodsId(Map<String,Object> queryParam) throws DataAccessException;

    List<Long> findAnswerIdByCategoryAndGoodsId(Map<String,Object> queryParam) throws DataAccessException;

    List<UserSubErrorQuestion> getUserSubErrorQuestionList(Long uid, Long goodsId, Long categoryId, Date startTime, Date endTime, Integer noNeedLimit) throws DataAccessException;

    Integer countUserSubErrorQuestionList(Long uid, Long goodsId, Long categoryId, Date startTime, Date endTime) throws DataAccessException;

    Boolean insertBatch(List<UserSubErrorQuestion> userSubErrorQuestionList) throws DataAccessException;

    Integer getUserErrorQuestionCount(UserErrorAndCorrectQuestionQuery query) throws DataAccessException;

    List<UserSubErrorQuestion> findGroupByList(Page<UserSubErrorQuestion> page, UserErrorAndCorrectQuestionQuery query)throws DataAccessException;

    List<UserSubErrorQuestion> getUserQuestionErrorTimes(Long uid, Long goodsId, Long categoryId, String startTime, String endTime, Integer sourceType, List<Long> questionIdList)throws DataAccessException;

    long groupByCount(UserErrorAndCorrectQuestionQuery query)throws DataAccessException;
}
