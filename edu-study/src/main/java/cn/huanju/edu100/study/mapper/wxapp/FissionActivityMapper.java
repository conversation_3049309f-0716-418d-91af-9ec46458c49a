package cn.huanju.edu100.study.mapper.wxapp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hqwx.study.entity.wxapp.FissionActivity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19 18:16
 * @description
 */
@DS("study-item-data-slave-ds")
@Mapper
public interface FissionActivityMapper extends BaseMapper<FissionActivity> {
    static final String GROUP_BUY_SELECT_VIEW = """
            SELECT
                CAST(id AS UNSIGNED) AS id, activity_name AS name, null as app_id,
                1 AS activity_type, activity_type AS sub_activity_type,
                CAST(category_id AS CHAR) AS second_category_ids, CAST(status AS SIGNED) AS status,
                sort AS weight, FROM_UNIXTIME(activity_end_time) AS activity_end_time,
                group_members AS member_count, update_by AS update_uid,
                FROM_UNIXTIME(create_time) AS create_time, FROM_UNIXTIME(update_time) AS update_time
            FROM solution_wx_documents_activity
            """;
    static final String BARGAIN_SELECT_VIEW = """
            SELECT
                CAST(id AS UNSIGNED) AS id, name, null as app_id,
                2 AS activity_type, type AS sub_activity_type,
                second_category_ids, CAST(status AS SIGNED) AS status,
                sort_num AS weight, FROM_UNIXTIME(end_time) AS activity_end_time,
                cut_num AS member_count, update_by AS update_uid,
                FROM_UNIXTIME(create_date) AS create_time, FROM_UNIXTIME(update_by_date) AS update_time
            FROM solution_bargain_activity
            """;
    static final String HELP_SELECT_VIEW = """
            SELECT
                CAST(id AS UNSIGNED) AS id, good_name AS name, appid as app_id,
                3 AS activity_type, type AS sub_activity_type,
                CAST(second_category AS CHAR) AS second_category_ids, (CAST(status AS SIGNED) + 1) AS status,
                order_value AS weight, FROM_UNIXTIME(expire_time) AS activity_end_time,
                finish_num AS member_count, update_by AS update_uid,
                FROM_UNIXTIME(UNIX_TIMESTAMP(create_date)) AS create_time, FROM_UNIXTIME(UNIX_TIMESTAMP(update_date)) AS update_time
            FROM solution_activity_zuli_good
            """;

    static final String FISSION_ACTIVITY_VIEW = "( "
            + GROUP_BUY_SELECT_VIEW + " UNION ALL " + BARGAIN_SELECT_VIEW + " UNION ALL " + HELP_SELECT_VIEW
            + " ) AS fission_activity";

    /**
     * 分页查询裂变活动，把三种活动（拼团、砍价、助力）的数据合并查询
     * @param page 分页参数
     * @param wrapper 查询条件
     * @return 分页数据
     */
    @Select("SELECT * FROM " + FISSION_ACTIVITY_VIEW +  " ${ew.customSqlSegment}")
    IPage<FissionActivity> selectActivityPage(IPage<FissionActivity> page,
                                              @Param(Constants.WRAPPER) Wrapper<FissionActivity> wrapper);

    @Select(GROUP_BUY_SELECT_VIEW +  " ${ew.customSqlSegment} ORDER BY weight DESC, update_time DESC")
    List<FissionActivity> selectGroupBuyActivityList(@Param(Constants.WRAPPER) Wrapper<FissionActivity> wrapper);
    /**
     * 查询有指定设置的活动列表
     * @param settingType 活动设置类型
     * @param wrapper 查询条件
     * @return 活动列表
     */
    @Select("SELECT fission_activity.* FROM " + FISSION_ACTIVITY_VIEW +
            """
            , solution_fission_activity_setting setting
            WHERE fission_activity.id = setting.activity_id
            AND fission_activity.activity_type = setting.activity_type
            AND setting.type = #{settingType}
            AND ${ew.sqlSegment}
            ORDER BY fission_activity.weight DESC, fission_activity.update_time DESC
            """)
    List<FissionActivity> selectSettingActivityList(@Param(Constants.WRAPPER) Wrapper<FissionActivity> wrapper,
                                                    @Param("settingType") Integer settingType);


    /**
     * 获取拼团活动参与人数
     * @param activityId 活动id
     * @return 参与人数
     */
    @Select("SELECT COUNT(DISTINCT uid) FROM solution_wx_documents_activity_create_group_members WHERE aid = #{activityId}")
    Integer getGroupBuyActivityJoinCount(Long activityId);
    /**
     * 获取砍价活动参与人数
     * @param activityId 活动id
     * @return 参与人数
     */
    @Select("SELECT SUM(cuted_num) FROM solution_bargain_activity_opener WHERE bargain_activity_id = #{activityId}")
    Integer getBargainActivityJoinCount(Long activityId);
    /**
     * 获取助力活动参与人数
     * @param activityId 活动id
     * @return 参与人数
     */
    @Select("SELECT SUM(num + 1) FROM solution_activity_zuli_user_good WHERE zg_id = #{activityId}")
    Integer getHelpActivityJoinCount(Long activityId);

}
