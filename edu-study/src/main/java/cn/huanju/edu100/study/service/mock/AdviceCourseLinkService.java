package cn.huanju.edu100.study.service.mock;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.mock.AdviceCourseLink;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 试卷Service
 *
 * <AUTHOR>
 * @version 2015-05-08
 */
public interface AdviceCourseLinkService extends BaseService<AdviceCourseLink> {

    List<AdviceCourseLink> getAdviceCourseLinkList(Long adviceId) throws DataAccessException;
}