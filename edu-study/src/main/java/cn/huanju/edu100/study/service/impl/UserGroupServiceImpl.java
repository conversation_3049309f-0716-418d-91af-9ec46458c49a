package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.UserGroupDao;
import cn.huanju.edu100.study.model.GroupTeacher;
import cn.huanju.edu100.study.model.UserGroup;
import cn.huanju.edu100.study.service.UserGroupService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.concurrent.TimeUnit;

/**
 * 用户分组Service
 *
 * <AUTHOR>
 * @version 2015-05-12
 */
@Service
public class UserGroupServiceImpl extends BaseServiceImpl<UserGroupDao, UserGroup> implements UserGroupService {

    private static Logger logger = LoggerFactory.getLogger(UserGroupServiceImpl.class);

    @Override
    public Collection<UserGroup> getGroupsByUid(long uid) throws DataAccessException {
        Collection<UserGroup> groupList = dao.qryGroupByUid(uid);
        for (UserGroup userGroup : groupList) {
            Collection<GroupTeacher> gteachers = getGroupTeachers(userGroup.getId());
            for (GroupTeacher teacher : gteachers) {
                if (teacher.getType() == 0) {
                    userGroup.setClassTeacher(String.valueOf(teacher.getTuid()));
                } else if (teacher.getType() == 1) {
                    userGroup.setInstructor(String.valueOf(teacher.getTuid()));
                }
            }
        }
        return groupList;
    }

    @Override
    public UserGroup getGroupById(Long id) throws DataAccessException {
        UserGroup userGroup = super.get(id);
        if (null == userGroup) {
            return null;
        }
        Collection<GroupTeacher> gteachers = getGroupTeachers(userGroup.getId());
        for (GroupTeacher teacher : gteachers) {
            if (teacher.getType() == 0) {
                userGroup.setClassTeacher(String.valueOf(teacher.getTuid()));
            } else if (teacher.getType() == 1) {
                userGroup.setInstructor(String.valueOf(teacher.getTuid()));
            }
        }
        return userGroup;
    }

    private LoadingCache<Long, Collection<GroupTeacher>> groupTeachersCache = CacheBuilder.newBuilder()
            .maximumSize(1000).expireAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Collection<GroupTeacher>>() {
                @Override
                public Collection<GroupTeacher> load(Long groupId) throws Exception {
                    return dao.qryGroupTeacherByGid(groupId);
                }
            });

    @Override
    public Collection<GroupTeacher> getGroupTeachers(Long groupId) throws DataAccessException {
        if (groupId == null || groupId <= 0) {
            logger.error("getGroupTeachers error, parameter id:{}", groupId);
            throw new DataAccessException("getGroupTeachers parameter id is null.");
        }
        Collection<GroupTeacher> result = null;
        try {
            result = groupTeachersCache.getUnchecked(groupId);
            if (result != null && !result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // ignore exception
        }
        return result;
    }
}
