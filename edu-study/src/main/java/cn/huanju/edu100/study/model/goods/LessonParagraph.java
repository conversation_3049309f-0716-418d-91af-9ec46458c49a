package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Collection;

/**
 * 课件段落Entity
 * 
 * <AUTHOR>
 * @version 2015-07-29
 */
public class LessonParagraph extends DataEntity<LessonParagraph> {

	private static final long serialVersionUID = 1L;
	private Long point; // point
	private String name; // name
	private Long lessonId; // lesson_id
	private String draft; // draft

	private Collection<LessonParagraphKnowledge> knowledges;
	private Collection<LessonParagraphQuestion> questions;

	public LessonParagraph() {
		super();
	}

	public LessonParagraph(Long id) {
		super(id);
	}

	public Long getPoint() {
		return point;
	}

	public void setPoint(Long point) {
		this.point = point;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}

	public String getDraft() {
		return draft;
	}

	public void setDraft(String draft) {
		this.draft = draft;
	}

	public Collection<LessonParagraphKnowledge> getKnowledges() {
		return knowledges;
	}

	public void setKnowledges(Collection<LessonParagraphKnowledge> knowledges) {
		this.knowledges = knowledges;
	}

	public Collection<LessonParagraphQuestion> getQuestions() {
		return questions;
	}

	public void setQuestions(Collection<LessonParagraphQuestion> questions) {
		this.questions = questions;
	}

}