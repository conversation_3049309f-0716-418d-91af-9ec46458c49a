package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.study.model.CTaskResult;
import cn.huanju.edu100.study.resource.StustampResource;
import cn.huanju.edu100.study.service.CTaskResultService;
import cn.huanju.edu100.util.GsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by lynn on 2017/5/4.
 */
@Service
public class CTaskResultServiceImpl implements CTaskResultService {
    private static final Logger LOG = LoggerFactory.getLogger(CTaskResultServiceImpl.class);
    @Autowired
    private StustampResource stustampResource;
    @Override
    public void saveCTaskResult(CTaskResult cTaskResult) {
        if(null == cTaskResult){
            return;
        }
        if(null == cTaskResult.getUid() || null == cTaskResult.getType() || null == cTaskResult.getTaskId()){
            LOG.error("save task result , uid,type, taskid null:{}", GsonUtil.toJson(cTaskResult));
        }
        CTaskResult oldResult = stustampResource.queryByTaskId(cTaskResult.getUid(), cTaskResult.getType(), cTaskResult.getTaskId());
        if(null == oldResult || cTaskResult.bNeedSave(oldResult)) {
            stustampResource.saveCTaskResult(cTaskResult);
        }
    }
}
