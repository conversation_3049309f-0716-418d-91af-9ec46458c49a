package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.exception.SystemExecErrorException;
import cn.huanju.edu100.model.ResponseBean;
import cn.huanju.edu100.study.client.SubjectiveQuestionAiCorrectingService;
import cn.huanju.edu100.study.event.HomeworkSubmittedEvent;
import cn.huanju.edu100.study.event.PaperSubmittedEvent;
import cn.huanju.edu100.study.gpt.GptChatService;
import cn.huanju.edu100.study.gpt.impl.AiAgentChatService;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.homework.comment.UserHomeworkComment;
import cn.huanju.edu100.study.model.paper.UserAnswerCommentPO;
import cn.huanju.edu100.study.model.question.SubjectiveQuestionAICorrectingLogPO;
import cn.huanju.edu100.study.repository.SubjectiveQuestionAiCorrectingRepository;
import cn.huanju.edu100.study.repository.UserAnswerCommentRepository;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.resource.PromptResource;
import cn.huanju.edu100.study.resource.feigncall.EduStudyAssistantFeign;
import cn.huanju.edu100.study.resource.feigncall.ResourceCommonFeign;
import cn.huanju.edu100.study.resource.feigncall.dto.*;
import cn.huanju.edu100.study.service.HomeworkSubmittedEventHandler;
import cn.huanju.edu100.study.service.PaperSubmittedEventHandler;
import cn.huanju.edu100.study.service.UserAnswerService;
import cn.huanju.edu100.study.service.UserAnswerSumService;
import cn.huanju.edu100.study.service.homework.comment.NormalCommentContent;
import cn.huanju.edu100.study.service.homework.comment.UserHomeworkCommentService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.RetryUtil;
import cn.huanju.edu100.util.JSONUtils;
import cn.huanju.edu100.util.upload.OssUtil;
import com.hqwx.goods.client.ProductLessonService;
import com.hqwx.goods.dto.al.StudyPathDTO;
import com.hqwx.goods.dto.al.StudyPathResourceDTO;
import com.hqwx.goods.dto.productlesson.Lesson;
import com.hqwx.goods.dto.productlesson.LessonPaper;
import com.hqwx.goods.dto.productlesson.ProductLessonDTO;
import com.hqwx.goods.entity.GoodsLessonSetting;
import com.hqwx.goods.entity.resource.Homework;
import com.hqwx.goods.enums.ProductTypeEnum;
import com.hqwx.rpc.model.SubjectiveQuestionAICorrectingPromptTemplateDTO;
import com.hqwx.rpc.model.SubjectiveQuestionAICorrectingPromptTemplateQuery;
import com.hqwx.study.dto.SubjectiveQuestionAICorrectingLogDTO;
import com.hqwx.study.dto.UserAnswerComment;
import com.hqwx.study.dto.query.UserHomeworkCommentListQuery;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/7 16:28
 * @description
 */
@Slf4j
@Service
public class SubjectiveQuestionAiCorrectingServiceImpl implements SubjectiveQuestionAiCorrectingService, PaperSubmittedEventHandler, HomeworkSubmittedEventHandler {
    @Resource
    private ResourceCommonFeign resourceCommonFeign;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Resource
    private PromptResource promptResource;

    @Autowired
    private UserAnswerSumService userAnswerSumService;

    @Autowired
    private UserAnswerService userAnswerService;

    @Autowired
    private SubjectiveQuestionAiCorrectingRepository subjectiveQuestionAiCorrectingRepository;

    @Autowired
    GptChatService gptChatService;

    @Autowired
    private AiAgentChatService aiAgentChatService;

    @Autowired
    private ProductLessonService productLessonGrpcService;

    @Autowired
    private EduStudyAssistantFeign eduStudyAssistantFeign;

    @Resource
    private GoodsResource goodsResource;

    @Autowired
    private UserAnswerCommentRepository userAnswerCommentRepository;

    @Autowired
    private UserHomeworkCommentService userHomeworkCommentService;


    private final static String LOG_TAG = "logTag";

    /**
     * 主观题类型集合，包含：
     * 5：简答题
     * 6：论述题
     * 7：计算题
     */
    private static final Set<Integer> subjectiveQTypeSet = Set.of(5,6,7);

    private static final String COMMENT_HEADER = "- 学员您好，您的批阅如下：\n";
    private static final String COMMENT_FOOTER = "\n- 祝您学习愉快！";
    private static final String SUB_QUESTION_FORMAT = "-%s批阅如下:\n%s\n";



    /**
     * 监听试卷提交成功的事件，检查是否有需要AI批阅的题目
     * @param event 试卷提交事件
     */
    @Async
    @EventListener
    @Override
    public void onPaperSubmittedEvent(PaperSubmittedEvent event){
        UserAnswer userAnswer = event.getUserAnswer();
        // 基础参数校验
        if (userAnswer == null || userAnswer.getGoodsId() == null
                || userAnswer.getProductId() == null
                || (userAnswer.getLessonId() == null && userAnswer.getStudyPathId() == null)
                || userAnswer.getPaperId() == null) {
            log.info("onPaperSubmittedEvent no need to ai correct:{}", userAnswer.getId());
            return;
        }
        if (Objects.equals(userAnswer.getEventType(), 1)) { //初次提交事件不触发ai批阅，type=2最终处理完成时再触发
            log.info("{}: no need to ai correct for event type 1", userAnswer.getId());
            return;
        }
        String logTag = getLogTag(userAnswer);
        int isNeedAiCorrecting = isPaperNeedAiCorrecting(userAnswer);
        if (isNeedAiCorrecting == 0) {
            log.info("{}: no need to ai correct", logTag);
            return;
        }
        
        final Set<Integer> subjectiveQTypeSet = Set.of(
                Consts.QType.QType5.getCode(), Consts.QType.QType6.getCode(), Consts.QType.QType7.getCode());
        Map<Long, Set<Long>> subjectiveQuestionTopicMap = new HashMap<>(userAnswer.getAnswerDetail().size());
        List<QuestionGroup> questionGroupList = knowledgeResource.klg_getQuestionByPaperId(userAnswer.getPaperId());
        //存一下主观题
        Map<Long, Question> subjectiveQuestionMap = new HashMap<>(userAnswer.getAnswerDetail().size());
        for(QuestionGroup group : questionGroupList) {
            for(var question : group.getQuestionList()) {
                if(!subjectiveQTypeSet.contains(question.getQtype())) {
                    // 非主观题不处理
                    continue;
                }
                subjectiveQuestionMap.put(question.getId(), question);
                question.getTopicList().forEach(topic -> {
                    if(QuestionTopic.Type.ESSAY.equals(topic.getQtype())) {
                        subjectiveQuestionTopicMap.computeIfAbsent(question.getId(), k -> new HashSet<>()).add(topic.getId());
                    }
                });
            }
        }
        final int totalQuestion = userAnswer.getAnswerDetail().size();
        AtomicInteger currentIndex = new AtomicInteger(0);
        String finalLogTag = logTag;
        userAnswer.getAnswerDetail().forEach(detail -> {
            int index = currentIndex.incrementAndGet();
            if(!subjectiveQuestionTopicMap.containsKey(detail.getQuestionId())
                    || !subjectiveQuestionTopicMap.get(detail.getQuestionId()).contains(detail.getTopicId())) {
                // 非主观题不处理
                log.debug("{}/{} {} not subjective question, questionId={}, topicId={}",
                        index, totalQuestion, finalLogTag, detail.getQuestionId(), detail.getTopicId());
                return;
            }
            log.info("{}/{} {} questionId={}, topicId={} ai correcting...", index, totalQuestion,
                    finalLogTag, detail.getQuestionId(), detail.getTopicId());
            // 触发AI批阅
            // 根据配置走不同的逻辑
            if (isNeedAiCorrecting == 1 || isNeedAiCorrecting == 2) {

                try {
                    boolean isAnswer = isAnswer(detail);
                    if (isAnswer) {
                        // 构建params
                        Map<String, Object> params = new HashMap<>();
                        Question question = subjectiveQuestionMap.get(detail.getQuestionId());
                        if (Objects.isNull(question)) {
                            log.error("{}/{} {} can not find question:{}", index, totalQuestion, finalLogTag, detail.getQuestionId());
                            return;
                        }
                        params.put("questionId", detail.getQuestionId());
                        params.put("topicId", detail.getTopicId());
                        params.put("userAnswerId", userAnswer.getId());
                        params.put("questionSource", SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_PAPER.getValue());
                        params.put("secondCategory", question.getSecondCategory());
                        params.put("category", question.getCategoryId());
                        params.put("correctMethod", isNeedAiCorrecting);

                        subjectiveQuestionAiCorrecting(userAnswer.getUid(), params);
                    } else {
                        log.info("question:{} topic:{} not answer, skip,tag:{} ", detail.getQuestionId(), detail.getTopicId(), finalLogTag);
                    }
                } catch (Exception e) {
                    log.error("{}/{} {} subjectiveQuestionAiCorrecting error: {}", index, totalQuestion, finalLogTag, e.getMessage());
                }
            } else if (isNeedAiCorrecting == 3) {
                try {
                    // 构建params
                    Map<String, Object> params = new HashMap<>();
                    Question question = subjectiveQuestionMap.get(detail.getQuestionId());
                    if(Objects.isNull(question)) {
                        log.error("{}/{} {} can not find question:{}", index, totalQuestion, finalLogTag, detail.getQuestionId());
                        return;
                    }
                    params.put("questionId", detail.getQuestionId());
                    params.put("topicId", detail.getTopicId());
                    params.put("userAnswerId", userAnswer.getId());
                    params.put("questionSource", SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_PAPER.getValue());
                    params.put("secondCategory", question.getSecondCategory());
                    params.put("category", question.getCategoryId());
                    params.put("correctMethod", isNeedAiCorrecting);

                    boolean isContainImage = false;
                    List<String> photoAnswerUrlList = JSONUtils.parseArray(detail.getAnswerStr(), String.class);
                    if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(photoAnswerUrlList)){
                        for (String url : photoAnswerUrlList) {
                            if (StringUtils.containsIgnoreCase(url, "http://")
                                    || StringUtils.containsIgnoreCase(url, "https://")) {
                                isContainImage = true;
                                break;
                            }
                        }
                    }
                    // 构建content
                    List<Map<String, Object>> content = new ArrayList<>();
                    if(isContainImage){
                        for (String url : photoAnswerUrlList) {
                            Map<String, Object> image = new HashMap<>();
                            image.put("type", "image_url");
                            Map<String, Object> imageUrl = new HashMap<>();
                            imageUrl.put("url", url);
                            image.put("imageUrl", imageUrl);
                            content.add(image);
                        }
                        this.photoAnswerAiCorrecting(userAnswer.getUid(), "", params, content);
                    } else {
                        log.error("{}/{} {} photoAnswerUrlList is empty", index, totalQuestion, finalLogTag);
                    }

                } catch (Exception e) {
                    log.error("{}/{} {} triggerAiCorrectingOld error: {}", index, totalQuestion, finalLogTag, e.getMessage());
                }
            }
        });
        if (isNeedAiCorrecting == 2) {
            //ai自动批阅的话，需要同步数据到userAnswerComment
            syncPaperAiCorrectingResultToComment(userAnswer);
        }
        log.info("{} check trigger ai correct done", logTag);
    }

    private static boolean isAnswer(UserAnswerDetail detail) {
        boolean isAnswer = false;
        List<String> answerList = JSONUtils.parseArray(detail.getAnswerStr(), String.class);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(answerList)) {
            for (String answer : answerList) {
                if (StringUtils.isNotBlank(answer)) {
                    isAnswer = true;
                    break;
                }
            }
        }
        return isAnswer;
    }

    //ai自动批阅的话，需要同步数据到userAnswerComment
    @Override
    public void syncPaperAiCorrectingResultToComment(UserAnswer userAnswer) {
        Long uid = userAnswer.getUid();
        Long userAnswerId = userAnswer.getId();
        List<SubjectiveQuestionAICorrectingLogPO> logList = subjectiveQuestionAiCorrectingRepository.getLogList(
                uid, userAnswerId, null, null, SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_PAPER.getValue());
        if(CollectionUtils.isEmpty(logList)) {
            return;
        }
        //需要先查一下有没有，根据uid+answerId+questionId+topicId
        List<UserAnswerComment> commentList = userAnswerCommentRepository.selectByAnswerIdList(uid, List.of(userAnswerId));
        Map<String, UserAnswerComment> commentMap = commentList.stream().collect(Collectors.toMap(item -> {
            return item.getQuestionId() + "_"+ item.getTopicId();
        }, item -> item));

        //记录下更新的id，最后需要从现有的中删除没有更新的部分
        Set<Long> updatedKeySet = new HashSet<>();
        for(SubjectiveQuestionAICorrectingLogPO log : logList) {
            //构建comment
            UserAnswerCommentPO comment = new UserAnswerCommentPO();
            comment.setUid(uid);
            comment.setAnswerId(userAnswerId);
            comment.setPaperId(userAnswer.getPaperId());
            comment.setGoodsId(userAnswer.getGoodsId());
            comment.setProductId(userAnswer.getProductId());
            comment.setQuestionId(log.getQuestionId());
            comment.setTopicId(log.getTopicId());
            if (StringUtils.startsWith(log.getAiAnswer(), "http://") || StringUtils.startsWith(log.getAiAnswer(), "https://")) {
                String content = OssUtil.getContentDataFromOss(log.getAiAnswer());
                NormalCommentContent normalCommentContent = JSONUtils.parseObject(content, NormalCommentContent.class);
                comment.setScore(Objects.nonNull(normalCommentContent.getScore()) ? normalCommentContent.getScore() : 0);
            }else {
                comment.setScore(0.0d);
            }
            comment.setComment(log.getAiAnswer());
            comment.setCommentTime(new Date());
            comment.setTeacherId(0L);
            comment.setTeacherName("AI老师");
            comment.setState(UserAnswerComment.State.COMMENTED.getValue());
            comment.setIsAiComment(1);

            String key = log.getQuestionId() + "_"+ log.getTopicId();
            //如果有，取id放到comment中，后续会更新记录
            UserAnswerComment exist = commentMap.get(key);
            if (Objects.nonNull(exist)) {
                comment.setId(exist.getId());
                updatedKeySet.add(exist.getId());
            }
            userAnswerCommentRepository.saveOrUpdate(comment);
        }
        //删除没有更新的
        if (CollectionUtils.isNotEmpty(updatedKeySet)) {
            commentMap.values().forEach(item -> {
                if (updatedKeySet.contains(item.getId())) {
                    return;
                }
                userAnswerCommentRepository.removeById(item.getId());
            });
        }
        //需要校准userAnswer
        correctSubjectiveResult(uid, userAnswerId, SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_PAPER.getValue());
    }

    private List<SubjectiveQuestionAICorrectingLogDTO> convertLogList(List<SubjectiveQuestionAICorrectingLogPO> list) {
        if(CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return list.stream().map(item -> {
            SubjectiveQuestionAICorrectingLogDTO dto = new SubjectiveQuestionAICorrectingLogDTO();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).toList();
    }

    @Override
    public List<SubjectiveQuestionAICorrectingLogDTO> getAiCorrectingLogList(
            @NonNull Long uid, @NonNull Long userAnswerId, Long questionId, Long topicId, @NonNull Integer questionSource) {
        List<SubjectiveQuestionAICorrectingLogPO> list = subjectiveQuestionAiCorrectingRepository.getLogList(
                uid, userAnswerId, questionId, topicId, questionSource);
        return convertLogList(list);
    }

    @Override
    public List<SubjectiveQuestionAICorrectingLogDTO> batchQueryAiCorrectingLogList(@NonNull List<Long> userAnswerIdList, @NonNull Integer questionSource) {
        List<SubjectiveQuestionAICorrectingLogPO> list = subjectiveQuestionAiCorrectingRepository.getLogList(userAnswerIdList, questionSource);
        return convertLogList(list);
    }

    @Override
    public void triggerAiCorrecting(@NonNull Long uid, @NonNull Long userAnswerId, @NonNull Long categoryId,
                                    @NonNull Long questionId, @NonNull Long topicId, @NonNull Integer questionSource,
                                    StreamObserver<StreamData> streamObserver) {
        String logTag = "triggerAiCorrecting-" + uid + "-" + userAnswerId + "-" + questionSource
                + "-" + questionId + "-" + topicId + ":";
        MDC.put(LOG_TAG, logTag);
        try {
            //先看看这个科目有没有配置，如果没有配置，报错
            Category category = knowledgeResource.getCategoryInfoById(categoryId);
            if (Objects.isNull(category)) {
                log.error("can not find category:{}", categoryId);
                return;
            }
            Long secondCategory = category.getParentId();
            if (Objects.isNull(secondCategory)) {
                log.error("can not find secondCategory:{}", categoryId);
                return;
            }

            List<SubjectiveQuestionAICorrectingLogPO> poList = subjectiveQuestionAiCorrectingRepository.getLogList(
                    uid, userAnswerId, questionId, topicId, questionSource);
            SubjectiveQuestionAICorrectingLogPO po = CollectionUtils.isEmpty(poList)
                    ? new SubjectiveQuestionAICorrectingLogPO() : poList.get(0);
            int retryCount = 0;
            if (SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTED.getValue().equals(po.getState())) {
                if (StringUtils.isNumeric(po.getRemarks())){
                    retryCount = Integer.parseInt(po.getRemarks());
                }
                if (retryCount > 3) {
                    // 复用已批阅的结果
                    if (streamObserver != null) {
                        streamObserver.onNext(new StreamData().setCode(0).setData(po.getAiAnswer()));
                    }
                    return;
                }
            }
            if (SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTING.getValue().equals(po.getState())) {
                // 已经在批阅中，不重复触发
                if (po.getUpdateDate().getTime() < System.currentTimeMillis() - 3600 * 1000) {
                    // 如果已经超过1小时了允许重新触发
                } else {
                    throw new BusinessException(ErrorCode.ERROR_AI_CORRECTING.getCode(), ErrorCode.ERROR_AI_CORRECTING.getMessage());
                }
            }
//            if (supportAiCorrecting(secondCategory, categoryId)) {
            //全部切换到新的，并且所有科目都用固定的场景
            if (true) {

                po.setUid(uid).setQuestionId(questionId).setTopicId(topicId)
                        .setUserAnswerId(userAnswerId.toString())
                        .setQuestionSource(questionSource)
                        .setCategoryId(categoryId)
                        .setPrompt("")
                        .setAiAnswer("")
                        .setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTING.getValue());
                subjectiveQuestionAiCorrectingRepository.saveOrUpdate(po);
                try {
                    //查询题目，根据题目类型不同，使用不同的businessSceneName
                    List<Question> questionList = knowledgeResource.getQuestionByIds(List.of(questionId));
                    if (CollectionUtils.isEmpty(questionList)) {
                        log.error("can not find question:{}", questionId);
                        return;
                    }
                    Question question = questionList.get(0);
                    String businessSceneName = "ReviewSubjectiveQuestion";
                    if(Objects.equals(question.getQtype(),5)){
                        // 简答题
                        businessSceneName = "SimpleQuestionReview";
                    }
                    AiAsstantRequest aiAsstantRequest = new AiAsstantRequest();
                    aiAsstantRequest.setUid(uid);
                    aiAsstantRequest.setAppName("edu-study");
                    aiAsstantRequest.setEntryName("ReviewSubjectiveQuestion");
                    aiAsstantRequest.setBusinessSceneName(businessSceneName);
                    aiAsstantRequest.getParams().put("questionId", questionId);
                    aiAsstantRequest.getParams().put("topicId", topicId);
                    aiAsstantRequest.getParams().put("userAnswerIdList", userAnswerId + "-" + uid);
                    aiAsstantRequest.setContent("{}");
                    log.info("triggerAiCorrecting:{}", JSONUtils.toJsonString(aiAsstantRequest));
                    final int fRetryCount = retryCount+1;
                    CountDownLatch latch = new CountDownLatch(1);
                    aiAgentChatService.streamChat(aiAsstantRequest, new SubjectiveQuestionAiCorrectingService.StreamObserver<>() {

                        @Override
                        public void onNext(StreamData data) {
                            if (Objects.nonNull(data) && Objects.equals(data.getCode(), 0)) {
                                po.setAiAnswer(po.getAiAnswer() + data.getData());
                            }
                            if (streamObserver != null) {
                                streamObserver.onNext(data);
                            }
                        }

                        @Override
                        public void onError(Throwable throwable) {
                            po.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_FAILED.getValue());
                            log.warn("{} streamChat error: {}", MDC.get(LOG_TAG), throwable.getMessage());
                            subjectiveQuestionAiCorrectingRepository.saveOrUpdate(po);
                            if (streamObserver != null) {
                                streamObserver.onError(throwable);
                            }
                        }

                        @Override
                        public void onCompleted() {
                            log.warn("{} streamChat completed", MDC.get(LOG_TAG));
                            po.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTED.getValue());
                            po.setRemarks(fRetryCount + "");
                            subjectiveQuestionAiCorrectingRepository.saveOrUpdate(po);
                            if (streamObserver != null) {
                                streamObserver.onCompleted();
                            }
                            latch.countDown();
                        }
                    });
                    latch.await();
                } catch (Exception e) {
                    log.error("{} exception: ", logTag, e);
                    po.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_FAILED.getValue());
                    subjectiveQuestionAiCorrectingRepository.saveOrUpdate(po);
                }
            } else{
                log.info("category {} not support new ai correcting, so use old, info: uid:{}, answerId:{}, questionId:{}", categoryId, uid, userAnswerId, questionId);
                triggerAiCorrectingOld(uid, userAnswerId, categoryId, questionId, topicId, questionSource, streamObserver);
            }

        } catch (BusinessException e) {
            log.warn("{}{}", MDC.get(LOG_TAG), e.getMessage());
            if(streamObserver != null) {
                streamObserver.onNext(new StreamData().setCode(e.getCode()).setData(e.getMessage()));
            }
        } catch (Exception e) {
            log.error("{} exception: ", logTag, e);
            throw new SystemExecErrorException(MDC.get(LOG_TAG) + "系统执行异常");
        } finally {
            MDC.remove(LOG_TAG);
        }

    }
    private boolean supportAiCorrecting(Long secondCategory, Long categoryId) {
        EntryExamCategoryParameter entryExamCategoryParameter = new EntryExamCategoryParameter(
                "ReviewSubjectiveQuestion", secondCategory, categoryId);
        ResponseEntity<ResultBody<List<CueWordVO>>> response = eduStudyAssistantFeign.cueWordList(entryExamCategoryParameter);
        if (response.getStatusCode() != HttpStatus.OK) {
            log.error("cueWordList error:{}", response.getStatusCode());
            return false;
        }
        if (response.getBody() == null) {
            log.error("cueWordList is empty");
            return false;
        }
        if (response.getBody().getCode() != 0) {
            log.error("cueWordList error:{}", response.getBody().getMsg());
            return false;
        }
        if (CollectionUtils.isEmpty(response.getBody().getData())) {
            log.error("cueWordList is empty");
            return false;
        }
        List<CueWordVO> cueWordList = response.getBody().getData();
        for (CueWordVO cueWord : cueWordList) {
            if (Objects.equals("ReviewSubjectiveQuestion",cueWord.getBusinessSceneName())) {
                return true;
            }
        }
        return false;
    }
    @Deprecated
    public void triggerAiCorrectingOld(
            @NonNull Long uid, @NonNull Long userAnswerId, @NonNull Long categoryId,
            @NonNull Long questionId, @NonNull Long topicId, @NonNull Integer questionSource,
            StreamObserver<StreamData> streamObserver) {
        String logTag = "triggerAiCorrecting-" + uid + "-" + userAnswerId + "-" + questionSource
                + "-" + questionId + "-" + topicId + ":";
        MDC.put(LOG_TAG, logTag);
        try {
            // 获取答题记录
            UserAnswerDetail userAnswerDetail = findSubjectiveQuestionAnswerDetail(
                    uid, userAnswerId, questionId, topicId, questionSource);

            // 获取题目信息
            QuestionInfo4AiCorrectingDTO questionInfo4AiCorrecting = getQuestionInfo(questionId, topicId);

            // 获取提示词配置模板
            SubjectiveQuestionAICorrectingPromptTemplateDTO promptTemplate = getPromptTemplate(categoryId);

            GptChatService.ChatParam chatParam = buildParam(questionInfo4AiCorrecting, userAnswerDetail, promptTemplate);

            List<SubjectiveQuestionAICorrectingLogPO> poList = subjectiveQuestionAiCorrectingRepository.getLogList(
                    uid, userAnswerId, questionId, topicId, questionSource);
            SubjectiveQuestionAICorrectingLogPO po = CollectionUtils.isEmpty(poList)
                    ? new SubjectiveQuestionAICorrectingLogPO() : poList.get(0);
            if(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTED.getValue().equals(po.getState())) {
                // 复用已批阅的结果
                if(streamObserver != null) {
                    streamObserver.onNext(new StreamData().setCode(0).setData(po.getAiAnswer()));
                }
                return;
            }
            if (SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTING.getValue().equals(po.getState())) {
                // 已经在批阅中，不重复触发
                throw new BusinessException(ErrorCode.ERROR_AI_CORRECTING.getCode(), ErrorCode.ERROR_AI_CORRECTING.getMessage());
            }

            po.setUid(uid).setQuestionId(questionId).setTopicId(topicId)
                    .setUserAnswerId(userAnswerId.toString())
                    .setQuestionSource(questionSource)
                    .setCategoryId(questionInfo4AiCorrecting.getCategoryId())
                    .setPrompt(chatParam.getUserPrompt())
                    .setAiAnswer("")
                    .setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTING.getValue());
            subjectiveQuestionAiCorrectingRepository.saveOrUpdate(po);
            CountDownLatch latch = new CountDownLatch(1);
            gptChatService.streamChat(chatParam, new GptChatService.StreamObserver() {
                @Override
                public void onNext(String s) {
                    po.setAiAnswer(po.getAiAnswer() + s);
                    if(streamObserver != null) {
                        streamObserver.onNext(new StreamData().setCode(0).setData(s));
                    }
                }

                @Override
                public void onError(Throwable throwable) {
                    po.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_FAILED.getValue());
                    log.warn("{} streamChat error: {}", MDC.get(LOG_TAG), throwable.getMessage());
                    subjectiveQuestionAiCorrectingRepository.saveOrUpdate(po);
                    if(streamObserver != null) {
                        streamObserver.onError(throwable);
                    }
                }

                @Override
                public void onCompleted() {
                    log.warn("{} streamChat completed", MDC.get(LOG_TAG));
                    po.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTED.getValue());
                    subjectiveQuestionAiCorrectingRepository.saveOrUpdate(po);
                    if(streamObserver != null) {
                        streamObserver.onCompleted();
                    }
                    latch.countDown();
                }
            });
            latch.await();
        } catch (BusinessException e) {
            log.warn("{}{}", MDC.get(LOG_TAG), e.getMessage());
            if(streamObserver != null) {
                streamObserver.onNext(new StreamData().setCode(e.getCode()).setData(e.getMessage()));
            }
        } catch (Exception e) {
            log.error("{} exception: ", logTag, e);
            throw new SystemExecErrorException(MDC.get(LOG_TAG) + "系统执行异常");
        } finally {
            MDC.remove(LOG_TAG);
        }
    }

    @Override
    public void testTriggerAiCorrecting(@NonNull Long uid, @NonNull Long userAnswerId, @NonNull Long questionId, @NonNull Long topicId,
                                        @NonNull Integer questionSource, StreamObserver<StreamData> streamObserver) {
        String logTag = "testTriggerAiCorrecting-" + uid + "-" + userAnswerId + "-"
                + questionSource + "-" + questionId + "-" + topicId + ":";
        MDC.put(LOG_TAG, logTag);
        try {
            // 获取题目信息
            QuestionInfo4AiCorrectingDTO questionInfo4AiCorrecting = getQuestionInfo(questionId, topicId);
            // 获取答题记录
            UserAnswerDetail userAnswerDetail = findSubjectiveQuestionAnswerDetail(
                    uid, userAnswerId, questionId, topicId, questionSource);

            // 获取提示词配置模板
            SubjectiveQuestionAICorrectingPromptTemplateDTO promptTemplate = getPromptTemplate(
                    questionInfo4AiCorrecting.getCategoryId());

            GptChatService.ChatParam chatParam = buildParam(questionInfo4AiCorrecting, userAnswerDetail, promptTemplate);

            List<SubjectiveQuestionAICorrectingLogPO> poList = subjectiveQuestionAiCorrectingRepository.getLogList(
                    uid, userAnswerId, questionId, topicId, questionSource);
            if(CollectionUtils.isEmpty(poList)) {
                streamObserver.onError(new Exception("未找到测试的AI批阅记录"));
                return;
            }
            String testAiAnswer = "这是测试AI批阅流式接口的测试内容";
            for(int i = 0; i < testAiAnswer.length(); ++i) {
                String s = testAiAnswer.substring(i, i + 1);
                log.info("{}.{} send ai correcting text: {}", i, MDC.get(LOG_TAG), s);
                streamObserver.onNext(new StreamData().setCode(0).setData(s));
                Thread.sleep(200);
            }
        } catch (BusinessException e) {
            log.warn("{}{}", MDC.get(LOG_TAG), e.getMessage());
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("{} exception: ", logTag, e);
            streamObserver.onError(e);
        } finally {
            MDC.remove(LOG_TAG);
        }
    }

    private GptChatService.ChatParam buildParam(QuestionInfo4AiCorrectingDTO question, UserAnswerDetail userAnswerDetail,
                                                SubjectiveQuestionAICorrectingPromptTemplateDTO promptTemplate) {
        final String userKey = "subjective_question_ai_" + userAnswerDetail.getUid()
                + "_" + userAnswerDetail.getQuestionId() + "_" + userAnswerDetail.getTopicId();
        Category category = knowledgeResource.getCategoryInfoById(promptTemplate.getCategoryId());
        String categoryName = category == null ? "" : category.getName();

        String systemPrompt = StringUtils.replace(promptTemplate.getSystemPromptTemplate(), "${XX考试}", categoryName);
        String userPrompt = StringUtils.replace(promptTemplate.getUserPromptTemplate(), "${题目信息}", question.getQuestionInfo());
        userPrompt = StringUtils.replace(userPrompt, "${答案信息}", question.getAnswerInfo());
        userPrompt = StringUtils.replace(userPrompt, "${用户作答}", userAnswerDetail.getAnswerStr());

        GptChatService.ChatParam chatParam = new GptChatService.ChatParam();
        chatParam.setUserKey(userKey);
        chatParam.setSystemPrompt(systemPrompt);
        chatParam.setUserPrompt(userPrompt);
        chatParam.setGptConfigJson(promptTemplate.getOtherParam());
        return chatParam;
    }

    private UserAnswerDetail findSubjectiveQuestionAnswerDetail(Long uid, Long userAnswerId, Long questionId,
                                                                      Long topicId, Integer questionSource) throws BusinessException, DataAccessException {
        List<Long> questionIds = questionId == null ? null : List.of(questionId);
        List<UserAnswerSum> userAnswerSumList = null;
        if(SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_PAPER.getValue().equals(questionSource)) {
            userAnswerSumList = userAnswerSumService.findAnswerSumAndDetail(uid, userAnswerId, questionIds);
        } else if(SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_HOMEWORK.getValue().equals(questionSource)) {
            userAnswerSumList = userAnswerSumService.findHomeworkSumAndDetail(uid, userAnswerId, questionIds);
        } else {
            log.warn("{}{}", MDC.get(LOG_TAG), ErrorCode.ERROR_QUESTION_SOURCE_UNSUPPORTED.getMessage());
            throw new BusinessException(ErrorCode.ERROR_QUESTION_SOURCE_UNSUPPORTED.getCode(),
                    ErrorCode.ERROR_QUESTION_SOURCE_UNSUPPORTED.getMessage() + ":" + questionSource);
        }
        if(CollectionUtils.isEmpty(userAnswerSumList) || CollectionUtils.isEmpty(userAnswerSumList.get(0).getAnswerDetail())) {
            log.warn("{}{}", MDC.get(LOG_TAG), ErrorCode.ERROR_USER_ANSWER_NOT_FOUND.getMessage());
            throw new BusinessException(ErrorCode.ERROR_USER_ANSWER_NOT_FOUND.getCode(),
                    ErrorCode.ERROR_USER_ANSWER_NOT_FOUND.getMessage());
        }
        UserAnswerDetail userAnswerDetail = userAnswerSumList.stream()
                .filter(sum -> questionId.equals(sum.getQuestionId()))
                .map(UserAnswerSum::getAnswerDetail).flatMap(Collection::stream)
                .filter(item -> item.getTopicId().equals(topicId)).findFirst().orElse(null);

        if(userAnswerDetail == null || StringUtils.isEmpty(userAnswerDetail.getAnswerStr()) || "[]".equals(userAnswerDetail.getAnswerStr())){
            log.warn("{}{}", MDC.get(LOG_TAG), ErrorCode.ERROR_USER_ANSWER_NOT_FOUND.getMessage());
            throw new BusinessException(ErrorCode.ERROR_USER_ANSWER_NOT_FOUND.getCode(),
                    ErrorCode.ERROR_USER_ANSWER_NOT_FOUND.getMessage());
        }
        if(containsImage(userAnswerDetail.getAnswerStr())) {
            // 答案中包含图片，暂不支持AI批阅
            log.warn("{}{}", MDC.get(LOG_TAG), ErrorCode.ERROR_ANSWER_CONTAIN_IMAGE.getMessage());
            throw new BusinessException(ErrorCode.ERROR_ANSWER_CONTAIN_IMAGE.getCode(),
                    ErrorCode.ERROR_ANSWER_CONTAIN_IMAGE.getMessage());
        }
        return userAnswerDetail;
    }

    private boolean containsImage(String answerStr) {
        String pattern = "<img.*?>";
        Pattern imgTagPattern = Pattern.compile(pattern);
        Matcher matcher = imgTagPattern.matcher(answerStr);
        return matcher.find()
                && (StringUtils.containsIgnoreCase(answerStr, "http://")
                || StringUtils.containsIgnoreCase(answerStr, "https://"));
    }

    QuestionInfo4AiCorrectingDTO getQuestionInfo(Long questionId, Long topicId) throws BusinessException {
        QuestionInfo4PromptQuery query = new QuestionInfo4PromptQuery();
        query.setQuestionId(questionId);
        query.setTopicId(topicId);
        ResponseBean<QuestionInfo4AiCorrectingDTO> responseBean = resourceCommonFeign.getQuestionInfo4AiCorrecting(query);
        if (Objects.isNull(responseBean) || !responseBean.checkIsSuccess()){
            log.warn("{} {}", MDC.get(LOG_TAG), ErrorCode.ERROR_NO_QUESTION_INFO.getMessage());
            throw new BusinessException(ErrorCode.ERROR_NO_QUESTION_INFO.getCode(),
                    "获取题目信息失败：" + questionId + "." + topicId);
        }
        QuestionInfo4AiCorrectingDTO questionInfo4AiCorrectingDTO = responseBean.getData();
        if(questionInfo4AiCorrectingDTO.isHasImage()) {
            log.warn("{} {}", MDC.get(LOG_TAG), ErrorCode.ERROR_QUESTION_CONTAIN_IMAGE.getMessage());
            throw new BusinessException(ErrorCode.ERROR_QUESTION_CONTAIN_IMAGE.getCode(), "该题目包含图片，暂不支持AI批阅");
        }
        return responseBean.getData();
    }

    private SubjectiveQuestionAICorrectingPromptTemplateDTO getPromptTemplate(Long categoryId) throws BusinessException {
        SubjectiveQuestionAICorrectingPromptTemplateQuery promptTemplateQuery = new SubjectiveQuestionAICorrectingPromptTemplateQuery();
        promptTemplateQuery.setCategoryId(categoryId);
        List<SubjectiveQuestionAICorrectingPromptTemplateDTO> promptTemplateList =
                promptResource.getSubjectiveQuestionAiCorrectingPrompt(promptTemplateQuery);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(promptTemplateList)){
            log.warn("{}{} category={}", MDC.get(LOG_TAG), ErrorCode.ERROR_NO_PROMPT_TEMPLATE.getMessage(), categoryId);
            throw new BusinessException(ErrorCode.ERROR_NO_PROMPT_TEMPLATE.getCode(), "该科目暂不支持AI批阅！科目ID=" + categoryId);
        }
        return promptTemplateList.get(0);
    }

    @Async
    @EventListener
    @Override
    public void onHomeworkSubmittedEvent(HomeworkSubmittedEvent event){
        UserHomeWorkAnswer userAnswer = event.getUserAnswer();
        // 基础参数校验
        if (userAnswer == null || userAnswer.getGoodsId() == null
                || userAnswer.getProductId() == null
                || (userAnswer.getObjId() == null && userAnswer.getStudyPathId() == null)
                || userAnswer.getHomeworkId() == null) {
            log.info("onHomeworkSubmittedEvent no need to ai correct:{}", Objects.isNull(userAnswer) ? null : userAnswer.getId());
            return;
        }
        String logTag = getLogTag(userAnswer);
        List<Long> questionIdList = new ArrayList<>();
        int isNeedAiCorrecting = isHomeworkNeedAiCorrecting(userAnswer, questionIdList);
        if (isNeedAiCorrecting == 0) {
            log.info("{}: no need to ai correct", logTag);
            return;
        }

        final Set<Integer> subjectiveQTypeSet = Set.of(
                Consts.QType.QType5.getCode(), Consts.QType.QType6.getCode(), Consts.QType.QType7.getCode());
        Map<Long, Set<Long>> subjectiveQuestionTopicMap = new HashMap<>(userAnswer.getAnswerDetail().size());
        List<Question> questions = knowledgeResource.getQuestionByIds(questionIdList);
        //存一下主观题
        Map<Long, Question> subjectiveQuestionMap = new HashMap<>(userAnswer.getAnswerDetail().size());
        for(Question question : questions) {
            if(!subjectiveQTypeSet.contains(question.getQtype())) {
                // 非主观题不处理
                continue;
            }
            subjectiveQuestionMap.put(question.getId(), question);
            question.getTopicList().forEach(topic -> {
                if(QuestionTopic.Type.ESSAY.equals(topic.getQtype())) {
                    subjectiveQuestionTopicMap.computeIfAbsent(question.getId(), k -> new HashSet<>()).add(topic.getId());
                }
            });
        }
        final int totalQuestion = userAnswer.getAnswerDetail().size();
        AtomicInteger currentIndex = new AtomicInteger(0);
        String finalLogTag = logTag;
        userAnswer.getAnswerDetail().forEach(detail -> {
            int index = currentIndex.incrementAndGet();
            if(!subjectiveQuestionTopicMap.containsKey(detail.getQuestionId())
                    || !subjectiveQuestionTopicMap.get(detail.getQuestionId()).contains(detail.getTopicId())) {
                // 非主观题不处理
                log.debug("{}/{} {} not subjective question, questionId={}, topicId={}",
                        index, totalQuestion, finalLogTag, detail.getQuestionId(), detail.getTopicId());
                return;
            }
            log.info("{}/{} {} questionId={}, topicId={} ai correcting...", index, totalQuestion,
                    finalLogTag, detail.getQuestionId(), detail.getTopicId());
            // 触发AI批阅
            // 根据配置走不同的逻辑
            if (isNeedAiCorrecting == 1 || isNeedAiCorrecting == 2) {
                try {
                    //如果没有作答，跳过
                    boolean isAnswer = isAnswer(detail);
                    if (isAnswer) {
                        // 构建params
                        Map<String, Object> params = new HashMap<>();
                        Question question = subjectiveQuestionMap.get(detail.getQuestionId());
                        if (Objects.isNull(question)) {
                            log.error("{}/{} {} can not find question:{}", index, totalQuestion, finalLogTag, detail.getQuestionId());
                            return;
                        }
                        params.put("questionId", detail.getQuestionId());
                        params.put("topicId", detail.getTopicId());
                        params.put("userAnswerId", userAnswer.getId());
                        params.put("questionSource", SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_HOMEWORK.getValue());
                        params.put("secondCategory", question.getSecondCategory());
                        params.put("category", question.getCategoryId());
                        params.put("correctMethod", isNeedAiCorrecting);

                        subjectiveQuestionAiCorrecting(userAnswer.getUid(), params);
                    } else {
                        log.info("question:{} topic:{} not answer, skip,tag:{} ", detail.getQuestionId(), detail.getTopicId(), finalLogTag);
                    }

                } catch (Exception e) {
                    log.error("{}/{} {} triggerAiCorrecting error: {}", index, totalQuestion, finalLogTag, e.getMessage());
                }
            } else if (isNeedAiCorrecting == 3) {
                try {
                    // 构建params
                    Map<String, Object> params = new HashMap<>();
                    Question question = subjectiveQuestionMap.get(detail.getQuestionId());
                    if(Objects.isNull(question)) {
                        log.error("{}/{} {} can not find question:{}", index, totalQuestion, finalLogTag, detail.getQuestionId());
                        return;
                    }
                    params.put("questionId", detail.getQuestionId());
                    params.put("topicId", detail.getTopicId());
                    params.put("userAnswerId", userAnswer.getId());
                    params.put("questionSource", SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_PAPER.getValue());
                    params.put("secondCategory", question.getSecondCategory());
                    params.put("category", question.getCategoryId());
                    params.put("correctMethod", isNeedAiCorrecting);

                    List<String> photoAnswerUrlList = JSONUtils.parseArray(detail.getAnswerStr(), String.class);
                    // 构建content
                    List<Map<String, Object>> content = new ArrayList<>();
                    if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(photoAnswerUrlList)){
                        for (String url : photoAnswerUrlList) {
                            Map<String, Object> image = new HashMap<>();
                            image.put("type", "image_url");
                            Map<String, Object> imageUrl = new HashMap<>();
                            imageUrl.put("url", url);
                            image.put("imageUrl", imageUrl);
                            content.add(image);
                        }
                        this.photoAnswerAiCorrecting(userAnswer.getUid(), "", params, content);
                    } else {
                        log.error("{}/{} {} photoAnswerUrlList is empty", index, totalQuestion, finalLogTag);
                    }

                } catch (Exception e) {
                    log.error("{}/{} {} triggerAiCorrectingOld error: {}", index, totalQuestion, finalLogTag, e.getMessage());
                }
            }
        });
        if (isNeedAiCorrecting == 2) {
            //ai自动批阅的话，需要同步数据到userAnswerComment
            syncHomeworkAiCorrectingResultToComment(userAnswer);
        }
        log.info("{} check trigger ai correct done", logTag);
    }

    private void syncHomeworkAiCorrectingResultToComment(UserHomeWorkAnswer userHomeWorkAnswer) {
        Long uid = userHomeWorkAnswer.getUid();
        Long answerId = userHomeWorkAnswer.getId();

        List<SubjectiveQuestionAICorrectingLogPO> logList = subjectiveQuestionAiCorrectingRepository.getLogList(
                uid, answerId, null, null, SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_HOMEWORK.getValue());
        if(CollectionUtils.isEmpty(logList)) {
            return;
        }
        //需要先查一下有没有，根据uid+answerId查询，根据=questionId+topicId聚合map
        UserHomeworkCommentListQuery query = new UserHomeworkCommentListQuery();
        query.setUid(uid);
        query.setAnswerId(answerId);
        List<UserHomeworkComment> commentList = userHomeworkCommentService.getUserHomeworkCommentList(query);
        Map<String, UserHomeworkComment> commentMap = commentList.stream().collect(Collectors.toMap(item -> {
            return item.getQuestionId() + "_"+ item.getTopicId();
        }, item -> item));
        //记录下更新的id，最后需要从现有的中删除没有更新的部分
        Set<Long> updatedKeySet = new HashSet<>();
        for(SubjectiveQuestionAICorrectingLogPO log : logList) {
            //构建comment
            UserHomeworkComment comment = new UserHomeworkComment();
            comment.setUid(uid);
            comment.setAnswerId(answerId);
            comment.setGoodsId(userHomeWorkAnswer.getGoodsId());
            comment.setProductId(userHomeWorkAnswer.getProductId());
            comment.setHomeworkId(userHomeWorkAnswer.getHomeworkId());
            comment.setQuestionId(log.getQuestionId());
            comment.setTopicId(log.getTopicId());
            if (StringUtils.startsWith(log.getAiAnswer(), "http://") || StringUtils.startsWith(log.getAiAnswer(), "https://")) {
                String content = OssUtil.getContentDataFromOss(log.getAiAnswer());
                NormalCommentContent normalCommentContent = JSONUtils.parseObject(content, NormalCommentContent.class);
                comment.setScore(Objects.nonNull(normalCommentContent.getScore()) ? normalCommentContent.getScore() : 0);
            }else {
                comment.setScore(0.0d);
            }
            comment.setComment(log.getAiAnswer());
            comment.setCommentTime(new Date());
            comment.setTeacherId(0L);
            comment.setTeacherName("AI老师");
            comment.setStatus(Consts.UserHomeworkCommentStatus.COMMIT);
            comment.setIsAiComment(1);

            String key = log.getQuestionId() + "_"+ log.getTopicId();
            //如果有，取id放到comment中，后续会更新记录
            UserHomeworkComment exist = commentMap.get(key);
            if (Objects.nonNull(exist)) {
                comment.setId(exist.getId());
                updatedKeySet.add(exist.getId());
            }
            userHomeworkCommentService.saveOrUpdate(comment);
        }
        //删除没有更新的
        if (CollectionUtils.isNotEmpty(updatedKeySet)) {
            commentMap.values().forEach(item -> {
                if (updatedKeySet.contains(item.getId())) {
                    return;
                }
                userHomeworkCommentService.removeById(item.getId());
            });
        }
        //需要校准userAnswer
        correctSubjectiveResult(uid, answerId, SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_HOMEWORK.getValue());
    }

    /**
     * 构建AI助手请求参数
     */
    private AiAsstantRequest buildAiRequest(Long uid, String sessionId, Map<String, Object> params,
                                          List<Map<String, Object>> content, String entryName, String businessSceneName) {
        AiAsstantRequest request = new AiAsstantRequest();
        request.setUid(uid);
        request.setSessionId(sessionId);
        request.setParams(params);
        request.setContent(content);
        request.setAppName("edu-study");
        request.setEntryName(entryName);
        request.setBusinessSceneName(businessSceneName);
        return request;
    }

    /**
     * 执行Feign请求并获取结果
     */
    private List<Map> executeFeignRequest(AiAsstantRequest request, String logTag, String requestType) {
        return RetryUtil.executeWithRetry(() -> eduStudyAssistantFeign.question(request), 3, logTag + requestType);
    }

    /**
     * 按图片URL分组处理数据
     */
    private Map<String, List<Map<String, Object>>> groupByImageUrl(List<Map> relationData) {
        Map<String, List<Map<String, Object>>> imageGroups = new HashMap<>();
        for (Map<String, Object> relation : relationData) {
            String imgUrl = (String) relation.get("imgUrl");
            imageGroups.computeIfAbsent(imgUrl, k -> new ArrayList<>()).add(relation);
        }
        return imageGroups;
    }

    /**
     * 处理boundingBox字符串
     */
    private String processBoundingBox(String boundingBox, String url) {
        if (boundingBox == null || boundingBox.isEmpty()) {
            return "";
        }
        // 去掉<bbox>标签
        String content = boundingBox.replaceAll("<bbox>|</bbox>", "");
        // trim后按空格分割
        String[] coordinates = content.trim().split("\\s+");
        if (coordinates.length != 4) {
            return "";
        }

        try {
            // 获取图片尺寸
            String[] picInfo = getPicInfo(url);
            if (picInfo == null || picInfo.length != 2) {
                return Arrays.stream(coordinates).collect(Collectors.joining(","));
            }

            int w = Integer.parseInt(picInfo[0]);
            int h = Integer.parseInt(picInfo[1]);

            // 解析原始坐标
            int x_min = Integer.parseInt(coordinates[0]);
            int y_min = Integer.parseInt(coordinates[1]);
            int x_max = Integer.parseInt(coordinates[2]);
            int y_max = Integer.parseInt(coordinates[3]);

            // 应用缩放公式
            int x_min_real = (int) (x_min * w / 1000.0);
            int y_min_real = (int) (y_min * h / 1000.0);
            int x_max_real = (int) (x_max * w / 1000.0);
            int y_max_real = (int) (y_max * h / 1000.0);

            log.info("processBoundingBox url:{},w:{},h:{},x_min:{},y_min:{},x_max:{},y_max:{}", url, w, h, x_min, y_min, x_max, y_max);

            return String.format("%d,%d,%d,%d", x_min_real, y_min_real, x_max_real, y_max_real);
        } catch (Exception e) {
            log.error("Error processing bounding box: {}", e.getMessage());
            return Arrays.stream(coordinates).collect(Collectors.joining(","));
        }
    }

    //根据url获取图片的宽和高
    private String[] getPicInfo(String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return null;
        }
        String[] result = new String[2];
        try {
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);

            if (image != null) {
                int width = image.getWidth();
                int height = image.getHeight();
                result[0] = String.valueOf(width);
                result[1] = String.valueOf(height);
            }
        } catch (Exception e) {
            log.error("getPicInfo error", e);
        }
        return result;
    }

    /**
     * 构建单个图片的结果
     */
    private Map<String, Object> buildImageResult(String url, List<Map<String, Object>> imageRelations, 
                                               List<Map> relationData, List<Map> responseData) {
        Map<String, Object> imageResult = new HashMap<>();
        imageResult.put("imgUrl", url);
        
        List<Map<String, Object>> questions = new ArrayList<>();
        for (Map<String, Object> relation : imageRelations) {
            Map<String, Object> question = new HashMap<>();
            question.put("question", relation.get("question"));
            question.put("position", processBoundingBox((String) relation.get("boundingBox"), url));
            question.put("imgIndex", relation.get("imgIndex"));
            
            // 获取对应的批阅结果 - 使用索引一一对应
            int relationIndex = relationData.indexOf(relation);
            if (relationIndex >= 0 && relationIndex < responseData.size() && !responseData.get(relationIndex).isEmpty()) {
                question.put("smallQuestionScoreAnalysis", responseData.get(relationIndex).get("得分点分析"));
                question.put("smallQuestionScore", responseData.get(relationIndex).get("子题得分"));
                question.put("smallQuestionTotalScore", responseData.get(relationIndex).get("子题总分"));
                question.put("smallQuestionComment", responseData.get(relationIndex).get("评语"));
            }
            questions.add(question);
        }
        
        imageResult.put("relationQuestion", questions);
        return imageResult;
    }

    /**
     * 将结果保存到OSS并生成JS文件
     */
    private String saveToOSS(String jsonContent, String logTag) {
        try {
            // 生成唯一的文件名
            String fileName = "ai-correcting/" + UUID.randomUUID().toString() + ".js";

            // 使用OssUtil上传文件
            return OssUtil.uploadPublicFile(jsonContent.getBytes(), fileName);
        } catch (Exception e) {
            log.error("{} 保存结果到OSS失败: {}", logTag, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void photoAnswerAiCorrecting(@NonNull Long uid, @NonNull String sessionId, @NonNull Map<String, Object> params, @NonNull List<Map<String, Object>> content) {
        String logTag = "photoAnswerAiCorrecting-" + uid + "-" + sessionId + ": ";
        try {
            Long questionId = (Long) params.get("questionId");
            Long topicId = (Long) params.get("topicId");
            Long userAnswerId = (Long) params.get("userAnswerId");
            Integer questionSource = (Integer) params.get("questionSource");
            Integer correctMethod = (Integer) params.get("correctMethod");
            Long secondCategoryId = (Long) params.get("secondCategory");
            Long categoryId = (Long) params.get("category");

            // 获取待批阅的内容
            List<SubjectiveQuestionAICorrectingLogPO> sqacLogList = subjectiveQuestionAiCorrectingRepository.getLogList(uid, userAnswerId, questionId, topicId, questionSource);

            SubjectiveQuestionAICorrectingLogPO sqacLog = new SubjectiveQuestionAICorrectingLogPO();
            if (CollectionUtils.isEmpty(sqacLogList)) {
                sqacLog.setSecondCategory(secondCategoryId);
                sqacLog.setCategoryId(categoryId);
                sqacLog.setQuestionId(questionId);
                sqacLog.setTopicId(topicId);
                sqacLog.setCreateDate(new Date());
                sqacLog.setUpdateDate(new Date());
                sqacLog.setUid(uid);
                sqacLog.setUserAnswerId(userAnswerId != null ? userAnswerId.toString() : null);
                sqacLog.setQuestionSource(questionSource);
                sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTING.getValue());
                sqacLog.setCorrectMethod(correctMethod);
                subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
            }else{
                sqacLog = sqacLogList.get(0);
                sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTING.getValue());
                subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
            }

            // 构建两个请求参数
            AiAsstantRequest request = buildAiRequest(uid, sessionId, params, content, "ReviewSubjectiveQuestion", "CaseReviewHandwritten");
            AiAsstantRequest relationRequest = buildAiRequest(uid, sessionId, params, content, "ReviewSubjectiveQuestion", "HandwrittenAnswerImageRelation");

            // 并行执行两个请求
            CompletableFuture<List<Map>> responseFuture = CompletableFuture.supplyAsync(() ->
                executeFeignRequest(request, logTag, "response")
            );
            CompletableFuture<List<Map>> relationFuture = CompletableFuture.supplyAsync(() ->
                executeFeignRequest(relationRequest, logTag, "relation")
            );
            CompletableFuture.allOf(responseFuture, relationFuture).join();

            List<Map> responseData = responseFuture.get();
            List<Map> relationData = relationFuture.get();

            if (responseData == null || relationData == null) {
                log.error("{} 请求失败，responseData: {}, relationData: {}", logTag, responseData, relationData);
                sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_FAILED.getValue());
                subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
                return;
            }

            // 按图片URL分组处理数据
            Map<String, List<Map<String, Object>>> imageGroups = groupByImageUrl(relationData);

            // 构建最终结果
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> imageContent : content) {
                Map<String, Object> imageUrl = (Map<String, Object>) imageContent.get("imageUrl");
                String url = (String) imageUrl.get("url");
                List<Map<String, Object>> imageRelations = imageGroups.getOrDefault(url, new ArrayList<>());

                Map<String, Object> imageResult = buildImageResult(url, imageRelations, relationData, responseData);
                result.add(imageResult);
            }

            // 保存结果到OSS
            String jsonContent = JSONUtils.toJSONString(result);
            String ossUrl = saveToOSS(jsonContent, logTag);
            if (ossUrl != null) {
                sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTED.getValue());
                sqacLog.setAiAnswer(ossUrl);
                subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
            }
            log.info("{} 处理完成，共处理{}张图片", logTag, result.size());
        } catch (Exception e) {
            log.error("{} photoAnswerAiCorrecting error: {}", logTag, e.getMessage(), e);
        }
    }

    private Map<String, Object> processReviewSubjectiveQuestionResponse(Map<String, Object> response) {
        Map<String, Object> result = new HashMap<>();
        result.put("comment", response.get("批阅结果"));
        result.put("score", response.get("子题得分"));
        result.put("totalScore", response.get("子题总分"));
        return result;
    }

    private Map<String, Object> processSimpleQuestionReviewResponse(List<Map> responses) {
        Map<String, Object> result = new HashMap<>();
        StringBuilder commentBuilder = new StringBuilder(COMMENT_HEADER);
        int totalScore = 0;
        int score = 0;

        for (Map response : responses) {
            String subQuestion = (String) response.get("子题内容");
            String reviewResult = (String) response.get("批阅结果");
            Integer subScore = (Integer) response.get("子题得分");
            Integer subTotalScore = (Integer) response.get("子题总分");

            commentBuilder.append(String.format(SUB_QUESTION_FORMAT, subQuestion, reviewResult));
            score += subScore;
            totalScore += subTotalScore;
        }

        commentBuilder.append(COMMENT_FOOTER);
        result.put("comment", commentBuilder.toString());
        result.put("score", score);
        result.put("totalScore", totalScore);
        return result;
    }

    @Override
    public void subjectiveQuestionAiCorrecting(@NonNull Long uid, @NonNull Map<String, Object> params) {
        String logTag = "subjectiveQuestionAiCorrecting-" + uid + ": ";
        try {
            Long questionId = (Long) params.get("questionId");
            Long topicId = (Long) params.get("topicId");
            Long userAnswerId = (Long) params.get("userAnswerId");
            Integer questionSource = (Integer) params.get("questionSource");
            Integer correctMethod = (Integer) params.get("correctMethod");
            Long secondCategoryId = (Long) params.get("secondCategory");
            Long categoryId = (Long) params.get("category");

            // 获取待批阅的内容
            List<SubjectiveQuestionAICorrectingLogPO> sqacLogList = subjectiveQuestionAiCorrectingRepository.getLogList(uid, userAnswerId, questionId, topicId, questionSource);

            SubjectiveQuestionAICorrectingLogPO sqacLog = new SubjectiveQuestionAICorrectingLogPO();
            if (CollectionUtils.isEmpty(sqacLogList)) {
                sqacLog.setSecondCategory(secondCategoryId);
                sqacLog.setCategoryId(categoryId);
                sqacLog.setQuestionId(questionId);
                sqacLog.setTopicId(topicId);
                sqacLog.setCreateDate(new Date());
                sqacLog.setUpdateDate(new Date());
                sqacLog.setUid(uid);
                sqacLog.setUserAnswerId(userAnswerId != null ? userAnswerId.toString() : null);
                sqacLog.setQuestionSource(questionSource);
                sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTING.getValue());
                sqacLog.setCorrectMethod(correctMethod);
                subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
            } else {
                sqacLog = sqacLogList.get(0);
                sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTING.getValue());
                subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
            }

            List<Question> questionList = knowledgeResource.getQuestionByIds(List.of(questionId));
            if (CollectionUtils.isEmpty(questionList)) {
                log.error("can not find question:{}", questionId);
                return;
            }
            Question question = questionList.get(0);
            String businessSceneName = "ReviewSubjectiveQuestion";
            if(Objects.equals(question.getQtype(),5)){
                // 简答题
                businessSceneName = "SimpleQuestionReview";
            }
            AiAsstantRequest aiAsstantRequest = new AiAsstantRequest();
            aiAsstantRequest.setUid(uid);
            aiAsstantRequest.setAppName("edu-study");
            aiAsstantRequest.setEntryName("ReviewSubjectiveQuestion");
            aiAsstantRequest.setBusinessSceneName(businessSceneName);
            aiAsstantRequest.getParams().put("questionId", questionId);
            aiAsstantRequest.getParams().put("topicId", topicId);
            aiAsstantRequest.getParams().put("userAnswerIdList", userAnswerId + "-" + uid);
            aiAsstantRequest.setContent("{}");

            Map<String, Object> processedResult;
            if ("ReviewSubjectiveQuestion".equals(businessSceneName)) { //案例题
                Map responseData = RetryUtil.executeWithRetryForSingleObject(() -> eduStudyAssistantFeign.question(aiAsstantRequest), 3, logTag);
                if (responseData == null) {
                    log.error("{} 请求失败，responseData: {}", logTag, responseData);
                    sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_FAILED.getValue());
                    subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
                    return;
                }
                processedResult = processReviewSubjectiveQuestionResponse(responseData);
            } else { //简单题
                List<Map> responseData = RetryUtil.executeWithRetry(() -> eduStudyAssistantFeign.question(aiAsstantRequest), 3, logTag);
                if (responseData == null) {
                    log.error("{} 请求失败，responseData: {}", logTag, responseData);
                    sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_FAILED.getValue());
                    subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
                    return;
                }
                processedResult = processSimpleQuestionReviewResponse(responseData);
            }

            // 保存结果到OSS
            String jsonContent = JSONUtils.toJSONString(processedResult);
            String ossUrl = saveToOSS(jsonContent, logTag);
            if (ossUrl != null) {
                sqacLog.setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTED.getValue());
                sqacLog.setAiAnswer(ossUrl);
                subjectiveQuestionAiCorrectingRepository.saveOrUpdate(sqacLog);
            }
            log.info("{} 处理完成", logTag);
        } catch (Exception e) {
            log.error("{} subjectiveQuestionAiCorrecting error: {}", logTag, e.getMessage(), e);
        }
    }

    private String getLogTag(UserAnswer userAnswer) {
        final String logPrefix = "onPaperSubmittedEventAiCorrect-";
        return logPrefix + userAnswer.getId() + "-" + userAnswer.getUid() + "-" + userAnswer.getProductId() + "-"
                + (Objects.isNull(userAnswer.getLessonId()) ? 0 : userAnswer.getLessonId()) + "-"
                + (Objects.isNull(userAnswer.getStudyPathId()) ? 0 : userAnswer.getStudyPathId()) + ":";
    }
    private String getLogTag(UserHomeWorkAnswer userAnswer) {
        final String logPrefix = "onHomeworkSubmittedEventAiCorrect-";
        return logPrefix + userAnswer.getId() + "-" + userAnswer.getUid() + "-" + userAnswer.getProductId() + "-"
                + (Objects.isNull(userAnswer.getObjId()) ? 0 : userAnswer.getObjId()) + "-"
                + (Objects.isNull(userAnswer.getStudyPathId()) ? 0 : userAnswer.getStudyPathId()) + ":";
    }
    /**
     * 判断试卷提交是否需要进行批阅
     * @param userAnswer 用户答题记录
     * @return 是否需要AI批阅, 0 不需要 1 老师批改-在线提交 2 Ai批阅 3 老师批阅-拍照上传
     */
    @Override
    public int isPaperNeedAiCorrecting(UserAnswer userAnswer) {
        // 基础参数校验
        if (userAnswer == null || userAnswer.getGoodsId() == null 
            || userAnswer.getProductId() == null 
            || (userAnswer.getLessonId() == null && userAnswer.getStudyPathId() == null)
            || userAnswer.getPaperId() == null) {
            log.info("paper answer: parameter not enugh, no need to ai correct:{}", Objects.isNull(userAnswer) ? null : userAnswer.getId());
            return 0;
        }
        String logTag = getLogTag(userAnswer);
        // 获取AI批阅配置
        List<GoodsLessonSetting> settings = goodsResource.getGoodsLessonSettingList(
                userAnswer.getGoodsId(), 
                userAnswer.getProductId(),
                GoodsLessonSetting.Type.TYPE_ENABLE_AI_REVIEW.getValue());
        if (CollectionUtils.isEmpty(settings)) {
            return 0;
        }

        // 获取lessonId
        ProductLessonDTO productLesson = productLessonGrpcService.getProductLesson(userAnswer.getProductId());
        Long submitLessonId = ProductTypeEnum.PRODUCT_ADAPTIVE_LEARNING.typeEquals(productLesson.getProduct().getType())
                ? userAnswer.getStudyPathId() : userAnswer.getLessonId();
                
        // 过滤当前lesson的配置，有可能是0不批阅 1老师批阅在线提交 2ai批阅 3老师批阅拍照上传
        settings = settings.stream()
                .filter(x -> Objects.equals(x.getLessonId(), submitLessonId))
                .filter(x->x.parseSettings(Integer.class, 0) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(settings)) {
            return 0;
        }

        if (ProductTypeEnum.PRODUCT_ADAPTIVE_LEARNING.typeEquals(productLesson.getProduct().getType())) {
            //云私塾的核验
            List<StudyPathDTO> studyPathDTOList = goodsResource.getStudyPathV2(userAnswer.getProductId());
            if (CollectionUtils.isEmpty(studyPathDTOList)) {
                log.warn("{} studyPathDTOList is empty", logTag);
                return 0;
            }
            StudyPathDTO studyPathDTO = studyPathDTOList.stream().filter(x -> x.getId().equals(submitLessonId)).findFirst().orElse(null);
            if (studyPathDTO == null || org.apache.commons.collections4.CollectionUtils.isEmpty(studyPathDTO.getStudyPathResourceList())) {
                log.warn("{} studyPathDTO or resourceList is empty", logTag);
                return 0;
            }
            StudyPathResourceDTO resourceDTO = studyPathDTO.getStudyPathResourceList().stream().filter(x -> x.getResourceType().equals(3) && Objects.equals(x.getNewId(), userAnswer.getPaperId())).findFirst().orElse(null);
            if (resourceDTO == null) {
                log.warn("{} studyPathDTO and paper is not match ", logTag);
                return 0;
            }

        } else {
            LessonPaper lessonPaper = null;
            for (Lesson lesson : productLesson.getLessonList()) {
                if (lesson.getId().equals(submitLessonId) && Lesson.Type.TYPE_PAPER.getValue().equals(lesson.getType())) {
                    lessonPaper = (LessonPaper) lesson;
                    break;
                }
            }
            if (lessonPaper == null) {
                log.warn("{} lessonPaper not found", logTag);
                return 0;
            }
            if (lessonPaper.getPaperId() == null || !lessonPaper.getPaperId().equals(userAnswer.getPaperId())) {
                log.warn("{} lessonPaper not match, paperId={}, lessonPaperId={}",
                        logTag, userAnswer.getPaperId(), lessonPaper.getPaperId());
                return 0;
            }
        }

        // 检查是否包含主观题
        List<QuestionGroup> questionGroupList = knowledgeResource.klg_getQuestionByPaperId(userAnswer.getPaperId());
        boolean hasSubjectiveQuestion = false;
        for (QuestionGroup group : questionGroupList) {
            for (var question : group.getQuestionList()) {
                if (isSubjectiveQuestion(question.getQtype())) {
                    hasSubjectiveQuestion = true;
                    break;
                }
            }
            if (hasSubjectiveQuestion) {
                break;
            }
        }
        if (!hasSubjectiveQuestion) {
            return 0;
        }

        // 检查批阅配置和历史记录
        GoodsLessonSetting setting = settings.get(0);
        Integer config = setting.parseSettings(Integer.class, 0);
        if (config == 2) {
            // 每次提交都批阅
            return config;
        }

        // 配置为1或3时检查历史记录
        if (config == 1 || config == 3) {
            UserAnswer query = new UserAnswer();
            query.setUid(userAnswer.getUid());
            query.setGoodsId(userAnswer.getGoodsId());
            query.setPaperId(userAnswer.getPaperId());
            query.setState(3);
            try {
                List<UserAnswer> userAnswerList = userAnswerService.findList(query);
                // 有批阅记录则不再批阅
                if (!CollectionUtils.isEmpty(userAnswerList)) {
                    log.info("{} has ai correcting record, no need to ai correct", logTag);
                    return 0;
                }
            } catch (DataAccessException e) {
                log.error("查询用户答题记录异常", e);
                return 0;
            }
        }

        return config;
    }

    @Override
    public int isHomeworkNeedAiCorrecting(UserHomeWorkAnswer userAnswer) {
        return isHomeworkNeedAiCorrecting(userAnswer, null);
    }
    private int isHomeworkNeedAiCorrecting(UserHomeWorkAnswer userAnswer, List<Long> questionIdList) {
        // 基础参数校验
        if (userAnswer == null || userAnswer.getGoodsId() == null
                || userAnswer.getProductId() == null
                || userAnswer.getHomeworkId() == null
                || (userAnswer.getObjId() == null && userAnswer.getStudyPathId() == null)
        ) {
            log.info("homework answer: parameter not enugh, no need to ai correct:{}", Objects.isNull(userAnswer) ? null : userAnswer.getId());
            return 0;
        }
        String logTag = getLogTag(userAnswer);
        // 获取AI批阅配置
        List<GoodsLessonSetting> settings = goodsResource.getGoodsLessonSettingList(
                userAnswer.getGoodsId(),
                userAnswer.getProductId(),
                GoodsLessonSetting.Type.TYPE_ENABLE_AI_REVIEW.getValue());
        if (CollectionUtils.isEmpty(settings)) {
            return 0;
        }
        ProductLessonDTO productLesson = productLessonGrpcService.getProductLesson(userAnswer.getProductId());
        Long submitLessonId = ProductTypeEnum.PRODUCT_ADAPTIVE_LEARNING.typeEquals(productLesson.getProduct().getType())
                ? userAnswer.getStudyPathId() : userAnswer.getObjId();

        if (CollectionUtils.isNotEmpty(settings)){
            settings = settings.stream().filter(x->Objects.equals(x.getLessonId(), submitLessonId)).collect(Collectors.toList());
        }

        if(CollectionUtils.isEmpty(settings)) {
            // 未配置AI批阅选项
            log.debug("{} not enable ai correct", logTag);
            return 0;
        }

        com.hqwx.goods.entity.resource.Homework homework = null;
        // 增加作业有效性的判断
        if (ProductTypeEnum.PRODUCT_ADAPTIVE_LEARNING.typeEquals(productLesson.getProduct().getType())) {
            // 云私塾的核验
            List<StudyPathDTO> studyPathDTOList = goodsResource.getStudyPathV2(userAnswer.getProductId());
            if (CollectionUtils.isEmpty(studyPathDTOList)) {
                log.warn("{} studyPathDTOList is empty", logTag);
                return 0;
            }
            StudyPathDTO studyPathDTO = studyPathDTOList.stream().filter(x -> x.getId().equals(submitLessonId)).findFirst().orElse(null);
            if (studyPathDTO == null || org.apache.commons.collections4.CollectionUtils.isEmpty(studyPathDTO.getStudyPathResourceList())) {
                log.warn("{} studyPathDTO or resourceList is empty", logTag);
                return 0;
            }
            for(StudyPathResourceDTO studyPathResourceDTO: studyPathDTO.getStudyPathResourceList()){
                if (Objects.equals(studyPathResourceDTO.getNewId(), submitLessonId) && Objects.equals(studyPathResourceDTO.getHomeworkId(), userAnswer.getHomeworkId())){
                    homework = new Homework();
                    homework.setId(userAnswer.getHomeworkId());
                    homework.setQuestionIdList(studyPathResourceDTO.getHomewworkQuestionList().stream().map(x->x.getQuestionId()).collect(Collectors.toList()));
                    break;
                }
            }

        } else {
            for(Lesson lesson : productLesson.getLessonList()) {
                if(lesson.getId().equals(submitLessonId) && Objects.nonNull(lesson.getHomework()) && Objects.equals(lesson.getHomework().getId(), userAnswer.getHomeworkId())){
                    homework = lesson.getHomework();
                    break;
                }
            }
            if(homework == null) {
                log.warn("{} homework not found", logTag);
                return 0;
            }
        }
        if (homework == null || CollectionUtils.isEmpty(homework.getQuestionIdList())) {
            log.warn("{} homework not found or questionIdList is empty", logTag);
            return 0;
        }
        if (Objects.nonNull(questionIdList)) {
            questionIdList.addAll(homework.getQuestionIdList());
        }

        // 检查是否包含主观题
        List<Question> questionList = knowledgeResource.getQuestionByIds(homework.getQuestionIdList());
        boolean hasSubjectiveQuestion = false;
        for (Question question : questionList) {
            if (isSubjectiveQuestion(question.getQtype())) {
                hasSubjectiveQuestion = true;
                break;
            }
        }
        if (!hasSubjectiveQuestion) {
            return 0;
        }

        // 检查批阅配置和历史记录
        GoodsLessonSetting setting = settings.get(0);
        Integer config = setting.parseSettings(Integer.class, 0);
        if (config == 2) {
            // 每次提交都批阅
            return config;
        }

        // 配置为1或3时检查历史记录
        if (config == 1 || config == 3) {
            // 用户的一个作业，只批阅一次
            // 查询用户的这个作业的作答记录
            UserHomeWorkAnswer query = new UserHomeWorkAnswer();
            query.setUid(userAnswer.getUid());
            query.setHomeworkId(userAnswer.getHomeworkId());
            query.setState(3); // 已批阅状态
            List<UserHomeWorkAnswer> userHomeWorkAnswerList = null;
            try {
                userHomeWorkAnswerList = userAnswerService.getUserAnswerHomeworkList(query);
            } catch (DataAccessException e) {
                log.error("{} find userHomeWorkAnswerList error", logTag, e);
            }

            // 有过批阅记录的，就不自动批阅了
            if (!CollectionUtils.isEmpty(userHomeWorkAnswerList)) {
                log.warn("{} has ai correcting record, no need to ai correct", logTag);
                return 0;
            }
        }

        return config;
    }

    @Override
    public boolean isSubjectiveQuestion(int qtype) {
        return subjectiveQTypeSet.contains(qtype);
    }

    @Override
    public void correctSubjectiveResult(Long uid, Long userAnswerId, Integer questionSource) {
        userAnswerSumService.correctSubjectiveResult(uid, userAnswerId, questionSource);
    }
}

