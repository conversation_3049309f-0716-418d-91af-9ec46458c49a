package cn.huanju.edu100.study.service.impl.wxapp;


import cn.huanju.edu100.study.config.WxMaProperties;
import cn.huanju.edu100.study.mapper.wxapp.SolutionWxappAdvertisingPostionMapper;
import cn.huanju.edu100.study.model.wxapp.SolutionWxappAdvertisingPostion;
import cn.huanju.edu100.study.service.wxapp.SolutionWxappAdvertisingPostionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqwx.study.dto.query.wxapp.SolutionWxappAdvertisingPostionQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SolutionWxappAdvertisingPostionServiceImpl  extends ServiceImpl<SolutionWxappAdvertisingPostionMapper, SolutionWxappAdvertisingPostion> implements SolutionWxappAdvertisingPostionService {

    private static Logger logger = LoggerFactory.getLogger(SolutionWxappAdvertisingPostionServiceImpl.class);
    @Resource
    SolutionWxappAdvertisingPostionMapper solutionWxappAdvertisingPostionMapper;

    @Resource
    WxMaProperties wxMaProperties;

    @Override
    public List<SolutionWxappAdvertisingPostion> findByOneType(SolutionWxappAdvertisingPostionQuery query) {
        if (wxMaProperties.getHqAppIdConfigMap().containsKey(query.getHqAppId())) {
            query.setAppId(wxMaProperties.getHqAppIdConfigMap().get(query.getHqAppId()).getAppid());
        } else {
            logger.error("findByOneType error, hqAppId not exist, hqAppId:{}", query.getHqAppId());
            return null;
        }
        return solutionWxappAdvertisingPostionMapper.findByOneType(query);
    }

}
