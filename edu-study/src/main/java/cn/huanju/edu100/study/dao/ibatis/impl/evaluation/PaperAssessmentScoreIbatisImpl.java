package cn.huanju.edu100.study.dao.ibatis.impl.evaluation;


import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.evaluation.PaperAssessmentScoreDao;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.model.evaluation.PaperAssessmentScore;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import java.sql.SQLException;
import java.util.Map;

public class PaperAssessmentScoreIbatisImpl extends CrudIbatisImpl2<PaperAssessmentScore> implements PaperAssessmentScoreDao {

    public PaperAssessmentScoreIbatisImpl() {
        super("PaperAssessmentScore");
    }

    @Override
    public int getAssessmentAnswerCountByPaperId(Long paperId) throws DataAccessException{
        if (paperId == null) {
            String tip = "getAssessmentAnswerCountByPaperId error, paperId is empty";
            logger.error(tip);
            throw new DataAccessException(tip);
        }
        try {
            Map<String, Object> param = Maps.newHashMap();
            param.put("paperId", paperId);
            SqlMapClient sqlMap = super.getSlave();
            Integer ret = (Integer) sqlMap.queryForObject(namespace.concat(".getAssessmentAnswerCountByPaperId"), param);
            if (ret == null) {
                ret = 0;
            }
            return ret;
        } catch (SQLException e) {
            logger.error("getAssessmentAnswerCountByPaperId SQLException. paperId:{}", paperId, e);
            throw new DataAccessException("getAssessmentAnswerCountByPaperId SQLException error");
        }
    }

    @Override
    public int getAssessmentAnswerCountByPaperIdAndRightRate(Long paperId, Double rightRate) throws DataAccessException {
        if (paperId == null || rightRate==null) {
            String tip = "getAssessmentAnswerCountByPaperIdAndRightRate error, paperId or rightRate is empty";
            logger.error(tip);
            throw new DataAccessException(tip);
        }
        try {
            Map<String, Object> param = Maps.newHashMap();
            param.put("paperId", paperId);
            param.put("rightRate", rightRate);
            SqlMapClient sqlMap = super.getSlave();
            Integer ret = (Integer) sqlMap.queryForObject(namespace.concat(".getAssessmentAnswerCountByPaperIdAndRightRate"), param);
            if (ret == null) {
                ret = 0;
            }
            return ret;
        } catch (SQLException e) {
            logger.error("getAssessmentAnswerCountByPaperIdAndRightRate SQLException. paperId:{},rightRate:{}", paperId,rightRate,e);
            throw new DataAccessException("getAssessmentAnswerCountByPaperIdAndRightRate SQLException error");
        }
    }
}
