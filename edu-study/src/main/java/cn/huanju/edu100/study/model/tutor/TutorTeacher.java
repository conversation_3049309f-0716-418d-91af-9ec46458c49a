package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 班主任Entity
 * <AUTHOR>
 * @version 2016-01-12
 */
public class TutorTeacher extends DataEntity<TutorTeacher> {
	
	private static final long serialVersionUID = 1L;
	private String classes;		// 班别
	private Long tuid;		// 老师uid
	private String teachingCourse;		// 教授课程
	private String name;		// 老师名称
	private String phone;		// 老师名称
	private Integer type = 0;		// 类型
	private String ip;		// ip
	
	public TutorTeacher() {
		super();
	}

	public TutorTeacher(Long id){
		super(id);
	}

	public String getClasses() {
		return classes;
	}

	public void setClasses(String classes) {
		this.classes = classes;
	}
	
	public Long getTuid() {
		return tuid;
	}

	public void setTuid(Long tuid) {
		this.tuid = tuid;
	}
	
	public String getTeachingCourse() {
		return teachingCourse;
	}

	public void setTeachingCourse(String teachingCourse) {
		this.teachingCourse = teachingCourse;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
	
}