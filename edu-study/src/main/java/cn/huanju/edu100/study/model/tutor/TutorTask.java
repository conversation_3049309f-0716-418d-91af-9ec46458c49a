package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 个性化任务Entity
 *
 * <AUTHOR>
 * @version 2016-01-14
 */
public class TutorTask extends DataEntity<TutorTask> {

    private static final long serialVersionUID = 1L;
    private String classes; // 班别
    private Long planId; // 计划id
    private Long phaseId; // 阶段id
    private Long unitId; // 单元id
    private Long firstCategory; // first_category
    private Long secondCategory; // second_category
    private Long categoryId; // category_id
    private String title; // 任务标题
    private String number; // 任务编号
    private String remark; // 备注
    private Integer type; // 任务类型：0：录播课程学习，1:直播课程学习，2：自测评，3.练习，4：答疑，5：辅导
    private Long detailId; // 任务详情id
    private Integer isPublic; // 是否是公共任务，0：是，1：不是
    private Date startTime; // 任务开始时间
    private Date endTime; // 任务结束时间
    private Integer state; // '状态 1：已发布 0：未发布，默认值是0, 2：取消
    private Integer hours; // 预计耗时
    private Long sort; // 排序值，从小到大排序
    private String shortTitle; // 短标题
    private String ip; // ip

    public TutorTask() {
        super();
    }

    public TutorTask(Long id) {
        super(id);
    }

    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitile(String title) {
        this.title = title;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public Integer getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Integer isPublic) {
        this.isPublic = isPublic;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getHours() {
        return hours;
    }

    public void setHours(Integer hours) {
        this.hours = hours;
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("TutorTask [classes=");
        builder.append(classes);
        builder.append(", planId=");
        builder.append(planId);
        builder.append(", phaseId=");
        builder.append(phaseId);
        builder.append(", firstCategory=");
        builder.append(firstCategory);
        builder.append(", secondCategory=");
        builder.append(secondCategory);
        builder.append(", categoryId=");
        builder.append(categoryId);
        builder.append(", title=");
        builder.append(title);
        builder.append(", remark=");
        builder.append(remark);
        builder.append(", type=");
        builder.append(type);
        builder.append(", detailId=");
        builder.append(detailId);
        builder.append(", isPublic=");
        builder.append(isPublic);
        builder.append(", startTime=");
        builder.append(startTime);
        builder.append(", endTime=");
        builder.append(endTime);
        builder.append(", state=");
        builder.append(state);
        builder.append(", hours=");
        builder.append(hours);
        builder.append(", sort=");
        builder.append(sort);
        builder.append(", shortTitle=");
        builder.append(shortTitle);
        builder.append(", ip=");
        builder.append(ip);
        builder.append("]");
        return builder.toString();
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

}
