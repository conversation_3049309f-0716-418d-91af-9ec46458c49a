package cn.huanju.edu100.study.service.report;


import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.report.TikuPracticeDuration;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

public interface TikuPracticeDurationService extends BaseService<TikuPracticeDuration> {


    List<TikuPracticeDuration> findPracticeDurationList(Long uid, Long boxId, Long categoryId) throws DataAccessException;
}
