package cn.huanju.edu100.study.dao.qbox;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.questionBox.UserReciteQuestion;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 18/10/29
 */
public interface UserReciteQuestionDao extends CrudDao<UserReciteQuestion> {

    UserReciteQuestion getFromMasterDbByUidQboxIdAndKey(Long uid, Long qboxId, String key) throws DataAccessException;

    UserReciteQuestion getByUidQboxIdAndKey(Long uid, Long qboxId, String key) throws DataAccessException;

    List<UserReciteQuestion> getByUidQboxIdAndKeyLike(Long uid, Long qboxId, String key) throws DataAccessException;

    List<UserReciteQuestion> getByUidAndQboxId(Long uid, Long questionBoxId) throws DataAccessException;

    List<String> getKeysByUidAndQboxId(Long uid, Long qboxId) throws DataAccessException;

    void deleteByUidAndQboxId(Long uid, Long qboxId) throws  DataAccessException;

    void insertBatch(List<UserReciteQuestion> userReciteQuestionList) throws DataAccessException;

    void insertBatchNew(List<UserReciteQuestion> questionBoxId) throws DataAccessException;
}
