/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.onetoone.VMaterialDao;
import cn.huanju.edu100.study.model.onetoone.VMaterial;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 留学资料DAO接口
 * <AUTHOR>
 * @version 2016-12-12
 */
public class VMaterialIbatisImpl extends CrudIbatisImpl2<VMaterial> implements
		VMaterialDao {

	public VMaterialIbatisImpl() {
		super("VMaterial");
	}

    @Override
    public List<VMaterial> findListByParam(Map<String, Object> params) throws DataAccessException {

        if (MapUtils.isEmpty(params)) {
            logger.error("findListByParam fail, empty params map");
            throw new DataAccessException("findListByParam fail, empty params map");
        }

        try {
            Map<String, Object> map = Maps.newHashMap();
            if (null != params.get("clsIds")) {
                map.put("clsIds", params.get("clsIds"));
            }
            if (null != params.get("startTime")) {
                map.put("startTime", params.get("startTime"));
            }
            if (null != params.get("endTime")) {
                map.put("endTime", params.get("endTime"));
            }
            SqlMapClient sqlMap = super.getSlave();
            return sqlMap.queryForList(namespace.concat(".findList"), map);
        } catch (SQLException e) {
            logger.error("findListByParam {} SQLException.entity:{}", namespace, GsonUtil.toJson(params), e);
            throw new DataAccessException("findListByParam SQLException error" + e.getMessage());
        }
    }

    @Override
    public boolean insertBatch(Collection<VMaterial> materials) throws DataAccessException {

        if (CollectionUtils.isEmpty(materials)) {
            logger.error("batchInsert get error, parameter is empty");
            throw new DataAccessException("insertBatch get error, parameter is empty");
        }
        try {
            SqlMapClient sqlMap = super.getMaster();
            sqlMap.insert(namespace.concat(".insertBatch"), materials);
            return true;
        } catch (SQLException e) {
            logger.error("batchInsert SQLException.", e);
            throw new DataAccessException("insertBatch get SQLException error" + e.getMessage());
        } catch (Exception e) {
            logger.error("get SException.", e);
            throw new DataAccessException("insertBatch get Exception error" + e.getMessage());
        }
    }

}
