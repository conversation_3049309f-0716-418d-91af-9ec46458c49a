package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.study.model.dto.CourseScheduleDTO;
import cn.huanju.edu100.study.model.dto.GoodsRelateLessonDTO;
import cn.huanju.edu100.study.model.lesson.LessonForStatsDTO;
import cn.huanju.edu100.study.model.lesson.LessonForStatsQuery;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.ParamUtils;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class GoodsCommonService {
//    private static final String CACHE_NAME = "goodsCommonCache";
    private static final Logger log = LoggerFactory.getLogger(GoodsCommonService.class);

    @Value("${adminapiUrl}")
    private String adminapiUrl;

    @Autowired
    RestTemplate restTemplate;

    public List<GoodsRelateLessonDTO> getLessonsByResid(Long resId, String resType) {
        if (resId == null) {
            log.error("resId is null");
            return null;
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("resId", resId);
        param.put("resType", resType);
        try {
            String response = restTemplate.getForObject(adminapiUrl + "/feign/v1/course-lessons/lessons-by-resid?resId={resId}&resType={resType}", String.class, param);
            Map<String, Object> res = GsonUtil.getGenericGson().fromJson(response, Map.class);
            Integer code = ParamUtils.getInt(res, "code", false);
            String data = GsonUtil.getGenericGson().toJson(res.get("data"));
            if (code == 0 && StringUtils.isNotBlank(data)) {
                Type type = new com.google.common.reflect.TypeToken<List<GoodsRelateLessonDTO>>() {
                }.getType();
                return GsonUtil.getGenericGson().fromJson(data, type);
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("[getLessonsByResid] error param:{}", GsonUtil.toJson(param), e);
        }
        return null;
    }

//    @Cacheable(value = CACHE_NAME, key = "'getCourseScheduleByStageId' + #stageId")
    public CourseScheduleDTO getCourseScheduleByStageId(Long stageId) {
        if (stageId == null) {
            log.error("stageId is null");
            return null;
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("stageId", stageId);
        try {
            String response = restTemplate.getForObject(adminapiUrl + "/feign/v1/course-schedule/getCourseScheduleByStageId?stageId={stageId}", String.class, param);
            Map<String, Object> res = GsonUtil.getGenericGson().fromJson(response, Map.class);
            Integer code = ParamUtils.getInt(res, "code", false);
            String data = GsonUtil.getGenericGson().toJson(res.get("data"));
            if (code == 0 && StringUtils.isNotBlank(data)) {
                Type type = new com.google.common.reflect.TypeToken<CourseScheduleDTO>() {
                }.getType();
                return GsonUtil.getGenericGson().fromJson(data, type);
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("[getCourseScheduleByStageId] error param:{}", GsonUtil.toJson(param), e);
        }
        return null;
    }


    /**
     * @param goodsId 商品id
     * @return 查询课表商品下课节信息
     */
    public List<LessonForStatsDTO> getLessonByGoods(Long goodsId) {

        if (goodsId == null) {
            return null;
        }
        LessonForStatsQuery query = new LessonForStatsQuery();
        query.setHqGoodsId(goodsId);

        try {
            Map<String, Object> res = restTemplate.postForObject(adminapiUrl + "/feign/v1/course-lessons/lesson-by-goodsId",query, Map.class);
//            Map<String, Object> res = GsonUtil.getGenericGson().fromJson(response, Map.class);
//            Integer code = ParamUtils.getInt(res, "code", false);
            String code = res.get("code").toString();
            String data = GsonUtil.getGenericGson().toJson(res.get("data"));
            if ("0" .equals(code) && StringUtils.isNotBlank(data)) {
                Type type = new com.google.common.reflect.TypeToken<List<LessonForStatsDTO>>(){}.getType();
                return GsonUtil.getGenericGson().fromJson(data, type);
            } else {
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("[getLesonByGoods] error param:{}", GsonUtil.toJson(query), e);
        }
        return new ArrayList<>();
    }

    /**
     * @param goodsId 商品id
     * @return 根据条件查询课节列表
     */
    public List<LessonForStatsDTO> getLessons(LessonForStatsQuery lessonForStatsQuery) {

        if (lessonForStatsQuery == null) {
            return null;
        }

        try {
            Map<String, Object> res = restTemplate.postForObject(adminapiUrl + "/feign/v1/course-lessons/search",lessonForStatsQuery, Map.class);
            String code = res.get("code").toString();
            String data = GsonUtil.getGenericGson().toJson(res.get("data"));
            if ("0" .equals(code) && StringUtils.isNotBlank(data)) {
                Type type = new com.google.common.reflect.TypeToken<List<LessonForStatsDTO>>(){}.getType();
                return GsonUtil.getGenericGson().fromJson(data, type);
            } else {
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("[getLessons] error param:{}", GsonUtil.toJson(lessonForStatsQuery), e);
        }
        return new ArrayList<>();
    }

}
