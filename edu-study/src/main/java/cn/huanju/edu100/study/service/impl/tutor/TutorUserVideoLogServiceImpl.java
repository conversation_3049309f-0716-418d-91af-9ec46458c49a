package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.tutor.TutorUserVideoLogDao;
import cn.huanju.edu100.study.model.goods.Lesson;
import cn.huanju.edu100.study.model.goods.LessonVideo;
import cn.huanju.edu100.study.model.tutor.TutorUserVideoLog;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.service.tutor.TutorUserVideoLogService;
import cn.huanju.edu100.study.util.Constants;
import cn.huanju.edu100.util.DateUtils;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 学员观看记录Service
 * <AUTHOR>
 * @version 2016-01-20
 */
@Service
public class TutorUserVideoLogServiceImpl extends BaseServiceImpl<TutorUserVideoLogDao, TutorUserVideoLog> implements TutorUserVideoLogService {

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    @Autowired
    private GoodsResource goodsResource;

    private static String TUTOR_USER_VIDEO_LOG = "hq_tutor_uvl_";

    private static String LESSON_VIDEO_DURATION_KEY = "hq_lesson_video_duration_";

    private static Logger logger = LoggerFactory.getLogger(TutorUserVideoLogServiceImpl.class);

    private int EXPIRE_TIME = 60 * 60 * 25;		// 25小时

    @Override
    public TutorUserVideoLog getFromCache(Long uid, Long courseId, Long clsId, Long lessonId) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(TUTOR_USER_VIDEO_LOG);
        stringBuilder.append(uid).append("_").append(null == courseId ? -1 : courseId).append("_").append(null == clsId ? -1 : clsId).append("_").append(null == lessonId ? -1 : lessonId);
        String strValue = compatableRedisClusterClient.get(stringBuilder.toString());
        if(StringUtils.isBlank(strValue)){
            return null;
        }

        TutorUserVideoLog log = null;
        try {
            log = TutorUserVideoLog.parseFromStr(strValue);
        }catch(Exception e){
            logger.error("query from cache error :{}",strValue, e);
        }
        if(null == log){
            logger.error("parse tutoruservideolog null:{}", strValue);
        }
        return log;
    }

    @Override
    public boolean addTutorUserVideoLog(TutorUserVideoLog tutorUserVideoLog) throws DataAccessException {
        if(null == tutorUserVideoLog){
            return false;
        }
        tutorUserVideoLog.setUpdateDate(new Date());
        String key = null;
        String uidField = null;
        try {
            uidField = tutorUserVideoLog.getKey();
            key = TUTOR_USER_VIDEO_LOG + uidField;
            compatableRedisClusterClient.setex(key, EXPIRE_TIME, tutorUserVideoLog.getFormatStr());
            compatableRedisClusterClient.expire(key, EXPIRE_TIME);
            compatableRedisClusterClient.sadd(Constants.TUTOR_USER_VIDEO_LOG_KeyHqUserLog, uidField);

            String recordKey = "tutor_data_update_" + uidField;
            compatableRedisClusterClient.setex(recordKey, EXPIRE_TIME, String.valueOf(tutorUserVideoLog.getUpdateDate().getTime()));	//记录数据更新时间
            compatableRedisClusterClient.expire(recordKey, EXPIRE_TIME);
        }catch(Exception e){
            logger.error("addTutorUserVideoLog error", e);
            return false;
        }
        return true;
    }

    @Override
    public boolean saveStudyVideoResult(TutorUserVideoLog tutorUserVideoLog) throws DataAccessException {

        if (tutorUserVideoLog == null || tutorUserVideoLog.getUid() == null
                || tutorUserVideoLog.getLessonId() == null) {
            return false;
        }

        TutorUserVideoLog tUserVideoLog = new TutorUserVideoLog();
        tUserVideoLog.setLessonId(tutorUserVideoLog.getLessonId());
        tUserVideoLog.setUid(tutorUserVideoLog.getUid());
        List<TutorUserVideoLog> tutorUserVideoLogList = dao.findList(tUserVideoLog);
        if (CollectionUtils.isEmpty(tutorUserVideoLogList)) {
            return dao.insertTutorUserVideoLog(tutorUserVideoLog) > 0;
        }else {
            return dao.updateTutorUserVideoLog(tutorUserVideoLog);
        }

    }

    @Override
    public List<TutorUserVideoLog> getTutorUserVideoLog(Map<String, Object> params) throws DataAccessException {
        List<TutorUserVideoLog> tutorUserVideoLogs = dao.getTutorUserVideoLog(params);
        if(CollectionUtils.isEmpty(tutorUserVideoLogs)){
            return Collections.EMPTY_LIST;
        }
        TutorUserVideoLog cachedTutorUserVideoLog = null;
        Date nowTm = new Date();
        for (TutorUserVideoLog tutorUserVideoLog : tutorUserVideoLogs) {
            if(null != tutorUserVideoLog.getLastTime() && DateUtils.diffSecond(nowTm, tutorUserVideoLog.getLastTime()) >= EXPIRE_TIME){ //缓存肯定没有，则不查缓存
                continue;
            }
            cachedTutorUserVideoLog = this.getFromCache(tutorUserVideoLog.getUid(), tutorUserVideoLog.getCourseId(),tutorUserVideoLog.getClsId(), tutorUserVideoLog.getLessonId());
            if(null != cachedTutorUserVideoLog){
                tutorUserVideoLog.setPosition(cachedTutorUserVideoLog.getPosition());
                tutorUserVideoLog.setStatus(cachedTutorUserVideoLog.getStatus());
                tutorUserVideoLog.setResult(cachedTutorUserVideoLog.getResult());
                tutorUserVideoLog.setLastTime(cachedTutorUserVideoLog.getLastTime());
            }
        }
        return tutorUserVideoLogs;
    }

    @Override
    public List<TutorUserVideoLog> findByTaskListAndUid(String classes, Long uid, List<Long> taskIdList) throws DataAccessException {
        return dao.findByTaskListAndUid(classes,uid,taskIdList);
    }

    @Override
    public Map<Long, Integer> getDurationMap(List<Long> lessonIdList) throws DataAccessException{
        if (CollectionUtils.isEmpty(lessonIdList)){
            return null;
        }
        Map<Long,Integer> durationMap = Maps.newHashMap();
        List<Long> needLessonIdList = Lists.newArrayList();
        for (Long lessonId : lessonIdList) {
            String duration = compatableRedisClusterClient.get(LESSON_VIDEO_DURATION_KEY + lessonId);
            if (StringUtils.isNotBlank(duration)) {
                durationMap.put(lessonId,Integer.parseInt(duration));
            } else {
                needLessonIdList.add(lessonId);
            }
        }
        if (CollectionUtils.isNotEmpty(needLessonIdList)) {
            Map<Long, LessonVideo> lessonVideoMap = goodsResource.getLessonVideosByIdList(needLessonIdList);

            if (lessonVideoMap != null && lessonVideoMap.size() > 0) {

                List<Long> resIdList = Lists.newArrayList();
                for (LessonVideo lessonVideo : lessonVideoMap.values()) {
                    resIdList.add(lessonVideo.getResId());
                }
                Map<Long, Lesson> resourceMap = goodsResource.getLessonsByIdList(resIdList);

                if (resourceMap != null && resourceMap.size() > 0) {
                    for (LessonVideo lessonVideo : lessonVideoMap.values()) {
                        Long resId = lessonVideo.getResId();
                        Lesson resource = resourceMap.get(resId);
                        if (null != resource) {
                            durationMap.put(lessonVideo.getId(), Integer.parseInt(resource.getLength()));
                            compatableRedisClusterClient.setex(LESSON_VIDEO_DURATION_KEY + lessonVideo.getId(), EXPIRE_TIME, resource.getLength());
                        }
                    }
                }
            }
        }
        return durationMap;
    }

}
