/*
 * Copyright (c) 2011 duowan.com.
 * All Rights Reserved.
 * This program is the confidential and proprietary information of
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.BaseEntity;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.model.CTaskResult;
import cn.huanju.edu100.study.model.LessonKnowledge;
import cn.huanju.edu100.study.model.Teacher;
import cn.huanju.edu100.study.model.al.ProductAdaptiveLearning;
import cn.huanju.edu100.study.model.al.StudyPath;
import cn.huanju.edu100.study.model.dto.GoodsRelateLessonDTO;
import cn.huanju.edu100.study.model.goods.*;
import cn.huanju.edu100.study.model.homework.Homework;
import cn.huanju.edu100.study.resource.impl.core.ThriftReturnCode;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.EduGsonUtils;
import cn.huanju.edu100.study.util.GsonUtils;
import cn.huanju.edu100.thrift.edu100_goods;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.IdUtils;
import cn.huanju.edu100.util.JSONUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hqwx.goods.client.DataConfigService;
import com.hqwx.goods.client.ProductLessonService;
import com.hqwx.goods.dto.EPaperDTO;
import com.hqwx.goods.dto.HomeWorkSyncDataDTO;
import com.hqwx.goods.dto.HomeworkDTO;
import com.hqwx.goods.dto.ProductExternalLesson;
import com.hqwx.goods.dto.al.StudyPathDTO;
import com.hqwx.goods.entity.GoodsLessonSetting;
import com.hqwx.goods.param.EPaperLessonQueryParam;
import com.hqwx.goods.param.al.StudyPathParam;
import com.hqwx.thrift.client.api.HqwxGoodsThriftClient;
import com.hqwx.thrift.client.base.ThriftRequest;
import com.hqwx.thrift.client.base.ThriftResponse;
import com.hqwx.thrift.client.thrift.ThriftClientWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@Component
public class GoodsResource {

    public static final int APP_ID = 7;
    private static final String CACHE_NAME = "goodsCache";
    @Autowired
    private IGenericThriftResource genericThriftResource;
    @Autowired
    private EhCacheCacheManager cacheManager;

    @javax.annotation.Resource
    private HqwxGoodsThriftClient goodsThriftClient;

    @Resource
    private DataConfigService dataConfigService;

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    @Autowired
    private ProductLessonService productLessonGrpcService;

    private static final Logger LOG = LoggerFactory.getLogger(GoodsResource.class);

    public List<LessonKnowledge> getLessonKnowledgeByknowledgeIds(List<Long> ids) {

        if (ids == null || ids.size() == 0) {
            return Collections.emptyList();
        }
        String param = GsonUtil.toJson(ids);
        try {

            LOG.info("[getLessonKnowledgeByknowledgeIds] start, param is:{}", param);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getLessonKnowledgeByknowledgeIds");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<LessonKnowledge>>() {
                }.getType();
                List<LessonKnowledge> lessonKnowledgeList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return lessonKnowledgeList;
            }

        } catch (Exception e) {
            LOG.error("[getLessonKnowledgeByknowledgeIds] error param:{}", param, e);
        }
        return Collections.emptyList();
    }


    public Map<Long, LessonVideo> getLessonVideosByIdList(List<Long> ids) {
        Map<Long, LessonVideo> videoMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ids)) {
            return videoMap;
        }
        if (ids.size() > Constants.MAX_REQUEST){
            List<Long> subList = Lists.newArrayList();
            for (Long id : ids) {
                subList.add(id);
                if (subList.size() == Constants.MAX_REQUEST){
                    videoMap.putAll(getLessonVideosByIdList(subList));
                    subList.clear();
                }
            }
            if (subList.size() > 0){
                videoMap.putAll(getLessonVideosByIdList(subList));
            }
        } else {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("idList", ids);
            String param = GsonUtil.toJson(paramMap);
            try {
                LOG.info("[gds_getLessonVideosByIdList] start, param is:{}", param);
                response response = genericThriftResource.generalGoodsThriftMethodInvoke(param, APP_ID,
                        Consts.Code.CLIENT_IP, "gds_getLessonVideosByIdList");
                if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                    java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<LessonVideo>>() {
                    }.getType();
                    List<LessonVideo> lessonVideoList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                    if (CollectionUtils.isNotEmpty(lessonVideoList)) {

                        for (LessonVideo lessonVideo : lessonVideoList) {
                            videoMap.put(lessonVideo.getId(), lessonVideo);
                        }
                        return videoMap;
                    }
                }
            } catch (Exception e) {
                LOG.error("[gds_getLessonVideosByIdList] error param:{}", param, e);
            }
        }
        return videoMap;
    }
    /**
     * @param productId 产品id
     * @return 查询产品信息
     */
    @Cacheable(value = CACHE_NAME, key = "'GoodsResource.getProductById' + #productId")
    public Product getProductById(Long productId) {

        if (productId == null) {
            return null;
        }

        List<Long> productIdList = Lists.newArrayList();
        productIdList.add(productId);

        try {
            LOG.info("[getProductById] start, param :{}", GsonUtil.toJson(productIdList));
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGenericGson().toJson(productIdList), APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getProductsByIdList");
//            LOG.info("[getProductById] response:{}", response.getMsg());
            if (response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Product[] products = GsonUtils.fromJson(response.getMsg(), Product[].class);
                if (null != products && products.length > 0) {
                    return products[0];
                }
            }
        } catch (Exception e) {
            LOG.error("[getProductById] error param:{}", GsonUtil.toJson(productIdList), e);
        }
        return null;
    }


    /**
     * @param idList 产品id列表
     * @return 查询产品信息
     */
    public List<Product> getProductListByIdList(List<Long> idList) {
        LOG.info("[getProductListByIdList] start, param :{}", GsonUtil.toJson(idList));
        String cachePrefix = "GoodsResource.getProductById";
        List<Long> dbIdList = Lists.newArrayList();
        List<Product> results = getFromCacheByIdList(idList, cachePrefix, dbIdList);
        if (CollectionUtils.isNotEmpty(dbIdList)) {
            String parameterString = GsonUtil.getGenericGson().toJson(dbIdList);
            Type classType = new com.google.gson.reflect.TypeToken<List<Product>>() {
            }.getType();
            List<Product> dbResults = getFromDbByIdList(parameterString, classType, "gds_getProductsByIdList");
            if (CollectionUtils.isNotEmpty(dbResults)) {
                results.addAll(dbResults);
                setToCache(dbResults, cachePrefix);

            }
        }

        return results;
    }

    public Map<Long, Product> getProductsByIdList(List<Long> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<Product> productList = this.getProductListByIdList(ids);
        if (CollectionUtils.isNotEmpty(productList)) {
            Map<Long, Product> productMap = Maps.newHashMap();
            for (Product product : productList) {
                productMap.put(product.getId(), product);
            }
            return productMap;
        }

        return Collections.emptyMap();
    }
    @Cacheable(value = CACHE_NAME, key = "'GoodsResource.getGoodsById' + #goodsId")
    public Goods getGoodsById(Long goodsId) {
        if (goodsId == null) {
            return null;
        }

        List<Long> goodsIdList = Lists.newArrayList();
        goodsIdList.add(goodsId);

        try {
            LOG.info("[getGoodsById] start, param :{}", GsonUtil.toJson(goodsIdList));
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGenericGson().toJson(goodsIdList), APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getGoodsByIdList");
            if (response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Goods[] goodsArray = GsonUtils.fromJson(response.getMsg(), Goods[].class);
                if (null != goodsArray && goodsArray.length > 0) {
                    return goodsArray[0];
                }
            }
        } catch (Exception e) {
            LOG.error("[getGoodsById] error param:{}", GsonUtil.toJson(goodsIdList), e);
        }
        return null;
    }
    public List<Goods> getGoodsListByIdList(List<Long> idList) {
        LOG.info("[getGoodsListByIdList] start, param :{}", GsonUtil.toJson(idList));
        String cachePrefix = "GoodsResource.getGoodsById";
        List<Long> dbIdList = Lists.newArrayList();
        List<Goods> results = getFromCacheByIdList(idList, cachePrefix, dbIdList);

        if (CollectionUtils.isNotEmpty(dbIdList)) {
            String parameterString = GsonUtil.getGenericGson().toJson(dbIdList);
            Type classType = new com.google.gson.reflect.TypeToken<List<Goods>>() {
            }.getType();
            List<Goods> dbResults = getFromDbByIdList(parameterString, classType, "gds_getGoodsByIdList");
            if (CollectionUtils.isNotEmpty(dbResults)) {
                results.addAll(dbResults);
                setToCache(dbResults, cachePrefix);
            }
        }

        return results;
    }

    public Map<Long, Goods> getGoodsByIdList(List<Long> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<Goods> goodsList = this.getGoodsListByIdList(ids);
        if (CollectionUtils.isNotEmpty(goodsList)) {
            Map<Long, Goods> goodsMap = Maps.newHashMap();
            for (Goods goods : goodsList) {
                goodsMap.put(goods.getId(), goods);
            }
            return goodsMap;
        }

        return Collections.emptyMap();
    }

    public Map<Long, Lesson> getLessonsByIdList(List<Long> ids) {
        Map<Long, Lesson> lessonMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ids)) {
            return lessonMap;
        }
        if (ids.size() > Constants.MAX_REQUEST){
            List<Long> subList = Lists.newArrayList();
            for (Long id : ids) {
                subList.add(id);
                if (subList.size() == Constants.MAX_REQUEST){
                    lessonMap.putAll(getLessonsByIdList(subList));
                    subList.clear();
                }
            }
            if (subList.size() > 0){
                lessonMap.putAll(getLessonsByIdList(subList));
            }
        } else {
            String param = GsonUtil.toJson(ids);
            try {
                LOG.info("[gds_listLessonsByIdList] start, param is:{}", param);
                response response = genericThriftResource.generalGoodsThriftMethodInvoke(param, APP_ID,
                        Consts.Code.CLIENT_IP, "gds_listLessonsByIdList");
                if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                    java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Lesson>>() {
                    }.getType();
                    List<Lesson> lessonList = GsonUtil.getGson().fromJson(response.getMsg(), type);
                    if (CollectionUtils.isNotEmpty(lessonList)) {
                        for (Lesson lesson : lessonList) {
                            lessonMap.put(lesson.getId(), lesson);
                        }
                        return lessonMap;
                    }
                }

            } catch (Exception e) {
                LOG.error("[gds_listLessonsByIdList] error param:{}", param, e);
            }
        }
        return lessonMap;
    }

    public Map<Long, GoodsGroup> getGoodGroupsByIdList(List<Long> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        String param = GsonUtil.toJson(ids);
        try {

            LOG.info("[gds_getGoodsGroupsByIdList] start, param is:{}", param);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getGoodsGroupsByIdList");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<GoodsGroup>>() {
                }.getType();
                List<GoodsGroup> goodsGroups = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if (CollectionUtils.isNotEmpty(goodsGroups)) {
                    Map<Long, GoodsGroup> goodsGroupMap = Maps.newHashMap();
                    for (GoodsGroup goodsGroup : goodsGroups) {
                        goodsGroupMap.put(goodsGroup.getId(), goodsGroup);
                    }
                    return goodsGroupMap;
                }
            }

        } catch (Exception e) {
            LOG.error("[gds_getGoodsGroupsByIdList] error param:{}", param, e);
        }
        return Collections.emptyMap();
    }


    public void saveCTaskResult(CTaskResult cTaskResult) {

        if (cTaskResult == null) {
            return;
        }

        try {
            LOG.info("[gds_saveTaskResult] start, param is:{}", GsonUtil.toJson(cTaskResult));
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGenericGson().toJson(cTaskResult), APP_ID,
                    Consts.Code.CLIENT_IP, "gds_saveTaskResult");
            LOG.info("[gds_saveTaskResult] response:{}", response.getMsg());
        } catch (Exception e) {
            LOG.error("[gds_saveTaskResult] error param:{}", GsonUtil.toJson(cTaskResult), e);
        }
    }


    public Map<Long, Goods> gds_getGoodsByGroupId(Long groupId) {

        if (null == groupId || 0 == groupId) {
            return Collections.emptyMap();
        }
        Map<String,Object> map = Maps.newHashMap();
        map.put("group_id",groupId);
        String param = GsonUtil.toJson(map);
        try {
            LOG.info("[gds_getGoodsByGroupId] start, param is:{}", param);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getGoodsByGroupId");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Goods>>() {
                }.getType();
                List<Goods> goodsList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    Map<Long, Goods> goodsMap = Maps.newHashMap();
                    for (Goods goods : goodsList) {
                        goodsMap.put(goods.getId(), goods);
                    }
                    return goodsMap;
                }
            }
        } catch (Exception e) {
            LOG.error("[gds_getGoodsGroupsByIdList] error param:{}", param, e);
        }
        return Collections.emptyMap();
    }









    public Map<Long, Goods> gds_getReviewGoodsByGroupId(Long groupId) {

        if (null == groupId || 0 == groupId) {
            return Collections.emptyMap();
        }
        Map<String,Object> map = Maps.newHashMap();
        map.put("group_id",groupId);
        String param = GsonUtil.toJson(map);
        try {
            LOG.info("[gds_getGoodsByGroupId] start, param is:{}", param);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getReviewGoodsByGroupId");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Goods>>() {
                }.getType();
                List<Goods> goodsList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    Map<Long, Goods> goodsMap = Maps.newHashMap();
                    for (Goods goods : goodsList) {
                        goodsMap.put(goods.getId(), goods);
                    }
                    return goodsMap;
                }
            }
        } catch (Exception e) {
            LOG.error("[gds_getGoodsGroupsByIdList] error param:{}", param, e);
        }
        return Collections.emptyMap();
    }

    /**
     * @description 根据产品Id查询商品信息。限制一次50条
     * <AUTHOR>
     * @time 2019年1月11日 下午4:33:10
     * */
    public List<Goods> getGoodsListByProductId(Long productId) {
        if (null==productId ||  0==productId) {
            return null;
        }
        Map<String,Object> map = Maps.newHashMap();
        map.put("productId",productId);
        String param = GsonUtil.toJson(map);
        try {
            LOG.info("[gds_getGoodsListByProductId] start, param is:{}", param);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getGoodsListByProductId");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Goods>>() {
                }.getType();
                List<Goods> goodsList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return goodsList;
            }
        } catch (Exception e) {
            LOG.error("[gds_getGoodsListByProductId] error param:{}", param, e);
        }
        return null;
    }

    /**
     *
     * @return 查询学习路径信息
     */

    public List<StudyPath> getStudyPath(Long productId,Long categoryId, List<Long> resourceIdList, Integer resourceType) {
        if (Objects.isNull(productId) || categoryId == null || CollectionUtils.isEmpty(resourceIdList) || resourceType == null) {
            LOG.error("getStudyPath param error, categoryId or resourceIdList or resourceType is empty");
            return null;
        }
        //先查缓存
        List<StudyPath> results = Lists.newArrayList();
        List<Long> dbIdList = Lists.newArrayList();
        String cachePrefix = "GoodsResource.getStudyPath";
        Cache cache = cacheManager.getCache("resourceCache");
        for (Long id : resourceIdList){
            String key = cachePrefix+categoryId+"_"+resourceType+"_"+id;
            if (null == cache.get(key)) {
                dbIdList.add(id);
            } else {
                results.add((StudyPath) cache.get(key).get());
            }
        }
        if (CollectionUtils.isEmpty(dbIdList)) {
            return results;
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("categoryId", categoryId);
        param.put("resourceIdList", dbIdList);
        param.put("resourceType", resourceType);
        param.put("productId", productId);
        LOG.info("[getStudyPathByCategoryAndResource] start, param :{}", GsonUtil.toJson(param));

        try {
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGenericGson().toJson(param), APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getAlStudyPath");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                Type type = new com.google.gson.reflect.TypeToken<List<StudyPath>>(){}.getType();
                List<StudyPath> studyPathList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                results.addAll(studyPathList);
            }
        } catch (DataAccessException e) {
            LOG.error("[gds_getAlStudyPath] error param:{}", param, e);
        }
        return results;
    }


    private <T extends BaseEntity> List<T> getFromCacheByIdList(Collection<Long> idList, String cachePrefix,
                                                                List<Long> dbIdList) {
        if (idList == null || idList.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        List<T> results = Lists.newArrayList();
        Cache cache = cacheManager.getCache(CACHE_NAME);
        for (Long id : idList) {
            String key = cachePrefix + id;
            if (null == cache.get(key)) {
                dbIdList.add(id);
            } else {
                results.add((T) cache.get(key).get());
            }
        }
        return results;
    }

    private <T extends BaseEntity> List<T> getFromDbByIdList(String parameterString, Type classType, String methodName) {
        try {
            LOG.info("[" + methodName + "] start, param :{}", parameterString);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(parameterString, APP_ID,
                    Consts.Code.CLIENT_IP, methodName);
            if (response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                List<T> dbResults = GsonUtil.getGenericGson().fromJson(response.getMsg(), classType);
                return dbResults;
            }
        } catch (Exception e) {
            LOG.error("[" + methodName + "] error param:{}", parameterString, e);
        }
        return Collections.EMPTY_LIST;
    }

    private <T extends BaseEntity> List<T> getUnderLineFromDbByIdList(String parameterString, Type classType, String methodName) {
        try {
            LOG.info("[" + methodName + "] start, param :{}", parameterString);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(parameterString, APP_ID,
                    Consts.Code.CLIENT_IP, methodName);
            if (response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                List<T> dbResults = GsonUtil.getGson().fromJson(response.getMsg(), classType);
                return dbResults;
            }
        } catch (Exception e) {
            LOG.error("[" + methodName + "] error param:{}", parameterString, e);
        }
        return Collections.EMPTY_LIST;
    }

    private <T extends BaseEntity> void setToCache(List<T> dbObjectList, String cachePrefix) {
        if (CollectionUtils.isNotEmpty(dbObjectList)) {
            Cache cache = cacheManager.getCache(CACHE_NAME);
            for (T dbResult : dbObjectList) {
                cache.put(cachePrefix + dbResult.getId(), dbResult);
            }
        }
    }

    public HomeWorkSyncDataDTO getHomeWorkSyncDataInLessonQuestion(Long startTime) {
        try {
            Map<String, Object> param = Maps.newHashMap();
            param.put("startTime", startTime);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGson().toJson(param), APP_ID, Consts.Code.CLIENT_IP, "gds_getHomeWorkSyncDataInLessonQuestion");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<HomeWorkSyncDataDTO>() {
                }.getType();
                HomeWorkSyncDataDTO rs = GsonUtil.getGson().fromJson(response.getMsg(), type);
                return rs;
            }
        } catch (Exception e) {
            LOG.error("getHomeWorkSyncDataInLessonQuestion error e:{}", e);
        }
        return null;
    }

    public List<LessonQuestion> getLessonQuestionByIdList(List<Long> idList) {
        try {
            if (CollectionUtils.isEmpty(idList)) {
                LOG.error("getLessonQuestionByIdList idList is null. ");
                return null;
            }
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.toJson(idList), APP_ID, Consts.Code.CLIENT_IP, "gds_getLessonQuestionByLessonIdList");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<LessonQuestion>>() {
                }.getType();
                List<LessonQuestion> lessonQuestionList = GsonUtil.getGson().fromJson(response.getMsg(), type);
                return lessonQuestionList;
            }
        } catch (Exception e) {
            LOG.error("getLessonQuestionByIdList error e:{}", e);
        }
        return null;
    }

    public List<LessonVideo> getLessonVideoListByResId(Long resId) {
        try {
            if (resId == null) {
                LOG.error("getLessonVideoListByResId resId is null. ");
                return null;
            }
            Map<String, Object> param = Maps.newHashMap();
            param.put("resId", resId);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.toJson(param), APP_ID, Consts.Code.CLIENT_IP, "gds_getLessonVideoListByResId");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<LessonVideo>>() {
                }.getType();
                List<LessonVideo> list = GsonUtil.getGson().fromJson(response.getMsg(), type);
                return list;
            }
        } catch (Exception e) {
            LOG.error("getLessonVideoListByResId error e:{}", e);
        }
        return null;
    }

    public HomeWorkSyncDataDTO getHomeWorkSyncDataInStudyPathQuestion(Long startTime) {
        try {
            Map<String, Object> param = Maps.newHashMap();
            param.put("startTime", startTime);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGson().toJson(param), APP_ID, Consts.Code.CLIENT_IP, "gds_getHomeWorkSyncDataInStudyPathQuestion");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<HomeWorkSyncDataDTO>() {
                }.getType();
                return GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
            }
        } catch (Exception e) {
            LOG.error("getHomeWorkSyncDataInStudyPathQuestion error e:{}", e);
        }
        return null;
    }

    public List<StudyPath> getStudyPathByIdList(List<Long> idList) {
        try {
            if (CollectionUtils.isEmpty(idList)) {
                LOG.error("getStudyPathByIdList resId is null. ");
                return null;
            }
            Map<String, Object> param = Maps.newHashMap();
            param.put("idList", idList);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.toJson(param), APP_ID, Consts.Code.CLIENT_IP, "gds_getStudyPathByIdList");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<StudyPath>>() {
                }.getType();
                List<StudyPath> list = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return list;
            }
        } catch (Exception e) {
            LOG.error("getStudyPathByIdList error e:{}", e);
        }
        return null;
    }

    public Long saveHomework(HomeworkDTO homeworkDTO) {
        try {
            if (homeworkDTO.getObjId() == null || homeworkDTO.getProductType() == null) {
                LOG.error("saveHomework objId or productType is null. ");
                return null;
            }
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGenericGson().toJson(homeworkDTO), APP_ID, Consts.Code.CLIENT_IP, "gds_saveHomework");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                if (StringUtils.isNotBlank(response.getMsg())) {
                    return Long.parseLong(response.getMsg());
                }
            }
        } catch (Exception e) {
            LOG.error("saveHomework error e:{}", e);
        }
        return null;
    }

    public Homework getHomeworkByObjInfo(Long objId, Integer productType){
        try {
            if (objId==null || objId<=0 || productType==null || productType<0) {
                LOG.error("getHomeworkByObjInfo objId or productType is null. ");
                return null;
            }
            Map<String, Object> param = Maps.newHashMap();
            param.put("objId", objId);
            param.put("productType",productType);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.toJson(param), APP_ID, Consts.Code.CLIENT_IP, "gds_getHomeworkByObjInfo");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Homework>>() {
                }.getType();
                List<Homework> homeworkList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if(CollectionUtils.isNotEmpty(homeworkList)){
                    return homeworkList.get(0);
                }
            }
        } catch (Exception e) {
            LOG.error("getHomeworkByObjInfo error e:{}", e);
        }
        return null;
    }

    public Homework getHomeworkByObjAndTypeInfo(Long objId, Integer productType,Integer homeworkType){
        try {
            if (objId==null || objId<=0 || productType==null || productType<0) {
                LOG.error("getHomeworkByObjInfo objId or productType is null. ");
                return null;
            }
            Map<String, Object> param = Maps.newHashMap();
            param.put("objId", objId);
            param.put("productType",productType);
            param.put("homeworkType",homeworkType);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.toJson(param), APP_ID, Consts.Code.CLIENT_IP, "gds_getHomeworkByObjInfo");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Homework>>() {
                }.getType();
                List<Homework> homeworkList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if(CollectionUtils.isNotEmpty(homeworkList)){
                    return homeworkList.get(0);
                }
            }
        } catch (Exception e) {
            LOG.error("getHomeworkByObjInfo error e:{}", e);
        }
        return null;
    }

    public Homework getHomeworkById(Long id){
        if (!IdUtils.isValid(id)) {
            LOG.error("getHomeworkById id is null. ");
            return null;
        }
        Homework result = null;
        ThriftRequest<Long> request = ThriftClientRequestReady.creatThriftRequest(id);
        try {
            ThriftResponse<HomeworkDTO> response = goodsThriftClient.findHomeworkById(request);
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                HomeworkDTO dto = response.getMsg();
                if (dto != null){
                    result = new Homework();
                    BeanUtils.copyProperties(dto, result);
                }
            }
        } catch (Exception e) {
            LOG.error("[findHomeworkById] error param:{}", GsonUtil.toJson(id), e);
        }
        return result;
    }

    public ProductExternalLesson getProductExternalLessonById(Long id) {
        try {
            if (id == null || id <= 0) {
                LOG.error("getProductExternalLessonById id is null. ");
                return null;
            }
            Map<String, Object> param = Maps.newHashMap();
            param.put("id", id);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.toJson(param), APP_ID, Consts.Code.CLIENT_IP, "gds_getProductExternalLessonById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<ProductExternalLesson>() {
                }.getType();
                ProductExternalLesson productExternalLesson = GsonUtil.getGson().fromJson(response.getMsg(), type);
                return productExternalLesson;
            }
        } catch (Exception e) {
            LOG.error("getProductExternalLessonById error e:{}", e);
        }
        return null;
    }

    public EPaperDTO getEPaperById(Long productId, Long paperId, Long lessonId) {
        if (productId == null) {
            LOG.error("getEPaperById productId is null");
            return null;
        }
        try {
            EPaperLessonQueryParam ePaperLessonQueryParam = new EPaperLessonQueryParam();
            ePaperLessonQueryParam.setProductId(productId);
            ePaperLessonQueryParam.setPaperId(paperId);
            ePaperLessonQueryParam.setLessonId(lessonId);
            String paramString = GsonUtil.getGenericGson().toJson(ePaperLessonQueryParam);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(paramString, APP_ID, Consts.Code.CLIENT_IP, "gds_getEPaperById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<EPaperDTO>() {
                }.getType();
                EPaperDTO result = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return result;
            }
        } catch (Exception e) {
            LOG.error("getEPaperById error e:{}", e);
        }
        return null;
    }


    public List<Product> getProductByGoodIdAndProdType(Long goodsId, Integer prodType) {
        List<Product> alProductList = this.getAllProductsByGoodId(goodsId);
        return alProductList.stream().filter(p->p.getType()==prodType).collect(Collectors.toList());
    }

    /**
     * @return 查询学习路径信息
     */
    @Deprecated
    public List<StudyPath> getStudyPathForProduct(Long categoryId, Long productId) {
        if (productId == null) {
            LOG.error("getStudyPathForProduct param error,  productId is empty");
            return Lists.newArrayList();
        }
        List<StudyPath> results = Lists.newArrayList();
        Map<String, Object> param = Maps.newHashMap();
        param.put("categoryId", categoryId);
        param.put("productId", productId);
        param.put("resourceType", 0);
        LOG.info("[gds_getAlStudyPath] start, param :{}", GsonUtil.toJson(param));
        try {
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGenericGson().toJson(param), APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getAlStudyPath");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                Type type = new com.google.gson.reflect.TypeToken<List<StudyPath>>() {
                }.getType();
                List<StudyPath> studyPathList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                results.addAll(studyPathList);
                results = results.stream().filter(path -> Objects.nonNull(path.getStatus()) && path.getStatus().equals(1)).collect(Collectors.toList());
            }
        } catch (DataAccessException e) {
            LOG.error("[gds_getAlStudyPath] error param:{}", param, e);
        }
        return results;
    }

    /**
     * @param goodsId 商品id
     * @return 查询产品信息
     */
    @Cacheable(value = CACHE_NAME, key = "'GoodsResource.getAllProductListByGoodId' + #goodsId")
    public List<Product> getAllProductsByGoodId(Long goodsId) {
        if (goodsId == null) {
            return Lists.newArrayList();
        }
        Map<String, Object> param = Maps.newHashMap();
        List<Long> ids = Lists.newArrayList();
        ids.add(goodsId);
        param.put("id", ids);
        List<Product> productList=new ArrayList<>();
        try {
            LOG.info("[gds_getProductByGoodsIdList] start, param :{}", GsonUtil.toJson(param));
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGenericGson().toJson(param), APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getProductByGoodsIdList");
            if (response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Map<Long, Map<Long, List<Product>>> prods = (Map<Long, Map<Long, List<Product>>>) EduGsonUtils
                        .fromJson(response.getMsg(),
                                new com.google.gson.reflect.TypeToken<Map<Long, Map<Long, List<Product>>>>() {
                                }.getType());
                if (null != prods && prods.containsKey(goodsId)) {
                    //返回Map<Long,Map<Long,List<Product>>>，第一个key为参数的goodid 第二个key为productid
                    Map<Long, List<Product>> pMap = prods.get(goodsId);
                    for(List<Product> products:pMap.values()){
                        productList.addAll(products);
                    }
                    return productList;
                }
            }
        } catch (Exception e) {
            LOG.error("[getAllProductsByGoodId] error param:{}", GsonUtil.toJson(param), e);
        }
        return Lists.newArrayList();
    }

    public List<ProductAdaptiveLearning> getAdaptiveProductByIdList(List<Long> idList) {
        List<ProductAdaptiveLearning> productAdaptiveLearningList = null;
        Map<String, Object> params = Maps.newHashMap();
        params.put("type", Consts.ProductType.PRODUCT_ADAPTIVE_LEARNING);
        params.put("productIdList", idList);
        String paramString = GsonUtil.getGenericGson().toJson(params);
        try {
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(paramString, APP_ID,
                    Consts.Code.CLIENT_IP, "gds_newGetProductByIdList");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type jsonType = new com.google.gson.reflect.TypeToken<List<ProductAdaptiveLearning>>() {
                }.getType();
                productAdaptiveLearningList = GsonUtil.getGson().fromJson(response.getMsg(), jsonType);
            } else {
                LOG.error("gds_newGetProductByIdList request:{} errcode:{} errMsg:{}", paramString, response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error("gds_newGetProductByIdList failed, request:{}, e:{}", paramString, e);
        }
        return productAdaptiveLearningList;
    }

    public Teacher getTeacherById(Long id) {
        String entry = "gds_getTeacherById";
        Teacher result = null;
        Teacher query = new Teacher();
        query.setId(id);
        String paramString = cn.huanju.edu100.util.GsonUtils.toJson(query);
        ThriftClientWrapper<edu100_goods.Iface> goodsClient = null;
        LOG.info(entry + " start, request:{}", paramString);
        try {
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(paramString, APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getTeacherById");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                String msg = response.getMsg();
                LOG.debug(entry + " success, request:{}, response:{}", paramString, msg);
                result = GsonUtil.getGson().fromJson(msg, Teacher.class);
            } else {
                LOG.error(entry + " request:{} errcode:{} errMsg:{}", paramString, response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error(entry + " failed, request:{}, e:{}", paramString, e);
        }
        return result;
    }

    @Cacheable(value = CACHE_NAME, key = "'GoodsResource.getGoodsRelateLessonById' + #id")
    public GoodsRelateLessonDTO getGoodsRelateLessonById(Long id) {
        if (id == null) {
            return null;
        }
        try {
            LOG.info("[getGoodsRelateLessonById] start, param :{}", id);
            response response = genericThriftResource.generalGoodsThriftMethodInvoke(GsonUtil.getGenericGson().toJson(id), APP_ID,
                    Consts.Code.CLIENT_IP, "gds_getGoodsRelateLessonById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<GoodsRelateLessonDTO>() {
                }.getType();
                return GsonUtil.getGson().fromJson(response.getMsg(), type);
            }
        } catch (Exception e) {
            LOG.error("[getGoodsRelateLessonById] error param:{}", id, e);
        }
        return null;
    }

    public List<StudyPathDTO> getStudyPathV2(Long productId) {
        List<StudyPathDTO> result = new ArrayList<>();
        if (Objects.isNull(productId)) {
            return result;
        }
        String studyPathCacheKeyV790 = "GoodsResource.studyPath_cache_from_goods_v790";
        String key = studyPathCacheKeyV790 + "_" + productId;
        String values = compatableRedisClusterClient.get(key);
        if (StringUtils.isNotBlank(values)) {
            try {
                result.addAll(JSONUtils.parseArray(values, StudyPathDTO.class));
            } catch (Exception e) {
                LOG.error("getStudyPathForProduct convert from redis catch exception, values:{}", values, e);
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            result = result.stream().filter(path -> Objects.nonNull(path.getStatus()) && path.getStatus().equals(1)).collect(Collectors.toList());
        } else {
            StudyPathParam param = new StudyPathParam();
            param.setProductId(productId);
            try {
                result.addAll(dataConfigService.getStudyPathV2(param));
                try {
                    //设置到 redis 里
                    String resultJson = JSONUtils.toJsonString(result);
                    compatableRedisClusterClient.setex(key, 60 * 10, resultJson);
                } catch (Exception e) {
                    LOG.error("set study path to redis catch exception, key:{}", key, e);
                }
            } catch (Exception e) {
                LOG.error("getStudyPathV2 catch exception for param:{}", JSONUtils.toJsonString(param), e);
            }

        }
        return result;
    }

    // 获取AI批阅配置, 注意添加redis缓存
    public List<GoodsLessonSetting> getGoodsLessonSettingList(Long goodsId, Long productId, Integer type) {
        if (goodsId == null || productId == null || type == null) {
            LOG.error("getGoodsLessonSettingList param error, goodsId or productId or type is null");
            return new ArrayList<>();
        }
        String key = String.format("getGoodsLessonSettingList.%d.%d.%d", goodsId, productId, type);
        try {
            String value = compatableRedisClusterClient.get(key);
            if (value != null) {
                return JSONUtils.parseArray(value,GoodsLessonSetting.class);
            }
        } catch (Exception e) {
            LOG.error("getGoodsLessonSettingList from redis catch exception, key:{}", key, e);
        }
        List<GoodsLessonSetting> settings = productLessonGrpcService.getGoodsLessonSettingList(goodsId, productId, type);
        try {
            compatableRedisClusterClient.setex(key, 60 * 10L, JSONUtils.toJsonString(settings));
        } catch (Exception e) {
            LOG.error("setGoodsLessonSettingList to redis catch exception, key:{}", key, e);
        }
        return settings;
    }
}
