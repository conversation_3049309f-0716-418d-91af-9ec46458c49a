/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 做题记录日详情DAO接口
 * 
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentAnswerDetailDao extends CrudDao<TutorStudentAnswerDetail> {

    List<Long> getDoneQuestionIdByUidAndTaskIdList(Long uid,  List<Long> taskIdList) throws DataAccessException;
}