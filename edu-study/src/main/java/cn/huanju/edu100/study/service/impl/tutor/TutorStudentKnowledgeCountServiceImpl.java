package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorStudentKnowledgeCountDao;
import cn.huanju.edu100.study.dao.tutor.TutorStudentKnowledgeDao;
import cn.huanju.edu100.study.dao.tutor.TutorStudentPushResDao;
import cn.huanju.edu100.study.model.Knowledge;
import cn.huanju.edu100.study.model.tutor.TutorStudentKnowledge;
import cn.huanju.edu100.study.model.tutor.TutorStudentKnowledgeCount;
import cn.huanju.edu100.study.model.tutor.TutorStudentPushRes;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.tutor.TutorStudentKnowledgeCountService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 知识点通过率Service
 *
 * <AUTHOR>
 * @version 2016-01-20
 */
@Service
public class TutorStudentKnowledgeCountServiceImpl extends
        BaseServiceImpl<TutorStudentKnowledgeCountDao, TutorStudentKnowledgeCount> implements
        TutorStudentKnowledgeCountService {

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private TutorStudentKnowledgeDao tutorStudentKnowledgeDao;

    @Autowired
    private TutorStudentPushResDao tutorStudentPushResDao;

    /**
     * 根据章节idList获取知识点学习详情
     *
     * @throws DataAccessException
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<Long, List<TutorStudentKnowledgeCount>> listPushResByChapterIdList(Map<String, Object> params)
            throws DataAccessException {

        if (CollectionUtils.isEmpty(params) || params.get("firstCategory") == null
                || params.get("secondCategory") == null || params.get("categoryId") == null
                || params.get("classes") == null || params.get("chapterIds") == null || params.get("uid") == null) {
            return Maps.newHashMap();
        }

        // 1. 查出科目下所有已统计并且用户有学过的知识点
        Long uid = (Long) params.get("uid");
        Long firstCategory = (Long) params.get("firstCategory");
        Long secondCategory = (Long) params.get("secondCategory");
        Long categoryId = (Long) params.get("categoryId");
        String classes = (String) params.get("classes");
        TutorStudentKnowledgeCount knowledgeCountEntity = new TutorStudentKnowledgeCount();
        knowledgeCountEntity.setFirstCategory(firstCategory);
        knowledgeCountEntity.setSecondCategory(secondCategory);
        knowledgeCountEntity.setCategoryId(categoryId);
        knowledgeCountEntity.setClasses(classes);
        List<TutorStudentKnowledgeCount> knowledgeCountList = this.dao.findList(knowledgeCountEntity);

        List<Long> knowledgeIdList = Lists.newArrayList();
        Map<Long, TutorStudentKnowledgeCount> knowledgeCountMap = Maps.newHashMap();
        TutorStudentKnowledge studentKnowledgeEntity = new TutorStudentKnowledge();
        studentKnowledgeEntity.setUid(uid);
        studentKnowledgeEntity.setClasses(classes);
        for (TutorStudentKnowledgeCount knowledgeCount : knowledgeCountList) {
            studentKnowledgeEntity.setKnowledgeId(knowledgeCount.getKnowledgeId());
            if (!CollectionUtils.isEmpty(tutorStudentKnowledgeDao.findList(studentKnowledgeEntity))) {
                // 查出微课推送
                setPushRes(knowledgeCount, uid);
                knowledgeCountMap.put(knowledgeCount.getKnowledgeId(), knowledgeCount);
                knowledgeIdList.add(knowledgeCount.getKnowledgeId());
            }
        }

        Map<Long, TutorStudentKnowledge> tuMap = getKnowledgeResultMap(uid, classes, knowledgeIdList);

        // 2. 根据章节idList获取知识点与已学习的知识点交集
        Map<Long, List<TutorStudentKnowledgeCount>> chapterknowledgeCountMap = Maps.newHashMap();
        List<TutorStudentKnowledgeCount> chapterKnowledgeCountList = null;
        TutorStudentKnowledgeCount chapterKnowledgeCount = null;
        List<Knowledge> knowledges = null;
        List<Long> chapterIds = (List<Long>) params.get("chapterIds");
        for (Long chapterId : chapterIds) {
            chapterKnowledgeCountList = Lists.newArrayList();
            knowledges = knowledgeResource.getKnowledgeByChapterId(chapterId);
            if (!CollectionUtils.isEmpty(knowledges)) {
                for (Knowledge knowledge : knowledges) {
                    chapterKnowledgeCount = knowledgeCountMap.get(knowledge.getId());
                    if (null != chapterKnowledgeCount) {
                        chapterKnowledgeCount.setKnowledgeName(knowledge.getName());
                        TutorStudentKnowledge tutorStudentKnowledge = tuMap.get(knowledge.getId());
                        if (tutorStudentKnowledge == null || tutorStudentKnowledge.getStatus() == null) {
                            chapterKnowledgeCount.setResult(Consts.Tutor_Student_Knowledge_Result.WRONG);
                        } else {
                            chapterKnowledgeCount.setResult(tutorStudentKnowledge.getStatus());
                        }
                        chapterKnowledgeCountList.add(chapterKnowledgeCount);
                    }
                }
            }
            chapterknowledgeCountMap.put(chapterId, chapterKnowledgeCountList);
        }

        return chapterknowledgeCountMap;
    }

    private Map<Long, TutorStudentKnowledge> getKnowledgeResultMap(Long uid, String classes, List<Long> knowledgeIdList)
            throws DataAccessException {
        Map<String, Object> tMap = new HashMap<String, Object>();
        tMap.put("uid", uid);
        tMap.put("classes", classes);
        tMap.put("knowlegeIdList", knowledgeIdList);
        tMap.put("orderBy", "create_date desc");
        List<TutorStudentKnowledge> tutorStudentKnowledges = tutorStudentKnowledgeDao.getTutorStudentKnowledge(tMap);
        Map<Long, TutorStudentKnowledge> tuMap = new HashMap<Long, TutorStudentKnowledge>();
        if (!CollectionUtils.isEmpty(tutorStudentKnowledges)) {
            for (TutorStudentKnowledge tutorStudentKnowledge : tutorStudentKnowledges) {
                if (!tuMap.containsKey(tutorStudentKnowledge.getKnowledgeId())) {
                    tuMap.put(tutorStudentKnowledge.getKnowledgeId(), tutorStudentKnowledge);
                }
            }
        }
        return tuMap;
    }

    /**
     * 有微课推送就一起查出
     *
     * @param knowledgeCount
     * @param uid
     * @throws DataAccessException
     */
    private void setPushRes(TutorStudentKnowledgeCount knowledgeCount, Long uid) throws DataAccessException {

        if (knowledgeCount == null) {
            return;
        }

        Page<TutorStudentPushRes> page = new Page<TutorStudentPushRes>(1, 1, "a.update_date desc");
        TutorStudentPushRes pushResEntity = new TutorStudentPushRes();
        pushResEntity.setUid(uid);
        pushResEntity.setCategoryId(knowledgeCount.getCategoryId());
        pushResEntity.setKnowledgeId(knowledgeCount.getKnowledgeId());
        List<TutorStudentPushRes> lastestPushRes = tutorStudentPushResDao.findList(page, pushResEntity);
        if (!CollectionUtils.isEmpty(lastestPushRes)) {
            knowledgeCount.setLessonId(lastestPushRes.get(0).getLessonId());
            knowledgeCount.setTaskId(lastestPushRes.get(0).getTaskId());
        }
    }
}
