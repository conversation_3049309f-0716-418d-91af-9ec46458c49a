package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 * 推送记录Entity
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
public class TutorStudentPushRes extends DataEntity<TutorStudentPushRes> {

    private static final long serialVersionUID = 1L;
    private String classes; // classes
    private Long firstCategory; // first_category
    private Long secondCategory; // first_category
    private Long categoryId; // first_category
    private Long lessonId; // 课件id
    private Long knowledgeId; // knowledge_id
    private Long studyId; // study_id
    private Long taskId; // task_id
    private Long uid; // uid
    private Integer resType; // res_type

    private List<Long> taskIdList;

    public TutorStudentPushRes() {
        super();
    }

    public TutorStudentPushRes(Long id) {
        super(id);
    }

    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

    public Long getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(Long knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public Long getStudyId() {
        return studyId;
    }

    public void setStudyId(Long studyId) {
        this.studyId = studyId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public Long getLessonId() {
        return lessonId;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public List<Long> getTaskIdList() {
        return taskIdList;
    }

    public void setTaskIdList(List<Long> taskIdList) {
        this.taskIdList = taskIdList;
    }

}
