package cn.huanju.edu100.study.service.evaluation;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.evaluation.EvaluationUserAnswer;
import com.hqwx.study.dto.EvaluationSubmitDTO;
import com.hqwx.study.dto.EvaluationSubmitRetDTO;
import com.hqwx.study.dto.EvaluationUserAnswerDTO;
import com.hqwx.study.dto.query.EvaluationUserAnswerQuery;

import java.util.List;

public interface EvaluationUserAnswerService extends BaseService<EvaluationUserAnswer> {

    List<EvaluationUserAnswerDTO> getEvaluationUserAnswerList(EvaluationUserAnswerQuery query) throws DataAccessException;

    EvaluationSubmitRetDTO submitEvaluationQuestion(EvaluationSubmitDTO evaluationSubmitDTO) throws DataAccessException;

    void transferEvaluationUserAnswerList();

    EvaluationUserAnswerDTO getEvaluationUserAnswerByAnswerId(EvaluationUserAnswerQuery query) throws DataAccessException;

}
