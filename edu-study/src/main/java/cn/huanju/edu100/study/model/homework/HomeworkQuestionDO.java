package cn.huanju.edu100.study.model.homework;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * Created by zhanghong on 2022/5/6.
 */
@Data
public class HomeworkQuestionDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 作业ID
     */
    private Long homeworkId;

    /**
     * 课节或云私塾任务id，为作业挂载主体
     */
    private Long objId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 题目排序，最小值1
     */
    private Integer sort;

    /**
     * 分值
     */
    private Double score;

    /**
     * 是否自定义题目，0-否 1-是
     */
    private Integer isDiy;

    /**
     * 删除标识 0-未删除 1-已删除
     */
    private Integer delFlag;

    /**
     * 创建用户ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改用户ID
     */
    private Long updateBy;

    /**
     * 修改时间
     */
    private Date updateDate;

}
