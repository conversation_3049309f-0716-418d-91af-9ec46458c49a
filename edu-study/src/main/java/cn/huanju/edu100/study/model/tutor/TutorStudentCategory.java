package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.study.model.onetoone.VStudentProduct;

import java.util.Date;

/**
 * 个性化学员科目表Entity
 *
 * <AUTHOR>
 * @version 2016-01-12
 */
public class TutorStudentCategory extends DataEntity<TutorStudentCategory> {

    private static final long serialVersionUID = 1L;
    private Long uid; // uid
    private String userName; // 用户名称
    private Integer type = 0; // type 0:个性化，1：一对一, 2:100留学一对一
    private String classes; // classes
    private Date startTime; // start_time
    private Date endTime; // end_time
    private Long firstCategory; //
    private Long secondCategory; // second_category
    private Long categoryId; // category_id
    private Long goodsId; // goods_id
    private String goodsName; // goods_name
    private Long orderId;
    private Integer status;
    private VStudentProduct vStudentProduct;


    public TutorStudentCategory() {
        super();
    }

    public TutorStudentCategory(Long id) {
        super(id);
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public VStudentProduct getvStudentProduct() {
        return vStudentProduct;
    }

    public void setvStudentProduct(VStudentProduct vStudentProduct) {
        this.vStudentProduct = vStudentProduct;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "TutorStudentCategory [uid=" + uid + ", type=" + type + ", classes=" + classes + ", startTime="
                + startTime + ", endTime=" + endTime + ", firstCategory=" + firstCategory + ", secondCategory="
                + secondCategory + ", categoryId=" + categoryId + ", goodsId=" + goodsId + ", goodsName=" + goodsName
                + ", orderId=" + orderId + ", status=" + status + "]";
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

}
