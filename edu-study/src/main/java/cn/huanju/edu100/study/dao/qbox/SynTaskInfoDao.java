package cn.huanju.edu100.study.dao.qbox;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.questionBox.SynTaskInfo;

/**
 * <AUTHOR>
 * @description
 * @date 18/11/2
 */
public interface SynTaskInfoDao extends <PERSON>rudDao<SynTaskInfo> {
    SynTaskInfo getByName(final String name);

    void updateByName(final SynTaskInfo synTaskInfo);

    void saveUpdateByName(final SynTaskInfo synTaskInfo);
}
