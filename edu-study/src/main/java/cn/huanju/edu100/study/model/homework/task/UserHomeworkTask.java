package cn.huanju.edu100.study.model.homework.task;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "user_homework_task",autoResultMap = true)
public class UserHomeworkTask {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 课节ID
     */
    private Long lessonId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 作业ID
     */
    private Long homeworkId;

    /**
     * 状态  0 未提交  1 已提交  2 未批改  3已批改
     */
    private Integer status;

    /**
     * 答案ID
     */
    private Long answerId;

    /**
     * 评分
     */
    private Double score;

    /**
     * 答题时间
     */
    private Date answerTime;

    /**
     * 作业点评ID
     */
    private Long commentId;

    /**
     * 作业点评时间
     */
    private Date commentTime;

    /**
     * 版本号
     */
    private Integer version;
}
