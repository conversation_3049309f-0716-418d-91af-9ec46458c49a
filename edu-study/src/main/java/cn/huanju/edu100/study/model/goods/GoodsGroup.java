/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 商品组Entity
 * 
 * <AUTHOR>
 * @version 1.0
 * @time 2015年9月7日10:56:58
 */
public class GoodsGroup extends DataEntity<GoodsGroup> {

    private static final long serialVersionUID = 1L;
    private String name; // 分组名称
    private Long firstCategory; // 所属大类
    private Long secondCategory; // 所属考试
    private Long categoryId; // 所属科目

    private String summary; // 简介
    private Long videoId; // 宣传视频id
    private String price; // 商品价格
    private Integer buyerCount; // 初始购买人数
    private String content; // 商品详细介绍

    private String bigPic; // 大图
    private String middlePic; // 中图
    private String smallPic; // 小图
    private Integer groupType; // 0：套餐，1：单班，2：题库
    private String others; // 其他属性json字符串
    
    /**
     * 课程使用
     */
    private Integer isNew; //区分商品组和课程，0：商品组，1：课程
    private Integer lessonType; //课程类型，0：直播，1：录播
    private Integer status; //课程状态，0：未生效，1：已生效
    private Integer sort; //排序值，降序排序
    private Integer limit; //购买限制人身
    private String showTime; //课程展示时间
    private Integer hours; //小时数
    private Integer lessonCount; //课次数
//    private List<Teacher> teachers; //老师列表
    
    private List<GoodsGroupContent> goodsGroupContentList = Lists.newArrayList();

    public GoodsGroup() {
        super();
    }

    public GoodsGroup(Long id) {
        super(id);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Integer getBuyerCount() {
        return buyerCount;
    }

    public void setBuyerCount(Integer buyerCount) {
        this.buyerCount = buyerCount;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<GoodsGroupContent> getGoodsGroupContentList() {
        return goodsGroupContentList;
    }

    public void setGoodsGroupContentList(List<GoodsGroupContent> goodsGroupContentList) {
        if (CollectionUtils.isNotEmpty(goodsGroupContentList)) {
            this.goodsGroupContentList = goodsGroupContentList;
        }
    }

    public String getBigPic() {
        return bigPic;
    }

    public void setBigPic(String bigPic) {
        this.bigPic = bigPic;
    }

    public String getMiddlePic() {
        return middlePic;
    }

    public void setMiddlePic(String middlePic) {
        this.middlePic = middlePic;
    }

    public String getSmallPic() {
        return smallPic;
    }

    public void setSmallPic(String smallPic) {
        this.smallPic = smallPic;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    public String getOthers() {
        return others;
    }

    public void setOthers(String others) {
        this.others = others;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    public Integer getLessonType() {
        return lessonType;
    }

    public void setLessonType(Integer lessonType) {
        this.lessonType = lessonType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getShowTime() {
        return showTime;
    }

    public void setShowTime(String showTime) {
        this.showTime = showTime;
    }

    public Integer getHours() {
        return hours;
    }

    public void setHours(Integer hours) {
        this.hours = hours;
    }

    public Integer getLessonCount() {
        return lessonCount;
    }

    public void setLessonCount(Integer lessonCount) {
        this.lessonCount = lessonCount;
    }



}