package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.StudyReportDetailDao;
import cn.huanju.edu100.study.model.PlanPhase;
import cn.huanju.edu100.study.model.StudyReportDetail;
import cn.huanju.edu100.study.model.StudyTask;
import cn.huanju.edu100.study.model.StudyTaskReport;
import cn.huanju.edu100.study.service.StudyPlanService;
import cn.huanju.edu100.study.service.StudyReportDetailService;
import cn.huanju.edu100.study.service.StudyTaskService;
import cn.huanju.edu100.util.EduStringUtils;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 学习计划Service
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
@Service
public class StudyReportDetailServiceImpl extends BaseServiceImpl<StudyReportDetailDao, StudyTaskReport> implements
        StudyReportDetailService {

    @Autowired
    private StudyTaskService studyTaskService;
    @Autowired
    private StudyPlanService studyPlanService;

    @Override
    public StudyTaskReport findTaskMsg(Long pid, Long uid) throws DataAccessException {
        StudyTaskReport taskReport = new StudyTaskReport();
        taskReport.setFinishCount(0);
        taskReport.setUnfinishCount(0);
        taskReport.setTaskCount(0);

        Collection<StudyTask> studyTaskList = studyTaskService.getStudyTasksByPidFromCache(pid);
        if (CollectionUtils.isEmpty(studyTaskList)) {
            return taskReport;
        }

        List<Long> tidList = getTaskIdListOfCurPhases(pid, studyTaskList);
        if (CollectionUtils.isEmpty(tidList)) {
            return taskReport;
        }
        String tidsStr = EduStringUtils.getUniqueIds(tidList);
        Integer finishCount = dao.qryFinishStudyTaskCount(tidsStr, uid);
        if (finishCount == null) {
            finishCount = 0;
        }
        // 屏蔽链接型任务按结束时间判断完成的状态
        // int linkTaskFinishCnt = countFinishLinkTask(studyTaskList);
        // finishCount = finishCount + linkTaskFinishCnt;
        Integer unfinishCount;
        Integer allCount = tidList.size();
        if (allCount == null) {
            allCount = 0;
            unfinishCount = 0;
        } else {
            unfinishCount = allCount - finishCount;
        }
        taskReport.setFinishCount(finishCount);
        taskReport.setUnfinishCount(unfinishCount);
        taskReport.setTaskCount(allCount);
        return taskReport;
    }

    /**
     * 按计划id获取当前阶段下的任务ID列表
     *
     * @param pid
     * @param studyTaskList
     * @return
     */
    private List<Long> getTaskIdListOfCurPhases(Long pid, Collection<StudyTask> studyTaskList)
            throws DataAccessException {
        Collection<PlanPhase> planPhaseList = studyPlanService.getPlanPhasesByPlanId(pid);
        if (CollectionUtils.isEmpty(planPhaseList)) {
            return null;
        }
        List<PlanPhase> curPhasesList = new ArrayList<PlanPhase>();
        Date nowDate = new Date();
        for (PlanPhase planPhase : planPhaseList) {
            if (nowDate.after(planPhase.getStartTime())) {
                curPhasesList.add(planPhase);
            }
        }
        if (CollectionUtils.isEmpty(curPhasesList)) {
            return null;
        }
        List<Long> tidList = new ArrayList<Long>();
        for (StudyTask task : studyTaskList) {
            for (PlanPhase curPhase : curPhasesList) {
                if (task.getPhaseId().equals(curPhase.getId())) {
                    tidList.add(task.getId());
                }
            }
        }
        return tidList;
    }

    /**
     * @param studyTaskList
     * @return
     */
    private int countFinishLinkTask(Collection<StudyTask> studyTaskList) {
        int linkTaskFinishCnt = 0;
        Date nowDate = new Date();
        for (StudyTask task : studyTaskList) {
            if (task.getType() == 6 && task.getEndTime() != null) {
                if (nowDate.after(task.getEndTime())) {
                    ++linkTaskFinishCnt;
                }
            }
        }
        return linkTaskFinishCnt;
    }

    @Override
    public List<StudyReportDetail> findStudyLogList(Long pid, Long uid) throws DataAccessException {
        return dao.findStudyLogList(pid, uid);
    }
}
