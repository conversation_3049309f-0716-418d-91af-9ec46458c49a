package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 评价Entity
 * <AUTHOR>
 * @version 2017-12-06
 */
public class Comment extends DataEntity<Comment> {
	
	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private String nickName;		// 用户昵称
	private Long commentElementId;		// 评价元素id
	private String content;		// 内容
	private Float star;		// 星星数
	private Float teacherStar;//老师打分
	private Integer status;		// 状态， 0:未审核 1：审核不通过 2：审核通过

	private Integer thumbFlag;//0,没点赞，1点赞
	private Integer thumbUpNum;// 点赞数
	private Integer isStick;// 是否置顶 0：否 1:是
	private String replyContent;   //回复内容

	private Integer isRead; //是否已读：0未读，1已读
	private Long objId;		// 对象id，obj_type为0，表示讲id，obj_type为1，表示课节id，obj_type为2，表示题目id， obj_type为3，表示云私塾id obj_type为4，表示每日一练
	private Integer objType;	// 对象类型 0：录播课，1：直播课，2：题目，3:云私塾  4：每日一练
	private String objName; //对象名称
	private String platform;	//评论提交所属终端
	private Integer level;
	private Integer replyNum;
	private List<ReplyComment> replyCommentList;

	@Getter
	@Setter
	private Long goodsId;

	@Getter
	@Setter
	private Long goodsGroupId;

	@Getter
	@Setter
	private Long productId;

	@Getter
	@Setter
	private Long categoryId;

	// 笔记来源：1=题库；2=普通课；3=云私塾
	@Getter
	@Setter
	private Integer source;

	public Comment() {
		super();
	}

	public Comment(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public Long getCommentElementId() {
		return commentElementId;
	}

	public void setCommentElementId(Long commentElementId) {
		this.commentElementId = commentElementId;
	}
	
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	public Float getStar() {
		return star;
	}

	public void setStar(Float star) {
		this.star = star;
	}
	
	public Float getTeacherStar() {
        return teacherStar;
    }

    public void setTeacherStar(Float teacherStar) {
        this.teacherStar = teacherStar;
    }

    public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getThumbFlag() {
		return thumbFlag;
	}

	public void setThumbFlag(Integer thumbFlag) {
		this.thumbFlag = thumbFlag;
	}

	public Integer getThumbUpNum() {
		return thumbUpNum;
	}

	public void setThumbUpNum(Integer thumbUpNum) {
		this.thumbUpNum = thumbUpNum;
	}

	public Integer getIsStick() {
		return isStick;
	}

	public void setIsStick(Integer isStick) {
		this.isStick = isStick;
	}

	public String getReplyContent() {
		return replyContent;
	}

	public void setReplyContent(String replyContent) {
		this.replyContent = replyContent;
	}

	public Integer getIsRead() {
		return isRead;
	}

	public void setIsRead(Integer isRead) {
		this.isRead = isRead;
	}

	public Long getObjId() {
		return objId;
	}

	public void setObjId(Long objId) {
		this.objId = objId;
	}

	public Integer getObjType() {
		return objType;
	}

	public void setObjType(Integer objType) {
		this.objType = objType;
	}

	public String getObjName() {
		return objName;
	}

	public void setObjName(String objName) {
		this.objName = objName;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	@Override
	public void preInsert() {
		createBy = 1L;
		updateBy = 1L;
		createDate = new Date();
		updateDate = createDate;
	}

	public Integer getReplyNum() {
		return replyNum;
	}

	public void setReplyNum(Integer replyNum) {
		this.replyNum = replyNum;
	}

	public List<ReplyComment> getReplyCommentList() {
		return replyCommentList;
	}

	public void setReplyCommentList(List<ReplyComment> replyCommentList) {
		this.replyCommentList = replyCommentList;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}
}