package cn.huanju.edu100.study.model.practice;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;
import java.util.List;

public class DailyQueryParam extends DataEntity<DailyQueryParam> {

    private Long firstCategory;//大类id
    private Long secondCategory;//考试id
    private Long categoryId;//科目Id
    private Long startTime;//开始时间戳 秒
    private Long endTime;//结束时间戳 秒
    private int from;
    private int row;
    private List<Long> schIds;

    private Date startDate;
    private Date endDate;

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getFrom() {
        return from;
    }

    public void setFrom(int from) {
        this.from = from;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public List<Long> getSchIds() {
        return schIds;
    }

    public void setSchIds(List<Long> schIds) {
        this.schIds = schIds;
    }
}