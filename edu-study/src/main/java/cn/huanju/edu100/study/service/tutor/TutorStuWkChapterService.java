package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.study.model.tutor.TutorStuWkChapter;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 学生对微课班章节任务完成情况Service
 * <AUTHOR>
 * @version 2017-12-28
 */
public interface TutorStuWkChapterService extends BaseService<TutorStuWkChapter> {

    Map<Long,Integer> getStuKnowNum(Long uid, List<Long> sectionIdList) throws BusinessException, DataAccessException;

    Map<Long,Integer> getStuTaskStatus(Long uid, Integer type, List<Long> sectionIdList) throws BusinessException, DataAccessException;

    TutorSectionTask getWkLastTask(Long uid, Long weikeId, Long sectionId, Long phaseId, Long unitId, Long secondCategory, Long categoryId, Integer type)
            throws BusinessException, DataAccessException;

    Map<Long,Integer> getUnitTaskNum(List<Long> unitIdList, Long uid, Integer type) throws BusinessException, DataAccessException;
}