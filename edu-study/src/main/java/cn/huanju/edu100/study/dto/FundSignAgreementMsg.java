package cn.huanju.edu100.study.dto;

import com.hqwx.fund.dto.enums.CertifyContractEnum;
import lombok.Data;


@Data
public class FundSignAgreementMsg extends KafkaBaseReq {

    private Long buyOrderId;
    private String name;
    private String idcard;
    private String phone;
    private CertifyContractEnum certifyStatus;

    public FundSignAgreementMsg(String eventType) {
        super(eventType);
    }

    public FundSignAgreementMsg(String eventType,Long buyOrderId, String name, String idcard, String phone,CertifyContractEnum certifyStatus) {
        super(eventType);
        this.buyOrderId = buyOrderId;
        this.name = name;
        this.idcard = idcard;
        this.phone = phone;
        this.certifyStatus = certifyStatus;
    }
}
