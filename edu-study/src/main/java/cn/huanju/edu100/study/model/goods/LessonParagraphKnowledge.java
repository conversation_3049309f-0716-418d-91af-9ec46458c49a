package cn.huanju.edu100.study.model.goods;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 段落知识点Entity
 * <AUTHOR>
 * @version 2015-07-29
 */
public class LessonParagraphKnowledge extends DataEntity<LessonParagraphKnowledge> {
	
	private static final long serialVersionUID = 1L;
	private Long lessonId;		// lesson_id
	private Long knowledgeId;		// knowledge_id
	private Long paragraphId;		// paragraph_id
	
	public LessonParagraphKnowledge() {
		super();
	}

	public LessonParagraphKnowledge(Long id){
		super(id);
	}

	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}
	
	public Long getKnowledgeId() {
		return knowledgeId;
	}

	public void setKnowledgeId(Long knowledgeId) {
		this.knowledgeId = knowledgeId;
	}
	
	public Long getParagraphId() {
		return paragraphId;
	}

	public void setParagraphId(Long paragraphId) {
		this.paragraphId = paragraphId;
	}
	
}