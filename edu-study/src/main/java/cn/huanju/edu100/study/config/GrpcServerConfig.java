package cn.huanju.edu100.study.config;


import cn.huanju.edu100.grpc.service.impl.DefaultEdu100StudyServiceGrpcImpl;
import cn.huanju.edu100.study.entry.grpc.GrpcProxy;
import cn.huanju.edu100.study.entry.thrift.Edu100StudyThriftImpl;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class GrpcServerConfig {

	/**
	 * 采用新的grpc-common1.5.0启动，不需要在配置SmartLifecycle了
	 * 只针对有老服务这样改造，新服务直接标记@GrpcService即可
	 */
	@Bean
	public DefaultEdu100StudyServiceGrpcImpl edu100StustampServiceGrpcImpl(Edu100StudyThriftImpl edu100StudyThrift){
		return createCompatibilityThriftGrpcServiceImpl(new DefaultEdu100StudyServiceGrpcImpl(), edu100StudyThrift);
	}

	/**
	 * 创建兼容 Thrift 的 Grpc 服务
	 * TODO: 考虑提取到公共模块，以便其他服务可直接复用
	 * @param defaultGrpcServiceImpl
	 * @param thriftImplBean
	 * @return
	 * @param <DefaultGrpcServiceImplType>
	 * @param <ThriftImplType>
	 */
	public <DefaultGrpcServiceImplType, ThriftImplType> DefaultGrpcServiceImplType createCompatibilityThriftGrpcServiceImpl(
			DefaultGrpcServiceImplType defaultGrpcServiceImpl, ThriftImplType thriftImplBean){
		Enhancer enhancer = new Enhancer();
		enhancer.setSuperclass(defaultGrpcServiceImpl.getClass());
		enhancer.setCallback(new GrpcProxy<ThriftImplType>(defaultGrpcServiceImpl, thriftImplBean));
		DefaultGrpcServiceImplType impl2 = (DefaultGrpcServiceImplType) enhancer.create();
		return impl2;
	}
}
