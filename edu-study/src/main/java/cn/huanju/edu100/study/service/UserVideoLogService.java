package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.UserVideoLog;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;

/**
 * 记录学生在学习录播课程过程中的信息Service
 *
 * <AUTHOR>
 * @version 2015-05-18
 */
public interface UserVideoLogService extends BaseService<UserVideoLog> {

//	public void updateLogCache(final UserVideoLog userVideoLog);
//	public UserVideoLog getFromCache(final Long uid, final Long courseId, final Long clsId, final Long lessonId, final String strDt)throws DataAccessException;
//	public UserVideoLog getFromCacheDb(final Long uid, final Long courseId, final Long clsId, final Long lessonId, final String strDt)throws DataAccessException;

//	public Long getUpdateTime(final String field);
//	public Long getLengthFromCache(final Long uid, final Long courseId, final Long clsId, final Long lessonId, final String strDt);
	/**
	 * @param userVideoLog
	 * @return
	 */
	Long addUserVideoLog(UserVideoLog userVideoLog) throws DataAccessException,BusinessException;


	void addUserVideoLogCache(UserVideoLog userVideoLog) throws DataAccessException,BusinessException;

	/**
	 * @param uid
	 * @param courseId
	 * @return
	 */
	Collection<UserVideoLog> queryUserVideoLogsByUidCourseId(Long uid,
			Long courseId) throws DataAccessException,BusinessException;

	/**
	 *
	 * @param uid
	 * @param courseId
	 * @return
	 * @throws DataAccessException
	 */
	UserVideoLog getLastUserVideoLog(long uid, long courseId)
			throws DataAccessException,BusinessException;

	Collection<UserVideoLog> queryUserVideoLogsByUidCourseIdList(Long uid,
			Collection<Long> courseIds) throws DataAccessException,BusinessException;


	boolean addBatch(Collection<UserVideoLog> list) throws DataAccessException;

}
