/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.onetoone.VLessonRelationDao;
import cn.huanju.edu100.study.model.onetoone.VLessonRelation;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 直播课节对应关系表DAO接口
 * <AUTHOR>
 * @version 2017-11-22
 */
public class VLessonRelationIbatisImpl extends CrudIbatisImpl2<VLessonRelation> implements
		VLessonRelationDao {

	public VLessonRelationIbatisImpl() {
		super("VLessonRelation");
	}

	@Override
	public List<VLessonRelation> findListByLiveLessonIdList(List<Long> liveLessonIdList,Integer type)
			throws DataAccessException {

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String,Object> param = Maps.newHashMap();
			param.put("idList",liveLessonIdList);
			param.put("type",type);
			return (List<VLessonRelation>) sqlMap.queryForList(
					"VLessonRelation.findListByLiveLessonIdList", param);
		} catch (SQLException e) {
			logger.error("findListByLiveLessonIdList SQLException.idList:{}", liveLessonIdList, e);
			throw new DataAccessException("findListByLiveLessonIdList SQLException error");
		}
	}
}
