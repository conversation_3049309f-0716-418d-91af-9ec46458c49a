package cn.huanju.edu100.study.util.pdf;

import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.QuestionOptions;
import cn.huanju.edu100.study.model.QuestionTopic;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.pdf.handler.PDFHeaderFooterHandler;
import cn.huanju.edu100.study.util.pdf.handler.PDFWatermarkHandler;
import cn.huanju.edu100.study.util.pdf.model.PdfContant;
import cn.huanju.edu100.study.util.pdf.model.PdfHtmlElement;
import cn.huanju.edu100.study.util.pdf.model.PdfTextBO;
import cn.huanju.edu100.study.util.pdf.util.HQFileUtil;
import cn.huanju.edu100.study.util.pdf.util.PdfDataConverter;
import cn.huanju.edu100.util.IdUtils;
import cn.huanju.edu100.util.JSONUtils;
import cn.huanju.edu100.util.upload.OssUtil;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.property.HorizontalAlignment;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import com.itextpdf.layout.property.VerticalAlignment;
import com.itextpdf.text.DocumentException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;


public class PdfUtil {
    private static final Integer pdfToImage = 1;

    private static final Logger logger = LoggerFactory.getLogger(PdfUtil.class);

    public static String generatePdfForQuestions(List<cn.huanju.edu100.study.model.Question> questionList, Long uid) throws DocumentException,IOException {
        byte[] bytes = null;
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            PdfWriter writer = new PdfWriter(baos);
            PdfDocument pdfDocument = new PdfDocument(writer);
            Document document = new Document(pdfDocument, PageSize.A4);
            document.setMargins(54, 54, 54, 54);

            PdfFont normalFont = HQFileUtil.getSysFont(PdfContant.Font.NormalFile);
            PdfFont boldFont = HQFileUtil.getSysFont(PdfContant.Font.BoldFile);


            //页头页脚
            pdfDocument.addEventHandler(PdfDocumentEvent.END_PAGE,
                    new PDFHeaderFooterHandler(normalFont, "用户"+uid+"生成"));

            //水印
            pdfDocument.addEventHandler(PdfDocumentEvent.END_PAGE,
                    new PDFWatermarkHandler(HQFileUtil.getDefaultImageWaterMark()));

            AtomicInteger questionIdx = new AtomicInteger(0); // 每次生成PDF时重置为0
            for (Question question : questionList) {
                drawQuestion(pdfDocument, document, question, questionIdx, normalFont, boldFont);
            }
            document.close();
            bytes = baos.toByteArray();
        } catch (Exception e) {
            logger.error("生成 PDF 文件失败", e);
            throw new RuntimeException("生成 PDF 文件失败", e);
        }

        if (bytes == null || bytes.length == 0) {
            logger.error("生成 PDF 文件失败");
            return null;
        }
        // 修改后的 PDF 转换逻辑
        if (pdfToImage == 1) {
            byte[] imageBytes =  getImagePdfBytes(bytes);
            if (imageBytes != null && imageBytes.length > 0) {
                return OssUtil.uploadPublicFile(imageBytes, System.currentTimeMillis() + "_question_" + uid + ".pdf");
            } else {
                logger.error("转换 PDF 为图片失败");
                return null;
            }
        } else {
            return OssUtil.uploadPublicFile(bytes, System.currentTimeMillis() + "_question_" + uid + ".pdf");
        }
    }
    private static void drawQuestion(PdfDocument pdfDocument, Document document,Question question, AtomicInteger questionIdx, PdfFont normalFont, PdfFont boldFont){
        if(Objects.isNull(question)
                || CollectionUtils.isEmpty(question.getTopicList())){
            return;
        }

        List<QuestionTopic> questionTopicList = question.getTopicList();
        final boolean IsMulti = (IdUtils.isValid(question.getIsMulti()) && question.getIsMulti() == 1);
        //题目标题
        if(IsMulti || question.getQtype() == Consts.Question_QType.AN_LI_FEN_XI || question.getQtype() == Consts.Question_QType.LUN_SHU) {
            //包含多题，大题标题
            drawQuestionTitle(pdfDocument, document,null, question.getContent(), normalFont);
        }

        //题目内容
        for (QuestionTopic questionTopic : questionTopicList) {
            int qSeq = questionIdx.incrementAndGet();
            //题目标题
            drawQuestionTitle(pdfDocument, document,qSeq, questionTopic.getContent(), normalFont);

            //题目项
            if(CollectionUtils.isNotEmpty(questionTopic.getOptionList())){
                for (QuestionOptions option : questionTopic.getOptionList()) {
                    drawQuestionOptions(pdfDocument, document,option, normalFont);
                }
            }
        }

        Integer anIdx = 0; //分析索引
        for (QuestionTopic questionTopic : questionTopicList){
            if(IsMulti){
                anIdx++;
            }
            drawAnalysis(pdfDocument, document, IsMulti, anIdx, questionTopic, normalFont, boldFont);
        }
    }

    private static void drawQuestionTitle(PdfDocument pdfDocument, Document document,Integer qSeq, String content, PdfFont normalFont ){
        if(StringUtils.isBlank(content)){
            return;
        }

        String qSeqStr = "";
        if(IdUtils.isValid(qSeq)){
            qSeqStr = textIndent(qSeq+".", null);
        }
        PdfTextBO paperText = PdfDataConverter.getPdfText(content);
        com.itextpdf.layout.element.Paragraph pTitle = createLeftPara(normalFont, PdfContant.Font.SizeQuestion, null);
        drawPdfText(pdfDocument, document,qSeqStr, paperText, pTitle);
        document.add(pTitle);
    }
    private static void drawQuestionOptions(PdfDocument pdfDocument, Document document, QuestionOptions option, PdfFont normalFont){
        String qSeqStr = "";
        if(StringUtils.isNotBlank(option.getSeq())){
            qSeqStr = textIndent(option.getSeq()+".", PdfContant.Text.FirstIndent_Normal);
        }

        PdfTextBO text = PdfDataConverter.getPdfText(option.getContent());

        com.itextpdf.layout.element.Paragraph pgOption = createLeftPara(normalFont, PdfContant.Font.SizeQuestion, null);
        drawPdfText(pdfDocument, document, qSeqStr, text, pgOption);
        document.add(pgOption);
    }
    private static void drawAnalysis(PdfDocument pdfDocument, Document document, final boolean isMulti, final Integer idx, QuestionTopic questionTopic, PdfFont normalFont, PdfFont boldFont){
        if(isMulti){
            String title = PdfContant.Analysis.AnliQuestionIdxPrefix+ NumberUtil.translateNumber(idx);
            title = textIndent(title, PdfContant.Text.FirstIndent_Normal);
            document.add(createLeftPara(title, boldFont, PdfContant.Font.SizeQuestion, null));
        }

        if(StringUtils.isNotBlank(questionTopic.getAnswerOption())){
            String answerOption = PdfContant.Analysis.OptionTitle+" "+questionTopic.getAnswerOption();
            answerOption = textIndent(answerOption, PdfContant.Text.FirstIndent_Space);
            document.add(createLeftPara(answerOption, boldFont, PdfContant.Font.SizeQuestion, null));
        }

        if(Objects.equals(Consts.Question_QType.AN_LI_FEN_XI, questionTopic.getQtype()) || Objects.equals(Consts.Question_QType.WEN_DA, questionTopic.getQtype()) || Objects.equals(Consts.Question_QType.LUN_SHU, questionTopic.getQtype())
        ){
            document.add(createLeftPara(textIndent(PdfContant.Analysis.AnliQuestionAnalysis, PdfContant.Text.FirstIndent_Space), boldFont, PdfContant.Font.SizeQuestion, null));
        }else {
            document.add(createLeftPara(textIndent(PdfContant.Analysis.OptionAnalysis, PdfContant.Text.FirstIndent_Space), boldFont, PdfContant.Font.SizeQuestion, 2f));
        }
        drawAnalysisText(pdfDocument, document, questionTopic.getAnalysisText(), normalFont);
    }
    private static void drawAnalysisText(PdfDocument pdfDocument, Document document, String analysisText, PdfFont normalFont){
        if(StringUtils.isBlank(analysisText)) {
            return;
        }

        PdfTextBO text = PdfDataConverter.getPdfText(analysisText);

        com.itextpdf.layout.element.Paragraph paragraph = createLeftPara(normalFont, PdfContant.Font.SizeQuestion, null);
        paragraph.setMarginLeft(UnitValue.createPointValue(32f).getValue());
        paragraph.setMarginRight(UnitValue.createPointValue(32f).getValue());
        drawPdfText(pdfDocument, document, null, text, paragraph);
        document.add(paragraph);
    }

    private static String textIndent(String originalText, Integer indentCount) {
        if (originalText == null || originalText.isEmpty() || !IdUtils.isValid(indentCount)) {
            return originalText;
        }

        StringBuilder sbi = new StringBuilder();
        for (Integer i = 0; i < indentCount; i++) {
            sbi.append("\u00a0");
        }
        String[] split = originalText.split("\n");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < split.length; i++) {
            sb.append(sbi);
            sb.append(split[i]);
            if(i<split.length-1) {sb.append("\n");}
        }
        return sb.toString();
    }

    public static com.itextpdf.layout.element.Paragraph createLeftPara(String content, PdfFont font, float fontSize, Float space){
        com.itextpdf.layout.element.Paragraph p = new com.itextpdf.layout.element.Paragraph(content)
                .setFont(font).setFontSize(fontSize)
                .setHorizontalAlignment(HorizontalAlignment.LEFT)
                .setVerticalAlignment(VerticalAlignment.TOP)
                .setTextAlignment(TextAlignment.LEFT);
        if(Objects.nonNull(space)) {
            p.setMarginTop(UnitValue.createPointValue(space).getValue());
            p.setMarginBottom(UnitValue.createPointValue(space).getValue());
        }
        return p;
    }
    public static com.itextpdf.layout.element.Paragraph createLeftPara(PdfFont font, float fontSize, Float space){
        com.itextpdf.layout.element.Paragraph p = new com.itextpdf.layout.element.Paragraph()
                .setFont(font).setFontSize(fontSize)
                .setHorizontalAlignment(HorizontalAlignment.LEFT)
                .setVerticalAlignment(VerticalAlignment.TOP)
                .setTextAlignment(TextAlignment.LEFT);
        if(Objects.nonNull(space)) {
            p.setMarginTop(UnitValue.createPointValue(space).getValue());
            p.setMarginBottom(UnitValue.createPointValue(space).getValue());
        }
        return p;
    }
    private static void drawPdfText(PdfDocument pdfDocument, Document document, String preText, PdfTextBO pdfText, com.itextpdf.layout.element.Paragraph paragraph){
        if(Objects.isNull(preText)) {
            preText = "";
        }
        if(StringUtils.isNotBlank(pdfText.getText())){
            paragraph.add(preText+pdfText.getText());
            return;
        }
        if(CollectionUtils.isNotEmpty(pdfText.getElementList())){
            drawElementList(pdfDocument, document, preText, pdfText.getElementList(), paragraph);
        }
    }
    private static void drawElementList(PdfDocument pdfDocument, Document document, String preText, List<PdfHtmlElement> elementList, com.itextpdf.layout.element.Paragraph paragraph){
        PageSize pageSize = pdfDocument.getDefaultPageSize();
        float pageWidth = pageSize.getWidth() - document.getLeftMargin() - document.getRightMargin();
        float pageHeight = pageSize.getHeight() - document.getTopMargin() - document.getBottomMargin();
        if(Objects.isNull(paragraph.getWidth())){
            paragraph.setWidth(pageWidth);
        }
        paragraph.setVerticalAlignment(VerticalAlignment.TOP);
        if(StringUtils.isNotBlank(preText)){paragraph.add(preText);}
        for (int i = 0; i < elementList.size(); i++) {
            PdfHtmlElement element = elementList.get(i);
            if (Objects.equals(PdfHtmlElement.Type.Text, element.getType())) {
                if (element.getValue().equals("\n")) {
                    //首尾换行 <p>转化的换行标签不要
                    if(0<i && i<elementList.size()-1){
                        paragraph.add(element.getValue());
                    }
                    continue;
                }
                paragraph.add(element.getValue());
            } else if (Objects.equals(PdfHtmlElement.Type.Img, element.getType())) {
                try {
                    ImageData imageData = ImageDataFactory.create(element.getValue());
                    float imgWidth = imageData.getWidth();
                    float imgHeight = imageData.getHeight();
                    if (element.getWidth() > 0) {
                        imgWidth = element.getWidth();
                        imgHeight = element.getHeight();
                    }

                    com.itextpdf.layout.element.Image image = new com.itextpdf.layout.element.Image(imageData);
                    image.setWidth(imgWidth);
                    image.setHeight(imgHeight);
                    if (pageWidth <= imgWidth) {
                        image.setAutoScaleWidth(true);
                    } else if(imgHeight>pageHeight/3f){
                        image.scale(0.5f,0.5f);
                    }else {
                        // 应用缩放比例
                        float scale = imgWidth/pageWidth;
                        if(scale >= 0.5f) {
                            image.scale(0.5f, 0.5f);
                        }else {
                            image.scale(0.76f, 0.76f);
                        }
                    }
                    paragraph.add(image);
                } catch (Exception e) {
                    logger.error("图片解析失败", e);
                }
            }
        }
    }

    public static byte[] getImagePdfBytes(byte[] pdfBytes) throws DocumentException, IOException {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(pdfBytes);
             PDDocument pdfDocument = PDDocument.load(bais)) {
            PDFRenderer pdfRenderer = new PDFRenderer(pdfDocument);

            try (PDDocument newPdfDocument = new PDDocument()) {
                for (int page = 0; page < pdfDocument.getNumberOfPages(); page++) {
                    // 1. 渲染图片时使用 BufferedImage.TYPE_INT_RGB 确保颜色模式正确
                    BufferedImage image = pdfRenderer.renderImageWithDPI(page, 150, ImageType.RGB);

                    // 2. 获取原始页面尺寸
                    PDPage originalPage = pdfDocument.getPage(page);
                    float pageWidth = originalPage.getMediaBox().getWidth();
                    float pageHeight = originalPage.getMediaBox().getHeight();

                    // 3. 创建新页面（保持原始尺寸）
                    PDPage newPage = new PDPage(originalPage.getMediaBox());
                    newPdfDocument.addPage(newPage);

                    try (PDPageContentStream contentStream = new PDPageContentStream(newPdfDocument, newPage)) {
                        // 4. 将图片缩放到页面尺寸
                        ByteArrayOutputStream imageBaos = new ByteArrayOutputStream();
                        ImageIO.write(image, "png", imageBaos);
                        byte[] imageBytes = imageBaos.toByteArray();

                        PDImageXObject pdImage = PDImageXObject.createFromByteArray(newPdfDocument, imageBytes, "page_" + page);

                        // 关键修改：正确计算位置和缩放
                        contentStream.drawImage(
                                pdImage,
                                0,  // x 坐标从左下角开始
                                0,  // y 坐标从左下角开始
                                pageWidth,  // 宽度与页面一致
                                pageHeight  // 高度与页面一致
                        );
                    } // 自动关闭 contentStream
                }

                // 保存新的 PDF
                try (ByteArrayOutputStream newPdfBaos = new ByteArrayOutputStream()) {
                    newPdfDocument.save(newPdfBaos);
                    return newPdfBaos.toByteArray();
                }
            }
        }
    }

    public static void main(String[] args) {
        String json = """
                [
                    {
                        "id": 3023679,
                        "isNewRecord": false,
                        "schId": 2,
                        "createBy": 4371,
                        "createDate": 1619765171000,
                        "updateBy": 2313,
                        "updateDate": 1708673827000,
                        "delFlag": "0",
                        "title": "第十七章-习题-单3",
                        "qtype": 0,
                        "firstCategory": 5597,
                        "secondCategory": 5846,
                        "categoryId": 5847,
                        "isMulti": 0,
                        "qlevel": 2,
                        "score": 0,
                        "content": "",
                        "state": 1,
                        "topicList": [
                            {
                                "id": 2123164,
                                "isNewRecord": false,
                                "delFlag": "0",
                                "seq": 1,
                                "qtype": 0,
                                "scoreRule": 0,
                                "score": 0,
                                "content": "<p>甲公司是一家大型制药企业，集科研、制造、贸易为一体，自2020年1月1日起授予客户5年内享有其某项药品的专利权许可证，并承诺为客户生产该药品，假设该药品的生产流程极为特殊，没有能生产该药品的其他企业，并且甲公司不单独提供生产服务。下列表述中正确的是（ &nbsp; &nbsp;）。</p>",
                                "answerOption": "C",
                                "analysisText": "https://oss-hqwx-edu24ol.hqwx.com/59dd6fcd2dc8a9c02637fe754c72a6e4eacdf5b2.js",
                                "optionList": [
                                    {
                                        "id": 4785235,
                                        "isNewRecord": false,
                                        "delFlag": "0",
                                        "seq": "A",
                                        "content": "&lt;p&gt;授予客户该药品的专利权许可证构成单项履约义务&lt;/p&gt;",
                                        "scoreProp": 0,
                                        "tid": 2123164
                                    },
                                    {
                                        "id": 4785236,
                                        "isNewRecord": false,
                                        "delFlag": "0",
                                        "seq": "B",
                                        "content": "&lt;p&gt;为客户提供生产服务构成单项履约义务&lt;/p&gt;",
                                        "scoreProp": 0,
                                        "tid": 2123164
                                    },
                                    {
                                        "id": 4785237,
                                        "isNewRecord": false,
                                        "delFlag": "0",
                                        "seq": "C",
                                        "content": "&lt;p&gt;授予客户该药品专利权许可证与为客户提供生产服务构成单项履约义务&lt;/p&gt;",
                                        "scoreProp": 100,
                                        "tid": 2123164
                                    },
                                    {
                                        "id": 4785238,
                                        "isNewRecord": false,
                                        "delFlag": "0",
                                        "seq": "D",
                                        "content": "&lt;p&gt;授予客户该药品的专利权许可证与为客户提供生产服务属于可明确区分的商品&lt;/p&gt;",
                                        "scoreProp": 0,
                                        "tid": 2123164
                                    }
                                ],
                                "qid": 3023679
                            }
                        ],
                        "proTagRelation": "2,3,5,11,15,14,13"
                    }
                ]
                """;

        var list = JSONUtils.parseArray(json, Question.class);
        try {
            long start = System.currentTimeMillis();
            String url = generatePdfForQuestions(list, 11498699L);
            System.out.println(url);
            System.out.println("耗时" + (System.currentTimeMillis() - start));
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


}
