package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.cache.DataCache;
import cn.huanju.edu100.study.dao.UserHomeWorkAnswerDao;
import cn.huanju.edu100.study.dao.UserVirtualHomeworkDao;
import cn.huanju.edu100.study.dao.calculate.TikuQuestionCalculateDao;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.al.KnowledgeGraph;
import cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise;
import cn.huanju.edu100.study.model.calculate.TikuQuestionCalculate;
import cn.huanju.edu100.study.model.question.WeekAnswerStat;
import cn.huanju.edu100.study.model.questionBox.*;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserQuestionBoxNewChapterService;
import cn.huanju.edu100.study.service.UserQuestionBoxService;
import cn.huanju.edu100.study.service.calculate.TikuLastChapterExerciseService;
import cn.huanju.edu100.study.service.qbox.UserDoneQuestionService;
import cn.huanju.edu100.study.service.qbox.UserLogSyncRecordService;
import cn.huanju.edu100.study.service.qbox.UserWipeOutWrongQuestionService;
import cn.huanju.edu100.study.service.qbox.UserWrongQuestionService;
import cn.huanju.edu100.study.service.question.WeekAnswerStatService;
import cn.huanju.edu100.study.util.*;
import cn.huanju.edu100.util.Collections3;
import cn.huanju.edu100.util.DateUtil;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.gson.reflect.TypeToken;
import com.hqwx.study.entity.UserAnswerDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
public class UserQuestionBoxNewChapterServiceImpl implements UserQuestionBoxNewChapterService {

	private static Logger logger = LoggerFactory.getLogger(UserQuestionBoxNewChapterServiceImpl.class);

	@Autowired
	private CompatableRedisClusterClient compatableRedisClusterClient;

	@Autowired
	private KnowledgeResource knowledgeResource;

	@Autowired
	private WeekAnswerStatService weekAnswerStatService;

	@Autowired
	@Qualifier("userVirtualHomeworkDao")
	private UserVirtualHomeworkDao userVirtualHomeworkDao;

	@Autowired
	private UserHomeWorkAnswerDao userHomeWorkAnswerDao;

	@Autowired
	private UserDoneQuestionService userDoneQuestionService;

	@Autowired
	private UserWrongQuestionService userWrongQuestionService;

	@Autowired
	private UserWipeOutWrongQuestionService userWipeOutWrongQuestionService;

	@Autowired
	private UserQuestionBoxService userQuestionBoxService;

	@Autowired
	private UserLogSyncRecordService userLogSyncRecordService;

	@Autowired
	private DataCache dataCache;

	@Autowired
    private TikuQuestionCalculateDao tikuQuestionCalculateDao;

	@Autowired
    private TikuLastChapterExerciseService tikuLastChapterExerciseService;

	private static int fixNum = 15;

    //15天过期时间
	private static Long expTime = 15*24*60*60L;

    private static ExecutorService executor = Executors.newFixedThreadPool(Constants.DEFAULT_POOL_SIZE, new ThreadFactoryBuilder().setNameFormat("userQuestionBoxThreadPool-%d").setDaemon(true).build());


    /**
	 * 获取题目的源数据
	 * */
	@Override
	public void getOriQuestionIds(Set<Long> oriQuestionIds, Long boxId, Long teachBookId, Long objId, Integer objType, StringBuffer homeworkName) throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {
		}.getType();

		switch (objType.intValue()) {
			case 0:
                Map<String, String> questionMaps = knowledgeResource.getTiku5QuestionMapByBoxAndTeachBook(boxId, teachBookId);
				if (questionMaps!=null && questionMaps.size()>0) {
					Iterator questionMapsIterator = questionMaps.entrySet().iterator();
					while (questionMapsIterator.hasNext()) {
						Entry questionEntry = (Entry)questionMapsIterator.next();
						String qidStr = questionEntry.getValue().toString();
						List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);
						oriQuestionIds.addAll(qidList);
					}
				}

				break;
			case 1://章节
                String questionCpListStr = knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId, NewRedisConsts.getTeachbookQuestionboxChapterFiled(objId));
                if (StringUtils.isNotBlank(questionCpListStr)) {
                    List<Long> questionCpList = GsonUtil.getGson().fromJson(questionCpListStr, type);
                    if (questionCpList != null && questionCpList.size() > 0) {
                        oriQuestionIds.addAll(questionCpList);
                    }
                }
                if (homeworkName != null) {
                    ChapterSection chapterSection = knowledgeResource.getChapterSectionItemById(objId);
                    if (chapterSection != null) {
                        homeworkName.append(chapterSection.getName());
                    } else {
                        throw new DataAccessException("chapterSection is not exsit! id:" + objId);
                    }
                }
				break;
			case 2://知识点
                String questionKlgListStr = knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId, NewRedisConsts.getTeachbookQuestionboxKnowledgeFiled(objId));
                if (StringUtils.isNotBlank(questionKlgListStr)) {
                    List<Long> questionKlgList = GsonUtil.getGson().fromJson(questionKlgListStr, type);
                    if (questionKlgList != null && questionKlgList.size() > 0) {
                        oriQuestionIds.addAll(questionKlgList);
                    }
                }
                if (homeworkName != null) {
                    KnowledgeGraph knowledge = knowledgeResource.getKnowledgeGraphById(objId);
                    if (knowledge != null) {
                        homeworkName.append(knowledge.getName());
                    } else {
                        throw new DataAccessException("knowledge is not exsit! id:" + objId);
                    }
                }
				break;
		}
	}

	private Map<Long, List<Long>> getOriQuestionIds_new(Long boxId, Long teachBookId, List<Long> objIds, Integer objType) throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {
		}.getType();
		Map<Long, Map<String,String>> resMap = Maps.newHashMap();
		Map<Long, List<Long>> result = Maps.newHashMap();
		Map<Long, String> res = Maps.newHashMap();
        Map<String, String> questionMaps = null;
        switch (objType.intValue()) {
            case 0:
                for (Long bId : objIds) {
                    resMap.put(bId, knowledgeResource.getTiku5QuestionMapByBoxAndTeachBook(bId, teachBookId));
                }
                if (resMap != null && resMap.size() > 0) {
                    for (Long key : resMap.keySet()) {
                        questionMaps = resMap.get(key);
                        if (questionMaps != null && questionMaps.size() > 0) {
                            Iterator questionMapsIterator = questionMaps.entrySet().iterator();
                            while (questionMapsIterator.hasNext()) {
                                Entry questionEntry = (Entry) questionMapsIterator.next();
                                String qidStr = questionEntry.getValue().toString();
                                List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);
                                List<Long> oldQidList = result.get(key);
                                if (oldQidList != null) {
                                    Set<Long> tempSet = Sets.newHashSet();
                                    tempSet.addAll(oldQidList);
                                    tempSet.addAll(qidList);
                                    result.put(key, new ArrayList<Long>(tempSet));
                                } else {
                                    result.put(key, qidList);
                                }

                            }
                        }
                    }
                }
                break;
            case 1://章节
                questionMaps = knowledgeResource.getTiku5QuestionMapByBoxAndTeachBook(boxId, teachBookId);
                if (questionMaps != null && questionMaps.size() > 0) {
                    for (Long objId : objIds) {
                        res.put(objId, questionMaps.get(NewRedisConsts.getTeachbookQuestionboxChapterFiled(objId)));
                    }
                }
                if (res != null && res.size() > 0) {
                    for (Long key : res.keySet()) {
                        String questionCpListStr = res.get(key);
                        List<Long> questionCpList = GsonUtil.getGson().fromJson(questionCpListStr, type);
                        if (questionCpList != null) {
                            if (result.containsKey(key)) {
                                result.get(key).addAll(questionCpList);
                            } else {
                                result.put(key, questionCpList);
                            }
                        }
                    }
                }
                break;
            case 2://知识点
                questionMaps = knowledgeResource.getTiku5QuestionMapByBoxAndTeachBook(boxId, teachBookId);
                if (questionMaps != null && questionMaps.size() > 0) {
                    for (Long objId : objIds) {
                        res.put(objId, questionMaps.get(NewRedisConsts.getTeachbookQuestionboxKnowledgeFiled(objId)));
                    }
                }
                if (res != null && res.size() > 0) {
                    for (Long key : res.keySet()) {
                        String questionKlgListStr = res.get(key);
                        List<Long> questionKlgList = GsonUtil.getGson().fromJson(questionKlgListStr, type);
                        if (questionKlgList != null) {
                            if (result.containsKey(key)) {
                                result.get(key).addAll(questionKlgList);
                            } else {
                                result.put(key, questionKlgList);
                            }
                        }
                    }
                }
                break;
        }
        return result;
	}

	/**
	 * 根据随机类型过滤题目数据
	 * */
	private void filterQuestionList(Integer randomType, Long uid, Long boxId, Long teachBookId, Long objId, Integer objType,
									List<Integer> qTypes, Set<Long> oriQuestionIds, List<Long> resultQuestionIds) throws DataAccessException{
		switch (randomType.intValue()) {
			case Consts.ExerciseRandomType.ALL://所有试题
				resultQuestionIds = new ArrayList<Long>(oriQuestionIds);
				break;

			case Consts.ExerciseRandomType.WRONG://错误试题
				resultQuestionIds = getUserWrongQuestionIdList(uid, boxId, teachBookId, objId, objType);
				break;

			case Consts.ExerciseRandomType.UNDO://未做试题
				List<Long> filterQuestions = getUserDoneQuestionIdList(uid, boxId, teachBookId, objId, objType);

				//过滤掉已经做过的题目
				resultQuestionIds = (List<Long>)CollectionUtils.subtract(oriQuestionIds, filterQuestions);
				break;
			case Consts.ExerciseRandomType.INTELLIGENT://智能练习随机（未做+错题随机，如果两者都为空，则直接是所有随机）
				List<Long> wrongQuestions = getUserWrongQuestionIdList(uid, boxId, teachBookId, objId, objType);
				List<Long> hasdoQuesitons = getUserDoneQuestionIdList(uid, boxId, teachBookId, objId, objType);
				List<Long> undoQuestions = (List<Long>)CollectionUtils.subtract(oriQuestionIds, hasdoQuesitons);

				if (!wrongQuestions.isEmpty() && !undoQuestions.isEmpty()) {//如果两个均不为空
					List<Long> unionDatas = (List<Long>)CollectionUtils.union(wrongQuestions, undoQuestions);
					HashSet<Long> unionQuestions = new HashSet<Long>(unionDatas);
					resultQuestionIds = new ArrayList<Long>(unionQuestions);
				}else {
					if (!wrongQuestions.isEmpty()) {
						resultQuestionIds = wrongQuestions;
					}else if(!undoQuestions.isEmpty()){
						resultQuestionIds = undoQuestions;
					}else {
						resultQuestionIds = new ArrayList<Long>(oriQuestionIds);
					}
				}

				break;
		}

		//2.2.如果有子过滤项，再根据题型的子过滤项，再过滤一遍目标集合
		if (qTypes != null && qTypes.size() > 0) {
			Set<Long> mQuestionIds = new HashSet<Long>();//最终结果集合

			List<Question> questions = knowledgeResource.getQuestionByIds(resultQuestionIds);
			if (questions != null && questions.size() > 0) {
				for (Question question : questions) {
					if (qTypes.contains(question.getQtype())) {
						mQuestionIds.add(question.getId());
					}
				}
			}
			resultQuestionIds = new ArrayList<Long>(mQuestionIds);
		}
	}

	/**
	 * 根据条件获取题库内的题目
	 * @param boxId
	 * @param teachBookId
	 * @param objType (0：所有，1：章节，2：知识点)
	 * @param objId
	 *
	 * */
	@Override
	public List<Long> getBoxQuestionIds(Long boxId, Long teachBookId, Long objId, Integer objType) throws DataAccessException{
		Set<Long> boxQuestions = new HashSet<Long>();


		if (teachBookId != null && teachBookId > 0L) {
			getOriQuestionIds(boxQuestions, boxId, teachBookId, objId, objType, null);
		}else {
			List<Long> bookIds = dataCache.getTiku5BookIdByBoxId(boxId);

			for (Long bookId : bookIds) {
				Set<Long> tempBoxQuestions = new HashSet<Long>();
				getOriQuestionIds(tempBoxQuestions, boxId, bookId, objId, objType, null);

				boxQuestions.addAll(tempBoxQuestions);
			}
		}
		if (boxQuestions != null && !boxQuestions.isEmpty()) {
			return new ArrayList<Long>(boxQuestions);
		}

		return new ArrayList<Long>();
	}

	@Override
	public Map<Long, List<Long>> getBoxQuestionIdsBatch(Long boxId, Long teachBookId, List<Long> objIds, Integer objType) throws DataAccessException{
		Map<Long, List<Long>> result = Maps.newHashMap();

		if (teachBookId != null && teachBookId > 0L) {
			result = getOriQuestionIds_new(boxId, teachBookId, objIds, objType);
		}else {
			List<Long> bookIds = Lists.newArrayList();
			if(objType == 0){
				for(Long boxid : objIds){
					bookIds.addAll(dataCache.getTiku5BookIdByBoxId(boxid));
				}
			}else{
				bookIds = dataCache.getTiku5BookIdByBoxId(boxId);
			}
			Map<Long, List<Long>> tempMap;
			Map<Long, Set<Long>> qset = Maps.newHashMap();
			for (Long bookId : bookIds) {
				tempMap = getOriQuestionIds_new(boxId, bookId, objIds, objType);
				if(tempMap != null && tempMap.size() > 0){
					for(Long key : tempMap.keySet()){
						if(qset.containsKey(key)){
							qset.get(key).addAll(tempMap.get(key));
						}else{
							qset.put(key, new HashSet<Long>(tempMap.get(key)));
						}
					}
				}
			}
			for(Long key : qset.keySet()){
				result.put(key, new ArrayList<Long>(qset.get(key)));
			}
		}
		if (result != null && !result.isEmpty()) {
			return result;
		}

		return Maps.newHashMap();
	}

	/**
	 * 根据策略随机抽取X道题目
	 * @param num
	 * @param randomType
	 * @param objType (0：所有，1：章节，2：知识点)
	 * @param objId
	 * @param qTypes List<Integer>(在objType的前提下，增加题型的子过滤项)
	 * @param teachBookId
	 * @param boxId
	 * @param uid
	 *
	 * */
	@Override
	@Transactional(readOnly = false)
	public VirtualHomework getRamdonBoxQuestionList(Long uid, Long boxId,
													Long teachBookId, Long objId, Integer objType, List<Integer> qTypes,Integer randomType,
													Integer num) throws DataAccessException {

		Type type = new TypeToken<List<Long>>() {
		}.getType();

		List<Long> resultQuestionIds = new ArrayList<Long>();//最终结果集合
		StringBuffer homeworkName = new StringBuffer("");//作业名称
		//1.首先取出对应条件的所有题目
		Set<Long> oriQuestionIds = new HashSet<Long>();
		getOriQuestionIds(oriQuestionIds, boxId, teachBookId, objId, objType, homeworkName);
		if (CollectionUtils.isNotEmpty(oriQuestionIds)) {
			switch (randomType.intValue()) {
				case Consts.ExerciseRandomType.ALL://所有试题
					// 当随机策略为从所有试题中随机时，保证随机友好性，剔除最近一次作答过的题目id
					resultQuestionIds = new ArrayList<Long>(oriQuestionIds);
					break;
				case Consts.ExerciseRandomType.WRONG://错误试题
					resultQuestionIds = new ArrayList<Long>(oriQuestionIds);
					List<Long> wrongIds = getUserWrongQuestionIdList(uid, boxId, teachBookId, objId, objType);
					resultQuestionIds.retainAll(wrongIds);
					break;

				case Consts.ExerciseRandomType.UNDO://未做试题
					List<Long> filterQuestions = getUserDoneQuestionIdList(uid, boxId, teachBookId, objId, objType);
					//过滤掉已经做过的题目
					resultQuestionIds = (List<Long>)CollectionUtils.subtract(oriQuestionIds, filterQuestions);
					break;
			}

			//2.2.如果有子过滤项，再根据题型的子过滤项，再过滤一遍目标集合
			if (qTypes != null && qTypes.size() > 0) {
				Set<Long> mQuestionIds = new HashSet<Long>();//最终结果集合

				List<Question> questions = knowledgeResource.getQuestionByIds(resultQuestionIds);
				if (questions != null && questions.size() > 0) {
					for (Question question : questions) {
						if (qTypes.contains(question.getQtype())) {
							mQuestionIds.add(question.getId());
						}
					}
				}
				resultQuestionIds = new ArrayList<Long>(mQuestionIds);
			}

			//3.根据取出的源集合，取出对应的题目数量
			if (resultQuestionIds != null && resultQuestionIds.size() > 0) {
				if (num < resultQuestionIds.size()) {//如果请求的数量小于源集合数量
					int max=resultQuestionIds.size()-1;
					int min=0;
					Set<Integer> chooseHis = new HashSet<Integer>();//TODO:检查检查检查！！
					if ((resultQuestionIds.size() - num) < fixNum && num-fixNum >0) {//如果目标集数量X和源集合Y数量之间的差值小余15，则直接取源集合中的前X-15道题，剩下的再随机
						for (int i = 0; i < num-(resultQuestionIds.size() - num); i++) {
							chooseHis.add(i);
						}
						min = num-(resultQuestionIds.size() - num);
						while (chooseHis.size() != num) {
							int s = RandomUtils.nextInt(max)%(max-min+1) + min;
							chooseHis.add(s);
						}
					}else {
						while (chooseHis.size() != num) {
							int s = RandomUtils.nextInt(max)%(max-min+1) + min;
							chooseHis.add(s);
						}
					}
					List<Long> finalList = new ArrayList<Long>();
					for (Integer seq : chooseHis) {
						finalList.add(resultQuestionIds.get(seq));
					}

					resultQuestionIds = finalList;
				}else {//如果请求的数量大于源集合数量，则直接取源集合即可
				}

				//4.根据取出来的题目，给当前用户形成一份练习，并记录入库，若无题目，则跳过
				Questionbox box = knowledgeResource.getQuestionBoxById(boxId);
				if (box == null) {
					logger.error("ramdonBoxQuestion4Tourist boxId:{} not find valid questionBox!!",boxId);
					return null;
				}
				if (StringUtils.isNotBlank(homeworkName)) {
					homeworkName = new StringBuffer(box.getName()+"《"+homeworkName.toString()+"》");
				}else {
					homeworkName = new StringBuffer(box.getName());
				}
				VirtualHomework homework = new VirtualHomework();
				homework.setUid(uid);
				homework.setName(homeworkName.toString());
				homework.setNum(resultQuestionIds.size());
				homework.setHomeworkTypeId(objId != null ? objId : 0L);//作业类型id(比如章节id，知识点id)
				homework.setHomeworkType(objType);//作业类型(0：所有，1：章节作业，2：知识点作业)
				homework.setSourceId(boxId);
				homework.setSourceType(0);//作业来源类型（0：题库，1：...）
				homework.setBookId(teachBookId);
				homework.setRandomType(randomType);
				homework.setCreateBy(uid);
				homework.setUpdateBy(uid);

				Long homeworkId = userVirtualHomeworkDao.insert(homework);
				homework.setId(homeworkId);

				List<VirtualHomeworkDetail> details = new ArrayList<VirtualHomeworkDetail>();

				for (Long questionIds : resultQuestionIds) {
					VirtualHomeworkDetail detail = new VirtualHomeworkDetail();
					detail.setElementId(questionIds);
					detail.setElementType(0);
					detail.setHomeworkId(homeworkId);
					detail.setCreateBy(uid);
					detail.setUpdateBy(uid);
					details.add(detail);
				}

				Integer insertNum = userVirtualHomeworkDao.insertDetailBatch(details, uid);
				if (insertNum == null || insertNum == 0){
					return null;
				}
				homework.setVirtualHomeworkDetails(details);
				return homework;
			}else {
				return null;
			}

		}
		return null;
	}


	/**
	 * 获取用户做错的题目List
	 * */
	@Override
	public List<Long> getUserWrongQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType) throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {
		}.getType();
		Set<Long> filterQuestionIds = new HashSet<Long>();
		switch (objType) {
			case 0://所有
                checkAndReloadWrongQuestions(uid, boxId);
				String[] fields = {NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DAN_XUAN),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.BU_DING_XUAN),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DUO_XUAN),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.PAN_DUAN),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.TIAN_KONG),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.WEN_DA)};
				//所有题目即所有题型的题目
				List<String> allQIdList = compatableRedisClusterClient.hmget(NewRedisConsts.getUserQuestionboxWrong(uid, boxId), fields);
				if(null != allQIdList && !allQIdList.isEmpty()) {
					for (String qIdsStr : allQIdList) {
						if (StringUtils.isNotBlank(qIdsStr)) {
							List<Long> qidList = GsonUtil.getGson().fromJson(qIdsStr, type);
							filterQuestionIds.addAll(qidList);
						}
					}
				}

				break;
			case 1://章节
				String questionCpListStr = this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, objId));
				if (StringUtils.isNotBlank(questionCpListStr)) {
					List<Long> qIdCpList = GsonUtil.getGson().fromJson(questionCpListStr, type);
					filterQuestionIds.addAll(qIdCpList);
				}
				break;
			case 2://知识点
				String questionKlgListStr = this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, objId));
				if (StringUtils.isNotBlank(questionKlgListStr)) {
					List<Long> qIdKlgList = GsonUtil.getGson().fromJson(questionKlgListStr, type);
					filterQuestionIds.addAll(qIdKlgList);
				}
				break;
			case 3://题型
				String questionQtypeListStr = this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(objId.intValue()));
				List<Long> qIdQtypeList = GsonUtil.getGson().fromJson(questionQtypeListStr, type);
				if (qIdQtypeList != null && qIdQtypeList.size() > 0) {
					filterQuestionIds.addAll(qIdQtypeList);
				}

				break;
			default:
				break;
		}
		// 过滤掉关闭的错题

//		Map<Long, Integer> questionStatMap = knowledgeResource.getQuestionStateByIdList(new ArrayList<Long>(filterQuestionIds));
/*		List<Long> resultUserWrongList = new ArrayList<Long>();
		Integer state = null;
		for (Long aLong : filterQuestionIds) {
			if(null != questionStatMap && !questionStatMap.isEmpty()) { //判断是否为空
				state = questionStatMap.get(aLong);
				if(null != state && state.equals(Consts.QuestionState.VALID)){
					resultUserWrongList.add(aLong);
				}
			}
		}*/
		List<Long> resultUserWrongList = new ArrayList<>(filterQuestionIds);
		return resultUserWrongList;
	}


    /**
     * 获取用户做错的题目List
     * */
    private Map<Long, List<Long>> getUserWrongQuestionIdListBatch(Long uid, Long boxId, Long teachBookId, List<Long> objIds, Integer objType) throws DataAccessException{
        if(objIds == null || objIds.size() == 0){
            return Maps.newHashMap();
        }
        Type type = new TypeToken<List<Long>>() {}.getType();
        Map<Long, List<Long>> result = Maps.newHashMap();
        Map<Long, String> responseMap = Maps.newHashMap();
        HashMap<Long, List<Long>> qIdMap = new HashMap<Long, List<Long>>();
        List<Long> idsList = Lists.newArrayList();
		switch (objType) {
			case 0://所有
				checkAndReloadWrongQuestionsBatch(uid,objIds);
				String[] fields = {NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DAN_XUAN),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.BU_DING_XUAN),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DUO_XUAN),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.PAN_DUAN),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.TIAN_KONG),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.WEN_DA),
						NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.AN_LI_FEN_XI)
				};

				//所有题目即所有题型的题目
				HashMap<Long, List<Long>> qIdRedisMap = new HashMap<Long, List<Long>>();
				Map<String, List<String>> responses = new HashMap<String, List<String>>(objIds.size());

				for (Long objId : objIds) {
					responses.put(String.valueOf(objId), compatableRedisClusterClient.hmget(NewRedisConsts.getUserQuestionboxWrong(uid, objId), fields));
				}

				if (responses != null && !responses.isEmpty()) {
					for (String key : responses.keySet()) {
						idsList.clear();
						List<String> allList = responses.get(key);
						if (allList != null) {
							for (String idstr : allList) {
								List<Long> list = GsonUtil.getGson().fromJson(idstr, type);
								if (list != null && list.size() > 0) {
									idsList.addAll(list);
								}
							}
						}
						if (idsList != null && idsList.size() > 0) {
							List<Long> listNew = new ArrayList<Long>(new HashSet(idsList));
							qIdRedisMap.put(Long.parseLong(key), listNew);
						}
					}
				}

				result = qIdRedisMap;
				break;
			case 1://章节
				checkAndReloadWrongQuestions(uid, boxId);
				setUserQuestionboxWrongByHmget(uid, boxId, teachBookId, objIds, responseMap, objType);
				result = getLongListMap(responseMap, type, qIdMap, result);
				break;
			case 2://知识点
				checkAndReloadWrongQuestions(uid, boxId);
//				for (Long objId : objIds) {
//					responseMap.put(objId, compatableRedisClusterClient.hget(NewRedisConsts.getUserQuestionboxWrong(uid, boxId),
//							NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, objId)));
//				}
				setUserQuestionboxWrongByHmget(uid, boxId, teachBookId, objIds, responseMap, objType);

				result = getLongListMap(responseMap, type, qIdMap, result);
				break;
			case 3://题型
				checkAndReloadWrongQuestions(uid, boxId);
//				for (Long objId : objIds) {
//					responseMap.put(objId, compatableRedisClusterClient.hget(NewRedisConsts.getUserQuestionboxWrong(uid, boxId),
//							NewRedisConsts.getUserQuestionboxWrongQtype(objId.intValue())));
//				}

				setUserQuestionboxWrongByHmget(uid, boxId, teachBookId, objIds, responseMap, objType);

				result = getLongListMap(responseMap, type, qIdMap, result);
				break;
			default:
				break;
		}

		// 过滤掉关闭的错题
		Set<Long> filterQuestionIds = new HashSet<Long>();
		if (result != null && result.size() > 0) {
			for (Long key : result.keySet()) {
				filterQuestionIds.addAll(result.get(key));
			}
		}

//		Map<Long, Integer> questionStatMap = knowledgeResource.getQuestionStateByIdList(new ArrayList<Long>(filterQuestionIds));

		List<Long> resultUserWrongList = new ArrayList<Long>(filterQuestionIds);
/*		Integer state = null;
		for (Long aLong : filterQuestionIds) {
			if (null != questionStatMap && !questionStatMap.isEmpty()) { //判断是否为空
				state = questionStatMap.get(aLong);
				if (null != state && state.equals(Consts.QuestionState.VALID)) {
					resultUserWrongList.add(aLong);
				}
			}
		}*/
		if (result != null && result.size() > 0) {
			for (Long key : result.keySet()) {
				List<Long> tempList = result.get(key);
				List<Long> resList = Lists.newArrayList();
				for (Long id : tempList) {
					if (resultUserWrongList.contains(id)) {
						resList.add(id);
					}
				}
				result.put(key, resList);
			}
		}
        return result;
    }

	private Map<Long, List<Long>> getLongListMap(Map<Long, String> responseMap, Type type, HashMap<Long, List<Long>> qIdMap, Map<Long, List<Long>> result) {
		for (Long key : responseMap.keySet()) {
			String idsStr = responseMap.get(key);
			List<Long> list = GsonUtil.getGson().fromJson(idsStr, type);
			if (list != null && list.size() > 0) {
				qIdMap.put(key, list);
			}
		}

		result = qIdMap;
		return result;
	}

	private void setUserQuestionboxWrongByHmget(Long uid, Long boxId, Long teachBookId, List<Long> objIds, Map<Long, String> responseMap, Integer objType) {
		List<String> fileds = Lists.newArrayList();
		for (Long objId : objIds) {
			if (objType == 1){
				fileds.add(NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, objId));
			} else if (objType == 2){
				fileds.add(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, objId));
			} else if (objType == 3){
				fileds.add(NewRedisConsts.getUserQuestionboxWrongQtype(objId.intValue()));
			}
		}

		String[] array = fileds.toArray(new String[0]);
		List<String> resultList = compatableRedisClusterClient.hmget(NewRedisConsts.getUserQuestionboxWrong(uid, boxId), array);
		if (CollectionUtils.isNotEmpty(resultList)){
			for (int i = 0; i < objIds.size(); i++){
				String resultStr = resultList.get(i);
				if (!"nil".equals(resultStr)){
					responseMap.put(objIds.get(i), resultStr);
				}
			}
		}
	}


	/**
	 * 获取用户已消灭做错的题目List
	 * */
	private List<Long> getWipeOutWrongQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType) throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {}.getType();
		Set<Long> filterQuestionIds = new HashSet<Long>();
		switch (objType) {
			case 1://章节
				String questionCpListStr = this.getUserWipeOutWrongQuestions(uid, boxId,
						NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, objId));
				if (StringUtils.isNotBlank(questionCpListStr)) {
					List<Long> qIdCpList = GsonUtil.getGson().fromJson(questionCpListStr, type);
					filterQuestionIds.addAll(qIdCpList);
				}
				break;
			case 2://知识点
				String questionKlgListStr = this.getUserWipeOutWrongQuestions(uid, boxId,
						NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, objId));
				if (StringUtils.isNotBlank(questionKlgListStr)) {
					List<Long> qIdKlgList = GsonUtil.getGson().fromJson(questionKlgListStr, type);
					filterQuestionIds.addAll(qIdKlgList);
				}
				break;
		}
		// 过滤掉关闭的错题
		Map<Long, Integer> questionStatMap = knowledgeResource.getQuestionStateByIdList(new ArrayList<Long>(filterQuestionIds));
		List<Long> resultUserWrongList = new ArrayList<Long>();
		Integer state = null;
		for (Long aLong : filterQuestionIds) {
			state = questionStatMap.get(aLong);
			if(null != state && state.equals(Consts.QuestionState.VALID)){
				resultUserWrongList.add(aLong);
			}
		}
		return resultUserWrongList;
	}

	/**
	 * 获取周用户做错的题目List
	 * */
	private List<Long> getWeekWrongQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType, Long reportWeekNum) throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {}.getType();
		Set<Long> filterQuestionIds = new HashSet<Long>();
		switch (objType) {
			case 1://章节
				String questionCpListStr = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum),
						NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, objId));
				if (StringUtils.isNotBlank(questionCpListStr)) {
					List<Long> qIdCpList = GsonUtil.getGson().fromJson(questionCpListStr, type);
					filterQuestionIds.addAll(qIdCpList);
				}
				break;
			case 2://知识点
				String questionKlgListStr = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum),
						NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, objId));
				if (StringUtils.isNotBlank(questionKlgListStr)) {
					List<Long> qIdKlgList = GsonUtil.getGson().fromJson(questionKlgListStr, type);
					filterQuestionIds.addAll(qIdKlgList);
				}
				break;
		}
		// 过滤掉关闭的错题
		Map<Long, Integer> questionStatMap = knowledgeResource.getQuestionStateByIdList(new ArrayList<Long>(filterQuestionIds));
		List<Long> resultUserWrongList = new ArrayList<Long>();
		Integer state = null;
		for (Long aLong : filterQuestionIds) {
			state = questionStatMap.get(aLong);
			if(null != state && state.equals(Consts.QuestionState.VALID)){
				resultUserWrongList.add(aLong);
			}
		}
		return resultUserWrongList;
	}

	/**
	 * 获取用户做过的题目List
	 * */
	@Override
	public List<Long> getUserDoneQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType) throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {
		}.getType();
		Set<Long> filterQuestionIds = new HashSet<Long>();
		switch (objType) {
			case 0://所有
                checkAndReloadDoneQuestions(uid, boxId);
				String[] fields = {NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.DAN_XUAN),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.BU_DING_XUAN),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.DUO_XUAN),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.PAN_DUAN),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.TIAN_KONG),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.WEN_DA)
				};
				//所有题目即所有题型的题目
				List<String> allQIdList = compatableRedisClusterClient.hmget(NewRedisConsts.getUserQuestionboxDone(uid, boxId), fields);

				if(null != allQIdList && !allQIdList.isEmpty()) {
					for (String qIdsStr : allQIdList) {
						if (StringUtils.isNotBlank(qIdsStr)) {
							List<Long> qidList = GsonUtil.getGson().fromJson(qIdsStr, type);
							filterQuestionIds.addAll(qidList);
						}
					}
				}


				break;
			case 1://章节
				String questionCpListStr = this.getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId, objId));
				if (StringUtils.isNotBlank(questionCpListStr)) {
					List<Long> qIdCpList = GsonUtil.getGson().fromJson(questionCpListStr, type);
					filterQuestionIds.addAll(qIdCpList);
				}
				break;
			case 2://知识点
				String questionKlgListStr = this.getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, objId));
				if (StringUtils.isNotBlank(questionKlgListStr)) {
					List<Long> qIdKlgList = GsonUtil.getGson().fromJson(questionKlgListStr, type);
					filterQuestionIds.addAll(qIdKlgList);
				}
				break;
			case 3://题型
				String questionQtypeListStr = this.getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneQtype(objId.intValue()));
				if (StringUtils.isNotBlank(questionQtypeListStr)) {
					List<Long> qIdQtypeList = GsonUtil.getGson().fromJson(questionQtypeListStr, type);
					if (qIdQtypeList != null && qIdQtypeList.size() > 0) {
						filterQuestionIds.addAll(qIdQtypeList);
					}
				}
				break;
			default:
				break;
		}


		return new ArrayList<Long>(filterQuestionIds);
	}

	/**
	 * 获取用户做过的题目List
	 * */
	public Map<Long, List<Long>> getUserDoneQuestionIdListBatch(Long uid, Long boxId, Long teachBookId, List<Long> objIds, Integer objType) throws DataAccessException{
		if(objIds == null || objIds.size() == 0){
			return Maps.newHashMap();
		}
		Type type = new TypeToken<List<Long>>() {}.getType();
		Map<Long, List<Long>> result = Maps.newHashMap();
		Map<Long, String> responseMap = Maps.newHashMap();
		HashMap<Long, List<Long>> qIdMap = new HashMap<Long, List<Long>>();
		List<Long> idsList = Lists.newArrayList();
		switch (objType) {
			case 0://所有
				checkAndReloadDoneQuestionsBatch(uid, objIds);
				String[] fields = {NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.DAN_XUAN),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.BU_DING_XUAN),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.DUO_XUAN),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.PAN_DUAN),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.TIAN_KONG),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.WEN_DA),
						NewRedisConsts.getUserQuestionboxDoneQtype(Consts.Question_QType.AN_LI_FEN_XI)
				};

				//所有题目即所有题型的题目
				HashMap<Long, List<Long>> qIdRedisMap = new HashMap<Long, List<Long>>();
				Map<String, List<String>> responses = new HashMap<String, List<String>>(objIds.size());

				for (Long objId : objIds) {
					responses.put(String.valueOf(objId), compatableRedisClusterClient.hmget(NewRedisConsts.getUserQuestionboxDone(uid, objId), fields));
				}

				if (responses != null && !responses.isEmpty()) {
					for (String key : responses.keySet()) {
						idsList.clear();
						List<String> allList = responses.get(key);
						if (allList != null) {
							for (String idstr : allList) {
								List<Long> list = GsonUtil.getGson().fromJson(idstr, type);
								if (list != null && list.size() > 0) {
									idsList.addAll(list);
								}
							}
						}
						if (idsList != null && idsList.size() > 0) {
							List<Long> listNew = new ArrayList<Long>(new HashSet(idsList));
							qIdRedisMap.put(Long.parseLong(key), listNew);
						}
					}
				}

				result = qIdRedisMap;
				break;
			case 1://章节
				checkAndReloadDoneQuestions(uid, boxId);
				setUserQuestionboxDoneByHmget(uid, boxId, teachBookId, objIds, responseMap, objType);
				result = getLongListMap(responseMap, type, qIdMap, result);
				break;
			case 2://知识点
				checkAndReloadDoneQuestions(uid, boxId);
				setUserQuestionboxDoneByHmget(uid, boxId, teachBookId, objIds, responseMap, objType);
				result = getLongListMap(responseMap, type, qIdMap, result);
				break;
			case 3://题型
				checkAndReloadDoneQuestions(uid, boxId);
				setUserQuestionboxDoneByHmget(uid, boxId, teachBookId, objIds, responseMap, objType);
				result = getLongListMap(responseMap, type, qIdMap, result);
				break;
			default:
				break;
		}
		return result;
	}

	private void setUserQuestionboxDoneByHmget(Long uid, Long boxId, Long teachBookId, List<Long> objIds, Map<Long, String> responseMap, Integer objType) throws DataAccessException {
		List<String> fileds = Lists.newArrayList();
		for (Long objId : objIds) {
			if (objType == 1){
				fileds.add(NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId, objId));
			} else if (objType == 2){
				fileds.add(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, objId));
			} else if (objType == 3){
				fileds.add(NewRedisConsts.getUserQuestionboxDoneQtype(objId.intValue()));
			}
		}

		String[] array = fileds.toArray(new String[0]);
		List<String> resultList = compatableRedisClusterClient.hmget(NewRedisConsts.getUserQuestionboxDone(uid, boxId), array);
		if (CollectionUtils.isNotEmpty(resultList)){
			for (int i = 0; i < objIds.size(); i++){
				String resultStr = resultList.get(i);
				if (!"nil".equals(resultStr)){
					responseMap.put(objIds.get(i), resultStr);
				}
			}
		}
	}

	/**
	 * 获取周用户做过的题目List
	 * */
	private List<Long> getWeekDoneQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType, Long reportWeekNum) throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {}.getType();
		Set<Long> filterQuestionIds = new HashSet<Long>();
		switch (objType) {
			case 1://章节
				String questionCpListStr = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum),
						NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId, objId));
				if (StringUtils.isNotBlank(questionCpListStr)) {
					List<Long> qIdCpList = GsonUtil.getGson().fromJson(questionCpListStr, type);
					filterQuestionIds.addAll(qIdCpList);
				}

				break;
			case 2://知识点
				String questionKlgListStr = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum),
						NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, objId));
				if (StringUtils.isNotBlank(questionKlgListStr)) {
					List<Long> qIdKlgList = GsonUtil.getGson().fromJson(questionKlgListStr, type);
					filterQuestionIds.addAll(qIdKlgList);
				}
				break;
		}
		return new ArrayList<Long>(filterQuestionIds);
	}

	/**
	 * 用户错题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 *
	 * */
	@Override
	public void cacheUserBoxWrongQuestion(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds, List<Long> topicIds) throws DataAccessException{
		if (CollectionUtils.isEmpty(questionIds)) {
			return;
		}
		if (teachBookId == null) {
			TeachingBook teachingBook = this.getBookByBoxId(boxId);
			if (teachingBook == null) {
				logger.error("teachingBook null,boxId:{}",boxId);
				return;
			} else {
				teachBookId = teachingBook.getId();
			}
		}
		//处理章节错题
		Map<String, String> chapterQuestionMap = dealUserBoxWrongQuestion4Chapter(uid, homeworkId, teachBookId, boxId, questionIds);
		//处理知识点错题
        Map<String, String> knowledgeQuestionMap = dealUserBoxWrongQuestion4Knowledge(uid, homeworkId, teachBookId, boxId, questionIds);
		//处理题型错题
        Map<String, String> typeQuestionMap = dealUserBoxWrongQuestion4Qtype(uid,null, boxId, questionIds, topicIds);
        logger.debug("cacheUserBoxWrongQuestion info,"
							+ "uid:{},homeworkId:{},boxId:{},questionIds:{},chapterQuestionMap:{},knowledgeQuestionMap:{},typeQuestionMap:{},"
					,uid,homeworkId,boxId,GsonUtil.toJson(questionIds),GsonUtil.toJson(chapterQuestionMap),GsonUtil.toJson(knowledgeQuestionMap),GsonUtil.toJson(typeQuestionMap));
		HashMap<String,String> allMap = Maps.newHashMap();
		allMap.putAll(chapterQuestionMap);
		allMap.putAll(knowledgeQuestionMap);
		allMap.putAll(typeQuestionMap);
		saveUserWrongQuestions(uid, boxId, allMap);
	}

	/**
	 * 周用户错题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 *
	 * */
	@Override
	public void cacheWeekWrongQuestion(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds, Long reportWeekNum) throws DataAccessException{
		//处理周章节错题
		Boolean errQ4Chapter = dealWeekWrongQuestion4Chapter(uid, homeworkId, teachBookId, boxId, questionIds, reportWeekNum);
		//处理知识点错题
		Boolean errQ4Knowledge = dealWeekUserBoxWrongQuestion4Knowledge(uid, homeworkId, teachBookId, boxId, questionIds, reportWeekNum);
		if (!errQ4Chapter || !errQ4Knowledge) {
			logger.warn("cacheWeekWrongQuestion error,"
							+ "uid:{},homeworkId:{},boxId:{},questionIds:{},errQ4Chapter:{},errQ4Knowledge:{}"
					,uid,homeworkId,boxId,GsonUtil.toJson(questionIds),errQ4Chapter,errQ4Knowledge);
		}
	}

	/**
	 * 处理用户错题-题型存储
     * @param wrongTopicIds
     * */
	@Override
	public Map<String, String> dealUserBoxWrongQuestion4Qtype(Long uid,Long teachBookId, Long boxId, List<Long> questionIds, List<Long> wrongTopicIds)throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {
		}.getType();

        Map<Integer, List<Long>> questionMap = getTypeQuestionMap(questionIds, wrongTopicIds);
		//针对每个题型去更新redis
        HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		Iterator questionMapsIterator = questionMap.entrySet().iterator();
		while (questionMapsIterator.hasNext()) {
			Entry questionEntry = (Entry)questionMapsIterator.next();

			Integer qtype = Integer.valueOf(questionEntry.getKey().toString());
			String qidStr = questionEntry.getValue().toString();

			List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);

			//redis中已有的错题
			String redisQIds = this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(qtype));

			if (StringUtils.isBlank(redisQIds)) {
				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongQtype(qtype), GsonUtil.toJson(new HashSet(qidList)));
				//用户有新增错题
				if (teachBookId != null){
					String hasNewWrongKey = "has_new_wrong_quesiton_uid"+uid+"_teachBookId:"+teachBookId;
					compatableRedisClusterClient.set(hasNewWrongKey,"1");
					// 用户查询时会删除，为防止用户一直不查询而一直占用缓存，设置一个较长的过期时间
					compatableRedisClusterClient.expire(hasNewWrongKey, 60*60*24*30/*30天过期*/);
				}
			}else {
				List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
				HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
				redisQIdSet.addAll(qidList);
				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongQtype(qtype), GsonUtil.toJson(redisQIdSet));
				if (redisQIdList.size()<redisQIdSet.size() && teachBookId!=null){
					//判断用户是否有新增错题,存储用户错题状态
					String hasNewWrongKey = "has_new_wrong_quesiton_uid"+uid+"_teachBookId:"+teachBookId;
					compatableRedisClusterClient.set(hasNewWrongKey,"1");
					// 用户查询时会删除，为防止用户一直不查询而一直占用缓存，设置一个较长的过期时间
					compatableRedisClusterClient.expire(hasNewWrongKey, 60*60*24*30/*30天过期*/);
				}
			}
		}
		return qIdRedisMap;
	}

	private Map<Integer, List<Long>> getTypeQuestionMap(List<Long> questionIds, List<Long> topicIds) {
        //首先根据题目id获取到题目信息
        List<Question> questions = knowledgeResource.getQuestionByIds(questionIds);
        HashMap<Integer, List<Long>> questionMap = new HashMap<Integer, List<Long>>();

        //根据qType来区分每个题目
        for (Question question : questions) {
            if (question.getIsMulti()!=null && question.getIsMulti().intValue() == 0) {//对单题进行计算
                Integer qtype = question.getQtype();
                Long questionId = question.getId();
                List<Long> qIdList = null;
                if (questionMap.containsKey(qtype)) {
                    qIdList = questionMap.get(qtype);
                    qIdList.add(questionId);
                }else {
                    qIdList = new ArrayList<Long>();
                    qIdList.add(questionId);
                }
                questionMap.put(qtype, qIdList);
            }else if (question.getIsMulti()!=null && question.getIsMulti().intValue() == 1) {
                //对于多题进行计算
                //针对每个大题中的小题，进行归类；若小题A是单选题，小题B是多选题，那这道大题既算单选，也算多选
                //但错误归属，需要归属到小题上
                List<QuestionTopic> topics = question.getTopicList();
                for (QuestionTopic questionTopic : topics) {
                    if (topicIds == null || topicIds.contains(questionTopic.getId())) {//判断当前子题id是否存在于错误子题id集合中
                        Integer qtype = questionTopic.getQtype();
                        Long questionId = question.getId();// TODO 这里存的是question的Id 而不是QuestionTopic的id
                        List<Long> qIdList = null;
                        if (questionMap.containsKey(qtype)) {
                            qIdList = questionMap.get(qtype);
                            qIdList.add(questionId);
                        }else {
                            qIdList = new ArrayList<Long>();
                            qIdList.add(questionId);
                        }
                        questionMap.put(qtype, qIdList);
                    }
                }
            }
        }
        return  questionMap;
    }
	/**
	 * 处理用户错题-章节存储
	 * */
	private Map<String, String> dealUserBoxWrongQuestion4Chapter(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds)throws DataAccessException{
        HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		//首先根据作业Id，获取到这份作业对应的章节id
		VirtualHomework homework = userVirtualHomeworkDao.get(homeworkId, uid);
		if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Chapter.intValue()) {
			Long chapterId = homework.getHomeworkTypeId();
			ChapterSection chapterSection = knowledgeResource.getChapterSectionItemById(chapterId);
			if (chapterSection != null) {
				qIdRedisMap.putAll(dealWrongChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds));
			}else {
				logger.warn("dealUserBoxWrongQuestion4Chapter get chapter is null.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",homeworkId,uid,homework.getHomeworkType(),homework.getHomeworkTypeId());
			}

		}else if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Knowledge.intValue()) {
			//好吧，这份练习是知识点练习，只能换个角度去拿章节id了

			//首先拿到知识点id
			Long knowledgeId = homework.getHomeworkTypeId();

			//根据知识点id与教材id，查询该教材下，包含该知识点的章节id
			List<ChapterSection> chapterSections = knowledgeResource.getChapterSectionItemByKnowledgeIdAndBookId(teachBookId,knowledgeId);

			if (chapterSections!=null && chapterSections.size()>0) {

				for (ChapterSection chapterSection : chapterSections) {
					qIdRedisMap.putAll(dealWrongChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds));
				}
			}

		}else {
			// 记录用户做错的题目
			qIdRedisMap.putAll(this.getPaperWrongQuestionMapByType(uid, teachBookId, boxId, new ArrayList<Long>(questionIds),Consts.Question_Exercise_Type.Chapter.intValue()));
		}
		return qIdRedisMap;
	}

	/**
	 * 处理用户周错题-章节存储
	 * */
	private boolean dealWeekWrongQuestion4Chapter(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds, Long reportWeekNum)throws DataAccessException{
		//首先根据作业Id，获取到这份作业对应的章节id
		VirtualHomework homework = userVirtualHomeworkDao.get(homeworkId, uid);
		if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Chapter.intValue()) {
			Long chapterId = homework.getHomeworkTypeId();
			ChapterSection chapterSection = knowledgeResource.getChapterSectionItemById(chapterId);
			if (chapterSection != null) {
				HashMap<String, String> qIdRedisMap = new HashMap<String, String>();

				qIdRedisMap.putAll(dealWeekWrongChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds, reportWeekNum));
				if (qIdRedisMap!=null && qIdRedisMap.size() != 0) {
					compatableRedisClusterClient.hmset(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), qIdRedisMap);
					compatableRedisClusterClient.expire(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), expTime.intValue());
				}
			}else {
				logger.warn("dealWeekWrongQuestion4Chapter get chapter is null.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",homeworkId,uid,homework.getHomeworkType(),homework.getHomeworkTypeId());
			}
		}else if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Knowledge.intValue()) {
			//好吧，这份练习是知识点练习，只能换个角度去拿章节id了
			//首先拿到知识点id
			Long knowledgeId = homework.getHomeworkTypeId();

			//根据知识点id与教材id，查询该教材下，包含该知识点的章节id
			List<ChapterSection> chapterSections = knowledgeResource.getChapterSectionItemByKnowledgeIdAndBookId(teachBookId,knowledgeId);

			if (chapterSections!=null && chapterSections.size()>0) {
				HashMap<String, String> qIdRedisMap = new HashMap<String, String>();

				for (ChapterSection chapterSection : chapterSections) {
					qIdRedisMap.putAll(dealWeekWrongChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds, reportWeekNum));
				}
				if (qIdRedisMap!=null && qIdRedisMap.size() != 0) {
					compatableRedisClusterClient.hmset(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), qIdRedisMap);
					compatableRedisClusterClient.expire(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), expTime.intValue());
				}
			} else {
				logger.warn("dealWeekWrongQuestion4Chapter knowledge get chapter is null.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",
						homeworkId,uid,homework.getHomeworkType(),homework.getHomeworkTypeId());
			}
			return true;
		}else {
			logger.warn("dealWeekWrongQuestion4Chapter not chapter exercise.homeworkId:{},uid:{},homework:{}",
					homeworkId,uid,GsonUtil.toJson(homework));
			return false;
		}
		return true;
	}

	/**
	 * 相比之前的方法，摒弃了递归调用啦！
	 * */
	@Override
	public Map<String, String> dealWrongChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds){
		return dealWrongChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds, false);
	}
	private Map<String, String> dealWrongChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds, boolean isSync){
		HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		Type type = new TypeToken<List<Long>>() {
		}.getType();

		try {
			Set<Long> needDealChapter = new HashSet<Long>();

			Long chapterSectionId = chapterSection.getId();
			needDealChapter.add(chapterSectionId);

			// 获取当前章节的父节点
			String parentIdsStr = chapterSection.getParentIds();
			parentIdsStr = parentIdsStr.substring(0, parentIdsStr.length()-1);
			for (String parendIdStr : parentIdsStr.split(",")) {
				if (!parendIdStr.equals("0")) {
					needDealChapter.add(Long.valueOf(parendIdStr));
				}
			}

			for (Long chapterId : needDealChapter) {
				//取出用户该章节的错题
				String redisQIds = "";
				if (!isSync) {
					redisQIds = getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId,chapterId));
				}
				if (StringUtils.isBlank(redisQIds)) {
					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId,chapterId), GsonUtil.toJson(questionIds));

				}else {
					List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
					HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
					redisQIdSet.addAll(questionIds);

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId,chapterId), GsonUtil.toJson(redisQIdSet));
				}
			}

			// 获取当前章节的子节点
			List<ChapterSection> chapterSections = knowledgeResource.getChildChapterSectionItemById(chapterSectionId);
			if (chapterSections != null && chapterSections.size() > 0) {
				Set<Long> needDealChildChapter = new HashSet<Long>();
				needDealChildChapter.addAll(Collections3.extractToList(chapterSections, "id"));

				// 计算questionIds的归属 start
				HashMap<Long, List<Long>> chapterQuestionMap = new HashMap<Long, List<Long>>();
				HashMap<Long, List<Long>> userCpQuestionMap = new HashMap<Long, List<Long>>();

				// 取出每个子章节下的题目集合,以及每个子章节下用户做错题目的集合
				for (Long childChapterId : needDealChildChapter) {
					// 获取该子章节下的题目集合
					String childChapterQidStr =  knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId,
							NewRedisConsts.getTeachbookQuestionboxChapterFiled(childChapterId));
					if (StringUtils.isNotBlank(childChapterQidStr)) {
						List<Long> childChapterQids = GsonUtil.getGson().fromJson(childChapterQidStr, type);
						chapterQuestionMap.put(childChapterId, childChapterQids);
					}

					// 获取该子章节下用户做错的题目集合
					String redisQIds = "";
					if (!isSync) {
						redisQIds = getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, childChapterId));
					}
					if (StringUtils.isNotBlank(redisQIds)) {
						List<Long> userChildCpQids = GsonUtil.getGson().fromJson(redisQIds, type);
						userCpQuestionMap.put(childChapterId, userChildCpQids);
					}else {
						userCpQuestionMap.put(childChapterId, new ArrayList<Long>());
					}

				}

				// 循环判断每个题目
				for (Long qId : questionIds) {
					Iterator chapterQuestionIterator = chapterQuestionMap.entrySet().iterator();
					while (chapterQuestionIterator.hasNext()) {
						Entry<Long, List<Long>> questionEntry = (Entry)chapterQuestionIterator.next();
						Long chapterId = Long.valueOf(questionEntry.getKey().toString());
						List<Long> qidList = questionEntry.getValue();

						if (qidList.contains(qId)) {// 该题目属于该章节的题目集合中
							userCpQuestionMap.get(chapterId).add(qId);
						}
					}
				}
				// 计算questionIds的归属 end

				// 将更新好的子节点的作答数据更新到qIdRedisMap
				Iterator userCpQuestionIterator = userCpQuestionMap.entrySet().iterator();
				while (userCpQuestionIterator.hasNext()) {
					Entry<Long, List<Long>> userCpQuestionEntry = (Entry<Long, List<Long>>)userCpQuestionIterator.next();
					Long chapterId = Long.valueOf(userCpQuestionEntry.getKey().toString());
					List<Long> qidList = userCpQuestionEntry.getValue();

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId,chapterId), GsonUtil.toJson(new HashSet<Long>(qidList)));
				}
			}

		} catch (Exception e) {
			logger.error("dealWrongChapterData",e);
		}
		return qIdRedisMap;
	}

	/**
	 * 处理周用户章节数据
	 * */
	private HashMap<String, String> dealWeekWrongChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds, Long reportWeekNum){
		HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		Type type = new TypeToken<List<Long>>() {}.getType();

		try {
			Set<Long> needDealChapter = new HashSet<Long>();

			Long chapterSectionId = chapterSection.getId();
			needDealChapter.add(chapterSectionId);

			// 获取当前章节的父节点
			String parentIdsStr = chapterSection.getParentIds();
			parentIdsStr = parentIdsStr.substring(0, parentIdsStr.length()-1);
			for (String parendIdStr : parentIdsStr.split(",")) {
				if (!parendIdStr.equals("0")) {
					needDealChapter.add(Long.valueOf(parendIdStr));
				}
			}

			for (Long chapterId : needDealChapter) {
				//取出用户该章节的错题
				String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, chapterId));
				if (StringUtils.isBlank(redisQIds)) {
					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, chapterId), GsonUtil.toJson(questionIds));
				}else {
					List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
					HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
					redisQIdSet.addAll(questionIds);

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, chapterId), GsonUtil.toJson(redisQIdSet));
				}
			}

			// 获取当前章节的子节点
			List<ChapterSection> chapterSections = knowledgeResource.getChildChapterSectionItemById(chapterSectionId);
			if (chapterSections != null && chapterSections.size() > 0) {
				Set<Long> needDealChildChapter = new HashSet<Long>();
				needDealChildChapter.addAll(Collections3.extractToList(chapterSections, "id"));

				// 计算questionIds的归属 start
				HashMap<Long, List<Long>> chapterQuestionMap = new HashMap<Long, List<Long>>();
				HashMap<Long, List<Long>> userCpQuestionMap = new HashMap<Long, List<Long>>();

				// 取出每个子章节下的题目集合,以及每个子章节下用户做错题目的集合
				for (Long childChapterId : needDealChildChapter) {
					// 获取该子章节下的题目集合
					String childChapterQidStr =  knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId,
							NewRedisConsts.getTeachbookQuestionboxChapterFiled(childChapterId));
					if (StringUtils.isNotBlank(childChapterQidStr)) {
						List<Long> childChapterQids = GsonUtil.getGson().fromJson(childChapterQidStr, type);
						chapterQuestionMap.put(childChapterId, childChapterQids);
					}

					// 获取该子章节下用户做错的题目集合
					String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, childChapterId));
					if (StringUtils.isNotBlank(redisQIds)) {
						List<Long> userChildCpQids = GsonUtil.getGson().fromJson(redisQIds, type);
						userCpQuestionMap.put(childChapterId, userChildCpQids);
					}else {
						userCpQuestionMap.put(childChapterId, new ArrayList<Long>());
					}
				}

				// 循环判断每个题目
				for (Long qId : questionIds) {
					Iterator chapterQuestionIterator = chapterQuestionMap.entrySet().iterator();
					while (chapterQuestionIterator.hasNext()) {
						Entry<Long, List<Long>> questionEntry = (Entry)chapterQuestionIterator.next();
						Long chapterId = Long.valueOf(questionEntry.getKey().toString());
						List<Long> qidList = questionEntry.getValue();

						if (qidList.contains(qId)) {// 该题目属于该章节的题目集合中
							userCpQuestionMap.get(chapterId).add(qId);
						}
					}
				}
				// 计算questionIds的归属 end

				// 将更新好的子节点的作答数据更新到qIdRedisMap
				Iterator userCpQuestionIterator = userCpQuestionMap.entrySet().iterator();
				while (userCpQuestionIterator.hasNext()) {
					Entry<Long, List<Long>> userCpQuestionEntry = (Entry<Long, List<Long>>)userCpQuestionIterator.next();
					Long chapterId = Long.valueOf(userCpQuestionEntry.getKey().toString());
					List<Long> qidList = userCpQuestionEntry.getValue();

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongChapter(teachBookId, chapterId), GsonUtil.toJson(new HashSet<Long>(qidList)));
				}
			}
		} catch (Exception e) {
			logger.error("dealWeekWrongChapterData",e);
		}
		return qIdRedisMap;
	}


	/**
	 * 处理用户错题-知识点存储
	 * */
	private Map<String, String> dealUserBoxWrongQuestion4Knowledge(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds)throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {
		}.getType();
        HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		//首先根据作业Id，获取到这份作业对应的知识点id
		VirtualHomework homework = userVirtualHomeworkDao.get(homeworkId, uid);
		if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Knowledge.intValue()) {
			Long KnowledgeId = homework.getHomeworkTypeId();

			//取出用户该知识点的错题
			String redisQIds = getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId));
			if (StringUtils.isBlank(redisQIds)) {
				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(questionIds));
			}else {
				List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
				HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
				redisQIdSet.addAll(questionIds);

				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(redisQIdSet));
			}
		}else if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Chapter.intValue()) {

			//好吧，这份练习是章节练习，只能换个角度去拿知识点id了

			//首先先根据章节id拿到 这个章节下的所有知识点Id(包括自身的知识点，以及子节点的知识点)
			Long chapterId = homework.getHomeworkTypeId();
            qIdRedisMap.putAll(dealWrongKnowledgeDataNew(uid, chapterId, teachBookId, boxId, questionIds));
		}else {
			// 记录用户做错的题目
			qIdRedisMap.putAll(this.getPaperWrongQuestionMapByType(uid, teachBookId, boxId, new ArrayList<Long>(questionIds),Consts.Question_Exercise_Type.Knowledge.intValue()));
		}
		return qIdRedisMap;
	}

    private Map<String, String> dealWrongKnowledgeDataNew(Long uid, Long chapterId, Long teachBookId, Long boxId, List<Long> questionIds) throws DataAccessException {
        HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
        Type type = new TypeToken<List<Long>>() {}.getType();
        HashMap<Long, List<Long>> userDoInKlg = getKnowledgeDoneQuestionMap(chapterId,boxId,teachBookId,questionIds);

        if (userDoInKlg.size()>0) {//把梳理出来的知识点题目关系入到用户做过的知识点题目缓存中

            Iterator userDoInKlgIter = userDoInKlg.entrySet().iterator();
            while (userDoInKlgIter.hasNext()) {
                Entry userDoInKlgEntry = (Entry)userDoInKlgIter.next();
                String qidStr = userDoInKlgEntry.getValue().toString();

                Long KnowledgeId = Long.valueOf(userDoInKlgEntry.getKey().toString());
                List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);

                //取出用户该知识点的做错的题
                String redisQIds = getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId,KnowledgeId));
                if (StringUtils.isBlank(redisQIds)) {
                    qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId,KnowledgeId), GsonUtil.toJson(qidList));
                }else {
                    List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
                    HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
                    redisQIdSet.addAll(qidList);

                    qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(redisQIdSet));
                }
            }
        }

        return qIdRedisMap;
    }

	/**
	 * 处理周用户错题-知识点存储
	 * */
	private boolean dealWeekUserBoxWrongQuestion4Knowledge(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds, Long reportWeekNum)throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {}.getType();
		//首先根据作业Id，获取到这份作业对应的知识点id
		VirtualHomework homework = userVirtualHomeworkDao.get(homeworkId, uid);
		if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Knowledge.intValue()) {
			Long KnowledgeId = homework.getHomeworkTypeId();

			HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
			//取出用户该知识点的错题
			String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId));
			if (StringUtils.isBlank(redisQIds)) {
				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(questionIds));
			}else {
				List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
				HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
				redisQIdSet.addAll(questionIds);

				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(redisQIdSet));
			}
			if (qIdRedisMap!=null && qIdRedisMap.size() != 0) {
				compatableRedisClusterClient.hmset(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), qIdRedisMap);
				compatableRedisClusterClient.expire(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), expTime.intValue());
			}
		}else if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Chapter.intValue()) {

			//好吧，这份练习是章节练习，只能换个角度去拿知识点id了

			//首先先根据章节id拿到 这个章节下的所有知识点Id(包括自身的知识点，以及子节点的知识点)
			Long chaperId = homework.getHomeworkTypeId();
			Set<Long> knowledgeIds = new HashSet<Long>();

			// 获取自身的知识点id
			List<KnowledgeGraph> knowledges = knowledgeResource.getKnowledgeGraphByChapterId(chaperId);
			if (knowledges!=null && !knowledges.isEmpty()) {
				for (KnowledgeGraph knowledge : knowledges) {
					knowledgeIds.add(knowledge.getId());
				}
			}

			// 获取所有子节点的知识点id
			List<ChapterSection> childs = knowledgeResource.getChildChapterSectionItemById(chaperId);
			if (childs != null && childs.size() > 0) {
				for (ChapterSection childChapterion : childs) {
					List<KnowledgeGraph> klgs = knowledgeResource.getKnowledgeGraphByChapterId(childChapterion.getId());
					if (klgs!=null && !klgs.isEmpty()) {
						for (KnowledgeGraph knowledge : klgs) {
							knowledgeIds.add(knowledge.getId());
						}
					}
				}
			}

			HashMap<Long, List<Long>> knowledgeQuestionMap = new HashMap<Long, List<Long>>();
			//循环每个知识点Id，拿到每个知识点下的题目
			for (Long knowledgeId : knowledgeIds) {
				String knowledgeQuestionIdStr =  knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId,
						NewRedisConsts.getTeachbookQuestionboxKnowledgeFiled(knowledgeId));

				if (StringUtils.isNotBlank(knowledgeQuestionIdStr)) {
					List<Long> knowledgeQuestionIds = GsonUtil.getGson().fromJson(knowledgeQuestionIdStr, type);
					knowledgeQuestionMap.put(knowledgeId, knowledgeQuestionIds);
				}
			}

			//判断用户本次做的题目是属于哪几个知识点的
			if (knowledgeQuestionMap.size() >0) {

				HashMap<Long, List<Long>> userDoInKlg = new HashMap<Long, List<Long>>();//key:knowledgeId,value:qIdList
				//循环判断题目是属于哪个知识点
				for (Long userDoQuestionId : questionIds) {
					Iterator questionMapsIterator = knowledgeQuestionMap.entrySet().iterator();
					while (questionMapsIterator.hasNext()) {
						Entry questionEntry = (Entry)questionMapsIterator.next();
						Long knowledgeId = Long.valueOf(questionEntry.getKey().toString());
						String qidStr = questionEntry.getValue().toString();
						List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);

						if (qidList.contains(userDoQuestionId)) {
							if (userDoInKlg.containsKey(knowledgeId)) {
								List<Long> userDoQuestionList = userDoInKlg.get(knowledgeId);
								userDoQuestionList.add(userDoQuestionId);
								userDoInKlg.put(knowledgeId, userDoQuestionList);
							}else {
								List<Long> userDoQuestionList = new ArrayList<Long>();
								userDoQuestionList.add(userDoQuestionId);
								userDoInKlg.put(knowledgeId, userDoQuestionList);
							}
						}
					}
				}

				if (userDoInKlg.size()>0) {//把梳理出来的知识点题目关系入到用户做过的知识点题目缓存中
					HashMap<String, String> qIdRedisMap = new HashMap<String, String>();

					Iterator userDoInKlgIter = userDoInKlg.entrySet().iterator();
					while (userDoInKlgIter.hasNext()) {
						Entry userDoInKlgEntry = (Entry)userDoInKlgIter.next();
						String qidStr = userDoInKlgEntry.getValue().toString();

						Long KnowledgeId = Long.valueOf(userDoInKlgEntry.getKey().toString());
						List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);

						//取出用户该知识点的已做错的题
						String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum),
								NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId));
						if (StringUtils.isBlank(redisQIds)) {
							qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(qidList));
						}else {
							List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
							HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
							redisQIdSet.addAll(qidList);

							qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(redisQIdSet));
						}
					}
					if (qIdRedisMap!=null && qIdRedisMap.size() != 0) {
						compatableRedisClusterClient.hmset(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), qIdRedisMap);
						compatableRedisClusterClient.expire(NewRedisConsts.getWeekUserQuestionboxWrong(uid, boxId, reportWeekNum), expTime.intValue());
					}
				}

			}
			return true;
		}else {
			boolean isnull = Objects.isNull(homework);
			logger.warn("dealWeekUserBoxWrongQuestion4Knowledge not chapter exercise.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",
					homeworkId,uid, isnull ? null : homework.getHomeworkType(), isnull ? null : homework.getHomeworkTypeId());
			return false;
		}
		return true;
	}

	/**
	 * 用户做过的题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param teachBookId 教材id
	 * @param questionIds 题目id列表
	 *
	 * */
	@Override
	public void cacheUserBoxDoneQuestion(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds) throws DataAccessException{
		if (teachBookId == null) {
			TeachingBook teachingBook = this.getBookByBoxId(boxId);
			if (teachingBook == null) {
				logger.error("teachingBook null,boxId:{}",boxId);
				return;
			} else {
				teachBookId = teachingBook.getId();
			}
		}
		//处理章节关系
		Map<String,String> chapterQuestionMap = dealUserBoxDoneQuestion4Chapter(uid, homeworkId, teachBookId, boxId, questionIds);
		//处理知识点关系
        Map<String,String> knowledgeQuestionMap = dealUserBoxDoneQuestion4Knowledge(uid, homeworkId, teachBookId, boxId, questionIds);
		//处理题型关系
        Map<String,String> typeQuestionMap = dealUserBoxDoneQuestion4Qtype(uid, boxId, questionIds);

		logger.debug("cacheUserBoxDoneQuestion result,"
						+ "uid:{},homeworkId:{},teachBookId:{},boxId:{},questionIds:{},chapterQuestionMap:{},knowledgeQuestionMap:{},typeQuestionMap:{},"
				, uid, homeworkId, teachBookId, boxId, GsonUtil.toJson(questionIds), GsonUtil.toJson(chapterQuestionMap), GsonUtil.toJson(knowledgeQuestionMap), GsonUtil.toJson(typeQuestionMap));
		HashMap<String,String> questionMap = Maps.newHashMap();
		questionMap.putAll(chapterQuestionMap);
		questionMap.putAll(knowledgeQuestionMap);
		questionMap.putAll(typeQuestionMap);
		saveUserDoneQuestions(uid, boxId, questionMap,questionIds);
	}

	/**
	 * 周用户做题情况入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param doneIds 做过题目id列表
	 * @param wrongIds 做错题目id列表
	 *
	 * */
	@Override
	public void cacheWeekQuestion(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> doneIds, List<Long> wrongIds) throws DataAccessException{
		if (teachBookId == null) {
			TeachingBook teachingBook = this.getBookByBoxId(boxId);
			if (teachingBook == null) {
				logger.error("teachingBook null,boxId:{}",boxId);
				return;
			} else {
				teachBookId = teachingBook.getId();
			}
		}
		//获取答题报告周计数
		String reportWeekValue = weekAnswerStatService.getReportWeekValue();
		if(null == reportWeekValue || "".equals(reportWeekValue)) {
			logger.error("cacheWeekQuestion reportWeekValue is null reportWeekValue:{},uid:{},homeworkId:{},boxId:{}", reportWeekValue, uid, homeworkId, boxId);
			return;
		}
		String weekMonday = reportWeekValue.split(",")[1];
		String numStr = reportWeekValue.split(",")[0];
		Long reportWeekNum = Long.parseLong(numStr);
		//周用户做过的题入redis缓存
		cacheWeekDoneQuestion(uid, homeworkId, teachBookId, boxId, doneIds, reportWeekNum);
		//周用户错题入redis缓存
		cacheWeekWrongQuestion(uid, homeworkId, teachBookId, boxId, wrongIds, reportWeekNum);

		//用户周答题报告数据入库
		saveWeekAnswerStat(uid, teachBookId, boxId, doneIds, wrongIds, weekMonday, reportWeekNum);
	}

	/**
	 * 用户周答题报告数据入库
	 * @throws DataAccessException
	 */
	public void saveWeekAnswerStat(Long uid, Long teachBookId, Long boxId, List<Long> doneIds, List<Long> wrongIds, String weekMonday, Long reportWeekNum) throws DataAccessException {
		WeekAnswerStat weekAnswerStat = new WeekAnswerStat();
		weekAnswerStat.setUid(uid);
		weekAnswerStat.setTeachBookId(teachBookId);
		weekAnswerStat.setBoxId(boxId);
		weekAnswerStat.setWeekNum(reportWeekNum);
		List<WeekAnswerStat> weekAnswerStatList = weekAnswerStatService.findList(weekAnswerStat);
		int rightNum = doneIds.size() - wrongIds.size();
		long answerMum = 0L;
		Double accuracy = 0D;
		if(null == weekAnswerStatList || weekAnswerStatList.isEmpty()) { //插入
			weekAnswerStat.setWeekStartDate(weekMonday);
			answerMum = Long.valueOf(doneIds.size());
			weekAnswerStat.setAnswerNum(answerMum);

			if (doneIds.size() > 0) {
				accuracy = CalUtils.div(rightNum, doneIds.size(), CalUtils.DEF_DIV_SCALE);
			}
			weekAnswerStat.setRightNum(Long.valueOf(rightNum));
			weekAnswerStat.setAccuracy(accuracy);
			weekAnswerStat.setYearWeekNum(CalUtils.getWeekOfYear());
			weekAnswerStatService.insert(weekAnswerStat);
		} else { //更新
			weekAnswerStat = weekAnswerStatList.get(0);
			answerMum = weekAnswerStat.getAnswerNum() + doneIds.size();
			long rightAddNum = weekAnswerStat.getRightNum() + rightNum;
			if (answerMum > 0) {
				accuracy = CalUtils.div(rightAddNum, answerMum, CalUtils.DEF_DIV_SCALE);
			}
			weekAnswerStat.setAnswerNum(answerMum);
			weekAnswerStat.setRightNum(rightAddNum);
			weekAnswerStat.setAccuracy(accuracy);
			weekAnswerStatService.update(weekAnswerStat);
		}
		//设置全站最高答题量
		String reportWeekMaxAnswerKey = RedisConsts.REPORT_WEEK_MAX_ANSWER_KEY + reportWeekNum + "_" + boxId;
		String maxAnswerNumStr = compatableRedisClusterClient.get(reportWeekMaxAnswerKey);
		if(null == maxAnswerNumStr || "".equals(maxAnswerNumStr)) {
			compatableRedisClusterClient.setex(reportWeekMaxAnswerKey, expTime.intValue(), answerMum + "");
		} else {
			Long maxAnswerNum = Long.parseLong(maxAnswerNumStr);
			if(answerMum > maxAnswerNum) {
				compatableRedisClusterClient.setex(reportWeekMaxAnswerKey, expTime.intValue(), answerMum + "");
			}
		}
		//处理排行数据
		String reportWeekListKey = RedisConsts.REPORT_WEEK_LIST_KEY + reportWeekNum + "_" + boxId;
		Double scoreOld = compatableRedisClusterClient.zscore(reportWeekListKey, uid+"");
		if(null != scoreOld) { //删除旧用户排行数据
			Long zret = compatableRedisClusterClient.zrem(reportWeekListKey, uid+"");
			logger.info("cacheWeekQuestion zrem reportWeekListKey:{},uid:{},score:{},zret:{}", reportWeekListKey, uid, scoreOld, zret);
		}
		//计算分数
		Double answerScore = CalUtils.mul(answerMum, 1000);
		Double accuracyScore = CalUtils.mul(accuracy, 100);
		Double score = CalUtils.add(answerScore, accuracyScore);
		//添加排行数据
		Long zret = compatableRedisClusterClient.zadd(reportWeekListKey, score, uid+"");
		Long expret = compatableRedisClusterClient.expire(reportWeekListKey, expTime.intValue());
		logger.info("cacheWeekQuestion zadd reportWeekListKey:{},uid:{},score:{},zret:{},expret:{}", reportWeekListKey, uid, score, zret, expret);
	}

	/**
	 * 周用户做过的题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param teachBookId 教材id
	 * @param questionIds 题目id列表
	 * @param reportWeekNum 答题报告周计数
	 * */
	@Override
	public void cacheWeekDoneQuestion(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds, Long reportWeekNum) throws DataAccessException{
		//处理章节关系
		Boolean doneQ4Chapter = dealWeekDoneQuestion4Chapter(uid, homeworkId, teachBookId, boxId, questionIds, reportWeekNum);
		//处理知识点关系
		Boolean doneQ4Knowledge = dealWeekDoneQuestion4Knowledge(uid, homeworkId, teachBookId, boxId, questionIds, reportWeekNum);
		if (!doneQ4Chapter || !doneQ4Knowledge) {
			logger.warn("cacheWeekDoneQuestion error,"
							+ "uid:{},homeworkId:{},teachBookId:{},boxId:{},questionIds:{},doneQ4Chapter:{},doneQ4Knowledge:{},"
					, uid, homeworkId, teachBookId, boxId, GsonUtil.toJson(questionIds), doneQ4Chapter, doneQ4Knowledge);
		}
	}

	/**
	 * 处理用户做过的题-题型存储
	 * */
	@Override
	public Map<String, String> dealUserBoxDoneQuestion4Qtype(Long uid, Long boxId, List<Long> questionIds)throws DataAccessException{
        Map<String, String> qIdRedisMap = new HashMap<String, String>();
		//首先根据题目id获取到题目信息
        Map<Integer, List<Long>> questionMap = getTypeQuestionMap(questionIds,null);

		Type type = new TypeToken<List<Long>>() {
		}.getType();

		//针对每个题型去更新redis
		Iterator questionMapsIterator = questionMap.entrySet().iterator();
		while (questionMapsIterator.hasNext()) {
			Entry questionEntry = (Entry)questionMapsIterator.next();

			Integer qtype = Integer.valueOf(questionEntry.getKey().toString());
			String qidStr = questionEntry.getValue().toString();

			List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);

			String redisQIds = getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneQtype(qtype));
			if (StringUtils.isBlank(redisQIds)) {
				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneQtype(qtype), GsonUtil.toJson(new HashSet(qidList)));
			}else {
				List<Long> redisQIdList = null;
				try { //处理缓存中异常数据
					redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
					HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
					redisQIdSet.addAll(qidList);

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneQtype(qtype), GsonUtil.toJson(redisQIdSet));
				} catch (Exception e) {
					logger.error("dealUserBoxDoneQuestion4Qtype redis fromJson error:uid:{},boxId:{},redisQIds,doneQtypeKey:{},Exception:{}",
							uid, boxId, redisQIds, NewRedisConsts.getUserQuestionboxDoneQtype(qtype), e);
					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneQtype(qtype), GsonUtil.toJson(new HashSet(qidList)));
				}
			}
		}

		return qIdRedisMap;
	}

	/**
	 * 处理用户做过的题-章节存储
	 * */
	private Map<String, String> dealUserBoxDoneQuestion4Chapter(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds)throws DataAccessException{
        Map<String, String> qIdRedisMap = new HashMap<String, String>();
		//首先根据作业Id，获取到这份作业对应的章节id
		VirtualHomework homework = userVirtualHomeworkDao.get(homeworkId, uid);

		//如果本次对应的恰好是章节练习
		if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Chapter.intValue()) {
			Long chapterId = homework.getHomeworkTypeId();
			ChapterSection chapterSection = knowledgeResource.getChapterSectionItemById(chapterId);
			if (chapterSection != null) {
				qIdRedisMap.putAll(dealDoneChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds));
			}else {
				logger.warn("dealUserBoxDoneQuestion4Chapter get chapter is null.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",homeworkId,uid,homework.getHomeworkType(),homework.getHomeworkTypeId());
			}
		}else if(homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Knowledge.intValue()){
			//好吧，这份练习是知识点练习，只能换个角度去拿章节id了

			Long knowledgeId = homework.getHomeworkTypeId();

			//根据知识点id与教材id，查询该教材下，包含该知识点的章节id
			List<ChapterSection> chapterSections = knowledgeResource.getChapterSectionItemByKnowledgeIdAndBookId(teachBookId,knowledgeId);
			if (chapterSections!=null && chapterSections.size()>0) {
				for (ChapterSection chapterSection : chapterSections) {
					qIdRedisMap.putAll(dealDoneChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds));
				}
			}
		}
		return qIdRedisMap;
	}


	/**
	 * 周处理用户做过的题-章节存储
	 * */
	private boolean dealWeekDoneQuestion4Chapter(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds, Long reportWeekNum)throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {}.getType();
		//首先根据作业Id，获取到这份作业对应的章节id
		VirtualHomework homework = userVirtualHomeworkDao.get(homeworkId, uid);
		//如果本次对应的恰好是章节练习
		if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Chapter.intValue()) {
			Long chapterId = homework.getHomeworkTypeId();
			ChapterSection chapterSection = knowledgeResource.getChapterSectionItemById(chapterId);
			if (chapterSection != null) {
				HashMap<String, String> qIdRedisMap = new HashMap<String, String>();

				qIdRedisMap.putAll(dealWeekDoneChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds, reportWeekNum));
				if (qIdRedisMap!=null && qIdRedisMap.size() != 0) {
					compatableRedisClusterClient.hmset(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), qIdRedisMap);
					compatableRedisClusterClient.expire(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), expTime.intValue());
				} else {
					logger.warn("dealWeekDoneQuestion4Chapter qIdRedisMap is null.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",homeworkId,uid,homework.getHomeworkType(),homework.getHomeworkTypeId());
				}
			} else {
				logger.warn("dealWeekDoneQuestion4Chapter get chapter is null.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",homeworkId,uid,homework.getHomeworkType(),homework.getHomeworkTypeId());
			}
		}else if(homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Knowledge.intValue()){
			//好吧，这份练习是知识点练习，只能换个角度去拿章节id了
			//首先拿到知识点id
			Long knowledgeId = homework.getHomeworkTypeId();

			//根据知识点id与教材id，查询该教材下，包含该知识点的章节id
			List<ChapterSection> chapterSections = knowledgeResource.getChapterSectionItemByKnowledgeIdAndBookId(teachBookId,knowledgeId);
			if (chapterSections!=null && chapterSections.size()>0) {
				HashMap<String, String> qIdRedisMap = new HashMap<String, String>();

				for (ChapterSection chapterSection : chapterSections) {
					qIdRedisMap.putAll(dealWeekDoneChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds, reportWeekNum));
				}
				if (qIdRedisMap!=null && qIdRedisMap.size() != 0) {
					compatableRedisClusterClient.hmset(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), qIdRedisMap);
					compatableRedisClusterClient.expire(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), expTime.intValue());
				} else {
					logger.warn("dealWeekDoneQuestion4Chapter knowledge qIdRedisMap is null.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",
							homeworkId, uid, homework.getHomeworkType(), homework.getHomeworkTypeId());
				}
			} else {
				logger.warn("dealWeekDoneQuestion4Chapter knowledge get chapter is null.homeworkId:{},uid:{},homeworkType:{},homeworkTypeId:{}",
						homeworkId,uid,homework.getHomeworkType(),homework.getHomeworkTypeId());
			}
			return true;
		}else {
			logger.warn("dealWeekDoneQuestion4Chapter not chapter exercise.homeworkId:{},uid:{},homework:{}",
					homeworkId,uid,GsonUtil.toJson(homework));
			return false;
		}
		return true;
	}

	@Override
	public HashMap<String, String> dealDoneChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds){
		return dealDoneChapterDataNew(chapterSection, uid, teachBookId, boxId, questionIds, false);
	}

		/**
         * 相比之前的方法，摒弃了递归调用啦！
         * */
	private HashMap<String, String> dealDoneChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds, boolean isSync){
		HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		Type type = new TypeToken<List<Long>>() {
		}.getType();

		try {
			Set<Long> needDealChapter = new HashSet<Long>();
			Long chapterSectionId = chapterSection.getId();
			needDealChapter.add(chapterSectionId);

			// 处理父节点，获取当前章节的父节点
			String parentIdsStr = chapterSection.getParentIds();
			parentIdsStr = parentIdsStr.substring(0, parentIdsStr.length()-1);
			for (String parendIdStr : parentIdsStr.split(",")) {
				if (!parendIdStr.equals("0")) {
					needDealChapter.add(Long.valueOf(parendIdStr));
				}
			}
			for (Long chapterId : needDealChapter) {
				//取出用户该章节已做过的题
				String redisQIds = "";
				if (!isSync) {
					redisQIds = getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId, chapterId));
				}
				if (StringUtils.isBlank(redisQIds)) {
					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId,chapterId), GsonUtil.toJson(questionIds));

				}else {
					List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
					HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
					redisQIdSet.addAll(questionIds);

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId,chapterId), GsonUtil.toJson(redisQIdSet));
				}
			}

			// 处理子节点，获取当前章节的子节点
			List<ChapterSection> chapterSections = knowledgeResource.getChildChapterSectionItemById(chapterSectionId);
			if (chapterSections != null && chapterSections.size() > 0) {
				Set<Long> needDealChildChapter = new HashSet<Long>();
				needDealChildChapter.addAll(Collections3.extractToList(chapterSections, "id"));// 取出所有子节点id

				// 计算questionIds的归属 start
				HashMap<Long, List<Long>> chapterQuestionMap = new HashMap<Long, List<Long>>();
				HashMap<Long, List<Long>> userCpQuestionMap = new HashMap<Long, List<Long>>();

				// 取出每个子章节下的题目集合,以及每个子章节下用户已作答题目的集合
				for (Long childChapterId : needDealChildChapter) {
					// 获取该子章节下的题目集合
					String childChapterQidStr =  knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId,
							NewRedisConsts.getTeachbookQuestionboxChapterFiled(childChapterId));
					if (StringUtils.isNotBlank(childChapterQidStr)) {
						List<Long> childChapterQids = GsonUtil.getGson().fromJson(childChapterQidStr, type);
						chapterQuestionMap.put(childChapterId, childChapterQids);
					}

					// 获取该子章节下用户已作答的题目集合
					String redisQIds = "";
					if (!isSync) {
						redisQIds = getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId, childChapterId));
					}
					if (StringUtils.isNotBlank(redisQIds)) {
						List<Long> userChildCpQids = GsonUtil.getGson().fromJson(redisQIds, type);
						userCpQuestionMap.put(childChapterId, userChildCpQids);
					}else {
						userCpQuestionMap.put(childChapterId, new ArrayList<Long>());
					}

				}

				// 循环判断每个题目
				for (Long qId : questionIds) {
					Iterator chapterQuestionIterator = chapterQuestionMap.entrySet().iterator();
					while (chapterQuestionIterator.hasNext()) {
						Entry<Long, List<Long>> questionEntry = (Entry)chapterQuestionIterator.next();
						Long chapterId = Long.valueOf(questionEntry.getKey().toString());
						List<Long> qidList = questionEntry.getValue();

						if (qidList.contains(qId)) {// 该题目属于该章节的题目集合中
							userCpQuestionMap.get(chapterId).add(qId);
						}
					}
				}
				// 计算questionIds的归属 end

				// 将更新好的子节点的作答数据更新到qIdRedisMap
				Iterator userCpQuestionIterator = userCpQuestionMap.entrySet().iterator();
				while (userCpQuestionIterator.hasNext()) {
					Entry<Long, List<Long>> userCpQuestionEntry = (Entry<Long, List<Long>>)userCpQuestionIterator.next();
					Long chapterId = Long.valueOf(userCpQuestionEntry.getKey().toString());
					List<Long> qidList = userCpQuestionEntry.getValue();

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId,chapterId), GsonUtil.toJson(new HashSet<Long>(qidList)));
				}
			}

		} catch (Exception e) {
			logger.error("dealParentDoneChapterData",e);
		}
		return qIdRedisMap;
	}

	/**
	 * 周处理做过的题目
	 * */
	private HashMap<String, String> dealWeekDoneChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds, Long reportWeekNum){
		HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		Type type = new TypeToken<List<Long>>() {}.getType();

		try {
			Set<Long> needDealChapter = new HashSet<Long>();

			Long chapterSectionId = chapterSection.getId();
			needDealChapter.add(chapterSectionId);

			// 处理父节点，获取当前章节的父节点
			String parentIdsStr = chapterSection.getParentIds();
			parentIdsStr = parentIdsStr.substring(0, parentIdsStr.length()-1);
			for (String parendIdStr : parentIdsStr.split(",")) {
				if (!parendIdStr.equals("0")) {
					needDealChapter.add(Long.valueOf(parendIdStr));
				}
			}
			logger.debug("dealWeekDoneChapterDataNew needDealChapter: uid:{},teachBookId:{},boxId:{},reportWeekNum:{},getWeekUserQuestionboxDoneKey:{},needDealChapter:{}",
					uid, teachBookId, boxId, reportWeekNum, NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), needDealChapter.toString());

			for (Long chapterId : needDealChapter) {
				//取出用户该章节已做过的题
				String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId,chapterId));
				if (StringUtils.isBlank(redisQIds)) {
					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId,chapterId), GsonUtil.toJson(questionIds));

				}else {
					List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
					HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
					redisQIdSet.addAll(questionIds);

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId,chapterId), GsonUtil.toJson(redisQIdSet));
				}
			}

			logger.debug("dealWeekDoneChapterDataNew qIdRedisMap: uid:{},teachBookId:{},boxId:{},reportWeekNum:{},qIdRedisMap:{},chapterSectionId:{}",
					uid, teachBookId, boxId, reportWeekNum, qIdRedisMap.toString(),chapterSectionId);

			// 处理子节点，获取当前章节的子节点
			List<ChapterSection> chapterSections = knowledgeResource.getChildChapterSectionItemById(chapterSectionId);
			logger.debug("dealWeekDoneChapterDataNew chapterSections: uid:{},teachBookId:{},boxId:{},reportWeekNum:{},chapterSections:{}",
					uid, teachBookId, boxId, reportWeekNum, null == chapterSections ? "null" : chapterSections.toString());
			if (chapterSections != null && chapterSections.size() > 0) {
				Set<Long> needDealChildChapter = new HashSet<Long>();
				needDealChildChapter.addAll(Collections3.extractToList(chapterSections, "id"));// 取出所有子节点id

				// 计算questionIds的归属 start
				HashMap<Long, List<Long>> chapterQuestionMap = new HashMap<Long, List<Long>>();
				HashMap<Long, List<Long>> userCpQuestionMap = new HashMap<Long, List<Long>>();

				// 取出每个子章节下的题目集合,以及每个子章节下用户已作答题目的集合
				for (Long childChapterId : needDealChildChapter) {
					// 获取该子章节下的题目集合
					String childChapterQidStr =  knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId,
							NewRedisConsts.getTeachbookQuestionboxChapterFiled(childChapterId));
					if (StringUtils.isNotBlank(childChapterQidStr)) {
						List<Long> childChapterQids = GsonUtil.getGson().fromJson(childChapterQidStr, type);
						chapterQuestionMap.put(childChapterId, childChapterQids);
					}

					// 获取该子章节下用户已作答的题目集合
					String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId,childChapterId));
					if (StringUtils.isNotBlank(redisQIds)) {
						List<Long> userChildCpQids = GsonUtil.getGson().fromJson(redisQIds, type);
						userCpQuestionMap.put(childChapterId, userChildCpQids);
					}else {
						userCpQuestionMap.put(childChapterId, new ArrayList<Long>());
					}
				}

				// 循环判断每个题目
				for (Long qId : questionIds) {
					Iterator chapterQuestionIterator = chapterQuestionMap.entrySet().iterator();
					while (chapterQuestionIterator.hasNext()) {
						Entry<Long, List<Long>> questionEntry = (Entry)chapterQuestionIterator.next();
						Long chapterId = Long.valueOf(questionEntry.getKey().toString());
						List<Long> qidList = questionEntry.getValue();

						if (qidList.contains(qId)) {// 该题目属于该章节的题目集合中
							userCpQuestionMap.get(chapterId).add(qId);
						}
					}
				}
				// 计算questionIds的归属 end

				// 将更新好的子节点的作答数据更新到qIdRedisMap
				Iterator userCpQuestionIterator = userCpQuestionMap.entrySet().iterator();
				while (userCpQuestionIterator.hasNext()) {
					Entry<Long, List<Long>> userCpQuestionEntry = (Entry<Long, List<Long>>)userCpQuestionIterator.next();
					Long chapterId = Long.valueOf(userCpQuestionEntry.getKey().toString());
					List<Long> qidList = userCpQuestionEntry.getValue();

					qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneChapter(teachBookId,chapterId), GsonUtil.toJson(new HashSet<Long>(qidList)));
				}

			}
			logger.debug("dealWeekDoneChapterDataNew qIdRedisMap: uid:{},teachBookId:{},boxId:{},reportWeekNum:{},qIdRedisMap:{}",
					uid, teachBookId, boxId, reportWeekNum, qIdRedisMap.toString());
		} catch (Exception e) {
			logger.error(String.format("dealWeekDoneChapterDataNew Error: uid:%d,teachBookId:%d,boxId:%d,reportWeekNum:%d,questionIds:%s",
					uid,teachBookId,boxId,reportWeekNum,questionIds.toString()),e);
		}
		return qIdRedisMap;
	}

	/**
	 * 处理用户做过的题-知识点存储
	 * */
	private Map<String, String> dealUserBoxDoneQuestion4Knowledge(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds)throws DataAccessException{
        Map<String, String> qIdRedisMap = new HashMap<String, String>();
		//首先根据作业Id，获取到这份作业对应的知识点id

		//如果本次对应的恰好是知识点练习
		VirtualHomework homework = userVirtualHomeworkDao.get(homeworkId, uid);
		if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Knowledge.intValue()) {
			Long KnowledgeId = homework.getHomeworkTypeId();
			//取出用户该知识点的已做过的题
			String redisQIds = getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,KnowledgeId));
			if (StringUtils.isBlank(redisQIds)) {
				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,KnowledgeId), GsonUtil.toJson(questionIds));
			}else {
                Type type = new TypeToken<List<Long>>() {}.getType();
				List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
				HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
				redisQIdSet.addAll(questionIds);

				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(redisQIdSet));
			}
		}else if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Chapter.intValue()) {
			//好吧，这份练习是章节练习，只能换个角度去拿知识点id了

			//首先先根据章节id拿到 这个章节下的所有知识点Id
			Long chapterId = homework.getHomeworkTypeId();

            qIdRedisMap.putAll(dealDoneKnowledgeDataNew(uid, chapterId, teachBookId, boxId, questionIds));

		}
		return qIdRedisMap;
	}
	private Map<String, String> dealDoneKnowledgeDataNew(Long uid, Long chapterId, Long teachBookId, Long boxId, List<Long> questionIds) throws DataAccessException {
        HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
        Type type = new TypeToken<List<Long>>() {}.getType();
        HashMap<Long, List<Long>> userDoInKlg = getKnowledgeDoneQuestionMap(chapterId,boxId,teachBookId,questionIds);

        if (userDoInKlg.size()>0) {//把梳理出来的知识点题目关系入到用户做过的知识点题目缓存中

            Iterator userDoInKlgIter = userDoInKlg.entrySet().iterator();
            while (userDoInKlgIter.hasNext()) {
                Entry userDoInKlgEntry = (Entry)userDoInKlgIter.next();
                String qidStr = userDoInKlgEntry.getValue().toString();

                Long KnowledgeId = Long.valueOf(userDoInKlgEntry.getKey().toString());
                List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);

                //取出用户该知识点的已做过的题
                String redisQIds = getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,KnowledgeId));
                if (StringUtils.isBlank(redisQIds)) {
                    qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,KnowledgeId), GsonUtil.toJson(qidList));
                }else {
                    List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
                    HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
                    redisQIdSet.addAll(qidList);

                    qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(redisQIdSet));
                }
            }
        }

        return qIdRedisMap;
    }

    /**
     * 根据章节id,和题目id列表,把所有题目分配到知识点下
     * @param chapterId
     * @param boxId
     * @param teachBookId
     * @param questionIds
     * @return  key:knowledgeId,value:qIdList
     */
    private HashMap<Long, List<Long>> getKnowledgeDoneQuestionMap(Long chapterId, Long boxId, Long teachBookId, List<Long> questionIds) {
        Type type = new TypeToken<List<Long>>() {}.getType();
        HashMap<Long, List<Long>> userDoInKlg = new HashMap<Long, List<Long>>();

        Set<Long> knowledgeIds = new HashSet<Long>();
        List<KnowledgeGraph> knowledges = knowledgeResource.getKnowledgeGraphByChapterItemId(chapterId);
        if (knowledges!=null && knowledges.size()>0) {
            for (KnowledgeGraph knowledge : knowledges) {
                knowledgeIds.add(knowledge.getId());
            }
        }

        // 获取所有子节点的知识点id
        List<ChapterSection> childs = knowledgeResource.getChildChapterSectionItemById(chapterId);
        if (childs != null && childs.size() > 0) {
            for (ChapterSection childChapterion : childs) {
                List<KnowledgeGraph> klgs = knowledgeResource.getKnowledgeGraphByChapterItemId(childChapterion.getId());
                if (klgs!=null && !klgs.isEmpty()) {
                    for (KnowledgeGraph knowledge : klgs) {
                        knowledgeIds.add(knowledge.getId());
                    }
                }
            }
        }

        HashMap<Long, List<Long>> knowledgeQuestionMap = new HashMap<Long, List<Long>>();
        //循环每个知识点Id，拿到每个知识点下的题目
        for (Long knowledgeId : knowledgeIds) {
            String knowledgeQuestionIdStr =  knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId,
                    NewRedisConsts.getTeachbookQuestionboxKnowledgeFiled(knowledgeId));

            if (StringUtils.isNotBlank(knowledgeQuestionIdStr)) {
                List<Long> knowledgeQuestionIds = GsonUtil.getGson().fromJson(knowledgeQuestionIdStr, type);
                knowledgeQuestionMap.put(knowledgeId, knowledgeQuestionIds);
            }
        }

        //判断用户本次做的题目是属于哪几个知识点的
        if (knowledgeQuestionMap.size() >0) {
            //循环判断题目是属于哪个知识点
            for (Long userDoQuestionId : questionIds) {
                Iterator questionMapsIterator = knowledgeQuestionMap.entrySet().iterator();
                while (questionMapsIterator.hasNext()) {
                    Entry questionEntry = (Entry) questionMapsIterator.next();
                    Long knowledgeId = Long.valueOf(questionEntry.getKey().toString());
                    String qidStr = questionEntry.getValue().toString();
                    List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);

                    if (qidList.contains(userDoQuestionId)) {
                        List<Long> userDoQuestionList = null;
                        if (userDoInKlg.containsKey(knowledgeId)) {
                            userDoQuestionList = userDoInKlg.get(knowledgeId);
                            userDoQuestionList.add(userDoQuestionId);
                        } else {
                            userDoQuestionList = new ArrayList<Long>();
                            userDoQuestionList.add(userDoQuestionId);
                        }
                        userDoInKlg.put(knowledgeId, userDoQuestionList);
                    }
                }
            }
        }
        return userDoInKlg;
    }
	/**
	 * 周处理用户做过的题-知识点存储
	 * */
	private boolean dealWeekDoneQuestion4Knowledge(Long uid, Long homeworkId, Long teachBookId, Long boxId, List<Long> questionIds, Long reportWeekNum)throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {}.getType();
		//首先根据作业Id，获取到这份作业对应的知识点id

		//如果本次对应的恰好是知识点练习
		VirtualHomework homework = userVirtualHomeworkDao.get(homeworkId, uid);
		if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Knowledge.intValue()) {
			Long KnowledgeId = homework.getHomeworkTypeId();

			HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
			//取出用户该知识点的已做过的题
			String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,KnowledgeId));
			if (StringUtils.isBlank(redisQIds)) {
				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,KnowledgeId), GsonUtil.toJson(questionIds));
			}else {
				List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
				HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
				redisQIdSet.addAll(questionIds);

				qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(redisQIdSet));
			}
			if (qIdRedisMap!=null && qIdRedisMap.size() != 0) {
				compatableRedisClusterClient.hmset(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), qIdRedisMap);
				compatableRedisClusterClient.expire(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), expTime.intValue());
			}
		}else if (homework != null && homework.getHomeworkType() != null && homework.getHomeworkType().intValue() == Consts.Question_Exercise_Type.Chapter.intValue()) {
			//好吧，这份练习是章节练习，只能换个角度去拿知识点id了
			//首先先根据章节id拿到 这个章节下的所有知识点Id
			Long chapterId = homework.getHomeworkTypeId();

            HashMap<Long, List<Long>> userDoInKlg = getKnowledgeDoneQuestionMap(chapterId,boxId,teachBookId,questionIds);

            if (userDoInKlg.size()>0) {//把梳理出来的知识点题目关系入到用户做过的知识点题目缓存中
                HashMap<String, String> qIdRedisMap = new HashMap<String, String>();

                Iterator userDoInKlgIter = userDoInKlg.entrySet().iterator();
                while (userDoInKlgIter.hasNext()) {
                    Entry userDoInKlgEntry = (Entry)userDoInKlgIter.next();
                    String qidStr = userDoInKlgEntry.getValue().toString();

                    Long KnowledgeId = Long.valueOf(userDoInKlgEntry.getKey().toString());
                    List<Long> qidList = GsonUtil.getGson().fromJson(qidStr, type);

                    //取出用户该知识点的已做过的题
                    String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,KnowledgeId));
                    if (StringUtils.isBlank(redisQIds)) {
                        qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,KnowledgeId), GsonUtil.toJson(qidList));
                    }else {
                        List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
                        HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
                        redisQIdSet.addAll(qidList);

                        qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, KnowledgeId), GsonUtil.toJson(redisQIdSet));
                    }
                }
                if (qIdRedisMap!=null && qIdRedisMap.size() != 0) {
                    compatableRedisClusterClient.hmset(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), qIdRedisMap);
                    compatableRedisClusterClient.expire(NewRedisConsts.getWeekUserQuestionboxDone(uid, boxId, reportWeekNum), expTime.intValue());
                }
            }

			return true;
		}else {
			logger.warn("dealWeekDoneQuestion4Knowledge not chapter exercise.homeworkId:{},uid:{},homework:{}",
					homeworkId,uid,GsonUtil.toJson(homework));
			return false;
		}
		return true;
	}

	/**
	 * 刷新用户最近一次做过的题目（数据有效期：12个小时）
	 * @param uid
	 * @param boxId
	 * @param questions
	 *
	 * */
	@Override
	public void refreshUserLatestAnswerQId(Long uid, Long boxId, Collection<Question> questions)
			throws DataAccessException {
		List<Long> qIds = Collections3.extractToList(questions,"id");
		compatableRedisClusterClient.setex(NewRedisConsts.getUserBoxLatestAnswerQids(uid, boxId), 43200, GsonUtil.getGenericGson().toJson(qIds));
	}

	private List<HashMap<String, Object>> dealNoCachedData(List<UserAnswerDetail> details){
		List<HashMap<String, Object>> tempResult = new ArrayList<HashMap<String, Object>>();

		Map<Long, List<UserAnswerDetail>> topic2Detail = new HashMap<Long, List<UserAnswerDetail>>();
		//按子题id进行归类
		for (UserAnswerDetail userAnswerDetail : details) {
			List<UserAnswerDetail> userAnswerDetails = topic2Detail.get(userAnswerDetail.getTopicId());
			if (userAnswerDetails == null) {
				userAnswerDetails = new ArrayList<UserAnswerDetail>();
			}

			userAnswerDetails.add(userAnswerDetail);
			topic2Detail.put(userAnswerDetail.getTopicId(), userAnswerDetails);
		}

		//统计每个题目的作答情况
		Set<Long> questionIdSet = new HashSet<Long>();//存储从DB查出来了的questionId

		Iterator<Entry<Long, List<UserAnswerDetail>>> topic2DetailIterator = topic2Detail.entrySet().iterator();
		while (topic2DetailIterator.hasNext()) {
			Entry<Long, List<UserAnswerDetail>> topic2DetailEntry = (Entry<Long, List<UserAnswerDetail>>)topic2DetailIterator.next();
			Long topicId = topic2DetailEntry.getKey();
			List<UserAnswerDetail> userAnswerDetails = topic2DetailEntry.getValue();

			questionIdSet.add(userAnswerDetails.get(0).getQuestionId());

			int rightCnt = 0;
			UserAnswerDetail latestAnswerDetail = null;
			for (UserAnswerDetail userAnswerDetail : userAnswerDetails) {
				if (userAnswerDetail.getIsRight().intValue() == UserAnswerDetail.IsRight.RIGHT) {
					rightCnt++;
				}
				if (latestAnswerDetail == null) {
					latestAnswerDetail = userAnswerDetail;
				}else if(DateUtil.compare(userAnswerDetail.getCreateDate(),latestAnswerDetail.getCreateDate()) > 0){
					latestAnswerDetail = userAnswerDetail;
				}
			}

			HashMap<String, Object> resultMap = new HashMap<String, Object>();

			resultMap.put("question_id", userAnswerDetails.get(0).getQuestionId());
			resultMap.put("topic_id", topicId);
			resultMap.put("answer_num", userAnswerDetails.size());
			resultMap.put("right_num", rightCnt);
			resultMap.put("last_answer", latestAnswerDetail==null?"":latestAnswerDetail.getAnswerStr());
			resultMap.put("last_answer_detail", latestAnswerDetail);
			tempResult.add(resultMap);
		}
		return tempResult;
	}

	@Override
	public List<Long> getUserAnswerBoxQuestionInfo(Long uid, Long boxId,
												   Long teachBookId, Long objId, Integer objType)
			throws DataAccessException {
		return getUserDoneQuestionIdList(uid, boxId, teachBookId, objId, objType);
	}

	@Override
	public Map<Long, List<Long>> getUserAnswerBoxQuestionInfoBatch(Long uid, Long boxId,
                                                                   Long teachBookId, List<Long> objIds, Integer objType)
			throws DataAccessException {
		return getUserDoneQuestionIdListBatch(uid, boxId, teachBookId, objIds, objType);
	}

	@Override
	public List<Long> getWeekAnswerBoxQuestionInfo(Long uid, Long boxId,
												   Long teachBookId, Long objId, Integer objType, Long reportWeekNum)
			throws DataAccessException {
		return getWeekDoneQuestionIdList(uid, boxId, teachBookId, objId, objType, reportWeekNum);
	}

	@Override
	public List<Long> getUserWrongBoxQuestionInfo(Long uid, Long boxId,
												  Long teachBookId, Long objId, Integer objType) throws DataAccessException {
		return getUserWrongQuestionIdList(uid, boxId, teachBookId, objId, objType);
	}

    @Override
    public Map<Long, List<Long>> getUserWrongBoxQuestionInfoBatch(Long uid, Long boxId,
                                                                  Long teachBookId, List<Long> objIds, Integer objType) throws DataAccessException {
        return getUserWrongQuestionIdListBatch(uid, boxId, teachBookId, objIds, objType);
    }

	@Override
	public List<Long> getWeekWrongBoxQuestionInfo(Long uid, Long boxId, Long teachBookId,
												  Long objId, Integer objType, Long reportWeekNum) throws DataAccessException {
		return getWeekWrongQuestionIdList(uid, boxId, teachBookId, objId, objType, reportWeekNum);
	}

	@Override
	public List<Long> getWipeOutWrongBoxQuestionInfo(Long uid, Long boxId, Long teachBookId,
													 Long objId, Integer objType) throws DataAccessException {
		return getWipeOutWrongQuestionIdList(uid, boxId, teachBookId, objId, objType);
	}

    @Override
    public void removeUserWrongQuestion(Long uid, Long boxId, List<Long> filterQuestions ) throws DataAccessException {
        removeUserWrongQuestion(uid, boxId, filterQuestions, true);
    }
	/**
	 * 移除用户的错题
	 * @param uid
	 * @param boxId 题库id
	 * @param filterQuestions 需要移除的题目ids
	 *
	 * */
	@Override
	public void removeUserWrongQuestion(Long uid, Long boxId, List<Long> filterQuestions , boolean updateWrong) throws DataAccessException {
		if (CollectionUtils.isEmpty(filterQuestions)) {
			return;
		}
		Type type = new TypeToken<List<Long>>() {}.getType();

		HashMap<String, String> qIdNewData = new HashMap<String, String>();
        //消灭错题redis数据
		HashMap<String, String> intersectionData = new HashMap<String, String>();
        //消灭错题数据
        HashMap<String, List<Long>> intersectionMap = new HashMap<String, List<Long>>();

		reloadWrongQuestionsFromMaster(uid, boxId);
		//取出用户所有的错题关系
		Map<String, String> allWrongMap = compatableRedisClusterClient.hgetAll(NewRedisConsts.getUserQuestionboxWrong(uid, boxId));
        //处理数据为空返回
		if(null == allWrongMap || allWrongMap.isEmpty()) {
			logger.info("removeUserWrongQuestion allWrongMap null:uid:{},boxId:{},allWrongMap:{}", uid, boxId, allWrongMap);
			return;
		}

		Iterator allWrongIter = allWrongMap.entrySet().iterator();
		while (allWrongIter.hasNext()) {
			Entry allWrongEntry = (Entry)allWrongIter.next();
			String key = allWrongEntry.getKey().toString();
			if (!StringUtils.startsWith(key, NewRedisConsts.QUESTION_BOX_NEW_PREFIX)) {
				continue;
			}
			String qidStr = allWrongEntry.getValue().toString();
			List<Long> oriQuestionIds = GsonUtil.getGson().fromJson(qidStr, type);
			logger.debug("removeUserWrongQuestion oriQuestionIds:{},filterQuestions:{},key:{}", GsonUtil.toJson(oriQuestionIds), GsonUtil.toJson(filterQuestions), key);
			//过滤掉需要移除的题目id
			List<Long> resultQuestionIds = (List<Long>)CollectionUtils.subtract(oriQuestionIds, filterQuestions);

			qIdNewData.put(key, GsonUtil.toJson(resultQuestionIds));

			//获取消灭错题即交集的题目id
			List<Long> intersectionIds = (List<Long>)CollectionUtils.intersection(oriQuestionIds, filterQuestions);
			intersectionMap.put(key, intersectionIds);
			//如果无已消灭错题，初始化数据
			intersectionData.put(key, GsonUtil.toJson(intersectionIds));
		}

		if (qIdNewData!=null && qIdNewData.size() != 0 && updateWrong) {
		    saveUserWrongQuestions(uid, boxId, qIdNewData);
		}

		//取出用户所有的已消灭错题关系
		Map<String, String> allWipeOutMap = this.getUserWipeOutWrongQuestionsAll(uid, boxId);

		if(null != allWipeOutMap) {
			Iterator allWipeOutIter = allWipeOutMap.entrySet().iterator();
			while (allWipeOutIter.hasNext()) {
				Entry allWrongEntry = (Entry)allWipeOutIter.next();
				String key = allWrongEntry.getKey().toString();
				if (!StringUtils.startsWith(key, NewRedisConsts.QUESTION_BOX_NEW_PREFIX)) {
					continue;
				}
				String qidStr = allWrongEntry.getValue().toString();
				List<Long> oriQuestionIds = GsonUtil.getGson().fromJson(qidStr, type);

				List<Long> addQuestions = intersectionMap.get(key);
				if(null != addQuestions && !addQuestions.isEmpty()) {
					//合并已消灭的题目id
					List<Long> resultQuestionIds = (List<Long>)CollectionUtils.union(oriQuestionIds, addQuestions);
					intersectionData.put(key, GsonUtil.toJson(resultQuestionIds));
				} else {
					intersectionData.put(key, GsonUtil.toJson(oriQuestionIds));
				}
			}
		}
		logger.debug("removeUserWrongQuestion intersectionData before set is {}", GsonUtil.toJson(intersectionData));
		//设置消灭错题数据
		if (intersectionData!=null && intersectionData.size() != 0) {
			this.saveUserWipeOutWrongQuestions(uid,boxId,intersectionData);
		}
	}

	/**
	 * 移除用户已消灭的错题
	 * @param uid
	 * @param boxId 题库id
	 * @param filterQuestions 需要移除的题目ids
	 * */
	@Override
	public void removeWipeOutWrongQuestion(Long uid, Long boxId, List<Long> filterQuestions) throws DataAccessException{
		Type type = new TypeToken<List<Long>>() {}.getType();

		HashMap<String, String> qIdNewData = new HashMap<String, String>();

		//取出用户所有的错题关系
		Map<String, String> allWipeOutMap = this.getUserWipeOutWrongQuestionsAll(uid, boxId);

		Iterator allWipeOutIter = allWipeOutMap.entrySet().iterator();
		while (allWipeOutIter.hasNext()) {
			Entry allWrongEntry = (Entry)allWipeOutIter.next();
			String key = allWrongEntry.getKey().toString();
			String qidStr = allWrongEntry.getValue().toString();
			List<Long> oriQuestionIds = GsonUtil.getGson().fromJson(qidStr, type);

			//过滤掉需要移除的题目id
			List<Long> resultQuestionIds = (List<Long>)CollectionUtils.subtract(oriQuestionIds, filterQuestions);
			qIdNewData.put(key, GsonUtil.toJson(resultQuestionIds));
		}

		if (qIdNewData!=null && qIdNewData.size() != 0) {
			this.saveUserWipeOutWrongQuestions(uid,boxId, qIdNewData);
		}
	}

	/**
	 * 自动生成练习
	 *
	 * */
	@Override
	public Long GenerateBoxExercise(UserGenerateExerciseAnswer userExerciseAnswer)
			throws DataAccessException {
		// 1.先取出所有的题目
		List<UserAnswerDetail> userAnswerDetails = userExerciseAnswer.getAnswerDetail();
		Set<Long> questionList = new HashSet<Long>();
		for (UserAnswerDetail userAnswerDetail : userAnswerDetails) {
			questionList.add(userAnswerDetail.getQuestionId());
		}

		if (!questionList.isEmpty()) {
			// 2.生成作业
			String homeworkName = "";//作业名称
			switch (userExerciseAnswer.getHomeworkType().intValue()) {
				case 0:
					if (userExerciseAnswer.getHomeworkModel().equals(Consts.ExerciseRandomType.BRUSH_QUESTION)){
						homeworkName = "刷题挑战"+questionList.size()+"题";
					}
					break;
				case 1://章节
					ChapterSection chapterSection = knowledgeResource.getChapterSectionItemById(userExerciseAnswer.getHomeworkTypeId());
					if (chapterSection!=null) {
						homeworkName = chapterSection.getName();
					}else {
						throw new DataAccessException("chapterSection is not exsit! id:"+userExerciseAnswer.getHomeworkTypeId());
					}
					break;
				case 2://知识点
					KnowledgeGraph knowledge = knowledgeResource.getKnowledgeGraphById(userExerciseAnswer.getHomeworkTypeId());
					if (knowledge!=null) {
						homeworkName = knowledge.getName();
					}else {
						throw new DataAccessException("knowledge is not exsit! id:"+userExerciseAnswer.getHomeworkTypeId());
					}
					break;
			}

			Questionbox box = knowledgeResource.getQuestionBoxById(userExerciseAnswer.getBoxId());
			if (StringUtils.isNotBlank(homeworkName)) {
				homeworkName = box.getName()+"《"+homeworkName+"》";
			}else {
				homeworkName = box.getName();
			}
			VirtualHomework homework = new VirtualHomework();
			homework.setUid(userExerciseAnswer.getUid());
			homework.setName(homeworkName);
			homework.setNum(questionList.size());
			homework.setHomeworkTypeId(userExerciseAnswer.getHomeworkTypeId() != null ? userExerciseAnswer.getHomeworkTypeId() : 0L);//作业类型id(比如章节id，知识点id)
			homework.setHomeworkType(userExerciseAnswer.getHomeworkType());//作业类型 (0：所有，1：章节，2：知识点)
			homework.setRandomType(userExerciseAnswer.getHomeworkModel());// 作业模式（0：未做试题，1：错误试题，2：全部试题,3：智能练习【未做+错误】,4:刷题挑战）
			homework.setSourceId(userExerciseAnswer.getBoxId());
			homework.setSourceType(0);//作业来源类型（0：题库，1：...）
			homework.setBookId(userExerciseAnswer.getTeachBookId());
			homework.setCreateBy(userExerciseAnswer.getUid());
			homework.setUpdateBy(userExerciseAnswer.getUid());

			Long homeworkId = userVirtualHomeworkDao.insert(homework);
			homework.setId(homeworkId);

			List<VirtualHomeworkDetail> details = new ArrayList<VirtualHomeworkDetail>();

			for (Long questionIds : questionList) {
				VirtualHomeworkDetail detail = new VirtualHomeworkDetail();
				detail.setElementId(questionIds);
				detail.setElementType(0);
				detail.setHomeworkId(homeworkId);
				detail.setCreateBy(userExerciseAnswer.getUid());
				detail.setUpdateBy(userExerciseAnswer.getUid());
				details.add(detail);
			}

			userVirtualHomeworkDao.insertDetailBatch(details, userExerciseAnswer.getUid());

			return homeworkId;
		}


		return null;
	}

	/**
	 * 保存用户做错的题目, 同时写redis和数据库
	 * @param uid
	 * @param boxId
	 * @param questionIdMap
	 * @throws DataAccessException
	 */
	@Override
	public void saveUserWrongQuestions(Long uid, Long boxId, HashMap<String, String> questionIdMap){
		try {
			if (questionIdMap!=null && questionIdMap.size() != 0) {
			    //写入到数据库里
                List<UserWrongQuestion> saveList = Lists.newArrayList();
                for (Entry<String, String> entry : questionIdMap.entrySet() ) {
                    saveList.add(generateUserWrongQuestion(uid,boxId,entry.getKey(),entry.getValue()));
                }
                if (CollectionUtils.isNotEmpty(saveList)) {
                    userWrongQuestionService.batchSave(uid,boxId,saveList);
                }

                compatableRedisClusterClient.del(NewRedisConsts.getUserQuestionboxWrong(uid, boxId));
			}
		}catch (Exception e){
			logger.error("Error when save wrong questions with uid:{}, box id:{}", uid, boxId, e);
		}
	}

	private UserWrongQuestion generateUserWrongQuestion(Long uid, Long boxId, String key, String value) {
		UserWrongQuestion userWrongQuestion = new UserWrongQuestion();
		userWrongQuestion.setUid(uid);
		userWrongQuestion.setQuestionBoxId(boxId);
		userWrongQuestion.setKey(key);
		userWrongQuestion.setQuestionIdList(value);
		return userWrongQuestion;
	}

	/**
	 * 根据key获取用户做错的题目列表
	 * @param uid
	 * @param boxId
	 * @param key
	 * @return
	 * @throws DataAccessException
	 */
	@Override
	public String getUserWrongQuestions(Long uid, Long boxId, String key) throws DataAccessException {
		logger.debug("UserQuestionBoxNewChapterServiceImpl.getUserWrongQuestions.level1：key:{}",key);
		logger.debug("UserQuestionBoxNewChapterServiceImpl.getUserWrongQuestions.level1：key:{}",NewRedisConsts.getUserQuestionboxWrong(uid, boxId));
        checkAndReloadWrongQuestions(uid, boxId);
		String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getUserQuestionboxWrong(uid, boxId), key);
		return redisQIds;
	}

	/**
	 * 保存用户做过的题目, 同时写redis和数据库
	 * @param uid
	 * @param boxId
	 * @param questionIdMap
	 * @throws DataAccessException
	 */
	@Override
	public void saveUserDoneQuestions(Long uid, Long boxId, HashMap<String, String> questionIdMap,List<Long> questionIds) throws DataAccessException {
	    try{
            if (questionIdMap!=null && questionIdMap.size() != 0) {
                List<UserDoneQuestion> saveList = Lists.newArrayList();
                for (Entry<String, String> entry : questionIdMap.entrySet() ) {
                    saveList.add(generateUserDoneQuestion(uid,boxId,entry.getKey(),entry.getValue()));
                }
                if (CollectionUtils.isNotEmpty(saveList)) {
                    userDoneQuestionService.batchSave(uid,boxId,saveList);
                }
                compatableRedisClusterClient.del(NewRedisConsts.getUserQuestionboxDone(uid, boxId));
            }
            //题库4.8需求
            if (questionIds != null && questionIds.size() > 0) {
                Questionbox questionbox = knowledgeResource.getQuestionBoxById(boxId);
                if (questionbox != null && questionbox.getSecondCategory() != null) {
                    doTikuQuestionCalculate(questionIds.size(),questionbox.getSecondCategory(),uid);
                }

            }
        }catch (Exception e){
            logger.error("Error when save done questions with uid:{}, box id:{}", uid, boxId, e);
        }
	}
	private UserDoneQuestion generateUserDoneQuestion(Long uid, Long boxId, String key, String value) {
		UserDoneQuestion userDoneQuestion = new UserDoneQuestion();
		userDoneQuestion.setUid(uid);
		userDoneQuestion.setQuestionBoxId(boxId);
		userDoneQuestion.setKey(key);
		userDoneQuestion.setQuestionIdList(value);
		return userDoneQuestion;
	}

    // 计算用户做题数量统计
    private void doTikuQuestionCalculate(int doListSize,Long secondeCategory,Long uid) {
        TikuQuestionCalculate tikuQuestionCalculate = new TikuQuestionCalculate();
        try {
            tikuQuestionCalculate = tikuQuestionCalculateDao.getByUidSecondCategory(uid,secondeCategory);
            if (tikuQuestionCalculate != null) {
                Boolean b = DateUtils.isSameDay(tikuQuestionCalculate.getTodayDate(),new Date());
                if (b) {
                    tikuQuestionCalculate.setTodayCount(tikuQuestionCalculate.getTodayCount() + doListSize);
                    tikuQuestionCalculate.setTotalCount(tikuQuestionCalculate.getTotalCount() + doListSize);
                    tikuQuestionCalculate.setUpdateDate(new Date());
                    tikuQuestionCalculateDao.update(tikuQuestionCalculate);
                }else {
                    tikuQuestionCalculate.setTodayCount((long)doListSize);
                    tikuQuestionCalculate.setTotalCount(tikuQuestionCalculate.getTotalCount() + doListSize);
                    tikuQuestionCalculate.setUpdateDate(new Date());
                    tikuQuestionCalculateDao.update(tikuQuestionCalculate);
                }
            }else {
                TikuQuestionCalculate tikuQuestionCalculate1 = new TikuQuestionCalculate();
                tikuQuestionCalculate1.setUid(uid);
                tikuQuestionCalculate1.setSecondCategory(secondeCategory);
                tikuQuestionCalculate1.setTodayCount((long)doListSize);
                tikuQuestionCalculate1.setTotalCount((long)doListSize);
                Date now = new Date();
                tikuQuestionCalculate1.setTodayDate(now);
                tikuQuestionCalculate1.setUpdateDate(now);
                tikuQuestionCalculate1.setCreateDate(now);
                tikuQuestionCalculateDao.insert(tikuQuestionCalculate1);
            }

        } catch (DataAccessException e) {
            e.printStackTrace();
        }


    }


	/**
	 * 根据key获取用户做过的题目列表
	 * @param uid
	 * @param boxId
	 * @param key
	 * @return
	 * @throws DataAccessException
	 */
	@Override
	public String getUserDoneQuestions(Long uid, Long boxId, String key) throws DataAccessException {
	    checkAndReloadDoneQuestions(uid, boxId);
		String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getUserQuestionboxDone(uid, boxId), key);
		return redisQIds;
	}

	void reloadWrongQuestionsFromMaster(Long uid, Long boxId) throws DataAccessException{
		String key = NewRedisConsts.getUserQuestionboxWrong(uid, boxId);
		List<UserWrongQuestion> list = userWrongQuestionService.getByUidQboxIdAndKeyLikeFromMaster(uid,boxId, NewRedisConsts.QUESTION_BOX_NEW_PREFIX);
		Map<String, String> questionMap = Maps.newHashMap();
		if (CollectionUtils.isNotEmpty(list)) {
			for (UserWrongQuestion wrongQuestion : list) {
				questionMap.put(wrongQuestion.getKey(), wrongQuestion.getQuestionIdList());
			}
			compatableRedisClusterClient.hmset(key, questionMap);
			logger.debug("checkAndReloadWrongQuestions.key:{}",GsonUtil.toJson(key));
			compatableRedisClusterClient.expire(key, Constants.USER_QUESTIONS_EXPIRE_TIME);
		}

	}


	void checkAndReloadWrongQuestions(Long uid, Long boxId) throws DataAccessException{
	    String key = NewRedisConsts.getUserQuestionboxWrong(uid, boxId);
	    Boolean exist = compatableRedisClusterClient.exists(key);
	    if (exist==null || !exist) {
            //缓存中不存在数据,reload数据到缓存
            List<UserWrongQuestion> list = userWrongQuestionService.getByUidQboxIdAndKeyLike(uid,boxId, NewRedisConsts.QUESTION_BOX_NEW_PREFIX);
            Map<String, String> questionMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(list)) {
                for (UserWrongQuestion wrongQuestion : list) {
                    questionMap.put(wrongQuestion.getKey(), wrongQuestion.getQuestionIdList());
                }
                compatableRedisClusterClient.hmset(key, questionMap);
				logger.debug("checkAndReloadWrongQuestions.key:{}",GsonUtil.toJson(key));
                compatableRedisClusterClient.expire(key, Constants.USER_QUESTIONS_EXPIRE_TIME);
            }
        }

    }
    void checkAndReloadDoneQuestions(Long uid, Long boxId) throws DataAccessException{
        String key = NewRedisConsts.getUserQuestionboxDone(uid, boxId);
        Boolean exist = compatableRedisClusterClient.exists(key);
		if (exist==null || !exist) {
            //缓存中不存在数据,reload数据到缓存
            List<UserDoneQuestion> list = userDoneQuestionService.getByUidQboxIdAndKeyLike(uid,boxId,NewRedisConsts.QUESTION_BOX_NEW_PREFIX);
            Map<String, String> questionMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(list)) {
                for (UserDoneQuestion doneQuestion : list) {
                    questionMap.put(doneQuestion.getKey(), doneQuestion.getQuestionIdList());
                }
                compatableRedisClusterClient.hmset(key, questionMap);
                compatableRedisClusterClient.expire(key, Constants.USER_QUESTIONS_EXPIRE_TIME);
            }
        }
    }
	@Override
	public String getUserWipeOutWrongQuestions(Long uid, Long boxId, String key) throws DataAccessException {
		checkAndReloadWipeOutWrongQuestions(uid, boxId);
		String redisQIds = compatableRedisClusterClient.hget(NewRedisConsts.getWipeOutQuestionboxWrong(uid, boxId), key);
		return redisQIds;
	}

	@Override
	public Map<String,String> getUserWipeOutWrongQuestionsAll(Long uid, Long boxId) throws DataAccessException {
		checkAndReloadWipeOutWrongQuestions(uid, boxId);
		Map<String, String> map = compatableRedisClusterClient.hgetAll(NewRedisConsts.getWipeOutQuestionboxWrong(uid, boxId));
		return map;
	}

	void checkAndReloadWipeOutWrongQuestions(Long uid, Long boxId) throws DataAccessException{
		String key = NewRedisConsts.getWipeOutQuestionboxWrong(uid, boxId);
		Boolean exist = compatableRedisClusterClient.exists(key);
		if (exist==null || !exist) {
			//缓存中不存在数据,reload数据到缓存
			List<UserWipeOutWrongQuestion> list = userWipeOutWrongQuestionService.getByUidQboxIdAndKeyLike(uid,boxId,NewRedisConsts.QUESTION_BOX_NEW_PREFIX);
			Map<String, String> questionMap = Maps.newHashMap();
			if (CollectionUtils.isNotEmpty(list)) {
				for (UserWipeOutWrongQuestion wipeOutWrongQuestion : list) {
					questionMap.put(wipeOutWrongQuestion.getKey(), wipeOutWrongQuestion.getQuestionIdList());
				}
				if (!questionMap.isEmpty()) {
					compatableRedisClusterClient.hmset(key, questionMap);
					compatableRedisClusterClient.expire(key, Constants.USER_QUESTIONS_EXPIRE_TIME);
				}
			}
		}

	}

	@Override
	public void saveUserWipeOutWrongQuestions(Long uid, Long boxId, HashMap<String, String> questionIdMap){
		try {
			if (questionIdMap!=null && questionIdMap.size() != 0) {
				//写入到数据库里
				List<UserWipeOutWrongQuestion> saveList = Lists.newArrayList();
				for (Entry<String, String> entry : questionIdMap.entrySet() ) {
					saveList.add(generateUserWipeOutWrongQuestion(uid,boxId,entry.getKey(),entry.getValue()));
				}
				if (CollectionUtils.isNotEmpty(saveList)) {
					userWipeOutWrongQuestionService.batchSave(uid,boxId,saveList);
				}

				compatableRedisClusterClient.del(NewRedisConsts.getWipeOutQuestionboxWrong(uid, boxId));
			}
		}catch (Exception e){
			logger.error("Error when save wipe out wrong questions with uid:{}, box id:{}", uid, boxId, e);
		}
	}

	private UserWipeOutWrongQuestion generateUserWipeOutWrongQuestion(Long uid, Long boxId, String key, String value) {
		UserWipeOutWrongQuestion userWipeOutWrongQuestion = new UserWipeOutWrongQuestion();
		userWipeOutWrongQuestion.setUid(uid);
		userWipeOutWrongQuestion.setQuestionBoxId(boxId);
		userWipeOutWrongQuestion.setKey(key);
		userWipeOutWrongQuestion.setQuestionIdList(value);
		return userWipeOutWrongQuestion;
	}


    void checkAndReloadWrongQuestionsBatch(Long uid, List<Long> boxIdList) throws DataAccessException{
	    if (CollectionUtils.isNotEmpty(boxIdList)) {
			Map<Long, Boolean> responses = new HashMap<Long, Boolean>(boxIdList.size());
			for (Long boxId : boxIdList) {
				responses.put(boxId, compatableRedisClusterClient.exists(NewRedisConsts.getUserQuestionboxWrong(uid, boxId)));
			}
			List<Long> dbBoxIdList = Lists.newArrayList();
			if (responses != null && !responses.isEmpty()) {
				for (Long key : responses.keySet()) {
					Boolean exists = responses.get(key);
					if (!exists) {
						dbBoxIdList.add(key);
					}
				}
			}

			if (CollectionUtils.isEmpty(dbBoxIdList)) {
				return;
			} else {
				//缓存中不存在数据,reload数据到缓存
				List<UserWrongQuestion> list = userWrongQuestionService.getByUidQboxIdListAndKeyLike(uid, dbBoxIdList, NewRedisConsts.QUESTION_BOX_NEW_PREFIX);
				Map<Long, Map<String, String>> boxQuestionMap = Maps.newHashMap();
				if (CollectionUtils.isNotEmpty(list)) {
					for (UserWrongQuestion wrongQuestion : list) {
						Long boxId = wrongQuestion.getQuestionBoxId();
						Map<String, String> questionMap = boxQuestionMap.get(boxId);
						if (questionMap == null) {
							questionMap = Maps.newHashMap();
						}
						questionMap.put(wrongQuestion.getKey(), wrongQuestion.getQuestionIdList());
						boxQuestionMap.put(boxId, questionMap);
					}
					for (Long boxId : dbBoxIdList) {
						Map<String, String> questionMap = boxQuestionMap.get(boxId);
						if (questionMap != null && questionMap.size() > 0) {
							compatableRedisClusterClient.hmset(NewRedisConsts.getUserQuestionboxWrong(uid, boxId), questionMap);
							compatableRedisClusterClient.expire(NewRedisConsts.getUserQuestionboxWrong(uid, boxId), Constants.USER_QUESTIONS_EXPIRE_TIME);
						}
					}
				}
			}
        }
    }
    void checkAndReloadDoneQuestionsBatch(Long uid, List<Long> boxIdList) throws DataAccessException{
        if (CollectionUtils.isNotEmpty(boxIdList)) {
			Map<Long, Boolean> responses = new HashMap<Long, Boolean>(boxIdList.size());
			for (Long boxId : boxIdList) {
				responses.put(boxId, compatableRedisClusterClient.exists(NewRedisConsts.getUserQuestionboxDone(uid, boxId)));
			}
			List<Long> dbBoxIdList = Lists.newArrayList();
			if (responses != null && !responses.isEmpty()) {
				for (Long key : responses.keySet()) {
					Boolean exists = responses.get(key);
					if (!exists) {
						dbBoxIdList.add(key);
					}
				}
			}

			if (CollectionUtils.isEmpty(dbBoxIdList)) {
				return;
			} else {
				//缓存中不存在数据,reload数据到缓存
				List<UserDoneQuestion> list = userDoneQuestionService.getByUidQboxIdListAndKeyLike(uid, dbBoxIdList, NewRedisConsts.QUESTION_BOX_NEW_PREFIX);
				Map<Long, Map<String, String>> boxQuestionMap = Maps.newHashMap();
				if (CollectionUtils.isNotEmpty(list)) {
					for (UserDoneQuestion doneQuestion : list) {
						Long boxId = doneQuestion.getQuestionBoxId();
						Map<String, String> questionMap = boxQuestionMap.get(boxId);
						if (questionMap == null) {
							questionMap = Maps.newHashMap();
						}
						questionMap.put(doneQuestion.getKey(), doneQuestion.getQuestionIdList());
						boxQuestionMap.put(boxId, questionMap);
					}
					for (Long boxId : dbBoxIdList) {
						Map<String, String> questionMap = boxQuestionMap.get(boxId);
						if (questionMap != null && questionMap.size() > 0) {
							compatableRedisClusterClient.hmset(NewRedisConsts.getUserQuestionboxDone(uid, boxId), questionMap);
							compatableRedisClusterClient.expire(NewRedisConsts.getUserQuestionboxDone(uid, boxId), Constants.USER_QUESTIONS_EXPIRE_TIME);
						}
					}
				}
			}
        }
    }


    @Override
    public List<Long> getUserWrongBoxQuestionUncategorized(Long uid, Long boxId) throws DataAccessException {
	    //查询该用户uid 该科目下category id 的所有错题 id list
        Type type = new TypeToken<List<Long>>() {}.getType();
        Set<Long> filterQuestionIds = new HashSet<Long>();
        checkAndReloadWrongQuestions(uid, boxId);
        String[] fields = {NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DAN_XUAN),
                NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.BU_DING_XUAN),
                NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DUO_XUAN),
                NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.PAN_DUAN),
                NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.TIAN_KONG),
                NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.WEN_DA),
                NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.AN_LI_FEN_XI)};
        //所有题目即所有题型的题目
        List<String> allQIdList = compatableRedisClusterClient.hmget(NewRedisConsts.getUserQuestionboxWrong(uid, boxId), fields);
        if(null != allQIdList && !allQIdList.isEmpty()) {
            for (String qIdsStr : allQIdList) {
                if (StringUtils.isNotBlank(qIdsStr)) {
                    List<Long> qidList = GsonUtil.getGson().fromJson(qIdsStr, type);
                    filterQuestionIds.addAll(qidList);
                }
            }
        }
        // 过滤掉关闭的错题
        Map<Long, Integer> questionStatMap = knowledgeResource.getQuestionStateByIdList(new ArrayList<Long>(filterQuestionIds));
        List<Long> resultUserWrongList = new ArrayList<Long>();
        Integer state = null;
        for (Long aLong : filterQuestionIds) {
            if(null != questionStatMap && !questionStatMap.isEmpty()) { //判断是否为空
                state = questionStatMap.get(aLong);
                if(null != state && state.equals(Consts.QuestionState.VALID)){
                    resultUserWrongList.add(aLong);
                }
            }
        }
        //TODO 这里要查询QuestionKnowledgeGraph
        //经过调查，knowledge服务中的代码逻辑，查询结果不包括没有关联知识点的question id
        Map<Long, List<QuestionKnowledge>> questionMap = knowledgeResource.getKnowledgeByQuestionIdList(resultUserWrongList);
        List<Long> subtractDatas = new ArrayList<Long>();
        if (questionMap != null) {
            List<Long> list = new ArrayList<Long>(questionMap.keySet());
            //所有错题与关联知识点的题，取差集，结果就是我们想要的未分类题目
            subtractDatas = (List<Long>)CollectionUtils.subtract(resultUserWrongList,list);
            return subtractDatas;
        } else {
            return subtractDatas;
        }

    }

    @Override
    public List<Long> getWipeOutWrongBoxQuestionUncategorized(Long uid, Long boxId) throws DataAccessException {

        Type type = new TypeToken<List<Long>>() {}.getType();
        //1、获取该用户该科目下 所有已经消灭的错题。2、获取该科目下不在知识点下的题目。然后取差集
        Map<String, String> allWipeOutMap = this.getUserWipeOutWrongQuestionsAll(uid,boxId);

        List<Long> wipeOutQuestionIds = new ArrayList<Long>();

        if(null != allWipeOutMap) {
            Iterator allWipeOutIter = allWipeOutMap.entrySet().iterator();
            while (allWipeOutIter.hasNext()) {
                Entry allWrongEntry = (Entry)allWipeOutIter.next();
                String qidStr = allWrongEntry.getValue().toString();
                List<Long> oriQuestionIds = GsonUtil.getGson().fromJson(qidStr, type);
                wipeOutQuestionIds.addAll(oriQuestionIds);
            }
        }
		//TODO 这里要查询QuestionKnowledgeGraph
        //经过调查，knowledge服务中的代码逻辑，查询结果不包括没有关联知识点的question id
        Map<Long, List<QuestionKnowledge>> questionMap = knowledgeResource.getKnowledgeByQuestionIdList(wipeOutQuestionIds);
        List<Long> subtractDatas = new ArrayList<Long>();
        if (questionMap != null) {
            List<Long> list = new ArrayList<Long>(questionMap.keySet());
            //所有错题与关联知识点的题，取差集，结果就是我们想要的未分类题目
            subtractDatas = (List<Long>)CollectionUtils.subtract(wipeOutQuestionIds,list);
            return subtractDatas;
        } else {
            return subtractDatas;
        }
    }

    @Override
    public Map<Integer, List<Long>> getUserWrongBoxQuestionAccordingToQType(Long uid, Long boxId) throws DataAccessException {

        Map<Integer,List<Long>> map = new HashMap<Integer, List<Long>>();
        //单选
        List<Long> dan_xuan_list = filterClosedQuestion(this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DAN_XUAN)));
        map.put(Consts.Question_QType.DAN_XUAN, filterAnliQuestion(dan_xuan_list));

        //多选
        List<Long> duo_xuan_list = filterClosedQuestion(this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DUO_XUAN)));
        map.put(Consts.Question_QType.DUO_XUAN, filterAnliQuestion(duo_xuan_list));

        //不定项
        List<Long> bu_ding_xuan_list = filterClosedQuestion(this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.BU_DING_XUAN)));
        map.put(Consts.Question_QType.BU_DING_XUAN, filterAnliQuestion(bu_ding_xuan_list));

        //判断
        List<Long> pan_duan_list = filterClosedQuestion(this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.PAN_DUAN)));
        map.put(Consts.Question_QType.PAN_DUAN,filterAnliQuestion(pan_duan_list) );

        //填空
       /* List<Long> tian_kong_list = filterClosedQuestion(this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.TIAN_KONG)));
        Map<String,List<Long>> map_tian_kong = filterAnliQuestion(tian_kong_list,Consts.Question_QType.TIAN_KONG);
        if (map_tian_kong != null) {
            map.put(Consts.Question_QType.TIAN_KONG, map_tian_kong.get(Consts.Question_QType.TIAN_KONG));
        }*/

        //问答
       /* List<Long> wen_da_list = filterClosedQuestion(this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.WEN_DA)));
        Map<String,List<Long>> map_wen_da = filterAnliQuestion(wen_da_list,Consts.Question_QType.WEN_DA);
        if (map_wen_da != null) {
            map.put(Consts.Question_QType.WEN_DA, map_wen_da.get(Consts.Question_QType.WEN_DA));
        }*/

        //map.put(Consts.Question_QType.AN_LI_FEN_XI, filterClosedQuestion(this.getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.AN_LI_FEN_XI))));

        return map;
    }



    List<Long> filterAnliQuestion(List<Long> idList) {
        List<Long> idListWithoutMuti = new ArrayList<Long>();
        if (idList != null && idList.size() > 0) {
            List<Question> questions = knowledgeResource.getQuestionByIds(idList);
            if (questions != null && questions.size() > 0) {
                for (int i = 0; i < questions.size(); i++) {
                    if (questions.get(i).getIsMulti() != 1) {
                        idListWithoutMuti.add(questions.get(i).getId());
                    }
                }
            }
        }
	    return idListWithoutMuti;

    }


    //从idList中（这里是有当前题型和案例题两种类型的题目id），拆分案例题和当前题型 。
    Map<String,List<Long>> filterAnliQuestion(List<Long> idList,int questionType) {

        Map<String, List<Long>> questionTypeMap = new HashMap<String, List<Long>>();

        if (idList != null && idList.size() > 0) {
            for (int i = 0; i < idList.size() ; i++) {
                final Long questionId = idList.get(i);
                List<Question> questions = knowledgeResource.getQuestionByIds(new ArrayList<Long>(){{add(questionId);}});
                if (questions != null && questions.size() > 0) {
                    if (questionType == questions.get(0).getQtype()) {
                        //当前类型题目
                        if (questionTypeMap.get(String.valueOf(questionType)) != null) {
                            //
                            List<Long> idLists = questionTypeMap.get(String.valueOf(questionType));
                            idLists.add(questionId);
                            questionTypeMap.put(String.valueOf(questionType),idLists);

                        }else {
                            questionTypeMap.put(String.valueOf(questionType),new ArrayList<Long>(){{add(questionId);}});
                        }
                    } else {
                        //案例题
                        if (questionTypeMap.get(String.valueOf(Consts.Question_QType.AN_LI_FEN_XI)) != null) {
                            //
                            List<Long> idLists = questionTypeMap.get(String.valueOf(Consts.Question_QType.AN_LI_FEN_XI));
                            idLists.add(questionId);
                            questionTypeMap.put(String.valueOf(Consts.Question_QType.AN_LI_FEN_XI),idLists);

                        }else {
                            questionTypeMap.put(String.valueOf(Consts.Question_QType.AN_LI_FEN_XI),new ArrayList<Long>(){{add(questionId);}});
                        }

                    }
                }


            }

        }

        return questionTypeMap;
    }

    @Override
    public Map<Integer, List<Long>> getWipeOutWrongBoxQuestionAccordingToQType(Long uid, Long boxId) throws DataAccessException {

        Map<Integer,List<Long>> map = new HashMap<Integer, List<Long>>();
        Type type = new TypeToken<List<Long>>() {}.getType();

        Map<String, String> allWipeOutMap = this.getUserWipeOutWrongQuestionsAll(uid,boxId);
        for(int i =0 ; i <= 6; i++){
            String key = NewRedisConsts.getUserQuestionboxWrongQtype(i);
            String value = allWipeOutMap.get(key);
            if (value != null && !"".equals(value)) {
                List<Long> questionIds = GsonUtil.getGson().fromJson(value, type);
                map.put(i, questionIds);
            }
        }
        return map;

    }

    @Override
    public Boolean resetNewChapterPractice(final Long uid, final Long boxId, Long teachBookId) throws DataAccessException {
        try {
        	List<UserDoneQuestion> userDoneQuestions = userDoneQuestionService.getByUidQboxId(uid, boxId);
        	if (CollectionUtils.isNotEmpty(userDoneQuestions)) {
        		List<UserDoneQuestion> updateList = Lists.newArrayList();
				for (UserDoneQuestion userDoneQuestion : userDoneQuestions) {
					//章节和知识点的key置空
					if (StringUtils.startsWith(userDoneQuestion.getKey(), NewRedisConsts.User_QuestionBox_Done_chapter)
							|| StringUtils.startsWith(userDoneQuestion.getKey(), NewRedisConsts.User_QuestionBox_Done_knowledge)) {
						userDoneQuestion.setQuestionIdList("[]");
						updateList.add(userDoneQuestion);
					}
				}
				if (CollectionUtils.isNotEmpty(updateList)) {
					userDoneQuestionService.batchSave(uid, boxId, updateList);
					//删除缓存以重置
					compatableRedisClusterClient.del(NewRedisConsts.getUserQuestionboxDone(uid, boxId));
				}
			}
			executor.submit(new Runnable() {
				@Override
				public void run() {
					TikuLastChapterExercise tikuLastChapterExercise = new TikuLastChapterExercise();
					try {
						tikuLastChapterExercise = tikuLastChapterExerciseService.getByUidBoxId(uid, boxId);
						if (tikuLastChapterExercise != null && tikuLastChapterExercise.getId() != null) {
							tikuLastChapterExerciseService.delete(tikuLastChapterExercise.getId());
						}
					} catch (DataAccessException e) {
						logger.error("resetChapterPractice", e);
						e.printStackTrace();
					}
				}
			});
		}catch (Exception e) {
			logger.error("resetNewChapterPractice catch exception", e);
        	return false;
		}
		return true;
    }

    List<Long> filterClosedQuestion(String ids) {

        Type type = new TypeToken<List<Long>>() {}.getType();
        Set<Long> filterQuestionIds = new HashSet<Long>();

        List<Long> qIdQtypeList = GsonUtil.getGson().fromJson(ids, type);
        if (qIdQtypeList != null && qIdQtypeList.size() > 0) {
            filterQuestionIds.addAll(qIdQtypeList);
        }

        Map<Long, Integer> questionStatMap = knowledgeResource.getQuestionStateByIdList(new ArrayList<Long>(filterQuestionIds));
        List<Long> resultUserWrongList = new ArrayList<Long>();
        Integer state = null;
        for (Long aLong : filterQuestionIds) {
            if(null != questionStatMap && !questionStatMap.isEmpty()) { //判断是否为空
                state = questionStatMap.get(aLong);
                if(null != state && state.equals(Consts.QuestionState.VALID)){
                    resultUserWrongList.add(aLong);
                }
            }
        }
        return resultUserWrongList;
    }

    @Override
	public VirtualHomework saveVirtualHomework( Long boxId,
												Long uid,
												Long objId,
												Integer objType,
												Integer randomType,
												Long teachBookId,
												String homeworkName,
												List<Long> resultQuestionIds) throws DataAccessException {
		VirtualHomework homework = new VirtualHomework();
		homework.setUid(uid);
		homework.setName(homeworkName);
		homework.setNum(resultQuestionIds.size());
		homework.setHomeworkTypeId(objId != null ? objId : 0L);//作业类型id(比如章节id，知识点id)
		homework.setHomeworkType(objType);//作业类型(0：所有，1：章节作业，2：知识点作业)
		homework.setSourceId(boxId);
		homework.setSourceType(0);//作业来源类型（0：题库，1：...）
		homework.setBookId(teachBookId);
		homework.setRandomType(randomType);
		homework.setCreateBy(uid);
		homework.setUpdateBy(uid);

		Long homeworkId = userVirtualHomeworkDao.insert(homework);
		homework.setId(homeworkId);

		List<VirtualHomeworkDetail> details = new ArrayList<VirtualHomeworkDetail>();

		for (Long questionIds : resultQuestionIds) {
			VirtualHomeworkDetail detail = new VirtualHomeworkDetail();
			detail.setElementId(questionIds);
			detail.setElementType(0);
			detail.setHomeworkId(homeworkId);
			detail.setCreateBy(uid);
			detail.setUpdateBy(uid);
			details.add(detail);
		}

		userVirtualHomeworkDao.insertDetailBatch(details, uid);

		homework.setVirtualHomeworkDetails(details);

		return homework;
	}

	/**
	 * 用户做过的题入redis缓存
	 * @param uid
	 * @param teachBookId 教材id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 * */
	@Override
	public void cachePaperDoneQuestion(Long uid, Long teachBookId, Long boxId, List<Long> questionIds) throws DataAccessException{
		if (teachBookId == null) {
			TeachingBook teachingBook = this.getBookByBoxId(boxId);
			if (teachingBook == null) {
				logger.error("teachingBook null,boxId:{}",boxId);
				return;
			} else {
				teachBookId = teachingBook.getId();
			}
		}
		Map<Long,List<Long>> knowledgeQuestionsMap = getKnowledgeGraphQuestionMap(questionIds);
		HashMap<String, String> allMap = Maps.newHashMap();
		Map<String, List<Long>> chapterListMap = Maps.newHashMap();
		for (Map.Entry<Long,List<Long>> entry : knowledgeQuestionsMap.entrySet()) {
			Long knowledgeId = entry.getKey();
			List<Long> qIds = entry.getValue();
			//处理章节关系
			MapUtil.mergeListMap(chapterListMap, dealPaperDoneQuestion4Chapter(uid, knowledgeId, teachBookId, boxId, qIds));
			//处理知识点关系
			allMap.putAll(dealPaperDoneQuestion4Knowledge(uid, knowledgeId, teachBookId, boxId, qIds));
		}
		for (Entry<String, List<Long>> entry : chapterListMap.entrySet()) {
			allMap.put(entry.getKey(), GsonUtil.toJson(entry.getValue()));
		}
		//处理题型关系
		Map<String, String> typeQuestionMap= dealUserBoxDoneQuestion4Qtype(uid, boxId, questionIds);
		allMap.putAll(typeQuestionMap);

		logger.debug("cachePaperDoneQuestion info," +
						"uid:{},teachBookId:{},boxId:{},qIds:{},allMap:{}",
				uid,teachBookId,boxId,GsonUtil.toJson(questionIds),GsonUtil.toJson(allMap));
		saveUserDoneQuestions(uid, boxId, allMap,questionIds);
	}


	private Map<Long,List<Long>> getKnowledgeGraphQuestionMap(List<Long> questionIds) throws DataAccessException{
		Map<Long,List<Long>> knowledgeQuestionsMap = Maps.newHashMap();
		List<QuestionKnowledgeGraph> questionKnowledges = knowledgeResource.getKnowledgeGraphByQuestionIdList(questionIds);
		for(QuestionKnowledgeGraph questionKnowledgeGraph : questionKnowledges) {

			//获取知识点
			Long knowledgeId = questionKnowledgeGraph.getKnowledgeGraphId();
			List<Long> qList = knowledgeQuestionsMap.get(knowledgeId);
			if (qList == null){
				qList = Lists.newArrayList();
			}
			if (!qList.contains(questionKnowledgeGraph.getQuestionId())) {
				qList.add(questionKnowledgeGraph.getQuestionId());
			}
			knowledgeQuestionsMap.put(knowledgeId, qList);
		}
		return knowledgeQuestionsMap;
	}

	private Map<String, List<Long>> dealPaperDoneQuestion4Chapter(Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds)throws DataAccessException{
		return dealPaperDoneQuestion4Chapter(uid, knowledgeId, teachBookId, boxId, questionIds, false);
	}
	private Map<String, List<Long>> dealPaperDoneQuestion4Chapter(Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds, boolean isSync)throws DataAccessException{
		return dealPaperQuestion4ChapterByType(uid, knowledgeId, teachBookId, boxId, questionIds, isSync, Consts.QuestionLogDealType.done);
	}
	private Map<String, String> dealPaperDoneQuestion4Knowledge(Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds)throws DataAccessException{
		return dealPaperDoneQuestion4Knowledge(uid, knowledgeId, teachBookId, boxId, questionIds, false);
	}
	/**
	 * 处理用户做过的题-知识点存储
	 * */
	private Map<String, String> dealPaperDoneQuestion4Knowledge(Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds, boolean isSync)throws DataAccessException{
		java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {}.getType();

		Map<String, String> qIdRedisMap = new HashMap<String, String>();
		//取出用户该知识点的已做过的题
		String redisQIds = "";
		if (!isSync) {
			redisQIds = getUserDoneQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, knowledgeId));
		}
		if (StringUtils.isBlank(redisQIds)) {
			qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId,knowledgeId), GsonUtil.toJson(questionIds));
		}else {
			List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
			HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
			redisQIdSet.addAll(questionIds);

			qIdRedisMap.put(NewRedisConsts.getUserQuestionboxDoneKnowledge(teachBookId, knowledgeId), GsonUtil.toJson(redisQIdSet));
		}
		return qIdRedisMap;
	}

	/**
	 * 用户错题入redis缓存
	 * @param uid
	 * @param teachBookId 教材id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 * @param topicIds 答错的子题id列表
	 * */
	@Override
	public void cachePaperWrongQuestion(Long uid, Long teachBookId, Long boxId, List<Long> questionIds, List<Long> topicIds) throws DataAccessException{
		if (teachBookId == null) {
			TeachingBook teachingBook = this.getBookByBoxId(boxId);
			if (teachingBook == null) {
				logger.error("teachingBook null,boxId:{}",boxId);
				return;
			} else {
				teachBookId = teachingBook.getId();
			}
		}

		Map<Long,List<Long>> knowledgeQuestionsMap = getKnowledgeGraphQuestionMap(questionIds);
		HashMap<String, String> allMap = Maps.newHashMap();
		Map<String,List<Long>> chapterListMap = Maps.newHashMap();
		for (Map.Entry<Long,List<Long>> entry : knowledgeQuestionsMap.entrySet()) {
			Long knowledgeId = entry.getKey();
			List<Long> qIds = entry.getValue();
			//处理章节关系
			MapUtil.mergeListMap(chapterListMap,dealPaperWrongQuestion4Chapter(uid, knowledgeId, teachBookId, boxId, qIds));
			//处理知识点关系
			allMap.putAll(dealPaperWrongQuestion4Knowledge(uid, knowledgeId, teachBookId, boxId, qIds));
		}
		for (Entry<String, List<Long>> entry : chapterListMap.entrySet()) {
			allMap.put(entry.getKey(), GsonUtil.toJson(entry.getValue()));
		}
		//处理题型关系
		Map<String, String> typeQuestionMap= this.dealUserBoxWrongQuestion4Qtype(uid,teachBookId, boxId, questionIds, topicIds);
		allMap.putAll(typeQuestionMap);

		logger.debug("cachePaperWrongQuestion info," +
						"uid:{},teachBookId:{},boxId:{},qIds:{},allMap:{}",
				uid,teachBookId,boxId,GsonUtil.toJson(questionIds),GsonUtil.toJson(allMap));
		this.saveUserWrongQuestions(uid, boxId, allMap);
	}

	/**
	 * 用户错题入redis缓存
	 * @param uid
	 * @param teachBookId 教材id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 *
	 * */
	@Override
	public Map<String, String> getPaperWrongQuestionMapByType(Long uid, Long teachBookId, Long boxId, List<Long> questionIds, int type) throws DataAccessException{
		Map<Long,List<Long>> knowledgeQuestionsMap = getKnowledgeGraphQuestionMap(questionIds);
		Map<String, String> allMap = Maps.newHashMap();

		if (type == Consts.Question_Exercise_Type.Chapter) {
			Map<String, List<Long>> chapterListMap = Maps.newHashMap();
			for (Map.Entry<Long, List<Long>> entry : knowledgeQuestionsMap.entrySet()) {
				Long knowledgeId = entry.getKey();
				List<Long> qIds = entry.getValue();
				//处理章节关系
				MapUtil.mergeListMap(chapterListMap, dealPaperWrongQuestion4Chapter(uid, knowledgeId, teachBookId, boxId, qIds));
			}
			for (Entry<String, List<Long>> entry : chapterListMap.entrySet()) {
				allMap.put(entry.getKey(), GsonUtil.toJson(entry.getValue()));
			}
		} else {
			for (Map.Entry<Long, List<Long>> entry : knowledgeQuestionsMap.entrySet()) {
				Long knowledgeId = entry.getKey();
				List<Long> qIds = entry.getValue();
				//处理知识点关系
				allMap.putAll(dealPaperWrongQuestion4Knowledge(uid, knowledgeId, teachBookId, boxId, qIds));
			}
		}
		logger.debug("cachePaperWrongQuestion info," +
						"uid:{},teachBookId:{},boxId:{},qIds:{},allMap:{}",
				uid,teachBookId,boxId,GsonUtil.toJson(questionIds),GsonUtil.toJson(allMap));
		return allMap;
	}

	/**
	 * 根据科目获取教材
	 * @param categoryId
	 * @return
	 */
	@Override
	public TeachingBook getBookByCategoryId(Long categoryId) {
		//获取教材
		List<TeachingBook> teachingBooks = knowledgeResource.klg_getTeachBookItemByCategoryId(categoryId);
		if(null == teachingBooks || teachingBooks.isEmpty()) {
			logger.error("teachingBook null,categoryId:{}",categoryId);
			return null;
		}else {
			return teachingBooks.get(0);
		}
	}

	private TeachingBook getBookByBoxId(Long boxId) {
		Questionbox questionbox = knowledgeResource.getQuestionBoxById(boxId);
		if (questionbox == null) {
			logger.error("can not find questionbox for id :{}", boxId);
			return null;
		}
		Long categoryId = questionbox.getCategoryId();
		return this.getBookByCategoryId(categoryId);
	}
	public Questionbox getBoxByCategoryId(Long categoryId) {
		return knowledgeResource.getQBoxListByCategoryId(categoryId);
	}

	@Override
	public boolean isUserQuestionLogSync(Long uid, Long categoryId) throws DataAccessException {
		UserLogSyncRecord query = new UserLogSyncRecord();
		query.setUid(uid);
		query.setCategoryId(categoryId);
		try {
			List<UserLogSyncRecord> syncRecords = userLogSyncRecordService.findList(query);
			if (CollectionUtils.isNotEmpty(syncRecords)) {
				return true;
			} else {
				UserDoneQuestion query1 = new UserDoneQuestion();
				query1.setUid(uid);
				List<UserDoneQuestion> doneQuestionList = userDoneQuestionService.findList(query1);
				if (CollectionUtils.isEmpty(doneQuestionList)) {
					//没有需要迁移的,标记一下
					UserLogSyncRecord record = new UserLogSyncRecord();
					record.setUid(uid);
					record.setCategoryId(0L);
					record.setQuestionBoxId(0L);
					record.setStatus(1);
					record.setCreateDate(new Date());
					userLogSyncRecordService.insert(record);
					return  true;
				}
			}
		}catch (Exception e) {
			logger.error("isUserQuestionLogSync catch exception", e);
		}
		return false;
	}

	/**
	 * 传入categoryId时, 根据科目id进行迁移
	 * 不传入时, 先检查是否有需要迁移的数据,再根据每一个questionbox迁移
	 * @param uid
	 * @param categoryId
	 * @return
	 * @throws DataAccessException
	 */
	@Override
	public boolean syncUserQuestionLog(Long uid, Long categoryId) throws DataAccessException {
		if (categoryId != null) {
			return syncUserQuestionLogByCategory(uid, categoryId);
		} else{
			return syncUserQuestionLog(uid);
		}
	}
	private boolean syncUserQuestionLog(Long uid) throws DataAccessException {
		//检查是否有需迁移的
		UserDoneQuestion query = new UserDoneQuestion();
		query.setUid(uid);
		List<UserDoneQuestion> doneQuestionList = userDoneQuestionService.findList(query);
		List<Long> boxIds = null;
		if (CollectionUtils.isNotEmpty(doneQuestionList)) {
			boxIds = doneQuestionList.stream().filter(x -> !StringUtils.startsWith(x.getKey(), NewRedisConsts.QUESTION_BOX_NEW_PREFIX)).map(x -> x.getQuestionBoxId()).distinct().collect(Collectors.toList());
		}
		if (CollectionUtils.isNotEmpty(boxIds)) {
			for (Long boxId : boxIds) {
				syncUserQuestionLogByBoxId(uid,boxId);
			}
		} else {
			//没有需要迁移的,标记一下
			UserLogSyncRecord record = new UserLogSyncRecord();
			record.setUid(uid);
			record.setCategoryId(0L);
			record.setQuestionBoxId(0L);
			record.setStatus(1);
			record.setCreateDate(new Date());
			userLogSyncRecordService.insert(record);
		}
		return true;

	}
	private boolean syncUserQuestionLogByBoxId(Long uid, Long boxId) throws DataAccessException {
		Questionbox questionbox = knowledgeResource.getQuestionBoxById(boxId);
		if (questionbox == null) {
			logger.error("can not find questionbox for id :{}", boxId);
			//不需要迁移的,标记一下
			UserLogSyncRecord record = new UserLogSyncRecord();
			record.setUid(uid);
			record.setCategoryId(0L);
			record.setQuestionBoxId(boxId);
			record.setStatus(1);
			record.setCreateDate(new Date());
			userLogSyncRecordService.insert(record);
			return false;
		}
		Long categoryId = questionbox.getCategoryId();
		UserLogSyncRecord record = checkAndInitSyncRecord(uid, categoryId, boxId);
		if (record == null || record.getId() == null){
			logger.error("syncUserQuestionLogByBoxId cannot find or init valid record for uid:{}, categoryId:{}, boxId:{}",uid,categoryId,boxId);
			return false;
		} else if (record.getStatus() != null && record.getStatus() == 1) {
			logger.info("syncUserQuestionLogByBoxId has sync log for uid:{}, categoryId:{}, boxId:{}",uid,categoryId,boxId);
			return true;
		}
		TeachingBook teachingBook = this.getBookByCategoryId(categoryId);
		if (teachingBook == null) {
			logger.error("can not find teachbook for category :{}", categoryId);
			return false;
		}

		Long newBookId = teachingBook.getId();
		try {
			this.syncUserDoneQuestionLog(boxId, uid, newBookId);
			this.syncUserWrongQuestionLog(boxId, uid, newBookId);
			this.syncUserWipeOutWrongQuestionLog(boxId, uid, newBookId);
		} catch (Exception e) {
			logger.error("syncUserQuestionLogByBoxId catch exception", e);
			return false;
		}

		record.setStatus(1);
		updateSyncRecord(record);
		return true;

	}
	private boolean syncUserQuestionLogByCategory(Long uid, Long categoryId) throws DataAccessException {
		Questionbox questionbox = this.getBoxByCategoryId(categoryId);
		if (questionbox == null) {
			logger.error("can not find questionbox for category :{}", categoryId);
			throw new DataAccessException("can not find questionbox for category ");
		}
		Long boxId = questionbox.getId();
		UserLogSyncRecord record = checkAndInitSyncRecord(uid, categoryId, boxId);
		if (record == null || record.getId() == null){
			logger.error("syncUserQuestionLogByCategory cannot find or init valid record for uid:{}, categoryId:{}, boxId:{}",uid,categoryId,boxId);
			return false;
		} else if (record.getStatus() != null && record.getStatus() == 1) {
			logger.info("syncUserQuestionLogByCategory has sync log for uid:{}, categoryId:{}, boxId:{}",uid,categoryId,boxId);
			return true;
		}
		TeachingBook teachingBook = this.getBookByCategoryId(categoryId);
		if (teachingBook == null) {
			logger.error("can not find teachbook for category :{}", categoryId);
			throw new DataAccessException("can not find teachbook for category ");
		}
		Long newBookId = teachingBook.getId();
		this.syncUserDoneQuestionLog(boxId, uid, newBookId);
		this.syncUserWrongQuestionLog(boxId, uid, newBookId);
		this.syncUserWipeOutWrongQuestionLog(boxId, uid, newBookId);
		updateSyncRecord(record);
		return true;
	}
	private void updateSyncRecord(UserLogSyncRecord record) {
		try {
			userLogSyncRecordService.update(record);
		} catch (Exception e) {
			logger.error("updateSyncRecord catch exception", e);
		}
	}
	/**
	 * 检查是不是有,没有的话,insert一个,返回id
	 * @param uid
	 * @param categoryId
	 * @param boxId
	 * @return
	 */
	private UserLogSyncRecord checkAndInitSyncRecord(Long uid, Long categoryId, Long boxId) {
		UserLogSyncRecord result = new UserLogSyncRecord();
		result.setUid(uid);
		result.setCategoryId(categoryId);
		result.setQuestionBoxId(boxId);
		List<UserLogSyncRecord> syncRecordList = null;
		try {
			syncRecordList = userLogSyncRecordService.findList(result);
		} catch (Exception e) {
			logger.error("getSyncRecord find syncRecordList catch exception", e);
		}
		if (CollectionUtils.isNotEmpty(syncRecordList)) {
			result = syncRecordList.get(0);
		} else {
			result.setStatus(0);
			result.setCreateDate(new Date());
			try {
				Long id = userLogSyncRecordService.insert(result);
				result.setId(id);
			}catch (Exception e) {
				logger.error("getSyncRecord save syncRecordList catch exception", e);
			}
		}
		return result;
	}
	private boolean syncUserWrongQuestionLog(Long boxId, Long uid, Long newBookId) throws DataAccessException {
		List<UserWrongQuestion> list = userWrongQuestionService.getByUidQboxIdAndKeyLike(uid,boxId, RedisConsts.User_QuestionBox_Wrong_QType);
		Set<Long> questionIds = Sets.newHashSet();
		Type type = new com.google.gson.reflect.TypeToken<List<Long>>(){}.getType();
		Map<String, String> qtypeMap = Maps.newHashMap();
		for (UserWrongQuestion userWrongQuestion : list) {
			qtypeMap.put(NewRedisConsts.QUESTION_BOX_NEW_PREFIX + userWrongQuestion.getKey(), userWrongQuestion.getQuestionIdList());
			if (StringUtils.isNotBlank(userWrongQuestion.getQuestionIdList())) {
				List<Long> questionIdList = GsonUtil.getGson().fromJson(userWrongQuestion.getQuestionIdList(), type);
				questionIds.addAll(questionIdList);
			}
		}
		//所有题目即所有题型的题目
		Map<Long,List<Long>> knowledgeQuestionsMap = getKnowledgeGraphQuestionMap(Lists.newArrayList(questionIds));
		HashMap<String, String> allMap = Maps.newHashMap();
		Map<String,List<Long>> chapterListMap = Maps.newHashMap();
		for (Map.Entry<Long,List<Long>> entry : knowledgeQuestionsMap.entrySet()) {
			Long knowledgeId = entry.getKey();
			List<Long> qIds = entry.getValue();
			//处理章节关系
			MapUtil.mergeListMap(chapterListMap, dealPaperWrongQuestion4Chapter(uid, knowledgeId, newBookId, boxId, qIds, true));
			//处理知识点关系
			allMap.putAll( dealPaperWrongQuestion4Knowledge(uid, knowledgeId, newBookId, boxId, qIds, true));
		}
		for (Entry<String, List<Long>> entry : chapterListMap.entrySet()) {
			allMap.put(entry.getKey(), GsonUtil.toJson(entry.getValue()));
		}
		allMap.putAll(qtypeMap);
		logger.debug("syncUserWrongQuestionLog info," +
						"uid:{},newBookId:{},boxId:{},qIds:{},allMap:{}",
				uid,newBookId,boxId,GsonUtil.toJson(questionIds),GsonUtil.toJson(allMap));
		this.saveUserWrongQuestions(uid, boxId, allMap);
		return true;
	}


	private boolean syncUserDoneQuestionLog(Long boxId, Long uid, Long newBookId) throws DataAccessException {
		List<UserDoneQuestion> list = userDoneQuestionService.getByUidQboxIdAndKeyLike(uid,boxId, RedisConsts.User_QuestionBox_Done_QType);
		Set<Long> questionIds = Sets.newHashSet();
		Type type = new com.google.gson.reflect.TypeToken<List<Long>>(){}.getType();
		Map<String, String> qtypeMap = Maps.newHashMap();
		for (UserDoneQuestion userDoneQuestion : list) {
			qtypeMap.put(NewRedisConsts.QUESTION_BOX_NEW_PREFIX + userDoneQuestion.getKey(), userDoneQuestion.getQuestionIdList());
			if (StringUtils.isNotBlank(userDoneQuestion.getQuestionIdList())) {
				List<Long> questionIdList = GsonUtil.getGson().fromJson(userDoneQuestion.getQuestionIdList(), type);
				questionIds.addAll(questionIdList);
			}
		}
		List<Long> questionIdList = Lists.newArrayList(questionIds);
		//所有题目即所有题型的题目
		Map<Long,List<Long>> knowledgeQuestionsMap = getKnowledgeGraphQuestionMap(questionIdList);
		HashMap<String, String> allMap = Maps.newHashMap();
		Map<String, List<Long>> chapterListMap = Maps.newHashMap();
		for (Map.Entry<Long,List<Long>> entry : knowledgeQuestionsMap.entrySet()) {
			Long knowledgeId = entry.getKey();
			List<Long> qIds = entry.getValue();
			//处理章节关系
			MapUtil.mergeListMap(chapterListMap, dealPaperDoneQuestion4Chapter(uid, knowledgeId, newBookId, boxId, qIds, true));
			//处理知识点关系
			allMap.putAll( dealPaperDoneQuestion4Knowledge(uid, knowledgeId, newBookId, boxId, qIds, true));
		}
		for (Entry<String, List<Long>> entry : chapterListMap.entrySet()) {
			allMap.put(entry.getKey(), GsonUtil.toJson(entry.getValue()));
		}

		allMap.putAll(qtypeMap);

		logger.debug("syncUserDoneQuestionLog info," +
						"uid:{},newBookId:{},boxId:{},qIds:{},allMap:{}",
				uid,newBookId,boxId,GsonUtil.toJson(questionIdList),GsonUtil.toJson(allMap));
		this.saveUserDoneQuestions(uid, boxId, allMap, questionIdList);
		return true;
	}

	//迁移消灭错题记录数据
	private boolean syncUserWipeOutWrongQuestionLog(Long boxId, Long uid, Long newBookId) throws DataAccessException {
		//取出用户所有的已消灭错题关系
		List<String> fields = Lists.newArrayList(RedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DAN_XUAN),
				RedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.BU_DING_XUAN),
				RedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.DUO_XUAN),
				RedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.PAN_DUAN),
				RedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.TIAN_KONG),
				RedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.WEN_DA),
				RedisConsts.getUserQuestionboxWrongQtype(Consts.Question_QType.AN_LI_FEN_XI));
		Map<String, String> allWipeOutMap = userQuestionBoxService.getUserWipeOutWrongQuestionsAll(uid,boxId);
		if (allWipeOutMap == null || allWipeOutMap.isEmpty()) {
			return true;
		}
		Set<Long> questionIds = Sets.newHashSet();
		Type type = new com.google.gson.reflect.TypeToken<List<Long>>(){}.getType();
		Map<String, String> qtypeMap = Maps.newHashMap();
		for (Entry<String, String> entry : allWipeOutMap.entrySet()) {
			String key = entry.getKey();
			String value = entry.getValue();
			if (fields.contains(key)) {
				qtypeMap.put(NewRedisConsts.QUESTION_BOX_NEW_PREFIX + key, value);
				if (StringUtils.isNotBlank(value)) {
					List<Long> questionIdList = GsonUtil.getGson().fromJson(value, type);
					questionIds.addAll(questionIdList);
				}
			}
		}
		if (CollectionUtils.isEmpty(questionIds)) {
			return true;
		}
		//所有题目即所有题型的题目
		Map<Long,List<Long>> knowledgeQuestionsMap = getKnowledgeGraphQuestionMap(Lists.newArrayList(questionIds));
		HashMap<String, String> allMap = Maps.newHashMap();
		for (Map.Entry<Long,List<Long>> entry : knowledgeQuestionsMap.entrySet()) {
			Long knowledgeId = entry.getKey();
			List<Long> qIds = entry.getValue();
			//处理章节关系
			Map<String, String> chapterQuestionMap = dealWipeOutWrongQuestin4Chapter(uid, knowledgeId, newBookId, boxId, qIds, true);
			//处理知识点关系
			Map<String, String> knowledgeQuestionMap = dealWipeOutWrongQuestin4Knowledge(uid, knowledgeId, newBookId, boxId, qIds, true);
			allMap.putAll(chapterQuestionMap);
			allMap.putAll(knowledgeQuestionMap);
			allMap.putAll(qtypeMap);
		}
		logger.debug("syncUserWipeOutWrongQuestionLog info," +
						"uid:{},newBookId:{},boxId:{},qIds:{},allMap:{}",
				uid,newBookId,boxId,GsonUtil.toJson(questionIds),GsonUtil.toJson(allMap));
		if(allMap.isEmpty()){
			return true;
		}
		this.saveUserWipeOutWrongQuestions(uid,boxId,allMap);
		return true;
	}
	private Map<String, String> dealWipeOutWrongQuestin4Chapter(Long uid,Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds, Boolean isSync) throws DataAccessException{
		HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		//根据知识点id与教材id，查询该教材下，包含该知识点的章节id
		List<ChapterSection> chapterSections = knowledgeResource.getChapterSectionItemByKnowledgeIdAndBookId(teachBookId, knowledgeId);
		Map<String, List<Long>> map = Maps.newHashMap();
		if(CollectionUtils.isNotEmpty(chapterSections)) {
			for (ChapterSection chapterSection : chapterSections) {
				Map<String, List<Long>> chapterData = dealChapterDataNew(chapterSection, teachBookId, boxId, questionIds);
				if (chapterData != null && !chapterData.isEmpty()) {
					for (Entry<String, List<Long>> entry : chapterData.entrySet()) {
						if (CollectionUtils.isEmpty(entry.getValue())) {
							continue;
						}
						//替换key
						String key = NewRedisConsts.User_QuestionBox_Wrong_chapter + entry.getKey();
						List<Long> list = map.get(key);
						if (list == null) {
							list = Lists.newArrayList();
						}
						list.addAll(entry.getValue());
						map.put(key, list.stream().distinct().collect(Collectors.toList()));
					}
				}
			}
		}
		//前边只是获取变量, 如果不是同步操作, 需要获取全部数据合并进来
		if (!isSync) {
			Map<String, String> allWipeOutMap = this.getUserWipeOutWrongQuestionsAll(uid, boxId);
			Type type = new com.google.gson.reflect.TypeToken<List<Long>>(){}.getType();
			for (Entry<String, String> entry : allWipeOutMap.entrySet()) {
				Set<Long> questionIdSet = Sets.newHashSet();
				String key = entry.getKey();
				String value = entry.getValue();
				if (StringUtils.isNotBlank(value)) {
					List<Long> questionIdList = GsonUtil.getGson().fromJson(key, type);
					questionIdSet.addAll(questionIdList);
				}
				List<Long> list = map.get(key);
				if (CollectionUtils.isNotEmpty(list)) {
					questionIdSet.addAll(list);
				}
				map.put(key, Lists.newArrayList(questionIdSet));
			}
		}
		for (Entry<String, List<Long>> entry : map.entrySet()) {
			qIdRedisMap.put(entry.getKey(), GsonUtil.toJson(entry.getValue()));
		}
		return qIdRedisMap;
	}

	private Map<String, String> dealWipeOutWrongQuestin4Knowledge(Long uid,Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds, Boolean isSync) {
		java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {}.getType();

		HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		//取出用户该知识点的错题
		String redisQIds =  "";
		if (!isSync) {
			try {
				redisQIds = this.getUserWipeOutWrongQuestions(uid,boxId, NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, knowledgeId));
			} catch (DataAccessException exception) {
				logger.error("dealWipeOutWrongQuestin4Knowledge get value from redis catch exception", exception);
			}
		}
		if (StringUtils.isBlank(redisQIds)) {
			qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, knowledgeId), GsonUtil.toJson(questionIds));
		}else {
			List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
			HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
			redisQIdSet.addAll(questionIds);

			qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, knowledgeId), GsonUtil.toJson(redisQIdSet));
		}
		return qIdRedisMap;
	}

	private Map<String, List<Long>> dealChapterDataNew(ChapterSection chapterSection, Long teachBookId, Long boxId, List<Long> questionIds){
		HashMap<String, List<Long>> result = new HashMap<String, List<Long>>();
		Type type = new TypeToken<List<Long>>() {}.getType();

		try {
			Set<Long> needDealChapter = new HashSet<Long>();

			Long chapterSectionId = chapterSection.getId();
			needDealChapter.add(chapterSectionId);

			// 获取当前章节的父节点
			String parentIdsStr = chapterSection.getParentIds();
			parentIdsStr = parentIdsStr.substring(0, parentIdsStr.length()-1);
			for (String parendIdStr : parentIdsStr.split(",")) {
				if (!parendIdStr.equals("0")) {
					needDealChapter.add(Long.valueOf(parendIdStr));
				}
			}

			for (Long chapterId : needDealChapter) {
				//取出用户该章节的错题
				result.put(StringUtils.join(teachBookId,":",chapterId), questionIds);
			}

			// 获取当前章节的子节点
			List<ChapterSection> childChapterSections = knowledgeResource.getChildChapterSectionItemById(chapterSectionId);
			if (childChapterSections != null && childChapterSections.size() > 0) {
				Set<Long> needDealChildChapter = childChapterSections.stream().map(ChapterSection::getId).collect(Collectors.toSet());

				// 计算questionIds的归属 start
				HashMap<Long, List<Long>> chapterQuestionMap = new HashMap<Long, List<Long>>();
				HashMap<Long, List<Long>> userCpQuestionMap = new HashMap<Long, List<Long>>();

				// 取出每个子章节下的题目集合,以及每个子章节下用户做错题目的集合
				for (Long childChapterId : needDealChildChapter) {
					// 获取该子章节下的题目集合
					String childChapterQidStr =  knowledgeResource.getTiku5QuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId,
							NewRedisConsts.getTeachbookQuestionboxChapterFiled(childChapterId));
					String shuYeChapterQidStr = knowledgeResource.getShuYeQuestionListStrByBoxAndTeachBookAndField(boxId, teachBookId,
							NewRedisConsts.getShuYeChapterItemQuestionFiled(childChapterId));
					if (StringUtils.isNotBlank(childChapterQidStr)) {

						List<Long> childChapterQids = GsonUtil.getGson().fromJson(childChapterQidStr, type);
						if (StringUtils.isNotBlank(shuYeChapterQidStr)){
							List<Long> shuYeChapterQids = GsonUtil.getGson().fromJson(shuYeChapterQidStr, type);
							childChapterQids = (List<Long>)CollectionUtils.union(childChapterQids, shuYeChapterQids);
						}
						chapterQuestionMap.put(childChapterId, childChapterQids);
					}
					//初始化子章节map
					userCpQuestionMap.put(childChapterId, new ArrayList<Long>());
				}

				// 循环判断每个题目
				Iterator chapterQuestionIterator = chapterQuestionMap.entrySet().iterator();
				while (chapterQuestionIterator.hasNext()) {
					Entry<Long, List<Long>> questionEntry = (Entry)chapterQuestionIterator.next();
					Long chapterId = Long.valueOf(questionEntry.getKey().toString());
					List<Long> qidList = questionEntry.getValue();

					Collection<Long> intersectionIds = CollectionUtils.intersection(qidList, questionIds);
					if (CollectionUtils.isNotEmpty(intersectionIds)) {
						userCpQuestionMap.get(chapterId).addAll(intersectionIds);
					}
				}
				// 计算questionIds的归属 end

				// 将更新好的子节点的作答数据更新到qIdRedisMap
				Iterator userCpQuestionIterator = userCpQuestionMap.entrySet().iterator();
				while (userCpQuestionIterator.hasNext()) {
					Entry<Long, List<Long>> userCpQuestionEntry = (Entry<Long, List<Long>>)userCpQuestionIterator.next();
					Long chapterId = Long.valueOf(userCpQuestionEntry.getKey().toString());
					List<Long> qidList = userCpQuestionEntry.getValue();

					result.put(StringUtils.join(teachBookId,":",chapterId), qidList);
				}
			}
		} catch (Exception e) {
			logger.error("dealChapterDataNew",e);
		}
		return result;
	}

	private Map<String, List<Long>> dealPaperWrongQuestion4Chapter(Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds)throws DataAccessException{
		return dealPaperWrongQuestion4Chapter(uid, knowledgeId, teachBookId, boxId, questionIds, false);
	}
	private Map<String, List<Long>> dealPaperWrongQuestion4Chapter(Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds, boolean isSync)throws DataAccessException{
		return dealPaperQuestion4ChapterByType(uid, knowledgeId, teachBookId, boxId, questionIds, isSync, Consts.QuestionLogDealType.wrong);
	}
	/**
	 * 处理用户错题-章节存储
	 * */
	private Map<String, List<Long>> dealPaperQuestion4ChapterByType(
			Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds, boolean isSync, int dealType)throws DataAccessException{
		//根据知识点id与教材id，查询该教材下，包含该知识点的章节id
		List<ChapterSection> chapterSections = knowledgeResource.getChapterSectionItemByKnowledgeIdAndBookId(teachBookId, knowledgeId);
		String keyPrefix = "";
		if (dealType == Consts.QuestionLogDealType.done) {
			keyPrefix = NewRedisConsts.User_QuestionBox_Done_chapter;
		} else {
			keyPrefix = NewRedisConsts.User_QuestionBox_Wrong_chapter;
		}
		Map<String, List<Long>> map = Maps.newHashMap();
		if(CollectionUtils.isNotEmpty(chapterSections)) {
			for (ChapterSection chapterSection : chapterSections) {
				Map<String, List<Long>> chapterData = dealChapterDataNew(chapterSection, teachBookId, boxId, questionIds);
				if (chapterData != null && !chapterData.isEmpty()) {
					for (Entry<String, List<Long>> entry : chapterData.entrySet()) {
						if (CollectionUtils.isEmpty(entry.getValue())) {
							continue;
						}
						//替换key
						String key = keyPrefix + entry.getKey();
						List<Long> list = map.get(key);
						if (list == null) {
							list = Lists.newArrayList();
						}
						list.addAll(entry.getValue());
						map.put(key, list.stream().distinct().collect(Collectors.toList()));
					}
				}
			}
		}
		//前边只是获取变量, 如果不是同步操作, 需要获取全部数据合并进来
		if (!isSync) {
			Map<String, String> allMap = null;
			if (dealType == Consts.QuestionLogDealType.done) {
				checkAndReloadDoneQuestions(uid, boxId);
				allMap = compatableRedisClusterClient.hgetAll(NewRedisConsts.getUserQuestionboxDone(uid, boxId));
			} else {
				checkAndReloadWrongQuestions(uid, boxId);
				allMap = compatableRedisClusterClient.hgetAll(NewRedisConsts.getUserQuestionboxWrong(uid, boxId));
			}
			if (allMap != null && !allMap.isEmpty()) {
				Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {
				}.getType();
				for (Entry<String, List<Long>> entry : map.entrySet()) {
					Set<Long> questionIdSet = Sets.newHashSet();
					String key = entry.getKey();
					List<Long> value = entry.getValue();
					String existValue = allMap.get(key);
					if (StringUtils.isNotBlank(existValue)) {
						List<Long> questionIdList = GsonUtil.getGson().fromJson(existValue, type);
						questionIdSet.addAll(questionIdList);
					}
					questionIdSet.addAll(value);
					map.put(key, Lists.newArrayList(questionIdSet));
				}
			}
		}
		return map;
	}

	/**
	 * 处理用户错题-知识点存储
	 * */
	private Map<String, String> dealPaperWrongQuestion4Knowledge(Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds)throws DataAccessException {
		return dealPaperWrongQuestion4Knowledge(uid, knowledgeId, teachBookId, boxId, questionIds, false);
	}
	private Map<String, String> dealPaperWrongQuestion4Knowledge(Long uid, Long knowledgeId, Long teachBookId, Long boxId, List<Long> questionIds, boolean isSync)throws DataAccessException{
		java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {}.getType();

		HashMap<String, String> qIdRedisMap = new HashMap<String, String>();
		//取出用户该知识点的错题
		String redisQIds =  "";
		if (!isSync) {
			redisQIds = getUserWrongQuestions(uid, boxId, NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, knowledgeId));
		}
		if (StringUtils.isBlank(redisQIds)) {
			qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, knowledgeId), GsonUtil.toJson(questionIds));
		}else {
			List<Long> redisQIdList = GsonUtil.getGson().fromJson(redisQIds, type);
			HashSet<Long> redisQIdSet = new HashSet<Long>(redisQIdList);
			redisQIdSet.addAll(questionIds);

			qIdRedisMap.put(NewRedisConsts.getUserQuestionboxWrongKnowledge(teachBookId, knowledgeId), GsonUtil.toJson(redisQIdSet));
		}
		return qIdRedisMap;
	}


}
