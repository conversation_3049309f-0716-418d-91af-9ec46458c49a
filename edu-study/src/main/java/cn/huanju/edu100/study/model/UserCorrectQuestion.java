package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;
import lombok.Data;

/**
 * 用户纠正表
 */
@Data
public class UserCorrectQuestion extends DataEntity<UserCorrectQuestion> {

	private static final long serialVersionUID = 1L;
	private Long uid;		// 用户id
	private Long productId;		// 产品id
	private Long categoryId;		// 科目id
	private Long goodsId;		// 商品id
	private Long lessonId;		// 讲节id
	private Long questionId;		// 题目id
	private Long topicId;		// 子题id
	private Integer qtype;		// 题目类型 0：单选 1：多选(多个答案，全部选中得分)  2不定向选择(不少于1个答案，按选中的比例得分) 3、主观题
	private Integer productType;		// 产品类型
	private Long answerId;		// 作答id 可能是user_answer表id或者user_answer_homework表id
	private Long lastAnswerId;

	private Integer errorTime;

	public UserCorrectQuestion() {
		super();
	}

	public UserCorrectQuestion(Long id){
		super(id);
	}

}