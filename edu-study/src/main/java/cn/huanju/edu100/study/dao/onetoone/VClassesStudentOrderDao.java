/**
 *
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.QueryParam;
import cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 学员班级订单明细DAO接口
 * <AUTHOR>
 * @version 2016-09-01
 */
public interface VClassesStudentOrderDao extends CrudDao<VClassesStudentOrder> {

    List<VClassesStudentOrder> findListByQueryParam(QueryParam param, Integer unFinish) throws DataAccessException;

    List<CountModel> listStudentCountByClsIds(List<Long> clsIds) throws DataAccessException;
}
