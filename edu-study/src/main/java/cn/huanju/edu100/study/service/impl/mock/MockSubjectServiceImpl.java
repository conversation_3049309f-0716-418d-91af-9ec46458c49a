package cn.huanju.edu100.study.service.impl.mock;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.mock.MockSubjectDao;
import cn.huanju.edu100.study.model.mock.MockSubject;
import cn.huanju.edu100.study.service.mock.MockSubjectService;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 模考科目Service
 * <AUTHOR>
 * @version 2018-04-08
 */
@Service
public class MockSubjectServiceImpl extends BaseServiceImpl<MockSubjectDao, MockSubject> implements MockSubjectService {
    private static Logger logger = LoggerFactory.getLogger(MockSubjectServiceImpl.class);
    private static Gson gson = GsonUtil.getGson();

    @Autowired
    private MockSubjectDao mockSubjectDao;

    @Override
    public List<MockSubject> qryByMockExamId(Long mockExamId) throws DataAccessException {
        return mockSubjectDao.qryByMockExamId(mockExamId);
    }

    @Override
    public List<MockSubject> findMockSubjectListByMockId(Long mockExamId) throws DataAccessException {
        return mockSubjectDao.findMockSubjectListByMockId(mockExamId);
    }
}
