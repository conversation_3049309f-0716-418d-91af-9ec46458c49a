package cn.huanju.edu100.study.service.impl.qbox;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.qbox.UserWipeOutWrongQuestionDao;
import cn.huanju.edu100.study.model.questionBox.UserWipeOutWrongQuestion;
import cn.huanju.edu100.study.service.qbox.UserWipeOutWrongQuestionService;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.IdWorker;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 18/10/30
 */
@Service
public class UserWipeOutWrongQuestionServiceImpl extends BaseServiceImpl<UserWipeOutWrongQuestionDao, UserWipeOutWrongQuestion> implements UserWipeOutWrongQuestionService {

    final static IdWorker idworker=new IdWorker(0,6);
    @Override
    public Long save(UserWipeOutWrongQuestion userWipeOutWrongQuestion) throws DataAccessException {
        if (userWipeOutWrongQuestion.getUid() == null || userWipeOutWrongQuestion.getQuestionBoxId() == null ||
                StringUtils.isBlank(userWipeOutWrongQuestion.getKey())) {
            throw  new DataAccessException("UserWipeOutWrongQuestion save error, property uid or questionBoxId or key is empty, uid:"
                    + userWipeOutWrongQuestion.getUid()+",questionBoxId:"+ userWipeOutWrongQuestion.getQuestionBoxId()
                    +",key:"+ userWipeOutWrongQuestion.getKey());
        }
        UserWipeOutWrongQuestion exist = dao.getFromMasterDbByUidQboxIdAndKey(userWipeOutWrongQuestion.getUid(), userWipeOutWrongQuestion.getQuestionBoxId(), userWipeOutWrongQuestion.getKey());
        if (exist == null) {
            //CommonSelfIdGenerator idGenerator = new CommonSelfIdGenerator();
            userWipeOutWrongQuestion.setId(idworker.nextId());
            dao.insert(userWipeOutWrongQuestion);
        } else {
            userWipeOutWrongQuestion.setId(exist.getId());
            dao.update(userWipeOutWrongQuestion);
        }
        return userWipeOutWrongQuestion.getId();
    }

    @Override
    public UserWipeOutWrongQuestion getByUidQboxIdAndKey(Long uid, Long qboxId, String key) throws DataAccessException {
        return dao.getByUidQboxIdAndKey(uid, qboxId, key);
    }

    @Override
    public List<UserWipeOutWrongQuestion> getByUidQboxIdAndKeyLike(Long uid, Long qboxId, String key) throws DataAccessException {
        return dao.getByUidQboxIdAndKeyLike(uid,qboxId,key);
    }

    @Override
    public List<UserWipeOutWrongQuestion> getByUidQboxIdListAndKeyLike(Long uid, List<Long> qboxIds, String key) throws DataAccessException {
        return dao.getByUidQboxIdListAndKeyLike(uid,qboxIds,key);
    }

    @Override
    public List<UserWipeOutWrongQuestion> getByUidAndQboxId(Long uid, Long qboxId) throws DataAccessException {
        return dao.getByUidAndQboxId(uid, qboxId);
    }

    @Override
    public List<UserWipeOutWrongQuestion> getByUidAndBoxIds(Long uid, List<Long> boxIds) throws DataAccessException {
        return dao.getByUidAndBoxIds(uid, boxIds);
    }

    @Override
    public List<String> getKeysByUidAndQboxId(Long uid, Long qboxId) throws DataAccessException {
        return dao.getKeysByUidAndQboxId(uid, qboxId);
    }

    @Override
    public void batchSave(Long uid, Long questionBoxId, List<UserWipeOutWrongQuestion> userWipeOutWrongQuestionList) throws DataAccessException {
        if (uid == null || questionBoxId == null) {
            throw  new DataAccessException("UserWipeOutWrongQuestion save error, property uid or qboxIdis empty, uid:"
                    +uid+",questionBoxId:"+questionBoxId);
        }
        logger.info("UserWipeOutWrongQuestion batchSave for param, uid:{}, questionBoxId:{},UserWipeOutWrongQuestionList:{}", uid, questionBoxId, GsonUtil.toJson(userWipeOutWrongQuestionList));
        //移除不一致的
        Iterator<UserWipeOutWrongQuestion> iterator = userWipeOutWrongQuestionList.iterator();
        while (iterator.hasNext()) {
            UserWipeOutWrongQuestion userWipeOutWrongQuestion = iterator.next();
            if (!uid.equals(userWipeOutWrongQuestion.getUid()) || !questionBoxId.equals(userWipeOutWrongQuestion.getQuestionBoxId()) ) {
                iterator.remove();
            } else {
                userWipeOutWrongQuestion.setId(idworker.nextId());
            }
        }
        List<UserWipeOutWrongQuestion> list = dao.getByUidAndQboxId(uid,questionBoxId);
        Map<String,UserWipeOutWrongQuestion> keyMap = Maps.newHashMap();
        for (UserWipeOutWrongQuestion userWipeOutWrongQuestion : list) {
            keyMap.put(userWipeOutWrongQuestion.getKey(),userWipeOutWrongQuestion);
        }
        List<UserWipeOutWrongQuestion> insertList = Lists.newArrayList();
        for (UserWipeOutWrongQuestion userWipeOutWrongQuestion: userWipeOutWrongQuestionList) {
            String key = userWipeOutWrongQuestion.getKey();
            UserWipeOutWrongQuestion exist = keyMap.get(key);
            if (null != exist) {
                userWipeOutWrongQuestion.setId(exist.getId());
                insertList.add(userWipeOutWrongQuestion);
            } else {
                if (StringUtils.isNotBlank(userWipeOutWrongQuestion.getQuestionIdList()) && !"[]".equals(userWipeOutWrongQuestion.getQuestionIdList())) {
                    insertList.add(userWipeOutWrongQuestion);
                }
            }

        }
        logger.info("UserWipeOutWrongQuestion insertBatchNew for UserWipeOutWrongQuestionList:{}", GsonUtil.toJson(userWipeOutWrongQuestionList));
        if (!CollectionUtils.isEmpty(insertList)) {
            dao.insertBatchNew(insertList);
        }
        return;
    }
}
