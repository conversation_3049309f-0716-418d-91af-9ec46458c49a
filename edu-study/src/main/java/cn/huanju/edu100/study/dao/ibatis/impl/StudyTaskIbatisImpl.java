/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.StudyTaskDao;
import cn.huanju.edu100.study.model.StudentTask;
import cn.huanju.edu100.study.model.StudyTask;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 学习任务DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudyTaskIbatisImpl extends CrudIbatisImpl2<StudyTask> implements StudyTaskDao {

    public StudyTaskIbatisImpl() {
        super("StudyTask");
    }

    @Override
    public Collection<StudyTask> qryStudyTasksByPid(Long pid) throws DataAccessException {
        if (pid == null) {
            logger.error("list {} error, parameter pid is null,gid:{}", namespace, pid);
            throw new DataAccessException("list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("pid", pid);
            return (Collection<StudyTask>) sqlMap.queryForList("StudyTask.qryStudyTasksByPid", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.gid:{}", namespace, pid, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public Collection<StudentTask> qryStudentTasksByTidsAndUid(String tids, Long uid) throws DataAccessException {
        if (tids == null || uid == null) {
            logger.error("list {} error, parameter tids or uid is null,tids:{}", namespace, tids);
            throw new DataAccessException("list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("tids", tids);
            param.put("uid", uid);
            return (Collection<StudentTask>) sqlMap.queryForList("StudyTask.qryStudentTasksByTidsAndUid", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.tids:{}", namespace, tids, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

}
