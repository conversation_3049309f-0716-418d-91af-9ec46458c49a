package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorGroupDao;
import cn.huanju.edu100.study.dao.tutor.TutorGroupStudentDao;
import cn.huanju.edu100.study.model.tutor.TutorGroup;
import cn.huanju.edu100.study.model.tutor.TutorGroupStudent;
import cn.huanju.edu100.study.model.tutor.TutorTaskDto;
import cn.huanju.edu100.study.service.tutor.TutorGroupService;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学员分组Service
 * <AUTHOR>
 * @version 2016-01-14
 */
@Service
public class TutorGroupServiceImpl extends BaseServiceImpl<TutorGroupDao, TutorGroup> implements TutorGroupService {

    @Autowired
    private TutorGroupStudentDao tutorGroupStudentDao;

    @Override
    public List<TutorGroup> getCategoryListByUid(TutorTaskDto params) throws DataAccessException {
        if (params == null || params.getUid() == null
                || params.getClasses() == null || params.getSecondCategory() == null) {
            return null;
        }

        TutorGroup tutorGroup = new TutorGroup();
        tutorGroup.setSecondCategory(params.getSecondCategory());
        tutorGroup.setClasses(params.getClasses());
        if (null != params.getCategoryId()) {
            tutorGroup.setCategoryId(params.getCategoryId());
        }
        List<TutorGroup> tutorGroupList = dao.findList(tutorGroup);
        if (CollectionUtils.isEmpty(tutorGroupList)) {
            return null;
        }

        List<Long> groupIdList = new ArrayList<Long>();
        for (TutorGroup tGroup : tutorGroupList) {
            groupIdList.add(tGroup.getId());
        }

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("uid", params.getUid());
        paramMap.put("groupIdList", groupIdList);
        List<TutorGroupStudent> tutorGroupStudents = tutorGroupStudentDao.getTutorGroupStudent(paramMap);
        if (CollectionUtils.isEmpty(tutorGroupStudents)) {
            return null;
        }

        List<Long> containList = new ArrayList<Long>();
        for (TutorGroupStudent tutorGroupStudent : tutorGroupStudents) {
            containList.add(tutorGroupStudent.getGroupId());
        }
        List<TutorGroup> tutorGroups = new ArrayList<TutorGroup>();
        for (TutorGroup tuGroup : tutorGroupList) {
            if (containList.contains(tuGroup.getId())) {
                tutorGroups.add(tuGroup);
            }
        }

        return tutorGroups;
    }

    @Override
    public TutorGroup findByClassesAndCategoryId
            (TutorGroup params) throws DataAccessException {
        if (params == null || StringUtils.isBlank(params.getClasses()) || params.getCategoryId() == null) {
            logger.info("findByClassesAndCategoryId params is empty,params:{}",GsonUtil.toJson(params));
            return null;
        }

        TutorGroup tutorGroup = dao.findByClassesAndCategory(params);
        return tutorGroup;
    }
}
