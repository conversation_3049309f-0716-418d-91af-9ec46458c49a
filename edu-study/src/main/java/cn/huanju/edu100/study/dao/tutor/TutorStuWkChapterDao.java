/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.study.model.tutor.TutorStuWkChapter;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 学生对微课班章节任务完成情况DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public interface TutorStuWkChapterDao extends CrudDao<TutorStuWkChapter> {

    List<CountModel> listStuKnowNum(Long uid, List<Long> sectionIdList) throws DataAccessException;

    List<CountModel> listStuTaskStatus(Long uid, Integer type, List<Long> sectionIdList) throws DataAccessException;

    boolean updateStatus(long result, Long uid, Integer status) throws DataAccessException;

    TutorSectionTask getWkLastTask(Long uid, Long weikeId, Long sectionId, Long phaseId, Long unitId, Long secondCategory,
                                   Long categoryId, Integer type) throws DataAccessException;

    Map<Long, Integer> getUnitTaskNum(List<Long> unitIdList, Long uid, Integer type) throws DataAccessException;
}