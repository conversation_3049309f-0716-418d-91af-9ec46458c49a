package cn.huanju.edu100.study.service.wxapp;

import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.client.FissionActivityService;
import cn.huanju.edu100.study.repository.wxapp.FissionActivityRepository;
import cn.huanju.edu100.study.repository.wxapp.FissionActivitySettingRepository;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.util.CacheUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hqwx.study.dto.Page;
import com.hqwx.study.entity.wxapp.FissionActivity;
import com.hqwx.study.entity.wxapp.FissionActivityQuery;
import com.hqwx.study.entity.wxapp.FissionActivitySetting;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/20 13:39
 * @description
 */
@Service
public class FissionActivityServiceImpl implements FissionActivityService {
    @Autowired
    private FissionActivityRepository fissionActivityRepository;

    @Autowired
    private FissionActivitySettingRepository fissionActivitySettingRepository;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    private Set<String> getPushedActivityUniqueIds()
    {
        List<FissionActivitySetting> allActivitySettings = getAllActivitySettings(FissionActivitySetting.Type.PUSH.getValue());
        if(CollectionUtils.isNotEmpty(allActivitySettings)) {
            return allActivitySettings.stream().filter(setting -> {
                        return setting.parseSetting(FissionActivitySetting.PushSetting.class).getEnable();
                    }).map(setting -> FissionActivity.activityUniqueId(setting.getActivityType(), setting.getActivityId()))
                    .collect(Collectors.toSet());
        }
        return null;
    }

    @Override
    public Page<FissionActivity> getActivityList(FissionActivityQuery query) {
        FissionActivityRepository.AdditionQueryParam additionQueryParam = new FissionActivityRepository.AdditionQueryParam();
        if(query.getIsPushed() != null) {
            if(query.getIsPushed()) {
                additionQueryParam.setIncludePushedActivityUniqueIds(getPushedActivityUniqueIds());
            } else {
                additionQueryParam.setExcludePushedActivityUniqueIds(getPushedActivityUniqueIds());
            }
        }
        var page = fissionActivityRepository.findActivityPage(query, additionQueryParam);
        return new Page<>(page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
    }

    @Override
    public FissionActivity getPushedActivity(@NonNull Long secondCategoryId) {
        return CacheUtil.getEntity(compatableRedisClusterClient, "getPushedActivity." + secondCategoryId, FissionActivity.class, () -> {
            FissionActivityRepository.AdditionQueryParam additionQueryParam = new FissionActivityRepository.AdditionQueryParam();
            additionQueryParam.setIncludePushedActivityUniqueIds(getPushedActivityUniqueIds());
            // 获取配置了推荐首页的活动
            FissionActivityQuery query = new FissionActivityQuery();
            query.setSecondCategoryId(secondCategoryId);
            query.setDescOrderFieldNames(List.of("weight", "updateTime"));
            final IPage<FissionActivity> page = fissionActivityRepository.findActivityPage(query, additionQueryParam);
            if (!page.getRecords().isEmpty()) {
                return page.getRecords().get(0);
            }
            // 如果没有，获取第一个拼团活动
            return page.getRecords().stream()
                    .filter(activity -> FissionActivity.ActivityType.GROUP_BUY.typeEquals(activity.getActivityType()))
                    .findFirst().orElse(null);
        }, 30 * 60 /*30分钟*/);
    }

    @Override
    public FissionActivitySetting getActivitySetting(@NonNull Long activityId, @NonNull Integer activityType, @NonNull Integer settingType) {
        return fissionActivitySettingRepository.findActivitySetting(activityId, activityType, settingType);
    }

    @Override
    public List<FissionActivitySetting> getActivityAllSettings(@NonNull Long activityId, @NonNull Long activityType) {
        return fissionActivitySettingRepository.findActivitySettingList(activityId, activityType);
    }

    @Override
    public List<FissionActivitySetting> getAllActivitySettings(@NonNull Integer settingType) {
        return fissionActivitySettingRepository.findActivitySettingList(settingType);
    }

    @Override
    public Boolean setActivitySetting(@NotNull FissionActivitySetting setting) {
        FissionActivityQuery query = new FissionActivityQuery();
        query.setId(setting.getActivityId()).setActivityType(setting.getActivityType());
        FissionActivity activity = fissionActivityRepository.findActivityPage(query, null).getRecords().get(0);
        if(!activity.getIsSupportPushSetting()) {
            throw new IllegalArgumentException("activity not support push setting");
        }
        if(setting.getId() == null) {
            FissionActivitySetting org = fissionActivitySettingRepository.findActivitySetting(
                    setting.getActivityId(), setting.getActivityType(), setting.getType());
            if(org != null) {
                setting.setId(org.getId());
            }
        }
        Boolean ret = false;
        if(setting.getId() == null) {
            ret = fissionActivitySettingRepository.insertActivitySetting(setting);
        } else {
            ret = fissionActivitySettingRepository.updateActivitySetting(setting);
        }
        if(ret) {
            invalidCache(setting);
        }
        return ret;
    }

    @Override
    public Integer getActivityJoinNum(@NonNull Long activityId, @NonNull Integer activityType) {
        return CacheUtil.getEntity(compatableRedisClusterClient, "getActivityJoinNum." + activityId + "." + activityType,
                Integer.class, () -> {
            return fissionActivityRepository.getActivityJoinNum(activityId, activityType);
        }, 10 * 60 /*10分钟*/);
    }

    private void invalidCache(FissionActivitySetting setting) {
        compatableRedisClusterClient.del(
                "findActivitySetting" +  setting.getActivityId() + "_" + setting.getActivityType() + "_" + setting.getType()
        );
    }
}
