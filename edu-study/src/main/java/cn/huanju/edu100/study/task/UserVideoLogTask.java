package cn.huanju.edu100.study.task;

import cn.huanju.edu100.redis.IRedisLockHandler;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.redis.cluster.lock.CompatableRedisClusterLockHandler;
import cn.huanju.edu100.study.model.UserVideoLog;
import cn.huanju.edu100.study.service.UserVideoLogService;
import cn.huanju.edu100.study.util.Constants;
import cn.huanju.edu100.study.util.NoneDevConditional;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Conditional(NoneDevConditional.class) // 非开发环境才注入该类
@Component
public class UserVideoLogTask {
    Logger logger = LoggerFactory.getLogger(UserVideoLogTask.class);

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    @Autowired
    private UserVideoLogService userVideoLogService;

    @Autowired
    private JedisCluster localJedisCluster;

    private static int OVER_MINUTE = 2;  // 2分钟往数据库写一次

    private static int OVER_MINUTE2 = 13;

    private int EXPIRE_TIME = 60 * 60 * 1;		// 1小时


    @Scheduled(cron = "0/40 * * * * ?")
    public void run() {
        try {
            logger.info("UserVideoLogTask start.");
            //多线程竞争更新总表的分布式锁
            IRedisLockHandler lock = new CompatableRedisClusterLockHandler(localJedisCluster);//直接读单一的主redis
            if(lock.tryLock(RedisConsts.getUserVideoLongKey(),15, TimeUnit.SECONDS)){//得到锁，则执行更新总表操作，获取锁的超时时间15s
                logger.info("UserVideoLogTask enter.");
                List<UserVideoLog> lst = getOverDataFromRedis(OVER_MINUTE, OVER_MINUTE2);
                logger.info("UserVideoLogTask enter.size:{}", lst.size());
                for (UserVideoLog log : lst){
                    userVideoLogService.addUserVideoLog(log);
                    log.setDbUpdateTime(new Date());
                    saveDbUpdateTime(log.getUserLogUidFieldKey(), log.getDbUpdateTime().getTime());
                }
            }
            lock.unLock(RedisConsts.getUserVideoLongKey());//解锁
            logger.info("UserVideoLogTask end.");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getDbUpdateKey(final String field){
        return "v_log_db_t_" + field;
    }
    private Long getDbUpdateTime(final String field){
        String str = compatableRedisClusterClient.get(getDbUpdateKey(field));
        if(StringUtils.isNotBlank(str)){
            return Long.valueOf(str);
        }
        return null;
    }

    private Long getDataUpdateTime(final String field){
        String str = compatableRedisClusterClient.get("data_update_" + field);
        if(StringUtils.isNotBlank(str)){
            return Long.valueOf(str);
        }
        return null;
    }


    private void saveDbUpdateTime(final String field, final Long dbTime) throws DataAccessException {
        if(StringUtils.isNotBlank(field) && null != dbTime) {
            String strKey = getDbUpdateKey(field);
            compatableRedisClusterClient.setex(strKey, EXPIRE_TIME, String.valueOf(dbTime));
        }
    }


    /**
     * 获取缓存中多少秒都没有更新的数据
     * 如果一个用户的所有数据都没有更新了
     * 则从缓存中清除uid，下一次就不再更新数据库
     * @return
     */
    private List<UserVideoLog> getOverDataFromRedis(final int minute, final  int overMinute){
        List<UserVideoLog> lst = new ArrayList<UserVideoLog>();
        UserVideoLog log = null;
        try {
            Set<String> keyRedis = compatableRedisClusterClient.smembers(Constants.USER_VIDEO_LOG_KeyHqUserLog);
            Long dbSaveTime = null;
            Long dataUpdateTime = null;
            String strLength = null;
            String srcField = null;
            if (keyRedis != null) {
                for (String field : keyRedis) {
//                    logger.info("enter: field:{}", field);
                    srcField = field;
                    field = field.substring("hq_uf_".length());
                    String[] data = field.split("_");
                    log = new UserVideoLog();
                    log.setUid(Long.parseLong(data[0]));
                    if(!"-1".equals(data[1])) {
                        log.setCourseId(Long.parseLong(data[1]));
                    }
                    if(!"-1".equals(data[2])) {
                        log.setClsId(Long.parseLong(data[2]));
                    }
                    if(!"-1".equals(data[3])) {
                        log.setLessonId(Long.parseLong(data[3]));
                    }
                    dbSaveTime = getDbUpdateTime(field);
                    dataUpdateTime = getDataUpdateTime(field);
                    if(null == dbSaveTime){
//                        logger.info("{},dbSave time null", srcField);
                        strLength = compatableRedisClusterClient.hget(log.getUserLogSetKey(), field.substring(field.indexOf("_") + 1));
                        if(StringUtils.isNotBlank(strLength)){
                            log.setLength(Long.valueOf(strLength));
                            lst.add(log);
                        }else{
                            compatableRedisClusterClient.srem(Constants.USER_VIDEO_LOG_KeyHqUserLog, srcField); // 要查明为什么为空
                        }

                    }else{
                        if(null == dataUpdateTime || (null != dataUpdateTime && dbSaveTime - dataUpdateTime > overMinute * 60 * 1000)){
                            compatableRedisClusterClient.srem(Constants.USER_VIDEO_LOG_KeyHqUserLog, srcField);    //超时需要更新到数据库了，这个就删掉
//                            logger.info("{},clear in cache for update",srcField);
                        }else {
                            if (new Date().getTime() - dbSaveTime > minute * 60 * 1000) {
//                                logger.info("{},need to save in db", srcField);
                                strLength = compatableRedisClusterClient.hget(log.getUserLogSetKey(), field.substring(field.indexOf("_") + 1));
                                if(StringUtils.isNotBlank(strLength)){
                                    log.setLength(Long.valueOf(strLength));
                                    lst.add(log);
                                }
                            }
                        }
                    }

                }
            }
        } catch (Exception ex) {
            logger.error("UserVideoLogTask fail");
        } finally {
        }
        return lst;
    }

    private List<UserVideoLog> getDataFromRedis(){
        List<UserVideoLog> lst = new ArrayList<UserVideoLog>();
        try {
            Set<String> keyRedis = compatableRedisClusterClient.smembers("HquserLogKey");
            compatableRedisClusterClient.del("HquserLogKey");
            if (keyRedis != null) {
                for (String field : keyRedis) {
                    String[] data = field.split("_");
                    UserVideoLog log = new UserVideoLog();
                    log.setUid(Long.parseLong(data[0]));
                    log.setCourseId(Long.parseLong(data[1]));
                    log.setClsId(Long.parseLong(data[2]));
                    log.setLessonId(Long.parseLong(data[3]));
                    log.setLength(Long.parseLong(compatableRedisClusterClient.hget("UserVideoLog_", field)));
                    lst.add(log);
                }
            }
        } catch (Exception ex) {
            logger.error("UserVideoLogTask fail");
        } finally {
        }
        return lst;
    }
}
