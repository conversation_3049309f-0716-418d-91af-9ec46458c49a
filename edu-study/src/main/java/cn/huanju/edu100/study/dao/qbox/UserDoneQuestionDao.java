package cn.huanju.edu100.study.dao.qbox;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.questionBox.UserDoneQuestion;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 18/10/29
 */
public interface UserDoneQuestionDao extends CrudDao<UserDoneQuestion> {

    UserDoneQuestion getFromMasterDbByUidQboxIdAndKey(Long uid, Long questionBoxId, String key) throws DataAccessException;

    UserDoneQuestion getByUidQboxIdAndKey(Long uid, Long questionBoxId, String key) throws DataAccessException;

    List<UserDoneQuestion> getByUidQboxId(Long uid, Long questionBoxId) throws DataAccessException;

    List<UserDoneQuestion> getByUidAndQboxId(Long uid, Long questionBoxId) throws DataAccessException;

    List<UserDoneQuestion> getByUidQboxIdAndKeyLike(Long uid, Long qboxId, String key) throws DataAccessException;

    List<UserDoneQuestion> getByUidQboxIdListAndKeyLike(Long uid, List<Long> boxIdList, String key) throws DataAccessException;

    List<String> getKeysByUidAndQboxId(Long uid, Long questionBoxId) throws DataAccessException;

    void deleteByUidAndQboxId(Long uid, Long questionBoxId) throws  DataAccessException;

    void deleteByUidAndQboxIdAndIdList(Long uid, Long questionBoxId, List<Long> idList) throws DataAccessException;

    void insertBatch(List<UserDoneQuestion> userDoneQuestionList) throws DataAccessException;

    void insertBatchNew(List<UserDoneQuestion> userDoneQuestionList) throws DataAccessException;

    List<UserDoneQuestion> getByUidAndBoxIds(Long uid, List<Long> boxIds) throws DataAccessException;

    List<UserDoneQuestion> getAllBoxIdByUid(Long uid) throws DataAccessException;
}
