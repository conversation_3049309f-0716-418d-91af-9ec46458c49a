package cn.huanju.edu100.study.model.adminstudy;


import cn.huanju.edu100.persistence.model.DataEntity;
import com.hqwx.study.entity.UserAnswer;

import java.util.Date;

/**
 * 学员学习情况-作业完成情况
 */
public class HomeworkCompletion extends DataEntity<UserAnswer> {

    private Long maxId;//homework answer id
    private Long objId;//课件ID
    private Integer questionNum;//课件关联题目数量
    private Long completionNum;//作业完成次数
    private Date lastAnswerTime;//最后一次作答时间
    private Long uid;//用户ID

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public Long getCompletionNum() {
        return completionNum;
    }

    public void setCompletionNum(Long completionNum) {
        this.completionNum = completionNum;
    }

    public Date getLastAnswerTime() {
        return lastAnswerTime;
    }

    public void setLastAnswerTime(Date lastAnswerTime) {
        this.lastAnswerTime = lastAnswerTime;
    }

    public Long getMaxId() {
        return maxId;
    }

    public void setMaxId(Long maxId) {
        this.maxId = maxId;
    }

    public Integer getQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(Integer questionNum) {
        this.questionNum = questionNum;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }
}
