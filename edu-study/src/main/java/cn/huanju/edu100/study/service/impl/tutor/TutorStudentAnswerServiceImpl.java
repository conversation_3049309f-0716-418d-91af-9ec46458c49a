package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorStudentAnswerDao;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.QuestionTopic;
import com.hqwx.study.entity.UserAnswerDetail;
import cn.huanju.edu100.study.model.UserErrorAnswer;
import cn.huanju.edu100.study.model.tutor.TutorStudentAnswer;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.impl.answer.*;
import cn.huanju.edu100.study.service.tutor.TutorStudentAnswerService;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * 做题记录Service
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
@Service
public class TutorStudentAnswerServiceImpl extends BaseServiceImpl<TutorStudentAnswerDao, TutorStudentAnswer> implements
        TutorStudentAnswerService {

    @Autowired
    private KnowledgeResource knowledgeResource;

    /**
     * 错题作答service, 不保存做题记录
     */
    @Override
    public HashMap<Long, Object> submitErrorQuestion(List<UserErrorAnswer> userErrorAnswerList)
            throws DataAccessException {
        HashMap<Long, Object> ret = new HashMap<Long, Object>();

        // 提炼questionId
        List<Long> questionIds = new ArrayList<Long>();
        for (UserErrorAnswer userErrorAnswer : userErrorAnswerList) {
            questionIds.add(userErrorAnswer.getQuestionId());
        }

        // 获得questionList
        List<Question> questionList = knowledgeResource.getQuestionByIds(questionIds);

        if (questionList == null || questionList.size() <= 0) {
            throw new DataAccessException("" + Constants.SYS_ERROR, "getQuestionByIds return null ,param:"
                    + GsonUtil.toJson(questionList));
        }

        // 提炼topicMap
        HashMap<Long, QuestionTopic> topicMap = new HashMap<Long, QuestionTopic>();

        for (Question question : questionList) {
            Collection<QuestionTopic> topics = question.getTopicList();
            if (CollectionUtils.isNotEmpty(topics)) {
                for (QuestionTopic topic : topics) {
                    topicMap.put(topic.getId(), topic);
                }
            }
        }
        List<UserErrorAnswer> rightAnswers = new ArrayList<UserErrorAnswer>();

        // 判断对错
        if (topicMap.size() > 0) {
            for (UserErrorAnswer userErrorAnswer : userErrorAnswerList) {
                QuestionTopic topic = topicMap.get(userErrorAnswer.getTopicId());

                UserAnswerDetail answerDetail = new UserAnswerDetail();
                answerDetail.setUid(userErrorAnswer.getUid());
                answerDetail.setQuestionId(userErrorAnswer.getQuestionId());
                answerDetail.setTopicId(userErrorAnswer.getTopicId());
                answerDetail.setAnswer(userErrorAnswer.getAnswer());

                if (topic != null) {
                    AbstractAnswerJudger answerJudger = getAnswerJudger(topic.getQtype());
                    int isRight = answerJudger.judge(topic, userErrorAnswer.getAnswer());
                    answerDetail.setIsRight(isRight);

                    if (isRight == UserAnswerDetail.IsRight.RIGHT) {
                        if (userErrorAnswer.getUserErrorId() != null && userErrorAnswer.getUserErrorId() != 0L) {
                            rightAnswers.add(userErrorAnswer);
                        }
                    }
                    ret.put(userErrorAnswer.getTopicId(), answerDetail);
                }
            }
        }

        return ret;
    }

    private static AbstractAnswerJudger[] judgers = new AbstractAnswerJudger[] { new SingleChoiceAnswerJudger(),// 0单选
            new MultiChoiceAnswerJudger(),// 1多选
            new UncertainChoiceAnswerJudger(),// 2不定项选择
            new DetermineAnswerJudger(),// 3判断题
            new SubjectiveAnswerJudger(),// 4填空题
            new SubjectiveAnswerJudger(),// 5简答题
    };

    private AbstractAnswerJudger getAnswerJudger(Integer questionType) {
        if (questionType == null || questionType >= 5) {
            questionType = 5;
        }
        return judgers[questionType];
    }

    @Override
    public List<TutorStudentAnswer> getAnswerNumberByUidAndTaskIdList(Long uid, List<Long> taskIdList) throws DataAccessException {
        return dao.getAnswerNumberByUidAndTaskIdList(uid, taskIdList);
    }

    @Override
    public List<TutorStudentAnswer> findByUidAndTaskIdList(Long uid, List<Long> taskIdList) throws DataAccessException {
        return dao.findByUidAndTaskIdList(uid, taskIdList);
    }
}
