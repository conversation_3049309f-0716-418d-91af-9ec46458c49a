package cn.huanju.edu100.study.model.tutor;

import io.swagger.annotations.ApiParam;
import lombok.Data;

@Data
public class ReplyCommentQuery {
    private Long uid;

    private  Long commentId;

    @ApiParam(value = "回复本页起始位置，从0开始", required = true)
    private  Integer replyFrom ;

    @ApiParam(value = "每页长度", required = true)
    private  Integer replyRows;

    @ApiParam(value = "查询方式1belongId，2replyId", required = true)
    private Integer queryType;
}
