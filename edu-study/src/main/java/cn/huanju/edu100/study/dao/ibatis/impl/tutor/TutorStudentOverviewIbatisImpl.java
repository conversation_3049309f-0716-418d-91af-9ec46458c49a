/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorStudentOverviewDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentOverview;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 学生学习报告DAO接口
 * <AUTHOR>
 * @version 2016-01-27
 */
public class TutorStudentOverviewIbatisImpl extends CrudIbatisImpl2<TutorStudentOverview> implements
		TutorStudentOverviewDao {

	public TutorStudentOverviewIbatisImpl() {
		super("TutorStudentOverview");
	}

    @Override
    public List<TutorStudentOverview> getTutorStudentOverviews(Map<String, Object> params) throws DataAccessException {
        if (params == null) {
            return null;
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorStudentOverview>) sqlMap
                    .queryForList("TutorStudentOverview.findList", params);
        } catch (SQLException e) {
            logger.error("getTutorStudentOverviews SQLException.", e);
            throw new DataAccessException("getTutorStudentOverviews SQLException error");
        }
    }

    @Override
    public void increaseCompleteCount(TutorStudentOverview tutorStudentOverview) throws DataAccessException {
        if (tutorStudentOverview == null) {
            return;
        }

        try {
            SqlMapClient sqlMap = super.getMaster();
            sqlMap.update("TutorStudentOverview.increaseCompleteCount", tutorStudentOverview);
        } catch (SQLException e) {
            logger.error("increaseCompleteCount SQLException.", e);
            throw new DataAccessException("increaseCompleteCount SQLException error");
        }
    }

}
