package cn.huanju.edu100.study.dao.solution;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.solution.QuestionAnswer;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Date;
import java.util.HashMap;
import java.util.List;


public interface QuestionAnswerDao extends CrudDao<QuestionAnswer> {
    List<QuestionAnswer> findUserAnswerListByQids(String qids) throws DataAccessException;
    List<QuestionAnswer> findUserAnswerListByQidListAndStartTime(List<Long> qidList, Date startTime) throws DataAccessException;
    boolean updateLikeNum(Long answerId, String likeNumStr) throws DataAccessException;
    Long countUnReadAnswer(HashMap<String, Long> paramMap) throws DataAccessException;

    List<QuestionAnswer> getQuestionAnswerByQidWriteDb(Long qid) throws DataAccessException;
}
