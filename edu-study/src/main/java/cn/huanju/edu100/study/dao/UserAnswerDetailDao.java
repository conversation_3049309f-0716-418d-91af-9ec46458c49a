/**
 * 
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import com.hqwx.study.entity.UserAnswerDetail;
import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.entity.UserAnswerDetailDto;

import java.util.List;

/**
 * 用户答案子题详情DAO接口
 * <AUTHOR>
 * @version 2015-05-12
 */
public interface UserAnswerDetailDao extends CrudDao<UserAnswerDetail> {

    List<UserAnswerDetail> findByPaperId(long uid, long paperId) throws DataAccessException;

	List<UserAnswerDetail> findByUserHomeworkId(Long uid,
			Long userHomeworkId)throws DataAccessException;

	List<UserAnswerDetail> findByUserHomeworkIdList(Long uid,
												List<Long> userHomeworkIds)throws DataAccessException;

	List<UserAnswerDetail> findLastQIdDetails(UserAnswerDetail param) throws DataAccessException;

	List<UserAnswerDetail> getUserAnswerDetailByQuestions(Long uid, List<Long> questionIdList) throws DataAccessException;

	List<Long> findStudyCenterLastHomeworkAnswerDetailList(Long uid, List<Long> answerSumIdList) throws DataAccessException;


	List<UserAnswerDetailDto> getUserAnswerDetailByQuestionAndAnswer(Long uid, List<Long> sumIdList , Long questionId) throws DataAccessException;

	List<UserAnswerDetail> getUserAnswerDetailBySumIdList(Long uid, List<Long> answerSumIdList) throws DataAccessException;
}