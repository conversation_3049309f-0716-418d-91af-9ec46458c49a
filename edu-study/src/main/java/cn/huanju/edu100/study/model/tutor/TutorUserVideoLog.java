package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.util.DateUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.util.Date;

/**
 * 学员观看记录Entity
 * <AUTHOR>
 * @version 2016-01-20
 */
public class TutorUserVideoLog extends DataEntity<TutorUserVideoLog> {
	
	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long courseId;		// course_id
	private Long clsId;		// cls_id
	private Long lessonId;		// lesson_id  courseId和clsId不为空时为讲ID，如果为空则为课件ID
	private Integer status;		// status
	private Date lastTime;		// last_time
	private Integer position;		// position
	private Integer result;		// 是否看懂
	private String ip;		// ip
	private String classes;		// classes

	private Date dbUpdateTime;	//更新到数据库的时间

	private String platForm;
	private Integer startPosition;
	private String appid;

	public TutorUserVideoLog() {
		super();
	}

	public TutorUserVideoLog(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}
	
	public Long getClsId() {
		return clsId;
	}

	public void setClsId(Long clsId) {
		this.clsId = clsId;
	}
	
	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}
	
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Date getLastTime() {
		return lastTime;
	}

	public void setLastTime(Date lastTime) {
		this.lastTime = lastTime;
	}
	
	public Integer getPosition() {
		return position;
	}

	public void setPosition(Integer position) {
		this.position = position;
	}
	
	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

	public String getKey(){
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(this.uid).append("_").append(null == this.courseId ? -1 : this.courseId).append("_").append(null == this.clsId ? -1 : this.clsId).append("_")
				.append(null == this.lessonId ? -1 : this.lessonId);
		return stringBuilder.toString();
	}

	public String getFormatStr(){
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(this.uid).append("_").append(null == this.courseId ? -1 : this.courseId).append("_")
				.append(null == this.clsId ? -1 : this.clsId).append("_")
				.append(null == this.lessonId ? -1 : this.lessonId).append("_").append(this.classes)
				.append("_").append(null == this.status ? "-1": this.status).append("_")
				.append(null == this.position ? "-1" : this.position).append("_")
				.append(null == this.result ? "-1": this.result).append("_").append(DateUtils.getDateTimeSec(lastTime));
//				.append("_").append(DateUtils.getDateTimeSec(this.updateDate));
		return stringBuilder.toString();
	}

	public static TutorUserVideoLog parseFromStr(final String str){
		if(StringUtils.isBlank(str)){
			return null ;
		}
		TutorUserVideoLog log = new TutorUserVideoLog();
		String[] data = str.split("_");
		if(data.length < 9){
			return null;
		}

		log.setUid(Long.parseLong(data[0]));
		if(!data[1].equals("-1")) {
			log.setCourseId(Long.parseLong(data[1]));
		}
		if(!data[2].equals("-1")) {
			log.setClsId(Long.parseLong(data[2]));
		}
		if(!data[3].equals("-1")) {
			log.setLessonId(Long.parseLong(data[3]));
		}
		log.setClasses(data[4]);
		if(!data[5].equals("-1")){
			log.setStatus(Integer.valueOf(data[5]));
		}
		if(!data[6].equals("-1")) {
			log.setPosition(Integer.valueOf(data[6]));
		}
		if(!data[7].equals("-1")) {
			log.setResult(Integer.valueOf(data[7]));
		}
		try {
			log.setLastTime(DateUtils.parseDateTimeSec(data[8]));
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return log;
	}

	public Date getDbUpdateTime() {
		return dbUpdateTime;
	}

	public void setDbUpdateTime(Date dbUpdateTime) {
		this.dbUpdateTime = dbUpdateTime;
	}

	public String getPlatForm() {
		return platForm;
	}

	public void setPlatForm(String platForm) {
		this.platForm = platForm;
	}

	public Integer getStartPosition() {
		return startPosition;
	}

	public void setStartPosition(Integer startPosition) {
		this.startPosition = startPosition;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public static void main(String args[]){
		String str = "8597288_4879_3721_535172016_-1_664_-1_2016-05-05 17:27:31";
		TutorUserVideoLog.parseFromStr(str);
	}
}