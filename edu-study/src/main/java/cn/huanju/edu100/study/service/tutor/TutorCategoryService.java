package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorCategory;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 个性化考试Service
 * <AUTHOR>
 * @version 2016-01-12
 */
public interface TutorCategoryService extends BaseService<TutorCategory> {

    List<TutorCategory> getCategoryByUid(Long uid, Long goodsId) throws DataAccessException;

}
