/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorStudentAnswerDetailDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 做题记录日详情DAO接口
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
public class TutorStudentAnswerDetailIbatisImpl extends CrudIbatisImpl2<TutorStudentAnswerDetail> implements
        TutorStudentAnswerDetailDao {

    public TutorStudentAnswerDetailIbatisImpl() {
        super("TutorStudentAnswerDetail");
    }

    @Override
    public List<Long> getDoneQuestionIdByUidAndTaskIdList(Long uid, List<Long> taskIdList) throws DataAccessException {
        if(null == uid || CollectionUtils.isEmpty(taskIdList)) {
            logger.error("getDoneQuestionIdByUidAndTaskIdList parameter uid or taskIdList is empty,uid:{},taskIdList:{}",uid,GsonUtil.toJson(taskIdList));
            throw new DataAccessException("getDoneQuestionIdByUidAndTaskIdList parameter uid or taskIdList is empty");
        }
        try {
            Map<String,Object> paramMap = Maps.newHashMap();
            paramMap.put("uid",uid);
            paramMap.put("taskIdList",taskIdList);
            SqlMapClient sqlMap = super.getSlave();
            return (List<Long>) sqlMap.queryForList(namespace.concat(".getDoneQuestionIdByUidAndTaskIdList"), paramMap);
        } catch (SQLException e) {
            logger.error("getDoneQuestionIdByUidAndTaskIdList SQLException.", e);
            throw new DataAccessException("getDoneQuestionIdByUidAndTaskIdList SQLException error");
        }
    }
}
