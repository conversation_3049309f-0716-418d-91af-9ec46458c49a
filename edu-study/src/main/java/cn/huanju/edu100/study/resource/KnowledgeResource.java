/*
 * Copyright (c) 2011 duowan.com.
 * All Rights Reserved.
 * This program is the confidential and proprietary information of
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.grpc.client.impl.KnowledgeGrpcClient;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.al.KnowledgeGraph;
import cn.huanju.edu100.study.model.al.QuestionKnowledgeGraphRelationVO;
import cn.huanju.edu100.study.model.dto.CategoryQuestionInfo;
import cn.huanju.edu100.study.model.dto.QuestionTreeVo;
import cn.huanju.edu100.study.model.enterschooltest.KnowledgeGraphChapterInfoVo;
import cn.huanju.edu100.study.model.practice.DailyPracticeResult;
import cn.huanju.edu100.study.model.practice.DailyQueryParam;
import cn.huanju.edu100.study.model.questionBox.QuestionKnowledgeGraph;
import cn.huanju.edu100.study.model.questionBox.Questionbox;
import cn.huanju.edu100.study.model.questionBox.QuestionboxItems;
import cn.huanju.edu100.study.resource.impl.core.ThriftReturnCode;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.NewRedisConsts;
import cn.huanju.edu100.study.util.ThreadPoolFactoryUtil;
import cn.huanju.edu100.stustamp.model.ResourceLive;
import cn.huanju.edu100.thrift.client.dto.ChapterSectionItemDTO;
import cn.huanju.edu100.thrift.client.dto.EvaluationBaseQuestionDTO;
import cn.huanju.edu100.thrift.client.dto.KnowledgeQuestionListDTO;
import cn.huanju.edu100.thrift.edu100_knowledge;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.GsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hqwx.goods.dto.ResourceVideo;
import com.hqwx.thrift.client.api.HqwxKnowledgeThriftClient;
import com.hqwx.thrift.client.base.ThriftRequest;
import com.hqwx.thrift.client.base.ThriftResponse;
import com.hqwx.thrift.client.param.EvaluationBaseQuestionQuery;
import com.hqwx.thrift.client.param.QuestionBoxQuestionQuery;
import com.hqwx.thrift.client.thrift.ThriftClientWrapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class KnowledgeResource {

    public static final int APP_ID = 7;
    public static final int BATCH = 100;
    @Autowired
    private IGenericThriftResource genericThriftResource;
    @Autowired
    private EhCacheCacheManager cacheManager;
    @javax.annotation.Resource
    private HqwxKnowledgeThriftClient knowledgeClient;
    @Autowired
    private KnowledgeGrpcClient knowledgeGrpcClient;

    private static final Logger LOG = LoggerFactory.getLogger(KnowledgeResource.class);
    private static ExecutorService es = ThreadPoolFactoryUtil.createDefaultPool("knowledgeThreedPool");

    public List<Question> getQuestionByIds(List<Long> idList) {

        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        }
        //如果超过x道题，就拆分成x道一组，并发请求
        if (idList.size() > BATCH) {
            List<Question> result = new ArrayList<>();
            List<Future<List<Question>>> futureList = new ArrayList<>();
            for (int i = 0; i < idList.size(); i += BATCH) {
                List<Long> subList = idList.subList(i, Math.min(i + BATCH, idList.size()));
                Future<List<Question>> future = es.submit(() -> getQuestionByIdsInBatch(subList));
                futureList.add(future);
            }
            for (Future<List<Question>> future : futureList) {
                try {
                    result.addAll(future.get(2000, TimeUnit.MILLISECONDS));
                } catch (Exception e) {
                    LOG.error("[getQuestionByIds] error", e);
                }
            }
            return result;

        } else {
            return getQuestionByIdsInBatch(idList);
        }
    }

    private List<Question> getQuestionByIdsInBatch(List<Long> ids) {


        if (ids == null || ids.size() == 0) {
            return Collections.emptyList();
        } else if (ids.size() > BATCH) {
            ids = ids.subList(0,BATCH);
        }

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("id", ids);
        String param = GsonUtil.toJson(paramMap);
        try {

            LOG.info("[getQuestionByIds] start, param is:{}", param);
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Question>>() {
                }.getType();
                List<Question> questions = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questions;
            }

        } catch (Exception e) {
            LOG.error("[getQuestionByIds] error param:{}", param, e);
        }
        return Collections.emptyList();
    }

    public List<Category> getCategorys(Long level, Long parent_id) {

        Map<String, Object> param = Maps.newHashMap();
        if(null != level) {
            param.put("level", level);
        }
        if(null != parent_id) {
            param.put("parent_id", parent_id);
        }
        String strParam = GsonUtil.getGenericGson().toJson(param);
        try {
            LOG.info("[klg_getCategorys] start, param is:{}", param);
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(strParam, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getCategorys");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Category>>() {}.getType();
                List<Category> categoryList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return categoryList;
            }
        } catch (Exception e) {
            LOG.error("[klg_getCategorys] error param:{}", param, e);
        }
        return null;
    }

    public Map<Long, List<QuestionKnowledge>> getKnowledgeByQuestionIdList(List<Long> questionIds) {

        if (questionIds == null || questionIds.size() <= 0) {
            return null;
        }
        String param = GsonUtil.getGenericGson().toJson(questionIds);
        try {
            LOG.info("[klg_getKnowledgeByQuestionIdList] start, param is:{}", param);
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeByQuestionIdList");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<Long, List<QuestionKnowledge>>>() {
                }.getType();
                Map<Long, List<QuestionKnowledge>> questionKnowledges = GsonUtil.getGenericGson().fromJson(
                        response.getMsg(), type);
                return questionKnowledges;
            }

        } catch (Exception e) {
            LOG.error("[klg_getKnowledgeByQuestionIdList] error param:{}", param, e);
        }
        return null;
    }

    public Map<Long, Integer> getQuestionStateByIdList(List<Long> questionIds) {

        if (questionIds == null || questionIds.size() <= 0) {
            return null;
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("id", questionIds);
        String strParam = GsonUtil.getGenericGson().toJson(param);
        try {
            LOG.info("[klg_getQuestionStateByIdList] start, param is:{}", param);
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(strParam, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionStateByIdList");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<Long, Integer>>() {
                }.getType();
                Map<Long, Integer> questionKnowledges = GsonUtil.getGenericGson().fromJson(
                        response.getMsg(), type);
                return questionKnowledges;
            }

        } catch (Exception e) {
            LOG.error("[klg_getQuestionStateByIdList] error param:{}", param, e);
        }
        return null;
    }

    @Cacheable(value = "questionCache", key = "'KnowledgeResource.'+methodName + #questionId")
    public List<QuestionKnowledge> getKnowledgeByQuestionId(Long questionId) throws DataAccessException{

        if (questionId == null || questionId <= 0) {
            return Collections.emptyList();
        }
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("question_id", questionId);
        String param = GsonUtil.getGenericGson().toJson(paramMap);
        try {
            LOG.info("[getKnowledgeByQuestionId] start, param is:{}", param);
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeByQuestionId");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<QuestionKnowledge>>() {
                }.getType();
                List<QuestionKnowledge> questionKnowledges = GsonUtil.getGenericGson()
                        .fromJson(response.getMsg(), type);
                return questionKnowledges;
            }

        } catch (Exception e) {
            LOG.error("[getKnowledgeByQuestionId] error param:{}", param, e);
            throw new DataAccessException(e);
        }
        return Collections.emptyList();
    }

    @Cacheable(value = "knowledgeCache", key = "'getSpecialTypeQuestionIdsByKnowlwdgeIdList=' + #knowledgeIdList +'-'+#specialType")
    public List<QuestionKnowledgeGraph> getSpecialTypeQuestionIdsByKnowlwdgeIdList(List<Long> knowledgeIdList, Integer specialType)  {
        List<QuestionKnowledgeGraph> result = null;
        Map<String,Object> params = Maps.newHashMap();
        params.put("knowledgeIdList", knowledgeIdList);
        params.put("specialType", specialType);
        String paramString = GsonUtils.toJson(params);
        ThriftClientWrapper<edu100_knowledge.Iface> knowledgeClient = null;
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(paramString, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getSpecialTypeQuestionIdsByKnowlwdgeIdList");
            if (response != null && response.getCode() == 0 && org.apache.commons.lang.StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<List<QuestionKnowledgeGraph>>(){}.getType();
                result = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
            } else {
                LOG.error("getSpecialTypeQuestionIdsByKnowlwdgeIdList param:{} errcode:{} errMsg:{}", GsonUtils.toDefaultJson(params), response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error("getSpecialTypeQuestionIdsByKnowlwdgeIdList failed param: {} ,error: {}" + GsonUtils.toDefaultJson(params), e);
        }
        return result;
    }

    public QuestionGroupRelation getQuestionScoreInPaper(Long paperId, Long questionId) throws DataAccessException{
        if (paperId == null || paperId <= 0 || questionId == null || questionId <= 0) {
            return null;
        }
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("paper_id", paperId);
        paramMap.put("question_id", questionId);
        String param = GsonUtil.getGenericGson().toJson(paramMap);
        try {
            LOG.info("[getQuestionScoreInPaper] start, param is:{}", param);
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionScoreInPaper");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                QuestionGroupRelation questionGroupRelation = GsonUtil.getGenericGson().fromJson(response.getMsg(),
                        QuestionGroupRelation.class);

                return questionGroupRelation;
            }

        } catch (Exception e) {
            LOG.error("[getQuestionScoreInPaper] error param:{}", param, e);
        }
        return null;
    }

    public Map<Long,QuestionGroupRelation> getQuestionScoresInPaper(Long paperId, List<Long> questionIds) throws DataAccessException{
        if (paperId == null || paperId <= 0 ) {
            return null;
        }
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("paper_id", paperId);
        String param = GsonUtil.getGenericGson().toJson(paramMap);
        Map<Long,QuestionGroupRelation> result = new HashMap<Long, QuestionGroupRelation>();
        try {
            LOG.info("[getQuestionScoreInPaper] start, param is:{}", param);
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionScoresInPaper");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {

                QuestionGroupRelation[] questionGroupRelations = (QuestionGroupRelation[]) GsonUtil.getGenericGson().fromJson(response.getMsg(),
                        QuestionGroupRelation[].class);
                for(QuestionGroupRelation questionGroupRelation : questionGroupRelations ){
                    result.put(questionGroupRelation.getQuestionId(),questionGroupRelation);
                }

                return result;
            }

        } catch (Exception e) {
            LOG.error("[getQuestionScoreInPaper] error param:{}", param, e);
        }
        return null;
    }

    /**
     * 根据知识点IdList查询微课
     *
     * @param knowledgeIdList
     * @return
     */
    public List<Knowledge> getWeiCoursesByKnowledgeIdList(List<Long> knowledgeIdList) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("", "");

        String param = GsonUtil.getGenericGson().toJson(knowledgeIdList);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getWeiCoursesByKnowledgeIdList");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Knowledge>>() {
                }.getType();
                List<Knowledge> knowledges = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return knowledges;
            }
        } catch (DataAccessException e) {
            LOG.error("[getWeiCoursesByKnowledgeIdList] error param:{}", param, e);
        }
        return null;
    }

    /**
     * 根据resId查询资源
     *
     * @param resId
     * @return
     */
    @Cacheable(value = "resourceCache", key = "'KnowledgeResource.'+methodName + #resId")
    public Resource getResourceById(Long resId) {
        if (resId == null || resId <= 0) {
            return null;
        }
        List<Long> resIdList = new ArrayList<Long>();
        resIdList.add(resId);
        String param = GsonUtil.getGenericGson().toJson(resIdList);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getResourcesByIdList");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Resource>>() {
                }.getType();
                List<Resource> resources = GsonUtil.getGson().fromJson(response.getMsg(), type);
                if (CollectionUtils.isEmpty(resources)) {
                    return null;
                }
                return resources.get(0);
            }
        } catch (DataAccessException e) {
            LOG.error("[getResourceById] error param:{}", param, e);
        }
        return null;
    }

    /**
     * 根据试卷id查询试卷
     *
     * @param paperId
     * @return
     */
//    @Cacheable(value = "paperCache", key = "'KnowledgeResource.'+methodName + #paperId")
    public Paper getPaperInfoById(Long paperId) {
        if (paperId == null || paperId <= 0) {
            return null;
        }
        Paper paper = new Paper();
        paper.setId(paperId);
        String param = GsonUtil.getGenericGson().toJson(paper);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getPaperInfoById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
                Paper result = GsonUtil.getGenericGson().fromJson(response.getMsg(), Paper.class);
                return result;
            }
        } catch (DataAccessException e) {
            LOG.error("[getPaperInfoById] error param:{}", param, e);
        }
        return null;
    }


    public Paper getPaperQuestionsByPaperId(Long id) {
        Paper result = new Paper();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        String param = GsonUtil.getGenericGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionByPaperId");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<Paper>(){}.getType();
                result = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return result;
            } else {
                LOG.error("getPaperQuestionsByPaperId  param:{} response:{}", GsonUtils.toDefaultJson(param),
                        GsonUtils.toDefaultJson(response));
            }
        } catch (Exception e) {
            LOG.error("getPaperQuestionsByPaperId failed param: {} ,error: {}" + GsonUtils.toDefaultJson(param), e);
        }
        return null;
    }

    /**
     * 消息通知推送
     *
     * @param categoryId
     * @param notifyType
     * @return
     */
    public boolean addOaNotifyRecord(Long categoryId, Long notifyType, String title, String content, Long uid) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("category_id", categoryId);
        map.put("notify_type", notifyType);
        map.put("title", title);
        map.put("content", content);
        map.put("uid", uid);

        String param = GsonUtil.getGenericGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_addOaNotifyRecord");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                return true;
            }
        } catch (DataAccessException e) {
            LOG.error("[getWeiCoursesByKnowledgeIdList] error param:{}", param, e);
        }
        return false;
    }

    /**
     * 根据id查询章节
     *
     * @param id
     * @return
     */
    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #id")
    public ChapterSection getChapterSectionById(Long id) throws DataAccessException{
        if (id == null || id <= 0) {
            return null;
        }
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("id", id);
        String paramStr = GsonUtil.getGenericGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(paramStr, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChapterSectionById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {

            	ChapterSection chapterSection = GsonUtil.getGenericGson().fromJson(response.getMsg(), ChapterSection.class);
                return chapterSection;
            }
        } catch (DataAccessException e) {
            LOG.error("[getChapterSectionById] error param:{}", param, e);
            throw e;
        }
        return null;
    }

    /**
     * 根据id查询题库信息
     *
     * @param id
     * @return
     */
    @Cacheable(value = "questionCache", key = "'KnowledgeResource.'+methodName + #id")
    public Questionbox getQuestionBoxById(Long id){
        if (id == null || id <= 0) {
            return null;
        }
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("id", id);
        String paramStr = GsonUtil.getGenericGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(paramStr, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionBoxById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {

            	Questionbox questionbox = GsonUtil.getGenericGson().fromJson(response.getMsg(), Questionbox.class);
                return questionbox;
            }
        } catch (DataAccessException e) {
            LOG.error("[getQuestionBoxById] error param:{}", param, e);
        }
        return null;
    }

    /**
     * 根据id查询知识点
     *
     * @param id
     * @return
     */
    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #id")
    public Knowledge getKnowledgeById(Long id) throws DataAccessException{
        if (id == null || id <= 0) {
            return null;
        }
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("id", id);
        String paramStr = GsonUtil.getGenericGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(paramStr, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {

            	Knowledge knowledge = GsonUtil.getGenericGson().fromJson(response.getMsg(), Knowledge.class);
                return knowledge;
            }
        } catch (DataAccessException e) {
            LOG.error("[getKnowledgeById] error param:{}", param, e);
            throw e;
        }
        return null;
    }
    /**
     * 根据id查询题库信息
     *
     * @param items
     * @return
     */
    public List<QuestionboxItems> getQuestionBoxItemsByBoxId(QuestionboxItems items) throws DataAccessException{
        if (items == null || items.getBoxId() == null || items.getBoxId() <= 0) {
            return null;
        }
        String paramStr = GsonUtil.getGenericGson().toJson(items);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(paramStr, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionBoxItemsByBoxId");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
            	java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<QuestionboxItems>>() {
                }.getType();
                List<QuestionboxItems> questionboxItems = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if (CollectionUtils.isEmpty(questionboxItems)) {
                    return null;
                }
                return questionboxItems;
            }
        } catch (DataAccessException e) {
            LOG.error("[getQuestionBoxItemsByBoxId] error param:{}", items, e);
            throw e;
        }
        return null;
    }

	public List<ChapterSection> getChapterSectionByKnowledgeIdAndBookId(
			Long teachBookId, Long knowledgeId) {
		Map<String, Object> map = new HashMap<String, Object>();
        map.put("teach_book_id", teachBookId);
        map.put("knowledge_id", knowledgeId);

        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChapterSectionByKnowledgeId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<ChapterSection>>() {
                }.getType();
                List<ChapterSection> chapterSections = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return chapterSections;
            }
        } catch (DataAccessException e) {
            LOG.error("[getChapterSectionByKnowledgeIdAndBookId] error param:{}", param, e);
        }
        return null;
	}

    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #chaperId")
	public List<Knowledge> getKnowledgeByChapterId(Long chaperId) {
		Map<String, Object> map = new HashMap<String, Object>();
        map.put("chapter_id", chaperId);

        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeByChapterId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Knowledge>>() {
                }.getType();
                List<Knowledge> knowledges = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return knowledges;
            }
        } catch (DataAccessException e) {
            LOG.error("[getKnowledgeByChapterId] error param:{}", param, e);
        }
        return null;
	}

	/**
	 * 获取当前章节的子节点
	 * */
    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #chapterSectionId")
	public List<ChapterSection> getChildChapterSectionById(Long chapterSectionId) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("chapter_id", chapterSectionId);

        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChildChapterSectionById");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<ChapterSection>>() {
                }.getType();
                List<ChapterSection> chapterSections = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return chapterSections;
            }
        } catch (DataAccessException e) {
            LOG.error("[getChildChapterSectionById] error param:{}", param, e);
        }
        return null;
	}
    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #schId")
	public List<Long> getAllSubSchIds(Long schId) {
		if (schId == null || schId < 0L) {
            return null;
        }
		HashMap<String, Long> paramMap = new HashMap<String, Long>();
        String param = GsonUtil.toJson(paramMap);
        try {
            LOG.info("[klg_getAllSubSchIds] start, param is:{}", param);
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, 7,
                    Consts.Code.CLIENT_IP, null, schId, "klg_getAllSubSchIds");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {
            	java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {
                }.getType();
                List<Long> schIds = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return schIds;
            }

        } catch (Exception e) {
            LOG.error("[klg_getAllSubSchIds] error param:{}", param, e);
        }
        return Collections.emptyList();
	}

	public List<Questionbox> klg_listQuestionBoxBatch(Map<String, Object> params) {
		if (params.get("from") == null || Double.valueOf(params.get("from").toString()) < 0
				|| params.get("rows") == null || Double.valueOf(params.get("rows").toString()) < 0) {
            return null;
        }
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_listQuestionBoxBatch");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<PageModel<Questionbox>>() {
                }.getType();
                PageModel<Questionbox> questionBoxs = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questionBoxs.getList();
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_listQuestionBoxBatch] error param:{}", param, e);
        }
        return null;
	}

	public List<TeachingBook> klg_getTeachBookByCategoryId(Map<String, Object> params) {
		if (params.get("category_id") == null || Double.valueOf(params.get("category_id").toString()) < 0) {
            return null;
        }
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getTeachBookByCategoryId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<TeachingBook>>() {
                }.getType();
                List<TeachingBook> books = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return books;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getTeachBookByCategoryId] error param:{}", param, e);
        }
        return null;
	}

    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #categoryId")
    public List<TeachingBook> klg_getTeachBookItemByCategoryId(Long categoryId) {
        if (categoryId == null || categoryId < 0) {
            return null;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("category_id", categoryId);
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getTeachBookItemByCategoryId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<TeachingBook>>() {
                }.getType();
                List<TeachingBook> books = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return books;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getTeachBookByCategoryId] error param:{}", param, e);
        }
        return null;
    }

    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #categoryId ")
    public Questionbox getQBoxListByCategoryId(Long categoryId) {
        if (categoryId == null || categoryId <= 0L) {
            return null;
        }
        Map<String,Object> params = Maps.newHashMap();
        params.put("category_ids", Lists.newArrayList(categoryId));
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQBoxListByCategoryIds");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Questionbox>>() {}.getType();
                List<Questionbox> boxs = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if (CollectionUtils.isEmpty(boxs)) {
                    return null;
                } else {
                    return boxs.get(0);
                }
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQBoxListByCategoryIds] error param:{}", param, e);
        }
        return null;
    }

    public List<Questionbox> getQBoxListByCategoryIds(Map<String, Object> params) {
        if (params.get("category_ids") == null) {
            return null;
        }
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQBoxListByCategoryIds");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Questionbox>>() {}.getType();
                List<Questionbox> boxs = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return boxs;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQBoxListByCategoryIds] error param:{}", param, e);
        }
        return null;
    }
    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #boxId + '_' + #teachBookId")
    public Map<String, String> getQuestionMapByBoxAndTeachBook(Long boxId, Long teachBookId) {
        if (boxId == null || teachBookId == null) {
            return null;
        }
        Map<String, Long> params = Maps.newHashMap();
        params.put("box_id", boxId);
        params.put("teach_book_id", teachBookId);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(GsonUtil.toJson(params), APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionMapByBoxAndTeachBook");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType();
                Map<String, String> questionMap = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questionMap;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQuestionMapByBoxAndTeachBook] error param:{}", params, e);
        }
        return null;
    }


    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName +  '_' + #teachBookItemId")
    public Map<String, String> getQuestionMapByTeachBookItem(Long teachBookItemId) {
        if (teachBookItemId == null) {
            return null;
        }
        Map<String, Long> params = Maps.newHashMap();
        params.put("teach_book_id", teachBookItemId);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(GsonUtil.toJson(params), APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionMapByTeachBookItem");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType();
                Map<String, String> questionMap = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questionMap;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQuestionMapByTeachBookItem] error param:{}", params, e);
        }
        return null;
    }

//    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #boxId + '_' + #teachBookId")
    public Map<String, String> getShuYeQuestionMapByBoxAndTeachBook(Long teachBookId) {
        if (teachBookId == null) {
            return null;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("teach_book_id", teachBookId);
        params.put("origin",Consts.QuestionOrigin.Shuye);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(GsonUtil.toJson(params), APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionMapByTeachBookItem");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType();
                Map<String, String> questionMap = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questionMap;
            }
        } catch (DataAccessException e) {
            LOG.error("getShuyeQuestionMapByBoxAndTeachBook error param:{}", params, e);
        }
        return null;
    }


    public String getQuestionListStrByBoxAndTeachBookAndField(Long boxId, Long teachBookId, String field) {
        Cache cache = cacheManager.getCache("knowledgeCache");
        String key = "KnowledgeResource.getQuestionMapByBoxAndTeachBook"+boxId+"_"+teachBookId;
        Map<String, String> questionMap = null;
        if (cache.get(key) != null) {
            questionMap = (Map<String,String>)cache.get(key).get();
        }
        if (questionMap == null || questionMap.size() == 0) {
            questionMap = this.getQuestionMapByBoxAndTeachBook(boxId, teachBookId);
        }
        if (questionMap != null && questionMap.size() > 0) {
            return questionMap.get(field);
        } else {
            return null;
        }
    }


    public String getQuestionListStrByTeachBookItemAndField(Long teachBookItemId, String field) {
        Cache cache = cacheManager.getCache("knowledgeCache");
        String key = "KnowledgeResource.getQuestionMapByTeachBookItem"+"_"+teachBookItemId;
        Map<String, String> questionMap = null;
        if (cache.get(key) != null) {
            questionMap = (Map<String,String>)cache.get(key).get();
        }
        if (questionMap == null || questionMap.size() == 0) {
            questionMap = this.getQuestionMapByTeachBookItem(teachBookItemId);
        }
        if (questionMap != null && questionMap.size() > 0) {
            return questionMap.get(field);
        } else {
            return null;
        }
    }

    public String getShuYeQuestionListStrByTeachBookItemAndField(Long teachBookItemId, String field) {
        Cache cache = cacheManager.getCache("knowledgeCache");
        String key = "KnowledgeResource.getShuYeQuestionMapByBoxAndTeachBook"+"_"+teachBookItemId;
        Map<String, String> questionMap = null;
        if (cache.get(key) != null) {
            questionMap = (Map<String,String>)cache.get(key).get();
        }
        if (questionMap == null || questionMap.size() == 0) {
            questionMap = this.getShuYeQuestionMapByBoxAndTeachBook(teachBookItemId);
        }
        if (questionMap != null && questionMap.size() > 0) {
            return questionMap.get(field);
        } else {
            return null;
        }
    }

    public List<ChapterSection> klg_getChapterSectionByBookId(Long teachBookId) {

        if (teachBookId == null) {
            return null;
        }
        Map<String, Long> params = Maps.newHashMap();
        params.put("teach_book_id", teachBookId);
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChapterSectionByBookId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<ChapterSection>>() {}.getType();
                List<ChapterSection> chapterSectionList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return chapterSectionList;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getChapterSectionByBookId] error param:{}", param, e);
        }
        return null;
    }

    public List<Paper> klg_getPaperListByCategoryId(Long categoryId,Integer paperType) {
        if (categoryId == null) {
            return Collections.emptyList();
        }
        Map param = Maps.newHashMap();
        param.put("category_id",categoryId);
        param.put("type",paperType);
        String json = GsonUtil.getGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(json, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getPaperListByParam");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Paper>>() {
                }.getType();
                List<Paper> papers = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return papers;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getPaperListByCategoryId] error param:{}", param, e);
        }
        return Collections.emptyList();
    }
    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #paperId")
    public List<QuestionGroup> klg_getQuestionByPaperId(Long paperId) {
        if (paperId == null) {
            return null;
        }
        Map param = Maps.newHashMap();
        param.put("id",paperId);
        String json = GsonUtil.getGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(json, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionByPaperId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Paper>() {
                }.getType();
                Paper paper = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return paper!=null?Lists.newArrayList(paper.getGroupList()):null;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQuestionByPaperId] error param:{}", param, e);
        }
        return null;
    }
    public List<KnowledgeGraph> klg_getKnowledgeGraphByParam(Long categoryId) {
        if (categoryId == null) {
            return null;
        }
        KnowledgeGraph knowledgeGraph = new KnowledgeGraph();
        knowledgeGraph.setCategoryId(categoryId);
        knowledgeGraph.setState(Byte.parseByte("1"));
        knowledgeGraph.setType((byte) 1);
        Page<KnowledgeGraph> page = new Page<>();
        page.setFrom(0);
        page.setPageSize(1000);
        knowledgeGraph.setPage(page);
        String json = GsonUtil.getGenericGson().toJson(knowledgeGraph);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(json, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeGraphByParam");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<KnowledgeGraph>>() {
                }.getType();
                List<KnowledgeGraph> knowledgeGraphs = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return knowledgeGraphs;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getKnowledgeGraphByParam] error param:{}", json, e);
        }
        return null;
    }

    public List<Question> klg_getQuestionByKnowledgeId(Long knowledgeGraphId,int num) {
        if (knowledgeGraphId == null) {
            return null;
        }
        Map param = Maps.newHashMap();
        param.put("knowledge_id",knowledgeGraphId);
        param.put("num",num);
        String json = GsonUtil.getGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(json, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionByKnowledgeId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Question>>() {
                }.getType();
                List<Question> questions = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questions;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQuestionByKnowledgeId] error param:{}", param, e);
        }
        return null;
    }


    public List<Question> klg_getQuestionByKnowleldgeGraphIds(List<Long> knowledgeGraphIds,int num,Long categoryId) {
        if (knowledgeGraphIds == null) {
            return null;
        }
        Map param = Maps.newHashMap();
        param.put("knowledge_graph_ids",knowledgeGraphIds);
        param.put("num",num);
        param.put("category_id",categoryId);
        String json = GsonUtil.getGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(json, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionByKnowleldgeGraphIds");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Question>>() {
                }.getType();
                List<Question> questions = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questions;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQuestionByKnowleldgeGraphIds] error param:{}", param, e);
        }
        return null;
    }

    public List<Question> klg_getQuestionByCategoryId(Long categoryId,int num,List<Long> filterQuestionIds) {
        if (categoryId == null) {
            return null;
        }
        Map param = Maps.newHashMap();
        param.put("category_id",categoryId);
        param.put("num",num);
        param.put("question_id_list",filterQuestionIds);
        String json = GsonUtil.getGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(json, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionByCategoryId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Question>>() {
                }.getType();
                List<Question> questions = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questions;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQuestionByCategoryId] error param:{}", param, e);
        }
        return null;
    }


    public List<QuestionKnowledgeGraphRelationVO> klg_getQuestionKnowledgeGraphRelationByQuestionIds(Long categoryId,List<Long> questionIds) {
        if (categoryId == null) {
            return null;
        }
        Map param = Maps.newHashMap();
        param.put("category_id",categoryId);
        param.put("question_ids",questionIds);
        String json = GsonUtil.getGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(json, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionKnowledgeGraphRelationByQuestionIds");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<QuestionKnowledgeGraphRelationVO>>() {
                }.getType();
                List<QuestionKnowledgeGraphRelationVO> questions = GsonUtil.getGson().fromJson(response.getMsg(), type);
                return questions;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQuestionKnowledgeGraphRelationByQuestionIds] error param:{}", param, e);
        }
        return null;
    }

    public List<QuestionKnowledgeGraph> getQuestionKnowledgeGraphByIdList(List<Long> knowledgeIds, List<Long> questionIds) {
        List<QuestionKnowledgeGraph> result = Collections.emptyList();

        if (CollectionUtils.isEmpty(knowledgeIds) && CollectionUtils.isEmpty(questionIds)) {
            LOG.error("getQuestionKnowledgeGraphByIdList param error");
            return result;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("knowledgeList", knowledgeIds);
        map.put("questionList",questionIds);

        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionKnowledgeGraphByIdList");
            if (null != response && 0 == response.getCode() && StringUtils.isNotBlank(response.getMsg())) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<QuestionKnowledgeGraph>>() {
                }.getType();
                result =  GsonUtil.getGenericGson().fromJson(response.getMsg(),type);
            }
        } catch (Exception e) {
            LOG.error("[klg_getQuestionKnowledgeGraphByIdList] error param:{},{}", param, e);
        }
        return result;
    }

    public KnowledgeGraphChapterInfoVo klg_getKnowledgeGraphOneChapterInfo(Long knowledgeGraphId) {
        if (knowledgeGraphId == null) {
            return null;
        }
        Map param = Maps.newHashMap();
        param.put("knowledge_graph_id",knowledgeGraphId);
        String json = GsonUtil.getGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(json, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeGraphOneChapterInfo");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<KnowledgeGraphChapterInfoVo>() {
                }.getType();
                KnowledgeGraphChapterInfoVo vo = GsonUtil.getGson().fromJson(response.getMsg(), type);
                return vo;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getKnowledgeGraphOneChapterInfo] error param:"+param, e);
        }
        return null;
    }

    public DailyPracticeResult getDailyPractice(DailyQueryParam dailyQueryParam){
        String param = GsonUtil.getGson().toJson(dailyQueryParam);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getDailyPractices");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<List<DailyPracticeResult>>(){}.getType();
                List<DailyPracticeResult> resultList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if (CollectionUtils.isEmpty(resultList)){
                    return null;
                } else {
                    return resultList.get(0);
                }
            } else {
                LOG.error("klg_getDailyPractices  param:{} response:{}", GsonUtils.toDefaultJson(param),
                        GsonUtils.toDefaultJson(response));
            }
        } catch (Exception e) {
            LOG.error("klg_getDailyPractices failed param: {} ,error: {}" + GsonUtils.toDefaultJson(param), e);
        }
        return null;

    }

    public Category getCategoryInfoById(Long categoryId){
        Map<String, Object> params = new HashMap<>();
        params.put("id", categoryId);
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getCategoryInfoById");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<Category>(){}.getType();
                Category category = GsonUtil.getGson().fromJson(response.getMsg(), type);
                return category;
            } else {
                LOG.error("klg_getCategoryInfoById  param:{} response:{}", GsonUtils.toDefaultJson(param),
                        GsonUtils.toDefaultJson(response));
            }
        } catch (Exception e) {
            LOG.error("klg_getCategoryInfoById failed param: {} ,error: {}" + GsonUtils.toDefaultJson(param), e);
        }
        return null;

    }
    public List<Category> getCategoryList(Collection<Long> ids){
        String param = GsonUtil.getGson().toJson(ids);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getCategorysByIdList");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<List<Category>>(){}.getType();
                List<Category> categoryList = GsonUtil.getGson().fromJson(response.getMsg(), type);
                return categoryList;
            } else {
                LOG.error("klg_getCategoryInfoById  param:{} response:{}", GsonUtils.toDefaultJson(param),
                        GsonUtils.toDefaultJson(response));
            }
        } catch (Exception e) {
            LOG.error("klg_getCategoryInfoById failed param: {} ,error: {}", param, e);
        }
        return null;

    }
    public List<QuestionTreeVo> klg_findQuestionsTreeByIds(List<Long> questionIds){
        Map<String, Object> params = new HashMap<>();
        params.put("question_ids", questionIds);
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_findQuestionsTreeByIds");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<List<QuestionTreeVo>>(){}.getType();
                List<QuestionTreeVo> questionTreeVos = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questionTreeVos;
            } else {
                LOG.error("klg_findQuestionsTreeByIds  param:{} response:{}", GsonUtils.toDefaultJson(param),
                        GsonUtils.toDefaultJson(response));
            }
        } catch (Exception e) {
            LOG.error("klg_findQuestionsTreeByIds failed param: {} ,error: {}" + GsonUtils.toDefaultJson(param), e);
        }
        return null;

    }


    /**
     * 获取当前章节的子节点
     * */
    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #chapterSectionId")
    public List<ChapterSection> getChildChapterSectionItemById(Long chapterSectionId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("chapter_id", chapterSectionId);

        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChildChapterSectionItemById");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<ChapterSection>>() {
                }.getType();
                List<ChapterSection> chapterSections = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return chapterSections;
            }
        } catch (DataAccessException e) {
            LOG.error("[getChildChapterSectionItemById] error param:{}", param, e);
        }
        return null;
    }

    /**
     * 根据id查询章节
     *
     * @param id
     * @return
     */
    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #id",unless = "#result == null")
    public ChapterSection getChapterSectionItemById(Long id) throws DataAccessException{
        if (id == null || id <= 0) {
            return null;
        }
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("id", id);
        String paramStr = GsonUtil.getGenericGson().toJson(param);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(paramStr, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChapterSectionItemById");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()) {

                ChapterSection chapterSection = GsonUtil.getGenericGson().fromJson(response.getMsg(), ChapterSection.class);
                return chapterSection;
            }
        } catch (DataAccessException e) {
            LOG.error("[getChapterSectionById] error param:{}", param, e);
            throw e;
        }
        return null;
    }


    public List<ChapterSection> getChapterSectionItemByKnowledgeIdAndBookId(
            Long teachBookId, Long knowledgeId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("teach_book_id", teachBookId);
        map.put("knowledge_id", knowledgeId);

        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChapterSectionItemByKnowledgeId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<ChapterSection>>() {
                }.getType();
                List<ChapterSection> chapterSections = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return chapterSections;
            }
        } catch (DataAccessException e) {
            LOG.error("[getChapterSectionItemByKnowledgeIdAndBookId] error param:{}", param, e);
        }
        return null;
    }


    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #chaperId")
    public List<KnowledgeGraph> getKnowledgeGraphByChapterId(Long chaperId) {
        String param = GsonUtil.getGson().toJson(chaperId);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeGraphByChapterId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<KnowledgeGraph>>() {
                }.getType();
                List<KnowledgeGraph> knowledges = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return knowledges;
            }
        } catch (DataAccessException e) {
            LOG.error("[getKnowledgeGraphByChapterId] error param:{}", param, e);
        }
        return null;
    }

    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #id")
    public KnowledgeGraph getKnowledgeGraphById(Long id) {
        List<KnowledgeGraph> knowledgeGraphList = getKnowledgeGraphByIdList(Lists.newArrayList(id));
        if (CollectionUtils.isEmpty(knowledgeGraphList)) {
            return null;
        } else {
            return knowledgeGraphList.get(0);
        }
    }

    public List<KnowledgeGraph> getKnowledgeGraphByIdList(List<Long> idList) {
        Cache cache = cacheManager.getCache("knowledgeCache");
        List<KnowledgeGraph> result = Lists.newArrayList();
        List<Long> dbIdList = Lists.newArrayList();
        for (Long id : idList) {
            String key = "KnowledgeResource.getKnowledgeGraphById"+id;
            if (cache.get(key) != null) {
                result.add((KnowledgeGraph)cache.get(key).get());
            } else {
                dbIdList.add(id);
            }
        }
        if (CollectionUtils.isEmpty(dbIdList)) {
            return result;
        }
        String param = GsonUtil.getGson().toJson(dbIdList);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeGraphByIdList");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<KnowledgeGraph>>() {
                }.getType();
                List<KnowledgeGraph> knowledges = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                result.addAll(knowledges);
            }
        } catch (DataAccessException e) {
            LOG.error("[getKnowledgeGraphByIdList] error param:{}", param, e);
        }
        return result;
    }

    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #questionId")
    public List<QuestionKnowledgeGraph> getKnowledgeGraphByQuestionId(Long questionId) throws DataAccessException{

        if (questionId == null || questionId <= 0) {
            return Collections.emptyList();
        }
        return getKnowledgeGraphByQuestionIdList(Lists.newArrayList(questionId));
    }

    public List<QuestionKnowledgeGraph> getKnowledgeGraphByQuestionIdList(List<Long> idList) {
        Cache cache = cacheManager.getCache("knowledgeCache");
        List<QuestionKnowledgeGraph> result = Lists.newArrayList();
        List<Long> dbIdList = Lists.newArrayList();
        for (Long id : idList) {
            String key = "KnowledgeResource.getKnowledgeGraphByQuestionId"+id;
            if (cache.get(key) != null) {
                result.add((QuestionKnowledgeGraph)cache.get(key).get());
            } else {
                dbIdList.add(id);
            }
        }
        if (CollectionUtils.isEmpty(dbIdList)) {
            return result;
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("questionList", dbIdList);
        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionKnowledgeGraphByIdList");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<QuestionKnowledgeGraph>>() {
                }.getType();
                List<QuestionKnowledgeGraph> knowledges = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                result.addAll(knowledges);
            }
        } catch (DataAccessException e) {
            LOG.error("[getKnowledgeGraphByQuestionIdList] error param:{}", param, e);
        }
        return result;
    }

    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #boxId + '_' + #teachBookId")
    public Map<String, String> getTiku5QuestionMapByBoxAndTeachBook(Long boxId, Long teachBookId) {
        if (boxId == null || teachBookId == null) {
            return null;
        }
        Map<String, Long> params = Maps.newHashMap();
//        params.put("box_id", boxId);
        params.put("teach_book_id", teachBookId);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(GsonUtil.toJson(params), APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionMapByTeachBookItem");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType();
                Map<String, String> questionMap = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questionMap;
            }
        } catch (DataAccessException e) {
            LOG.error("getTiku5QuestionMapByBoxAndTeachBook error param:{}", params, e);
        }
        return null;
    }



    public String getTiku5QuestionListStrByBoxAndTeachBookAndField(Long boxId, Long teachBookId, String field) {
        Cache cache = cacheManager.getCache("knowledgeCache");
        String key = "KnowledgeResource.getTiku5QuestionMapByBoxAndTeachBook"+boxId+"_"+teachBookId;
        Map<String, String> questionMap = null;
        if (cache.get(key) != null) {
            questionMap = (Map<String,String>)cache.get(key).get();
        }
        if (questionMap == null || questionMap.size() == 0) {
            questionMap = this.getTiku5QuestionMapByBoxAndTeachBook(boxId, teachBookId);
        }
        if (questionMap != null && questionMap.size() > 0) {
            return questionMap.get(field);
        } else {
            return null;
        }
    }

    public String getShuYeQuestionListStrByBoxAndTeachBookAndField(Long boxId, Long teachBookId, String field) {
        Cache cache = cacheManager.getCache("knowledgeCache");
        String key = "KnowledgeResource.getShuYeQuestionMapByBoxAndTeachBook"+boxId+"_"+teachBookId;
        Map<String, String> questionMap = null;
        if (cache.get(key) != null) {
            questionMap = (Map<String,String>)cache.get(key).get();
        }
        if (questionMap == null || questionMap.size() == 0) {
            questionMap = this.getShuYeQuestionMapByBoxAndTeachBook(teachBookId);
        }
        if (questionMap != null && questionMap.size() > 0) {
            return questionMap.get(field);
        } else {
            return null;
        }
    }


    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + '_' + #teachBookId")
    public Map<String, String> getTiku5QuestionMapByQTypeAndTeachBook( Long teachBookId,Integer QType) {
        if (teachBookId == null) {
            return null;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("teach_book_id", teachBookId);
        params.put("QType",QType);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(GsonUtil.toJson(params), APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionMapByTeachBookAndQType");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType();
                Map<String, String> questionMap = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return questionMap;
            }
        } catch (DataAccessException e) {
            LOG.error("klg_getQuestionMapByBoxAndTeachBook error param:{}", params, e);
        }
        return null;
    }

    public String getTiku5QuestionListStrByQTypeAndTeachBookAndField( Long teachBookId, Integer objType,Integer qType,Long objId) {
        Cache cache = cacheManager.getCache("knowledgeCache");
        String key = "KnowledgeResource.getTiku5QuestionMapByQTypeAndTeachBook"+"_"+teachBookId;
        Map<String, String> questionMap = null;
        if (cache.get(key) != null) {
            questionMap = (Map<String,String>)cache.get(key).get();
        }
        if (questionMap == null || questionMap.size() == 0) {
            questionMap = this.getTiku5QuestionMapByQTypeAndTeachBook(teachBookId,qType);
        }
        if (questionMap != null && questionMap.size() > 0) {
            String itemKey = "";
            if (Consts.Question_Exercise_Type.Chapter.equals(objType)){
                itemKey = NewRedisConsts.getChapterItemQtypeQuestionFiled(objId);
            }else{
                itemKey = NewRedisConsts.getKnowledgeGraphItemQtypeQuestionFiled(objId);
            }

            return questionMap.get(itemKey);
        } else {
            return null;
        }
    }


    public CategoryQuestionInfo klg_getQuestionInfoByCategoryId(Long categoryId) {
        if (categoryId == null || categoryId == 0l) {
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("category_id", categoryId);
        String param = GsonUtil.getGson().toJson(params);
        try {

            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionInfoByCategoryId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<CategoryQuestionInfo>() {}.getType();
                CategoryQuestionInfo categoryQuestionInfo = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return categoryQuestionInfo;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getQuestionInfoByCategoryId] error param:{}", param, e);
        }
        return null;
    }

    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #chaperId")
    public List<KnowledgeGraph> getKnowledgeGraphByChapterItemId(Long chaperId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("chapterId", chaperId);

        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeGraphsByChapterItemId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<KnowledgeGraph>>() {
                }.getType();
                List<KnowledgeGraph> knowledges = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return knowledges;
            }
        } catch (DataAccessException e) {
            LOG.error("[getKnowledgeGraphByChapterItemId] error param:{}", param, e);
        }
        return null;
    }


    public List<ChapterSection> klg_getChapterSectionItemByBookId(Long teachBookId) {

        if (teachBookId == null) {
            return null;
        }
        Map<String, Long> params = Maps.newHashMap();
        params.put("teach_book_id", teachBookId);
        String param = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChapterSectionItemByBookId");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<ChapterSection>>() {}.getType();
                List<ChapterSection> chapterSectionList = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return chapterSectionList;
            }
        } catch (DataAccessException e) {
            LOG.error("[klg_getChapterSectionItemByBookId] error param:{}", param, e);
        }
        return null;
    }


    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #categoryId")
    public List<Questionbox> getQuestionBoxList(Long categoryId) {
        if (Objects.isNull(categoryId)) {
            return Collections.emptyList();
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("category_id", categoryId);

        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionBoxList");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Questionbox>>() {
                }.getType();
                List<Questionbox> boxes = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return boxes;
            }
        } catch (DataAccessException e) {
            LOG.error("[getQuestionBoxList] error param:{}", param, e);
        }
        return Collections.emptyList();
    }

    @Cacheable(value = "knowledgeCache", key = "'KnowledgeResource.'+methodName + #bookId")
    public Map<String,String> getQboxQuestionMap(Long bookId) {
        Map<String,String> result = new HashMap<>();
        if (Objects.isNull(bookId)) {
            return new HashMap<>();
        }

        QuestionBoxQuestionQuery query = new QuestionBoxQuestionQuery();
        query.setTeachBookId(bookId);
        ThriftRequest<QuestionBoxQuestionQuery> request = ThriftClientRequestReady.creatThriftRequest(query);
        try {
            ThriftResponse<Map<String,String>> response = knowledgeClient.getQuestionBoxQuestionMap(request);
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                result = response.getMsg();
            }
        } catch (Exception e) {
            LOG.error("[getQboxQuestionMap] error param:{}", GsonUtil.toJson(query), e);
        }
        return result;
    }


    public Map<Long,Integer> klg_getChaptersQuestionNum(Long bookId,Integer objType,List<Long> chapterIds) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("bookId", bookId);
        map.put("chapterIdList", chapterIds);
        Map<Long, Integer> result = new HashMap<>();
        ThriftClientWrapper<edu100_knowledge.Iface> knowledgeClient = null;
        String param = GsonUtil.getGenericGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getChaptersQuestionNum");
            if (null != response && 0 == response.getCode() && StringUtils.isNotBlank(response.getMsg())) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<Long, Integer>>() {
                }.getType();
                result = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                }
            } catch (Exception e) {
                LOG.error("[klg_getChapterSectionByBookId] error param:{}", param, e);
            }
            return result;

    }

    public List<Paper> klg_getQuestionByPaperIdBatch(List<Long> listIds) {
        List<Paper> result = new ArrayList<>();
        Map<String,Object> params = Maps.newHashMap();
        params.put("id",listIds.toArray());
        String paramString = GsonUtils.toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(paramString, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getQuestionByPaperIdBatch");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<List<Paper>>(){}.getType();
                result = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                return result;
            } else {
                LOG.error("klg_getQuestionByPaperIdBatch param:{} errcode:{} errMsg:{}", GsonUtils.toDefaultJson(paramString),
                        response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error("klg_getQuestionByPaperIdBatch failed param: {} ,error: {}" + GsonUtils.toDefaultJson(paramString), e);
        }
        return null;
    }

    public List<KnowledgeQuestionListDTO> getKnowledgeQuestionListByQuestionIds(List<Long> questionIds) {
        List<KnowledgeQuestionListDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(questionIds)) {
            return result;
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("questionIds", questionIds);
        String param = GsonUtil.getGson().toJson(map);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID,
                    Consts.Code.CLIENT_IP, "klg_getKnowledgeQuestionListByQuestionIds");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<KnowledgeQuestionListDTO>>() {
                }.getType();
                List<KnowledgeQuestionListDTO> knowledgeQuestionListDTOList= GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                result.addAll(knowledgeQuestionListDTOList);
            }
        } catch (DataAccessException e) {
            LOG.error("[getKnowledgeQuestionListByQuestionIds] error param:{}", param, e);
        }
        return result;
    }

    public List<EvaluationBaseQuestionDTO> getEvaluationBaseQuestionList(EvaluationBaseQuestionQuery query) {
        List<EvaluationBaseQuestionDTO> result = null;
        if (query == null || query.getSecondCategory() == null) {
            return result;
        }
        String param = GsonUtil.getGenericGson().toJson(query);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke(param, APP_ID, Consts.Code.CLIENT_IP, "klg_getEvaluationBaseQuestionList");
            if (null != response && ThriftReturnCode.SUCCESS.getType() == response.getCode()) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<EvaluationBaseQuestionDTO>>() {
                }.getType();
                result = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
            }
        } catch (DataAccessException e) {
            LOG.error("[getEvaluationBaseQuestionList] error param:{}", param, e);
        }
        return result;
    }

    public ResourceVideo getReturnResourceVideoById(Long id) {
        ResourceVideo result = null;

        Map<String, Object> params = Maps.newHashMap();
        params.put("id", id);
        String paramString =  GsonUtils.toJson(params);
        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke
                    (paramString, APP_ID, Consts.Code.CLIENT_IP, "klg_getResourceVideoById");

            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<ResourceVideo>(){}.getType();
                result = GsonUtil.getGson().fromJson(response.getMsg(), type);
            } else {
                LOG.error("klg_getResourceVideoById param:{} errcode:{} errMsg:{}", GsonUtils.toDefaultJson(params),
                        response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error("klg_getResourceVideoById failed param: {} = " + GsonUtils.toDefaultJson(params), e);
        }
        return result;
    }

    public List<ResourceLive> getResourceLiveByIdList(List<Long> liveIdList) {
        LOG.debug("[getResourceLiveByIdList] start, param :{}", GsonUtil.toJson(liveIdList));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(liveIdList)) {
            return Collections.emptyList();
        }
        List<ResourceLive> results = new ArrayList<>();
        Map<String, Object> params = Maps.newHashMap();
        params.put("id_list", liveIdList);
        String paramStr = GsonUtil.getGson().toJson(params);

        List<ResourceLive> dbResults= null;

        try {
            response response = genericThriftResource.generalKnowledgeThriftMethodInvoke
                    (paramStr, APP_ID, Consts.Code.CLIENT_IP, "klg_getResourceLiveByIdList");

            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<List<ResourceLive>>(){}.getType();
                dbResults = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
            }
        } catch (Exception e) {
            LOG.error("[klg_getResourceLiveByIdList] error param:{}", paramStr, e);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dbResults)) {
            results.addAll(dbResults);
        }

        return results;
    }
    public List<ChapterSectionItemDTO> getChapterTreeWithKnowledge(Long categoryId, List<Long> questionIdList) {
        return knowledgeGrpcClient.getChapterTreeWithKnowledge(categoryId, questionIdList);
    }

}
