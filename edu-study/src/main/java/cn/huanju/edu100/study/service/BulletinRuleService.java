package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.BulletinRule;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Set;

/**
 * 公告规则Service
 *
 * <AUTHOR>
 * @version 2016-05-23
 */
public interface BulletinRuleService extends BaseService<BulletinRule> {

    List<BulletinRule> findListByParam(Set<Long> groupIdSet, BulletinRule bulletinRule) throws DataAccessException;
}
