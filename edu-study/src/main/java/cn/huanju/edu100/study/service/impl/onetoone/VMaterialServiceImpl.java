package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.VMaterialDao;
import cn.huanju.edu100.study.model.onetoone.VMaterial;
import cn.huanju.edu100.study.service.onetoone.VMaterialService;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 留学资料Service
 * <AUTHOR>
 * @version 2016-12-12
 */
@Service
public class VMaterialServiceImpl extends BaseServiceImpl<VMaterialDao, VMaterial> implements VMaterialService {

    @Override
    public List<VMaterial> findListByParam(Map<String, Object> params) throws DataAccessException {

        if (MapUtils.isEmpty(params)) {
            logger.error("findListByParam fail, emtpy params");
            throw new DataAccessException("empty param map");
        }

        return dao.findListByParam(params);
    }

    @Override
    public boolean insertBatch(List<VMaterial> vMaterials) throws DataAccessException {

        if (CollectionUtils.isEmpty(vMaterials)) {
            logger.error("insertBatch fail, emtpy vMaterials");
            throw new DataAccessException("insertBatch fail, emtpy vMaterials");
        }


        return dao.insertBatch(vMaterials);
    }

}
