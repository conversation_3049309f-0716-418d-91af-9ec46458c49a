package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorStudentTaskCountDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentTaskCount;
import cn.huanju.edu100.study.service.tutor.TutorStudentTaskCountService;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 任务统计Service
 * <AUTHOR>
 * @version 2016-01-18
 */
@Service
public class TutorStudentTaskCountServiceImpl extends BaseServiceImpl<TutorStudentTaskCountDao, TutorStudentTaskCount> implements TutorStudentTaskCountService {

    @Override
    public Double getStudyProgressByUid(TutorStudentTaskCount params) throws DataAccessException {

        if (params == null || params.getUid() == null || StringUtils.isBlank(params.getClasses())
                || (params.getPlanId() == null && params.getPhaseId() == null)) {
            return 0.0d;
        }

        List<TutorStudentTaskCount> tutorStudentTaskCounts = dao.findList(params);
        if (CollectionUtils.isEmpty(tutorStudentTaskCounts)) {
            return 0.0d;
        }

        TutorStudentTaskCount taskCount = tutorStudentTaskCounts.get(0);
        if (taskCount.getCount() == null || taskCount.getCount() == 0
                || taskCount.getComplete() == null || taskCount.getComplete() == 0) {
            return 0.0d;
        }

        params.setUid(null);
        params.setComplete(taskCount.getComplete());
        Integer lessCount = dao.getStudentTaskCount(params);
        params.setComplete(null);
        Integer allCount = dao.getStudentTaskCount(params);
        if (lessCount == null || allCount == null || allCount == 0) {
            return 0.0d;
        }

        BigDecimal bg = BigDecimal.valueOf(lessCount * 1.0 / allCount);
        return bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

}
