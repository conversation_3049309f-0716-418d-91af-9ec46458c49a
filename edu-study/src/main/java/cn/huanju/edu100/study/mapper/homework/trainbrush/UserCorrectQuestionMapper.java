//package cn.huanju.edu100.study.mapper.homework.trainbrush;
//
//import cn.huanju.edu100.study.model.homework.trainbrush.UserCorrectQuestion;
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import org.apache.ibatis.annotations.Insert;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//import org.springframework.stereotype.Repository;
//
//@Repository
//@Mapper
//@DS("al-default-ds")
//public interface UserCorrectQuestionMapper extends BaseMapper<UserCorrectQuestion> {
//
//    @Insert({"""
//            <script>
//            insert into user_correct_question
//            ( id,uid,product_id,goods_id,category_id,group_id,task_id,question_id,topic_id,qtype,answer_id,last_answer_id,create_date,update_date )
//            values
//            (
//            #{userCorrectQuestion.id},
//            #{userCorrectQuestion.uid},
//            #{userCorrectQuestion.productId},
//            #{userCorrectQuestion.goodsId},
//            #{userCorrectQuestion.categoryId},
//            #{userCorrectQuestion.groupId},
//            #{userCorrectQuestion.taskId},
//            #{userCorrectQuestion.questionId},
//            #{userCorrectQuestion.topicId},
//            #{userCorrectQuestion.qtype},
//            #{userCorrectQuestion.answerId},
//            #{userCorrectQuestion.lastAnswerId},
//            #{userCorrectQuestion.createDate},
//            #{userCorrectQuestion.updateDate}
//            )
//            on duplicate key update last_answer_id=values(last_answer_id)
//            </script>
//            """})
//    void saveOrUpdate(@Param("userCorrectQuestion")UserCorrectQuestion userCorrectQuestion);
//}
