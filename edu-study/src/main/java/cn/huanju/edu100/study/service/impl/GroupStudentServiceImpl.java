package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.GroupStudentDao;
import cn.huanju.edu100.study.model.GroupStudent;
import cn.huanju.edu100.study.service.GroupStudentService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

/**
 * 分组学生Service
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
@Service
public class GroupStudentServiceImpl extends BaseServiceImpl<GroupStudentDao, GroupStudent> implements
        GroupStudentService {

    @Override
    public boolean updateType(Long groupId, Long suid, Integer type) throws DataAccessException {
        return dao.updateType(groupId, suid, type);
    }

    @Override
    public GroupStudent qryGroupStudentByGroupIdAndUid(Long groupId, Long uid) throws DataAccessException {
        return dao.qryGroupStudentByGroupIdAndUid(groupId, uid);
    }

}
