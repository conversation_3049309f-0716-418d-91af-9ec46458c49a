/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.tutor.TutorSectionKnowledge;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 章节知识点DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public interface TutorSectionKnowledgeDao extends CrudDao<TutorSectionKnowledge> {

    List<CountModel> listSectionKnowNums(List<Long> sectionIdList) throws DataAccessException;
}