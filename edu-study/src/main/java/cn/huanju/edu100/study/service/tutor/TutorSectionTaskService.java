package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 章节任务表Service
 * <AUTHOR>
 * @version 2017-12-28
 */
public interface TutorSectionTaskService extends BaseService<TutorSectionTask> {

    Map<Long, Integer> getTaskNum(List<Long> sectionIdList, Integer type) throws BusinessException, DataAccessException;

    Map<Long, Integer> getWKTaskNum(List<Long> weikeIdList, Integer type) throws BusinessException, DataAccessException;

    List<TutorSectionTask> findListByParam(TutorSectionTask search) throws BusinessException, DataAccessException;
}