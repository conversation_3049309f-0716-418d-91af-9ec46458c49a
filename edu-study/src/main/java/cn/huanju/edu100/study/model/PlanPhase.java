package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 计划阶段Entity
 * <AUTHOR>
 * @version 2015-05-15
 */
public class PlanPhase extends DataEntity<PlanPhase> {

	private static final long serialVersionUID = 1L;
	private Long planId;		// plan_id
	private String name;		// name
	private Date startTime;		// start_time
	private Date endTime;		// end_time

	public PlanPhase() {
		super();
	}

	public PlanPhase(Long id){
		super(id);
	}

	public Long getPlanId() {
		return planId;
	}

	public void setPlanId(Long planId) {
		this.planId = planId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

}
