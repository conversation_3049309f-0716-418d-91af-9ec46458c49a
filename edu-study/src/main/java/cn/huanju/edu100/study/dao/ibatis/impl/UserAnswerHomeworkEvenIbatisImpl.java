package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserAnswerHomeworkEvenDao;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.*;

public class UserAnswerHomeworkEvenIbatisImpl extends CrudIbatisImpl2<UserHomeWorkAnswer> implements UserAnswerHomeworkEvenDao {

	Logger logger = LoggerFactory.getLogger(UserAnswerHomeworkEvenIbatisImpl.class);

	public UserAnswerHomeworkEvenIbatisImpl() {
		super("UserAnswerHomeworkEven");
	}

	@Override
	public int updateByParam(Map<String, Object> param) throws DataAccessException {
		if (null == param.get("tbIndex") || null == param.get("idList") || null == param.get("homeworkId")) {
			String errorInfo = "illegal param, tbIndex or idList or homeworkId is null";
			logger.error(errorInfo);
			throw new DataAccessException(errorInfo);
		}
		try {
			SqlMapClient sqlMap = super.getShardingMaster();
			return sqlMap.update(namespace + ".updateByParam", param);
		} catch (SQLException e) {
			logger.error("updateByParam SQLException.param:{}", param, e);
			throw new DataAccessException("updateByParam SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<UserHomeWorkAnswer> selectByParam(Map<String, Object> param) throws DataAccessException {
		if (null == param.get("tbIndex") || null == param.get("objId") || null == param.get("productId")) {
			String errorInfo = "illegal param, tbIndex or objId or productId is null";
			logger.error(errorInfo);
			throw new DataAccessException(errorInfo);
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			List<UserHomeWorkAnswer> list = (List<UserHomeWorkAnswer>) sqlMap.queryForList(namespace + ".selectByParam", param);
			return list;
		} catch (SQLException e) {
			logger.error("selectByParam SQLException.param:{}", param, e);
			throw new DataAccessException("selectByParam SQLException error");
		}
	}

	@Override
	public int updateHomeworkIdByUid(Map<String, Object> param) throws DataAccessException {
		if (null == param.get("id") || null == param.get("uid") || null == param.get("homeworkId")) {
			String errorInfo = "illegal param, id or uid or homeworkId is null";
			logger.error(errorInfo);
			throw new DataAccessException(errorInfo);
		}
		try {
			SqlMapClient sqlMap = super.getShardingMaster();
			return sqlMap.update(namespace + ".updateHomeworkIdByUid", param);
		} catch (SQLException e) {
			logger.error("updateHomeworkIdByUid SQLException.param:{}", param, e);
			throw new DataAccessException("updateHomeworkIdByUid SQLException error" + e.getMessage());
		}
	}

	@Override
	public UserHomeWorkAnswer getMaxIdByTbIndex(Integer tbIndex) throws DataAccessException {
		if (null == tbIndex) {
			String errorInfo = "illegal param, tbIndex is null";
			logger.error(errorInfo);
			throw new DataAccessException(errorInfo);
		}
		try {
			Map<String, Object> param = Maps.newHashMap();
			param.put("tbIndex", tbIndex);
			param.put("uid", tbIndex);
			SqlMapClient sqlMap = super.getSlave();
			UserHomeWorkAnswer userHomeWorkAnswer = (UserHomeWorkAnswer) sqlMap.queryForObject(namespace + ".getMaxIdByTbIndex", param);
			return userHomeWorkAnswer;
		} catch (SQLException e) {
			logger.error("getMaxIdByTbIndex SQLException.tbIndex:{}", tbIndex, e);
			throw new DataAccessException("getMaxIdByTbIndex SQLException error");
		}
	}

	@Override
	public Integer selectCountByMaxId(Integer tbIndex, Long id) throws DataAccessException {
		if (null == tbIndex || null == id) {
			String errorInfo = "illegal param, tbIndex or id is null";
			logger.error(errorInfo);
			throw new DataAccessException(errorInfo);
		}
		try {
			Map<String, Object> param = Maps.newHashMap();
			param.put("tbIndex", tbIndex);
			param.put("uid", tbIndex);
			param.put("id", id);
			SqlMapClient sqlMap = super.getSlave();
			Integer total = (Integer) sqlMap.queryForObject(namespace + ".selectCountByMaxId", param);
			return total;
		} catch (SQLException e) {
			logger.error("selectCountByMaxId SQLException.tbIndex:{},id:{}", tbIndex, id, e);
			throw new DataAccessException("selectCountByMaxId SQLException error");
		}
	}

	@Override
	public List<UserHomeWorkAnswer> selectListByMaxId(Integer tbIndex, Long id, int from, int rows) throws DataAccessException {
		if (null == tbIndex || null == id) {
			String errorInfo = "illegal param, tbIndex or id is null";
			logger.error(errorInfo);
			throw new DataAccessException(errorInfo);
		}
		try {
			Map<String, Object> param = Maps.newHashMap();
			param.put("tbIndex", tbIndex);
			param.put("id", id);
			param.put("uid", tbIndex);
			param.put("from", from);
			param.put("rows", rows);
			SqlMapClient sqlMap = super.getSlave();
			List<UserHomeWorkAnswer> userHomeWorkAnswerList = (List<UserHomeWorkAnswer>) sqlMap.queryForList(namespace + ".selectListByMaxId", param);
			return userHomeWorkAnswerList;
		} catch (SQLException e) {
			logger.error("selectListByMaxId SQLException.tbIndex:{},id:{}", tbIndex, id, e);
			throw new DataAccessException("selectListByMaxId SQLException error");
		}
	}

}
