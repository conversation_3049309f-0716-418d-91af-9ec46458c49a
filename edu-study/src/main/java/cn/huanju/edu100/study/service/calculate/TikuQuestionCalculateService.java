package cn.huanju.edu100.study.service.calculate;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.calculate.TikuQuestionCalculate;
import cn.huanju.edu100.exception.DataAccessException;

public interface TikuQuestionCalculateService extends BaseService<TikuQuestionCalculate> {

    TikuQuestionCalculate getByUidSecondCategoryId(Long uid,Long secondCategoryId) throws DataAccessException;


}
