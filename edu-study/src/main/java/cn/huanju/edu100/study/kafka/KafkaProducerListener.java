package cn.huanju.edu100.study.kafka;

/**
 * Created by lynn on 2017/11/15.
 */

import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.support.ProducerListener;

/**
 * kafkaProducer监听器，在producer配置文件中开启
 * <AUTHOR>
 *
 */
@SuppressWarnings("rawtypes")
public class KafkaProducerListener implements ProducerListener {
    protected final Logger LOG = LoggerFactory.getLogger(KafkaProducerListener.class);
    /**
     * 发送消息成功后调用
     */
//    @Override
    public void onSuccess(String topic, Integer partition, Object key,
                          Object value, RecordMetadata recordMetadata) {
        LOG.info("send suc---topic:"+topic + ",value:"+value + ", key:" + key  + ",partition:" + partition);
    }

    /**
     * 发送消息错误后调用
     */
//    @Override
    public void onError(String topic, Integer partition, Object key,
                        Object value, Exception exception) {
        LOG.error("send error---topic:"+topic + ",value:"+value + ", key:" + key  + ",partition:" + partition,exception);
    }

    /**
     * 方法返回值代表是否启动kafkaProducer监听器
     */
//    @Override
    public boolean isInterestedInSuccess() {
//        LOG.info("///kafkaProducer监听器启动///");
        return true;
    }

}