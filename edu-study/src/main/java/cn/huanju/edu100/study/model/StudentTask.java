package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 学生学习任务Entity
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudentTask extends DataEntity<StudentTask> {

	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long taskId;		// 任务id
	private Integer state;		// 0：未提交，1：部分完成，2：已提交
	private Long creator;		// 修改者
	private Date createTime;		// create_time
	private Date modifyTime;		// modify_time

	public StudentTask() {
		super();
	}

	public StudentTask(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Long getCreator() {
		return creator;
	}

	public void setCreator(Long creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	@Override
	public String toString() {
		return "StudentTask [uid=" + uid + ", taskId=" + taskId + ", state="
				+ state + ", creator=" + creator + ", createTime=" + createTime
				+ ", modifyTime=" + modifyTime + "]";
	}

}
