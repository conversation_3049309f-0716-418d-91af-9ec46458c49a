package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.PaperStudyDao;
import cn.huanju.edu100.study.event.PaperSubmittedEvent;
import cn.huanju.edu100.study.model.Paper;
import cn.huanju.edu100.study.model.PaperStudy;
import cn.huanju.edu100.study.model.PaperSubmitStatisticInfo;
import cn.huanju.edu100.study.repository.PaperSubmitCompareInfoRepository;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.PaperStudyService;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.util.PaperSubmitStatisticRule;
import cn.huanju.edu100.util.JSONUtils;
import com.hqwx.study.dto.PaperSubmitCompareInfo;
import com.hqwx.study.entity.UserAnswer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PaperStudyServiceImpl extends BaseServiceImpl<PaperStudyDao, PaperStudy> implements PaperStudyService {
	@Override
	public List<PaperStudy> findListByTaskIdList(List<Long> taskIdList)	throws DataAccessException {
		return dao.findListByTaskIdList(taskIdList);
	}
}
