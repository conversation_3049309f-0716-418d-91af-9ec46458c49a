/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorStudentKnowledgeDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentKnowledge;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 学员知识点学习情况DAO接口
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
public class TutorStudentKnowledgeIbatisImpl extends CrudIbatisImpl2<TutorStudentKnowledge> implements
        TutorStudentKnowledgeDao {

    public TutorStudentKnowledgeIbatisImpl() {
        super("TutorStudentKnowledge");
    }

    @Override
    public List<TutorStudentKnowledge> getTutorStudentKnowledge(Map<String, Object> params)
            throws DataAccessException {

        if (params == null) {
            return null;
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorStudentKnowledge>) sqlMap
                    .queryForList("TutorStudentKnowledge.findList", params);
        } catch (SQLException e) {
            logger.error("getTutorStudentKnowledge SQLException.", e);
            throw new DataAccessException("getTutorStudentKnowledge SQLException error");
        }
    }

}
