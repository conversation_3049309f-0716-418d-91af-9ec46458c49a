/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.onetoone.VFeedbackDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.onetoone.VFeedback;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 留学反馈/报告DAO接口
 * <AUTHOR>
 * @version 2016-12-12
 */
public class VFeedbackIbatisImpl extends CrudIbatisImpl2<VFeedback> implements
		VFeedbackDao {

	public VFeedbackIbatisImpl() {
		super("VFeedback");
	}

    @Override
    public List<VFeedback> findListByParam(Map<String, Object> params) throws DataAccessException {
        if (MapUtils.isEmpty(params)) {
            logger.error("findListByParam fail, empty params map");
            throw new DataAccessException("findListByParam fail, empty params map");
        }

        try {
            Map<String, Object> map = Maps.newHashMap();
            if (null != params.get("type")) {
                map.put("type", params.get("type"));
            }
            if (null != params.get("id")) {
                map.put("id", params.get("id"));
            }
            if (null != params.get("lessonId")) {
                map.put("vLessonId", params.get("lessonId"));
            }
            if (null != params.get("uid")) {
                map.put("uid", params.get("uid"));
            }
            if (null != params.get("from")) {
                map.put("from", params.get("from"));
                map.put("pageSize", params.get("rows"));
            }
            if (null != params.get("vClsId")) {
                map.put("vClsId", params.get("vClsId"));
            }
            if (null != params.get("vLessonId")) {
                map.put("vLessonId", params.get("vLessonId"));
            }
            if (null != params.get("uidList")) {
                map.put("uidList", params.get("uidList"));
            }
            if (null != params.get("maxId")) {
                map.put("maxId", params.get("maxId"));
            }
            if (null != params.get("orderBy")) {
                map.put("orderBy", params.get("orderBy"));
            }
            if (null != params.get("sendRange")) {
                map.put("sendRange", params.get("sendRange"));
            }
            if (null != params.get("startTime")) {
                map.put("startTime", params.get("startTime"));
            }
            if (null != params.get("endTime")) {
                map.put("endTime", params.get("endTime"));
            }
            SqlMapClient sqlMap = super.getSlave();
            return sqlMap.queryForList(namespace.concat(".findList"), map);
        } catch (SQLException e) {
            logger.error("findListByParam {} SQLException.entity:{}", namespace, GsonUtil.toJson(params), e);
            throw new DataAccessException("findListByParam SQLException error" + e.getMessage());
        }
    }

    @Override
    public Integer findListCountByParam(Map<String, Object> params) throws DataAccessException {
        if (MapUtils.isEmpty(params)) {
            logger.error("findListCountByParam fail, empty params map");
            throw new DataAccessException("findListCountByParam fail, empty params map");
        }

        try {
            Map<String, Object> map = Maps.newHashMap();
            if (null != params.get("type")) {
                map.put("type", params.get("type"));
            }
            if (null != params.get("uid")) {
                map.put("uid", params.get("uid"));
            }
//            if (null != params.get("from")) {
//                map.put("from", params.get("from"));
//                map.put("pageSize", params.get("rows"));
//            }
            if (null != params.get("vClsId")) {
                map.put("vClsId", params.get("vClsId"));
            }
            if (null != params.get("vLessonId")) {
                map.put("vLessonId", params.get("vLessonId"));
            }
            if (null != params.get("uidList")) {
                map.put("uidList", params.get("uidList"));
            }
            if (null != params.get("sendRange")) {
                map.put("sendRange", params.get("sendRange"));
            }
            SqlMapClient sqlMap = super.getSlave();
            Integer ret = (Integer) sqlMap.queryForObject(namespace.concat(".findListCount"), params);
            return ret;
        } catch (SQLException e) {
            logger.error("findListByParam {} SQLException.entity:{}", namespace, GsonUtil.toJson(params), e);
            throw new DataAccessException("findListByParam SQLException error" + e.getMessage());
        }
    }


    @Override
    public boolean insertBatch(Collection<VFeedback> feedbacks) throws DataAccessException {
        if (CollectionUtils.isEmpty(feedbacks)) {
            logger.error("batchInsert get error, parameter is empty");
            throw new DataAccessException("insertBatch get error, parameter is empty");
        }
        try {
            SqlMapClient sqlMap = super.getMaster();
            sqlMap.insert(namespace.concat(".insertBatch"), feedbacks);
            return true;
        } catch (SQLException e) {
            logger.error("batchInsert SQLException.", e);
            throw new DataAccessException("insertBatch get SQLException error" + e.getMessage());
        } catch (Exception e) {
            logger.error("get SException.", e);
            throw new DataAccessException("insertBatch get Exception error" + e.getMessage());
        }
    }

    @Override
    public boolean updateBatch(List<VFeedback> vFeedbacks) throws DataAccessException {

        if ( CollectionUtils.isEmpty(vFeedbacks)) {
            logger.error("updateBatch fail, vFeedbacks is empty");
            throw new DataAccessException("updateBatch fail, vFeedbacks is empty");
        }

        try {
            SqlMapClient sqlMap = super.getMaster();
//            Map<String, Object> param = Maps.newHashMap();
//            param.put("idList", idList);
//            if (StringUtils.isNotBlank(vFeedbacks.getContent())) {
//                param.put("content", vFeedbacks.getContent());
//            }
//            if (IdUtils.isValid(vFeedbacks.getUpdateBy())) {
//                param.put("updateBy", vFeedbacks.getUpdateBy());
//            }
            int row = sqlMap.update(namespace.concat(".updateBatch"), vFeedbacks);
            return 1 <= row;
        } catch (SQLException e) {
            logger.error("updateBatch SQLException.", e);
            throw new DataAccessException("updateBatch get SQLException error" + e.getMessage());
        } catch (Exception e) {
            logger.error("updateBatch SException.", e);
            throw new DataAccessException("updateBatch get Exception error" + e.getMessage());
        }
    }


    @Override
    public List<CountModel> listFeedbackCountByLessonIds(List<Long> lessonIds, Integer type) throws DataAccessException {

        if (null == type || CollectionUtils.isEmpty(lessonIds)) {
            logger.error("listFeedbackCountByLessonIds fail, empty clsIsd or type is null, lessonIds :{}, type :{}",
                    lessonIds, type);
            throw new DataAccessException("listFeedbackCountByLessonIds fail, empty clsIsd or type is null");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> map = Maps.newHashMap();
            map.put("vLessonIds", lessonIds);
            map.put("type", type);
            return sqlMap.queryForList(namespace.concat(".listFeedbackCountByLessonIds"), map);
        } catch (SQLException e) {
            logger.error("listFeedbackCountByLessonIds {} SQLException. queryParam:{}", namespace, GsonUtil.toJson(lessonIds), e);
            throw new DataAccessException(String.format("listFeedbackCountByLessonIds SQLException error :%s", e.getMessage()));
        }
    }

}
