package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 学生学习任务Entity
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudyLog extends DataEntity<StudyLog> {

    private static final long serialVersionUID = 1L;

    public Long groupId;
    public Long planId;
    public Long phaseId;
    public Long taskId;
    public Long categoryId;
    public Long stuTaskId; // 学生任务id
    public Long uid; // 学生ID
    public Integer type; // 类型状态 0：主动学习 1：推送学习
    public Integer answerType; // 状态 0：讲作业 1：段落作业 2：微课作业 3：试卷 4：测评
    public Long knowledgeId; //
    public Integer answerNum; //
    public String answerIds;
    public Integer wrongNum; //
    public String wrongIds; //
    public Integer studyDuration; // 学习时长
    public Date create_time; // 创建时间

    public StudyLog() {
        super();
    }

    public StudyLog(Long id) {
        super(id);
    }

    public Long getStuTaskId() {
        return stuTaskId;
    }

    public void setStuTaskId(Long stuTaskId) {
        this.stuTaskId = stuTaskId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(Long knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public Integer getAnswerNum() {
        return answerNum;
    }

    public void setAnswerNum(Integer answerNum) {
        this.answerNum = answerNum;
    }

    public String getAnswerIds() {
        return answerIds;
    }

    public void setAnswerIds(String answerIds) {
        this.answerIds = answerIds;
    }

    public Integer getWrongNum() {
        return wrongNum;
    }

    public void setWrongNum(Integer wrongNum) {
        this.wrongNum = wrongNum;
    }

    public String getWrongIds() {
        return wrongIds;
    }

    public void setWrongIds(String wrongIds) {
        this.wrongIds = wrongIds;
    }

    public Integer getStudyDuration() {
        return studyDuration;
    }

    public void setStudyDuration(Integer studyDuration) {
        this.studyDuration = studyDuration;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public Integer getAnswerType() {
        return answerType;
    }

    public void setAnswerType(Integer answerType) {
        this.answerType = answerType;
    }

}
