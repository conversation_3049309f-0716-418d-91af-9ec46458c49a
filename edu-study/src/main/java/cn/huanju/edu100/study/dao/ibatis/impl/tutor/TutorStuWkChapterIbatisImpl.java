/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorStuWkChapterDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.study.model.tutor.TutorStuWkChapter;
import cn.huanju.edu100.study.util.CountUtils;
import cn.huanju.edu100.study.util.IdUtils;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 学生对微课班章节任务完成情况DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public class TutorStuWkChapterIbatisImpl extends CrudIbatisImpl2<TutorStuWkChapter> implements
		TutorStuWkChapterDao {

	public TutorStuWkChapterIbatisImpl() {
		super("TutorStuWkChapter");
	}

	@Override
	public List<CountModel> listStuKnowNum(Long uid, List<Long> sectionIdList) throws DataAccessException {

		if (!IdUtils.isValid(uid) || CollectionUtils.isEmpty(sectionIdList)) {
			logger.error("illegal param, uid or sectionIdList is null");
			throw new DataAccessException("illegal param, uid or sectionIdList is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("uid", uid);
			param.put("sectionIdList", sectionIdList);
			List<CountModel>  resutlLit = sqlMap.queryForList(namespace.concat(".listStuKnowNum"), param);
			return resutlLit;
		} catch (SQLException e) {
			logger.error("listStuKnowNum {} SQLException. uid:{}, sectionIdList:{}", namespace, uid, sectionIdList, e);
			throw new DataAccessException("listStuKnowNum SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<CountModel> listStuTaskStatus(Long uid, Integer type, List<Long> sectionIdList) throws DataAccessException {

		if (null == type || !IdUtils.isValid(uid) || CollectionUtils.isEmpty(sectionIdList)) {
			logger.error("illegal param, uid or sectionIdList or type is null");
			throw new DataAccessException("illegal param, uid or sectionIdList or type is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("uid", uid);
			param.put("type", type);
			param.put("sectionIdList", sectionIdList);
			List<CountModel>  resutlLit = sqlMap.queryForList(namespace.concat(".listStuTaskStatus"), param);
			return resutlLit;
		} catch (SQLException e) {
			logger.error("listStuTaskStatus {} SQLException. uid:{}, type:{}, sectionIdList:{}", namespace, uid, type, sectionIdList, e);
			throw new DataAccessException("listStuTaskStatus SQLException error" + e.getMessage());
		}
	}

	@Override
	public boolean updateStatus(long id, Long uid, Integer status) throws DataAccessException {
		if (!IdUtils.isValid(id) || !IdUtils.isValid(uid) || null == status) {
			logger.error("illegal param, taskId or uid or status is null");
			throw new DataAccessException("illegal param, taskId or uid or status is null");
		}


		try {
			SqlMapClient sqlMap = super.getMaster();
			Map<String, Object> param = Maps.newHashMap();
			param.put("id", id);
			param.put("uid", uid);
			param.put("status", status);
			int row = sqlMap.update(namespace.concat(".updateStatus"), param);
			return row >= 1;
		} catch (SQLException e) {
			logger.error("updateStatus {} SQLException.taskId:{}, uid:{}, status :{}", namespace, id, uid, status, e);
			throw new DataAccessException("updateStatus " + namespace + " SQLException fail.");
		}
	}

	@Override
	public TutorSectionTask getWkLastTask(Long uid, Long weikeId, Long sectionId, Long phaseId, Long unitId, Long secondCategory, Long categoryId, Integer type) throws DataAccessException {
		if (!IdUtils.isValid(uid)) {
			logger.error("illegal param, uid is null");
			throw new DataAccessException("illegal param, uid is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("uid", uid);
			param.put("weikeId", weikeId);
			param.put("sectionId", sectionId);
			param.put("phaseId", phaseId);
			param.put("unitId", unitId);
			param.put("secondCategory", secondCategory);
			param.put("categoryId", categoryId);
			param.put("type", type);
			TutorSectionTask task = (TutorSectionTask) sqlMap.queryForObject(namespace.concat(".getWkLastTask"), param);
			return task;
		} catch (SQLException e) {
			logger.error("getWkLastTask {} SQLException.uid:{}, weikeId:{}, sectionId :{}, phaseId :{}, unitId :{}, secondCategory :{}, categoryId :{}, type :{}",
					namespace, uid, weikeId, sectionId, phaseId, unitId, secondCategory, categoryId, type, e);
			throw new DataAccessException("getWkLastTask " + namespace + " SQLException fail." + e.getMessage());
		}
	}

	@Override
	public Map<Long, Integer> getUnitTaskNum(List<Long> unitIdList, Long uid, Integer type) throws DataAccessException {
		if (CollectionUtils.isEmpty(unitIdList) || !IdUtils.isValid(uid)) {
			logger.error("illegal param, unitIdList or uid is null");
			throw new DataAccessException("illegal param, unitIdList or uid is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("unitIdList", unitIdList);
			param.put("type", type);
			param.put("uid", uid);
			param.put("status", 2);
			List<CountModel>  resutlLit = sqlMap.queryForList(namespace.concat(".listUnitTaskNum"), param);
			return CountUtils.getCountMap(resutlLit);
		} catch (SQLException e) {
			logger.error("listUnitTaskNum {} SQLException.unitIdList:{}, type:{}, uid :{}",
					namespace, unitIdList, type, uid, e);
			throw new DataAccessException("listUnitTaskNum " + namespace + " SQLException fail." + e.getMessage());
		}
	}
}
