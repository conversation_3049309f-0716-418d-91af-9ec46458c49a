package cn.huanju.edu100.study.model.onetoone;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 学员班级订单明细Entity
 * <AUTHOR>
 * @version 2016-09-01
 */
public class VClassesStudentOrder extends DataEntity<VClassesStudentOrder> {
	
	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long clsId;		// cls_id
	private Long orderId;		// order_id
	private Long goodsId;		// 商品id
	private Long productId;		// 产品id
	private Integer status;		// status
	private Float lessonCount;		// lesson_count
	private Float completeCount;		// complete_count
	
	public VClassesStudentOrder() {
		super();
	}

	public VClassesStudentOrder(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public Long getClsId() {
		return clsId;
	}

	public void setClsId(Long clsId) {
		this.clsId = clsId;
	}
	
	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Float getLessonCount() {
		return lessonCount;
	}

	public void setLessonCount(Float lessonCount) {
		this.lessonCount = lessonCount;
	}
	
	public Float getCompleteCount() {
		return completeCount;
	}

	public void setCompleteCount(Float completeCount) {
		this.completeCount = completeCount;
	}

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }
	
}