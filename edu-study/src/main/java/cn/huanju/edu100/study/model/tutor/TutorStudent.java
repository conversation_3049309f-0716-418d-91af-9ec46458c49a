package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 个性化学员Entity
 * 
 * <AUTHOR>
 * @version 2016-01-12
 */
public class TutorStudent extends DataEntity<TutorStudent> {

    private static final long serialVersionUID = 1L;
    private Long uid; // uid
    private String classes; // classes
    private Long area; // area
    private Integer type; // type 0表示云私塾； 1表示助学服务
    private String expFollowDate; // exp_follow_date
    private String lastFollowDate; // last_follow_date
    private Long teacherId; // teacher_id
    private Integer status; // status
    private String ip; // ip

    public TutorStudent() {
        super();
    }

    public TutorStudent(Long id) {
        super(id);
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

    public Long getArea() {
        return area;
    }

    public void setArea(Long area) {
        this.area = area;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getExpFollowDate() {
        return expFollowDate;
    }

    public void setExpFollowDate(String expFollowDate) {
        this.expFollowDate = expFollowDate;
    }

    public String getLastFollowDate() {
        return lastFollowDate;
    }

    public void setLastFollowDate(String lastFollowDate) {
        this.lastFollowDate = lastFollowDate;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

}