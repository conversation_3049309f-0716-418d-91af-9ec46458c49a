package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorWeikeClass;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 微课班Service
 * <AUTHOR>
 * @version 2017-12-28
 */
public interface TutorWeikeClassService extends BaseService<TutorWeikeClass> {

    List<TutorWeikeClass> listByIds(List<Long> idList) throws BusinessException, DataAccessException;
}