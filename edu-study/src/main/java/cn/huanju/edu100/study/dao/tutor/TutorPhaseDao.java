/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorPhase;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 个性化阶段DAO接口
 * <AUTHOR>
 * @version 2016-01-14
 */
public interface TutorPhaseDao extends CrudDao<TutorPhase> {

    List<TutorPhase> listPhases(Map<String, Object> params) throws DataAccessException;

}
