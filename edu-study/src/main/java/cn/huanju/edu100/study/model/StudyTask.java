package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 学习任务Entity
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudyTask extends DataEntity<StudyTask> {

    private static final long serialVersionUID = 1L;
    private Long planId; // plan_id
    private Long phaseId; // phase_id
    private String title; // 任务标题
    private String remark; // 任务说明
    private Integer type; // 任务类型：0：录播课程学习，1:直播课程学习，2：自测评，3.练习，4：答疑，5：辅导，6：链接
    private Long detailId; // 任务详情id
    private Integer state; // 状态 1：已发布 0：未发布，默认值是0, 2：取消
    private Long hours; // hours
    private Long firstCategory; // first_category
    private Long secondCategory; // second_category
    private Long categoryId; // category_id
    // 初始化为0
    private Integer finishState = 0; // 完成情况：0：未完成，1：部分完成，2:已完成
    private int weicosCount; // 微课数
    private String url; // 链接型任务链接内容
    private Date startTime; // 任务开始时间
    private Date endTime; // 任务结束时间

    public StudyTask() {
        super();
    }

    public StudyTask(Long id) {
        super(id);
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public int getWeicosCount() {
        return weicosCount;
    }

    public void setWeicosCount(int weicosCount) {
        this.weicosCount = weicosCount;
    }

    public Integer getFinishState() {
        return finishState;
    }

    public void setFinishState(Integer finishState) {
        this.finishState = finishState;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getHours() {
        return hours;
    }

    public void setHours(Long hours) {
        this.hours = hours;
    }

}
