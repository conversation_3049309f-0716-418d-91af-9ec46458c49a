package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.*;
import cn.huanju.edu100.study.dao.tutor.TutorStudentCategoryDao;
import cn.huanju.edu100.study.model.common.ResultCount;
import cn.huanju.edu100.study.model.onetoone.*;
import cn.huanju.edu100.study.model.tutor.TutorStudentCategory;
import cn.huanju.edu100.study.service.onetoone.VStudentProductService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.util.DateUtils;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 学员购买记录Service
 *
 * <AUTHOR>
 * @version 2016-04-19
 */
@Service
public class VStudentProductServiceImpl extends BaseServiceImpl<VStudentProductDao, VStudentProduct> implements
        VStudentProductService {
    @Autowired
    private TutorStudentCategoryDao tutorStudentCategoryDao;
    @Autowired
    private VStudentLessonCountDao vStudentLessonCountDao;
    @Autowired
    private VClassesStudentOrderDao vClassesStudentOrderDao;
    @Autowired
    private VClassesDao vClassesDao;
    @Autowired
    private VClassesStudentDao vClassesStudentDao;
    @Autowired
    private VStudentProductDao vStudentProductDao;
    @Autowired
    private VLessonDao vLessonDao;

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED)
    public boolean saveStudentPrivilege(TutorStudentCategory category, String ip) throws DataAccessException {

        List<TutorStudentCategory> categorys = tutorStudentCategoryDao.findListByParam(category);
        Long tutorStuCategoryId = null;
        if (CollectionUtils.isEmpty(categorys)) {
            // TutorStudentCategory不存在记录, 则插入
            //category.setType(1);
            category.setStatus(1);
            tutorStuCategoryId = tutorStudentCategoryDao.insert(category);
            if (!IdUtils.isValid(tutorStuCategoryId)) {
                logger.error("save tutorStudentCategory fail, tutorStudentCategory :{}", GsonUtil.toJson(category));
                throw new DataAccessException(String.format("save tutorStudentCategory fail, tutorStudentCategory :%s",
                        GsonUtil.toJson(category)));
            }
        } else {
            tutorStuCategoryId = categorys.get(0).getId();
        }

        // 插入vStudentProduct学员产品记录
        VStudentProduct vStudentProduct = category.getvStudentProduct();
        vStudentProduct.setGoodsId(category.getGoodsId());
        List<VStudentProduct> vStudentProducts = dao.findList(vStudentProduct);
        //保证同一订单，商品和产品记录唯一
        if (vStudentProduct.getOrderId() != null && CollectionUtils.isNotEmpty(vStudentProducts)) {
            logger.info("save vStudentProduct fail, vStudentProduct has exsit:{}", GsonUtil.toJson(vStudentProduct));
            return true;
        }

        vStudentProduct.setLogId(tutorStuCategoryId);
        vStudentProduct.setLessonCount(vStudentProduct.getLessonCount());
        /*
         * 100留学一对一的自动分配，扣减课时
         * 一对多的需有匹配的班级，才会自动进班，这里先设置为未进班
         */
        if (category.getType().equals(Consts.Tutor_Stduent_Category_Type.NewOneToOnew)
                && (vStudentProduct.getType().equals(Consts.Live_Course_type.ONETOONE_FACE)
                        || vStudentProduct.getType().equals(Consts.Live_Course_type.ONETOONE_LIVE))) {
            vStudentProduct.setUseCount(vStudentProduct.getLessonCount());
        }else {
            vStudentProduct.setLeftCount(vStudentProduct.getLessonCount());
        }

        /**
         * 插入或更改学员课时, 如果学员课时记录不存在, 则插入, 否则更新
         */
        VStudentLessonCount lessonCount = new VStudentLessonCount();
        lessonCount.setProductId(vStudentProduct.getProductId());
        lessonCount.setUid(vStudentProduct.getUid());
        List<VStudentLessonCount> counts = vStudentLessonCountDao.findList(lessonCount);
        if (CollectionUtils.isEmpty(counts)) {
            // (Long uid, String classes, Long firstCategory, Long
            // secondCategory, Long category,
            // Long productId, Integer type, Long lessonCount, Long
            // completeCount, Integer status, String ip)
            // 插入学员课时记录
            VStudentLessonCount newLessonCount = new VStudentLessonCount(vStudentProduct.getUid(), "",
                    vStudentProduct.getFirstCategory(), vStudentProduct.getSecondCategory(),
                    vStudentProduct.getCategoryId(), vStudentProduct.getProductId(), vStudentProduct.getType(),
                    vStudentProduct.getLessonCount(), 0l, 0, ip, category.getSchId());
            if (!IdUtils.isValid(vStudentLessonCountDao.insert(newLessonCount))) {
                logger.error("save vStudentLessonCount fail, vStudentLessonCount :%s", GsonUtil.toJson(newLessonCount));
                throw new DataAccessException(String.format("save vStudentLessonCount fail, vStudentLessonCount :%s",
                        GsonUtil.toJson(newLessonCount)));
            }
        } else {
            // 更新学员课时记录
            VStudentLessonCount old = counts.get(0);
            old.setLessonCount(old.getLessonCount() + vStudentProduct.getLessonCount());
            if (!vStudentLessonCountDao.update(old)) {
                logger.error("saveGoodsAndProduct fail, vStudentLessonCountDao.update error, vStudentLessonCount :{}",
                        GsonUtil.toJson(old));
                throw new DataAccessException(String.format(
                        "saveGoodsAndProduct fail, vStudentLessonCountDao.update error, vStudentLessonCount :%s",
                        GsonUtil.toJson(old)));
            }
        }

        //100留学，自动创建班级和分配学员（一对一的）
        if (category.getType() != null && category.getType().equals(Consts.Tutor_Stduent_Category_Type.NewOneToOnew)) {
            if (distributeStduent(category) && !vStudentProduct.getType().equals(Consts.Live_Course_type.ONETOONE_FACE) && !vStudentProduct.getType()
                    .equals(Consts.Live_Course_type.ONETOONE_LIVE)) {
                vStudentProduct.setUseCount(vStudentProduct.getLessonCount());
                vStudentProduct.setLeftCount(0L);
            }
        }

        if (!IdUtils.isValid(dao.insert(vStudentProduct))) {
            logger.error("save vStudentProduct fail, vStudentProduct :{}", GsonUtil.toJson(vStudentProduct));
            throw new DataAccessException(String.format("save vStudentProduct fail, vStudentProduct :%s",
                    GsonUtil.toJson(vStudentProduct)));
        }

        return true;
    }

    /**
     * 1. 一对一产品：如果学员该产品没有分配班级，则自动创建班级，否则累加
     * 2. 一对n产品：自动进班
     *
     * @param category
     * @throws DataAccessException
     */
    private boolean distributeStduent(TutorStudentCategory category) throws DataAccessException {
        if (category == null) {
            return false;
        }
        VStudentProduct vStudentProduct = category.getvStudentProduct();
        VClassesStudentOrder vClassesStudentOrder = new VClassesStudentOrder();
        vClassesStudentOrder.setProductId(vStudentProduct.getProductId());
        vClassesStudentOrder.setGoodsId(vStudentProduct.getGoodsId());
        vClassesStudentOrder.setUid(vStudentProduct.getUid());

        //不存在，还没有分班，自动创建班级并分配班级，否则课时累加
        VClasses vClasses = null;
        if ((vStudentProduct.getType().equals(Consts.Live_Course_type.ONETOONE_FACE) || vStudentProduct.getType()
                .equals(Consts.Live_Course_type.ONETOONE_LIVE))) {
            // 一对一，自动创建班级并分配班级
            vClasses = new VClasses();
            vClasses.setFirstCategory(vStudentProduct.getFirstCategory());
            vClasses.setSecondCategory(vStudentProduct.getSecondCategory());
            vClasses.setCategoryId(vStudentProduct.getCategoryId());
            vClasses.setSchId(category.getSchId());
            vClasses.setProductId(vStudentProduct.getProductId());
            vClasses.setLessonCount(vStudentProduct.getLessonCount());
            vClasses.setServiceType(vStudentProduct.getType());

            setNameAndNum(category, vStudentProduct, vClasses);
            vClasses.preInsert();
            vClasses.setMaxStudent(1); // 一对一的人数上限是1
            vClassesDao.insert(vClasses);
        } else {
            // 一对多，自动进班
            List<VClasses> bigClasses = vClassesDao.findListByGoodsAndProduct(vStudentProduct.getGoodsId(),
                    vStudentProduct.getProductId());
            if (CollectionUtils.isNotEmpty(bigClasses)) {
                List<Long> clsIds = Lists.newArrayList();
                for (VClasses cls : bigClasses) {
                    clsIds.add(cls.getId());
                }

                List<ResultCount> resultCounts = vClassesStudentDao.getStudentCountByClsIds(clsIds);
                if (CollectionUtils.isNotEmpty(resultCounts)) {
                    Map<Long, Integer> clsStuCountMap = Maps.newHashMap();
                    for (ResultCount resultCount : resultCounts) {
                        clsStuCountMap.put(resultCount.getId(), resultCount.getCnt());
                    }

                    for (VClasses cls : bigClasses) {
                        Integer count = clsStuCountMap.get(cls.getId());
                        if (null != count) {
                            cls.setCurStudent(count);
                        } else {
                            cls.setCurStudent(0);
                        }
                    }
                }

                for (VClasses cls : bigClasses) {
                    if (cls.getCurStudent().intValue() < cls.getMaxStudent().intValue()) {
                        vClasses = cls;
                        break;
                    }
                }

            }

            if (null == vClasses) {
                // 没有合适的班级，无法进班；让运营人员先手动创班
                logger.warn("there is no classes for goodsId :{}, productId :{}, please add classes",
                        vStudentProduct.getGoodsId(), vStudentProduct.getProductId());
                return false;
            }
        }
        vClassesStudentOrder.setClsId(vClasses.getId());

        //分配班级
        VClassesStudent vClassesStudent = new VClassesStudent();
        vClassesStudent.setClsId(vClasses.getId());
        vClassesStudent.setLessonCount((float)vStudentProduct.getLessonCount());
        vClassesStudent.setProductId(vStudentProduct.getProductId());
        vClassesStudent.setStatus(Consts.V_CLASSES_STUDENT_STATUS.NORMAL);
        vClassesStudent.setUid(vStudentProduct.getUid());
        vClassesStudent.setCompleteCount(0f);
        vClassesStudent.setUpdateDate(new Date());
        vClassesStudentDao.insert(vClassesStudent);

        //插入订单明细
        vClassesStudentOrder.setLessonCount((float)vStudentProduct.getLessonCount());
        vClassesStudentOrder.setOrderId(vStudentProduct.getOrderId());
        vClassesStudentOrder.setCompleteCount(0f);
        vClassesStudentOrder.setStatus(Consts.V_CLASSES_STUDENT_STATUS.NORMAL);
        vClassesStudentOrderDao.insert(vClassesStudentOrder);
        return true;
    }

    private void setNameAndNum(TutorStudentCategory category, VStudentProduct vStudentProduct, VClasses vClasses) throws DataAccessException {

        String name = (StringUtils.isBlank(category.getUserName())
                ? category.getUid().toString() : category.getUserName()) + "_" + vStudentProduct.getName() + "班";

        VClasses param = new VClasses();
        param.setName(name);
        List<VClasses> vList = vClassesDao.findList(param);
        if (CollectionUtils.isNotEmpty(vList)) {
            vClasses.setName(name + "(" + vList.size() + ")");
        }else {
            vClasses.setName(name);
        }

        Date now = new Date();
        String num = category.getUserName() + "_" + vStudentProduct.getName() + "_" + DateUtils.formatDateByFormat(now, "MMdd");

        param.setName(null);
        param.setNum(num);
        List<VClasses> vList2 = vClassesDao.findList(param);
        if (CollectionUtils.isNotEmpty(vList2)) {
            vClasses.setNum(num + "_" + (vList2.size() + 1));
        }else {
            vClasses.setNum(num);
        }
    }

    /**
     * 1. 撤销学员班级订单明细
     * 2. 减去学员在该班级的课时
     * 4. 撤销商品记录
     */
    @Override
    public boolean deleteStudentPrivileg(Long orderId, Long goodsId, Long productId) throws DataAccessException {

        if (orderId == null || goodsId == null || productId == null) {
            return false;
        }

        TutorStudentCategory tutorStudentCategory = new TutorStudentCategory();
        tutorStudentCategory.setOrderId(orderId);
        List<TutorStudentCategory> tutorStudentCategories = tutorStudentCategoryDao.findListByParam(tutorStudentCategory);
        if (CollectionUtils.isNotEmpty(tutorStudentCategories)) {
            TutorStudentCategory tu = tutorStudentCategories.get(0);
            if (tu.getType() == 2) { //撤销新的一对一权限
                return deleteNewStudentPrivileg(orderId, goodsId, productId);
            }else if (tu.getType() == 1) { //撤销旧的一对一权限
                return deleteOldStudentPrivileg(orderId, goodsId, productId);
            }
        }

        return false;
    }

    private boolean deleteOldStudentPrivileg(Long buyOrderId, Long goodsId, Long productId) throws DataAccessException {
        logger.info("cancleLiveCoursePrivelege run!");
        TutorStudentCategory tutorStudentCategory = new TutorStudentCategory();
        tutorStudentCategory.setOrderId(buyOrderId);
        List<TutorStudentCategory> tutorStudentCategories = tutorStudentCategoryDao.findListByParam(tutorStudentCategory);
        if (CollectionUtils.isEmpty(tutorStudentCategories)) {
            return false;
        }

        for (TutorStudentCategory studentCategory : tutorStudentCategories) {
            studentCategory.setStatus(0);
            tutorStudentCategoryDao.update(studentCategory);

            VStudentProduct vStudentProduct = new VStudentProduct();
            vStudentProduct.setLogId(studentCategory.getId());
            vStudentProduct.setGoodsId(goodsId);
            vStudentProduct.setProductId(productId);
            vStudentProduct.setOrderId(buyOrderId);
            List<VStudentProduct> products = vStudentProductDao.findList(vStudentProduct);

            for (VStudentProduct vStudentProduct2 : products) {
                VClassesStudent vClassesStudent = new VClassesStudent();
                vClassesStudent.setLogId(vStudentProduct2.getId());
                vClassesStudent.setProductId(vStudentProduct2.getProductId());
                vClassesStudent.setStatus(1);
                vClassesStudentDao.updateStatus(vClassesStudent);

                List<VClassesStudent> vClassesStudents = vClassesStudentDao.findList(vClassesStudent);
                //总课时减去
                VStudentLessonCount vlessonCount = new VStudentLessonCount();
                vlessonCount.setProductId(vStudentProduct2.getProductId());
                vlessonCount.setUid(vStudentProduct2.getUid());
                if (!CollectionUtils.isEmpty(vClassesStudents)) {
                    Integer completeCount = vLessonDao.getCompleteCountByClsId(vClassesStudents.get(0).getClsId());
                    vlessonCount.setLessonCount(vStudentProduct2.getLessonCount() - (completeCount == null ? 0 : completeCount));
                    vlessonCount.setUseCount(vlessonCount.getLessonCount());
                }else {
                    vlessonCount.setLessonCount(vStudentProduct2.getLessonCount());
                }
                vStudentLessonCountDao.deCreaseLessonCount(vlessonCount);
            }
        }
        return true;
    }

    private boolean deleteNewStudentPrivileg(Long orderId, Long goodsId, Long productId) throws DataAccessException {
        VClassesStudentOrder vClassesStudentOrder = new VClassesStudentOrder();
        vClassesStudentOrder.setOrderId(orderId);
        vClassesStudentOrder.setGoodsId(goodsId);
        vClassesStudentOrder.setProductId(productId);
        List<VClassesStudentOrder> vClassesStudentOrders = vClassesStudentOrderDao.findList(vClassesStudentOrder);
        //为空或者已经撤销过订单的不处理
        if (CollectionUtils.isNotEmpty(vClassesStudentOrders)) {
            //已经撤销的返回成功
            if (vClassesStudentOrders.get(0).getStatus().equals(Consts.V_CLASSES_STUDENT_ORDER_STATUS.CANCEL)) {
                logger.info("deleteNewStudentPrivileg has cancel, orderId:{}, goodsId:{}, productId:{}",
                        orderId, goodsId, productId);
                return true;
            }
            vClassesStudentOrder.setStatus(Consts.V_CLASSES_STUDENT_ORDER_STATUS.CANCEL);
            vClassesStudentOrder.setUpdateDate(new Date());
            vClassesStudentOrderDao.update(vClassesStudentOrder);

            for (VClassesStudentOrder vOrder : vClassesStudentOrders) {
                vOrder.setLessonCount(-vOrder.getLessonCount());
                vOrder.setCompleteCount(-vOrder.getCompleteCount());
                vClassesStudentDao.addLessonCount(vOrder);
            }
        }

        TutorStudentCategory tutorStudentCategory = new TutorStudentCategory();
        tutorStudentCategory.setOrderId(orderId);
        tutorStudentCategory.setStatus(0);
        tutorStudentCategoryDao.update(tutorStudentCategory);
        return true;
    }

}
