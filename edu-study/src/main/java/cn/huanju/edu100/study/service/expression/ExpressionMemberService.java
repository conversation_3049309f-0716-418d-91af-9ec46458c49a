package cn.huanju.edu100.study.service.expression;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.expression.ExpressionMember;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Set;

/**
 * 规则关联Service
 * <AUTHOR>
 * @version 2016-05-23
 */
public interface ExpressionMemberService extends BaseService<ExpressionMember> {

    List<ExpressionMember> findListByParam(Set<Long> ruleIdSet, Set<Long> groupIdSet, Integer ruleType)
            throws DataAccessException;

}
