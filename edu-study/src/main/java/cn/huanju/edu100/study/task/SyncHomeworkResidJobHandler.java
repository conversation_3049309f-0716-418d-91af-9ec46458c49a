package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.service.UserAnswerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 同步产品排课、课表排课录播课后作业数据的resId表
 */
@Service
public class SyncHomeworkResidJobHandler  {

    private static final Logger logger = LoggerFactory.getLogger(SyncHomeworkResidJobHandler.class);
    @Autowired
    private UserAnswerService userAnswerService;

    @XxlJob("SyncHomeworkResidJobHandler")
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("------SyncHomeworkResidJobHandler start------");
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);
        if (zoneIndex == 0) {
            userAnswerService.syncHomeworkSyncdataResid();
        }
        logger.info("------SyncHomeworkResidJobHandler end------");
        return ReturnT.SUCCESS;
    }


}
