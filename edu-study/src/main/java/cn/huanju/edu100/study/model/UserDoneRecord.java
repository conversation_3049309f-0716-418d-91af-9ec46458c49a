package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;
import lombok.Data;

import java.util.List;

/**
 * 用户做题记录Entity
 * <AUTHOR>
 * @version 2021-09-07
 */
@Data
public class UserDoneRecord extends DataEntity<UserDoneRecord> {

	private Long uid;		// 用户uid
	private Integer objType;		// 类型 0-试卷 1-模考 2-作业 3-错题 4-收藏 30-云私塾试卷 32-云私塾错题集 33-云私塾收藏本 34-云私塾章节练习 35-云私塾作业 36-云私塾学情回顾
	private Long objId;		// 试卷id或模考id或作业id或章节id或知识点id
	private Long firstCategoryId;		// 大类id
	private Long secondCategoryId;		// 科目id
	private Long categoryId;		// 考试id
	private Long relAnswerId;		// 关联id  可能是user_answer表id或者user_answer_homework表id
	private Long productId;		// 产品id
	private Long goodsId;		// 商品id
	private Integer questionNum;		//问题总数
	private Integer answerNum;		//做题数
	private Integer rightNum;		//题目做对数
	private String accuracy;		//正确率
	private Integer state;		//状态，0未开始 1进行中 2已交卷 3已评卷
	private Integer errorListType; //错题集或收藏夹的类型 1场景归类 2题型归类
	private Integer errorType;		//错题集或收藏夹的题目类型 0试卷 1模考 2作业 3判断题 4填空题 5简答题 6案例题 7单项选择题 8多项选择题 9不定项选择题
	private String errorQuestionIds;		//错题目的id集合
	private Integer doneMode;		//做题模式 1练习模式 2考试模式
	private Long relationId;		// 资源关联id（新课程表的字段）
	private Integer isNewCourse;		//是否为新课程表的 0否（默认） 1是
	private Long lessonId;			//课节id
	private Integer isAl;			//是否云私塾 0-否 1-是
	private Long studyPathId;		//云私塾任务id
	private List<Long> secondCategoryIdList;	// 考试id集合
	private List<Long> categoryIdList;		// 科目id集合
	private List<Long> relAnswerIds;		    // 作答id集合
	private Integer isHaveError;			//作答记录中是否有错题
	private List<Integer> filterObjTypes; //查询时过滤的objType集合
	private List<Long> relAnswerIdList;

	//查询字段
	private Integer sourceType;

}