package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.paper.ReservationPaper;
import cn.huanju.edu100.study.service.*;
import cn.huanju.edu100.study.service.paper.ReservationPaperService;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.util.MaskClock;
import com.google.gson.Gson;
import com.hqwx.study.dto.ReservationPaperDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
@Component
public class StudentStudyThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(StudentStudyThriftImpl.class);
    private static Gson gson = GsonUtil.getGson();
    @Autowired
    private RecordcourseService recordcourseService;
    @Autowired
    private TestTaskService testTaskService;
    @Autowired
    private PaperStudyService paperStudyService;
    @Autowired
    private StudyTaskPushResService studyTaskPushResService;
    @Autowired
    private StudyReportOverviewService studyReportOverviewService;
    @Autowired
    private StudentTaskService studentTaskService;
    @Autowired
    private StudyReportDetailService studyReportDetailService;
    @Autowired
    private StudyLogService studyLogService;
    @Autowired
    private GroupStudentService groupStudentService;
    @Autowired
    private StudyTaskService studyTaskService;
    @Autowired
    private StudyPlanService studyPlanService;
    @Autowired
    private ReservationPaperService reservationPaperService;

    /**
     * 通过任务id查询录播课程
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getRecordCourseListByTidList(request req) throws BusinessException {
        String entry = "sty_getRecordCourseListByTidList";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            long[] param = gson.fromJson(req.getMsg(), long[].class);
            if (true == ValidateUtils.isEmpty(param)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskid List is empty.");
            }

            // ///////////////////////////////////////////////
            List<Long> taskIdList = new LinkedList<Long>();
            for (long p : param) {
                taskIdList.add(p);
            }

            List<Recordcourse> selectResult = recordcourseService.findListByTaskIdList(taskIdList);

            if (CollectionUtils.isEmpty(selectResult)) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getRecordCourseListByTidList return null");
            } else {
                Map<Long, RecordCoursesResponse> map = new HashMap<Long, StudentStudyThriftImpl.RecordCoursesResponse>();
                for (Recordcourse tem : selectResult) {
                    RecordCoursesResponse temDTO = map.get(tem.getTaskId());
                    if (temDTO == null) {
                        temDTO = new RecordCoursesResponse();
                        temDTO.taskId = tem.getTaskId();
                        map.put(tem.getTaskId(), temDTO);
                    }

                    RecordCoursesResponse.RecordCourseResponse recordcourseResponse = new RecordCoursesResponse.RecordCourseResponse();
                    recordcourseResponse.courseId = tem.getCourseId();
                    recordcourseResponse.clsId = tem.getClsId();
                    recordcourseResponse.lessonId = tem.getLessonId();
                    temDTO.recordCourses.add(recordcourseResponse);
                }

                List<RecordCoursesResponse> resultList = new LinkedList<RecordCoursesResponse>();
                resultList.addAll(map.values());
                // ///////////////////////////////////////////////

                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(resultList, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    /**
     * 2.1.7 根据任务idList获得测试任务列表
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getTestTaskListByTidList(request req) throws BusinessException {
        String entry = "sty_getTestTaskListByTidList";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            long[] param = gson.fromJson(req.getMsg(), long[].class);
            if (true == ValidateUtils.isEmpty(param)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskid List is empty.");
            }

            // ///////////////////////////////////////////////
            List<Long> taskIdList = new LinkedList<Long>();
            for (long p : param) {
                taskIdList.add(p);
            }

            List<TestTask> selectResult = testTaskService.findListByTaskIdList(taskIdList);

            if (CollectionUtils.isEmpty(selectResult)) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getTestTaskListByTidList return null");
            } else {
                Map<Long, TestTasksResponse> map = new HashMap<Long, StudentStudyThriftImpl.TestTasksResponse>();
                for (TestTask tem : selectResult) {
                    TestTasksResponse temDTO = map.get(tem.getTaskId());
                    if (temDTO == null) {
                        temDTO = new TestTasksResponse();
                        temDTO.taskId = tem.getTaskId();
                        map.put(tem.getTaskId(), temDTO);
                    }

                    TestTasksResponse.TestTaskResponse testTaskResponse = new TestTasksResponse.TestTaskResponse();
                    testTaskResponse.qId = tem.getqId();
                    testTaskResponse.title = tem.getTitle();
                    testTaskResponse.purpose = tem.getPurpose();
                    temDTO.testTasks.add(testTaskResponse);
                }

                List<TestTasksResponse> resultList = new LinkedList<TestTasksResponse>();
                resultList.addAll(map.values());
                // ///////////////////////////////////////////////

                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(resultList, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    /**
     * 2.1.8 根据任务idList获得作业学习列表
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getPaperStudyListByTidList(request req) throws BusinessException {
        String entry = "sty_getPaperStudyListByTidList";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            long[] param = gson.fromJson(req.getMsg(), long[].class);
            if (true == ValidateUtils.isEmpty(param)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskid List is empty.");
            }

            // ///////////////////////////////////////////////
            List<Long> taskIdList = new LinkedList<Long>();
            for (long p : param) {
                taskIdList.add(p);
            }

            List<PaperStudy> selectResult = paperStudyService.findListByTaskIdList(taskIdList);

            if (CollectionUtils.isEmpty(selectResult)) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getPaperStudyListByTidList return null");
            } else {
                Map<Long, PaperStudysResponse> map = new HashMap<Long, PaperStudysResponse>();
                for (PaperStudy tem : selectResult) {
                    PaperStudysResponse temDTO = map.get(tem.getTaskId());
                    if (temDTO == null) {
                        temDTO = new PaperStudysResponse();
                        temDTO.taskId = tem.getTaskId();
                        map.put(tem.getTaskId(), temDTO);
                    }

                    PaperStudysResponse.PaperStudyResponse paperStudyResponse = new PaperStudysResponse.PaperStudyResponse();
                    paperStudyResponse.paperId = tem.getPaperId();
                    paperStudyResponse.name = tem.getName();
                    temDTO.paperStudys.add(paperStudyResponse);
                }

                List<PaperStudysResponse> resultList = new LinkedList<PaperStudysResponse>();
                resultList.addAll(map.values());
                // ///////////////////////////////////////////////

                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(resultList, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    /**
     * 2.1.9 根据任务idList获得任务推荐资源（微课）列表
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getWeiCourseListByTidList(request req) throws BusinessException {
        String entry = "sty_getWeiCourseListByTidList";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            if (CollectionUtils.isEmpty(params)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskids or uid is empty.");
            }
            Object uidObj = params.get("uid");
            if (null == uidObj || NumberUtils.isDigits(uidObj.toString())) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is empty or not number.");
            }
            Long uid = ((Double) uidObj).longValue();
            Object taskIds = params.get("ids");
            if (ValidateUtils.isEmpty(taskIds)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskids is empty.");
            }
            long[] taskIdArr = gson.fromJson(taskIds.toString(), long[].class);
            if (ValidateUtils.isEmpty(taskIdArr)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskids is empty.");
            }

            // ///////////////////////////////////////////////
            List<Long> taskIdList = new LinkedList<Long>();
            for (long p : taskIdArr) {
                taskIdList.add(p);
            }

            List<StudyTaskPushRes> selectResult = studyTaskPushResService.findListByTaskIdList(taskIdList, uid);

            if (CollectionUtils.isEmpty(selectResult)) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getWeiCourseListByTidList return null");
            } else {
                Map<Long, StudyTaskPushResResponse> map = new HashMap<Long, StudyTaskPushResResponse>();
                for (StudyTaskPushRes tem : selectResult) {
                    StudyTaskPushResResponse temDTO = map.get(tem.getTaskId());
                    if (temDTO == null) {
                        temDTO = new StudyTaskPushResResponse();
                        temDTO.setTaskId(tem.getTaskId());
                        map.put(tem.getTaskId(), temDTO);
                    }

                    if (isExistResId(temDTO.getWeiCourses(), tem.getResId())) {
                        continue;
                    }
                    WeiCourse weiCourse = new WeiCourse();
                    weiCourse.setResId(tem.getResId());
                    weiCourse.setResType(tem.getResType());
                    weiCourse.setKnowledgeId(tem.getKnowledgeId());
                    temDTO.getWeiCourses().add(weiCourse);
                }

                List<StudyTaskPushResResponse> resultList = new LinkedList<StudyTaskPushResResponse>();
                resultList.addAll(map.values());
                // ///////////////////////////////////////////////

                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(resultList, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    /**
     * @param weiCourses
     * @param resId
     * @return
     */
    private boolean isExistResId(List<WeiCourse> weiCourses, Long resId) {
        if (CollectionUtils.isEmpty(weiCourses)) {
            return false;
        }
        for (WeiCourse weiCourse : weiCourses) {
            if (weiCourse.getResId().longValue() == resId.longValue()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 2.1.10 根据用户id获得学习报告总览
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getStudyReportOverviewByUid(request req) throws BusinessException {
        String entry = "sty_getStudyReportOverviewByUid";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            StudyReportOverviewRequest param = gson.fromJson(req.getMsg(), StudyReportOverviewRequest.class);
            if (ValidateUtils.isEmpty(param) || param.isEmpty()) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is empty.");
            }

            // ///////////////////////////////////////////////
            Long uid = param.uid;

            StudyReportOverview selectResult = studyReportOverviewService.findObjectByUid(uid);

            if (selectResult == null) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getStudyReportOverviewByUid return null");
            } else {
                StudyReportOverviewResponse result = new StudyReportOverviewResponse();
                result.taskCount = selectResult.getTaskCount();
                result.videoTime = selectResult.getVideoTime() / 60; // 换算分钟

                // TODO: 获取其他两个字段的值

                // ///////////////////////////////////////////////

                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    /**
     * 2.1.11 根据计划id和用户id获得学习报告详情
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getStudyReportDetailByPidAndUid(request req) throws BusinessException {
        String entry = "sty_getStudyReportDetailByPidAndUid";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            StudyReportDetailByPidAndUidRequest param = gson.fromJson(req.getMsg(),
                    StudyReportDetailByPidAndUidRequest.class);
            if (ValidateUtils.isEmpty(param) || param.isEmpty()) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter pid or uid is empty.");
            }

            Long pid = param.pid;
            Long uid = param.uid;

            StudyTaskReport taskReport = studyReportDetailService.findTaskMsg(pid, uid);
            if (taskReport == null) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getStudyReportDetailByPidAndUid return null");
                return res;
            }

            StudyReportDetailResponse response = new StudyReportDetailResponse();
            response.setTaskCount(taskReport.getTaskCount());
            response.setFinishCount(taskReport.getFinishCount());
            response.setUnfinishCount(taskReport.getUnfinishCount());

            List<StudyReportDetail> reportDetailList = studyReportDetailService.findStudyLogList(pid, uid);
            if (CollectionUtils.isEmpty(reportDetailList)) {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(response, req.getAppid()));
                return res;
            }
            // 统计、归总学生记录信息
            Map<Long, StudyReportDetail> reportDetailMap = new HashMap<Long, StudyReportDetail>();
            Map<Long, Set<Long>> wrongIdMap = new HashMap<Long, Set<Long>>();
            Map<Long, Set<Long>> taskIdMap = new HashMap<Long, Set<Long>>();
            for (StudyReportDetail detail : reportDetailList) {

                StudyReportDetail reportDetail = reportDetailMap.get(detail.getKnowledgeId());
                if (reportDetail == null) {
                    reportDetailMap.put(detail.getKnowledgeId(), detail);
                } else {
                    reportDetail.setAnswerNum(reportDetail.getAnswerNum() + detail.getAnswerNum());
                    reportDetail.setWrongNum(reportDetail.getWrongNum() + detail.getWrongNum());
                }

                Set<Long> wrongIdSet = wrongIdMap.get(detail.getKnowledgeId());
                if (wrongIdSet == null) {
                    wrongIdSet = new HashSet<Long>();
                    wrongIdMap.put(detail.getKnowledgeId(), wrongIdSet);
                }
                if (StringUtils.isNotEmpty(detail.getWrongIds())) {
                    String[] strs = gson.fromJson(detail.getWrongIds(), String[].class);
                    for (String str : strs) {
                        wrongIdSet.add(Long.valueOf(str));
                    }
                }

                Set<Long> taskIdSet = taskIdMap.get(detail.getKnowledgeId());
                if (taskIdSet == null) {
                    taskIdSet = new HashSet<Long>();
                    taskIdMap.put(detail.getKnowledgeId(), taskIdSet);
                }
                if (detail.getTaskId() != null) {
                    taskIdSet.add(detail.getTaskId());
                }
            }
            Iterator<Long> itr = reportDetailMap.keySet().iterator();
            while (itr.hasNext()) {
                Long knowledgeId = itr.next();
                KnowledgeDetail temDetail = new KnowledgeDetail();
                temDetail.setKnowledgeId(knowledgeId);

                StudyReportDetail reportDetail = reportDetailMap.get(knowledgeId);
                if (reportDetail.getWrongNum() <= 0) {
                    continue;
                }
                temDetail.setQuesCount(reportDetail.getAnswerNum());
                temDetail.setWrongCount(reportDetail.getWrongNum());

                for (Long wrongId : wrongIdMap.get(knowledgeId)) {

                    temDetail.getWrongQues().add(new QuestionVo(wrongId));
                }
                for (Long taskId : taskIdMap.get(knowledgeId)) {
                    temDetail.getTaskIds().add(taskId);
                }
                response.getKnowledgeDetails().add(temDetail);
            }

            sortAndFilterKnowledgeDetail(response.getKnowledgeDetails());
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.toJson(response, req.getAppid()));
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    /**
     * 排序并过滤知识点（倒数）及错题统计数据
     *
     * @param knowledgeDetailList
     */
    private void sortAndFilterKnowledgeDetail(List<KnowledgeDetail> knowledgeDetailList) {
        if (CollectionUtils.isEmpty(knowledgeDetailList)) {
            return;
        }
        // 删除错题数为0的知识点
        Iterator<KnowledgeDetail> itr = knowledgeDetailList.iterator();
        while (itr.hasNext()) {
            KnowledgeDetail report = itr.next();
            if (report.getWrongCount() == 0) {
                itr.remove();
            }
        }
        // 排序知识点
        Collections.sort(knowledgeDetailList, new Comparator<KnowledgeDetail>() {
            @Override
            public int compare(KnowledgeDetail detail1, KnowledgeDetail detail2) {
                if (detail1.getWrongCount() > detail2.getWrongCount())
                    return -1;
                if (detail1.getWrongCount() == detail2.getWrongCount())
                    return 0;
                else
                    return 1;
            }
        });
        // 只保留错题数最多的前5个知识点
        Iterator<KnowledgeDetail> itrAll = knowledgeDetailList.iterator();
        int i = 0;
        while (itrAll.hasNext()) {
            itrAll.next();
            if (i > 4) {
                itrAll.remove();
            }
            i++;
        }
    }

    /**
     * 2.1.15 根据任务id和用户id获得学生任务信息
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getStudentTaskByTidAndUid(request req) throws BusinessException {
        String entry = "sty_getStudentTaskByTidAndUid";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            StudentTaskByTidAndUidRequest param = gson.fromJson(req.getMsg(), StudentTaskByTidAndUidRequest.class);
            if (ValidateUtils.isEmpty(param) || param.isEmpty()) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter tid or uid is empty.");
            }

            Long tid = param.tid;
            Long uid = param.uid;
            StudyTask studyTask = studyTaskService.getStudyTaskById(tid);

            if (studyTask == null) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getStudentTaskByTidAndUid studyTask return null");
            } else {
                StudyPlan studyPlan = studyPlanService.getStudyPlanById(studyTask.getPlanId());
                if (studyPlan == null) {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_getStudentTaskByTidAndUid studyPlan return null");
                } else {
                    GroupStudent groupStudent = groupStudentService.qryGroupStudentByGroupIdAndUid(
                            studyPlan.getGroupId(), uid);
                    if (groupStudent == null) {
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("sty_getStudentTaskByTidAndUid groupStudent return null");
                    } else {
                        StudentTask selectResult = studentTaskService.getStudentTaskByTidAndUid(tid, uid);
                        if (selectResult != null) {
                            studyTask.setFinishState(selectResult.getState());
                        }
                        res.code = Constants.SUCCESS;
                        res.setMsg(GsonUtil.toJson(studyTask, req.getAppid()));
                    }
                }
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    /**
     * 2.1.16 根据任务id和用户id更新问题用户
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_updateProblemUserByTaskIdAndUid(request req) throws BusinessException {
        String entry = "sty_updateProblemUserByTaskIdAndUid";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.size() == 0) {
                logger.error("{} fail.parameter id is empty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter json is empty.");
            }
            if (!(param.get("group_id") instanceof Double)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter group_id is not number.");
            }
            if (!(param.get("task_id") instanceof Double)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter task_id is not number.");
            }
            if (!(param.get("uid") instanceof Double)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is not number.");
            }

            long groupId = param.get("group_id").longValue();
            long taskId = param.get("task_id").longValue();
            long uid = param.get("uid").longValue();

            // 0、跟新分组学生的type
            groupStudentService.updateType(groupId, uid, 1);

            // 1、将根据taskId和uid查询student_task表，是否有记录，有则更新状态为2，没有则insert并返回自增的主键值
            StudentTask oldStudentTask = studentTaskService.getStudentTaskByTidAndUid(taskId, uid);
            Long studentTaskId = null; // 学生任务的id值
            if (oldStudentTask != null) {
                studentTaskId = oldStudentTask.getId();
                boolean updateResult = studentTaskService.updateState(oldStudentTask.getId(), 0); // 状态修改为0（未提交）
                if (false == updateResult) {
                    throw new DataAccessException(String.valueOf(Constants.SYS_ERROR), "updata state fail.");
                }
            } else {
                StudentTask temStudentTask = new StudentTask();
                temStudentTask.setTaskId(taskId);
                temStudentTask.setUid(uid);
                temStudentTask.setState(0);
                temStudentTask.setCreator(0l);
                temStudentTask.setCreateTime(new Date());
                temStudentTask.setModifyTime(new Date());
                studentTaskId = studentTaskService.insertAndGetGenerateId(temStudentTask);
            }

            StudyLog temStudyLog = new StudyLog();
            temStudyLog.setStuTaskId(studentTaskId);
            temStudyLog.setUid(uid);
            temStudyLog.setType(2); // type为2(放弃学习)

            // 2、向学生记录表中插入一条记录，
            studyLogService.insert(temStudyLog);

            res.code = Constants.SUCCESS;
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    /**
     * 2.1.27 根据任务idList资源id和用户id判断查看权限
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_hasRightViewResByTidListAndUid(request req) throws BusinessException {
        String entry = "sty_hasRightViewResByTidListAndUid";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            if (CollectionUtils.isEmpty(params)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskids or uid or res_id is empty.");
            }
            Object uidObj = params.get("uid");
            if (null == uidObj || NumberUtils.isDigits(uidObj.toString())) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is empty or not number.");
            }
            Long uid = ((Double) uidObj).longValue();

            Object resIdObj = params.get("res_id");
            if (null == resIdObj || NumberUtils.isDigits(resIdObj.toString())) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter res_id is empty or not number.");
            }
            Long resId = ((Double) resIdObj).longValue();

            Object taskIds = params.get("ids");
            if (ValidateUtils.isEmpty(taskIds)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskids is empty.");
            }
            long[] taskIdArr = gson.fromJson(taskIds.toString(), long[].class);
            if (ValidateUtils.isEmpty(taskIdArr)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter taskids is empty.");
            }

            List<Long> taskIdList = new LinkedList<Long>();
            for (long p : taskIdArr) {
                taskIdList.add(p);
            }

            Boolean hasRight = studyTaskService.hasRightToViewResource(taskIdList, resId);
            if (hasRight == null) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_hasRightViewResByTidListAndUid return null");
            } else {
                ResHasRightResponse hasRightResponse = new ResHasRightResponse();
                if (hasRight.booleanValue()) {
                    hasRightResponse.status = 1;
                } else {
                    hasRightResponse.status = 0;
                }
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(hasRightResponse, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);

        return res;
    }

    // --------------------------封装DTO------------------------------------------------------------------------------

    // /////////////Json
    // Request/////////////////////////////////////////////////////////////////
    private static class StudyReportOverviewRequest {
        public long uid;

        public boolean isEmpty() {
            if (uid <= 0)
                return true;

            return false;
        }
    }

    private static class StudentTaskByTidAndUidRequest {
        public long tid;
        public long uid;

        public boolean isEmpty() {
            if (tid <= 0 || uid <= 0)
                return true;

            return false;
        }
    }

    private static class StudyReportDetailByPidAndUidRequest {
        public long pid;
        public long uid;

        public boolean isEmpty() {
            if (pid <= 0 || uid <= 0)
                return true;

            return false;
        }
    }

    // /////////////Json
    // Response/////////////////////////////////////////////////////////////////
    /** 录播课程 */
    private static class RecordCoursesResponse {
        public long taskId;
        public List<RecordCourseResponse> recordCourses = new LinkedList<RecordCourseResponse>();

        public static class RecordCourseResponse {
            public Long courseId; // course_id
            public Long clsId; // cls_id
            public Long lessonId; // lesson_id
        }
    }

    /** 测试任务 */
    private static class TestTasksResponse {
        public long taskId;
        public List<TestTaskResponse> testTasks = new LinkedList<TestTaskResponse>();

        public static class TestTaskResponse {
            public Long qId; // 题目ID
            public String title; // title
            public String purpose; // purpose
        }
    }

    /** 作业学习 */
    private static class PaperStudysResponse {
        public long taskId;
        public List<PaperStudyResponse> paperStudys = new LinkedList<PaperStudyResponse>();

        public static class PaperStudyResponse {
            private Long paperId; // paper_id
            private String name; // homework_name
        }
    }

    /** 学习报告总览 */
    private static class StudyReportOverviewResponse {
        public int taskCount; // 共完成任务数
        public long videoTime; // 观看视频时长(分钟)
        public int loginCount; // 本月登录次数
        public Date lastLoginTime; // 最近一次登录时间
    }

    private static class StudentTaskResponse {
        public long id;
        public int state;
    }

    private static class ResHasRightResponse {
        public int status;
    }
    // --------------------------封装DTO------------------------------------------------------------------------------

    public response sty_reservePaper(request req) throws BusinessException {
        String entry = "sty_reservePaper";
        long start = MaskClock.getCurtime();
        enterValidator(entry, start, req);
        response res = new response();
        boolean result = false;
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<ReservationPaperDTO>() {}.getType();
            ReservationPaperDTO reservationPaperDTO = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            if (reservationPaperDTO != null && reservationPaperDTO.getUid() != null && reservationPaperDTO.getPaperType() != null){
                ReservationPaper reservationPaper = new ReservationPaper();
                BeanUtils.copyProperties(reservationPaperDTO,reservationPaper);
                reservationPaperService.save(reservationPaper);
                res.setMsg(GsonUtil.toJson(true, req.getAppid()));
                res.code = Constants.SUCCESS;
            }
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
