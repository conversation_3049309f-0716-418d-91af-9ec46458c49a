package cn.huanju.edu100.study.config;

import com.hqwx.userevent.collect.client.message.UserEventPublisher;
import com.hqwx.userevent.collect.client.message.impl.UserEventPublisherImpl;
import com.hqwx.userevent.collect.client.message.properties.UserEventProperties;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.cloud.commons.httpclient.DefaultApacheHttpClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class UserEventConfig {

    /**
     * 本地日志存储方式可以不配置，如果是http请求上报方式则需要配置
     */
     /*@Bean
     public HttpClient buildHttpClient() {
        return HttpClientBuilder.create().build();
     }*/

    @Bean
    public UserEventPublisher buildUserEventPublisher() {
        UserEventProperties collectProperties = new UserEventProperties();
        // true 本地日志存储方式, 直接输出到日志文件, false 非本地存储，将日志以http请求的方式上报到指定的采集服务器
        collectProperties.setLocalLogStoreFlag(true);
        collectProperties.setHttpClient(HttpClientBuilder.create().build());
        return new UserEventPublisherImpl(collectProperties);
    }

 }
