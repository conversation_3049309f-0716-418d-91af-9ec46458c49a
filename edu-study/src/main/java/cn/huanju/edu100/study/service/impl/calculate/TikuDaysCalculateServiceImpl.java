package cn.huanju.edu100.study.service.impl.calculate;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.calculate.TikuDaysCalculateDao;
import cn.huanju.edu100.study.model.calculate.TikuDaysCalculate;
import cn.huanju.edu100.study.service.calculate.TikuDaysCalculateService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

@Service
public class TikuDaysCalculateServiceImpl extends BaseServiceImpl<TikuDaysCalculateDao, TikuDaysCalculate> implements TikuDaysCalculateService {
    @Override
    public TikuDaysCalculate getByUid(Long uid) throws DataAccessException {
        return dao.getByUid(uid);
    }
}
