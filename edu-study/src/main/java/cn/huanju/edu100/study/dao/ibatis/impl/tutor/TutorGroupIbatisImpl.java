/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorGroupDao;
import cn.huanju.edu100.study.model.tutor.TutorGroup;
import cn.huanju.edu100.util.GsonUtils;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.lang3.StringUtils;

import java.sql.SQLException;
import java.util.Map;

/**
 * 学员分组DAO接口
 * <AUTHOR>
 * @version 2016-01-14
 */
public class TutorGroupIbatisImpl extends CrudIbatisImpl2<TutorGroup> implements
		TutorGroupDao {

	public TutorGroupIbatisImpl() {
		super("TutorGroup");
	}

	@Override
	public TutorGroup findByClassesAndCategory(TutorGroup tutorGroup) throws DataAccessException {
		if (null == tutorGroup || StringUtils.isBlank(tutorGroup.getClasses()) || tutorGroup.getCategoryId() == null) {
			logger.error("findByClassesAndCategory param classes or category_id is empty, param:{}",GsonUtils.toJson(tutorGroup));
			throw new DataAccessException("findByClassesAndCategory param classes or category_id is empty");
		}
		try {
			Map<String,Object> paramMap = Maps.newHashMap();
			paramMap.put("classes",tutorGroup.getClasses());
			paramMap.put("categoryId",tutorGroup.getCategoryId());
			SqlMapClient sqlMap = super.getSlave();
			return (TutorGroup) sqlMap
					.queryForObject(namespace.concat(".findByClassesAndCategory"), paramMap);
		} catch (SQLException e) {
			logger.error("findByClassesAndCategory SQLException.", e);
			throw new DataAccessException("findByClassesAndCategory SQLException error");
		}
	}
}
