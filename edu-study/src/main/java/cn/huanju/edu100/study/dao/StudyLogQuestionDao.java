/**
 *
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.StudyLogQuestion;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 学习记录日志DAO接口
 *
 * <AUTHOR>
 */
public interface StudyLogQuestionDao extends CrudDao<StudyLogQuestion> {

    /**
     * 批量插入
     *
     * @param
     * @return
     * @throws DataAccessException
     */
    public long insertBatch(List<StudyLogQuestion> studyLogQuestionList) throws DataAccessException;

}
