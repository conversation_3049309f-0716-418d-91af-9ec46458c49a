/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.question;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.question.QualifyGradingDao;
import cn.huanju.edu100.study.model.question.QualifyGrading;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 排位赛段位信息DAO接口
 * <AUTHOR>
 * @version 2018-04-25
 */
public class QualifyGradingIbatisImpl extends CrudIbatisImpl2<QualifyGrading> implements
		QualifyGradingDao {

	public QualifyGradingIbatisImpl() {
		super("QualifyGrading");
	}

	@Override
	public List<QualifyGrading> getByCateIdAndUid(Long secondCategory, Long categoryId, Long uid) throws DataAccessException {
		if (secondCategory == null) {
			logger.error("getByCateIdAndUid {} error, parameter secondCategory is null,secondCategory:{}", namespace, secondCategory);
			throw new DataAccessException("getByCateIdAndUid error,entity is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();

			Map<String, Object> param = new HashMap<String, Object>();
			param.put("secondCategory", secondCategory);
			param.put("categoryId", categoryId);
			param.put("uid", uid);
			return (List<QualifyGrading>) sqlMap.queryForList("QualifyGrading.getByCateIdAndUid", param);
		} catch (SQLException e) {
			logger.error("getByCateIdAndUid {} SQLException.secondCategory:{}", namespace, secondCategory, e);
			throw new DataAccessException("getByCateIdAndUid SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<QualifyGrading> getLatestByUids(List<Long> uids) throws DataAccessException {
		if (uids == null || uids.isEmpty()) {
			logger.error("getLatestByUids {} error, parameter uids is null,uids:{}", namespace, uids);
			throw new DataAccessException("getLatestByUids error,entity is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();

			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uids", uids);
			return (List<QualifyGrading>) sqlMap.queryForList("QualifyGrading.getLatestByUids", param);
		} catch (SQLException e) {
			logger.error("getLatestByUids {} SQLException.uids:{}", namespace, uids, e);
			throw new DataAccessException("getLatestByUids SQLException error" + e.getMessage());
		}
	}
}
