/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserAnswerDao;
import com.hqwx.study.entity.UserAnswer;
import cn.huanju.edu100.study.model.adminstudy.StudyCompletionInfoQuery;
import cn.huanju.edu100.study.model.adminstudy.StudyReportQuery;
import cn.huanju.edu100.study.model.adminstudy.UserAnswerCompletion;
import cn.huanju.edu100.study.util.Constants;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户答题DAO接口
 * <AUTHOR>
 * @version 2015-05-12
 */
public class UserAnswerIbatisImpl extends CrudIbatisImpl2<UserAnswer> implements
		UserAnswerDao {

	Logger logger = LoggerFactory.getLogger(UserAnswerIbatisImpl.class);

	public UserAnswerIbatisImpl() {
		super("UserAnswer");
	}

	@Override
	public List<UserAnswer> queryUserFinishedPaperByGoodsIdAndPaperIds(Long uid, Long goodsId, List<Long> paperIds) throws DataAccessException {
		if(null == uid || null == goodsId || 0 == goodsId || CollectionUtils.isEmpty(paperIds)){
			logger.error("illegal param, uid or goodsId or paperIds is null");
			throw new DataAccessException("illegal param, uid or goodsId or paperIds is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("goodsId", goodsId);
			param.put("startTime", Constants.USER_VIDEO_LOG_IN_GOODS_START_TIME);
			param.put("paperIds", paperIds);
			return (List<UserAnswer>) sqlMap.queryForList(
					namespace+".queryUserFinishedPaperByGoodsIdAndPaperIds", param);
		} catch (SQLException e) {
			logger.error("queryUserFinishedPaperByGoodsIdAndPaperIds SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"queryUserFinishedPaperByGoodsIdAndPaperIds SQLException error");
		}
	}


	@Override
	public List<UserAnswer> getUserAnswersByPreClassExercise(Long uid, List<Long> paperIds) throws DataAccessException {
		if(null == uid || CollectionUtils.isEmpty(paperIds)){
			logger.error("illegal param, uid or goodsId or paperIds is null");
			throw new DataAccessException("illegal param, uid or goodsId or paperIds is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("paperIds", paperIds);
			return (List<UserAnswer>) sqlMap.queryForList(
					namespace+".getUserAnswersByPreClassExercise", param);
		} catch (SQLException e) {
			logger.error("getUserAnswersByPreClassExercise SQLException.uid:{}", uid, e);
			throw new DataAccessException("getUserAnswersByPreClassExercise SQLException error");
		}
	}

	@Override
	public List<UserAnswer> getUserAnswersByPaperIdsAndObjType(Long uid, List<Long> paperIds, Integer objType) throws DataAccessException {
		if(null == uid || CollectionUtils.isEmpty(paperIds)){
			logger.error("illegal param, uid or goodsId or paperIds is null");
			throw new DataAccessException("illegal param, uid or goodsId or paperIds is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("paperIds", paperIds);
			param.put("objType", objType);
			return (List<UserAnswer>) sqlMap.queryForList(
					namespace+".getUserAnswersByPaperIdsAndObjType", param);
		} catch (SQLException e) {
			logger.error("getUserAnswersByPaperIdsAndObjType SQLException.uid:{}", uid, e);
			throw new DataAccessException("getUserAnswersByPaperIdsAndObjType SQLException error");
		}
	}

	@Override
    public Long countUserFinishedPaperByGoodsIdAndPaperIds(Long uid, Long goodsId, List<Long> paperIds) throws DataAccessException {
        if(null == uid || null == goodsId || 0 == goodsId || CollectionUtils.isEmpty(paperIds)){
            logger.error("illegal param, uid or goodsId or paperIds is null");
            throw new DataAccessException("illegal param, uid or goodsId or paperIds is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", uid);
            param.put("goodsId", goodsId);
            param.put("startTime", Constants.USER_VIDEO_LOG_IN_GOODS_START_TIME);
            param.put("paperIds", paperIds);
            return (Long) sqlMap.queryForObject(
                    namespace+".countUserFinishedPaperByGoodsIdAndPaperIds", param);
        } catch (SQLException e) {
            logger.error("countUserFinishedPaperByGoodsIdAndPaperIds SQLException.uid:{}", uid, e);
            throw new DataAccessException(
                    "countUserFinishedPaperByGoodsIdAndPaperIds SQLException error");
        }
    }

	@Override
	public Long queryStudyStatisticsByParam(Long uid, Long objId, Long productId,String startTime, String endTime) throws DataAccessException {
		if(null == uid || null == objId || null == endTime){
			logger.error("illegal param, uid or objId or endTime is null");
			throw new DataAccessException("illegal param, uid or objId or endTime is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("objId", objId);
			param.put("productId", productId);
			param.put("startTime", startTime);
			param.put("endTime", endTime);
			return (Long) sqlMap.queryForObject(
					namespace+".queryStudyStatisticsByParam", param);
		} catch (SQLException e) {
			logger.error("queryStudyStatisticsByParam SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"queryStudyStatisticsByParam SQLException error");
		}
	}

	@Override
	public List<Map> avgCountList(Map<String, Object> params) throws DataAccessException {
		if(null == params.get("uid") ){
			logger.error("illegal param, uid is null");
			throw new DataAccessException("illegal param, uid is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();

			return (List<Map>)sqlMap.queryForList(
					namespace+".avgCountList", params);
		} catch (SQLException e) {
			logger.error("avgCountList SQLException.params:{}", params, e);
			throw new DataAccessException(
					"avgCountList SQLException error");
		}
	}

	@Override
	public Integer countByStudyReportQuery(StudyReportQuery params) throws DataAccessException {
		if(null == params.getUid() || null == params.getGoodsId()){
			logger.error("illegal param, uid or goodsIdis null");
			throw new DataAccessException("illegal param, uid or goodsId  is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();

			Integer ret = (Integer)sqlMap.queryForObject(
					namespace+".countByStudyReportQuery", params);
			if (ret == null) {
				ret = 0;
			}
			return ret;
		} catch (SQLException e) {
			logger.error("countByStudyReportQuery SQLException.params:{}", params, e);
			throw new DataAccessException(
					"countByStudyReportQuery SQLException error");
		}
	}

	@Override
	public List<UserAnswer> findListByStudyReportQuery(StudyReportQuery params) throws DataAccessException {
		if(null == params.getUid() || null == params.getGoodsId()){
			logger.error("illegal param, uid or goodsIdis null");
			throw new DataAccessException("illegal param, uid or goodsId  is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();

			return (List<UserAnswer>)sqlMap.queryForList(
					namespace+".findListByStudyReportQuery", params);
		} catch (SQLException e) {
			logger.error("findListByStudyReportQuery SQLException.params:{}", params, e);
			throw new DataAccessException(
					"findListByStudyReportQuery SQLException error");
		}
	}

    @Override
    public Integer countUserAnswerCompletion(StudyCompletionInfoQuery params) throws DataAccessException {
		if(null == params.getUid() || null == params.getCategoryId()){
			logger.error("illegal param, uid or categoryId is null");
			throw new DataAccessException("illegal param, uid or categoryId  is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();

			Integer ret = (Integer)sqlMap.queryForObject(
					namespace+".countUserAnswerCompletion", params);
			if (ret == null) {
				ret = 0;
			}
			return ret;
		} catch (SQLException e) {
			logger.error("countUserAnswerCompletion SQLException.params:{}", params, e);
			throw new DataAccessException(
					"countUserAnswerCompletion SQLException error");
		}
    }

    @Override
    public List<UserAnswerCompletion> getUserAnswerCompletionList(StudyCompletionInfoQuery params)  throws DataAccessException {
		if(null == params.getUid() || null == params.getCategoryId()){
			logger.error("illegal param, uid or categoryId is null");
			throw new DataAccessException("illegal param, uid or categoryId  is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();

			return (List<UserAnswerCompletion>)sqlMap.queryForList(
					namespace+".getUserAnswerCompletionList", params);
		} catch (SQLException e) {
			logger.error("getUserAnswerCompletionList SQLException.params:{}", params, e);
			throw new DataAccessException(
					"getUserAnswerCompletionList SQLException error");
		}
    }

    @Override
	public Long getPaperUseTimeByPaperId(Long uid ,Long paperId ,Long productId) throws DataAccessException {
		if(null == uid || null == paperId || Objects.nonNull(productId)){
			logger.error("illegal param, uid or paperId ,productId is null");
			throw new DataAccessException("illegal param, uid or paperId is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("paperId", paperId);
			param.put("productId", productId);
			Long useTime = (Long) sqlMap.queryForObject(
					namespace + ".getPaperUseTimeByPaperId", param);
			return Objects.nonNull(useTime)?useTime:0L;
		} catch (SQLException e) {
			logger.error("getPaperUseTimeByPaperId SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"getPaperUseTimeByPaperId SQLException error");
		}
	}


	@Override
	public Long getUserPaperUseTime(Long uid) throws DataAccessException {
		if(null == uid){
			logger.error("illegal param, uid or paperId ,productId is null");
			throw new DataAccessException("illegal param, uid or paperId is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			Long useTime = (Long) sqlMap.queryForObject(
					namespace + ".getUserUseTime", param);
			return Objects.nonNull(useTime)?useTime:0L;
		} catch (SQLException e) {
			logger.error("getUserUseTime SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"getUserUseTime SQLException error");
		}
	}


	@Override
	public List<UserAnswer> findAnswerGroupByPaperId(Long uid, List<Long> paperIdList, Long goodsId, List<Integer> objTypeList, Integer paperType) throws DataAccessException {
		if(null == uid || CollectionUtils.isEmpty(paperIdList)){
			logger.error("illegal param, uid or paperIdList null");
			throw new DataAccessException("illegal param, uid or paperIdList is null");
		}
		Map<String, Object> params = new HashMap<String, Object>();
		try {
			SqlMapClient sqlMap = super.getSlave();
			params.put("uid",uid);
			params.put("paperIds",paperIdList);
			if(goodsId!=null){
				params.put("goodsId",goodsId);
			}
			if (!CollectionUtils.isEmpty(objTypeList)) {
				params.put("objTypeList", objTypeList);
			}

			if (paperType != null) {
				params.put("paperType", paperType);
			}


			return (List<UserAnswer>)sqlMap.queryForList(
					namespace+".findAnswerGroupByPaperId", params);
		} catch (SQLException e) {
			logger.error("findAnswerGroupByPaperId SQLException.params:{}", params, e);
			throw new DataAccessException(
					"findAnswerGroupByPaperId SQLException error");
		}
	}

	@Override
	public List<UserAnswer> findLastReadOveredSubjectivePaperAnswers(Long uid, List<Long> paperIds, Long goodsId, Long productId, List<Integer> stateList) throws DataAccessException {
		if(null == uid || CollectionUtils.isEmpty(paperIds) || CollectionUtils.isEmpty(stateList)){
			logger.error("illegal param, uid or paperIds or stateList  null");
			throw new DataAccessException("illegal param, uid or paperIds or stateList is null");
		}
		Map<String, Object> params = new HashMap<String, Object>();
		try {
			SqlMapClient sqlMap = super.getSlave();
			params.put("uid",uid);
			params.put("paperIds",paperIds);
			if(goodsId!=null){
				params.put("goodsId",goodsId);
			}
			if(productId!=null){
				params.put("productId",productId);
			}
			params.put("stateList", stateList);

			return (List<UserAnswer>)sqlMap.queryForList(
					namespace+".findAnswerGroupByPaperId", params);
		} catch (SQLException e) {
			logger.error("findAnswerGroupByPaperId SQLException.params:{}", params, e);
			throw new DataAccessException(
					"findAnswerGroupByPaperId SQLException error");
		}
	}

}
