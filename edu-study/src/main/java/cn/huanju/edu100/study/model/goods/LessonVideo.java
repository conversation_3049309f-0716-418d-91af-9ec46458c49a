package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 讲Entity
 *
 */
public class LessonVideo extends DataEntity<LessonVideo> {

	private static final long serialVersionUID = 1L;
	private Long clsId; // 级班id
	private Long courseId; // 课程id
	private Integer idx; // 第几讲
	private String title; // 题标
	private Integer duration; // 长时
	private Integer status; // 默认值是0，表示未发布，1表示显示，已显示的讲学生才能看到，3表示已发布，已发布的讲学生才能看视频
	private Date publishDate; // 布发日期
	private Integer hasVideo; // 否是已经有音频
	private int hasDraft; // 是否有讲义,0表示没有讲义,1表示有讲义
	private int hasPara; // 是否有段落,0表示没有段落,1表示有段落

	private Integer isPrelisten; // 是否支持试听, 0:不支持，1：支持
	private String downloadUrl;// 下载URL
	private Long resId; // 频视地址
	private Integer resType; // 资源类型 0表示旧讲，1表示新讲
	private Long sort; // 序排值
	private Integer videoType; // 0：大屏，1小屏
	private Integer isPrestudy; // 是否支持试学
	private Integer isShow; // 是否显示 //0：不显示，1：显示
	private Integer isLight; // 是否点亮，0：置灰，1：点亮，2：删除
	private String transData;
	private Long teacherId;//老师id
	private String teacherName;//老师姓名
	private String introduction;//单讲介绍
	private Integer difficulty;//本讲难度
	private Long homeworkId;//作业id

	public LessonVideo() {
		super();
	}

	public LessonVideo(Long id) {
		super(id);
	}

	/**
	 * @return the hasPara
	 */
	public int getHasPara() {
		return hasPara;
	}

	/**
	 * @param hasPara
	 *            the hasPara to set
	 */
	public void setHasPara(int hasPara) {
		this.hasPara = hasPara;
	}

	/**
	 * @return the hasDraft
	 */
	public int getHasDraft() {
		return hasDraft;
	}

	/**
	 * @param hasDraft
	 *            the hasDraft to set
	 */
	public void setHasDraft(int hasDraft) {
		this.hasDraft = hasDraft;
	}


	public Long getClsId() {
		return clsId;
	}

	public void setClsId(Long clsId) {
		this.clsId = clsId;
	}

	public Integer getIdx() {
		return idx;
	}

	public void setIdx(Integer idx) {
		this.idx = idx;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getDuration() {
		return duration;
	}

	public void setDuration(Integer duration) {
		this.duration = duration;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getPublishDate() {
		return publishDate;
	}

	public void setPublishDate(Date publishDate) {
		this.publishDate = publishDate;
	}

	public Integer getHasVideo() {
		return hasVideo;
	}

	public void setHasVideo(Integer hasVideo) {
		this.hasVideo = hasVideo;
	}

	public Integer getIsPrelisten() {
		return isPrelisten;
	}

	public void setIsPrelisten(Integer isPrelisten) {
		this.isPrelisten = isPrelisten;
	}

	public Long getResId() {
		return resId;
	}

	public void setResId(Long resId) {
		this.resId = resId;
	}

	public Long getSort() {
		return sort;
	}

	public void setSort(Long sort) {
		this.sort = sort;
	}

	public String getDownloadUrl() {
		return downloadUrl;
	}

	public void setDownloadUrl(String downloadUrl) {
		this.downloadUrl = downloadUrl;
	}

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}

	public Integer getResType() {
		return resType;
	}

	public void setResType(Integer resType) {
		this.resType = resType;
	}

	public String getTransData() {
		return transData;
	}

	public void setTransData(String transData) {
		this.transData = transData;
	}

	public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public Integer getIsShow() {
		return isShow;
	}

	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}

	public Integer getIsLight() {
		return isLight;
	}

	public void setIsLight(Integer isLight) {
		this.isLight = isLight;
	}

	public Integer getIsPrestudy() {
		return isPrestudy;
	}

	public void setIsPrestudy(Integer isPrestudy) {
		this.isPrestudy = isPrestudy;
	}

	public Integer getVideoType() {
		return videoType;
	}

	public void setVideoType(Integer videoType) {
		this.videoType = videoType;
	}

	public Long getHomeworkId() {
		return homeworkId;
	}

	public void setHomeworkId(Long homeworkId) {
		this.homeworkId = homeworkId;
	}
}
