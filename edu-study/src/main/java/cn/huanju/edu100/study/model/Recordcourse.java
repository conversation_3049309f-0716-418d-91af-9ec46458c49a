/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 录播课程学习Entity
 * <AUTHOR>
 * @version 2015-05-15
 */
public class Recordcourse extends DataEntity<Recordcourse> {
	
	private static final long serialVersionUID = 1L;
	private Long taskId;		// task_id
	private Long courseId;		// course_id
	private Long clsId;			// cls_id
	private Long lessonId;		// lesson_id
	
	public Recordcourse() {
		super();
	}

	public Recordcourse(Long id){
		super(id);
	}

	public Long getTaskId() {
		return taskId;
	}
		public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}
	public Long getCourseId() {
		return courseId;
	}
		public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}
	public Long getClsId() {
		return clsId;
	}
		public void setClsId(Long clsId) {
		this.clsId = clsId;
	}
	public Long getLessonId() {
		return lessonId;
	}
		public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}
}