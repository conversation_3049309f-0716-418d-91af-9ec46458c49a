package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.UserAnswerDetailDao;
import cn.huanju.edu100.study.dao.UserHomeWorkAnswerDao;
import cn.huanju.edu100.study.dao.UserVirtualHomeworkDao;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import cn.huanju.edu100.study.model.questionBox.UserBoxHomework;
import cn.huanju.edu100.study.model.questionBox.UserBoxHomeworkDto;
import cn.huanju.edu100.study.model.questionBox.VirtualHomework;
import cn.huanju.edu100.study.model.questionBox.VirtualHomeworkDetail;
import cn.huanju.edu100.study.service.UserVirtualHomeworkService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.DateUtils;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.Map.Entry;
@Service
public class UserVirtualHomeworkServiceImpl
	extends BaseServiceImpl<UserVirtualHomeworkDao, VirtualHomework> implements
	UserVirtualHomeworkService{

	@Autowired
	private UserAnswerDetailDao userAnswerDetailDao;
	@Autowired
	private UserHomeWorkAnswerDao userHomeWorkAnswerDao;

	@Override
	@Transactional(readOnly = false)
    public Long insert(VirtualHomework virtualHomework) throws DataAccessException {
		Long id = dao.insert(virtualHomework);
        return id;
    }

	@Override
	@Transactional(readOnly = false)
	public Integer insertDetailBatch(List<VirtualHomeworkDetail> details,Long uid)
			throws DataAccessException {
		Integer num = dao.insertDetailBatch(details, uid);
		return num;
	}

	@Override
	public VirtualHomework get(long id,long uid) throws DataAccessException {
        return dao.get(id,uid);
    }
	/**
	 * 获取用户题库练习记录count
	 * */
	@Override
	public Integer findUserBoxExerciseCount(Map<String, Double> param)
			throws DataAccessException {
		if(param == null || param.get("uid") == null || param.get("uid")<=0
        		|| param.get("box_id") == null || param.get("box_id")<=0
        		|| param.get("state") == null ){
			throw new DataAccessException("getUserBoxExerciseList paramter lose, uid or box_id or state or from or rows is null");
		}

		Map<String,Object> query = Maps.newHashMap();
		query.put("uid", param.get("uid").longValue());
		query.put("sourceId", param.get("box_id").longValue());
		query.put("sourceType", Consts.Homework_Source_Type.BOX);
        if (param.get("random_type") != null){
            if(param.get("random_type").intValue() == 2) {
                query.put("randomType", 9); //此处特殊处理，用户查询章节练习记录时，random_type的值包括0，1，2
            }else {
                query.put("randomType", param.get("random_type").intValue());
            }
        }
		List<VirtualHomework> homeworkList = dao.findList(query);
		if (CollectionUtils.isNotEmpty(homeworkList)) {
			HashMap<String,Object> paramMap = Maps.newHashMap();
			paramMap.put("uid", param.get("uid").longValue());
			paramMap.put("objType", UserHomeWorkAnswer.HomeWorkType.BOX_HOMEWORK);
			if (param.get("state").intValue()>=0) {
				paramMap.put("state", param.get("state").intValue());
			}
			List<Long> objIdList = Lists.newArrayList();
			for (VirtualHomework virtualHomework : homeworkList) {
				objIdList.add(virtualHomework.getId());
			}
			if(param.containsKey("start_time")){
				paramMap.put("startTime",param.get("start_time"));
			}
			if(param.containsKey("end_time")){
				paramMap.put("endTime",param.get("end_time"));
			}
			paramMap.put("objIdList", objIdList);
			return userHomeWorkAnswerDao.findUserHomeWorkInfoCount(paramMap);
		} else {
			return 0;
		}
	}

	/**
	 * 获取用户题库练习记录List
	 * */
	@Override
	public List<UserBoxHomework> findUserBoxExerciseList(
			Map<String, Double> param, int from, int rows)
			throws DataAccessException {
		if(param == null || param.get("uid") == null || param.get("uid")<=0
        		|| param.get("box_id") == null || param.get("box_id")<=0
        		|| param.get("state") == null ){
			throw new DataAccessException("getUserBoxExerciseList paramter lose, uid or box_id or state or from or rows is null");
		}
        Map<String,Object> query = Maps.newHashMap();
        query.put("uid", param.get("uid").longValue());
        query.put("sourceId", param.get("box_id").longValue());
        query.put("sourceType", Consts.Homework_Source_Type.BOX);
		if (param.get("homework_types") != null){
			if(param.get("homework_types").intValue() == 99) {
				query.put("homeworkTypes", 99);
			}
		}
        if (param.get("random_type") != null){
            if(param.get("random_type").intValue() == 2) {
                query.put("randomType", 9); //此处特殊处理，用户查询章节练习记录时，random_type的值包括0，1，2
            }else {
                query.put("randomType", param.get("random_type").intValue());
            }
        }
        List<VirtualHomework> homeworkList = dao.findList(query);

		if (CollectionUtils.isNotEmpty(homeworkList)) {
			HashMap<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("uid", param.get("uid").longValue());
			if (param.get("state").intValue() >= 0) {
				paramMap.put("state", param.get("state").intValue());
			}
			paramMap.put("objType", UserHomeWorkAnswer.HomeWorkType.BOX_HOMEWORK);
			paramMap.put("from", from);
			paramMap.put("rows", rows);
			List<Long> objIdList = Lists.newArrayList();
			Map<Long,VirtualHomework> map = Maps.newHashMap();
			for (VirtualHomework virtualHomework : homeworkList) {
				objIdList.add(virtualHomework.getId());
				map.put(virtualHomework.getId(),virtualHomework);
			}
			paramMap.put("objIdList", objIdList);
			if(param.containsKey("startTime")){
				paramMap.put("startTime",param.get("startTime"));
			}
			if(param.containsKey("endTime")){
				paramMap.put("endTime",param.get("endTime"));
			}
			paramMap.put("orderBy", " a.update_date desc ");
			List<UserHomeWorkAnswer> userHomeWorkAnswers = userHomeWorkAnswerDao.findUserHomeWorkInfo(paramMap);
			if (CollectionUtils.isNotEmpty(userHomeWorkAnswers)) {
				List<UserBoxHomework> userBoxHomeworks = Lists.newArrayList();
				for (UserHomeWorkAnswer userHomeWorkAnswer : userHomeWorkAnswers) {
					UserBoxHomework userBoxHomework = new UserBoxHomework();
					userBoxHomework.setUserHomeworkId(userHomeWorkAnswer.getId());
					userBoxHomework.setUid(userHomeWorkAnswer.getUid());
					userBoxHomework.setState(userHomeWorkAnswer.getState());
					userBoxHomework.setHomeworkId(userHomeWorkAnswer.getObjId());
					userBoxHomework.setCreateDate(userHomeWorkAnswer.getCreateDate());
					userBoxHomework.setUpdateDate(userHomeWorkAnswer.getUpdateDate());
					VirtualHomework homework = map.get(userHomeWorkAnswer.getObjId());
					if (homework != null) {
						userBoxHomework.setBoxId(homework.getSourceId());
						userBoxHomework.setHomeworkName(homework.getName());
						userBoxHomework.setNum(homework.getNum());
						userBoxHomework.setRandomType(homework.getRandomType());
						userBoxHomework.setHomeWorkTypeId(homework.getHomeworkTypeId());
						userBoxHomework.setHomeworkType(homework.getHomeworkType());
						userBoxHomework.setBookId(homework.getBookId());
					}
					//根据练习记录，再去取出正确答题数
					List<UserAnswerDetail> answerDetails = userAnswerDetailDao.findByUserHomeworkId(param.get("uid").longValue(), userBoxHomework.getUserHomeworkId());
					if (answerDetails != null && answerDetails.size() > 0) {
						userBoxHomework.setRightCount(getUserHomeworkRightCount(answerDetails));
					} else {
						userBoxHomework.setRightCount(0);
					}
					userBoxHomeworks.add(userBoxHomework);
				}
				return userBoxHomeworks;
			} else {
				return Collections.EMPTY_LIST;
			}

		} else {
			return Collections.EMPTY_LIST;
		}
	}

	@Override
	public List<UserBoxHomework> findUserBoxExerciseIsDo(
			Map<String, Object> param, List<Long> user_homework_ids)
			throws DataAccessException {
		List<UserBoxHomework> userBoxHomeworks = new ArrayList<UserBoxHomework>();

		for (Long user_homework_id : user_homework_ids) {
			List<UserAnswerDetail> answerDetails = userAnswerDetailDao.findByUserHomeworkId(((Double)param.get("uid")).longValue(), user_homework_id);
			UserBoxHomework userBoxHomework = new UserBoxHomework();
			if (answerDetails!=null && answerDetails.size() > 0) {
				userBoxHomework.setUserHomeworkId(user_homework_id);
				userBoxHomework.setRightCount(getUserHomeworkRightCount(answerDetails));
				userBoxHomework.setState(UserAnswer.State.SUBMITTED);//练习作业只有提交状态
			}else {
				userBoxHomework.setRightCount(0);
			}
			userBoxHomeworks.add(userBoxHomework);
		}
		return userBoxHomeworks;
	}

	private Integer getUserHomeworkRightCount(List<UserAnswerDetail> answerDetails){
		Set<Long> rightQId = new HashSet<Long>();//存放正确的questionId
		Map<Long, List<UserAnswerDetail>> recordMap = new HashMap<Long, List<UserAnswerDetail>>();

		for (UserAnswerDetail userAnswerDetail : answerDetails) {
			List<UserAnswerDetail> userAnswerDetails = null;
			if (recordMap.get(userAnswerDetail.getQuestionId()) != null) {
				userAnswerDetails = recordMap.get(userAnswerDetail.getQuestionId());
			}else{
				userAnswerDetails = new ArrayList<UserAnswerDetail>();
			}
			userAnswerDetails.add(userAnswerDetail);

			recordMap.put(userAnswerDetail.getQuestionId(), userAnswerDetails);
		}

		if (recordMap.size() > 0) {
			Iterator<Entry<Long, List<UserAnswerDetail>>> recordIterator = recordMap.entrySet().iterator();
			while (recordIterator.hasNext()) {
				Map.Entry<Long, List<UserAnswerDetail>> recordEntry = (Map.Entry<Long, List<UserAnswerDetail>>)recordIterator.next();
	            Long questionId = Long.valueOf(recordEntry.getKey().toString());
	            List<UserAnswerDetail> userAnswerDetails = recordEntry.getValue();

	            int rightNum = 0;
	            for (UserAnswerDetail userAnswerDetail : userAnswerDetails) {
					if (userAnswerDetail.getIsRight() == UserAnswerDetail.IsRight.RIGHT) {
						rightNum++;
					}
				}

	            if (rightNum == userAnswerDetails.size()) {
	            	rightQId.add(questionId);
				}
			}
		}

		return rightQId.size();
	}

	@Override
	public List<VirtualHomeworkDetail> getHomeWorkDetails(Long homeworkId,
			Long uid, Integer elementType) throws DataAccessException {
		return dao.getHomeWorkDetails(homeworkId,uid,elementType);
	}


	/**
	 * 获取用户当天的章节练习记录List
	 * */
	@Override
	public List<UserBoxHomeworkDto> findUserBoxExerciseListToday(Long uid,List<Long> bookIds )
			throws DataAccessException {
		if(uid == null || uid<=0 || CollectionUtils.isEmpty(bookIds)){
			throw new DataAccessException("findUserBoxExerciseListToday paramter lose, uid  or bookIds is null");
		}
		Map<String,Object> query = Maps.newHashMap();
		query.put("uid", uid);
		query.put("bookIds", bookIds);
		query.put("sourceType", Consts.Homework_Source_Type.BOX);
//		query.put("homeWorkType", Consts.Question_Exercise_Type.Chapter);//查询章节练习
		query.put("randomType", 9); //此处特殊处理，用户查询章节练习记录时，random_type的值包括0，1，2
		Date todayBegin = DateUtils.getBeginOfDate(new Date());
		query.put("startTime", todayBegin); //查询今天开始的
		List<VirtualHomework> homeworkList = dao.findList(query);

		if (CollectionUtils.isNotEmpty(homeworkList)) {
			HashMap<String, Object> paramMap = Maps.newHashMap();
			paramMap.put("uid", uid);
			paramMap.put("objType", UserHomeWorkAnswer.HomeWorkType.BOX_HOMEWORK);
			List<Long> objIdList = Lists.newArrayList();
			Map<Long,VirtualHomework> map = Maps.newHashMap();
			for (VirtualHomework virtualHomework : homeworkList) {
				objIdList.add(virtualHomework.getId());
				map.put(virtualHomework.getId(),virtualHomework);
			}
			paramMap.put("objIdList", objIdList);
			paramMap.put("startTime",todayBegin);
			paramMap.put("states", Lists.newArrayList(2,3));//已完成，已交卷。
			paramMap.put("orderBy", " a.update_date desc ");
			List<UserHomeWorkAnswer> userHomeWorkAnswers = userHomeWorkAnswerDao.findUserHomeWorkInfoNotPage(paramMap);
			if (CollectionUtils.isNotEmpty(userHomeWorkAnswers)) {
				List<UserBoxHomeworkDto> userBoxHomeworks = Lists.newArrayList();
				for (UserHomeWorkAnswer userHomeWorkAnswer : userHomeWorkAnswers) {
					UserBoxHomeworkDto userBoxHomework = new UserBoxHomeworkDto();
					userBoxHomework.setUserHomeworkId(userHomeWorkAnswer.getId());
					userBoxHomework.setUid(userHomeWorkAnswer.getUid());
					userBoxHomework.setState(userHomeWorkAnswer.getState());
					userBoxHomework.setHomeworkId(userHomeWorkAnswer.getObjId());
					userBoxHomework.setCreateDate(userHomeWorkAnswer.getCreateDate());
					userBoxHomework.setUpdateDate(userHomeWorkAnswer.getUpdateDate());
					userBoxHomework.setUseSecondTime(userHomeWorkAnswer.getUsetime());
					userBoxHomework.setAnswerNum(userHomeWorkAnswer.getAnswerNum());
					VirtualHomework homework = map.get(userHomeWorkAnswer.getObjId());
					if (homework != null) {
						userBoxHomework.setBoxId(homework.getSourceId());
						userBoxHomework.setHomeworkName(homework.getName());
						userBoxHomework.setNum(homework.getNum());
						userBoxHomework.setRandomType(homework.getRandomType());
						userBoxHomework.setChapterId(homework.getHomeworkTypeId());
						userBoxHomework.setBookId(homework.getBookId());
					}
					//根据练习记录，再去取出正确答题数
					List<UserAnswerDetail> answerDetails = userAnswerDetailDao.findByUserHomeworkId(uid, userBoxHomework.getUserHomeworkId());
					if (answerDetails != null && answerDetails.size() > 0) {
						userBoxHomework.setRightCount(getUserHomeworkRightCount(answerDetails));
					} else {
						userBoxHomework.setRightCount(0);
					}
					userBoxHomeworks.add(userBoxHomework);
				}
				return userBoxHomeworks;
			} else {
				return Collections.EMPTY_LIST;
			}

		} else {
			return Collections.EMPTY_LIST;
		}
	}


	/**
	 * 获取用户最近一次的章节练习记录
	 * */
	@Override
	public UserBoxHomeworkDto findLastUserBoxExercise(Long uid,List<Long> bookIds )
			throws DataAccessException {
		if(uid == null || uid<=0 || CollectionUtils.isEmpty(bookIds)){
			throw new DataAccessException("findUserBoxExerciseListToday paramter lose, uid  or bookIds is null");
		}
		Map<String,Object> query = Maps.newHashMap();
		query.put("uid", uid);
		query.put("bookIds", bookIds);
		query.put("sourceType", Consts.Homework_Source_Type.BOX);
		query.put("homeWorkType", Consts.Question_Exercise_Type.Chapter);//查询章节练习
		query.put("randomType", 9); //此处特殊处理，用户查询章节练习记录时，random_type的值包括0，1，2
		Date todayBegin = DateUtils.getBeginOfDate(new Date());
		query.put("orderBy", "a.id desc ");
		query.put("from", 0);
		query.put("rows", 1);
//		query.put("startTime", todayBegin); //查询今天开始的
		List<VirtualHomework> homeworkList = dao.findList(query);

		if (CollectionUtils.isNotEmpty(homeworkList)) {
			HashMap<String, Object> paramMap = Maps.newHashMap();
			paramMap.put("uid", uid);
			paramMap.put("objType", UserHomeWorkAnswer.HomeWorkType.BOX_HOMEWORK);
			List<Long> objIdList = Lists.newArrayList();
			Map<Long,VirtualHomework> map = Maps.newHashMap();
			for (VirtualHomework virtualHomework : homeworkList) {
				objIdList.add(virtualHomework.getId());
				map.put(virtualHomework.getId(),virtualHomework);
			}
			paramMap.put("objIdList", objIdList);
			paramMap.put("startTime",todayBegin);
			paramMap.put("states", Lists.newArrayList(2,3));//已完成，已交卷。
			paramMap.put("orderBy", " a.update_date desc ");
			paramMap.put("from", 0);
			paramMap.put("rows", 1);
			List<UserHomeWorkAnswer> userHomeWorkAnswers = userHomeWorkAnswerDao.findUserHomeWorkInfo(paramMap);
			if (CollectionUtils.isNotEmpty(userHomeWorkAnswers)) {
				UserBoxHomeworkDto userBoxHomework = null;
				for (UserHomeWorkAnswer userHomeWorkAnswer : userHomeWorkAnswers) {
					userBoxHomework = new UserBoxHomeworkDto();
					userBoxHomework.setUserHomeworkId(userHomeWorkAnswer.getId());
					userBoxHomework.setUid(userHomeWorkAnswer.getUid());
					userBoxHomework.setState(userHomeWorkAnswer.getState());
					userBoxHomework.setHomeworkId(userHomeWorkAnswer.getObjId());
					userBoxHomework.setCreateDate(userHomeWorkAnswer.getCreateDate());
					userBoxHomework.setUpdateDate(userHomeWorkAnswer.getUpdateDate());
					userBoxHomework.setUseSecondTime(userHomeWorkAnswer.getUsetime());
					VirtualHomework homework = map.get(userHomeWorkAnswer.getObjId());
					if (homework != null) {
						userBoxHomework.setBoxId(homework.getSourceId());
						userBoxHomework.setHomeworkName(homework.getName());
						userBoxHomework.setNum(homework.getNum());
						userBoxHomework.setRandomType(homework.getRandomType());
						userBoxHomework.setChapterId(homework.getHomeworkTypeId());
						userBoxHomework.setBookId(homework.getBookId());
					}
					//根据练习记录，再去取出正确答题数
					List<UserAnswerDetail> answerDetails = userAnswerDetailDao.findByUserHomeworkId(uid, userBoxHomework.getUserHomeworkId());
					if (answerDetails != null && answerDetails.size() > 0) {
						userBoxHomework.setRightCount(getUserHomeworkRightCount(answerDetails));
					} else {
						userBoxHomework.setRightCount(0);
					}
					break;
				}
				return userBoxHomework;
			} else {
				return null;
			}

		} else {
			return null;
		}
	}



}
