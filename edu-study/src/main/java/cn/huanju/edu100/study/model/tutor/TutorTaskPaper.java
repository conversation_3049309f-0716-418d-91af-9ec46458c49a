package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 试卷任务Entity
 * <AUTHOR>
 * @version 2016-01-18
 */
public class TutorTaskPaper extends DataEntity<TutorTaskPaper> {
	
	private static final long serialVersionUID = 1L;
	private Integer type;		// type
	private Integer flag;		// 单元测评标志，0：不突出， 1：突出
	private Long detailId;		// detail_id
	private Integer isSend;		// 0：推送，1：不推送
	private String bak1;		// bak1
	private String bak2;		// bak2
	
	public TutorTaskPaper() {
		super();
	}

	public TutorTaskPaper(Long id){
		super(id);
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public Long getDetailId() {
		return detailId;
	}

	public void setDetailId(Long detailId) {
		this.detailId = detailId;
	}
	
	public Integer getIsSend() {
		return isSend;
	}

	public void setIsSend(Integer isSend) {
		this.isSend = isSend;
	}
	
	public String getBak1() {
		return bak1;
	}

	public void setBak1(String bak1) {
		this.bak1 = bak1;
	}
	
	public String getBak2() {
		return bak2;
	}

	public void setBak2(String bak2) {
		this.bak2 = bak2;
	}

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }
	
}