package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.StudentTaskDao;
import cn.huanju.edu100.study.model.StudentTask;
import cn.huanju.edu100.study.service.StudentTaskService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

/**
 * 学习计划Service
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
@Service
public class StudentTaskServiceImpl extends BaseServiceImpl<StudentTaskDao, StudentTask> implements StudentTaskService {

    @Override
    public StudentTask getStudentTaskByTidAndUid(Long tid, Long uid) throws DataAccessException, BusinessException {
        return dao.findLimitOneByTidAndUid(tid, uid);
    }

    @Override
    public boolean updateState(Long id, Integer state) throws DataAccessException {
    	return dao.updateState(id, state);
    }

    @Override
    public long insertAndGetGenerateId(StudentTask studentTask) throws DataAccessException {
    	return dao.insertAndGetGenerateId(studentTask);
    }
}
