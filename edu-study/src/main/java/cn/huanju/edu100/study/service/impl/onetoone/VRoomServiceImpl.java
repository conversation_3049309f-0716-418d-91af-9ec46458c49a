package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.VRoomDao;
import cn.huanju.edu100.study.model.onetoone.VRoom;
import cn.huanju.edu100.study.service.onetoone.VRoomService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 面授课室Service
 *
 * <AUTHOR>
 * @version 2016-04-12
 */
@Service
public class VRoomServiceImpl extends BaseServiceImpl<VRoomDao, VRoom> implements VRoomService {


    @Override
    public List<VRoom> findListByIds(Set<Long> ids) throws DataAccessException {

        if (CollectionUtils.isEmpty(ids)) {
             logger.error("findListByIds fail, empty ids");
             throw new DataAccessException("findListByIds fail, empty ids");
        }
        List<Long> idList = Lists.newArrayList();
        idList.addAll(ids);
        return dao.findListByIds(idList);
    }
}
