package cn.huanju.edu100.study.model.wxapp;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

public class SolutionWxappAdvertisingPostion extends DataEntity<SolutionWxappAdvertisingPostion> {

    private Long orgId; // 机构id

    private Long type; // 广告类型 1弹窗 2悬浮图 3banner  4动态icon 5推荐广告 6微销广告图

    private String appid; //  所属wxapp

    private Integer position; // 位置

    private Long secondCategory; // 所属考试  0 为全部

    private String secondCategoryIds; //type=4/5时，所属考试id集合，逗号隔开

    private Long contentType; //内容类型

    private Long showType;  // 显示类型 1 文本 2 图片

    private String images; // 图片地址

    private String title; // 标题

    private String content; // 内容

    private String button;  // 按钮

    private Long urlType; //跳转类型 1客服消息 2小程序路径 3外部链接

    private String path; //wxapp路径

    private String extLink; //外部链接

    private String targetAppid; //目标小程序appid

    private Long openTime;  // 限时上线时间

    private Long closeTime; // 限时下线时间

    private Long sort;  // 排序

    private Long status; // 状态 0未上线 1已上线 9 删除

    private Long channel; // 频道



    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public String getSecondCategoryIds() {
        return secondCategoryIds;
    }

    public void setSecondCategoryIds(String secondCategoryIds) {
        this.secondCategoryIds = secondCategoryIds;
    }

    public Long getContentType() {
        return contentType;
    }

    public void setContentType(Long contentType) {
        this.contentType = contentType;
    }

    public Long getShowType() {
        return showType;
    }

    public void setShowType(Long showType) {
        this.showType = showType;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getButton() {
        return button;
    }

    public void setButton(String button) {
        this.button = button;
    }

    public Long getUrlType() {
        return urlType;
    }

    public void setUrlType(Long urlType) {
        this.urlType = urlType;
    }

    public String getExtLink() {
        return extLink;
    }

    public void setExtLink(String extLink) {
        this.extLink = extLink;
    }

    public String getTargetAppid() {
        return targetAppid;
    }

    public void setTargetAppid(String targetAppid) {
        this.targetAppid = targetAppid;
    }

    public Long getOpenTime() {
        return openTime;
    }

    public void setOpenTime(Long openTime) {
        this.openTime = openTime;
    }

    public Long getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Long closeTime) {
        this.closeTime = closeTime;
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getChannel() {
        return channel;
    }

    public void setChannel(Long channel) {
        this.channel = channel;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

}
