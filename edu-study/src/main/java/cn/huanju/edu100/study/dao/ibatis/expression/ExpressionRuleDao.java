/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.expression;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.expression.ExpressionRule;

import java.util.List;
import java.util.Map;

/**
 * 规则DAO接口
 * <AUTHOR>
 * @version 2016-05-23
 */
public interface ExpressionRuleDao extends CrudDao<ExpressionRule> {

    List<ExpressionRule> findListByParam(List<Long> idList, Integer type) throws DataAccessException;

    List<ExpressionRule> getExpressionRules(Map<String, Object> params) throws DataAccessException;
}
