package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.Bulletin;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;
import java.util.Set;

/**
 * 网校公告Service
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface BulletinService extends BaseService<Bulletin> {

	/**
	 * @param uid
	 * @param schId
	 * @param date
	 * @return
	 */
	Collection<Bulletin> listBulletinByType(Long uid, Long schId, Integer type) throws DataAccessException;

	Collection<Bulletin> getBulletinListByIds(String ids, String category_ids, Long uid, Long schId) throws DataAccessException;

	Collection<Bulletin> findListByIds(Set<Long> bulletinIdSet, Long schId, Long uid) throws DataAccessException;

    boolean cancelBulletin(Long uid, String[] bulletinIds) throws DataAccessException;

}
