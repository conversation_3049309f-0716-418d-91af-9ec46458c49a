package cn.huanju.edu100.study.service.question;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.question.QualifyGrading;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 排位赛段位信息Service
 * <AUTHOR>
 * @version 2018-04-25
 */
public interface QualifyGradingService extends BaseService<QualifyGrading> {
    List<QualifyGrading> getByCateIdAndUid(Long secondCategory, Long categoryId, Long uid) throws DataAccessException;

    List<QualifyGrading> getLatestByUids(List<Long> uids) throws DataAccessException;
}