package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.tutor.CommentThumb;
import cn.huanju.edu100.study.service.tutor.CommentService;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.ParamUtils;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class CommentThriftImpl extends AbstractServiceThrift{
    private static Logger logger = LoggerFactory.getLogger(CommentThriftImpl.class);
    private static Gson gson = GsonUtil.getGson();
    @Autowired
    private CommentService commentService;

    public response sty_findUserThumbUpList(request req) throws BusinessException {
        String entry = "sty_findUserThumbUpList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            List<Long> commentIdList = ParamUtils.getLongList(params,"commentIdList", false);

            if (uid == null || CollectionUtils.isEmpty(commentIdList)) {
                logger.error("{} fail.paramerter uid or commentIdList  is null or empty.", entry);
                throw new BusinessException(Constants.PARAM_LOSE, "fail.paramerter uid or commentIdList  is null or empty..");
            }
            List<CommentThumb> result = commentService.findUserThumbUpList(uid, commentIdList);
            res.setCode(Constants.SUCCESS);
            res.setMsg(gson.toJson(result));
        } catch (BusinessException e) {
            throw e;
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
