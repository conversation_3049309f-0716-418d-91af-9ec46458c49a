package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorGroupStudentDao;
import cn.huanju.edu100.study.model.tutor.TutorGroupStudent;
import cn.huanju.edu100.study.service.tutor.TutorGroupStudentService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 学员跟分组关联Service
 *
 * <AUTHOR>
 * @version 2016-01-26
 */
@Service
public class TutorGroupStudentServiceImpl extends BaseServiceImpl<TutorGroupStudentDao, TutorGroupStudent> implements
        TutorGroupStudentService {


    @Override
    public List<TutorGroupStudent> findByGroupIdAndUid(Long groupId, Long uid) throws DataAccessException {
        Map<String,Object> params = Maps.newHashMap();
        params.put("groupId", groupId);
        params.put("uid", uid);
        return dao.getTutorGroupStudent(params);
    }
}
