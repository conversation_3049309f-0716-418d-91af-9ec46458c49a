package cn.huanju.edu100.study.config.redis;

import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.redis.cluster.client.RedisClusterClient;
import com.alibaba.cloud.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Configuration
public class RedisClusterConfig {

    @Value("${al.master.redis.host}")
    private String alMasterRedisHost;

    @Value("${al.master.redis.port}")
    private int alMasterRedisPort;

    @Value("${al.master.redis.timeout}")
    private int alMasterRedisTimeout;

    @Value("${al.master.redis.password:null}")
    private String alMasterRedisPassword;

    @Bean
    @ConfigurationProperties("rediscluster")
    public RedisConfig  rediscluster(){
        return new RedisConfig();
    }

    @Bean
    @ConfigurationProperties("answer.rediscluster")
    public RedisConfig answerLocalRedisCluster(){
        return new RedisConfig();
    }

    @Bean("jedisPoolConfig")
    public JedisPoolConfig getJedisPoolConfig(){
        JedisPoolConfig cfg = new JedisPoolConfig();
        cfg.setMaxTotal(500);
        cfg.setMaxIdle(50);
        cfg.setMinIdle(1);
//        cfg.setMaxWaitMillis(10000);
        cfg.setMaxWait(Duration.ofNanos(10000));
        cfg.setTestOnBorrow(false);
        cfg.setTimeBetweenEvictionRunsMillis(60000);
        cfg.setTestWhileIdle(true);
        return cfg;
    }

    @Bean("localJedisCluster")
    public JedisCluster getLocalJedisCluster(@Qualifier("jedisPoolConfig") JedisPoolConfig jedisPoolConfig,RedisConfig rediscluster){
        Set<HostAndPort> set = new HashSet<>();
        for(String str: rediscluster.getLocal()) {
            set.add(new HostAndPort(str.split(":")[0],Integer.valueOf(str.split(":")[1])));
        }
        return new JedisCluster(set, 2000, 1500, 10, rediscluster.getPassword(),
                jedisPoolConfig);
    }

    @Bean("farJedisCluster")
    public JedisCluster getFarJedisCluster(@Qualifier("jedisPoolConfig") JedisPoolConfig jedisPoolConfig,RedisConfig rediscluster){
        Set<HostAndPort> set = new HashSet<>();
        for(String str: rediscluster.getFar()) {
            set.add(new HostAndPort(str.split(":")[0],Integer.valueOf(str.split(":")[1])));
        }
        return new JedisCluster(set, 2000, 1500, 10, rediscluster.getPassword(),
                jedisPoolConfig);
    }

    @Bean("lockJedisCluster")
    public JedisCluster getLockJedisCluster(@Qualifier("jedisPoolConfig") JedisPoolConfig jedisPoolConfig,RedisConfig rediscluster){
        Set<HostAndPort> set = new HashSet<>();
        for(String str: rediscluster.getLock()) {
            set.add(new HostAndPort(str.split(":")[0],Integer.valueOf(str.split(":")[1])));
        }
        return new JedisCluster(set, 2000, 1500, 10, rediscluster.getPassword(),
                jedisPoolConfig);
    }

//    @Bean("localLockJedisCluster")
//    public JedisCluster getLocalLockJedisCluster(@Qualifier("jedisPoolConfig") JedisPoolConfig jedisPoolConfig,RedisConfig rediscluster){
//        Set<HostAndPort> set = new HashSet<>();
//        for(String str: rediscluster.getLock()) {
//            set.add(new HostAndPort(str.split(":")[0],Integer.valueOf(str.split(":")[1])));
//        }
//        return new JedisCluster(set, 2000, 1500, 10, rediscluster.getPassword(),
//                jedisPoolConfig);
//    }

    @Bean("answerLocalJedisCluster")
    public JedisCluster getAnswerLocalJedisCluster(@Qualifier("jedisPoolConfig") JedisPoolConfig jedisPoolConfig,RedisConfig answerLocalRedisCluster){
        Set<HostAndPort> set = new HashSet<>();
        for(String str: answerLocalRedisCluster.getLocal()) {
            set.add(new HostAndPort(str.split(":")[0],Integer.valueOf(str.split(":")[1])));
        }
        return new JedisCluster(set, 2000, 1500, 10, answerLocalRedisCluster.getPassword(),
                jedisPoolConfig);
    }

    @Bean("answerFarJedisCluster")
    public JedisCluster getAnswerFarJedisCluster(@Qualifier("jedisPoolConfig") JedisPoolConfig jedisPoolConfig,RedisConfig answerLocalRedisCluster){
        Set<HostAndPort> set = new HashSet<>();
        for(String str: answerLocalRedisCluster.getFar()) {
            set.add(new HostAndPort(str.split(":")[0],Integer.valueOf(str.split(":")[1])));
        }
        return new JedisCluster(set, 2000, 1500, 10, answerLocalRedisCluster.getPassword(),
                jedisPoolConfig);
    }

    @Bean("answerCompatableRedisClusterClient")
    public CompatableRedisClusterClient getAnswerCompatableRedisClusterClient(
            @Qualifier("answerLocalJedisCluster")
            JedisCluster answerLocalJedisCluster,
            @Qualifier("answerFarJedisCluster")
            JedisCluster answerFarJedisCluster
    ){
        CompatableRedisClusterClient client = new CompatableRedisClusterClient();
        client.setLocalJedisCluster(answerLocalJedisCluster);
        client.setFarJedisCluster(answerFarJedisCluster);
        client.setMutiWrite(false);
        return client;
    }

    @Bean("compatableRedisClusterClient")
    public CompatableRedisClusterClient getCompatableRedisClusterClient(
            @Qualifier("localJedisCluster")
            JedisCluster localJedisCluster,
            @Qualifier("farJedisCluster")
            JedisCluster farJedisCluster
    ){
        CompatableRedisClusterClient client = new CompatableRedisClusterClient();
        client.setLocalJedisCluster(localJedisCluster);
        client.setFarJedisCluster(farJedisCluster);
        client.setMutiWrite(false);
        return client;
    }

    @Bean("redisClusterClient")
    public RedisClusterClient getRedisClusterClient(
            @Qualifier("localJedisCluster") JedisCluster localJedisCluster,
            @Qualifier("farJedisCluster") JedisCluster farJedisCluster
    ){

        RedisClusterClient client = new RedisClusterClient();
        client.setLocalJedisCluster(localJedisCluster);
        client.setFarJedisCluster(farJedisCluster);
        return client;
    }

    @Bean("defaultJedisPoolConfig")
    public JedisPoolConfig getDefaultJedisPoolConfig(){
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(500);
        config.setMaxIdle(50);
        config.setMaxWaitMillis(10000);
        config.setTestOnBorrow(false);
        config.setTestWhileIdle(true);
        config.setTimeBetweenEvictionRunsMillis(60000);
        config.setTimeBetweenEvictionRuns(Duration.ofMillis(60000));
        return config;
    }

    @Bean("alMasterPool")
    public JedisPool getAlMasterPool(@Qualifier("defaultJedisPoolConfig") JedisPoolConfig defaultJedisPoolConfig){
        if(StringUtils.isNotEmpty(alMasterRedisPassword)){
            JedisPool pool = new JedisPool(defaultJedisPoolConfig, alMasterRedisHost, alMasterRedisPort,
                    alMasterRedisTimeout, alMasterRedisPassword);
            return pool;
        }
        JedisPool pool = new JedisPool(defaultJedisPoolConfig, alMasterRedisHost, alMasterRedisPort,
                alMasterRedisTimeout);
        return pool;
    }
}
