package cn.huanju.edu100.study.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.google.common.base.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/2 14:21
 * @description 试卷提交统计信息
 */
@Data
public class PaperSubmitStatisticInfo {

    @TableId(value = "id", type = IdType.AUTO)
    Long id;
    Long paperId;

    /**
     * 分数范围索引，取值为 0 ～ 9，计算公式：分数*100/试卷总分
     * 0: [0, 10)
     * 1: [10, 20)
     * 2: [20, 30)
     * 3: [30, 40)
     * 4: [40, 50)
     * 5: [50, 60)
     * 6: [60, 70)
     * 7: [70, 80)
     * 8: [80, 90)
     * 9: [90, 100]
     */
    Integer scoreIndex;

    /**
     * 当前试卷在 score_index 分数区间提交的人次，已加上 baseSubmitCount 的值
     */
    Long submitCount;

    /**
     * 当前分数段内的提交的总分数，已加上 baseTotalScore 的值
     */
    Double totalScore;

    /**
     * 历史总提交数，从数仓同步，之后不再修改
     */
    Long baseSubmitCount;

    /**
     * 历史提交总分数，从数仓同步，之后不再修改
     */
    Double baseTotalScore;

    /**
     * 当前试卷正确率在该区间的提交数量
     */
    Long submitAccuracyCount;

    /**
     * 当前试卷正确率在该区间的总正确率
     */
    Double totalAccuracy;

    /**
     * 当前试卷正确率在该区间的提交数量，历史数据，从数仓同步
     */
    Long baseSubmitAccuracyCount;

    /**
     * 当前试卷正确率在该区间的总正确率，历史数据，从数仓同步
     */
    Double baseTotalAccuracy;


    /**
     * 指定总分
     * @param score
     * @param totalScore
     * @return
     */
    public static Integer score2Index(Double score, Double totalScore) {
        if(score == null || Objects.equal(score,0.0) || totalScore == null || Objects.equal(totalScore,0.0)){
            return 0;
        }
        Double newScore = score/totalScore*100; // 先转换为总分 100 的得分
        Double temp = newScore/10;
        Integer v = temp.intValue();
        return v > 9 ? 9 : v; // 如果100，v的结果会是 10，这里修正一下
    }

    public static Integer score2IndexByPaperAccuracy(Double paperAccuracy) {
        if(paperAccuracy == null || Objects.equal(paperAccuracy,0.0)){
            return 0;
        }
        Double temp = paperAccuracy/10;
        Integer v = temp.intValue();
        return v > 9 ? 9 : v; // 如果100，v的结果会是 10，这里修正一下
    }

    public void ensureNonNullFields() {
        if (this.submitCount == null) {
            this.submitCount = 0L;
        }
        if (this.totalScore == null) {
            this.totalScore = 0.0;
        }
        if (this.submitAccuracyCount == null) {
            this.submitAccuracyCount = 0L;
        }
        if (this.totalAccuracy == null) {
            this.totalAccuracy = 0.0;
        }
    }
}
