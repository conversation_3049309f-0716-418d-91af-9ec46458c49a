package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.StudyReportOverviewDao;
import cn.huanju.edu100.study.model.StudyReportOverview;
import cn.huanju.edu100.study.util.ValidateUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;

public class StudyReportOverviewIbatisImpl extends CrudIbatisImpl2<StudyReportOverview> implements StudyReportOverviewDao {

	public StudyReportOverviewIbatisImpl() {
		super("StudyReportOverview");
	}

	@Override
	public StudyReportOverview findObjectByUid(Long uid) throws DataAccessException {
		if (ValidateUtils.isEmpty(uid)) {
			logger.error("findObjectByUid {} error, parameter uid is invalid,uid:{}", namespace, uid);
			throw new DataAccessException("findObjectByUid error,parameter error");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			return (StudyReportOverview) sqlMap.queryForObject("StudyReportOverview.findObjectByUid", uid);
		} catch (SQLException e) {
			logger.error("findObjectByUid {} SQLException uid:{}", namespace, uid, e);
			throw new DataAccessException("get SQLException error" + e.getMessage());
		}
	}

}
