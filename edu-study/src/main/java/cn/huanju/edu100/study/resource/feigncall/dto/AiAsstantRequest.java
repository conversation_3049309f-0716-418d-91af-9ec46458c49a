package cn.huanju.edu100.study.resource.feigncall.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @Description
 * @author: zhangtong
 * @create: 2023/12/13 11:33 AM
 */
//@Data
@SuppressWarnings({"unchecked","unused"})
public class AiAsstantRequest extends HashMap<String,Object> implements Serializable {
    public AiAsstantRequest(){}
    public AiAsstantRequest(Map<String,Object> map){super(map);}

    @ApiModelProperty("用户ID")
    public void setUid(Long uid){
        this.put("uid", uid);
    }
    @ApiModelProperty("appName")
    public void setAppName(String appName){
        this.put("appName", appName);
    }
    public Long getUid(){
        if(this.containsKey("uid"))
            return Long.parseLong(this.get("uid")+"");
        else
            return null;
    }
    @ApiModelProperty("用户输入的内容")
    public void setContent(Object content){
        this.put("content",content);
    }
    public Object getContent(){
        return this.get("content");
    }
    @ApiModelProperty("会话ID")
    public void setSessionId(String sessionId){
        this.put("sessionId",sessionId);
    }
    public String getSessionId(){
        return (String)this.get("sessionId");
    }

    @ApiModelProperty("业务场景id")
    public void setBusinessSceneId(Long businessSceneId){
        this.put("businessSceneId",businessSceneId);
    }
    public String getBusinessSceneId(){
        return (String)this.get("businessSceneId");
    }

    /**
     * <AUTHOR>
     * 2024-09-04 新增业务场景名称字段,用来兼容生产测试场景id不同的情况
     */
    @ApiModelProperty("业务场景名称")
    public void setBusinessSceneName(String businessSceneName){
        this.put("businessSceneName",businessSceneName);
    }
    public String getBusinessSceneName(){
        if(this.containsKey("businessSceneName")){
            return this.get("businessSceneName").toString() ;
        }else{
            return null;
        }
    }

    @ApiModelProperty("入口名称")
    public void setEntryName(String entryName){
        this.put("entryName",entryName);
    }
    public String getEntryName(){
        return (String)this.get("entryName");
    }
    @ApiModelProperty(hidden = true)
    public void setCurrentSceneId(Long currentSceneId){
        this.put("currentSceneId",currentSceneId);
    }
    public Long getCurrentSceneId(){
        if(this.containsKey("currentSceneId")){
            return Long.parseLong(this.get("currentSceneId") + "");
        }else{
            return null;
        }
    }

    @ApiModelProperty("查询场景ID")
    public void setQuerySceneId(Long querySenceId){
        this.put("querySceneId",querySenceId);
    }
    public Long getQuerySceneId(){
        if(this.containsKey("querySceneId")){
            return Long.parseLong(this.get("querySceneId") + "");
        }else{
            return null;
        }

    }

    @ApiModelProperty("当前场景的输入参数")
    private Map<String,Object> params;
    public Map<String,Object> getParams(){
        if (!this.containsKey("params")){
            this.put("params",new HashMap<String,Object>());
        }
        return (Map<String, Object>) this.get("params");
    }

    public void setParams(Map<String, Object> params){
        this.put("params",params);
    }

    public void setMsgId(String msgId){
        this.put("msgId",msgId);
    }
    public String getMsgId(){
        if(this.containsKey("msgId")){
            return this.get("msgId") + "";
        }else{
            var uuid =  UUID.randomUUID().toString().replace("-","");
            this.put("msgId",uuid);
            return uuid;
        }
    }
}
