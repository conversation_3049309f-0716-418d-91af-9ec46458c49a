package cn.huanju.edu100.study.model.onetoone;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 班级督导Entity
 * <AUTHOR>
 * @version 2016-12-16
 */
public class VSysUserClasses extends DataEntity<VSysUserClasses> {
	
	private static final long serialVersionUID = 1L;
	private Long userId;		// 管理后台用户id
	private Long vClsId;		// 班级id
	private String name;		// 管理后台用户名
	
	public VSysUserClasses() {
		super();
	}

	public VSysUserClasses(Long id){
		super(id);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getvClsId() {
        return vClsId;
    }

    public void setvClsId(Long vClsId) {
        this.vClsId = vClsId;
    }
}	
