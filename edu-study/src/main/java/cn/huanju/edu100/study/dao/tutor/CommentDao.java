/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.dto.CommentStarGroup;
import cn.huanju.edu100.study.model.dto.MyComment;
import cn.huanju.edu100.study.model.tutor.Comment;
import cn.huanju.edu100.study.model.tutor.CommentElement;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 评价DAO接口
 * <AUTHOR>
 * @version 2017-12-06
 */
public interface CommentDao extends CrudDao<Comment> {

    List<Comment> findListByObj(Map<String, Object> param) throws DataAccessException;

    List<Comment> findListByObjGroupByContent(Map<String, Object> param) throws DataAccessException;

    int queryCommentCount(Map<String, Object> param) throws DataAccessException;

    List<CommentStarGroup> findStarByGroup(Map<String, Object> param) throws DataAccessException;

    /**
     * @description 根据uid,content去重计算数量
     * <AUTHOR>
     * @time 2019年2月27日 上午10:13:27
     * */
    int queryCommentCountGroupByContent(Map<String, Object> param) throws DataAccessException;

    boolean inCreThumbUpNum(Long id) throws DataAccessException;

    List<CommentElement> findListByUid(Map<String, Object> param) throws DataAccessException;

    int updateBatch(Map<String, Object> param) throws DataAccessException;

    List<Comment> findCommentListByIdList(Map<String, Object> param) throws DataAccessException;
    boolean minusThumbUpNum(Long id) throws DataAccessException;

    List<MyComment> findMyComments(Map<String, Object> param) throws DataAccessException;


    List<MyComment> findListByObjGroupByMyContent(Map<String, Object> param) throws DataAccessException;
}
