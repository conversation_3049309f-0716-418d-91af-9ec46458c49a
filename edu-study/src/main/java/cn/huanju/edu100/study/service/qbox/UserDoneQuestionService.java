package cn.huanju.edu100.study.service.qbox;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.questionBox.UserDoneQuestion;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 18/10/30
 */
public interface UserDoneQuestionService extends BaseService<UserDoneQuestion> {

    @Override
    Long save(UserDoneQuestion userDoneQuestion) throws DataAccessException ;

    UserDoneQuestion getByUidQboxIdAndKey(Long uid, Long qboxId, String key) throws DataAccessException;

    List<UserDoneQuestion> getByUidQboxId(Long uid, Long qboxId) throws DataAccessException;

    List<UserDoneQuestion> getByUidQboxIdAndKeyLike(Long uid, Long qboxId, String key) throws DataAccessException;

    List<UserDoneQuestion> getByUidQboxIdListAndKeyLike(Long uid, List<Long> boxIdList, String key) throws DataAccessException;

    List<UserDoneQuestion> getByUidAndQboxId(Long uid, Long qboxId) throws DataAccessException;

    List<String> getKeysByUidQAndboxId(Long uid, Long qboxId) throws DataAccessException;
    /**
     * 这批数据库的uid和qboxId应该是一样的
     * @param userDoneQuestionList
     * @throws DataAccessException
     */
    void batchSave(Long uid, Long questionBoxId, List<UserDoneQuestion> userDoneQuestionList) throws DataAccessException ;

    List<UserDoneQuestion> getByUidAndBoxIds(Long uid, List<Long> boxIds) throws DataAccessException;

    List<UserDoneQuestion> getAllBoxIdByUid(Long uid) throws DataAccessException;
}
