package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.HomeworkSyncdataResid;

import java.util.List;

public interface HomeworkSyncdataResidDao extends CrudDao<HomeworkSyncdataResid> {

    List<HomeworkSyncdataResid> getDifferenceResid(Integer productType) throws DataAccessException;

}
