/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.study.model.tutor.TutorStuKnowledgeFavorite;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;
import java.util.List;

/**
 * 学生微课班知识点收藏DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public interface TutorStuKnowledgeFavoriteDao extends CrudDao<TutorStuKnowledgeFavorite> {

    List<TutorStuKnowledgeFavorite> getByUidAndKIds(Long uid, Long weikeId, List<Long> kIds) throws DataAccessException;

    boolean updateStatus(Long id, Integer status) throws DataAccessException;

    List<TutorSectionTask> listCollectWkTask(Long uid, Long weikeId, Integer type) throws DataAccessException;

    boolean insertBatch(Collection<TutorStuKnowledgeFavorite> favorites) throws DataAccessException;
}