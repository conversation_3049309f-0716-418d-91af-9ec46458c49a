package cn.huanju.edu100.study.gpt;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/7 17:48
 * @description
 */
public interface GptChatService {
    public interface StreamObserver {
        void onNext(String value);

        void onError(Throwable t);

        void onCompleted();
    }
    @Data
    public static class ChatParam {
        private String userKey;
        private String systemPrompt;
        private String userPrompt;
        /**
         * gpt 专门的配置信息
         */
        private String gptConfigJson;
    }

    void streamChat(ChatParam param, StreamObserver streamObserver);
}
