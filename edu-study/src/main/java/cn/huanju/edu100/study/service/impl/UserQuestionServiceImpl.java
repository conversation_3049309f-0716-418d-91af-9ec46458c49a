package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.UserErrorQuestionDao;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.UserErrorQuestion;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import cn.huanju.edu100.study.model.UserSubErrorQuestion;
import cn.huanju.edu100.study.model.goods.Product;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserQuestionService;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.service.UserSubErrorQuestionService;
import cn.huanju.edu100.study.util.Consts;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 用户错题集Service
 * <AUTHOR>
 * @version 2015-05-12
 */
@Service
public class UserQuestionServiceImpl extends BaseServiceImpl<UserErrorQuestionDao, UserErrorQuestion> implements UserQuestionService {

    @Autowired
    private UserSubErrorQuestionService userSubErrorQuestionService;
    @Autowired
    private KnowledgeResource knowledgeResource;
    @Autowired
    private GoodsResource goodsResource;
    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    private static final ExecutorService executor = Executors.newFixedThreadPool(4);

    private static String IS_STOP_TRANSFER_USERERRORQUESTION = "is_stop_transfer_usererrorquestion";//是否停止进行迁移

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void saveErrorQuestion(UserErrorQuestion errorQuestion) throws DataAccessException {
        if (errorQuestion == null || errorQuestion.getUid() == null || errorQuestion.getUid() == 0
                || errorQuestion.getQuestionId() == null || errorQuestion.getQuestionId() == 0
                || errorQuestion.getTopicId() == null || errorQuestion.getTopicId() == 0) {
            throw new DataAccessException("save error uid or question_id not found");
        }
        if (errorQuestion.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON) {
        	errorQuestion.setLessonId(errorQuestion.getObjId());
        	errorQuestion.setParagraphId(0L);
		}else if (errorQuestion.getObjType() == UserHomeWorkAnswer.HomeWorkType.PARAGRAPH) {
			errorQuestion.setLessonId(0L);
			errorQuestion.setParagraphId(errorQuestion.getObjId());
		}
        UserErrorQuestion oldErrorQuestion = dao.get(errorQuestion);
        if (oldErrorQuestion != null) {
            errorQuestion.setId(oldErrorQuestion.getId());
        }
        save(errorQuestion);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void removeErrorQuestion(UserErrorQuestion errorQuestion) throws DataAccessException {
        //TODO 这里要确认一下，是否是只要用户作答对了错题集中的某个问题，就从错题集中去掉，
    	// 而不care这道题是在哪个载体上面回答的？
    	if (errorQuestion == null || errorQuestion.getUid() == null || errorQuestion.getUid() == 0
                || errorQuestion.getQuestionId() == null || errorQuestion.getQuestionId() == 0
                || errorQuestion.getTopicId() == null || errorQuestion.getTopicId() == 0) {
            throw new DataAccessException("removeErrorQuestion error uid or question_id or topic_id not found");
        }

    	if ((errorQuestion.getParagraphId() != null && errorQuestion.getParagraphId() > 0l) ||
    			(errorQuestion.getLessonId() != null && errorQuestion.getLessonId() > 0l)) {
			dao.removeErrQuestionWithlesson(errorQuestion);
		}else {
			dao.removeErrQuestion(errorQuestion);
		}
    }

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public Integer removeUserErrorQuestionList(List<UserErrorQuestion> errorList)
			throws DataAccessException {
		return dao.removeUserErrorQuestionList(errorList);
	}

    @Override
    public List<UserErrorQuestion> findHomeworkErrorQuestionList(Map<String, Object> params) throws DataAccessException {
        return dao.findHomeworkErrorQuestionList(params);
    }

    @Override
    public void transferUserErrorQuestion() {
        if (!getNoStopTransferUserErrorQuestion()) {
            logger.info("停止user_error_question 的迁移. ");
            return;
        }
        executor.submit(new Callable<Boolean>() {
            @Override
            public Boolean call() throws Exception {
                transferUserErrorQuestionStart();
                return true;
            }
        });
    }

    private void transferUserErrorQuestionStart() throws InterruptedException {
        logger.info("transferUserErrorQuestionStart start");
        int pageSize = 600;
        String startIdKey = "UserErrorQuestionStartId";
        String startIdStr = compatableRedisClusterClient.get(startIdKey);
        if (StringUtils.isNotBlank(startIdStr)) {
            Long startId = Long.parseLong(startIdStr);//正式环境由这个id=201840000开始
            boolean flag = true;//标识，是否可以继续遍历获取userErrorQuestion数据
            while (flag) {
                Map<String, Object> params = new HashMap<>();
                params.put("startId", startId);
                params.put("from", 0);
                params.put("pageSize", pageSize);
                params.put("orderBy", "id asc");
                List<UserErrorQuestion> userErrorQuestions = Lists.newArrayList();
                try {
                    logger.info("查询userErrorQuestion，startId；{}", startId);
                    userErrorQuestions = findHomeworkErrorQuestionList(params);
                } catch (DataAccessException e) {
                    e.printStackTrace();
                }
                if (CollectionUtils.isNotEmpty(userErrorQuestions)) {
                    startId = userErrorQuestions.get(userErrorQuestions.size() - 1).getId();

                    Set<Long> productIds = Sets.newHashSet();
                    for (UserErrorQuestion userErrorQuestion : userErrorQuestions) {
                        if (userErrorQuestion.getProductId() != null && userErrorQuestion.getProductId() > 0) {
                            productIds.add(userErrorQuestion.getProductId());
                        }
                    }
                    Map<Long, Product> productIdMap = goodsResource.getProductsByIdList(Lists.newArrayList(productIds));

                    Set<Long> questionIds = Sets.newHashSet();
                    List<UserSubErrorQuestion> userSubErrorQuestionList = Lists.newArrayList();
                    for (UserErrorQuestion userErrorQuestion : userErrorQuestions) {
                        Long goodsId = userErrorQuestion.getGoodsId();
                        Long productId = userErrorQuestion.getProductId();
                        Long questionId = userErrorQuestion.getQuestionId();
                        if (goodsId == null || goodsId.longValue() <= 0) {
                            continue;
                        }
                        if (productId == null || productId.longValue() <= 0) {
                            continue;
                        }
                        if (questionId == null || questionId.longValue() <= 0) {
                            continue;
                        }
                        Product product = productIdMap.get(userErrorQuestion.getProductId());
                        if (product == null || product.getType() == null || product.getType() != Consts.ProductType.PRODUCT_ADAPTIVE_LEARNING) {
                            continue;
                        }
                        UserSubErrorQuestion userSubErrorQuestion = new UserSubErrorQuestion();
                        userSubErrorQuestion.setProductType(Consts.ProductType.PRODUCT_ADAPTIVE_LEARNING);
                        userSubErrorQuestion.setUid(userErrorQuestion.getUid());
                        userSubErrorQuestion.setProductId(userErrorQuestion.getProductId());
                        userSubErrorQuestion.setGoodsId(userErrorQuestion.getGoodsId());
                        userSubErrorQuestion.setLessonId(userErrorQuestion.getLessonId());
                        userSubErrorQuestion.setQuestionId(userErrorQuestion.getQuestionId());
                        userSubErrorQuestion.setTopicId(userErrorQuestion.getTopicId());
                        userSubErrorQuestion.setLastErrorAnswer(userErrorQuestion.getLastErrorAnswer());
                        userSubErrorQuestion.setLastErrorTime(userErrorQuestion.getLastErrorTime());
                        userSubErrorQuestion.setCreateDate(userErrorQuestion.getCreateDate());
                        userSubErrorQuestionList.add(userSubErrorQuestion);
                        questionIds.add(userErrorQuestion.getQuestionId());
                    }
                    logger.info("userSubErrorQuestionList size is :{}", userSubErrorQuestionList.size());
                    try {
                        if (CollectionUtils.isNotEmpty(userSubErrorQuestionList)) {
                            List<Question> questionList = knowledgeResource.getQuestionByIds(Lists.newArrayList(questionIds));
                            Map<Long, Question> questionMap = Maps.newHashMap();
                            if (CollectionUtils.isNotEmpty(questionList)) {
                                questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Question -> Question, (key1, key2) -> key2));
                            }
                            for (UserSubErrorQuestion userSubErrorQuestion : userSubErrorQuestionList) {
                                Question question = questionMap.get(userSubErrorQuestion.getQuestionId());
                                if (question != null) {
                                    userSubErrorQuestion.setCategoryId(question.getCategoryId());
                                    userSubErrorQuestion.setQtype(question.getQtype());
                                }
                                userSubErrorQuestionService.saveSubErrorQuestion(userSubErrorQuestion);
                            }
                        }
                    } catch (Exception ex) {
                        logger.info("insert subQuestion error:{}", ex);
                    }
                    logger.info("user_error_question startId:{}", startId);
                    compatableRedisClusterClient.set(startIdKey, startId.toString());
                } else {
                    flag = false;
                }
                Thread.sleep(500);

                if (!getNoStopTransferUserErrorQuestion()) {
                    logger.info("停止user_error_question 的迁移. ");
                    flag = false;
                }
            }
        } else {
            logger.error("UserErrorQuestionStartId is null.");
        }
        logger.info("transferUserErrorQuestionStart end");
    }

    private boolean getNoStopTransferUserErrorQuestion() {
        /**人为停止迁移*/
        String isStop = compatableRedisClusterClient.get(IS_STOP_TRANSFER_USERERRORQUESTION);
        if (StringUtils.isNotBlank(isStop) && isStop.equals("1")) {
            return false;
        }
        return true;
    }

}
