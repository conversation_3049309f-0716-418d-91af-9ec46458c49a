/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.onetoone.VTeacherDao;
import cn.huanju.edu100.study.model.onetoone.VTeacher;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 留学老师DAO接口
 * <AUTHOR>
 * @version 2016-09-09
 */
public class VTeacherIbatisImpl extends CrudIbatisImpl2<VTeacher> implements
		VTeacherDao {

	public VTeacherIbatisImpl() {
		super("VTeacher");
	}

    @Override
    public List<VTeacher> findListByIds(List<Long> ids) throws DataAccessException {

        if (CollectionUtils.isEmpty(ids)) {
            logger.error("findListByIds {} error, parameter ids is empty, entity:{}", namespace, ids);
            throw new DataAccessException(String.format(
                    "findListByIds %s error, parameter ids is empty, entity: %s", namespace, ids));
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = Maps.newHashMap();
            param.put("ids", ids);
            return sqlMap.queryForList(namespace.concat(".findListByParam"), param);
        } catch (SQLException e) {
            logger.error("findListByIds {} SQLException. ids:{}", namespace, ids, e);
            throw new DataAccessException(String.format("findListByIds SQLException error :%s", e.getMessage()));
        }
    }

    @Override
    public List<VTeacher> findListByUids(List<Long> uids) throws DataAccessException {

        if (CollectionUtils.isEmpty(uids)) {
            logger.error("findListByIds {} error, parameter ids is empty, entity:{}", namespace, uids);
            throw new DataAccessException(String.format(
                    "findListByIds %s error, parameter ids is empty, entity: %s", namespace, uids));
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = Maps.newHashMap();
            param.put("uids", uids);
            return sqlMap.queryForList(namespace.concat(".findListByParam"), param);
        } catch (SQLException e) {
            logger.error("findListByIds {} SQLException. ids:{}", namespace, uids, e);
            throw new DataAccessException(String.format("findListByIds SQLException error :%s", e.getMessage()));
        }
    }
}
