/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.CommentThumb;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 评论点赞DAO接口
 * <AUTHOR>
 * @version 2018-03-06
 */
public interface CommentThumbDao extends CrudDao<CommentThumb> {

	List<CommentThumb> findUserThumbUpList(Long uid, List<Long> commentIdList) throws DataAccessException;
	
}