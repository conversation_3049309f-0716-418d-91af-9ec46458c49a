/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 题目组与题目关系Entity
 * <AUTHOR>
 * @version 2015-05-14
 */
public class QuestionGroupRelation extends DataEntity<QuestionGroupRelation> {
	
	private static final long serialVersionUID = 1L;
	private Long paperId;		// paper_id
	private Long groupId;		// 题目组id
	private Long questionId;		// 题目id
	private Integer seq;		// 题目顺序，从0开始
	private Double questionScore;		// question_score
	private String topicScore;		// topic_score
	private Long limitTime;		// 单位/分钟，限定时间，（0：不限时间，非0：答题限定的时间）
	
	public QuestionGroupRelation() {
		super();
	}

	public String getTopicScore() {
		return topicScore;
	}

	public void setTopicScore(String topicScore) {
		this.topicScore = topicScore;
	}

	public Long getPaperId() {
		return paperId;
	}
	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}
	public Long getGroupId() {
		return groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public Long getQuestionId() {
		return questionId;
	}
	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}
	public Integer getSeq() {
		return seq;
	}
	public void setSeq(Integer seq) {
		this.seq = seq;
	}
	public Double getQuestionScore() {
		return questionScore;
	}
	public void setQuestionScore(Double questionScore) {
		this.questionScore = questionScore;
	}
	public Long getLimitTime() {
		return limitTime;
	}
	public void setLimitTime(Long limitTime) {
		this.limitTime = limitTime;
	}
}