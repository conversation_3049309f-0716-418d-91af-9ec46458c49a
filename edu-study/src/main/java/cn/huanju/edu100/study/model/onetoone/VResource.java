package cn.huanju.edu100.study.model.onetoone;


import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.util.upload.OssUtil;

/**
 * 留学资源Entity
 * <AUTHOR>
 * @version 2016-12-12
 */
public class VResource extends DataEntity<VResource> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 原文件名
	private String filename;		// 上传后的文件名
	private Integer status;		// 0: 上传中 1： 可用 2:关闭
	private Integer type;		// 0: pdf 1:图片
	private Integer userType;		// 0: 老师 1: 督导
//	private Long schId;		// 机构id
	private String url;
	
	private static String PICTURE_DOMAIN = "http://edu24ol.bs2cdn.100.com";
	
	private static final String RESOURCE_VIDEO_DOMAIN = "http://edu100hqvideo.bs2cdn.100.com";
	
	public VResource() {
		super();
	}

	public VResource(Long id){
		super(id);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}
	
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = OssUtil.bs2UrlConvertToOssUrl(url);
    }
}