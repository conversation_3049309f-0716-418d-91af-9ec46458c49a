package cn.huanju.edu100.study.service.impl.evaluation;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.evaluation.EvaluationUserAnswerDao;
import cn.huanju.edu100.study.model.Paper;
import cn.huanju.edu100.study.model.evaluation.EvaluationUserAnswer;
import cn.huanju.edu100.study.model.evaluation.EvaluationUserBaseAnswerDetail;
import cn.huanju.edu100.study.model.goods.Goods;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.resource.StustampResource;
import cn.huanju.edu100.study.service.UserAnswerService;
import cn.huanju.edu100.study.service.UserAnswerSumService;
import cn.huanju.edu100.study.service.evaluation.EvaluationUserAnswerService;
import cn.huanju.edu100.study.service.evaluation.EvaluationUserBaseAnswerDetailService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.enums.InspectionContentSecondEnum;
import cn.huanju.edu100.stustamp.dto.AlUserAssessmentAnswerDTO;
import cn.huanju.edu100.stustamp.dto.PageDTO;
import cn.huanju.edu100.stustamp.dto.query.AlUserAssessmentAnswerQuery;
import cn.huanju.edu100.thrift.client.dto.EvaluationBaseQuestionDTO;
import cn.huanju.edu100.thrift.client.dto.EvaluationBaseQuestionOptionsDTO;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.IdUtils;
import cn.huanju.edu100.util.IdWorker;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.gson.reflect.TypeToken;
import com.hqwx.study.dto.*;
import com.hqwx.study.dto.query.EvaluationUserAnswerQuery;
import com.hqwx.study.dto.query.EvaluationUserBaseAnswerDetailQuery;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.thrift.client.param.EvaluationBaseQuestionQuery;
import com.hqwx.userevent.collect.client.dto.StudyBaseInfoEntity;
import com.hqwx.userevent.collect.client.dto.StudyEvaluationEntity;
import com.hqwx.userevent.collect.client.dto.UserBaseInfoEntity;
import com.hqwx.userevent.collect.client.message.UserEventPublisher;
import com.hqwx.userevent.collect.client.message.param.UserEventMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EvaluationUserAnswerServiceImpl extends BaseServiceImpl<EvaluationUserAnswerDao, EvaluationUserAnswer> implements EvaluationUserAnswerService {

    final static IdWorker idWorker = new IdWorker(0, 8);

    private static ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("evaluation-thread-pool-%d").build();
    private static ExecutorService executorService = new ThreadPoolExecutor(5, 50, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(1024), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;
    @Autowired
    private StustampResource stustampResource;
    @Resource
    private GoodsResource goodsResource;
    @Autowired
    private EvaluationUserBaseAnswerDetailService evaluationUserBaseAnswerDetailService;
    @Autowired
    private UserAnswerService userAnswerService;
    @Autowired
    private UserAnswerSumService userAnswerSumService;
    @Autowired
    private KnowledgeResource knowledgeResource;
    @Resource
    private UserEventPublisher userEventPublisher;

    @Override
    public List<EvaluationUserAnswerDTO> getEvaluationUserAnswerList(EvaluationUserAnswerQuery query) throws DataAccessException {
        if (query == null) {
            return null;
        }
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy(" a.id desc ");
        }
        List<EvaluationUserAnswerDTO> evaluationUserAnswerList = dao.getEvaluationUserAnswerList(query);
        if (query != null && query.getIsGetAnswerDetail() == 1 && CollectionUtils.isNotEmpty(evaluationUserAnswerList)) {
            setAnswerDetail(query, evaluationUserAnswerList);
        }
        return evaluationUserAnswerList;
    }

    private void setAnswerDetail(EvaluationUserAnswerQuery query, List<EvaluationUserAnswerDTO> evaluationUserAnswerList) throws DataAccessException {
        List<Long> evaluationUserAnswerIdList = evaluationUserAnswerList.stream().filter(a -> a.getEvaluationType() != null && a.getEvaluationType().intValue() == Consts.EvaluationType.BASE).map(EvaluationUserAnswerDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(evaluationUserAnswerIdList)) {
            EvaluationUserBaseAnswerDetailQuery detailQuery = new EvaluationUserBaseAnswerDetailQuery();
            detailQuery.setUid(query.getUid());
            detailQuery.setEvaluationAnswerIdList(evaluationUserAnswerIdList);
            detailQuery.setOrderBy(" a.id asc ");
            List<EvaluationUserBaseAnswerDetailDTO> evaluationUserBaseAnswerDetailList = evaluationUserBaseAnswerDetailService.getEvaluationUserBaseAnswerDetailList(detailQuery);
            if (CollectionUtils.isNotEmpty(evaluationUserBaseAnswerDetailList)) {
                Map<Long, List<EvaluationUserBaseAnswerDetailDTO>> baseAnswerDetailMap = evaluationUserBaseAnswerDetailList.stream().collect(Collectors.groupingBy(EvaluationUserBaseAnswerDetailDTO::getEvaluationAnswerId));
                for (EvaluationUserAnswerDTO item : evaluationUserAnswerList) {
                    if (item.getEvaluationType() != null && item.getEvaluationType().intValue() == Consts.EvaluationType.BASE) {
                        item.setEvaluationUserBaseAnswerDetailList(baseAnswerDetailMap.get(item.getId()));
                    }
                }
            }
        }

        List<Long> orgAnswerIdList = evaluationUserAnswerList.stream().filter(a -> a.getOrgAnswerId() != null && ((a.getEvaluationType() != null && a.getEvaluationType().intValue() == Consts.EvaluationType.MAJOR) || a.getBusType() == 1)).map(EvaluationUserAnswerDTO::getOrgAnswerId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orgAnswerIdList)) {
            Map<String, Object> reqDetailParams = Maps.newHashMap();
            reqDetailParams.put("uid", query.getUid());
            reqDetailParams.put("answerIds", orgAnswerIdList);
            List<UserAnswerDetail> userAnswerDetailList = userAnswerSumService.getQuestionListByAnswerIds(reqDetailParams);
            if (CollectionUtils.isNotEmpty(userAnswerDetailList)) {
                Type type = new com.google.common.reflect.TypeToken<List<UserAnswerDetailDTO>>() {
                }.getType();
                String rsStr = GsonUtil.getGenericGson().toJson(userAnswerDetailList);
                List<UserAnswerDetailDTO> userAnswerDetailDTOList = GsonUtil.getGenericGson().fromJson(rsStr, type);
                Map<Long, List<UserAnswerDetailDTO>> userAnswerDetailDTOMap = userAnswerDetailDTOList.stream().collect(Collectors.groupingBy(UserAnswerDetailDTO::getUserAnswerId));
                for (EvaluationUserAnswerDTO item : evaluationUserAnswerList) {
                    if (item.getOrgAnswerId() != null && ((item.getEvaluationType() != null && item.getEvaluationType().intValue() == Consts.EvaluationType.MAJOR) || item.getBusType() == 1)) {
                        item.setUserAnswerDetailList(userAnswerDetailDTOMap.get(item.getOrgAnswerId()));
                    }
                }
            }
        }
    }

    @Override
    public EvaluationSubmitRetDTO submitEvaluationQuestion(EvaluationSubmitDTO evaluationSubmitDTO) throws DataAccessException {
        EvaluationSubmitRetDTO evaluationSubmitRetDTO = new EvaluationSubmitRetDTO();
        Type type = new TypeToken<List<UserAnswerDetail>>() {
        }.getType();
        String baseAnswerDetails = evaluationSubmitDTO.getBaseAnswerDetails();
        if (StringUtils.isNotBlank(baseAnswerDetails)) {
            List<UserAnswerDetail> baseAnswerDetailList = GsonUtil.getGson().fromJson(baseAnswerDetails, type);
            if (CollectionUtils.isNotEmpty(baseAnswerDetailList)) {
                EvaluationUserAnswer baseEvaluationUserAnswer = saveBaseEvaluationUserAnswer(evaluationSubmitDTO, baseAnswerDetailList);
                saveBaseAnswerDetailList(baseAnswerDetailList, evaluationSubmitDTO.getUid(), baseEvaluationUserAnswer.getId());
                evaluationSubmitRetDTO.setBaseEvaluationUserAnswerId(baseEvaluationUserAnswer.getId() + "");

                /*后端埋点-入学测评信息*/
                try{
                    executorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            setUserEventEvaluation(evaluationSubmitDTO, baseAnswerDetailList);//入学测评埋点
                        }
                    });
                } catch (Exception e){
                    log.error("setUserEventEvaluation fail! ", e);
                }
            }
        }

        String majorAnswerDetails = evaluationSubmitDTO.getMajorAnswerDetails();
        if (StringUtils.isNotBlank(majorAnswerDetails)) {
            List<UserAnswerDetail> majorAnswerDetailList = GsonUtil.getGson().fromJson(majorAnswerDetails, type);
            if (CollectionUtils.isNotEmpty(majorAnswerDetailList)) {
                UserAnswer userAnswer = saveUserAnswer(evaluationSubmitDTO, majorAnswerDetailList);
                evaluationSubmitRetDTO.setUserAnswerId(userAnswer.getId() + "");
                EvaluationUserAnswer majorEvaluationUserAnswer = saveMajorEvaluationUserAnswer(evaluationSubmitDTO, userAnswer);
                evaluationSubmitRetDTO.setMajorEvaluationUserAnswerId(majorEvaluationUserAnswer.getId() + "");
            }
        }
        return evaluationSubmitRetDTO;
    }

    private void setUserEventEvaluation(EvaluationSubmitDTO evaluationSubmitDTO, List<UserAnswerDetail> baseAnswerDetailList) {
        Goods goods = goodsResource.getGoodsById(evaluationSubmitDTO.getGoodsId());
        if (goods == null) {
            return;
        }
        Integer goodsCategory = goods.getGoodsCategory();
        Long secondCategory = goods.getSecondCategory();
        EvaluationBaseQuestionQuery query = new EvaluationBaseQuestionQuery();
        query.setSecondCategory(secondCategory);
        query.setSuitGoodsType(goodsCategory + "");
        List<EvaluationBaseQuestionDTO> evaluationBaseQuestionList = knowledgeResource.getEvaluationBaseQuestionList(query);
        if (CollectionUtils.isEmpty(evaluationBaseQuestionList)) {
            return;
        }
        Map<Long, EvaluationBaseQuestionDTO> baseQuestionDTOMap = evaluationBaseQuestionList.stream().collect(Collectors.toMap(EvaluationBaseQuestionDTO::getId, Function.identity(), (s1, s2) -> s1));

        EvaluationUserDataDTO evaluationUserDataDTO = new EvaluationUserDataDTO();
        for (UserAnswerDetail userAnswerDetail : baseAnswerDetailList) {
            Long questionId = userAnswerDetail.getQuestionId();
            EvaluationBaseQuestionDTO evaluationBaseQuestionDTO = baseQuestionDTOMap.get(questionId);
            if (evaluationBaseQuestionDTO == null) {
                continue;
            }
            String answer = getAnswerStr(userAnswerDetail, evaluationBaseQuestionDTO);
            Integer inspectionContentSecondType = evaluationBaseQuestionDTO.getInspectionContentSecondType();
            if (inspectionContentSecondType == null) {
                continue;
            }
            setEvaluationUserDataDTO(answer, inspectionContentSecondType, evaluationUserDataDTO);
        }

        sendUserEventMessage(evaluationSubmitDTO, evaluationUserDataDTO);//发送埋点信息
    }

    /**
     * 发送埋点信息
     */
    private void sendUserEventMessage(EvaluationSubmitDTO evaluationSubmitDTO, EvaluationUserDataDTO evaluationUserDataDTO) {
        UserBaseInfoEntity.UserBaseInfo.Builder userInfoBuilder = UserBaseInfoEntity.UserBaseInfo.newBuilder();
        userInfoBuilder.setUid(evaluationSubmitDTO.getUid());
        if (evaluationSubmitDTO.getSchId() != null) {
            userInfoBuilder.setSchId(evaluationSubmitDTO.getSchId());
        }
        StudyEvaluationEntity.StudyEvaluation.Builder eventDataBuilder = StudyEvaluationEntity.StudyEvaluation.newBuilder();
        eventDataBuilder.setUserInfo(userInfoBuilder.build());
        if (evaluationUserDataDTO.getApplyProvince() != null) {
            eventDataBuilder.setApplyProvince(evaluationUserDataDTO.getApplyProvince());// 申报省份
        }

        // 用户社会属性信息，学历、职业、行业、工作单位等
        UserBaseInfoEntity.UserSocialInfo.Builder userSocialBuilder = UserBaseInfoEntity.UserSocialInfo.newBuilder();
        if (evaluationUserDataDTO.getCompany() != null) {
            userSocialBuilder.setCompany(evaluationUserDataDTO.getCompany());
        }
        if (evaluationUserDataDTO.getPosition() != null) {
            userSocialBuilder.setPosition(evaluationUserDataDTO.getPosition());
        }
        if (evaluationUserDataDTO.getProfessional() != null) {
            userSocialBuilder.setProfessional(evaluationUserDataDTO.getProfessional());
        }
        if (evaluationUserDataDTO.getEducation() != null) {
            userSocialBuilder.setEducation(evaluationUserDataDTO.getEducation());
        }
        if (evaluationUserDataDTO.getGraduationTime() != null) {
            userSocialBuilder.setGraduationTime(evaluationUserDataDTO.getGraduationTime());
        }
        if (evaluationUserDataDTO.getIndustry() != null) {
            userSocialBuilder.setIndustry(evaluationUserDataDTO.getIndustry());
        }
        if (evaluationUserDataDTO.getYearOfEmployment() != null) {
            userSocialBuilder.setYearOfEmployment(evaluationUserDataDTO.getYearOfEmployment());
        }
        if (evaluationUserDataDTO.getWorkingYears() != null) {
            userSocialBuilder.setWorkingYears(evaluationUserDataDTO.getWorkingYears());
        }
        eventDataBuilder.setUserSocial(userSocialBuilder.build());

        // 学习信息
        StudyBaseInfoEntity.StudyBaseInfo.Builder studyInfoBuilder = StudyBaseInfoEntity.StudyBaseInfo.newBuilder();
        if (evaluationUserDataDTO.getStudyHis() != null) {
            studyInfoBuilder.setStudyHis(evaluationUserDataDTO.getStudyHis());
        }
        if (evaluationUserDataDTO.getStudyWdZone() != null) {
            studyInfoBuilder.setStudyWdZone(evaluationUserDataDTO.getStudyWdZone());
        }
        if (evaluationUserDataDTO.getStudyRdZone() != null) {
            studyInfoBuilder.setStudyRdZone(evaluationUserDataDTO.getStudyRdZone());
        }
        if (evaluationUserDataDTO.getWeekLearnDuration() != null) {
            studyInfoBuilder.setWeekLearnDuration(evaluationUserDataDTO.getWeekLearnDuration());
        }
        if (evaluationUserDataDTO.getExamPurpose() != null) {
            studyInfoBuilder.setExamPurpose(evaluationUserDataDTO.getExamPurpose());
        }
        if (evaluationUserDataDTO.getStudyAbility() != null) {
            studyInfoBuilder.setStudyAbility(evaluationUserDataDTO.getStudyAbility());
        }
        eventDataBuilder.setStudyInfo(studyInfoBuilder.build());

        UserEventMessage userEvent = new UserEventMessage();
        userEvent.setData(eventDataBuilder.build());
        userEventPublisher.publishMessage(userEvent);// 数据组装完成后 发布消息
    }

    private void setEvaluationUserDataDTO(String answer, Integer inspectionContentSecondType, EvaluationUserDataDTO evaluationUserDataDTO) {
        int inspectionContentSecondInt = inspectionContentSecondType.intValue();
        if (inspectionContentSecondInt == InspectionContentSecondEnum.education.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setEducation(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.major.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setProfessional(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.graduation_year.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setGraduationTime(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.practice_year.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setYearOfEmployment(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.industry.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setIndustry(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.career.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setPosition(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.work_unit.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setCompany(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.exam_province.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setApplyProvince(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.exam_objective.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setExamPurpose(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.study_base.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setStudyHis(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.work_study_time.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setStudyWdZone(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.week_study_time.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setStudyRdZone(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.weekly_study_duration.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setWeekLearnDuration(answer);
            }
        } else if (inspectionContentSecondInt == InspectionContentSecondEnum.study_capacity.getVal()) {
            if (StringUtils.isNotBlank(answer)) {
                evaluationUserDataDTO.setStudyAbility(answer);
            }
        }
    }

    private String getAnswerStr(UserAnswerDetail userAnswerDetail, EvaluationBaseQuestionDTO evaluationBaseQuestionDTO) {
        String[] answerStr = userAnswerDetail.getAnswer();
        String answer = null;
        Integer qtype = evaluationBaseQuestionDTO.getQtype();
        if (qtype.intValue() == Consts.Question_QType.DAN_XUAN) {
            if (answerStr.length > 0) {
                answer = answerStr[0];
                List<EvaluationBaseQuestionOptionsDTO> questionOptionsList = evaluationBaseQuestionDTO.getQuestionOptionsList();
                if (CollectionUtils.isNotEmpty(questionOptionsList)) {
                    for (EvaluationBaseQuestionOptionsDTO item : questionOptionsList) {
                        if (answer.equals(item.getSeq())) {
                            answer = item.getContent().replaceAll("<p>", "").replaceAll("</p>", "");
                        }
                    }
                }
            }
        } else if (qtype.intValue() == Consts.Question_QType.TIAN_KONG || qtype.intValue() == Consts.Question_QType.WEN_DA) {
            if (answerStr.length > 0) {
                answer = answerStr[0];
                answer = answer.replaceAll("<p>", "").replaceAll("</p>", "");
            }
        }
        return answer;
    }

    private UserAnswer saveUserAnswer(EvaluationSubmitDTO evaluationSubmitDTO, List<UserAnswerDetail> majorAnswerDetailList) throws DataAccessException {
        Date curDate = new Date();
        UserAnswer userAnswer = new UserAnswer();
        userAnswer.setUid(evaluationSubmitDTO.getUid());
        userAnswer.setPaperId(evaluationSubmitDTO.getPaperId());
        if (evaluationSubmitDTO.getPaperId() != null) {
            Paper paper = knowledgeResource.getPaperInfoById(evaluationSubmitDTO.getPaperId());
            if (paper != null) {
                userAnswer.setPaperType(paper.getType());
            }
        }
        userAnswer.setUsetime(evaluationSubmitDTO.getUsetime() != null ? evaluationSubmitDTO.getUsetime().longValue() : 0L);
        userAnswer.setAnswerNum(0L);
        userAnswer.setStartTime(evaluationSubmitDTO.getStartTime());
        userAnswer.setEndTime(evaluationSubmitDTO.getEndTime());
        userAnswer.setGoodsId(evaluationSubmitDTO.getGoodsId());
        userAnswer.setPlatForm(evaluationSubmitDTO.getPlatForm());
        userAnswer.setAppid(evaluationSubmitDTO.getAppid());
        userAnswer.setCreateDate(curDate);
        userAnswer.setUpdateDate(curDate);
        userAnswer.setObjType(UserAnswer.ObjType.EVALUATION_PAPER);
        userAnswer.setSource(UserAnswer.Source.EVALUATION_PAPER);
        userAnswer.setAnswerDetail(majorAnswerDetailList);
        userAnswer.setIsSubmit(1);
        List<UserAnswerDetail> userAnswerDetailList = userAnswerService.submit(userAnswer, 0);
        return userAnswer;
    }

    private void saveBaseAnswerDetailList(List<UserAnswerDetail> userAnswerDetailList, Long uid, Long evaluationAnswerId) throws DataAccessException {
        if (CollectionUtils.isNotEmpty(userAnswerDetailList)) {
            List<EvaluationUserBaseAnswerDetail> baseAnswerDetailList = Lists.newArrayList();
            Date curDate = new Date();
            for (UserAnswerDetail userAnswerDetail : userAnswerDetailList) {
                EvaluationUserBaseAnswerDetail baseAnswerDetail = new EvaluationUserBaseAnswerDetail();
                baseAnswerDetail.setId(idWorker.nextId());
                baseAnswerDetail.setUid(uid);
                baseAnswerDetail.setEvaluationQuestionId(userAnswerDetail.getQuestionId());
                baseAnswerDetail.setEvaluationAnswerId(evaluationAnswerId);
                baseAnswerDetail.setAnswer(GsonUtil.toJson(userAnswerDetail.getAnswer()));
                baseAnswerDetail.setCreateDate(curDate);
                baseAnswerDetail.setUpdateDate(curDate);
                baseAnswerDetailList.add(baseAnswerDetail);
            }
            evaluationUserBaseAnswerDetailService.insertBatch(baseAnswerDetailList);
        }
    }

    private EvaluationUserAnswer saveBaseEvaluationUserAnswer(EvaluationSubmitDTO evaluationSubmitDTO, List<UserAnswerDetail> userAnswerDetailList) throws DataAccessException {
        Integer answerNum = 0;
        if (CollectionUtils.isNotEmpty(userAnswerDetailList)) {
            for (UserAnswerDetail userAnswerDetail : userAnswerDetailList) {
                if (userAnswerDetail.getAnswer() != null && userAnswerDetail.getAnswer().length > 0) {
                    answerNum++;
                }
            }
        }
        Date curDate = new Date();
        EvaluationUserAnswer evaluationUserAnswer = new EvaluationUserAnswer();
        Long id = idWorker.nextId();
        evaluationUserAnswer.setId(id);
        evaluationUserAnswer.setUid(evaluationSubmitDTO.getUid());
        evaluationUserAnswer.setEvaluationType(Consts.EvaluationType.BASE);
        evaluationUserAnswer.setBusType(0);
        evaluationUserAnswer.setPaperId(evaluationSubmitDTO.getPaperId());
        evaluationUserAnswer.setUsetime(evaluationSubmitDTO.getUsetime());
        evaluationUserAnswer.setAnswerNum(answerNum);
        evaluationUserAnswer.setStartTime(evaluationSubmitDTO.getStartTime());
        evaluationUserAnswer.setEndTime(evaluationSubmitDTO.getEndTime());
        evaluationUserAnswer.setSecondCategoryId(evaluationSubmitDTO.getSecondCategoryId());
        evaluationUserAnswer.setGoodsId(evaluationSubmitDTO.getGoodsId());
        evaluationUserAnswer.setPlatForm(evaluationSubmitDTO.getPlatForm());
        evaluationUserAnswer.setAppid(evaluationSubmitDTO.getAppid());
        evaluationUserAnswer.setCreateDate(curDate);
        evaluationUserAnswer.setUpdateDate(curDate);
        dao.insertBatch(Lists.newArrayList(evaluationUserAnswer));
        return evaluationUserAnswer;
    }

    private EvaluationUserAnswer saveMajorEvaluationUserAnswer(EvaluationSubmitDTO evaluationSubmitDTO, UserAnswer userAnswer) throws DataAccessException {
        Date curDate = new Date();
        EvaluationUserAnswer evaluationUserAnswer = new EvaluationUserAnswer();
        Long id = idWorker.nextId();
        evaluationUserAnswer.setId(id);
        evaluationUserAnswer.setUid(evaluationSubmitDTO.getUid());
        evaluationUserAnswer.setEvaluationType(Consts.EvaluationType.MAJOR);
        evaluationUserAnswer.setBusType(0);
        evaluationUserAnswer.setPaperId(evaluationSubmitDTO.getPaperId());
        evaluationUserAnswer.setUsetime(evaluationSubmitDTO.getUsetime());
        evaluationUserAnswer.setAnswerNum(userAnswer.getAnswerNum() == null ? 0 : userAnswer.getAnswerNum().intValue());
        evaluationUserAnswer.setStartTime(evaluationSubmitDTO.getStartTime());
        evaluationUserAnswer.setEndTime(evaluationSubmitDTO.getEndTime());
        evaluationUserAnswer.setSecondCategoryId(evaluationSubmitDTO.getSecondCategoryId());
        evaluationUserAnswer.setGoodsId(evaluationSubmitDTO.getGoodsId());
        evaluationUserAnswer.setPlatForm(evaluationSubmitDTO.getPlatForm());
        evaluationUserAnswer.setAppid(evaluationSubmitDTO.getAppid());
        evaluationUserAnswer.setCreateDate(curDate);
        evaluationUserAnswer.setUpdateDate(curDate);
        evaluationUserAnswer.setOrgAnswerId(userAnswer.getId());
        dao.insertBatch(Lists.newArrayList(evaluationUserAnswer));
        return evaluationUserAnswer;
    }

    /**
     * 数据迁移
     */
    @Override
    public void transferEvaluationUserAnswerList() {
        String key = "AlUserAssessmentAnswerDTOIdKey";
        Long idGt = getLongValInRedis(key);
        AlUserAssessmentAnswerQuery query = new AlUserAssessmentAnswerQuery();
        Integer pageSize = 1000;
        query.setPageSize(pageSize);
        query.setFrom(0);
        query.setOrderBy(" a.id asc ");
        query.setIdGt(idGt);
        PageDTO<AlUserAssessmentAnswerDTO> page = stustampResource.getAlUserAssessmentAnswerPage(query);
        if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
            return;
        }
        long pages = page.getPages();
        for (int i = 0; i < pages; i++) {
            query.setFrom(i * pageSize);
            PageDTO<AlUserAssessmentAnswerDTO> rsPage = stustampResource.getAlUserAssessmentAnswerPage(query);
            if (rsPage == null || CollectionUtils.isEmpty(rsPage.getRecords())) {
                continue;
            }
            List<EvaluationUserAnswer> evaluationUserAnswerList = Lists.newArrayList();
            for (AlUserAssessmentAnswerDTO item : rsPage.getRecords()) {
                EvaluationUserAnswer evaluationUserAnswer = new EvaluationUserAnswer();
                evaluationUserAnswer.setId(item.getId());
                evaluationUserAnswer.setUid(item.getUid());
                evaluationUserAnswer.setEvaluationType(item.getAssessmentType());
                evaluationUserAnswer.setBusType(1);
                evaluationUserAnswer.setPaperId(item.getPaperId());
                evaluationUserAnswer.setUsetime(item.getUsetime());
                evaluationUserAnswer.setAnswerNum(item.getAnswerNum());
                evaluationUserAnswer.setStartTime(item.getStartTime());
                evaluationUserAnswer.setEndTime(item.getEndTime());
                evaluationUserAnswer.setCategoryId(item.getCategoryId());
                if (item.getGoodsId() != null) {
                    evaluationUserAnswer.setGoodsId(item.getGoodsId());
                    Goods goods = goodsResource.getGoodsById(item.getGoodsId());
                    if (goods != null) {
                        evaluationUserAnswer.setSecondCategoryId(goods.getSecondCategory());
                    }
                }
                evaluationUserAnswer.setPlatForm(item.getPlatForm());
                evaluationUserAnswer.setAppid(item.getAppId());
                evaluationUserAnswer.setOrgAnswerId(item.getOrgAnswerId());
                evaluationUserAnswer.setCreateDate(item.getCreateDate());
                evaluationUserAnswer.setUpdateDate(item.getUpdateDate());
                evaluationUserAnswerList.add(evaluationUserAnswer);
            }
            if (CollectionUtils.isNotEmpty(evaluationUserAnswerList)) {
                Map<Long, List<EvaluationUserAnswer>> rsMap = Maps.newHashMap();
                for (EvaluationUserAnswer item : evaluationUserAnswerList) {
                    if (item.getUid() == null) {
                        log.error("EvaluationUserAnswer uid is null. {}", GsonUtil.getGenericGson().toJson(item));
                        continue;
                    }
                    Long uidMod = item.getUid() % 32;
                    List<EvaluationUserAnswer> evaluationUserAnswers = rsMap.get(uidMod);
                    if (CollectionUtils.isEmpty(evaluationUserAnswers)) {
                        evaluationUserAnswers = Lists.newArrayList();
                    }
                    evaluationUserAnswers.add(item);
                    rsMap.put(uidMod, evaluationUserAnswers);
                }
                for (Long uidMod : rsMap.keySet()) {
                    List<EvaluationUserAnswer> evaluationUserAnswers = rsMap.get(uidMod);
                    if (CollectionUtils.isEmpty(evaluationUserAnswers)) {
                        continue;
                    }
                    try {
                        dao.insertBatch(evaluationUserAnswers);
                    } catch (Exception e) {
                        log.error("insertBatch fail. evaluationUserAnswers :{}", evaluationUserAnswers);
//                        log.error("EvaluationUserAnswer insertBatch fail.", e);
                    }
                }
                try {
                    compatableRedisClusterClient.set(key, evaluationUserAnswerList.get(evaluationUserAnswerList.size() - 1).getId().toString());
                } catch (Exception e) {
                    log.error("set AlUserAssessmentAnswerDTOIdKey fail.", e);
                }

            }

        }

    }

    private Long getLongValInRedis(String key) {
        Long val = null;
        try {
            String valStr = compatableRedisClusterClient.get(key);
            if (StringUtils.isNotBlank(valStr)) {
                val = Long.parseLong(valStr);
            }
        } catch (Exception e) {
            log.error("get getLongValInRedis fail.", e);
        }
        return val;
    }


    @Override
    public EvaluationUserAnswerDTO getEvaluationUserAnswerByAnswerId(EvaluationUserAnswerQuery query) throws DataAccessException{
        if(query==null || (!IdUtils.isValid(query.getAnswerId())) && !IdUtils.isValid(query.getOrgAnswerId())){
            return null;
        }
        List<EvaluationUserAnswerDTO> evaluationUserAnswerList = dao.getEvaluationUserAnswerList(query);
        if(CollectionUtils.isEmpty(evaluationUserAnswerList)){
            return null;
        }
        EvaluationUserAnswerDTO evaluationUserAnswerDTO = evaluationUserAnswerList.get(0);
        EvaluationUserBaseAnswerDetailQuery detailQuery = new EvaluationUserBaseAnswerDetailQuery();
        if(query.getEvaluationType()==null){
            return evaluationUserAnswerDTO;
        }
        if(Consts.EvaluationType.BASE.equals(query.getEvaluationType())){
            detailQuery.setUid(query.getUid());
            detailQuery.setEvaluationAnswerId(evaluationUserAnswerDTO.getId());
            detailQuery.setOrderBy(" a.id asc ");
            List<EvaluationUserBaseAnswerDetailDTO> evaluationUserBaseAnswerDetailList = evaluationUserBaseAnswerDetailService.getEvaluationUserBaseAnswerDetailList(detailQuery);
            if (CollectionUtils.isNotEmpty(evaluationUserBaseAnswerDetailList)) {
                evaluationUserAnswerDTO.setEvaluationUserBaseAnswerDetailList(evaluationUserBaseAnswerDetailList);
            }
        } else if (Consts.EvaluationType.MAJOR.equals(query.getEvaluationType())){
            List<Long> orgAnswerIdList = Lists.newArrayList(evaluationUserAnswerDTO.getOrgAnswerId());
            if (CollectionUtils.isNotEmpty(orgAnswerIdList)) {
                Map<String, Object> reqDetailParams = Maps.newHashMap();
                reqDetailParams.put("uid", query.getUid());
                reqDetailParams.put("answerIds", orgAnswerIdList);
                List<UserAnswerDetail> userAnswerDetailList = userAnswerSumService.getQuestionListByAnswerIds(reqDetailParams);
                if (CollectionUtils.isNotEmpty(userAnswerDetailList)) {
                    Type type = new com.google.common.reflect.TypeToken<List<UserAnswerDetailDTO>>() {
                    }.getType();
                    String rsStr = GsonUtil.getGenericGson().toJson(userAnswerDetailList);
                    List<UserAnswerDetailDTO> userAnswerDetailDTOList = GsonUtil.getGenericGson().fromJson(rsStr, type);
                    evaluationUserAnswerDTO.setUserAnswerDetailList(userAnswerDetailDTOList);
                }
            }
        }

        return evaluationUserAnswerDTO;
    }

}
