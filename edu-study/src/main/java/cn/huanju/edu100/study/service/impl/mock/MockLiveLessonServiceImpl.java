package cn.huanju.edu100.study.service.impl.mock;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.mock.MockLiveLessonDao;
import cn.huanju.edu100.study.model.mock.MockLiveLesson;
import cn.huanju.edu100.study.service.mock.MockLiveLessonService;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 模考考生报名Service
 * <AUTHOR>
 * @version 2018-04-08
 */
@Service
public class MockLiveLessonServiceImpl extends BaseServiceImpl<MockLiveLessonDao, MockLiveLesson> implements MockLiveLessonService {
    private static Logger logger = LoggerFactory.getLogger(MockLiveLessonServiceImpl.class);
    private static Gson gson = GsonUtil.getGson();

/*    @Autowired
    private MockLiveLessonDao mockLiveLessonDao;*/


    @Override
    public List<MockLiveLesson> getByMockExamId(Long mockExamId) throws DataAccessException {
        List<MockLiveLesson> mockLiveLessonList = dao.getByMockId(mockExamId);
        return mockLiveLessonList;
    }
}
