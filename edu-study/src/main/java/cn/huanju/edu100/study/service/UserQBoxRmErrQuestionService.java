package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;

import java.util.Date;
import java.util.List;

/**
 * 用户题库错题移除Service
 *
 * */
public interface UserQBoxRmErrQuestionService {

	void updateUserRmErrQuestionLog(Long uid, Long boxId, Long bookId, Long objId,
			Integer objType, List<Long> questionIds) throws DataAccessException;

	//判断用户该错题在作答时间t1之后是否有主动移除过？
	Boolean judgeUserQuestionIsRemove(Long uid,Long boxId, Long bookId, Long objId,
			Integer objType, Long questionId, Date answerDate) throws DataAccessException;

	void updateUserRmErrQuestionLogBatchObj(Long uid, Long boxId, Long bookId,
			List<Long> objIds, Integer objType, Long questionId) throws DataAccessException;

	void updateWipeOutRmErrQuestionLogBatchObj(Long uid, Long boxId, Long bookId,
			List<Long> objIds, Integer objType, Long questionId) throws DataAccessException;

	public void updateNewWipeOutRmErrQuestionLogBatchObj(Long uid, Long boxId,Long bookId,
			List<Long> objIds, Integer objType, Long questionId) throws DataAccessException;
}
