package cn.huanju.edu100.study.task;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.QuestionTopic;
import cn.huanju.edu100.study.model.question.SubjectiveQuestionAICorrectingLogPO;
import cn.huanju.edu100.study.repository.SubjectiveQuestionAiCorrectingRepository;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.resource.feigncall.EduStudyAssistantFeign;
import cn.huanju.edu100.study.resource.feigncall.dto.AiAsstantRequest;
import cn.huanju.edu100.study.service.UserAnswerSumService;
import cn.huanju.edu100.study.util.RetryUtil;
import cn.huanju.edu100.util.JSONUtils;
import cn.huanju.edu100.util.upload.OssUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hqwx.study.dto.SubjectiveQuestionAICorrectingLogDTO;
import com.hqwx.study.entity.UserAnswerDetail;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class GenerateAiOverallCommentTask {

    @Autowired
    private SubjectiveQuestionAiCorrectingRepository subjectiveQuestionAiCorrectingRepository;

    @Autowired
    private UserAnswerSumService userAnswerSumService;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private EduStudyAssistantFeign eduStudyAssistantFeign;

    @XxlJob("GenerateOverallCommentJobHandler")
    public void generateOverallComment() {
        log.info("Start generating AI overall comments");
        try {
            String params = XxlJobHelper.getJobParam();

            Date twentyFourHoursAgo = new Date(System.currentTimeMillis() - 12 * 60 * 60 * 1000);
            LambdaQueryWrapper<SubjectiveQuestionAICorrectingLogPO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.ge(SubjectiveQuestionAICorrectingLogPO::getCreateDate, twentyFourHoursAgo)
                    .eq(SubjectiveQuestionAICorrectingLogPO::getCorrectMethod, 3)
                    .eq(SubjectiveQuestionAICorrectingLogPO::getQuestionSource, SubjectiveQuestionAICorrectingLogDTO.QuestionSource.SOURCE_PAPER.getValue())
                    .eq(SubjectiveQuestionAICorrectingLogPO::getState, SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTED.getValue())
                    .eq(StringUtils.isNotBlank(params), SubjectiveQuestionAICorrectingLogPO::getUserAnswerId,  params);
            List<SubjectiveQuestionAICorrectingLogPO> logs = subjectiveQuestionAiCorrectingRepository.list(queryWrapper);
            if (CollectionUtils.isEmpty(logs)) {
                log.info("No logs found for processing");
                return;
            }
            List<Long> userAnswerIdList = logs.stream().map(o -> Long.parseLong(o.getUserAnswerId())).distinct().toList();
            LambdaQueryWrapper<SubjectiveQuestionAICorrectingLogPO> queryWrapper2 = Wrappers.lambdaQuery();
            queryWrapper2.in(SubjectiveQuestionAICorrectingLogPO::getUserAnswerId, userAnswerIdList);
            List<SubjectiveQuestionAICorrectingLogPO> logList = subjectiveQuestionAiCorrectingRepository.list(queryWrapper2);

            Map<String, List<SubjectiveQuestionAICorrectingLogPO>> logsByUserAnswerId = logList.stream().collect(Collectors.groupingBy(SubjectiveQuestionAICorrectingLogPO::getUserAnswerId));

            for (Map.Entry<String, List<SubjectiveQuestionAICorrectingLogPO>> entry : logsByUserAnswerId.entrySet()) {
                String userAnswerId = entry.getKey();
                List<SubjectiveQuestionAICorrectingLogPO> userLogs = entry.getValue();

                boolean hasOverallComment = userLogs.stream().anyMatch(log -> log.getQuestionId() == null && log.getTopicId() == null);
                if (hasOverallComment) {
                    continue;
                }

                if(!isOverallCommentGenerated(userAnswerId, userLogs)){
                    continue;
                }

                try {
                    generateOverallCommentForUserAnswer(userAnswerId, userLogs);
                } catch (Exception e) {
                    log.error("Error generating overall comment for userAnswerId: " + userAnswerId, e);
                }
            }
        } catch (Exception e) {
            log.error("Error in generateOverallComment task", e);
        }
    }

    /**
     * 是否满足获取总评语条件
     */
    private boolean isOverallCommentGenerated(String userAnswerId, List<SubjectiveQuestionAICorrectingLogPO> userLogs) {
        try {
            Map<String, Object> reqDetailParams = Maps.newHashMap();
            reqDetailParams.put("uid", userLogs.get(0).getUid());
            reqDetailParams.put("answerIds", Lists.newArrayList(Long.parseLong(userAnswerId)));
            List<UserAnswerDetail> userAnswerDetailList = userAnswerSumService.getQuestionListByAnswerIds(reqDetailParams);
            if(!CollectionUtils.isEmpty(userAnswerDetailList)){
                userAnswerDetailList = userAnswerDetailList.stream().filter(detail -> !Objects.equals(UserAnswerDetail.IsRight.NOT_ANSWER, detail.getIsRight())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(userAnswerDetailList)){
                    List<Long> questionIds = userAnswerDetailList.stream().map(UserAnswerDetail::getQuestionId).filter(Objects::nonNull).collect(Collectors.toList());
                    List<Long> topicIds = userAnswerDetailList.stream().map(UserAnswerDetail::getTopicId).filter(Objects::nonNull).collect(Collectors.toList());

                    List<Question> questionList = knowledgeResource.getQuestionByIds(questionIds);
                    if (!CollectionUtils.isEmpty(questionList)) {
                        List<QuestionTopic> subQuestions = questionList.stream()
                                .flatMap(question -> {
                                    List<QuestionTopic> subs = question.getTopicList();
                                    return subs != null ? subs.stream() : Stream.empty();
                                })
                                .filter(subQuestion -> (subQuestion.getQtype() == 5) && topicIds.contains(subQuestion.getId()))
                                .collect(Collectors.toList());

                        if (!CollectionUtils.isEmpty(subQuestions) && subQuestions.size() == userLogs.size()) {
                            return true;
                        }
                    }
                }
            }
        } catch (DataAccessException e) {
            log.error("Error retrieving userAnswerDetail for userAnswerId: " + userAnswerId, e);
        }
        return false;
    }

    private void generateOverallCommentForUserAnswer(String userAnswerId, List<SubjectiveQuestionAICorrectingLogPO> userLogs) {
        if (CollectionUtils.isEmpty(userLogs)) {
            return;
        }

        SubjectiveQuestionAICorrectingLogPO firstLog = userLogs.get(0);

        String logTag = "generateOverallCommentForUserAnswer-" + firstLog.getUid() + ": ";

        StringBuilder commentsBuilder = new StringBuilder();
        List<Map<String, Object>> contentList = new ArrayList<>();
        
        for (SubjectiveQuestionAICorrectingLogPO log : userLogs) {
            if (log.getAiAnswer() != null) {
                try {
                    String content = OssUtil.getContentDataFromOss(log.getAiAnswer());
                    if (StringUtils.isBlank(content)) {
                        continue;
                    }
                    
                    List<Map<String, Object>> jsonData = JSONUtils.parseObject(content, new TypeReference<List<Map<String, Object>>>() {});
                    if (CollectionUtils.isEmpty(jsonData)) {
                        continue;
                    }
                    
                    for (Map<String, Object> item : jsonData) {
                        List<Map<String, Object>> relationQuestions = (List<Map<String, Object>>) item.get("relationQuestion");
                        if (CollectionUtils.isEmpty(relationQuestions)) {
                            continue;
                        }
                        
                        for (Map<String, Object> question : relationQuestions) {
                            String scoreAnalysis = (String) question.get("smallQuestionScoreAnalysis");
                            String comment = (String) question.get("smallQuestionComment");
                            if (StringUtils.isNotBlank(scoreAnalysis) && StringUtils.isNotBlank(comment)) {
                                commentsBuilder.append(scoreAnalysis).append(comment).append(",");
                            }
                        }
                        
                        String imgUrl = (String) item.get("imgUrl");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            Map<String, Object> contentItem = new HashMap<>();
                            contentItem.put("type", "image_url");
                            Map<String, Object> imageUrl = new HashMap<>();
                            imageUrl.put("url", imgUrl);
                            contentItem.put("imageUrl", imageUrl);
                            contentList.add(contentItem);
                        }
                    }
                } catch (Exception e) {

                }
            }
        }

        String comments = commentsBuilder.toString();
        if (comments.endsWith(",")) {
            comments = comments.substring(0, comments.length() - 1);
        }

        AiAsstantRequest aiAsstantRequest = new AiAsstantRequest();
        aiAsstantRequest.setUid(firstLog.getUid());
        aiAsstantRequest.setAppName("edu-study");
        aiAsstantRequest.setEntryName("ReviewSubjectiveQuestion");
        aiAsstantRequest.setBusinessSceneName("GeneralComments");
        aiAsstantRequest.getParams().put("secondCategory", firstLog.getSecondCategory());
        aiAsstantRequest.getParams().put("category", firstLog.getCategoryId());
        aiAsstantRequest.getParams().put("comments", comments);
        aiAsstantRequest.setContent(contentList);

        String responseData = RetryUtil.executeWithRetryForString(() -> eduStudyAssistantFeign.question(aiAsstantRequest), 3, logTag);
        if (responseData == null) {
            log.error("{} 请求失败，responseData: {}", logTag, responseData);
            return;
        }
        addOverallComment(firstLog, responseData);
    }

    private void addOverallComment(SubjectiveQuestionAICorrectingLogPO firstLog, String aiAnswer){
        SubjectiveQuestionAICorrectingLogPO overallCommentLog = new SubjectiveQuestionAICorrectingLogPO();
        overallCommentLog.setUid(firstLog.getUid())
                .setUserAnswerId(firstLog.getUserAnswerId())
                .setQuestionSource(firstLog.getQuestionSource())
                .setCorrectMethod(firstLog.getCorrectMethod())
                .setSecondCategory(firstLog.getSecondCategory())
                .setCategoryId(firstLog.getCategoryId())
                .setQuestionId(null)
                .setTopicId(null)
                .setState(SubjectiveQuestionAICorrectingLogDTO.State.STATE_CORRECTED.getValue())
                .setAiAnswer(aiAnswer)
                .setCreateDate(new Date())
                .setUpdateDate(new Date());
        subjectiveQuestionAiCorrectingRepository.save(overallCommentLog);
        log.info("addOverallComment for userAnswerId: {}", firstLog.getUserAnswerId());
    }
}
