package cn.huanju.edu100.study.dao.solution;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.solution.SolutionQuestion;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface SolutionQuestionDao extends CrudDao<SolutionQuestion> {
    SolutionQuestion findUserQuestionInfoById(HashMap<String, Long> queryParam) throws DataAccessException;
    List<SolutionQuestion> findUserQuestionAnswerListByPid(HashMap<String, Long> queryParam) throws DataAccessException;
    List<SolutionQuestion> findUserQuestionListByIds(String qids) throws DataAccessException;
    List<SolutionQuestion> findUserQuestionListByIdsOrderById(String qids) throws DataAccessException;
    boolean updateCollectionNum(HashMap<String, Object> paramMap) throws DataAccessException;
    List<SolutionQuestion> findUserHotQuestionList(HashMap<String, Object> queryParam) throws DataAccessException;
    boolean updateViews(HashMap<String, Long> paramMap) throws DataAccessException;
    boolean updateComplained(HashMap<String, Long> paramMap) throws DataAccessException;
    Integer findUserHotQuestionListCount(HashMap<String, Object> queryParam) throws DataAccessException;

    SolutionQuestion getSolutionQuestionBriefByIdWithMaster(Long id) throws DataAccessException;

    Integer countSoFar(Map<String, Object> queryParam) throws DataAccessException;

    SolutionQuestion getSolutionQuestionByMessageIdWithMaster(String messageId) throws DataAccessException;
}
