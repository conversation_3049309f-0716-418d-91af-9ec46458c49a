package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 * 做题记录日详情Entity
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
public class TutorStudentAnswerDetail extends DataEntity<TutorStudentAnswerDetail> {

    private static final long serialVersionUID = 1L;
    private String classes; // 班别
    private Long logId; // log_id
    private Long questionId; // question_id
    private Long uid; // uid
    private Integer result; // result

    private List<Integer> resultList; // 做题结果集合

    public TutorStudentAnswerDetail() {
        super();
    }

    public TutorStudentAnswerDetail(Long id) {
        super(id);
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

    public List<Integer> getResultList() {
        return resultList;
    }

    public void setResultList(List<Integer> resultList) {
        this.resultList = resultList;
    }


}
