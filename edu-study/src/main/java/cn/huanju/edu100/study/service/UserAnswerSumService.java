package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.dto.UserAnswerSumDTO;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import com.hqwx.study.vo.UserAnswerErrorQuestionVo;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户答案大题详情Service
 * <AUTHOR>
 * @version 2015-05-12
 */
public interface UserAnswerSumService extends BaseService<UserAnswerSum> {

//    Page<UserAnswerSum> findWithDetails(Page<UserAnswerSum> page, UserAnswerSum t) throws DataAccessException;

//    List<UserAnswerSum> findByUserAnswerIdWithDetails(Long uid, Long userAnswerId,Long questionId) throws DataAccessException;

    List<UserAnswerDetail> submit(UserAnswer userAnswer) throws DataAccessException;

    List<UserAnswerDetail> submitHomeWork(UserHomeWorkAnswer userHomeWorkAnswer)throws DataAccessException;
	
	List<UserAnswerDetail> submitBoxExercise(UserExerciseAnswer userExerciseAnswer,Integer source) throws DataAccessException;
	
	List<UserAnswerSum> findAnswerSumAndDetail(Long uid, Long userAnswerId,List<Long> questionIdList) throws DataAccessException;

	/**
	 * 根据微课id查询最新的一次答题情况
	 * */
//	List<UserAnswerSum> findAnswerSumAndDetailByMClass(Long uid, Long mClassId,List<Long> questionIdList)throws DataAccessException;

	

	List<UserAnswerSum> findHomeworkSumAndDetail(Long uid, Long userHomeworkId,
			List<Long> questionIdList) throws DataAccessException;

	List<UserAnswerSum> findHomeworkSumAndDetailList(Long uid, List<Long> userHomeworkIds) throws DataAccessException;

	HashMap<Long, Object> submitErrorQuestion(List<UserErrorAnswer> userErrorAnswerList) throws DataAccessException;


	Page<UserAnswerSum> adminstudygetUserAnswerSumfindPage(Page page, UserAnswerSum userAnswerSum) throws DataAccessException;

	List<UserAnswerDetail> findAnswerSumQuestion(Map<String, Object> params) throws DataAccessException;
	
	List<UserAnswerDetail> getQuestionListByAnswerId(Map<String, Object> params) throws DataAccessException;

	List<UserAnswerDetail> getAnswerDetailList(UserAnswerDetail detail) throws DataAccessException;

	List<UserAnswerDetail> getQuestionListByHomeworkAnswerId(Map<String, Object> params) throws DataAccessException;

	Integer countQuestionListByHomeworkAnswerId(Map<String, Object> params) throws DataAccessException;

	Integer countQuestionListByAnswerId(Map<String, Object> params) throws DataAccessException;

	boolean delete(UserAnswerSum userAnswerSum) throws DataAccessException;

	List<UserAnswerSum> getUserAllQuestion(UserAnswerSum userAnswerSum) throws DataAccessException;

	List<UserAnswerHistory> getUserAnswerHistoryPage(Long uid, Integer pageSize, Integer pageNo) throws DataAccessException;

    Map getUserAnswerDetailByIdAndUid(Long id, Long uid,Integer type) throws DataAccessException;

	List<AlPaperReport> getUserAnswerListByUidAndDate(Long uid, Integer type,Long categoryId, Date startTime, Date endTime,List<Long> resIds) throws DataAccessException;

	List<UserAnswerSumDTO> findAllList(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException;

	List<UserAnswerDetail> getQuestionListByAnswerIds(Map<String, Object> params) throws DataAccessException;

    List<UserAnswerSumDTO> findListByGoodsId(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException;

	List<UserAnswerSumDTO> findListByGoodsIdAndProductId(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException;

	List<UserAnswerSumDTO> findAlSubmitQuestionList(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException;

	List<UserAnswerSumDTO> findAlSubmitQuestionListByPaperAndHomework(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException;

	/**
	 * 查询用户试卷作答ID集合获取作答错误题目统计信息
	 * @param uid
	 * @param answerIdIdList
	 * @return
	 * @throws BusinessException
	 */
	List<UserAnswerErrorQuestionVo> findErrorPaperAnswerSumDetailGroupByAnswerId(Long uid, List<Long> answerIdIdList) throws DataAccessException;

	/**
	 * 查询用户作业作答ID集合获取作答错误题目统计信息
	 * @param uid
	 * @param answerIdIdList
	 * @return
	 * @throws BusinessException
	 */
	List<UserAnswerErrorQuestionVo> findErrorHomeworkAnswerSumDetailGroupByAnswerId(Long uid, List<Long> answerIdIdList) throws DataAccessException;

	//根据批阅结果校准题目正确性和得分，前端接口，参数uid，作答id
	void correctSubjectiveResult(Long uid, Long userAnswerId, Integer questionSource);
}
