/**
 *
 */
package cn.huanju.edu100.study.dao.mock;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.mock.AdviceCourseLink;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 试卷DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-08
 */
public interface AdviceCourseLinkDao extends CrudDao<AdviceCourseLink> {


	List<AdviceCourseLink> getAdviceCourseLinkList(Long adviceId) throws DataAccessException;
}