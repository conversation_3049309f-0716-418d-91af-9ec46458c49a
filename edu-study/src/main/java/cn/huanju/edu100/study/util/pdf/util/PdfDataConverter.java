package cn.huanju.edu100.study.util.pdf.util;

import cn.huanju.edu100.study.util.pdf.model.PdfTextFormat;
import cn.huanju.edu100.study.util.pdf.model.PdfHtmlElement;
import cn.huanju.edu100.study.util.pdf.model.PdfTextBO;
import cn.huanju.edu100.util.upload.OssUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: PdfDataConverter
 * @Author: <EMAIL>
 * @Date: 2024-08-30
 * @Ver: v1.0 -create
 */
public class PdfDataConverter {
    private static String decodeUnicode(String input) {
        StringBuilder sb = new StringBuilder();
        int i = 0;
        while (i < input.length()) {
            if (input.startsWith("\\u", i)) {
                String hex = input.substring(i + 2, i + 6);
                char ch = (char) Integer.parseInt(hex, 16);
                sb.append(ch);
                i += 6;
            } else {
                sb.append(input.charAt(i));
                i++;
            }
        }
        return sb.toString();
    }
    private static String convertHtmlTags(String input) {
        // 映射 HTML 实体到实际字符
        Map<String, String> tagMap = Maps.newHashMap();
        tagMap.put("<sup>", "^");
        tagMap.put("</sup>", "");
        tagMap.put("<em>", "");
        tagMap.put("</em>", "");
        tagMap.put("<strong>", "");
        tagMap.put("</strong>", "");
        tagMap.put("<b>", "");
        tagMap.put("</b>", "");
        tagMap.put("<i>", "");
        tagMap.put("</i>", "");
//        tagMap.put("<a href='[^']*'>", "[");
//        tagMap.put("</a>", "]");
        tagMap.put("<ul>", "");
        tagMap.put("</ul>", "");
        tagMap.put("<li>", "");
        tagMap.put("</li>", "");
        tagMap.put("<h1>", "");
        tagMap.put("</h1>", "");
        tagMap.put("<h2>", "");
        tagMap.put("</h2>", "");
        tagMap.put("<h3>", "");
        tagMap.put("</h3>", "");
        tagMap.put("<h4>", "");
        tagMap.put("</h4>", "");
        tagMap.put("<h5>", "");
        tagMap.put("</h5>", "");
        tagMap.put("<h6>", "");
        tagMap.put("</h6>", "");
//        tagMap.put("<p>", "\n");
//        tagMap.put("</p>", "");
//        tagMap.put("<br>", "\n");
//        tagMap.put("<br/>", "\n");
        tagMap.put("<hr>", "");

        // 替换 HTML 实体
        for (Map.Entry<String, String> entry : tagMap.entrySet()) {
            input = input.replace(entry.getKey(), entry.getValue());
        }

        return input;

//        Map<String, String> tagMap = Maps.newHashMap();
//        tagMap.put("<sup>", "^");
//        tagMap.put("</sup>", "");
//        tagMap.put("<em>", "*");
//        tagMap.put("</em>", "*");
//        tagMap.put("<strong>", "**");
//        tagMap.put("</strong>", "**");
//        tagMap.put("<b>", "[");
//        tagMap.put("</b>", "]");
//        tagMap.put("<i>", "_");
//        tagMap.put("</i>", "_");
//        tagMap.put("<a href='[^']*'>", "[");
//        tagMap.put("</a>", "]");
//        tagMap.put("<ul>", "- ");
//        tagMap.put("</ul>", "");
//        tagMap.put("<li>", "- ");
//        tagMap.put("</li>", "\n");
//        tagMap.put("<h1>", "# ");
//        tagMap.put("</h1>", "\n");
//        tagMap.put("<h2>", "## ");
//        tagMap.put("</h2>", "\n");
//        tagMap.put("<h3>", "### ");
//        tagMap.put("</h3>", "\n");
//        tagMap.put("<h4>", "#### ");
//        tagMap.put("</h4>", "\n");
//        tagMap.put("<h5>", "##### ");
//        tagMap.put("</h5>", "\n");
//        tagMap.put("<h6>", "###### ");
//        tagMap.put("</h6>", "\n");
//        tagMap.put("<p>", "\n");
//        tagMap.put("</p>", "");
//        tagMap.put("<br>", "\n");
//        tagMap.put("<br/>", "\n");
//        tagMap.put("<hr>", "-----\n");
    }
    public static String formatHtml(String codeHtml) {
        String htmlText=codeHtml;
        htmlText = decodeUnicode(htmlText);
//        htmlText = htmlText.replace("&lt;","<").replace( "&gt;",">");
        htmlText = StringEscapeUtils.unescapeHtml4(htmlText);
        htmlText = convertHtmlTags(htmlText);
        return htmlText;
    }

    public static List<PdfHtmlElement> parseRawHtmlToList(String html) {
        if (StringUtils.isEmpty(html)) {
            return null;
        }
        Document document = Jsoup.parseBodyFragment(html);
        Element body = document.body();
        List<PdfHtmlElement> result = Lists.newArrayList();
        parseElement(body, result);

        return result;
    }
    private static void parseElement(Element rootElement, List<PdfHtmlElement> result) {
        LinkedList<Node> elementLinkedList = Lists.newLinkedList();
        elementLinkedList.push(rootElement);
        while (!elementLinkedList.isEmpty()) {
            Node node = elementLinkedList.pop();
            if (node.childNodeSize() > 0) {
                if ("p".equals(((Element) node).tagName())) {
                    PdfHtmlElement element = new PdfHtmlElement(PdfHtmlElement.Type.Text, "\n", 0, 0);
                    result.add(0, element);
                }
                List<Node> childNodes = node.childNodes();
                for (Node childNode : childNodes) {
                    elementLinkedList.push(childNode);
                }
            }
            else if (node instanceof TextNode) {
                //text
                String nodeString = Jsoup.parseBodyFragment(node.toString()).text();
                if (StringUtils.isNotBlank(nodeString)) {
                    PdfHtmlElement element = new PdfHtmlElement(PdfHtmlElement.Type.Text, nodeString, 0, 0);
                    result.add(0, element);
                }
            }
            else if (node instanceof Element) {
                //image.
                Element childElement = (Element) node;
                Element image = childElement.children().select("img").first();
                if (image == null && "img".equals(childElement.tagName())) {
                    image = childElement;
                }
                if (image != null) {
                    String url = image.attr("src");
                    if (StringUtils.isNotBlank(url)) {
                        long width = 0;
                        long height = 0;
                        String style = image.attr("style");
                        if (StringUtils.isNotBlank(style)) {
                            String[] styleArr = style.split(";");
                            for (String s : styleArr) {
                                String[] styleKv = s.split(":");
                                if (styleKv.length == 2) {
                                    if ("width".equals(styleKv[0])) {
                                        try {
                                            width = Long.parseLong(styleKv[1]);
                                        } catch (NumberFormatException e) {
                                            e.printStackTrace();
                                        }
                                    } else if ("height".equals(styleKv[0])) {
                                        try {
                                            height = Long.parseLong(styleKv[1]);
                                        } catch (NumberFormatException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }
                        String widthAttr = image.attr("width");
                        if (StringUtils.isNotBlank(widthAttr)) {
                            try {
                                width = Long.parseLong(widthAttr);
                            } catch (NumberFormatException e) {
                                e.printStackTrace();
                            }
                        }
                        String heightAttr = image.attr("height");
                        if (StringUtils.isNotBlank(widthAttr)) {
                            try {
                                height = Long.parseLong(heightAttr);
                            } catch (NumberFormatException e) {
                                e.printStackTrace();
                            }
                        }
                        PdfHtmlElement element = new PdfHtmlElement(PdfHtmlElement.Type.Img, url,
                                width, height);
                        result.add(0, element);
                    }

                } else {
                    if ("br".equals(((Element) node).tagName())) {
                        PdfHtmlElement element = new PdfHtmlElement(PdfHtmlElement.Type.Text, "\n", 0, 0);
                        result.add(0, element);
                    } else {
                        String nodeString = Jsoup.parseBodyFragment(node.toString()).text();
                        if (!StringUtils.isNotBlank(nodeString)) {
                            PdfHtmlElement element = new PdfHtmlElement(PdfHtmlElement.Type.Text, nodeString, 0, 0);
                            result.add(0, element);
                        }
                    }
                }
            }
        }
    }

    public static PdfTextBO getPdfText(String text){
        PdfTextBO result = new PdfTextBO();
        if(StringUtils.isBlank(text)){
            return result;
        }

        text = PdfDataConverter.formatHtml(text);
        PdfTextFormat textFormat = PdfTextFormat.getByText(text);
        if(Objects.equals(PdfTextFormat.HttpJs, textFormat)){
            text = getTextByJsUrl(text);
        }

        textFormat = PdfTextFormat.getByText(text);
        if(Objects.equals(PdfTextFormat.RichHtml, textFormat)){
            List<PdfHtmlElement> lst = PdfDataConverter.parseRawHtmlToList(text);
//            List<IElement> lst = PDFUtils.convertToElements(text, font);
            result.setElementList(lst);
        }else {
            result.setText(text);
        }
        return result;
    }

    private static String getTextByJsUrl(String url){
        String jsResponse = OssUtil.getContentDataFromOss(url);
        if(StringUtils.isBlank(jsResponse)){
            return url;
        }

        return StringEscapeUtils.unescapeHtml4(jsResponse);
    }
}
