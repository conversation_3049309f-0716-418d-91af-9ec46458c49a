package cn.huanju.edu100.study.model.solution;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @program: edu-study
 * @ClassName SolutionQuestionAiAnswerLogDetail
 * @description:
 * @Version 1.0
 **/
@Data
@TableName(value = "solution_question_ai_answer_log_detail",autoResultMap = true)
public class SolutionQuestionAiAnswerLogDetail {
    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    private Long id;

    private Long questionId;

    private Long logId;

    private Integer isAiAnswer;

    private String answerContent;

    private Date createdTime;

    private String remark;

}
