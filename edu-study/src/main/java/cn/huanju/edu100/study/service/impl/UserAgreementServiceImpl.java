package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.study.dto.FundSignAgreementMsg;
import cn.huanju.edu100.study.kafka.question.KafkaMsgProducerService;
import cn.huanju.edu100.study.mapper.UserAgreementMapper;
import cn.huanju.edu100.study.model.UserAgreement;
import cn.huanju.edu100.study.resource.FundResource;
import cn.huanju.edu100.study.service.UserAgreementService;
import cn.huanju.edu100.study.util.ThreadPoolFactoryUtil;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.util.JSONUtils;
import cn.huanju.edu100.util.MergerUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.hqwx.fund.dto.enums.CertifyContractEnum;
import com.hqwx.fund.dto.request.ContractAccountUpdateReq;
import com.hqwx.fund.dto.response.ContractAccountInfoDTO;
import com.hqwx.userevent.collect.client.dto.StudyAgreementEntity;
import com.hqwx.userevent.collect.client.dto.UserBaseInfoEntity;
import com.hqwx.userevent.collect.client.message.UserEventPublisher;
import com.hqwx.userevent.collect.client.message.param.UserEventMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 用户协议Service
 *
 * <AUTHOR>
 * @version 2015-05-11
 */
@DS("default-ds")
@Service
public class UserAgreementServiceImpl extends ServiceImpl<UserAgreementMapper, UserAgreement> implements
        UserAgreementService {
    Logger logger = LoggerFactory.getLogger(UserAgreementServiceImpl.class);

    @Autowired
    @Qualifier("secretStaticProducerService")
    private KafkaMsgProducerService secretStaticProducerService;

    @Resource
    private KafkaMsgProducerService fundProducerService;

    @Resource
    private FundResource fundResource;

    @Resource
    private UserEventPublisher userEventPublisher;

    @Value("${kafka.useragreement.topic}")
    private String topic;
    @Value("${kafka.fund.topic}")
    private String fundTopic;
    @Value("${kafka.useragreement.ifPartition}")
    private String ifPartition;
    @Value("${kafka.useragreement.partitionNum}")
    private Integer partitionNum;
    @Value("${kafka.useragreement.role}")
    String role;//用来生成key

    private static ExecutorService executor =
            ThreadPoolFactoryUtil.createDefaultPool("userAgreementThreadPool");

    @Override
    public Collection<UserAgreement> sty_getUserAgreementsByUid(Long uid, Long schId, String goodsId) throws DataAccessException {
        LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserAgreement::getUid,uid);
        if(schId != null) {
            queryWrapper.and(i ->
                    i.eq(UserAgreement::getSchId, schId).
                            or().isNull(UserAgreement::getSchId));
        }
        queryWrapper.eq(UserAgreement::getGoodsId,goodsId);
        queryWrapper.eq(UserAgreement::getDelFlag,0);
        return list(queryWrapper);
//        return dao.listByUid(uid, schId, goodsId);
    }

    @Override
    public Collection<UserAgreement> getUserAgreementsByUid(Long uid, List<Long> schIds, String goodsId) throws DataAccessException {
        LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserAgreement::getUid,uid);
        if(CollectionUtils.isNotEmpty(schIds)) {
            queryWrapper.in(UserAgreement::getSchId, schIds);
        }
        queryWrapper.eq(UserAgreement::getGoodsId,goodsId);
        queryWrapper.eq(UserAgreement::getDelFlag,0);
        return list(queryWrapper);
//        return dao.listByUid(uid, schIds, goodsId);
    }

    @Override
    public Collection<UserAgreement> getUserAgreementsByUid(Map<String, Object> params) throws DataAccessException {
        LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(UserAgreement::getDelFlag,0);
        if(MergerUtil.isMerge()){
            queryWrapper.in(UserAgreement::getUid, MergerUtil.getUids());
        }else{
            queryWrapper.eq(UserAgreement::getUid,params.get("uid"));
            if(params.containsKey("schId")) {
                queryWrapper.and(i ->
                        i.eq(UserAgreement::getSchId, params.get("schId")).
                                or().isNull(UserAgreement::getSchId));
            }
            if(params.containsKey("schIds")) {
                queryWrapper.in(UserAgreement::getSchId, (Collection<?>)params.get("schIds"));
            }
        }


        if(params.containsKey("orderId")) {
            queryWrapper.eq(UserAgreement::getOrderId, params.get("orderId"));
        }
        if(params.containsKey("goodsId")) {
            queryWrapper.eq(UserAgreement::getGoodsId, params.get("goodsId"));
        }
        if(params.containsKey("status")) {
            queryWrapper.eq(UserAgreement::getStatus, params.get("status"));
        }
//        return dao.listByUid(params);
        return list(queryWrapper);
    }

    @Override
    public int updateByIdUid(UserAgreement userAgreement, UserAgreement userAgreementDb) throws DataAccessException {
        if (userAgreement == null || userAgreement.getId() == null || userAgreement.getId() <= 0) {
            return 0;
        }
        checkParam(userAgreement);
        //增加资金监管实名认证和推送逻辑
        fundAuthAccount(userAgreement);

        LambdaUpdateWrapper<UserAgreement> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserAgreement::getId,userAgreement.getId());
        updateWrapper.eq(UserAgreement::getUid,userAgreement.getUid());
        userAgreement.setAgreeDate(new Date());
        int result = getBaseMapper().update(userAgreement,updateWrapper);//dao.updateByIdUid(userAgreement);
        executor.submit(() -> {
            try {
                submitUserEventInfo(userAgreement, userAgreementDb);
            } catch (Exception e) {
                logger.error("submitUserEventInfo fail. exception ", e);
            }

            LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(UserAgreement::getId, userAgreement.getId());
            queryWrapper.eq(UserAgreement::getUid, userAgreement.getUid());
//                query.setId(userAgreement.getId());
//                query.setUid(userAgreement.getUid());
            DynamicDataSourceContextHolder.push("default-ds");
            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.setWriteRouteOnly();
                List<UserAgreement> userAgreementList = list(queryWrapper);//dao.findListFromMatster(query);
                if (CollectionUtils.isNotEmpty(userAgreementList)) {
                    for (UserAgreement agreement : userAgreementList) {
                        agreement.initAttr();
                        secretStaticProducerService.sndMesForTemplate(topic, GsonUtil.toJson(agreement), ifPartition, partitionNum, role);
                    }
                }
            } catch (Exception e) {
                logger.error("user agreement findList catch exception", e);
            } finally {
                DynamicDataSourceContextHolder.poll();
            }

        });
        return result;
    }

    private void submitUserEventInfo(UserAgreement userAgreement, UserAgreement userAgreementDb) {
        StudyAgreementEntity.StudyAgreement.Builder eventDataBuilder = StudyAgreementEntity.StudyAgreement.newBuilder();
        // 用户基础信息组装
        UserBaseInfoEntity.UserBaseInfo.Builder userInfoBuilder = UserBaseInfoEntity.UserBaseInfo.newBuilder();
        userInfoBuilder.setUid(userAgreement.getUid());
        userInfoBuilder.setName(userAgreement.getName());
        userInfoBuilder.setSchId(userAgreementDb.getSchId());
        // 身份证号中提取生日(出生年月日)
        String idCard = userAgreement.getIdcard();
        if (idCard != null && idCard.length() > 16) {
            userInfoBuilder.setBirthday(idCard.substring(6, 14));
        }
//        userInfoBuilder.setSex(UserBaseInfoEntity.SexEnum.male);// 如果能取到身份证信息上的性别
        eventDataBuilder.setUserInfo(userInfoBuilder.build());
        eventDataBuilder.setGoodsId(Long.parseLong(userAgreementDb.getGoodsId()));
        eventDataBuilder.setGoodsName(userAgreementDb.getGoodsName());
        eventDataBuilder.setBuyOrderId(userAgreementDb.getOrderId());

        UserEventMessage userEvent = new UserEventMessage();
        userEvent.setData(eventDataBuilder.build());

        userEventPublisher.publishMessage(userEvent);// 数据组装完成后 发布消息
    }

    /**
     * 资金监管实名认证和推送签署消息
     * @param userAgreement
     * @throws DataAccessException
     */
    private void fundAuthAccount(UserAgreement userAgreement) throws DataAccessException {
        UserAgreement agreement = getById(userAgreement.getId());
        ContractAccountInfoDTO contractAccountInfo = fundResource.getContractAccountInfo(agreement.getOrderId());
        if (Objects.nonNull(contractAccountInfo) && (contractAccountInfo.getAgreeState() != 1 || contractAccountInfo.getAgreeState() != 2)) {
            //判定实名认证是否通过
            ContractAccountUpdateReq req = new ContractAccountUpdateReq();
            req.setCtfId(userAgreement.getIdcard());
            req.setClientName(userAgreement.getName());
            req.setMobile(userAgreement.getPhone());
            req.setAuthorCode(contractAccountInfo.getAuthCode());
            req.setAccountNo(contractAccountInfo.getBuyOrderId().toString());
            CertifyContractEnum certifyContractEnum = fundResource.updateContractAccountInfo(req);
            if (CertifyContractEnum.SUCCESS.equals(certifyContractEnum) || CertifyContractEnum.WAIT.equals(certifyContractEnum)) {
                //认证成功异步推送签署信息
                executor.submit(() -> {
                    FundSignAgreementMsg signAgreementMsg = new FundSignAgreementMsg("signAgreement", agreement.getOrderId(), userAgreement.getName(), userAgreement.getIdcard(), userAgreement.getPhone(), certifyContractEnum);
                    Header header = new RecordHeader("eventType", signAgreementMsg.getEventType().getBytes(StandardCharsets.UTF_8));
                    Map<String, Object> stringObjectMap = fundProducerService.sndMesForTemplateWithHeader(fundTopic, JSONUtils.toJsonString(signAgreementMsg), ifPartition, partitionNum, role, header);
                    logger.info("发送监管消息结果：{}", JSONUtils.toJsonString(stringObjectMap));
                });
            } else {
                logger.error("资金监管,实名校验未通过:{}", JSONUtils.toJsonString(agreement));
                throw new DataAccessException(certifyContractEnum.getMessage());
            }
        }
    }

    private void checkParam(UserAgreement userAgreement) throws DataAccessException {
        if (userAgreement == null || userAgreement.getUid() == null || userAgreement.getUid() <= 0) {
            logger.error("userAgreement uid is null or emtpty.");
            throw new DataAccessException("paramerter uid is null or emtpty.");
        }
        if (userAgreement.getId() == null || userAgreement.getId() <= 0) {
            logger.error("paramerter id is null or emtpty.");
            throw new DataAccessException("paramerter id is null or emtpty.");
        }
        if (userAgreement.getName() == null || userAgreement.getName().length() == 0) {
            logger.error("paramerter name is null or emtpty.");
            throw new DataAccessException("paramerter name is null or emtpty.");
        }
        if (userAgreement.getIdcard() == null || userAgreement.getIdcard().length() == 0) {
            logger.error("paramerter idcard is null or emtpty.");
            throw new DataAccessException("paramerter idcard is null or emtpty.");
        }
        if (userAgreement.getPhone() == null || userAgreement.getPhone().length() == 0) {
            logger.error("paramerter phone is null or emtpty.");
            throw new DataAccessException("paramerter phone is null or emtpty.");
        }
        if (userAgreement.getIp() == null || userAgreement.getIp().length() == 0) {
            logger.error("paramerter ip is null or emtpty.");
            throw new DataAccessException("paramerter ip is null or emtpty.");
        }
        if (userAgreement.getAddress() == null || userAgreement.getAddress().length() == 0) {
            logger.error("paramerter ip is null or emtpty.");
            throw new DataAccessException("paramerter address is null or emtpty.");
        }
        if (userAgreement.getEmail() == null || userAgreement.getEmail().length() == 0) {
            logger.error("paramerter email is null or emtpty.");
            throw new DataAccessException("paramerter email is null or emtpty.");
        }

    }


    @Override
    public boolean batchInsert(Collection<UserAgreement> param) throws DataAccessException {
        boolean result = saveBatch(param);//dao.batchInsert(param);
        executor.submit(new Runnable() {
            @Override
            public void run() {
                List<UserAgreement> queryList = Lists.newArrayList();
                Set<String> keys = Sets.newHashSet();
                for (UserAgreement userAgreement : param) {
                    String key = StringUtils.join(userAgreement.getUid(), "-",userAgreement.getGoodsId(),"-", userAgreement.getOrderId());
                    if (!keys.contains(key)) {
                        keys.add(key);
                        queryList.add(userAgreement);
                    }
                }
                //异步,另外这里(一个订单里的商品id)应该不是特别多,所以for循环中查询数据库
                if (CollectionUtils.isNotEmpty(queryList)) {
                    for (UserAgreement query : queryList) {
                        try {
                            LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
                            if(null != query.getUid()) {
                                queryWrapper.eq(UserAgreement::getUid, query.getUid());
                            }
                            if(null != query.getName()){
                                queryWrapper.like(UserAgreement::getName,"%"+query.getName()+"%");
                            }
//                            queryWrapper.in(UserAgreement::getGoodsId,query.getGoodsId());
//                            queryWrapper.eq(UserAgreement::getOrderId,query.getOrderId());
                            queryWrapper.eq(UserAgreement::getDelFlag,0);
                            if(null != query.getSchId()) {
                                queryWrapper.and(i ->
                                        i.eq(UserAgreement::getSchId, query.getSchId()).
                                                or().isNull(UserAgreement::getSchId));
                            }
                            List<UserAgreement> userAgreementList = list(queryWrapper);//dao.findList(query);
                            if (CollectionUtils.isNotEmpty(userAgreementList)) {
//                                logger.info("userAgreementList  is {}", GsonUtil.toJson(userAgreementList));
                                for (UserAgreement agreement : userAgreementList) {
                                    Map<String,Object> resp = secretStaticProducerService.sndMesForTemplate(topic, GsonUtil.toJson(agreement), ifPartition, partitionNum, role);
//                                    logger.info("sndMesForTemplate result is {}", GsonUtil.toJson(resp));
                                }
                            }
                        } catch (Exception e) {
                            logger.error("user agreement findList catch exception", e);
                        }
                    }
                }
            }
        });
        return result;
    }

    @Override
    public Collection<UserAgreement> listByUidOrderId(Long uid, Long orderId) throws DataAccessException {
        if (uid == null || uid <= 0) {
            return null;
        }
        if (orderId == null || orderId <= 0) {
            return null;
        }
        LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserAgreement::getUid,uid);
        queryWrapper.eq(UserAgreement::getOrderId,orderId);
        queryWrapper.eq(UserAgreement::getDelFlag,0);
        return list(queryWrapper);
        //return dao.listByUidOrderId(uid, orderId);
    }

    @Override
    public Collection<UserAgreement> listByUidOrderIdFromMaster(Long uid, Long orderId) throws DataAccessException {
        if (uid == null || uid <= 0) {
            return null;
        }
        if (orderId == null || orderId <= 0) {
            return null;
        }
        try(HintManager hintManager = HintManager.getInstance()) {
            hintManager.setWriteRouteOnly();
            LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(UserAgreement::getUid, uid);
            queryWrapper.eq(UserAgreement::getOrderId, orderId);
            queryWrapper.eq(UserAgreement::getDelFlag, 0);
            return list(queryWrapper);
        }
//        return dao.listByUidOrderIdFromMaster(uid, orderId);
    }

    @Override
    public Collection<Long> listGoodsIdByStatusUid(Long uid, Integer status, Long schId) throws DataAccessException {
        if (uid == null || uid <= 0) {
            return null;
        }
        if (status == null || status < 0) {
            return null;
        }
        LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(UserAgreement::getGoodsId);
        queryWrapper.eq(UserAgreement::getUid,uid);
        queryWrapper.eq(UserAgreement::getStatus,status);
        queryWrapper.eq(UserAgreement::getSchId,schId);
        queryWrapper.eq(UserAgreement::getDelFlag,0);
        return list(queryWrapper).stream().map(item->Long.valueOf(item.getGoodsId())).collect(Collectors.toList());
//        return dao.goodsIdlistByStatusUid(uid, status, schId);
    }

    @Override
    public int deleteByUidAndGoodsId(Long uid, Long[] goodsIds, Long orderId) throws DataAccessException {
        LambdaUpdateWrapper<UserAgreement> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(UserAgreement::getGoodsId,goodsIds);
        updateWrapper.eq(UserAgreement::getOrderId,orderId);
        updateWrapper.eq(UserAgreement::getUid,uid);
        updateWrapper.set(UserAgreement::getDelFlag,1);
        int result = getBaseMapper().update(null,updateWrapper);

        executor.submit(new Runnable() {
            @Override
            public void run() {
                DynamicDataSourceContextHolder.push("default-ds");
                try(HintManager hintManager = HintManager.getInstance()) {
                    hintManager.setWriteRouteOnly();
                    LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.in(UserAgreement::getGoodsId,goodsIds);
                    queryWrapper.eq(UserAgreement::getUid,uid);
                    queryWrapper.eq(UserAgreement::getOrderId,orderId);
                    Collection<UserAgreement> userAgreementList = list(queryWrapper);//dao.listByUidAndGoodsIdFromMaster(uid, goodsIds, orderId);
                    if (CollectionUtils.isNotEmpty(userAgreementList)) {
                        for (UserAgreement agreement : userAgreementList) {
                            secretStaticProducerService.sndMesForTemplate(topic, GsonUtil.toJson(agreement), ifPartition, partitionNum, role);
                        }
                    }
                } catch (Exception e) {
                    logger.error("user agreement listByUidAndGoodsIdFromMaster catch exception", e);
                }finally {
                    DynamicDataSourceContextHolder.poll();
                }

            }
        });
        return result;
    }

    @Override
	public Collection<Long> listGoodsIdByStatusUidSchIds(Long uid, Integer status,
			Collection<Long> schIds) throws DataAccessException {
		if (uid == null || uid <= 0) {
            return null;
        }
        if (status == null || status < 0) {
            return null;
        }
//        return dao.goodsIdlistByStatusUid(uid, status, schIds);
        LambdaQueryWrapper<UserAgreement> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(UserAgreement::getGoodsId);
        queryWrapper.eq(UserAgreement::getUid,uid);
        queryWrapper.eq(UserAgreement::getStatus,status);
        queryWrapper.in(UserAgreement::getSchId,schIds);
        queryWrapper.eq(UserAgreement::getDelFlag,0);
        return list(queryWrapper).stream().map(item->Long.valueOf(item.getGoodsId())).collect(Collectors.toList());
	}

}
