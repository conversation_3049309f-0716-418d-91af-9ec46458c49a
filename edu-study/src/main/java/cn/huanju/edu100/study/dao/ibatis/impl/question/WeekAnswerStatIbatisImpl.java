/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.question;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.question.WeekAnswerStatDao;
import cn.huanju.edu100.study.model.question.WeekAnswerStat;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 用户周答题统计DAO接口
 * <AUTHOR>
 * @version 2018-06-08
 */
public class WeekAnswerStatIbatisImpl extends CrudIbatisImpl2<WeekAnswerStat> implements
		WeekAnswerStatDao {

	public WeekAnswerStatIbatisImpl() {
		super("WeekAnswerStat");
	}

	@Override
	public List<WeekAnswerStat> findMaxWeekNum() throws DataAccessException {
		try {
			SqlMapClient sqlMap = super.getSlave();

			Map<String, Object> param = new HashMap<String, Object>();
			return (List<WeekAnswerStat>) sqlMap.queryForList("WeekAnswerStat.findMaxWeekNum", param);
		} catch (SQLException e) {
			logger.error("findMaxWeekNum {} SQLException.", namespace, e);
			throw new DataAccessException("findMaxWeekNum SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<WeekAnswerStat> findByWeekNum(Long boxId, Long teachBookId, Long uid, Long startWeekNum, Long endWeekNum) throws DataAccessException {
		if (boxId == null) {
			logger.error("findByWeekNum {} error, parameter boxId is null,boxId:{}", namespace, boxId);
			throw new DataAccessException("findByWeekNum error,entity is null");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();

			Map<String, Object> param = new HashMap<String, Object>();
			param.put("boxId", boxId);
			param.put("teachBookId", teachBookId);
			param.put("uid", uid);
			param.put("startWeekNum", startWeekNum);
			param.put("endWeekNum", endWeekNum);
			return (List<WeekAnswerStat>) sqlMap.queryForList("WeekAnswerStat.findByWeekNum", param);
		} catch (SQLException e) {
			logger.error("findByWeekNum {} SQLException.", namespace, e);
			throw new DataAccessException("findByWeekNum SQLException error" + e.getMessage());
		}
	}

}
