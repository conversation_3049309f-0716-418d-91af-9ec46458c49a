package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.VideoSnapshotService;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.hqwx.goods.dto.ResourceVideo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.net.URL;
import java.util.Date;

@Service
public class VideoSnapshotServiceImpl implements VideoSnapshotService {

    private static Logger logger = LoggerFactory
            .getLogger(VideoSnapshotServiceImpl.class);

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Value("${file-upload-oss.bucketList.accessKeyId}")
    private String ossVideoAccessKeyId;

    @Value("${file-upload-oss.bucketList.accessKeySecret}")
    private String ossVideoAccessKeySecret;

    @Value("${file-upload-oss.bucketList.endpoint}")
    private String ossVideoEndpoint;

    @Value("${file-upload-oss.bucketList.bucket}")
    private String ossVideoBucketName;

    @Value("${file-upload-oss.bucketList.domain}")
    private String ossVideoDomain;

    @Override
    public String getVideoSnapshotUrl(Long videoResId,Long position) {
        if(videoResId==null || position==null){
            logger.info("getVideoSnapshotUrl videoResId is null or position is null");
            return null;
        }
        ResourceVideo resourceVideo=knowledgeResource.getReturnResourceVideoById(videoResId);
        if(resourceVideo==null){
            logger.info("getVideoSnapshotUrl getReturnResourceVideoById is null,resId:{}"+videoResId);
            return null;
        }
        //https://help.aliyun.com/zh/oss/user-guide/video-snapshots  阿里云对接文档地址
        String fileName=resourceVideo.getFilename();
        String endpoint = ossVideoEndpoint;
        String bucketName=ossVideoBucketName;
        String objectName=fileName;
        String accessKeyId=ossVideoAccessKeyId;
        String accessKeySecret=ossVideoAccessKeySecret;
        OSS ossClient=new OSSClientBuilder().build(endpoint,accessKeyId,accessKeySecret);
        Long positionMilliSecond=position*1000;//秒转成毫秒
        //生成一个宽800，高600的jpg图片
        String style="video/snapshot,t_"+positionMilliSecond+",f_jpg,w_800,h_600";
        //设置有效期10年
        Date expiration=new Date(new Date().getTime()+3600l* 1000*24*365*10);
        GeneratePresignedUrlRequest req=new GeneratePresignedUrlRequest(bucketName,objectName, HttpMethod.GET);
        req.setExpiration(expiration);
        req.setProcess(style);
        URL signedUrl=ossClient.generatePresignedUrl(req);
        String videoSnapshotUrl = signedUrl.getProtocol() + "://" + ossVideoDomain;
        if (signedUrl.getPort() != -1) {
            videoSnapshotUrl += ":" + signedUrl.getPort() + "/";
        }
        videoSnapshotUrl += signedUrl.getPath() + "?" + signedUrl.getQuery();
        // 关闭OSSClient。
        ossClient.shutdown();
        return videoSnapshotUrl;
    }
}
