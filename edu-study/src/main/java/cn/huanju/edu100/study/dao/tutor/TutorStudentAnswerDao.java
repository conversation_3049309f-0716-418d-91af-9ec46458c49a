/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentAnswer;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 做题记录DAO接口
 * 
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentAnswerDao extends CrudDao<TutorStudentAnswer> {

    List<TutorStudentAnswer> getAnswerNumberByUidAndTaskIdList(Long uid, List<Long> taskIdList) throws DataAccessException;

    List<TutorStudentAnswer> findByUidAndTaskIdList(Long uid, List<Long> taskIdList) throws DataAccessException;
}