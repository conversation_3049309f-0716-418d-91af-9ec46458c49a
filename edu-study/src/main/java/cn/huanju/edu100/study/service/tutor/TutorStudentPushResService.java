package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorStrengthKnowlegdeDto;
import cn.huanju.edu100.study.model.tutor.TutorStudentPushRes;
import cn.huanju.edu100.study.model.tutor.TutorTask;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 推送记录Service
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentPushResService extends BaseService<TutorStudentPushRes> {

    /**
     * 查询用户需要强化的知识点列表
     *
     * @param tutorStudentPushRes
     * @return
     * @throws DataAccessException
     */
    List<TutorStrengthKnowlegdeDto> listPushResByUid(TutorStudentPushRes tutorStudentPushRes)
            throws DataAccessException;

    /**
     * 提交任务做错题给用户推送微课课件
     *
     * @param questionIdList
     * @param tutorTask
     * @param studentAnswerId
     * @param resType
     * @return
     * @throws DataAccessException
     */
    void pushLessonVideo(Long uid, List<Long> questionIdList, TutorTask tutorTask, Long studentAnswerId, Integer resType)
            throws DataAccessException;

}
