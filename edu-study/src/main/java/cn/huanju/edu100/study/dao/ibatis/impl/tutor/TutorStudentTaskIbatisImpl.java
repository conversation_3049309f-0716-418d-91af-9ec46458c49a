/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorStudentTaskDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.tutor.TutorStudentTask;
import cn.huanju.edu100.study.util.CountUtils;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.study.util.ValidateUtils;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.springframework.util.CollectionUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生任务DAO接口
 *
 * <AUTHOR>
 * @version 2016-01-18
 */
public class TutorStudentTaskIbatisImpl extends CrudIbatisImpl2<TutorStudentTask> implements TutorStudentTaskDao {

    public TutorStudentTaskIbatisImpl() {
        super("TutorStudentTask");
    }

    @Override
    public List<TutorStudentTask> getTutorStudentTaskByTaskList(Long uid, List<Long> taskIdList, Integer source)
            throws DataAccessException {
        if (CollectionUtils.isEmpty(taskIdList) || uid == null || null == source) {
            return null;
        }

        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("taskIdList", taskIdList);
            params.put("uid", uid);
            params.put("source", source);

            SqlMapClient sqlMap = super.getSlave();
            return sqlMap.queryForList("TutorStudentTask.findList", params);
        } catch (SQLException e) {
            logger.error("getTutorStudentTaskByTaskList SQLException.", e);
            throw new DataAccessException("getTutorStudentTaskByTaskList SQLException error");
        }
    }

    @Override
    public boolean updateByUidAndTaskId(TutorStudentTask tutorStudentTask) throws DataAccessException {
        try {
            SqlMapClient sqlMap = super.getMaster();
            return sqlMap.update("TutorStudentTask.updateByUidAndTaskId", tutorStudentTask) > 0;
        } catch (SQLException e) {
            logger.error("updateByUidAndTaskId SQLException.", e);
            throw new DataAccessException("updateByUidAndTaskId SQLException error");
        }
    }

    @Override
    public TutorStudentTask findLimitOneByTidAndUid(Long tid, Long uid, Integer source) throws DataAccessException {
        if (ValidateUtils.isEmpty(tid)) {
            logger.error("findLimitOneByTidAndUid param error, parameter tid is invalid,tid:{}", namespace, tid);
            throw new DataAccessException("findLimitOneByTidAndUid param error,tid is invalid");
        }
        if (ValidateUtils.isEmpty(uid)) {
            logger.error("findLimitOneByTidAndUid param error, parameter uid is invalid,uid:{}", namespace, uid);
            throw new DataAccessException("findLimitOneByTidAndUid param error,uid is invalid");
        }
        if (null == source) {
            logger.error("findLimitOneByTidAndUid param error, parameter source is invalid,source:{}", namespace, source);
            throw new DataAccessException("findLimitOneByTidAndUid param error,source is invalid");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("tid", tid);
            param.put("uid", uid);
            param.put("source", source);

            return (TutorStudentTask) sqlMap.queryForObject("TutorStudentTask.qryLimitOneByTidAndUid", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.tid:{} uid:{}", namespace, tid, uid, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public TutorStudentTask getLastTask(Long uid, Long unitId, Long phaseId, Long categoryId, Integer source) throws DataAccessException {

        if (!IdUtils.isValid(uid) ) {
            logger.error("getLastTask param error, parameter uid  is null");
            throw new DataAccessException("getLastTask param error, parameter uid is null");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", uid);
            param.put("unitId", unitId);
            param.put("phaseId", phaseId);
            param.put("categoryId", categoryId);
            param.put("source", source);

            return (TutorStudentTask) sqlMap.queryForObject("TutorStudentTask.getLastTask", param);
        } catch (SQLException e) {
            logger.error("getLastTask {} SQLException.uid:{} unitId:{} phaseId:{} categoryId:{}", namespace, uid, unitId, phaseId, categoryId, e);
            throw new DataAccessException("getLastTask SQLException error" + e.getMessage());
        }
    }

    @Override
    public Map<Long, Integer> getUnitTaskNum(List<Long> unitIdList, Long uid, Integer source) throws DataAccessException {
        if (!IdUtils.isValid(uid) || CollectionUtils.isEmpty(unitIdList)) {
            logger.error("illegal param, uid or unitIdList is empty");
            throw new DataAccessException("illegal param, uid or unitIdList is empty");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", uid);
            param.put("status", 2);
            param.put("unitIdList", unitIdList);
            param.put("source", source);

            List<CountModel> results = sqlMap.queryForList("TutorStudentTask.listUnitTaskNum", param);
            return CountUtils.getCountMap(results);
        } catch (SQLException e) {
            logger.error("getUnitTaskNum {} SQLException.uid:{} unitIdList:{} ", namespace, uid, unitIdList, e);
            throw new DataAccessException("getUnitTaskNum SQLException error" + e.getMessage());
        }
    }
}
