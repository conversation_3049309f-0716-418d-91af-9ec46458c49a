package cn.huanju.edu100.study.model;

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/9/13.
 */
public class UserErrorEpaperQuestion {

    private Long productId;

    private Long questionId;//题目id

    private Integer qtype;////题目类型 0单项选择题 1多项选择题 2不定项选择题 3判断题 4填空题 5简答题 6案例题

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Integer getQtype() {
        return qtype;
    }

    public void setQtype(Integer qtype) {
        this.qtype = qtype;
    }

}
