package cn.huanju.edu100.study.service.mock;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.mock.MockExam;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 模考活动Service
 * <AUTHOR>
 * @version 2018-04-08
 */
public interface MockExamService extends BaseService<MockExam> {

    List<MockExam> qryBySecCategoryAndStatus(Long secondCategory, Integer status) throws DataAccessException;

    List<MockExam> qryBySecCategoryAndStatusAndAreaId(Long secondCategory, Integer status,Long areaId) throws DataAccessException;

    List<Long> findMockAreaIdListBySecondCategoryId(Long secondCategory) throws DataAccessException;

    List<MockExam> findMyMockList(Long secondCategory,Long uid,int from,int rows) throws DataAccessException;

}