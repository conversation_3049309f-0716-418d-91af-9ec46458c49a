/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorGroupStudentDao;
import cn.huanju.edu100.study.model.tutor.TutorGroupStudent;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 学员跟分组关联DAO接口
 *
 * <AUTHOR>
 * @version 2016-01-26
 */
public class TutorGroupStudentIbatisImpl extends CrudIbatisImpl2<TutorGroupStudent> implements TutorGroupStudentDao {

    public TutorGroupStudentIbatisImpl() {
        super("TutorGroupStudent");
    }

    @Override
    public List<TutorGroupStudent> getTutorGroupStudent(Map<String, Object> paramMap) throws DataAccessException {
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorGroupStudent>) sqlMap
                    .queryForList("TutorGroupStudent.findList", paramMap);
        } catch (SQLException e) {
            logger.error("getTutorGroupStudent SQLException.", e);
            throw new DataAccessException("getTutorGroupStudent SQLException error");
        }
    }

}
