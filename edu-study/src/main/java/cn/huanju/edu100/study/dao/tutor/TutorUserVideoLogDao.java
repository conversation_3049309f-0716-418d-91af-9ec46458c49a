/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorUserVideoLog;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 学员观看记录DAO接口
 * <AUTHOR>
 * @version 2016-01-20
 */
public interface TutorUserVideoLogDao extends CrudDao<TutorUserVideoLog> {

    List<TutorUserVideoLog> getTutorUserVideoLog(Map<String, Object> params)
            throws DataAccessException;

    public Long insertTutorUserVideoLog(final TutorUserVideoLog tutorUserVideoLog)throws DataAccessException;
    public boolean updateTutorUserVideoLog(final TutorUserVideoLog tutorUserVideoLog)throws DataAccessException;

    List<TutorUserVideoLog> findByTaskListAndUid(String classes, Long uid, List<Long> taskIdList)
            throws DataAccessException;
}