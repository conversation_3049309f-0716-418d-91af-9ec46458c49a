package cn.huanju.edu100.study.model.mock;


import cn.huanju.edu100.persistence.model.DataEntity;


public class AdviceCourseLink extends DataEntity<AdviceCourseLink> {

	private static final long serialVersionUID = 1L;

    private Long adviceId;		// 学习建议id
    private Long goodsGroupId;		// 课程id
    private String name;		// 课程名称
    private String relateName;
    private Long goodsId;

    public Long getAdviceId() {
        return adviceId;
    }

    public void setAdviceId(Long adviceId) {
        this.adviceId = adviceId;
    }

    public Long getGoodsGroupId() {
        return goodsGroupId;
    }

    public void setGoodsGroupId(Long goodsGroupId) {
        this.goodsGroupId = goodsGroupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRelateName() {
        return relateName;
    }

    public void setRelateName(String relateName) {
        this.relateName = relateName;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }
}