package cn.huanju.edu100.study.model.goods;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 课件章节Entity
 * <AUTHOR>
 * @version 2015-07-29
 */
public class LessonChapter extends DataEntity<LessonChapter> {
	
	private static final long serialVersionUID = 1L;
	private Long lessonId;		// lesson_id
	private Long chapterId;		// chapter_id
	
	public LessonChapter() {
		super();
	}

	public LessonChapter(Long id){
		super(id);
	}

	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}
	
	public Long getChapterId() {
		return chapterId;
	}

	public void setChapterId(Long chapterId) {
		this.chapterId = chapterId;
	}
	
}