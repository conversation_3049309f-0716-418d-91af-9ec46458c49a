package cn.huanju.edu100.study.resource;


import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.service.Edu100MsgServiceGrpc;
import cn.huanju.edu100.util.GsonUtils;
import cn.huanju.edu100.util.IpAddressUtil;
import cn.huanju.edu100.util.IpConvert;
import com.hqwx.thrift.client.annotation.GrpcStub;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class MsgCenterThriftResource {
    private static final Logger log = LoggerFactory.getLogger(MsgCenterThriftResource.class);

    private static final int APP_ID = 7;

    @GrpcStub(serviceId = "push-grpc")
    Edu100MsgServiceGrpc.Edu100MsgServiceBlockingStub stub;


    public Integer pushNewMsg(Integer tid, List<Long> uidList, String addInfo, String taskKey) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("TID", tid);
        params.put("UidList", uidList);
        params.put("TaskKey", StringUtils.isBlank(taskKey) ? String.valueOf(System.currentTimeMillis()) : taskKey);
        params.put("AddInfo", addInfo);

        try {
            log.info("pushNewMsg info: param:{}", GsonUtils.toDefaultJson(params));

            GrpcRequest.Builder builder = GrpcRequest.newBuilder();
            if (StringUtils.isNotBlank(MDC.get("traceId"))) {
                builder.setTraceId(MDC.get("traceId"));
            }
            builder.setMsg(GsonUtils.toDefaultJson(params));
            builder.setCodetype(1);
            builder.setAppid(APP_ID);
            builder.setClientIp(IpConvert.ipToLong(IpAddressUtil.getIpAddress()));
            GrpcRequest request = builder.build();
            GrpcResponse response = stub.withDeadlineAfter(5, TimeUnit.SECONDS).cmsgPushNewMsg(request);
            if (response != null && response.getCode() == 1 && StringUtils.isNotBlank(response.getMsg())) {
                return 1;
            } else {
                log.error("pushNewMsg param:{} errcode:{} errMsg:{}", GsonUtils.toDefaultJson(params),
                        response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            log.error("pushNewMsg failed: param:{} " + GsonUtils.toDefaultJson(params), e);
        } finally {
        }
        return null;
    }
}
