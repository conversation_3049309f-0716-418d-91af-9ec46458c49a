package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.study.model.UserDoneRecord;
import java.util.List;

public interface UserDoneRecordDao extends CrudDao<UserDoneRecord> {

    void deleteByParam(Long uid, Integer objType, Long objId) throws DataAccessException;

    List<UserDoneRecord> findListCus(Page<UserDoneRecord> condition, UserDoneRecord userDoneRecord, String startDate, String endDate)throws DataAccessException;

    long count(UserDoneRecord userDoneRecord, String startDate, String endDate) throws DataAccessException;
}