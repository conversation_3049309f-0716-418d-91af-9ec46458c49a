/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentKnowledge;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 学员知识点学习情况DAO接口
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentKnowledgeDao extends CrudDao<TutorStudentKnowledge> {

    List<TutorStudentKnowledge> getTutorStudentKnowledge(Map<String, Object> params)
            throws DataAccessException;

}
