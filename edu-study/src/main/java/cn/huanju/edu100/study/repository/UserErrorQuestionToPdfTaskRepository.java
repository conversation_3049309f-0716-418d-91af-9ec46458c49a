package cn.huanju.edu100.study.repository;

import cn.huanju.edu100.study.mapper.paper.UserErrorQuestionToPdfTaskMapper;
import cn.huanju.edu100.study.model.paper.UserErrorQuestionToPdfTaskPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqwx.study.dto.query.UserErrorQuestionToPdfTaskQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Objects;

@Slf4j
@Repository
public class UserErrorQuestionToPdfTaskRepository extends ServiceImpl<UserErrorQuestionToPdfTaskMapper, UserErrorQuestionToPdfTaskPO> {

    public UserErrorQuestionToPdfTaskPO findOne(UserErrorQuestionToPdfTaskQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getUid()) || StringUtils.isBlank(query.getTaskId())) {
            log.error("findOne parameter lose, uid or taskId is null.");
            return null;
        }
        LambdaQueryWrapper<UserErrorQuestionToPdfTaskPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserErrorQuestionToPdfTaskPO::getUid, query.getUid());
        queryWrapper.eq(UserErrorQuestionToPdfTaskPO::getTaskId, query.getTaskId());
        queryWrapper.last(" limit 1 ");
        return getBaseMapper().selectOne(queryWrapper);
    }
}
