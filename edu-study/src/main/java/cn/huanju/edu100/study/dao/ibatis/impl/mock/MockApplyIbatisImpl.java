/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.mock;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.mock.MockApplyDao;
import cn.huanju.edu100.study.model.mock.MockApply;
import cn.huanju.edu100.util.GsonUtil;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 模考考生报名DAO接口
 * <AUTHOR>
 * @version 2018-04-08
 */
public class MockApplyIbatisImpl extends CrudIbatisImpl2<MockApply> implements
		MockApplyDao {

	public MockApplyIbatisImpl() {
		super("MockApply");
	}

	@Override
	public List<MockApply> qryByMockExamId(Long mockExamId, Long uid) throws DataAccessException {
		if (mockExamId == null) {
			logger.error("list {} error, parameter mockExamId is null,mockExamId:{}", namespace, mockExamId);
			throw new DataAccessException("list error,entity is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();

			Map<String, Object> param = new HashMap<String, Object>();
			param.put("mockExamId", mockExamId);
			param.put("uid", uid);
			return (List<MockApply>) sqlMap.queryForList("MockApply.qryByMockExamId", param);
		} catch (SQLException e) {
			logger.error("list {} SQLException.mockExamId:{}", namespace, mockExamId, e);
			throw new DataAccessException("list SQLException error" + e.getMessage());
		}
	}

    @Override
    public List<MockApply> findMockApplyListByMockId(Long mockExamId, Long number, Long uid) throws DataAccessException {
        if (mockExamId == null) {
            logger.error("list {} error, parameter mockExamId is null,mockExamId:{}", namespace, mockExamId);
            throw new DataAccessException("list error,entity is null");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("mockExamId", mockExamId);
            param.put("number", number);
            if (uid != null) {
                param.put("uid", uid);
            }
            return (List<MockApply>) sqlMap.queryForList("MockApply.findMockApplyListByMockId", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.mockExamId:{}", namespace, mockExamId, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public Long countMockApplyByParam(Map<String, Object> param) throws DataAccessException{
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (Long)sqlMap.queryForObject("MockApply.countMockApplyByParam", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException", namespace, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public List<MockApply> getMockUseTimeRank(Long mockExamId, Long uid) throws DataAccessException {
        if (mockExamId == null || uid == null) {
            logger.error("list {} error, parameter mockExamId or uid is null,mockExamId:{},uid:{}", namespace, mockExamId,uid);
            throw new DataAccessException("list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("mockExamId", mockExamId);
            param.put("uid", uid);
            return (List<MockApply>) sqlMap.queryForList("MockApply.findMockUseTimeRankList", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.mockExamId:{}", namespace, mockExamId, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }
    @Override
    public MockApply qryByUserAnswerId(Long userAnswerId, Long uid) throws DataAccessException {
        if (userAnswerId == null || uid == null) {
            logger.error("qryByUserAnswerId {} error, parameter userAnswerId or uid is null,userAnswerId:{},uid:{}", namespace, userAnswerId,uid);
            throw new DataAccessException("list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("userAnswerId", userAnswerId);
            param.put("uid", uid);
            logger.info("qryByUserAnswerId.param："+ GsonUtil.toJson(param));
            return (MockApply) sqlMap.queryForObject("MockApply.qryByUserAnswerId", param);
        } catch (SQLException e) {
            logger.error("qryByUserAnswerId {} SQLException.userAnswerId:{}", namespace, userAnswerId, e);
            throw new DataAccessException("qryByUserAnswerId SQLException error" + e.getMessage());
        }
    }
}
