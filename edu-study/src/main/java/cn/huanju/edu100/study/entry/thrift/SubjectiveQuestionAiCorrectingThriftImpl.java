package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.question.SubjectiveQuestionAICorrectingLogPO;
import cn.huanju.edu100.study.repository.SubjectiveQuestionAiCorrectingRepository;
import cn.huanju.edu100.study.service.UserAnswerService;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.GsonUtils;
import cn.huanju.edu100.util.ParamUtils;
import com.google.gson.Gson;
import com.hqwx.study.dto.SubjectiveQuestionAICorrectingLogDTO;
import com.hqwx.study.vo.ReadOveredHomeworkAnswer;
import com.hqwx.study.vo.ReadOveredPaperAnswer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
public class SubjectiveQuestionAiCorrectingThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(SubjectiveQuestionAiCorrectingThriftImpl.class);

    @Resource
    private SubjectiveQuestionAiCorrectingRepository subjectiveQuestionAiCorrectingRepository;

    private static Gson gson = GsonUtil.getGson();

    @Resource
    private UserAnswerService userAnswerService;


    public response sty_getSubjectiveQuestionAiCorrectingLog(request req) throws BusinessException {
        String entry = "sty_getSubjectiveQuestionAiCorrectingLog";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long topicId = ParamUtils.getLong(params, "topicId", true);
            Long questionId = ParamUtils.getLong(params, "questionId", true);
            Long userAnswerId = ParamUtils.getLong(params, "userAnswerId", true);
            Integer limit = ParamUtils.getInt(params, "limit", true);

            if (ValidateUtils.isEmpty(uid)) {
                logger.error("{} fail.parameter uid is null or empty.",entry);
                throw new BusinessException(Constants.PARAM_LOSE, "parameter uid is null or empty");
            }
            List<SubjectiveQuestionAICorrectingLogPO> poList = subjectiveQuestionAiCorrectingRepository.getSubjectiveQuestionAiCorrectingLog(topicId,questionId, uid, userAnswerId, limit);

            List<SubjectiveQuestionAICorrectingLogDTO> dtoList = poList.stream().map(po->{
                SubjectiveQuestionAICorrectingLogDTO dto = new SubjectiveQuestionAICorrectingLogDTO();
                BeanUtils.copyProperties(po, dto);
                return dto;
            }).collect(Collectors.toList());

            res.setMsg(GsonUtils.toJson(dtoList));

        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);
        return res;
    }

    public response sty_addSubjectiveQuestionAiCorrectingLog(request req) throws BusinessException{
        String entry = "sty_addSubjectiveQuestionAiCorrectingLog";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            SubjectiveQuestionAICorrectingLogDTO paramsLogDTO = GsonUtil.getGenericGson().fromJson(req.getMsg(), SubjectiveQuestionAICorrectingLogDTO.class);

            SubjectiveQuestionAICorrectingLogPO po = new SubjectiveQuestionAICorrectingLogPO();
            BeanUtils.copyProperties(paramsLogDTO, po);

            Long id = subjectiveQuestionAiCorrectingRepository.addLog(po);

            res.setMsg(GsonUtils.toJson(id));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);
        return res;
    }

    public response sty_updateSubjectiveQuestionAiCorrectingLog(request req) throws BusinessException{
        String entry = "sty_updateSubjectiveQuestionAiCorrectingLog";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            SubjectiveQuestionAICorrectingLogDTO paramsLogDTO = GsonUtil.getGenericGson().fromJson(req.getMsg(), SubjectiveQuestionAICorrectingLogDTO.class);

            SubjectiveQuestionAICorrectingLogPO po = new SubjectiveQuestionAICorrectingLogPO();
            BeanUtils.copyProperties(paramsLogDTO, po);

            boolean result = subjectiveQuestionAiCorrectingRepository.updateLog(po);

            res.setMsg(GsonUtils.toJson(result));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);
        return res;
    }

    public response sty_findLastReadOveredSubjectivePaperAnswers(request req) throws BusinessException{
        String entry = "sty_findLastReadOveredSubjectivePaperAnswers";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long goodsId = ParamUtils.getLong(params, "goodsId", true);
            Long productId = ParamUtils.getLong(params, "productId", true);
            Long[] paperIds = ParamUtils.getLongArray(params, "paperIds");
            List<ReadOveredPaperAnswer> result =userAnswerService.findLastReadOveredSubjectivePaperAnswers(uid,Arrays.stream(paperIds).toList(),goodsId,productId);
            res.setMsg(GsonUtils.toJson(result));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_findLastReadOveredSubjectiveHomeworkAnswers(request req) throws BusinessException{
        String entry = "sty_findLastReadOveredSubjectiveHomeworkAnswers";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long goodsId = ParamUtils.getLong(params, "goodsId", true);
            Long productId = ParamUtils.getLong(params, "productId", true);
            List<Long> homeworkIds = ParamUtils.getLongList(params, "homeworkIds",false);
            List<ReadOveredHomeworkAnswer> result =userAnswerService.findLastReadOveredSubjectiveHomeworkAnswers(uid,homeworkIds,goodsId,productId);
            res.setMsg(GsonUtils.toJson(result));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
