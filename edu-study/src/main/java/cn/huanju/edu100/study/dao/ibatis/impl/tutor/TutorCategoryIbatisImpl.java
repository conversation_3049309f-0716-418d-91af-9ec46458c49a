/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorCategoryDao;
import cn.huanju.edu100.study.model.tutor.TutorCategory;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.springframework.util.CollectionUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 个性化考试DAO接口
 * <AUTHOR>
 * @version 2016-01-12
 */
public class TutorCategoryIbatisImpl extends CrudIbatisImpl2<TutorCategory> implements
		TutorCategoryDao {

	public TutorCategoryIbatisImpl() {
		super("TutorCategory");
	}

    @Override
    public List<TutorCategory> getCategoryByIdList(List<Long> categoryIdList)
            throws DataAccessException {

        if (CollectionUtils.isEmpty(categoryIdList)) {
            return null;
        }

        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("categoryIdList", categoryIdList);
            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorCategory>) sqlMap
                    .queryForList("TutorCategory.getCategoryByIdList", params);
        } catch (SQLException e) {
            logger.error("getCategoryByIdList SQLException.", e);
            throw new DataAccessException("getCategoryByIdList SQLException error");
        }
    }

}
