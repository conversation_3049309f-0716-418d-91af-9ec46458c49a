package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Reflections;
import com.hqwx.thrift.client.thrift.ThriftClientFactory;
import com.hqwx.thrift.client.thrift.ThriftClientWrapper;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.util.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @time 2015年6月11日 上午11:02:18
 */
@Slf4j
@Lazy
@Component
public class GoodsThriftServiceImpl extends
		GenericThriftServerImpl<cn.huanju.edu100.thrift.edu100_goods.Iface> {

	private static Map<String, Method> methodMap = new ConcurrentHashMap<String, Method>();

	@Autowired
	@Qualifier("goodsClientFactory")
	private ThriftClientFactory<cn.huanju.edu100.thrift.edu100_goods.Iface> clientFactory;

	@Override
	public response generalThriftMethodInvoke(Map<String, Object> param,
											  int appid, int clientIp, String methodName)
			throws DataAccessException {
		ThriftClientWrapper<cn.huanju.edu100.thrift.edu100_goods.Iface> client = null;
		response res = new response();
		Class<?> clazz = Reflections.getClassGenricType(this.getClass());
		try {
			client = clientFactory.createClient();
			request req = new request();
			req.setAppid(appid);
			req.setClient_ip(clientIp);
			req.setCodetype(1);
			req.setMsg(GsonUtils.toJson(param));
			Method method = methodMap.get(methodName);
			if (method == null) {
				method = clazz.getMethod(methodName, request.class);
				if (method != null) {
					methodMap.put(methodName, method);
				}
			}
			if (method != null) {
				res = (response) method.invoke(client.getClient(), req);
			}
		} catch (Exception e) {
			log.error("invoke {} ThriftServer method {} param:{} exception.",
					clazz.getSimpleName(), methodName, GsonUtils.toJson(param),
					e);
			throw new DataAccessException(e.getMessage());
		} finally {
			if (client != null) {
				client.close();
			}
		}
		return res;
	}

	@Override
	public response generalThriftMethodInvoke(Collection<Long> param,
											  int appid, int clientIp, String methodName)
			throws DataAccessException {
		ThriftClientWrapper<cn.huanju.edu100.thrift.edu100_goods.Iface> client = null;
		response res = new response();
		Class<?> clazz = Reflections.getClassGenricType(this.getClass());
		try {
			client = clientFactory.createClient();
			request req = new request();
			req.setAppid(appid);
			req.setClient_ip(clientIp);
			req.setCodetype(1);
			req.setMsg(GsonUtils.toJson(param));
			Method method = clazz.getMethod(methodName, request.class);
			if (method != null) {
				res = (response) method.invoke(client.getClient(), req);
			}
		} catch (Exception e) {
			log.error("invoke {} ThriftServer method {} param:{} exception.",
					clazz.getSimpleName(), methodName, GsonUtils.toJson(param),
					e);
			throw new DataAccessException(e.getMessage());
		} finally {
			if (client != null) {
				client.close();
			}
		}
		return res;
	}
}
