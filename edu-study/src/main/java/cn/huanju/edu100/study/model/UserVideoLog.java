package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.util.DateUtils;

import java.util.Date;
import java.util.List;

/**
 * 记录学生在学习录播课程过程中的信息Entity
 * <AUTHOR>
 * @version 2015-05-18
 */
public class UserVideoLog extends DataEntity<UserVideoLog> {

	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long courseId;		// course_id
	private Long clsId;		// cls_id
	private Long lessonId;		// lesson_id
	private Date startTime;		// start_time
	private Long length;		// length
	private Integer status;		// 0表示学习中，1表示学习完成，学习中和学习完成
	private String ip;
	private Integer type;
	private Date dbUpdateTime;

	private String platForm;	// 平台 web app
	private Integer videoSrc;	// 0 普通班型 1 微课  2 题目解析 3 微课班视频
	private Integer tutorType;	// 0:普通录播课,1:云私塾
	private Integer videoPosition;		// 当前观看到的时刻 不存uservideo表，临时变量，传给tutoruserlog，存tutor表
	private Long goodsId;// 商品id
	private Long secondCategory;// 考试
	private Long categoryId;// 科目
	private Integer startPosition;   //视频开始播放位置
	private String appid;   //app的类型 所属终端，web、PC客户端、环球网校APP、快题库、建造师题库…、快题库小程序
	private Long videoDuration;//视频时长
	private Long liveCourseId;
	private Long liveLessonId;
	private String speed;	//上传时的播放速率
	private Long resourceId = 0L;
	private Long playLength;	//播放时长 即时间流逝时长
	private Integer multiSpeed;	//是否多个倍速播放过 0未更新过倍速 1更新过倍速

	/**
	 * @return the ip
	 */
	public String getIp() {
		return ip;
	}

	/**
	 * @param ip the ip to set
	 */
	public void setIp(String ip) {
		this.ip = ip;
	}

	public UserVideoLog() {
		super();
	}

	public UserVideoLog(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Long getClsId() {
		return clsId;
	}

	public void setClsId(Long clsId) {
		this.clsId = clsId;
	}

	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Long getLength() {
		return length;
	}

	public void setLength(Long length) {
		this.length = length;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return "UserVideoLog [uid=" + uid + ", courseId=" + courseId + ", clsId=" + clsId + ", lessonId=" + lessonId
				+ ", startTime=" + startTime + ", length=" + length + ", status=" + status + ", ip=" + ip + "]";
	}

	public Date getDbUpdateTime() {
		return dbUpdateTime;
	}

	public void setDbUpdateTime(Date dbUpdateTime) {
		this.dbUpdateTime = dbUpdateTime;
	}

	public String getUserLogSetKey(){
		return "UserVideoLogHq_" + uid;
	}
	public String getUserLogUidFieldKey() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(this.getUid()).append("_").append(getUserLogFieldKey(this.getCourseId(), this.getClsId(), this.getLessonId(), new Date()));
		return stringBuilder.toString();
	}
	private String getUserLogFieldKey() {
		return getUserLogFieldKey(this.getCourseId(), this.getClsId(), this.getLessonId(), new Date());
	}
	private String getUserLogFieldKey(final Long courseId, final Long clsId, final Long lessonId, final String strDt){
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(null == courseId ? -1 : courseId).append("_").append(null == clsId ? -1 : clsId).append("_").append(null == lessonId ? -1 : lessonId).append("_").append(strDt);
		return stringBuilder.toString();
	}
	private String getUserLogFieldKey(final Long courseId, final Long clsId, final Long lessonId, final Date dt){
		return getUserLogFieldKey(courseId, clsId, lessonId, DateUtils.getYearToDay(dt));
	}

	public String getPlatForm() {
		return platForm;
	}

	public void setPlatForm(String platForm) {
		this.platForm = platForm;
	}

	public Integer getVideoSrc() {
		return videoSrc;
	}

	public void setVideoSrc(Integer videoSrc) {
		this.videoSrc = videoSrc;
	}

	public Integer getTutorType() {
		return tutorType;
	}

	public void setTutorType(Integer tutorType) {
		this.tutorType = tutorType;
	}

	public Integer getVideoPosition() {
		return videoPosition;
	}

	public void setVideoPosition(Integer videoPosition) {
		this.videoPosition = videoPosition;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getStartPosition() {
		return startPosition;
	}

	public void setStartPosition(Integer startPosition) {
		this.startPosition = startPosition;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public Long getVideoDuration() {
		return videoDuration;
	}

	public void setVideoDuration(Long videoDuration) {
		this.videoDuration = videoDuration;
	}

	public Long getLiveCourseId() {
		return liveCourseId;
	}

	public void setLiveCourseId(Long liveCourseId) {
		this.liveCourseId = liveCourseId;
	}

	public Long getLiveLessonId() {
		return liveLessonId;
	}

	public void setLiveLessonId(Long liveLessonId) {
		this.liveLessonId = liveLessonId;
	}

	public String getSpeed() {
		return speed;
	}

	public void setSpeed(String speed) {
		this.speed = speed;
	}

	public Long getResourceId() {
		return resourceId;
	}

	public void setResourceId(Long resourceId) {
		this.resourceId = resourceId;
	}

	public Long getPlayLength() {
		return playLength;
	}

	public void setPlayLength(Long playLength) {
		this.playLength = playLength;
	}

	public Integer getMultiSpeed() {
		return multiSpeed;
	}

	public void setMultiSpeed(Integer multiSpeed) {
		this.multiSpeed = multiSpeed;
	}
}
