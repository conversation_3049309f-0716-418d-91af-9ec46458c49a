package cn.huanju.edu100.study.model.dto;

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/9/7.
 *
 * 用户最近一次答题情况统计
 */
public class UserAnswerHomeworkLastInfo {

    private Long lessonId;

    private Long userHomeworkId;    //最近一次的作答id

    private Long totalCount; //提交课后作业总数量

    private Long finishCount;    //提交作业的数量

    private String accuracy;    //本次做题准确率


    public Long getLessonId() {
        return lessonId;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public Long getUserHomeworkId() {
        return userHomeworkId;
    }

    public void setUserHomeworkId(Long userHomeworkId) {
        this.userHomeworkId = userHomeworkId;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Long getFinishCount() {
        return finishCount;
    }

    public void setFinishCount(Long finishCount) {
        this.finishCount = finishCount;
    }

    public String getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(String accuracy) {
        this.accuracy = accuracy;
    }
}
