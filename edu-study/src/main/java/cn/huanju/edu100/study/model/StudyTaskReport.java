package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 学生学习任务Entity
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudyTaskReport extends DataEntity<StudyTaskReport> {
	
	private static final long serialVersionUID = 1L;
	private Integer taskCount;			// 
	private Integer finishCount;		// 
	private Integer unfinishCount;		// 
	
	public StudyTaskReport() {
		super();
	}

	public StudyTaskReport(Long id){
		super(id);
	}

	public Integer getTaskCount() {
		return taskCount;
	}

	public void setTaskCount(Integer taskCount) {
		this.taskCount = taskCount;
	}

	public Integer getFinishCount() {
		return finishCount;
	}

	public void setFinishCount(Integer finishCount) {
		this.finishCount = finishCount;
	}

	public Integer getUnfinishCount() {
		return unfinishCount;
	}

	public void setUnfinishCount(Integer unfinishCount) {
		this.unfinishCount = unfinishCount;
	}

}