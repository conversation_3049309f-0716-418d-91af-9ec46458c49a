package cn.huanju.edu100.study.service.onetoone;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.onetoone.VLessonRelation;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 直播课节对应关系表Service
 * <AUTHOR>
 * @version 2017-11-22
 */
public interface VLessonRelationService extends BaseService<VLessonRelation> {

    List<VLessonRelation> findListByLiveLessonIdList(List<Long> liveLessonIdList,Integer type) throws DataAccessException,BusinessException;


    List<VLessonRelation> findListByParam(VLessonRelation param) throws DataAccessException;
}