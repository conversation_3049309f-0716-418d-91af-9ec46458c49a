/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.StudyTaskPushRes;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 任务推荐资源（微课）列表Service
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface StudyTaskPushResService extends BaseService<StudyTaskPushRes> {

	List<StudyTaskPushRes> findListByTaskIdList(List<Long> taskIdList,
			Long uid) throws DataAccessException;
}
