package cn.huanju.edu100.study.service.impl.calculate;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.calculate.TikuLastChapterExerciseDao;
import cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise;
import cn.huanju.edu100.study.service.calculate.TikuLastChapterExerciseService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TikuLastChapterExerciseServiceImpl extends BaseServiceImpl<TikuLastChapterExerciseDao,
        TikuLastChapterExercise> implements TikuLastChapterExerciseService {

    @Override
    public TikuLastChapterExercise getByUidBoxId(Long uid, Long boxId) throws DataAccessException {

        return dao.getByUidBoxId(uid, boxId);
    }
    @Override
    public TikuLastChapterExercise getByUidBoxIds(Long uid, List<Long> boxIds) throws DataAccessException {

        return dao.getByUidBoxIds(uid, boxIds);
    }
}
