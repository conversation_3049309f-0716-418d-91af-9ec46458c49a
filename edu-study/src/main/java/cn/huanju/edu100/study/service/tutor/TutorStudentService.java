package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorStudent;
import cn.huanju.edu100.exception.DataAccessException;

/**
 * 个性化学员Service
 * <AUTHOR>
 * @version 2016-01-12
 */
public interface TutorStudentService extends BaseService<TutorStudent> {

    TutorStudent getTutorStudentByUid(Long uid) throws DataAccessException;

}
