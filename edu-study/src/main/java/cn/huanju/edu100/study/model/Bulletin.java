package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.util.upload.OssUtil;

import java.util.Date;

/**
 * 网校公告Entity
 * <AUTHOR>
 * @version 2015-05-15
 */
public class Bulletin extends DataEntity<Bulletin> {

	private static final long serialVersionUID = 1L;
	private String title;		// title
	private String url;		// url
	private String content;		// content
	private Integer status;		// status
	private Date pushTime;		// push_time
	private Date pullTime;		// pull_time
	private String ip;		// ip

	private Integer isShow; //是否显示
    private Integer sort; //排序值
    private Integer type;       // status
    private Integer toType;       // 推送客户端方式

    private BulletinRule bulletinRule; //公告规则内容

	public Bulletin() {
		super();
	}

	public Bulletin(Long id){
		super(id);
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = OssUtil.bs2UrlConvertToOssUrl(content);
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getPushTime() {
		return pushTime;
	}

	public void setPushTime(Date pushTime) {
		this.pushTime = pushTime;
	}

	public Date getPullTime() {
		return pullTime;
	}

	public void setPullTime(Date pullTime) {
		this.pullTime = pullTime;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getToType() {
        return toType;
    }

    public void setToType(Integer toType) {
        this.toType = toType;
    }

    public BulletinRule getBulletinRule() {
        return bulletinRule;
    }

    public void setBulletinRule(BulletinRule bulletinRule) {
        this.bulletinRule = bulletinRule;
    }

}
