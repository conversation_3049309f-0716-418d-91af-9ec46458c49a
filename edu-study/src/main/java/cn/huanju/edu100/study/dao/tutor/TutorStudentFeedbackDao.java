/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentFeedback;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 老师回访记录DAO接口
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentFeedbackDao extends CrudDao<TutorStudentFeedback> {

    List<TutorStudentFeedback> listFeedBackByUid(Map<String, Object> paramsMap)
            throws DataAccessException;

    Integer getFeedBackCount(Map<String, Object> params) throws DataAccessException;

}
