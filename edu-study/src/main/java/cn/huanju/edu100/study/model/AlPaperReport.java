package cn.huanju.edu100.study.model;


import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;
import java.util.List;

/**
 * @description: 我是干啥的
 * @author: wus<PERSON>yu<PERSON>
 * @create: 2020-03-05 14:37
 **/

public class AlPaperReport extends DataEntity<AlPaperReport> {
    private Long resourceId;
    private Long uid;
    private String name;//课件名称/试卷名称
    private Long categoryId;//所属科目
    private Integer studyCount;//视频学习次数/题目个数
    private Date lastStudyTime;//最后一次学习时间/试卷作答时间

    private Double rightRate;
    private Double score;
    private Date date;
    private Long userAnswerId;//答题id

    //查询条件
    private Integer startMaster;
    private Integer endMaster;
    private Date startTime;
    private Date endTime;
    private Integer resourceType;//1视频2资料

    private List<Long> resIds;

    private Integer isFirst;//是否是第一次1：不是，0是


    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getStudyCount() {
        return studyCount;
    }

    public void setStudyCount(Integer studyCount) {
        this.studyCount = studyCount;
    }

    public Date getLastStudyTime() {
        return lastStudyTime;
    }

    public void setLastStudyTime(Date lastStudyTime) {
        this.lastStudyTime = lastStudyTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getStartMaster() {
        return startMaster;
    }

    public void setStartMaster(Integer startMaster) {
        this.startMaster = startMaster;
    }

    public Integer getEndMaster() {
        return endMaster;
    }

    public void setEndMaster(Integer endMaster) {
        this.endMaster = endMaster;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public List<Long> getResIds() {
        return resIds;
    }

    public void setResIds(List<Long> resIds) {
        this.resIds = resIds;
    }


    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public Double getRightRate() {
        return rightRate;
    }

    public void setRightRate(Double rightRate) {
        this.rightRate = rightRate;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Long getUserAnswerId() {
        return userAnswerId;
    }

    public void setUserAnswerId(Long userAnswerId) {
        this.userAnswerId = userAnswerId;
    }

    public Integer getIsFirst() {
        return isFirst;
    }

    public void setIsFirst(Integer isFirst) {
        this.isFirst = isFirst;
    }
}
