package cn.huanju.edu100.study.config.rpc;

import cn.huanju.edu100.grpc.client.impl.KnowledgeGrpcClient;
import cn.huanju.edu100.grpc.service.*;
import cn.huanju.edu100.study.config.rpc.leaf.LeafThriftService;
import cn.huanju.edu100.stustamp.util.IpConvert;
import cn.huanju.edu100.thrift.*;
import cn.huanju.edu100.util.SpringBeanAwareFactory;
import com.hqwx.goods.client.DataConfigService;
import com.hqwx.goods.client.ProductLessonService;
import com.hqwx.goods.client.impl.DataConfigServiceClientImpl;
import com.hqwx.goods.client.impl.ProductLessonServiceClientImpl;
import com.hqwx.thrift.client.annotation.GrpcStub;
import com.hqwx.thrift.client.thrift.ThriftClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import static cn.huanju.edu100.study.resource.GoodsResource.APP_ID;

@Configuration
public class RPCConfig {

    @GrpcStub(serviceId = "edu-goods-grpc")
    ProductLessonServiceGrpc.ProductLessonServiceBlockingStub productLessonServiceBlockingStub;

    @GrpcStub(serviceId = "edu-goods-grpc")
    DataConfigServiceGrpc.DataConfigServiceBlockingStub dataConfigServiceBlockingStub;

    @GrpcStub(serviceId = "edu-knowledge-grpc")
    KnowledgeServiceGrpc.KnowledgeServiceBlockingStub knowledgeServiceBlockingStub;

    @Bean
    @Order(1)
    public SpringBeanAwareFactory springBeanAwareFactory(){
        return new SpringBeanAwareFactory();
    }

    @Bean("analyseClientFactory")
    public ThriftClientFactory analyseClientFactory() throws Exception {
        return new ThriftClientFactory(edu100_analyse.Iface.class,"edu-analyse-grpc", Edu100AnalyseServiceGrpc.Edu100AnalyseServiceBlockingStub.class);
    }

    @Bean("goodsClientFactory")
    public ThriftClientFactory goodsClientFactory() throws Exception {
        return new ThriftClientFactory(edu100_goods.Iface.class,"edu-goods-grpc", Edu100GoodsServiceGrpc.Edu100GoodsServiceBlockingStub.class);
    }

    @Bean("hqUserClientFactory")
    public ThriftClientFactory hqUserClientFactory() throws Exception {
        return new ThriftClientFactory(edu_user.Iface.class,"edu-user-grpc", Edu100UserServiceGrpc.Edu100UserServiceBlockingStub.class);
    }

    @Bean("knowledgeClientFactory")
    public ThriftClientFactory knowledgeClientFactory() throws Exception {
        return new ThriftClientFactory(edu100_knowledge.Iface.class,"edu-knowledge-grpc", Edu100KnowledgeServiceGrpc.Edu100KnowledgeServiceBlockingStub.class);
    }

    @Bean("leafClientFactory")
    public ThriftClientFactory leafClientFactory() throws Exception {
        return new ThriftClientFactory(edu_leaf.Iface.class,"edu-leaf-grpc", Edu100LeafServiceGrpc.Edu100LeafServiceBlockingStub.class);
    }

    @Bean("memClientFactory")
    public ThriftClientFactory memClientFactory() throws Exception {
        return new ThriftClientFactory(edu100_mem.Iface.class,"edu-member-grpc", Edu100MemberServiceGrpc.Edu100MemberServiceBlockingStub.class);
    }

    @Bean("hqSearchClientFactory")
    public ThriftClientFactory hqSearchClientFactory() throws Exception {
        return new ThriftClientFactory(edu100_search.Iface.class,"edu-search-grpc", Edu100HqSearchServiceGrpc.Edu100HqSearchServiceBlockingStub.class);
    }

    @Bean("hqStustampClientFactory")
    public ThriftClientFactory hqStustampClientFactory() throws Exception {
        return new ThriftClientFactory(edu100_stustamp.Iface.class,"edu-stustamp-grpc", Edu100StustampServiceGrpc.Edu100StustampServiceBlockingStub.class);
    }

    @Bean("leafThriftService")
    public LeafThriftService leafThriftService(){
        return new LeafThriftService();
    }

    @Bean("productLessonGrpcService")
    public ProductLessonService productLessonGrpcService() {
        return new ProductLessonServiceClientImpl(productLessonServiceBlockingStub, builder -> {
            builder.setAppid(APP_ID)
                    .setCodetype(1)
                    .setSchId(2L).setPschId(14L)
                    .setClientIp(IpConvert.ipToLong("127.0.0.1"));
        });
    }

    @Bean("dataConfigGrpcService")
    public DataConfigService dataConfigGrpcService() {
        return new DataConfigServiceClientImpl(dataConfigServiceBlockingStub, builder -> {
            builder.setAppid(APP_ID)
                    .setCodetype(1)
                    .setClientIp(IpConvert.ipToLong("127.0.0.1"));
        });
    }
    @Bean("knowledgeGrpcClient")
    public KnowledgeGrpcClient knowledgeGrpcClient() {
        return new KnowledgeGrpcClient(knowledgeServiceBlockingStub);
    }
}
