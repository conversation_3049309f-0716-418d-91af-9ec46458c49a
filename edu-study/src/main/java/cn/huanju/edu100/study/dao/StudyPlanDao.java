/**
 *
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.PlanPhase;
import cn.huanju.edu100.study.model.StudyPlan;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;

/**
 * 学习计划DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface StudyPlanDao extends CrudDao<StudyPlan> {

    public Collection<StudyPlan> qryStudyPlansByGid(Long gid) throws DataAccessException,BusinessException;

    public Collection<PlanPhase> queryPlanPhasesByPids(String pids) throws DataAccessException,BusinessException;

}
