package cn.huanju.edu100.study.model;

import java.util.Date;

import cn.huanju.edu100.persistence.model.HqFiledAnnotation;
import cn.huanju.edu100.util.JSONUtils;
import cn.huanju.edu100.util.upload.OssUtil;
import cn.huanju.edu100.study.config.ibatis.encrypt.SM4EncryptCardTypeHandler;
import cn.huanju.edu100.study.config.ibatis.encrypt.SM4EncryptNameTypeHandler;
import cn.huanju.edu100.study.config.ibatis.encrypt.SM4EncryptPhoneTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户协议Entity
 * <AUTHOR>
 * @version 2015-05-13
 */
@Data
@TableName(value = "user_agreement",autoResultMap = true)
public class UserAgreement{//} extends DataEntity<UserAgreement> {
	
	private static final long serialVersionUID = 1L;

	@TableId(type= IdType.AUTO)
	private Long id;
	private Long orderId;		// order_id
	private Long uid;		// uid
	private String username;		// username
	private String title;		// title
	@TableField(exist = false)
	private String phone;		// phone
	private Integer status;		// status
	private Date buyDate;		// buy_date
	private Date agreeDate;		// agree_date
	private String content;		// content
	private String ip;		// ip
	private String goodsId;		// goods_id
	private String goodsName;		// goods_name
	@TableField(exist = false)
	private String name;		// name
	@TableField(exist = false)
	private String idcard;		// idcard
	private String address;		// address
	private String email;		// email
	private Integer orgIsFirstParty;		//机构是否是甲方：0否，1是
	private Long agreementId;		//协议ID

	private Long schId;
	private String delFlag = "0"; 	// 删除标记（0：正常；1：删除；2：审核）

	@TableField(typeHandler = SM4EncryptPhoneTypeHandler.class)
	private String encPhone;
	@TableField(typeHandler = SM4EncryptCardTypeHandler.class)
	private String encIdcard;
	@TableField(typeHandler = SM4EncryptNameTypeHandler.class)
	private String encName;

	@TableField(exist = false)
	private Integer qryUid;		// idcard

	public void setName(String name){
		this.name = name;
		this.setEncName(name);
	}
	public String getName(){
		if(StringUtils.isNotBlank(name)){
			return name;
		}else{
			return getEncName();
		}

	}
	public void setIdcard(String idcard){
		this.idcard = idcard;
		setEncIdcard(idcard);
	}

	public String getIdcard(){
		if(StringUtils.isNotBlank(idcard)){
			return idcard;
		}else{
			return getEncIdcard();
		}
	}

	public void setPhone(String phone){
		this.phone = phone;
		setEncPhone(phone);
	}

	public String getPhone(){
		if(StringUtils.isNotBlank(phone)){
			return phone;
		}else{
			return getEncPhone();
		}
	}

	@HqFiledAnnotation(filedGrpLevel = 3)
	protected Long createBy; // 创建者
	@HqFiledAnnotation(filedGrpLevel = 3)
	protected Date createDate; // 创建日期
	@HqFiledAnnotation(filedGrpLevel = 3)
	protected Long updateBy; // 更新者
	@HqFiledAnnotation(filedGrpLevel = 3)
	protected Date updateDate; // 更新日期

	public void setContent(String content) {
		this.content = OssUtil.bs2UrlConvertToOssUrl(content);
	}
	//主要是Gson的问题,所以这个查询和写入的都调用一下
	public UserAgreement initEncAttr(){
		if(StringUtils.isNotBlank(this.name)) {
			this.setEncName(this.name);
		}
		if(StringUtils.isNotBlank(this.idcard)) {
			this.setIdcard(this.idcard);
		}
		if(StringUtils.isNotBlank(this.phone)) {
			this.setEncPhone(this.phone);
		}
		return this;
	}
	//主要是Gson的问题,所以这个查询和写入的都调用一下
	public UserAgreement initAttr(){
		if(StringUtils.isNotBlank(this.encName)) {
			this.name = this.encName;
		}
		if(StringUtils.isNotBlank(this.encIdcard)) {
			this.idcard = this.encIdcard;
		}
		if(StringUtils.isNotBlank(this.encPhone)) {
			this.phone = this.encPhone;
		}
		return this;
	}
	public static void main(String[] args){
		UserAgreement userAgreement = new UserAgreement();
		userAgreement.setName("haha");
		userAgreement.setPhone("13312341234");
		userAgreement.setIdcard("123456781234567890");
		userAgreement.setAgreementId(1L);
		userAgreement.setAddress("xxx");
		userAgreement.setAgreeDate(new Date());
		System.out.println(JSONUtils.toJsonString(userAgreement));
	}
}