package cn.huanju.edu100.study.model.goods;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 关联产品Entity
 * <AUTHOR>
 * @version 2015-06-18
 */
public class GoodsPairs extends DataEntity<GoodsPairs> {
	
	private static final long serialVersionUID = 1L;
	private Long goodsId;		// 商品id
	private Integer type;		// 类型，默认值是0,1表示配件，0表示赠品
	private Long relateId;		// relate_id
	private Double price;		// price

	private Double realCostPrice;		// 商品的成本价格
	private Double defaultRealCostPrice;		// 不包含服务、第三方支付的成本价

	public GoodsPairs() {
		super();
	}

	public GoodsPairs(Long id){
		super(id);
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public Long getRelateId() {
		return relateId;
	}

	public void setRelateId(Long relateId) {
		this.relateId = relateId;
	}
	
	public Double getPrice() {
		return price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}

	public Double getRealCostPrice() {
		return realCostPrice;
	}

	public void setRealCostPrice(Double realCostPrice) {
		this.realCostPrice = realCostPrice;
	}

	public Double getDefaultRealCostPrice() {
		return defaultRealCostPrice;
	}

	public void setDefaultRealCostPrice(Double defaultRealCostPrice) {
		this.defaultRealCostPrice = defaultRealCostPrice;
	}
}