package cn.huanju.edu100.study.service.onetoone;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.onetoone.VStudentProduct;
import cn.huanju.edu100.study.model.tutor.TutorStudentCategory;
import cn.huanju.edu100.exception.DataAccessException;

/**
 * 学员购买记录Service
 * <AUTHOR>
 * @version 2016-04-19
 */
public interface VStudentProductService extends BaseService<VStudentProduct> {

    boolean saveStudentPrivilege(TutorStudentCategory category, String ip) throws DataAccessException;

    boolean deleteStudentPrivileg(Long orderId, Long goodsId, Long productId) throws DataAccessException;
}
