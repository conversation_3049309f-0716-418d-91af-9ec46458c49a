/*
 * @(#) Consts.java 
 * Copyright(c) 欢聚时代科技有限公司
*/
/*
 * Copyright (C) 多玩游戏 ©2005-2012.
 * 
 * @# Consts.java
 * 
 */
package cn.huanju.edu100.study.util;

import org.apache.commons.lang.math.RandomUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * 
 * 
 * @version 1.0
 * <AUTHOR>
 * @time 2015年5月30日 下午4:28:47
 */
public class RedisConsts {
	static final String QUESTION_STATIC_REDIS_KEY = "q_static";
	static final String PAPER_STATIC_REDIS_KEY = "p_static_";
	
	static final String QUESTION_STATIC_LOCK_KEY = "p_static_t_lock";
	static final String QUESTION_STATIC_DETAIL_LOCK_KEY = "p_static_d_lock";
	
	static final String USER_QUESTION_BOX_SYNC_LOCK_KEY = "u_qbox_sync_lock";
	
	static final String USER_HOMEWORK_ANSWER_REDIS_KEY = "u:home:";
	static final String USER_PAPER_ANSWER_REDIS_KEY = "u:paper:";
	static final String USER_VIDEOLOG_LOCK_KEY = "user_video_log_lock";
	static final String TUTOR_USER_VIDEOLOG_LOCK_KEY = "tutor_user_video_log_lock";
	static final String DEL_USER_VIDEOLOG_LOCK_KEY = "del_user_video_log_lock";
	
	static final String QUESTION_REDIS_KEY = "question_";
	static final String QUESTIONS_INFO_REDIS_KEY = "quesion_info_";
	
	//用户每日上报纠错题目的次数Key
	static final String USER_RP_ERR_QUSE_NUM_KEY = "user_rp_err_cnt_k:";
	//用户每日上报纠错题目的次数Field
	static final String USER_RP_ERR_QUSE_NUM_FIELD = "user_rp_f:";

	// 题目总数 答对数 答错数
	static final String QUESTION_TOTAL = "qst_tt_";
	static final String QUESTION_RIGHT = "qst_rt_";
	static final String QUESTION_WRONG = "qst_wg_";

	public static final String REPORT_WEEK_NUM_KEY = "report_week_num"; //答题报告周计数
	public static final String REPORT_WEEK_MAX_ANSWER_KEY = "report_week_max_answer_"; //答题报告周全站最高答题数
	public static final String REPORT_WEEK_LIST_KEY = "report_week_list_";

	public static final String LOAD_DONE_QUESTION_SET_KEY = "loaded_done_question_set";
    public static final String LOAD_WRONG_QUESTION_SET_KEY = "loaded_wrong_question_set";
    public static final String LOAD_RECITE_QUESTION_SET_KEY = "loaded_recite_question_set";
	public static final String LOAD_WIPE_OUT_QUESTION_SET_KEY = "loaded_wipe_out_wrong_question_set";

	/**
	 * 用户收藏题目
	 */
	static final String USER_COLLECT_QUESTION_REDIS_KEY = "u_collect_q_";
	
	/**
	 * 用户上次做题记录
	 */
	static final String USER_LAST_EXERCISE_REDIS_KEY = "u_last_e_";
	
	/**
	 * 用户做过的历史题目key
	 * */
	static final String USER_HIS_QUESTIONS_INFO_REDIS_KEY = "q_info_his_";
	
	/**
	 * 题库教材Key
	 * */
	static final String TeachBook_QuestionBox_Relation_Key = "teach_qb_";
	
	/**
	 * 题库教材-章节 field
	 * */
	static final String TeachBook_QuestionBox_Chapter_Filed = "chp:";
	
	/**
	 * 题库教材 - 知识点 field
	 * */
	static final String TeachBook_QuestionBox_Knowledge_Filed = "klg:";
	
	/**
	 * 用户在题库中做错过的题目 key
	 * 
	 * */
	static final String User_QuestionBox_Wrong = "qBox_w_";

	/**
	 * 用户消灭在题库中做错过的题目 key
	 *
	 * */
	static final String Wipe_Out_QuestionBox_Wrong = "wipe_out_qBox_w_";

	/**
	 * 周用户在题库中做错过的题目 key
	 *
	 * */
	static final String Week_User_QuestionBox_Wrong = "week_qBox_w_";
	
	/**
	 * 用户在题库中做错过的题目 章节field
	 * 
	 * */
	static final String User_QuestionBox_Wrong_chapter = "cp:wrong:";
	
	/**
	 * 用户在题库中做错过的题目 知识点field
	 * 
	 * */
	static final String User_QuestionBox_Wrong_knowledge = "klg:wrong:";
	
	/**
	 * 用户在题库中做错过的题目 题型field
	 * 
	 * */
	public static final String User_QuestionBox_Wrong_QType = "qtype:wrong:";


	/**
	 * 用户在题库中做过的题目 key
	 * 
	 * */
	static final String User_QuestionBox_Done = "qBox_d_";

	/**
	 * 周用户在题库中做过的题目 key
	 *
	 * */
	static final String Week_User_QuestionBox_Done = "week_qBox_d_";
	
	/**
	 * 用户在题库中做过的题目 章节field
	 * 
	 * */
	public static final String User_QuestionBox_Done_chapter = "cp:done:";
	
	/**
	 * 用户在题库中做过的题目 知识点field
	 * 
	 * */
	public static final String User_QuestionBox_Done_knowledge = "klg:done:";
	
	/**
	 * 用户在题库中做过的题目 题型field
	 * 
	 * */
	public static final String User_QuestionBox_Done_QType = "qtype:done:";
	
	/**
	 * （背题模式）用户在题库中背过的题目 key
	 * 
	 * */
	static final String User_QuestionBox_Recite = "qBox_read_";
	
	/**
	 * （背题模式）用户在题库中背过的题目 章节field
	 * 
	 * */
	static final String User_QuestionBox_Recite_chapter = "cp:read:";
	
	/**
	 * （背题模式）用户在题库中背过的题目 知识点field
	 * 
	 * */
	static final String User_QuestionBox_Recite_knowledge = "klg:read:";
	
	/**
	 * （背题模式）用户在题库中做背的题目 题型field
	 * 
	 * */
	public static final String User_QuestionBox_Recite_QType = "qtype:read:";

	/**
	 * 用户某道题目的历史详情 key
	 * 
	 * */
	static final String User_Question_History = "u_box_q_his_";
	
	/**
	 * 用户某道题目的历史详情 作答次数field
	 * 
	 * */
	static final String User_Question_History_TotalCount = "total:";
	
	/**
	 * 用户某道题目的历史详情 答对次数field
	 * 
	 * */
	static final String User_Question_History_RightCount = "right:";
	
	/**
	 * 用户某道题目的历史详情  最后一次答案field
	 * 
	 * */
	static final String User_Question_History_LastAnswer = "lastAns:";
	
	/**
	 * 用户某道题目的历史详情 key 新版(ub_q_his_[uid]_[questionId])
	 * 
	 * */
	static final String User_Question_History_New = "uq_h_";
	
	/**
	 * 用户某道题目的历史详情 作答次数field 新版(total)
	 * 
	 * */
	static final String User_Question_History_TotalCount_New = "T";
	
	/**
	 * 用户某道题目的历史详情 答对次数field 新版(right:[topicId])
	 * 
	 * */
	static final String User_Question_History_RightCount_New = "RC:";
	
	/**
	 * 用户某道题目的历史详情  最后一次答案field(lastAns:topicId)
	 * 
	 * */
	static final String User_Question_History_LastAnswer_New = "LA:";

	/**
	 * 用户题库里最近一次作答的题目
	 */
	static final String User_Box_Latest_Answer_Qids = "ubq_latest_";

	/**
	 * 用户错题答对次数
	 */
	static final String USER_ERROR_QUESTION_TIME_HASH = "user_error_time_hash_%s_%s";


	/**
	 * 用户学习中心错题答对次数
	 */
	static final String USER_CENTER_SUB_ERROR_QUESTION_TIME_HASH = "user_center_sub_error_time_hash_%s";

	static final String USER_AUTO_REMOVE_ERROR_QUESTION_CONFIG = "user_auto_remove_error_question_config_%s";
	static final String USER_AUTO_REMOVE_ERROR_QUESTION_CONFIG_NEW = "user_auto_remove_error_question_config_new_%s";

	/**
	 * @param uid
	 * @param boxId
	 * */
	public static String getUserBoxLatestAnswerQids(Long uid, Long boxId) {
		return User_Box_Latest_Answer_Qids+uid+"_"+boxId;
	}

	/**
	 * 某个用户的答题历史
	 * @param uid
	 * @param questionId
	 * */
	public static String getUserQuestionHistory(Long uid, Long questionId) {
		return User_Question_History_New+uid+"_"+questionId;
	}

	/**
	 * 
	 * */
	public static String getUserQuestionHistoryTotalcount() {
		return User_Question_History_TotalCount_New;
	}

	/**
	 * @param topicId
	 * */
	public static String getUserQuestionHistoryRightcount(Long topicId) {
		return User_Question_History_RightCount_New+topicId;
	}

	/**
	 * @param topicId
	 * */
	public static String getUserQuestionHistoryLastanswer(Long topicId) {
		return User_Question_History_LastAnswer_New+topicId;
	}
	
	/**
	 * @param uid
	 * */
	public static String getUserQuestionHistory(Long uid) {
		return User_Question_History+uid;
	}

	/**
	 * @param questionId
	 * */
	public static String getUserQuestionHistoryTotalcount(Long questionId) {
		return User_Question_History_TotalCount+questionId;
	}

	/**
	 * @param questionId
	 * */
	public static String getUserQuestionHistoryRightcount(Long questionId, Long topicId) {
		return User_Question_History_RightCount+questionId+":"+topicId;
	}

	/**
	 * @param questionId
	 * */
	public static String getUserQuestionHistoryLastanswer(Long questionId, Long topicId) {
		return User_Question_History_LastAnswer+questionId+":"+topicId;
	}

	/**
	 * @param uid
	 * @param boxId
	 * */
	public static String getUserQuestionboxDone(Long uid, Long boxId) {
		return User_QuestionBox_Done+uid+"_"+boxId;
	}

	/**
	 * @param uid
	 * @param boxId
	 * @param reportWeekNum
	 * */
	public static String getWeekUserQuestionboxDone(Long uid, Long boxId, Long reportWeekNum) {
		return Week_User_QuestionBox_Done+uid+"_"+boxId+"_"+reportWeekNum;
	}

	/**
	 * @param chapterId
	 * */
	public static String getUserQuestionboxDoneChapter(Long teachBookId, Long chapterId) {
		return User_QuestionBox_Done_chapter+teachBookId+":"+chapterId;
	}

	/**
	 * @param knowledgeId
	 * */
	public static String getUserQuestionboxDoneKnowledge(Long teachBookId,Long knowledgeId) {
		return User_QuestionBox_Done_knowledge+teachBookId+":"+knowledgeId;
	}

	/**
	 * @param qType
	 * */
	public static String getUserQuestionboxDoneQtype(Integer qType) {
		return User_QuestionBox_Done_QType+qType;
	}

	/**
	 * @param uid
	 * @param boxId
	 * */
	public static String getUserQuestionboxWrong(Long uid, Long boxId) {
		return User_QuestionBox_Wrong+uid+"_"+boxId;
	}

	/**
	 * @param uid
	 * @param boxId
	 * */
	public static String getWipeOutQuestionboxWrong(Long uid, Long boxId) {
		return Wipe_Out_QuestionBox_Wrong+uid+"_"+boxId;
	}

	/**
	 * @param uid
	 * @param boxId
	 * @param reportWeekNum
	 * */
	public static String getWeekUserQuestionboxWrong(Long uid, Long boxId, Long reportWeekNum) {
		return Week_User_QuestionBox_Wrong+uid+"_"+boxId+"_"+reportWeekNum;
	}

	/**
	 * @param chapterId
	 * */
	public static String getUserQuestionboxWrongChapter(Long teachBookId,Long chapterId) {
		return User_QuestionBox_Wrong_chapter+teachBookId+":"+chapterId;
	}

	/**
	 * @param knowledgeId
	 * */
	public static String getUserQuestionboxWrongKnowledge(Long teachBookId,Long knowledgeId) {
		return User_QuestionBox_Wrong_knowledge+teachBookId+":"+knowledgeId;
	}

	/**
	 * @param qType
	 * */
	public static String getUserQuestionboxWrongQtype(Integer qType) {
		return User_QuestionBox_Wrong_QType+qType;
	}
	
	/**
	 * @param uid
	 * @param boxId
	 * */
	public static String getUserQuestionboxRecite(Long uid, Long boxId) {
		return User_QuestionBox_Recite+uid+"_"+boxId;
	}

	/**
	 * @param chapterId
	 * */
	public static String getUserQuestionboxReciteChapter(Long teachBookId,Long chapterId) {
		return User_QuestionBox_Recite_chapter+teachBookId+":"+chapterId;
	}

	/**
	 * @param knowledgeId
	 * */
	public static String getUserQuestionboxReciteKnowledge(Long teachBookId,Long knowledgeId) {
		return User_QuestionBox_Recite_knowledge+teachBookId+":"+knowledgeId;
	}

	/**
	 * @param qType
	 * */
	public static String getUserQuestionboxReciteQtype(Integer qType) {
		return User_QuestionBox_Recite_QType+qType;
	}
	
	/**
	 * 字符串里最后一个参数必须为教材id(我也不想这样的[*.*!!!])
	 * */
	public static String getTeachbookQuestionboxRelation(Long boxId, Long teachBookId) {
		return TeachBook_QuestionBox_Relation_Key+boxId+"_"+teachBookId;
	}
	
	/**
	 * 供模糊查询keys使用
	 * */
	public static String getTeachbookQuestionboxRelation4Blur(Long boxId) {
		return TeachBook_QuestionBox_Relation_Key+boxId+"_*";
	}

	public static String getTeachbookQuestionboxChapterFiled(Long chapterId) {
		return TeachBook_QuestionBox_Chapter_Filed+chapterId;
	}

	public static String getTeachbookQuestionboxKnowledgeFiled(Long knowledgeId) {
		return TeachBook_QuestionBox_Knowledge_Filed+knowledgeId;
	}
	
	public static String getUserHistoryQuestionRedisId(Long uid) {
		return USER_HIS_QUESTIONS_INFO_REDIS_KEY + uid;
	}

	public static String getQuestionRedisId(Long id) {
		return QUESTION_REDIS_KEY + id;
	}

	public static String getQuestionsInfoRedisId(Long id) {
		return QUESTIONS_INFO_REDIS_KEY + id;
	}
	
	public static String getUserHomeWorkAnswerKey(Long uid, Long homeWorkId){
		return USER_HOMEWORK_ANSWER_REDIS_KEY+uid+":"+homeWorkId;
	}
	
	public static String getUserPaperAnswerKey(Long uid, Long paperAnswerId){
		return USER_PAPER_ANSWER_REDIS_KEY+uid+":"+paperAnswerId;
	}

	public static String getQuestionTotal(final Long questionId){
		return QUESTION_TOTAL + questionId;
	}

	public static String getQuestionRight(final Long questionId){
		return QUESTION_RIGHT + questionId;
	}

	public static String getQuestionWrong(final Long questionId){
		return QUESTION_WRONG + questionId;
	}

	public static String getQuestionStaticLock(){
		return QUESTION_STATIC_DETAIL_LOCK_KEY;
	}
	public static String getQuestionStaticTotalLock(){
		return QUESTION_STATIC_LOCK_KEY;
	}
	public static String getQuestionStaticKey(Long id) {
		return QUESTION_STATIC_REDIS_KEY + id;
	}

	public static String getPaperStaticKey(Long id) {
		return PAPER_STATIC_REDIS_KEY + id;
	}
	
	public static String getUserVideoLongKey() {
	    return USER_VIDEOLOG_LOCK_KEY;
	}

	public static String getTutorUserVideoLongKey() {
	    return TUTOR_USER_VIDEOLOG_LOCK_KEY;
	}

	public static String getDelUserVideoLongKey() {
        return DEL_USER_VIDEOLOG_LOCK_KEY;
    }
	
	public static String getUserCollectQuestionKey(Long uid) {
		return USER_COLLECT_QUESTION_REDIS_KEY + uid;
	}
	
	public static String getUserLastExerciseKey(Long boxId, Long uid) {
		return USER_LAST_EXERCISE_REDIS_KEY +boxId + ":"+ uid;
	}
	
	public static String getUserQuestionBoxSyncLockKey() {
		return USER_QUESTION_BOX_SYNC_LOCK_KEY;
	}

	public static String getUserRpErrQuseNumKey(Long uid) {
		return USER_RP_ERR_QUSE_NUM_KEY + uid;
	}

	public static String getUserRpErrQuseNumField(Long questionId) {
		return USER_RP_ERR_QUSE_NUM_FIELD + questionId;
	}

	public static String getUserErrorQuestionTimeHashKey(Long uid, Long categoryId) {
		return String.format(USER_ERROR_QUESTION_TIME_HASH, uid, categoryId);
	}

	public static String getUserStudyCenterErrorQuestionTimeHashKey(Long uid) {
		return String.format(USER_CENTER_SUB_ERROR_QUESTION_TIME_HASH, uid);
	}

	public static String getUserAutoRemoveErrorQuestionConfigKey(Long uid) {
		return String.format(USER_AUTO_REMOVE_ERROR_QUESTION_CONFIG, uid);
	}
	public static String getUserAutoRemoveErrorQuestionConfigNewKey(Long uid) {
		return String.format(USER_AUTO_REMOVE_ERROR_QUESTION_CONFIG_NEW, uid);
	}
	public static void main(String[] args) throws Exception{
		
		int max=1000;
	    int min=0;
		Set<Integer> chooseHis = new HashSet<Integer>();
		if ((1000 - 991) < 10 ) {//如果目标集数量X和源集合Y数量之间的差值小余10，则直接取源集合中的前X-10道题，剩下的再从10里面随机
			for (int i = 0; i < 888-10; i++) {
				chooseHis.add(i);
			}
			min = 888-10;
			int i=0;
			while (chooseHis.size() != 888) {
				int s = RandomUtils.nextInt(max)%(max-min+1) + min;
				chooseHis.add(s);
				i++;
				System.out.println("random num:"+i);
			}
		}else {
			while (chooseHis.size() != 888) {
				int s = RandomUtils.nextInt(max)%(max-min+1) + min;
				chooseHis.add(s);
			}
		}
		
	}
}
