/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorTask;
import cn.huanju.edu100.study.model.tutor.TutorTaskDto;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 个性化任务DAO接口
 * <AUTHOR>
 * @version 2016-01-14
 */
public interface TutorTaskDao extends CrudDao<TutorTask> {

    List<TutorTaskDto> getTutorTaskByCategroyIdList(List<Long> categoryIdList, TutorTaskDto tutorTaskDto)
            throws DataAccessException;

    List<TutorTaskDto> getTutorTasks(Map<String, Object> params) throws DataAccessException;

    Integer getTutorTaskCount(Map<String, Object> paramMap) throws DataAccessException;

}
