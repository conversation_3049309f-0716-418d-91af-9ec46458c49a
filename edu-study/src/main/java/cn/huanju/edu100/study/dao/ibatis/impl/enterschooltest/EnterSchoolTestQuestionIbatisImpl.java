/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.enterschooltest;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.enterschooltest.EnterSchoolTestQuestionDao;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.model.enterschooltest.EnterSchoolTestQuestion;
import cn.huanju.edu100.study.util.ValidateUtils;
import com.google.gson.Gson;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;

/**
 * 入学测评 题目 dao实现类
 *
 * <AUTHOR>
 */
public class EnterSchoolTestQuestionIbatisImpl extends CrudIbatisImpl2<EnterSchoolTestQuestion> implements EnterSchoolTestQuestionDao {

    public EnterSchoolTestQuestionIbatisImpl() {
        super("EnterSchoolTestQuestion");
    }

    @Override
    public long insertBatch(List<EnterSchoolTestQuestion> enterSchoolTestQuestions) throws DataAccessException {
        if (ValidateUtils.isEmpty(enterSchoolTestQuestions)) {
            logger.error("param error, parameter userEnterSchoolTestQuestions is empty,userEnterSchoolTestQuestions:{}", namespace,
                    enterSchoolTestQuestions);
            throw new DataAccessException("param error,userEnterSchoolTestQuestions is empty");
        }
        try {
            SqlMapClient sqlMap = super.getMaster();
            sqlMap.insert(namespace + ".insertBatch", enterSchoolTestQuestions);
            return 0l;
        } catch (SQLException e) {
            logger.error("insertBatch {} SQLException.content:{}", namespace,
                    (new Gson()).toJson(enterSchoolTestQuestions), e);
            throw new DataAccessException("add " + namespace + " SQLException fail.exception:" + e.getMessage());
        } catch (Exception e) {
            throw new DataAccessException(e);
        }
    }
}
