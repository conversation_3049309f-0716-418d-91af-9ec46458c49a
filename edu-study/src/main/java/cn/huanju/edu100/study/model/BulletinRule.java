package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 公告规则Entity
 * 
 * <AUTHOR>
 * @version 2016-05-23
 */
public class BulletinRule extends DataEntity<BulletinRule> {

    private static final long serialVersionUID = 1L;
    private Long terminalRuleId; // 终端规则id
    private Integer terminalRuleType; // 终端规则类型：0规则，1规则组
    private Long actionRuleId; // 行为规则id：0规则，1规则组
    private Integer actionRuleType; // 行为规则类型：0规则，1规则组
    private Long bulletinId; // 告公id

    public BulletinRule() {
        super();
    }

    public BulletinRule(Long id) {
        super(id);
    }

    public Long getTerminalRuleId() {
        return terminalRuleId;
    }

    public void setTerminalRuleId(Long terminalRuleId) {
        this.terminalRuleId = terminalRuleId;
    }

    public Integer getTerminalRuleType() {
        return terminalRuleType;
    }

    public void setTerminalRuleType(Integer terminalRuleType) {
        this.terminalRuleType = terminalRuleType;
    }

    public Long getActionRuleId() {
        return actionRuleId;
    }

    public void setActionRuleId(Long actionRuleId) {
        this.actionRuleId = actionRuleId;
    }

    public Integer getActionRuleType() {
        return actionRuleType;
    }

    public void setActionRuleType(Integer actionRuleType) {
        this.actionRuleType = actionRuleType;
    }

    public Long getBulletinId() {
        return bulletinId;
    }

    public void setBulletinId(Long bulletinId) {
        this.bulletinId = bulletinId;
    }

    @Override
    public String toString() {
        return "BulletinRule [terminalRuleId=" + terminalRuleId + ", terminalRuleType=" + terminalRuleType
                + ", actionRuleId=" + actionRuleId + ", actionRuleType=" + actionRuleType + ", bulletinId="
                + bulletinId + "]";
    }

}