package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.UserCorrectQuestion;
import com.hqwx.study.dto.UserErrorAndCorrectQuestionCountDTO;
import com.hqwx.study.dto.query.UserErrorAndCorrectQuestionQuery;

import java.util.List;
import java.util.Map;

public interface UserCorrectQuestionService extends BaseService<UserCorrectQuestion> {

    void saveUserCorrectQuestion(UserCorrectQuestion userCorrectQuestion) throws DataAccessException;

    Integer getUserCorrectQuestionCount(UserErrorAndCorrectQuestionQuery query)throws DataAccessException;

    Boolean removeCorrectQuestionByCategory(Map<String, Object> params)throws DataAccessException;

    Page<UserCorrectQuestion> findGroypByPage(UserErrorAndCorrectQuestionQuery query)throws DataAccessException;

    List<Long> getUserCorrectQuestionIdList(UserErrorAndCorrectQuestionQuery query)throws DataAccessException;

    UserErrorAndCorrectQuestionCountDTO countForCategory(UserErrorAndCorrectQuestionQuery param) throws DataAccessException;

//    UserCorrectQuestion getByCondition(UserCorrectQuestion userCorrectQuestion) throws DataAccessException;

}
