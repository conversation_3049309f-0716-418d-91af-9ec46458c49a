package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.questionBox.UserBoxHomework;
import cn.huanju.edu100.study.model.questionBox.UserBoxHomeworkDto;
import cn.huanju.edu100.study.model.questionBox.VirtualHomework;
import cn.huanju.edu100.study.model.questionBox.VirtualHomeworkDetail;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * @desc 练习作业service
 * <AUTHOR>
 *
 * */
public interface UserVirtualHomeworkService extends BaseService<VirtualHomework>{

	Integer insertDetailBatch(List<VirtualHomeworkDetail> details,Long uid) throws DataAccessException;

	Integer findUserBoxExerciseCount(Map<String, Double> param) throws DataAccessException;

	List<UserBoxHomework> findUserBoxExerciseList(Map<String, Double> param,
			int from, int rows) throws DataAccessException;

	List<UserBoxHomework> findUserBoxExerciseIsDo(Map<String, Object> param,
			List<Long> user_homework_ids)throws DataAccessException;

	VirtualHomework get(long id, long uid) throws DataAccessException;

	List<VirtualHomeworkDetail> getHomeWorkDetails(Long homeworkId, Long uid, Integer elementType) throws DataAccessException;
	List<UserBoxHomeworkDto> findUserBoxExerciseListToday(Long uid,List<Long> bookIds ) throws DataAccessException;
	UserBoxHomeworkDto findLastUserBoxExercise(Long uid,List<Long> bookIds )
			throws DataAccessException;

}
