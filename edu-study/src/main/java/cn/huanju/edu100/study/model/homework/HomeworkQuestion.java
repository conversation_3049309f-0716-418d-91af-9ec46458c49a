package cn.huanju.edu100.study.model.homework;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 作业题目关系表 20220418
 */
@Data
public class HomeworkQuestion implements Serializable {
	/**
	 * ID
	 */
	private Long id;

	/**
	 * 作业ID
	 */
	private Long homeworkId;

	/**
	 * 题目ID
	 */
	private Long questionId;

	/**
	 * 题目排序，最小值1
	 */
	private Integer sort;

	/**
	 * 分值
	 */
	private Double score;

	/**
	 * 是否自定义题目，0-否 1-是
	 */
	private Integer isDiy;


	/**
	 * 删除标识 0-未删除 1-已删除
	 */
	private Integer delFlag;

	/**
	 * 创建用户ID
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	private Date createDate;

	/**
	 * 修改用户ID
	 */
	private Long updateBy;

	/**
	 * 修改时间
	 */
	private Date updateDate;

}