package cn.huanju.edu100.study.mapper.homework.comment;

import cn.huanju.edu100.study.model.tutor.CommentReply;
import cn.huanju.edu100.study.model.tutor.ReplyComment;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hqwx.study.dto.query.CommentReplyQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@Mapper
public interface CommentReplyMapper extends BaseMapper<CommentReply> {

    List<CommentReply> getCommentReplyPage(@Param(Constants.WRAPPER) CommentReplyQuery commentReplyQuery);

    Integer selectReplyCount(@Param(Constants.WRAPPER) CommentReplyQuery commentReplyQuery);

    /**
     * 查询评论回复列表
     * @param commentReplyQuery 查询参数
     * @return 评论回复列表
     */
    @Select("select " +
            " a.id AS id, " +
            " a.belong_comment_id AS belongCommentId, " +
            " a.reply_comment_id AS replyCommentId, " +
            " a.comment_id AS commentId, " +
            " b.create_date AS createDate," +
            " b.update_date AS updateDate," +
            " b.uid AS uid," +
            " b.nick_name AS nickName," +
            " b.comment_element_id AS commentElementId," +
            " b.content AS content," +
            " b.star AS star," +
            " b.teacher_star AS teacherStar," +
            " b.thumb_up_num AS thumbUpNum," +
            " b.is_stick AS isStick," +
            " b.create_date AS createDate," +
            " b.create_by AS createBy," +
            " b.del_flag AS delFlag," +
            " b.reply_content AS replyContent," +
            " b.is_read AS isRead," +
            " b.platform AS platform," +
            " b.level AS level," +
            " b.source AS source, " +
            " b.status AS status" +
            " from comment_reply a left join comment b on a.comment_id = b.id WHERE ${ew.sqlSegment}")
    List<ReplyComment> getCommentReplyList(@Param(Constants.WRAPPER) Wrapper<CommentReplyQuery> commentReplyQuery);
}
