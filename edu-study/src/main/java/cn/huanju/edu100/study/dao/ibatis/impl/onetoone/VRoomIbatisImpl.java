/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.onetoone.VRoomDao;
import cn.huanju.edu100.study.model.onetoone.VRoom;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;


/**
 * 面授课室DAO接口
 * <AUTHOR>
 * @version 2016-04-12
 */
public class VRoomIbatisImpl extends CrudIbatisImpl2<VRoom> implements
		VRoomDao {

	public VRoomIbatisImpl() {
		super("VRoom");
	}

	@Override
    public List<VRoom> findListByIds(List<Long> ids) throws DataAccessException {

        if (CollectionUtils.isEmpty(ids)) {
            logger.error("findListByIds {} error, parameter ids is empty, entity:{}", namespace, ids);
            throw new DataAccessException(String.format("findListByIds %s error, parameter ids is empty, entity: %s", namespace, ids));
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = Maps.newHashMap();
            param.put("ids", ids);
            return sqlMap.queryForList(namespace.concat(".findListByIds"), param);
        } catch (SQLException e) {
            logger.error("findListByIds {} SQLException. ids:{}", namespace, ids, e);
            throw new DataAccessException(String.format("findListByIds SQLException error :%s", e.getMessage()));
        }
    }

}
