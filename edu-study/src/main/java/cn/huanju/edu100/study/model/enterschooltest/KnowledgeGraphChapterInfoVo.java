package cn.huanju.edu100.study.model.enterschooltest;

import java.io.Serializable;

/**
 * 为题库5.0  入学测评报告 知识点图谱 报表模块 提供的vo
 */
public class KnowledgeGraphChapterInfoVo implements Serializable {

    private Long knowledgeGraphId;//知识点图谱id
    private String knowledgeGraphName;//知识点图谱名称

    private Long chapterId;//知识点图谱 对应的 其中一个章id
    private Long childChapterId;//知识点图谱 对应的 其中一个节id

    private String chapterName;//上方 对应的 章的名称
    private String childChapterName;//上方 对应的 节的名称

    public Long getKnowledgeGraphId() {
        return knowledgeGraphId;
    }

    public void setKnowledgeGraphId(Long knowledgeGraphId) {
        this.knowledgeGraphId = knowledgeGraphId;
    }

    public String getKnowledgeGraphName() {
        return knowledgeGraphName;
    }

    public void setKnowledgeGraphName(String knowledgeGraphName) {
        this.knowledgeGraphName = knowledgeGraphName;
    }

    public Long getChapterId() {
        return chapterId;
    }

    public void setChapterId(Long chapterId) {
        this.chapterId = chapterId;
    }

    public Long getChildChapterId() {
        return childChapterId;
    }

    public void setChildChapterId(Long childChapterId) {
        this.childChapterId = childChapterId;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public String getChildChapterName() {
        return childChapterName;
    }

    public void setChildChapterName(String childChapterName) {
        this.childChapterName = childChapterName;
    }
}
