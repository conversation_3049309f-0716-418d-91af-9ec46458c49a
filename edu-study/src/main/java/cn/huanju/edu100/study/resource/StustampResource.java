/*
 * Copyright (c) 2011 duowan.com.
 * All Rights Reserved.
 * This program is the confidential and proprietary information of
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.exception.ApiErrorEnum;
import cn.huanju.edu100.study.model.CTaskResult;
import cn.huanju.edu100.study.model.UserStudyResult;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.stustamp.dto.*;
import cn.huanju.edu100.stustamp.dto.al.AlUserChapterConsolidateAnswerRelationDTO;
import cn.huanju.edu100.stustamp.dto.query.AlUserAssessmentAnswerDetailQuery;
import cn.huanju.edu100.stustamp.dto.query.AlUserAssessmentAnswerQuery;
import cn.huanju.edu100.stustamp.dto.query.UserViewProductUpdateQuery;
import cn.huanju.edu100.stustamp.util.DateUtil;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.GsonUtils;
import com.hqwx.aibase.model.ResObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class StustampResource {

    public static final int APP_ID = 7;

    @Autowired
    private IGenericThriftResource genericThriftResource;

    private static final Logger LOG = LoggerFactory.getLogger(StustampResource.class);

    /**
     * 上报数据到上报服务
     *
     * @param cTaskResult
     */
    @CacheEvict(value = "taskCache", key = "'StustampResource.taskResult' + #cTaskResult.uid + ',' + #cTaskResult.type + ',' + #cTaskResult.taskId")
    public void saveCTaskResult(CTaskResult cTaskResult) {
        if (cTaskResult == null) {
            return;
        }

        try {
            LOG.info("[stu_saveTaskResult] start, param is:{}", GsonUtil.toJson(cTaskResult));
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(cTaskResult), APP_ID,
                    Consts.Code.CLIENT_IP, "stu_saveTaskResult");
            LOG.info("[stu_saveTaskResult] response:{}", response.getMsg());
        } catch (Exception e) {
            LOG.error("[stu_saveTaskResult] error param:{}", GsonUtil.toJson(cTaskResult), e);
        }
    }

    @Cacheable(value = "taskCache", key = "'StustampResource.taskResult' + #uid + ',' + #type + ',' + #taskId")
    public CTaskResult queryByTaskId(final Long uid, final Integer type, final Long taskId) {
        if (null == uid || null == type || null == taskId) {
            return null;
        }
        CTaskResult param = new CTaskResult();
        param.setUid(uid);
        param.setType(type);
        param.setTaskId(taskId);

        try {
            LOG.info("[queryByTaskId] start, param is:{}", GsonUtil.toJson(param));
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(param), APP_ID,
                    Consts.Code.CLIENT_IP, "stu_queryByTaskId");
            LOG.info("[queryByTaskId] response:{}", response.getMsg());
            return GsonUtil.getGenericGson().fromJson(response.getMsg(), CTaskResult.class);
        } catch (Exception e) {
            LOG.error("[queryByTaskId] error param:{}", GsonUtil.toJson(param), e);
        }
        return null;
    }

    public void insertUserStudyResult(UserStudyResult userStudyResult) {
        if (null == userStudyResult) {
            return;
        }
        try {
            LOG.info("[insertUserStudyResult] start, param is:{}", GsonUtil.toJson(userStudyResult));
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(userStudyResult), APP_ID,
                    Consts.Code.CLIENT_IP, "stu_insertReportForUserAnswer");
            LOG.info("[insertUserStudyResult] response:{}", response.getMsg());
        } catch (Exception e) {
            LOG.error("[insertUserStudyResult] error param:{}", GsonUtil.toJson(userStudyResult), e);
        }
    }

    public List<UserStudyResult> findUserStudyResultLessonListByLessonIds(Long uid, List<Long> lessonIdList) {
        if (null == uid || CollectionUtils.isEmpty(lessonIdList)) {
            return null;
        }
        Map<String, Object> reqMaps = new HashMap<>();
        reqMaps.put("uid", uid);
        reqMaps.put("lessonIdList", lessonIdList);
        reqMaps.put("type", Consts.StudyResultType.HOMEWORK);
        try {
            LOG.info("[findUserStudyResultListByLessonIds] start, param uid is:{} and lessonIdList is :{}", uid, lessonIdList);
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(reqMaps), APP_ID,
                    Consts.Code.CLIENT_IP, "stu_getUserStudyResultListByLessonIds");
            LOG.info("[findUserStudyResultListByLessonIds] response:{}", response.getMsg());
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserStudyResult>>() {
            }.getType();
            return GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
        } catch (Exception e) {
            LOG.error("[findUserStudyResultListByLessonIds] error param:{}", GsonUtil.toJson(reqMaps), e);
        }
        return null;
    }

    public List<AlUserStudyPathRealDTO> getStudyPathRealByTbIndex(Integer tbIndex, Long pathId, Long productId, Long resourceId, Integer pathIdIsNull) {
        if (null == tbIndex) {
            LOG.error("tbIndex is null");
            return null;
        }
        Map<String, Object> reqMaps = new HashMap<>();
        reqMaps.put("tbIndex", tbIndex);
        reqMaps.put("pathId", pathId);
        reqMaps.put("productId", productId);
        reqMaps.put("resourceId", resourceId);
        reqMaps.put("pathIdIsNull", pathIdIsNull);
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(reqMaps), APP_ID, Consts.Code.CLIENT_IP, "stu_al_getStudyPathRealByTbIndex");
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<AlUserStudyPathRealDTO>>() {
            }.getType();
            return GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
        } catch (Exception e) {
            LOG.error("[getStudyPathRealByTbIndex] error param:{}", GsonUtil.toJson(reqMaps), e);
        }
        return null;
    }

    public AlUserStudyPathRealDTO getUserStudyPathRealMaxIdByTbIndex(Integer tbIndex) {
        if (null == tbIndex) {
            LOG.error("tbIndex is null");
            return null;
        }
        Map<String, Object> reqMaps = new HashMap<>();
        reqMaps.put("tbIndex", tbIndex);
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(reqMaps), APP_ID, Consts.Code.CLIENT_IP, "stu_al_getUserStudyPathRealMaxIdByTbIndex");
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<AlUserStudyPathRealDTO>() {
            }.getType();
            return GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
        } catch (Exception e) {
            LOG.error("[getUserStudyPathRealMaxIdByTbIndex] error param:{}", GsonUtil.toJson(reqMaps), e);
        }
        return null;
    }

    public Integer selectUserStudyPathRealCountByMaxId(Integer tbIndex, Long id, Long pathId, Long productId, Long resourceId, Integer pathIdIsNull) {
        if (null == tbIndex) {
            LOG.error("tbIndex is null");
            return null;
        }
        Map<String, Object> reqMaps = new HashMap<>();
        reqMaps.put("tbIndex", tbIndex);
        reqMaps.put("id", id);
        reqMaps.put("pathId", pathId);
        reqMaps.put("productId", productId);
        reqMaps.put("resourceId", resourceId);
        reqMaps.put("pathIdIsNull", pathIdIsNull);
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(reqMaps), APP_ID, Consts.Code.CLIENT_IP, "stu_al_selectUserStudyPathRealCountByMaxId");
            if (StringUtils.isNotBlank(response.getMsg())) {
                return Integer.parseInt(response.getMsg());
            }
            return null;
        } catch (Exception e) {
            LOG.error("[selectUserStudyPathRealCountByMaxId] error param:{}", GsonUtil.toJson(reqMaps), e);
        }
        return null;
    }

    public List<AlUserStudyPathRealDTO> getUserStudyPathRealListByMaxId(Integer tbIndex, Long id, int from, int rows, Long pathId, Long productId, Long resourceId, Integer pathIdIsNull) {
        if (null == tbIndex) {
            LOG.error("tbIndex is null");
            return null;
        }
        Map<String, Object> reqMaps = new HashMap<>();
        reqMaps.put("tbIndex", tbIndex);
        reqMaps.put("id", id);
        reqMaps.put("from", from);
        reqMaps.put("rows", rows);
        reqMaps.put("pathId", pathId);
        reqMaps.put("productId", productId);
        reqMaps.put("resourceId", resourceId);
        reqMaps.put("pathIdIsNull", pathIdIsNull);
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(reqMaps), APP_ID, Consts.Code.CLIENT_IP, "stu_al_getUserStudyPathRealListByMaxId");
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<AlUserStudyPathRealDTO>>() {
            }.getType();
            return GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
        } catch (Exception e) {
            LOG.error("[getUserStudyPathRealListByMaxId] error param:{}", GsonUtil.toJson(reqMaps), e);
        }
        return null;
    }

    public PageDTO<AlUserAssessmentAnswerDTO> getAlUserAssessmentAnswerPage(AlUserAssessmentAnswerQuery query) {
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(query), APP_ID, Consts.Code.CLIENT_IP, "stu_getAlUserAssessmentAnswerPage");
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<PageDTO<AlUserAssessmentAnswerDTO>>() {
            }.getType();
            return GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
        } catch (Exception e) {
            LOG.error("[getAlUserAssessmentAnswerPage] error param:{}", GsonUtil.toJson(query), e);
        }
        return null;
    }

    public PageDTO<AlUserAssessmentAnswerDetailDTO> getAlUserAssessmentAnswerDetailPage(AlUserAssessmentAnswerDetailQuery query) {
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke(GsonUtil.getGenericGson().toJson(query), APP_ID, Consts.Code.CLIENT_IP, "stu_getAlUserAssessmentAnswerDetailPage");
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<PageDTO<AlUserAssessmentAnswerDetailDTO>>() {
            }.getType();
            return GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
        } catch (Exception e) {
            LOG.error("[getAlUserAssessmentAnswerDetailPage] error param:{}", GsonUtil.toJson(query), e);
        }
        return null;
    }

    public List<UserViewProductUpdateDTO> getUserViewProductUpdateByUid(Long uid, Long goodsId, Integer type, Long categoryId) throws Exception {
        String method = "getUserViewProductUpdateByUid";
        if (uid == null) {
            LOG.error("{} param uid is null.");
            return null;
        }
        if (categoryId != null && categoryId.longValue() == 0) {
            categoryId = null;
        }
        List<UserViewProductUpdateDTO> result = null;
        UserViewProductUpdateQuery userViewProductUpdateQuery = new UserViewProductUpdateQuery();
        userViewProductUpdateQuery.setUid(uid);
        userViewProductUpdateQuery.setGoodsId(goodsId);
        userViewProductUpdateQuery.setType(type);
        userViewProductUpdateQuery.setCategoryId(categoryId);
        String paramString = GsonUtil.getGenericGson().toJson(userViewProductUpdateQuery);
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke(paramString, APP_ID, Consts.Code.CLIENT_IP, "stu_getUserViewProductUpdateByUid");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type typeToken = new com.google.gson.reflect.TypeToken<List<UserViewProductUpdateDTO>>() {
                }.getType();
                result = GsonUtil.getGson().fromJson(response.getMsg(), typeToken);
            } else {
                LOG.error("{} param:{} errcode:{} errMsg:{}", method, paramString, response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error("{} failed: params:{}, e:{} ", method, paramString, e);
        }
        return result;
    }
    public ResObject<String> updateStudyPath(Long uid, Long categoryId, Long productId, Long pathId, String date,
                                             Long resourceId, Integer resourceType, Long userAnswerId, Integer pathSource, String planDate, Long paragraphId, Date resourceStartTime, Long goodsId, Integer completionRate) {
        if (uid == null || categoryId == null || resourceId == null || resourceType == null) {
            LOG.debug(
                    "updateStudyPath parameter uid, categoryId, resourceId, resourceType, date, userAnswerId is invalid");
            return ResObject.error(ApiErrorEnum.PARAM_INVALID.getCode(), ApiErrorEnum.PARAM_INVALID.getMsg());
        }

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("uid", uid);
        params.put("goods_id", goodsId);
        params.put("category_id", categoryId);
        params.put("product_id", productId);
        params.put("path_id", pathId);
        params.put("resource_id", resourceId);
        params.put("resource_type", resourceType);
        params.put("date", date);
        params.put("user_answer_id", userAnswerId);
        params.put("path_source", pathSource);
        params.put("plan_date", planDate);
        params.put("paragraph_id", paragraphId);
        params.put("completion_rate", completionRate);
        params.put("resource_start_time", Objects.nonNull(resourceStartTime)? DateUtil.dateToString(resourceStartTime,DateUtil.FORMAT_YMDHMS):null);

        String paramString = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke
                    (paramString, APP_ID, Consts.Code.CLIENT_IP, "stu_al_updateStudyPath");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                return ResObject.success(response.getMsg());
            } else {
                LOG.debug("stu_al_updateStudyPath param:{} errcode:{} errMsg:{}",
                        GsonUtils.toDefaultJson(params),
                        response.getCode(), response.getErrormsg());
                return ResObject.error(response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error("stu_al_updateStudyPath failed: uid = " + uid, e);
            return ResObject.error(999, e.getMessage());
        }
    }

    public ResObject<String> updateStudyPathForBrush(Long uid, Long categoryId, String date,
                                             Long resourceId, Integer resourceType, Long userAnswerId, Long goodsId, Integer completionRate) {
        if (uid == null || categoryId == null || resourceId == null || resourceType == null ) {
            LOG.debug(
                    "stu_al_updateStudyPathForBrush parameter uid, categoryId, resourceId, resourceType is invalid");
            return ResObject.error(ApiErrorEnum.PARAM_INVALID.getCode(), ApiErrorEnum.PARAM_INVALID.getMsg());
        }

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("uid", uid);
        params.put("goods_id", goodsId);
        params.put("category_id", categoryId);
        params.put("resource_id", resourceId);
        params.put("resource_type", resourceType);
        params.put("date", date);
        params.put("user_answer_id", userAnswerId);
        params.put("path_source", 36);
        params.put("completion_rate", completionRate);

        String paramString = GsonUtil.getGson().toJson(params);
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke
                    (paramString, APP_ID, Consts.Code.CLIENT_IP, "stu_al_updateStudyPathForBrush");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                return ResObject.success(response.getMsg());
            } else {
                LOG.debug("stu_al_updateStudyPathForBrush param:{} errcode:{} errMsg:{}",
                        GsonUtils.toDefaultJson(params),
                        response.getCode(), response.getErrormsg());
                return ResObject.error(response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error("stu_al_updateStudyPathForBrush failed: uid = " + uid, e);
            return ResObject.error(999, e.getMessage());
        }
    }

    public List<AlUserChapterConsolidateAnswerRelationDTO> getAlUserChapterConsolidateAnswerRelationList(List<Long> originalPaperAnswerIdList) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("originalPaperAnswerIdList", originalPaperAnswerIdList);
        String paramString = GsonUtils.toJson(params);
        try {
            response response = genericThriftResource.generalStustampThriftMethodInvoke(paramString, APP_ID, Consts.Code.CLIENT_IP, "stu_getAlUserChapterConsolidateAnswerRelationList");
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                Type type = new com.google.gson.reflect.TypeToken<List<AlUserChapterConsolidateAnswerRelationDTO>>() {
                }.getType();
                return GsonUtil.getGson().fromJson(response.getMsg(), type);
            } else {
                LOG.warn("getAlUserChapterConsolidateAnswerRelationList param:{} errcode:{} errMsg:{}", GsonUtils.toDefaultJson(params), response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            LOG.error("getAlUserChapterConsolidateAnswerRelationList failed: param:{}" + GsonUtils.toDefaultJson(params), e);
        }
        return null;
    }
}
