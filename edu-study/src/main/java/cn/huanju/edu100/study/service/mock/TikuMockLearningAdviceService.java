package cn.huanju.edu100.study.service.mock;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.mock.TikuMockLearningAdvice;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;


public interface TikuMockLearningAdviceService extends BaseService<TikuMockLearningAdvice> {

    /**
    *
    * <AUTHOR> duxiulei
    * @Description :qryByMockExamIdAndMockSubjectId
    * @Date : 2020/8/20
    *
    */
    List<TikuMockLearningAdvice> qryByMockExamIdAndMockSubjectId(Long mockExamId, Long mockSubjectId) throws DataAccessException;
}