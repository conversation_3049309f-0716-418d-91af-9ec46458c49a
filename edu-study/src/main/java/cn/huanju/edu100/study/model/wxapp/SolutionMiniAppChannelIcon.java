package cn.huanju.edu100.study.model.wxapp;


import cn.huanju.edu100.persistence.model.DataEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 小程序频道图标
 *   `id` int(11) NOT NULL AUTO_INCREMENT,
 *   `org_id` int(11) DEFAULT '2' COMMENT '机构ID',
 *   `appid` varchar(255) DEFAULT NULL COMMENT '所属wxapp',
 *   `position` int(11) DEFAULT NULL COMMENT '位置',
 *   `second_category_ids` varchar(2550) DEFAULT '0' COMMENT '所属考试 0全部 多个逗号隔开',
 *   `second_category_name` varchar(2550) DEFAULT NULL COMMENT '考试名称',
 *   `images` varchar(255) DEFAULT NULL COMMENT '图片',
 *   `title` varchar(255) DEFAULT NULL COMMENT '标题',
 *   `url_type` int(11) NOT NULL DEFAULT '0' COMMENT '跳转类型 1客服消息 2小程序路径 3外部链接',
 *   `path` varchar(255) DEFAULT NULL COMMENT 'wxapp路径',
 *   `ext_link` varchar(255) DEFAULT NULL COMMENT '外部链接',
 *   `target_appid` varchar(255) DEFAULT NULL COMMENT '跳转目标appid',
 *   `start_time` int(11) DEFAULT '0' COMMENT '开始时间',
 *   `end_time` int(11) DEFAULT '0' COMMENT '结束时间',
 *   `sort` int(11) DEFAULT '0' COMMENT '排序',
 *   `status` int(11) NOT NULL DEFAULT '1' COMMENT '1未发布 2已发布',
 *   `create_by` varchar(255) DEFAULT NULL,
 *   `update_by` varchar(255) DEFAULT NULL,
 *   `create_time` int(11) DEFAULT NULL,
 *   `update_time` int(11) DEFAULT NULL,
 */
@TableName("solution_mini_app_channel_icon")
public class SolutionMiniAppChannelIcon extends DataEntity<SolutionMiniAppChannelIcon> {

    private Long orgId; // 机构ID

    private String appid; // 所属wxapp

    private Integer position; // 位置

    private String secondCategoryIds; // 所属考试 0全部 多个逗号隔开

    private String secondCategoryName; // 考试名称

    private String images; // 图片

    private String title; // 标题

    private Integer urlType; // 跳转类型 1客服消息 2小程序路径 3外部链接

    private String path; // wxapp路径

    private String extLink; // 外部链接

    private String targetAppid; // 跳转目标appid

    private Integer startTime; // 开始时间

    private Integer endTime; // 结束时间

    private Integer sort; // 排序

    private Integer status; // 1未发布 2已发布

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public String getSecondCategoryIds() {
        return secondCategoryIds;
    }

    public void setSecondCategoryIds(String secondCategoryIds) {
        this.secondCategoryIds = secondCategoryIds;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getUrlType() {
        return urlType;
    }

    public void setUrlType(Integer urlType) {
        this.urlType = urlType;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getExtLink() {
        return extLink;
    }

    public void setExtLink(String extLink) {
        this.extLink = extLink;
    }

    public String getTargetAppid() {
        return targetAppid;
    }

    public void setTargetAppid(String targetAppid) {
        this.targetAppid = targetAppid;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
