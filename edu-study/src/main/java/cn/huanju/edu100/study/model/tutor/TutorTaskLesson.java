package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 录播课详情Entity
 * <AUTHOR>
 * @version 2016-01-18
 */
public class TutorTaskLesson extends DataEntity<TutorTaskLesson> {
	
	private static final long serialVersionUID = 1L;
	private Long courseId;		// course_id
	private Long classesId;		// classes_id
	private Long lessonId;		// lesson_id
	private Integer isSend;		// 0:不推送，1：推送
	private String bak1;		// bak1
	private String bak2;		// bak2
	
	public TutorTaskLesson() {
		super();
	}

	public TutorTaskLesson(Long id){
		super(id);
	}

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}
	
	public Long getClassesId() {
		return classesId;
	}

	public void setClassesId(Long classesId) {
		this.classesId = classesId;
	}
	
	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}
	
	public Integer getIsSend() {
		return isSend;
	}

	public void setIsSend(Integer isSend) {
		this.isSend = isSend;
	}
	
	public String getBak1() {
		return bak1;
	}

	public void setBak1(String bak1) {
		this.bak1 = bak1;
	}
	
	public String getBak2() {
		return bak2;
	}

	public void setBak2(String bak2) {
		this.bak2 = bak2;
	}
	
}