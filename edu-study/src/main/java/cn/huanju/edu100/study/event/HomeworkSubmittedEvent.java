package cn.huanju.edu100.study.event;

import com.hqwx.study.entity.UserHomeWorkAnswer;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/2 16:43
 * @description 试卷提交完成的事件
 */
@Getter
public class HomeworkSubmittedEvent extends ApplicationEvent {
    UserHomeWorkAnswer userAnswer;

    public HomeworkSubmittedEvent(Object source, UserHomeWorkAnswer userAnswer) {
        super(source);
        this.userAnswer = userAnswer;
    }

}
