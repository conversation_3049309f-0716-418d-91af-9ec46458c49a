package cn.huanju.edu100.study.model.enterschooltest;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 *
 *
 * <AUTHOR>
 * @date 2019-07-05 11:53:01
 */
public class EnterSchoolTestQuestionKnowledgeGraphRelation extends DataEntity<EnterSchoolTestQuestionKnowledgeGraphRelation> {
    private static final long serialVersionUID = 1L;
    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 试卷id
     */
    private Long paperId;
    /**
     * 知识图谱id
     */
    private Long knowledgeGraphId;

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public Long getKnowledgeGraphId() {
        return knowledgeGraphId;
    }

    public void setKnowledgeGraphId(Long knowledgeGraphId) {
        this.knowledgeGraphId = knowledgeGraphId;
    }

    public Long getQuestionId() {
        return this.questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }


}
