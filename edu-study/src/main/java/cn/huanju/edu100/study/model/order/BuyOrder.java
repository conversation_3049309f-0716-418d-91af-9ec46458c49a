package cn.huanju.edu100.study.model.order;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class BuyOrder  {

    private static final long serialVersionUID = 1L;

    private Long mOrderId; //父订单id

    private String mBuyOrderCode; //父订单编号

    private Integer mBuyOrderState; //父订单状态

    private Long uid; // 0

    private Long serviceType;// 订单服务类型,是多个服务类型的2的幂等之和。比如服务类型是1 和2，则为2^1+2^2=6

    private String serviceTypes;

    private String buyOrderCode; // 订单编号 给用户看

    private String name;// 订单名称

    private String username;// 用户名

    private String mobile;//

    private Integer sharing;// 是否分成

    private BigDecimal oriMoney; // 原始金额

    private BigDecimal discMoney; // 优惠金额

    private BigDecimal money; // 成交金额

    private BigDecimal payed;

    private BigDecimal reverseMoney; // 撤销金额撤销订单时，多少钱返回

    private BigDecimal studyCardLimit;

    private Integer needExpress; // 是否需要快递

    private BigDecimal freight; // 运费

    private String source; // 订单来源, 已改成存放用户代理商web_id

    private Long webSchId; // 订单来源网站的机构Id,环球网站买留学商品，订单机构为留学,webSchId为环球

    private String tag;// 订单标记

    private String platform;// 订单来源平台

    private String appId; // 应用的id,对手机端就是应用appId,保存提交订单时的appId

    private String termVersion; // 版本信息，app的话就是app版本

    private String orderType; // 订单类型：升级订单、天猫订单、团购订单等

    private Integer state;

    private String stateMsg; // 状态描述

    private Integer lastState; // last_state

    private String userIp; // user_ip

    private String mockBy;// 转正人

    private Date manualDate;// 190转正时间

    private Date mockDate;// 转正时间

    private Date formalDate;// 正式转正时间

    private Date unformalDate;// 转正撤销时间

    private String typeSrc; // ts_100yy 为留学订单

    private String params; // 附加参数

    private String payMethod; // 支付方式
    // private Date createTime; // 创建时间
    // private Date updateTime; // 最后更新时间

    private String recommendCode;// 推荐码

    private BigDecimal recommendMoney;// 推荐码价值

    private String keyParam; // 各个商品关键字字符串MD5

    private List<BuyOrderDetail> buyOrderDetailList;
}