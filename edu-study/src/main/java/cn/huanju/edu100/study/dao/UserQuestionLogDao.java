/**
 *
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.UserQuestionLog;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 记录用户问题解析反馈
 */
public interface UserQuestionLogDao
        extends CrudDao<UserQuestionLog> {

	/**
	 * 这几个参数唯一确定一条记录
	 * @param uid
	 * @param questionId
	 * @return
	 */
	public UserQuestionLog query(final Long uid, final Long questionId) throws DataAccessException;

	/**
	 * @param userQuestionLog
	 * @return
	 */
	long insertUserQuestionLog(UserQuestionLog userQuestionLog)
			throws DataAccessException;

	/**
	 * @param userQuestionLog
	 * @return
	 */
	int updateUserQuestionLog(UserQuestionLog userQuestionLog)
			throws DataAccessException;

	/**
	 * @param uid
	 * @param questionId
	 * @return
	 */
	Collection<UserQuestionLog> queryByUidQuestionId(Long uid, Long questionId)
			throws DataAccessException;

	/**
	 * @param uid
	 * @param questionIds
	 * @return
	 */
	List<UserQuestionLog>  getLastUserQuestionLog(long uid, List<Long> questionIds)
			throws DataAccessException;
	List<UserQuestionLog>  split_getLastUserQuestionLog(long uid, List<Long> questionIds)
			throws DataAccessException;

	/**
	 * @param param
	 * @return
	 * @throws DataAccessException
	 */
	UserQuestionLog findOne(Map<String, Object> param) throws DataAccessException;
	Collection<UserQuestionLog> queryUserQuestionLog(Map<String, Object> param)
			throws DataAccessException;
	void insertBatch(List<UserQuestionLog> userQuestionLogs) throws DataAccessException;

}
