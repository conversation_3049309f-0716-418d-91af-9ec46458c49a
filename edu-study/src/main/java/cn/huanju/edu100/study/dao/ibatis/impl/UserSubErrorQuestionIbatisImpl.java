/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.study.dao.UserSubErrorQuestionDao;
import cn.huanju.edu100.study.model.UserErrorEpaperQuestion;
import cn.huanju.edu100.study.model.UserErrorHomeworkLessonQuestionBean;
import cn.huanju.edu100.study.model.UserQuestionCountVo;
import cn.huanju.edu100.study.model.UserSubErrorQuestion;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.util.GsonUtils;
import cn.huanju.edu100.util.IdWorker;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.hqwx.study.dto.query.UserErrorAndCorrectQuestionQuery;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.*;

/**
 * 用户分表错题记录DAO接口
 * <AUTHOR>
 * @version 2021-09-08
 */
public class UserSubErrorQuestionIbatisImpl extends CrudIbatisImpl2<UserSubErrorQuestion> implements UserSubErrorQuestionDao {

	final static IdWorker idworker=new IdWorker(0,5);

	public UserSubErrorQuestionIbatisImpl() {
		super("UserSubErrorQuestion");
	}



	@SuppressWarnings("unchecked")
	@Override
	public long insertSharding(UserSubErrorQuestion entity) throws DataAccessException {
		if (entity == null) {
			logger.error("add {} error, parameter is null", namespace);
			throw new DataAccessException("add {} error,param is null", namespace);
		}
		SqlMapClient sqlMap = super.getShardingMaster();
		try {
			Number id = idworker.nextId();
			entity.setId(id.longValue());
			sqlMap.insert(namespace.concat(".insertSharding"), entity);
			return entity.getId();
		} catch (SQLException e) {
			logger.error("insert {} SQLException.content:{}", namespace, (new Gson()).toJson(entity), e);
			throw new DataAccessException("add " + namespace + " SQLException fail.");
		}
	}

	@Override
	public List<UserErrorHomeworkLessonQuestionBean> findUserErrorHomeWorkLessonIds(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null || queryParam.get("productIds")==null) {
			logger.error("findUserErrorHomeWorkLessonIds {} error, parameter uid is null,param:{}",
					namespace, GsonUtils.toJson(queryParam));
			throw new DataAccessException("findUserErrorHomeWorkLessonIds error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<UserErrorHomeworkLessonQuestionBean>) sqlMap.queryForList(namespace.concat(".findUserErrorHomeWorkLessonIds"),queryParam);
		} catch (SQLException e) {
			logger.error("findUserErrorHomeWorkLessonIds {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("findUserErrorHomeWorkLessonIds SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserErrorHomeworkLessonQuestionBean> findUserErrorHomeWorkQuestionIds(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null || queryParam.get("productId") == null  || queryParam.get("lessonIds")==null) {
			logger.error("findUserErrorHomeWorkQuestionIds {} error, parameter uid is null,param:{}",
					namespace, GsonUtils.toJson(queryParam));
			throw new DataAccessException("findUserErrorHomeWorkQuestionIds error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<UserErrorHomeworkLessonQuestionBean>) sqlMap.queryForList(namespace.concat(".findUserErrorHomeWorkQuestionIds"),queryParam);
		} catch (SQLException e) {
			logger.error("findUserErrorHomeWorkQuestionIds {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("findUserErrorHomeWorkQuestionIds SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserErrorEpaperQuestion> findUserErrorEpaperQuestionIds(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null || queryParam.get("productIds") == null) {
			logger.error("findUserErrorEpaperQuestionIds {} error, parameter uid is null,param:{}",
					namespace, GsonUtils.toJson(queryParam));
			throw new DataAccessException("findUserErrorEpaperQuestionIds error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<UserErrorEpaperQuestion>) sqlMap.queryForList(namespace.concat(".findUserErrorEpaperQuestionIds"),queryParam);
		} catch (SQLException e) {
			logger.error("findUserErrorEpaperQuestionIds {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("findUserErrorEpaperQuestionIds SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public Long getUserErrorQuestionCountByUidAndCategory(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null || queryParam.get("categoryId") == null) {
			logger.error("getUserErrorQuestionCountByUidAndCategory {} error, parameter uid or categoryId is null,param:{}",
					namespace, GsonUtils.toJson(queryParam));
			throw new DataAccessException("getUserErrorQuestionCountByUidAndCategory error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (Long) sqlMap.queryForObject(
					namespace+".getUserErrorQuestionCountByUidAndCategory", queryParam);
		} catch (SQLException e) {
			logger.error("getUserErrorQuestionCountByUidAndCategory SQLException.param:{}", GsonUtils.toJson(queryParam), e);
			throw new DataAccessException(
					"getUserErrorQuestionCountByUidAndCategory SQLException error");
		}
	}

	@Override
	public boolean removeErrorQuestionByCategory(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null || queryParam.get("categoryId") == null) {
			logger.error("removeErrorQuestionByCategory {} error, parameter is null", namespace);
			throw new DataAccessException("removeErrorQuestionByCategory {} error,param is null",
					namespace);
		}
		SqlMapClient sqlMap = super.getShardingMaster();
		if (sqlMap == null) {
			logger.error(
					"removeErrorQuestionByCategory SQLException sqlMap is null");
			throw new DataAccessException(
					"removeErrorQuestionByCategory SQLException error, sqlMap is null");
		}
		try {
			return sqlMap.delete(namespace.concat(".removeErrorQuestionByCategory"), queryParam) > 0;
		} catch (SQLException e) {
			logger.error("removeErrorQuestionByCategory SQLException.content:{}",
					(new Gson()).toJson(queryParam), e);
			throw new DataAccessException("removeErrorQuestionByCategory SQLException fail.");
		}
	}

	@Override
	public boolean removeErrorQuestionByCategoryGroupByQuestion(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null || queryParam.get("categoryId") == null) {
			logger.error("removeErrorQuestionByCategoryGroupByQuestion {} error, parameter is null", namespace);
			throw new DataAccessException("removeErrorQuestionByCategoryGroupByQuestion {} error,param is null",
					namespace);
		}
		SqlMapClient sqlMap = super.getShardingMaster();
		if (sqlMap == null) {
			logger.error(
					"removeErrorQuestionByCategoryGroupByQuestion SQLException sqlMap is null");
			throw new DataAccessException(
					"removeErrorQuestionByCategoryGroupByQuestion SQLException error, sqlMap is null");
		}
		try {
			return sqlMap.delete(namespace.concat(".removeErrorQuestionByCategoryGroupByQuestion"), queryParam) > 0;
		} catch (SQLException e) {
			logger.error("removeErrorQuestionByCategoryGroupByQuestion SQLException.content:{}",
					(new Gson()).toJson(queryParam), e);
			throw new DataAccessException("removeErrorQuestionByCategoryGroupByQuestion SQLException fail.");
		}
	}


	@Override
	public boolean removeUserSubErrorQuestion(UserSubErrorQuestion userSubErrorQuestion) throws DataAccessException {
		if (userSubErrorQuestion == null || userSubErrorQuestion.getUid() == null || userSubErrorQuestion.getUid() <= 0L ||
				userSubErrorQuestion.getQuestionId() == null || userSubErrorQuestion.getQuestionId() <=0L) {
			logger.error("removeUserSubErrorQuestion {} error, parameter is null", namespace);
			throw new DataAccessException("removeUserSubErrorQuestion {} error,param is null",
					namespace);
		}
		SqlMapClient sqlMap = super.getShardingMaster();
		if (sqlMap == null) {
			logger.error(
					"removeUserSubErrorQuestion SQLException sqlMap is null");
			throw new DataAccessException(
					"removeUserSubErrorQuestion SQLException error, sqlMap is null");
		}
		try {
			return sqlMap.delete(namespace.concat(".removeUserSubErrorQuestion"), userSubErrorQuestion) > 0;
		} catch (SQLException e) {
			logger.error("removeUserSubErrorQuestion SQLException.content:{}",
					(new Gson()).toJson(userSubErrorQuestion), e);
			throw new DataAccessException("removeUserSubErrorQuestion SQLException fail.");
		}
	}

	@Override
	public boolean removeUserSubErrorQuestionSoft(UserSubErrorQuestion userSubErrorQuestion) throws DataAccessException {
		if (userSubErrorQuestion == null || userSubErrorQuestion.getUid() == null || userSubErrorQuestion.getUid() <= 0L ||
				userSubErrorQuestion.getQuestionId() == null || userSubErrorQuestion.getQuestionId() <=0L) {
			logger.error("removeUserSubErrorQuestionSoft {} error, parameter is null", namespace);
			throw new DataAccessException("removeUserSubErrorQuestionSoft {} error,param is null",
					namespace);
		}
		SqlMapClient sqlMap = super.getShardingMaster();
		if (sqlMap == null) {
			logger.error(
					"removeUserSubErrorQuestionSoft SQLException sqlMap is null");
			throw new DataAccessException(
					"removeUserSubErrorQuestionSoft SQLException error, sqlMap is null");
		}
		try {
			return sqlMap.delete(namespace.concat(".removeUserSubErrorQuestionSoft"), userSubErrorQuestion) > 0;
		} catch (SQLException e) {
			logger.error("removeUserSubErrorQuestionSoft SQLException.content:{}",
					(new Gson()).toJson(userSubErrorQuestion), e);
			throw new DataAccessException("removeUserSubErrorQuestionSoft SQLException fail.");
		}
	}

	@Override
	public List<UserQuestionCountVo> getUserErrorQuestionCountByQtype(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null ) {
			logger.error("getUserErrorQuestionCountByQtype {} error, parameter uid is null,param:{}", namespace, GsonUtils.toJson(queryParam));
			throw new DataAccessException("getUserErrorQuestionCountByQtype error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<UserQuestionCountVo>) sqlMap.queryForList(namespace+".getUserErrorQuestionCountByQtype", queryParam);
		} catch (SQLException e) {
			logger.error("getUserErrorQuestionCountByQtype SQLException.param:{}", GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("getUserErrorQuestionCountByQtype SQLException error");
		}
	}

	@Override
	public List<UserErrorEpaperQuestion> getUserErrorQuestionListGroupByQtype(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null ) {
			logger.error("getUserErrorQuestionListGroupByQtype {} error, parameter uid is null,param:{}", namespace, GsonUtils.toJson(queryParam));
			throw new DataAccessException("getUserErrorQuestionListGroupByQtype error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<UserErrorEpaperQuestion>) sqlMap.queryForList(namespace+".getUserErrorQuestionListGroupByQtype", queryParam);
		} catch (SQLException e) {
			logger.error("getUserErrorQuestionListGroupByQtype SQLException.param:{}", GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("getUserErrorQuestionListGroupByQtype SQLException error");
		}
	}

	@Override
	public List<Long> findUserErrorQuestionIdsByCategoryAndGoodsId(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null) {
			logger.error("findUserErrorQuestionIdsByCategoryAndGoodsId {} error, parameter uid is null,param:{}",
					namespace, GsonUtils.toJson(queryParam));
			throw new DataAccessException("findUserErrorQuestionIdsByCategoryAndGoodsId error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<Long>) sqlMap.queryForList(namespace.concat(".findUserErrorQuestionIdsByCategoryAndGoodsId"),queryParam);
		} catch (SQLException e) {
			logger.error("findUserErrorQuestionIdsByCategoryAndGoodsId {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("findUserErrorQuestionIdsByCategoryAndGoodsId SQLException error"
					+ e.getMessage());
		}
	}
	@Override
	public List<Long> findAnswerIdByCategoryAndGoodsId(Map<String, Object> queryParam) throws DataAccessException {
		if (queryParam == null || queryParam.get("uid") == null) {
			logger.error("findAnswerIdByCategoryAndGoodsId {} error, parameter uid is null,param:{}", namespace, GsonUtils.toJson(queryParam));
			throw new DataAccessException("findAnswerIdByCategoryAndGoodsId error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<Long>) sqlMap.queryForList(namespace.concat(".findAnswerIdByCategoryAndGoodsId"),queryParam);
		} catch (SQLException e) {
			logger.error("findAnswerIdByCategoryAndGoodsId {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("findAnswerIdByCategoryAndGoodsId SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserSubErrorQuestion> getUserSubErrorQuestionList(Long uid, Long goodsId, Long categoryId, Date startTime, Date endTime, Integer noNeedLimit) throws DataAccessException {
		if (uid == null) {
			String errorTip = "getUserSubErrorQuestionList error, param uid is null";
			logger.error(errorTip);
			throw new DataAccessException(errorTip);
		}
		Map<String, Object> queryParam = Maps.newHashMap();
		queryParam.put("uid", uid);
		queryParam.put("goodsId", goodsId);
		queryParam.put("categoryId", categoryId);
		queryParam.put("startTime", startTime);
		queryParam.put("endTime", endTime);
		queryParam.put("noNeedLimit", noNeedLimit);
		queryParam.put("orderBy", " a.create_date desc ");
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<UserSubErrorQuestion>) sqlMap.queryForList(namespace.concat(".getUserSubErrorQuestionList"), queryParam);
		} catch (SQLException e) {
			logger.error("getUserSubErrorQuestionList {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("getUserSubErrorQuestionList SQLException error {}", e.getMessage());
		}
	}

	@Override
	public Integer countUserSubErrorQuestionList(Long uid, Long goodsId, Long categoryId, Date startTime, Date endTime) throws DataAccessException {
		if (uid == null) {
			String errorTip = "countUserSubErrorQuestionList error, param uid is null";
			logger.error(errorTip);
			throw new DataAccessException(errorTip);
		}
		Map<String, Object> queryParam = Maps.newHashMap();
		queryParam.put("uid", uid);
		queryParam.put("goodsId", goodsId);
		queryParam.put("categoryId", categoryId);
		queryParam.put("startTime", startTime);
		queryParam.put("endTime", endTime);
		queryParam.put("orderBy", " a.create_date desc ");
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (Integer) sqlMap.queryForObject(namespace.concat(".countUserSubErrorQuestionList"), queryParam);
		} catch (SQLException e) {
			logger.error("countUserSubErrorQuestionList {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("countUserSubErrorQuestionList SQLException error {}", e.getMessage());
		}
	}
	@Override
	public Boolean insertBatch(List<UserSubErrorQuestion> userSubErrorQuestionList) throws DataAccessException {
		if (CollectionUtils.isEmpty(userSubErrorQuestionList)) {
			logger.error("insertBatch {} error, param is null", namespace);
			throw new DataAccessException("insertBatch {} error,param is null", namespace);
		}
		SqlMapClient sqlMap = super.getShardingMaster();
		try {
			for (UserSubErrorQuestion item : userSubErrorQuestionList) {
				Number id = idworker.nextId();
				item.setId(id.longValue());
			}
			HashMap<String, Object> param = new HashMap<String, Object>();
			param.put("list", userSubErrorQuestionList);
			sqlMap.insert(namespace.concat(".insertBatch"), param);
			return true;
		} catch (SQLException e) {
			logger.error("insert {} SQLException.content:{}", namespace, GsonUtils.toJson(userSubErrorQuestionList), e);
			throw new DataAccessException("add " + namespace + " SQLException fail.");
		}
	}

	@Override
	public Integer getUserErrorQuestionCount(UserErrorAndCorrectQuestionQuery query) throws DataAccessException {
		if (Objects.isNull(query) || !IdUtils.isValid(query.getUid())) {
			String errorTip = "getUserErrorQuestionCount error, param uid is null";
			logger.error(errorTip);
			throw new DataAccessException(errorTip);
		}
		Map<String, Object> queryParam = Maps.newHashMap();
		queryParam.put("uid", query.getUid());
		queryParam.put("goodsId", query.getGoodsId());
		queryParam.put("categoryId", query.getCategoryId());
		queryParam.put("productId", query.getProductId());
		queryParam.put("moreThanTimes",query.getMoreThanTimes());
		queryParam.put("startTime", query.getStartTime());
		queryParam.put("endTime", query.getEndTime());
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (Integer) sqlMap.queryForObject(namespace.concat(".getUserErrorQuestionCount"), queryParam);
		} catch (SQLException e) {
			logger.error("getUserErrorQuestionCount {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
			throw new DataAccessException("getUserErrorQuestionCount SQLException error {}", e.getMessage());
		}
	}

	@Override
	public List<UserSubErrorQuestion> findGroupByList(Page<UserSubErrorQuestion> page, UserErrorAndCorrectQuestionQuery query)throws DataAccessException {
		if (query == null) {
			this.logger.error("findPage {} error, parameter entity is null,entity:{}", this.namespace, query);
			throw new DataAccessException("findPage error,entity is null");
		} else {
			try {
				SqlMapClient sqlMap = super.getSlave();
				Map<String, Object> param = this.transBean2Map(page);
				param.putAll(this.transBean2Map(query));
				return sqlMap.queryForList(this.namespace.concat(".findGroupByList"), param);
			} catch (SQLException e) {
				throw new DataAccessException("list SQLException error" + e.getMessage());
			}
		}
	}


	@Override
	public List<UserSubErrorQuestion> getUserQuestionErrorTimes(Long uid,Long goodsId,Long categoryId, String startTime, String endTime,Integer sourceType, List<Long> questionIdList)throws DataAccessException {
		if (Objects.isNull(uid) ) {
			this.logger.error("getUserQuestionErrorTimes {} error, parameter entity is null,entity:{}", this.namespace, uid);
			throw new DataAccessException("getUserQuestionErrorTimes error,entity is null");
		} else {
			try {
				SqlMapClient sqlMap = super.getSlave();
				Map<String, Object> param = new HashMap<>();
				param.put("uid", uid);
				param.put("goodsId", goodsId);
				param.put("categoryId", categoryId);
				param.put("startTime", startTime);
				param.put("endTime", endTime);
				param.put("sourceType", sourceType);
				param.put("questionIdList", questionIdList);
				return sqlMap.queryForList(this.namespace.concat(".getUserQuestionErrorTimes"), param);
			} catch (SQLException e) {
				throw new DataAccessException("getUserQuestionErrorTimes SQLException error" + e.getMessage());
			}
		}
	}

	@Override
	public long groupByCount(UserErrorAndCorrectQuestionQuery query)throws DataAccessException {
		if (query == null) {
			this.logger.error("groupByCount {} error, parameter entity is null,entity:{}", this.namespace, query);
			throw new DataAccessException("groupByCount error,entity is null");
		} else {
			try {
				SqlMapClient sqlMap = super.getSlave();
				Map<String, Object> param = this.transBean2Map(query);
				Integer ret = (Integer)sqlMap.queryForObject(this.namespace.concat(".groupByCount"), param);
				if (ret == null) {
					ret = 0;
				}
				return ret;
			} catch (SQLException var6) {
				SQLException e = var6;
				this.logger.error("groupByCount {} SQLException.entity:{}", new Object[]{this.namespace, query, e});
				throw new DataAccessException("groupByCount SQLException error" + e.getMessage());
			}
		}
	}

}
