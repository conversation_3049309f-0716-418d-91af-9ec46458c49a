/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorSectionKnowledgeDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.tutor.TutorSectionKnowledge;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 章节知识点DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public class TutorSectionKnowledgeIbatisImpl extends CrudIbatisImpl2<TutorSectionKnowledge> implements
		TutorSectionKnowledgeDao {

	public TutorSectionKnowledgeIbatisImpl() {
		super("TutorSectionKnowledge");
	}

	@Override
	public List<CountModel> listSectionKnowNums(List<Long> sectionIdList) throws DataAccessException {

		if (CollectionUtils.isEmpty(sectionIdList)) {
			logger.error("listSectionKnowNums fail,  param is illegal, sectionIdList");
			throw new DataAccessException( "listSectionKnowNums fail, param is illegal, sectionIdList");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("sectionIdList", sectionIdList);
			List<CountModel>  resutlLit = sqlMap.queryForList(namespace.concat(".listSectionKnowNums"), param);
			return resutlLit;
		} catch (SQLException e) {
			logger.error("listSectionKnowNums {} SQLException. sectionIdList:{}", namespace, sectionIdList, e);
			throw new DataAccessException("listSectionKnowNums SQLException error" + e.getMessage());
		}
	}
}
