package cn.huanju.edu100.study.model.expression;


import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.persistence.model.HqFiledAnnotation;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

//import org.codehaus.jackson.annotate.JsonIgnore;

/**
 * 规则Entity
 * <AUTHOR>
 * @version 2016-05-23
 */
public class ExpressionRule extends DataEntity<ExpressionRule> {

	private static final long serialVersionUID = 1L;
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String title;		// title
	private String tName;		// 表名称
	private String objName;		// 字段名称
	private String rightCompareType;		// 右边比较类型
	private String rightCompareVal;		// 右边比较值
	private String leftCompareType;		// 左边比较类型
	private String leftCompareVal;		// 左边比较值
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String description;		// 描述
    private Integer isOpposite;     // 是否取反，0:否，1：是
    private Integer userType;       // 用户身份，0:全部，1：注册用户，2：游客
    private Integer type;
	private List<String> rightCompareValList;

    private String source; // web或者app

	public ExpressionRule() {
		super();
	}

	public ExpressionRule(Long id){
		super(id);
	}

//	@JsonIgnore
	@JsonIgnore
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTName() {
		return tName;
	}

	public void setTName(String tName) {
		this.tName = tName;
	}

	public String getObjName() {
		return objName;
	}

	public void setObjName(String objName) {
		this.objName = objName;
	}

	public String getRightCompareType() {
		return rightCompareType;
	}

	public void setRightCompareType(String rightCompareType) {
		this.rightCompareType = rightCompareType;
	}

	public String getRightCompareVal() {
		return rightCompareVal;
	}

	public void setRightCompareVal(String rightCompareVal) {
		this.rightCompareVal = rightCompareVal;
	}

	public String getLeftCompareType() {
		return leftCompareType;
	}

	public void setLeftCompareType(String leftCompareType) {
		this.leftCompareType = leftCompareType;
	}

	public String getLeftCompareVal() {
		return leftCompareVal;
	}

	public void setLeftCompareVal(String leftCompareVal) {
		this.leftCompareVal = leftCompareVal;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

    public Integer getIsOpposite() {
        return isOpposite;
    }

    public void setIsOpposite(Integer isOpposite) {
        this.isOpposite = isOpposite;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

	public List<String> getRightCompareValList() {
		return rightCompareValList;
	}

	public void setRightCompareValList(List<String> rightCompareValList) {
		this.rightCompareValList = rightCompareValList;
	}

}
