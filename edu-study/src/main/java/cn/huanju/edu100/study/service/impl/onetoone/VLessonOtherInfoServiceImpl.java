package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.VLessonOtherInfoDao;
import cn.huanju.edu100.study.model.onetoone.VLesson;
import cn.huanju.edu100.study.model.onetoone.VLessonOtherInfo;
import cn.huanju.edu100.study.service.onetoone.VLessonOtherInfoService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 直播/面授课节其他信息Service
 *
 * <AUTHOR>
 * @version 2016-12-16
 */
@Service
public class VLessonOtherInfoServiceImpl extends BaseServiceImpl<VLessonOtherInfoDao, VLessonOtherInfo> implements
        VLessonOtherInfoService {

    @Override
    public void buildLessonOtherInfo(List<VLesson> vLessonList) throws DataAccessException {

        if (CollectionUtils.isNotEmpty(vLessonList)) {
            List<Long> lessonIds = Lists.newArrayList();
            for (VLesson vLesson : vLessonList) {
                lessonIds.add(vLesson.getId());
            }
            Map<Long, VLessonOtherInfo> lessonOtherMap = getLessonOtherInfoMap(lessonIds);
            if (MapUtils.isNotEmpty(lessonOtherMap)) {
                for (VLesson vLesson : vLessonList) {
                    VLessonOtherInfo otherInfo = lessonOtherMap.get(vLesson.getId());
                    if (null != otherInfo) {
                        vLesson.setOtherInfo(otherInfo);
                    }
                }
            }
        }
    }

    @Override
    public List<VLessonOtherInfo> findListByLessonIds(List<Long> lessonIds) throws DataAccessException {

        if (CollectionUtils.isEmpty(lessonIds)) {
            return Collections.emptyList();
        }

        return dao.findListByLessonIds(lessonIds);
    }

    @Override
    public Map<Long, VLessonOtherInfo> getLessonOtherInfoMap(List<Long> lessonIds) throws DataAccessException {

        if (CollectionUtils.isEmpty(lessonIds)) {
            return Collections.emptyMap();
        }

        List<VLessonOtherInfo> otherInfos = findListByLessonIds(lessonIds);
        if (CollectionUtils.isNotEmpty(otherInfos)) {
            Map<Long, VLessonOtherInfo> map = Maps.newHashMap();
            for (VLessonOtherInfo vLessonOtherInfo : otherInfos) {
                map.put(vLessonOtherInfo.getvLessonId(), vLessonOtherInfo);
            }

            return map;
        } else {
            return Collections.emptyMap();
        }
    }



}
