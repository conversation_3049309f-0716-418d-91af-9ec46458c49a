package cn.huanju.edu100.study.dao.qbox;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.questionBox.UserWrongQuestion;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 18/10/29
 */
public interface UserWrongQuestionDao extends CrudDao<UserWrongQuestion> {

    UserWrongQuestion getFromMasterDbByUidQboxIdAndKey(Long uid, Long questionBoxId, String key) throws DataAccessException;

    UserWrongQuestion getByUidQboxIdAndKey(Long uid, Long questionBoxId, String key) throws DataAccessException;

    List<UserWrongQuestion> getByUidAndQboxId(Long uid, Long questionBoxId) throws DataAccessException;

    List<UserWrongQuestion> getByUidAndBoxIds(Long uid, List<Long> boxIds) throws DataAccessException;

    List<UserWrongQuestion> getByUidQboxIdAndKeyLike(Long uid, Long qboxId, String key) throws DataAccessException;

    List<UserWrongQuestion> getByUidQboxIdAndKeyLikeFromMaster(Long uid, Long questionBoxId, String key) throws DataAccessException;

    List<UserWrongQuestion> getByUidQboxIdListAndKeyLike(Long uid, List<Long> qboxIds, String key) throws DataAccessException;

    List<String> getKeysByUidAndQboxId(Long uid, Long questionBoxId) throws DataAccessException;

    void deleteByUidAndQboxId(Long uid, Long questionBoxId) throws  DataAccessException;

    void insertBatch(List<UserWrongQuestion> userWrongQuestionList) throws DataAccessException;

    void insertBatchNew(List<UserWrongQuestion> userWrongQuestionList) throws DataAccessException;

    void deleteByUidAndQboxIdAndIdList(Long uid, Long questionBoxId, List<Long> deleteIdList) throws DataAccessException;
}
