package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.model.PaperSubmitStatisticInfo;
import cn.huanju.edu100.study.repository.PaperSubmitCompareInfoRepository;
import cn.huanju.edu100.study.util.PaperSubmitStatisticRule;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.function.Consumer;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/12 16:25
 * @description 定时把试卷提交统计数据保存到数据库
 */

@Component
@Slf4j
public class SavePaperSubmitStatisticTask {

    @Autowired
    JedisPool alMasterPool;

    @Autowired
    PaperSubmitCompareInfoRepository paperSubmitCompareInfoRepository;

    @XxlJob("SavePaperSubmitStatistic")
    public ReturnT<String> execute(String param) throws Exception {
        log.info("SavePaperSubmitStatistic start");

        execute();

        log.info("SavePaperSubmitStatistic end");
        return ReturnT.SUCCESS;
    }

    /**
     *
     */
    private void execute() {
        final String submitCountKeyPattern = PaperSubmitStatisticRule.SUBMITCOUNT_KEY_PREFIX + "*";
        final String totalScoreKeyPattern = PaperSubmitStatisticRule.SCORE_KEY_PREFIX + "*";
        final String submitAccuracyCountKeyPattern = PaperSubmitStatisticRule.SUBMITACCURACYCOUNT_KEY_PREFIX + "*";
        final String totalAccuracyKeyPattern = PaperSubmitStatisticRule.ACCURACY_KEY_PREFIX + "*";

        List<String> submitCountKeyList = null, totalScoreKeyList = null, submitAccuracyCountKeyList = null, totalAccuracyKeyList = null;
        List<String> submitCountValueList = null, totalScoreValueList = null, submitAccuracyCountValueList = null, totalAccuracyValueList = null;

        // 从 redis 中加载所有试卷提交统计数据
        try(Jedis jedis = alMasterPool.getResource()) {
            // 使用 SCAN 替代 KEYS
            Set<String> submitCountKeys = scanKeys(jedis, submitCountKeyPattern);
            Set<String> totalScoreKeys = scanKeys(jedis, totalScoreKeyPattern);
            Set<String> submitAccuracyCountKeys = scanKeys(jedis, submitAccuracyCountKeyPattern);
            Set<String> totalAccuracyKeys = scanKeys(jedis, totalAccuracyKeyPattern);

            if(submitCountKeys.isEmpty() || totalScoreKeys.isEmpty() || submitAccuracyCountKeys.isEmpty() || totalAccuracyKeys.isEmpty()) {
                log.info("submitCountKeys={}, totalScoreKeys={}, submitAccuracyCountKeys={}, totalAccuracyKeys={}, not need to handler!",
                        submitCountKeys.size(), totalScoreKeys.size(), submitAccuracyCountKeys.size(), totalAccuracyKeys.size());
                return;
            }

            submitCountKeyList = submitCountKeys.stream().toList();
            totalScoreKeyList = totalScoreKeys.stream().toList();
            submitAccuracyCountKeyList = submitAccuracyCountKeys.stream().toList();
            totalAccuracyKeyList = totalAccuracyKeys.stream().toList();
            submitCountValueList = jedis.mget(submitCountKeyList.toArray(new String[0]));
            totalScoreValueList = jedis.mget(totalScoreKeyList.toArray(new String[0]));
            submitAccuracyCountValueList = jedis.mget(submitAccuracyCountKeyList.toArray(new String[0]));
            totalAccuracyValueList = jedis.mget(totalAccuracyKeyList.toArray(new String[0]));
        }

        Map<Long, Map<Integer, PaperSubmitStatisticInfo>> paperSubmitStatisticInfoMap = new HashMap<>(submitCountKeyList.size());
        // submitCountKeyList 和 totalScoreKeyList 不能保证完全一致，所以分开处理
        for(Integer i = 0; i < submitCountKeyList.size(); ++i) {
            final String submitCountKey = submitCountKeyList.get(i);
            final Long submitCount = Long.valueOf(submitCountValueList.get(i));
            handleKey(paperSubmitStatisticInfoMap, submitCountKey, (info) -> {
                info.setSubmitCount(submitCount);
            });
        }
        for(Integer i = 0; i < totalScoreKeyList.size(); ++i) {
            final String submitCountKey = totalScoreKeyList.get(i);
            final Double totalScore = Double.valueOf(totalScoreValueList.get(i));
            handleKey(paperSubmitStatisticInfoMap, submitCountKey, (info) -> {
                info.setTotalScore(totalScore);
            });
        }
        for(Integer i = 0; i < submitAccuracyCountKeyList.size(); ++i) {
            final String submitAccuracyCountKey = submitAccuracyCountKeyList.get(i);
            final Long submitAccuracyCount = Long.valueOf(submitAccuracyCountValueList.get(i));
            handleKey(paperSubmitStatisticInfoMap, submitAccuracyCountKey, (info) -> {
                info.setSubmitAccuracyCount(submitAccuracyCount);
            });
        }
        for(Integer i = 0; i < totalAccuracyKeyList.size(); ++i) {
            final String totalAccuracyKey = totalAccuracyKeyList.get(i);
            final Double totalAccuracy = Double.valueOf(totalAccuracyValueList.get(i));
            handleKey(paperSubmitStatisticInfoMap, totalAccuracyKey, (info) -> {
                info.setTotalAccuracy(totalAccuracy);
            });
        }

        save(paperSubmitStatisticInfoMap);
    }

    private void handleKey(Map<Long, Map<Integer, PaperSubmitStatisticInfo>> paperSubmitStatisticInfoMap, String key, Consumer<PaperSubmitStatisticInfo> consumer) {
        PaperSubmitStatisticInfo paperSubmitStatisticInfo = PaperSubmitStatisticRule.parseKey(key);
        var scoreIndexPaperInfoMap = paperSubmitStatisticInfoMap.getOrDefault(paperSubmitStatisticInfo.getPaperId(), null);
        if(scoreIndexPaperInfoMap == null) {
            scoreIndexPaperInfoMap = new HashMap<>(PaperSubmitStatisticRule.SCORE_RANGE_COUNT);
            paperSubmitStatisticInfoMap.put(paperSubmitStatisticInfo.getPaperId(), scoreIndexPaperInfoMap);
        }
        var existPaperStatisticInfo = scoreIndexPaperInfoMap.getOrDefault(paperSubmitStatisticInfo.getScoreIndex(), paperSubmitStatisticInfo);
        consumer.accept(existPaperStatisticInfo);

        existPaperStatisticInfo.ensureNonNullFields();

        scoreIndexPaperInfoMap.put(paperSubmitStatisticInfo.getScoreIndex(), existPaperStatisticInfo);
    }

    private void save(Map<Long, Map<Integer, PaperSubmitStatisticInfo>> paperSubmitStatisticInfoMap) {
        for(var entry : paperSubmitStatisticInfoMap.entrySet()) {
            Long paperId = entry.getKey();
            var scoreIndexPaperInfoMap = entry.getValue();
            for(var entryPaperInfo : scoreIndexPaperInfoMap.entrySet()) {
                PaperSubmitStatisticInfo paperSubmitStatisticInfo = entryPaperInfo.getValue();
                if(paperSubmitStatisticInfo.getSubmitCount() > 0 || paperSubmitStatisticInfo.getSubmitAccuracyCount() > 0) {
                    // 有统计次数的数据才保存到数据库，避免数据库存储大量为0的数据
                    paperSubmitCompareInfoRepository.save(entryPaperInfo.getValue());
                }
            }
        }
    }

    private Set<String> scanKeys(Jedis jedis, String pattern) {
        Set<String> keys = new HashSet<>();
        String cursor = "0";
        ScanParams params = new ScanParams().match(pattern).count(100);
        do {
            ScanResult<String> result = jedis.scan(cursor, params);
            keys.addAll(result.getResult());
            cursor = result.getCursor();
        } while (!cursor.equals("0"));
        return keys;
    }
}
