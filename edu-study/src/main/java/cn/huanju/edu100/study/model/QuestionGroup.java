package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Collection;


public class QuestionGroup extends DataEntity<QuestionGroup> {

    private static final long serialVersionUID = 1L;
    //	private Long id;
    private Long questionTypeId;
    private String groupName;
    private Integer groupType;
    private String groupDesc;
    private Integer level;

    /*非数据库对应属性*/
    private Integer seq;
    private Integer questionTotal;
    private Double questionScore;
    private Collection<Question> questionList;

    public QuestionGroup(Long questionTypeId, String groupName, Integer groupType, String groupDesc, Integer level, Integer seq, Integer questionTotal, Double questionScore) {
        this.questionTypeId = questionTypeId;
        this.groupName = groupName;
        this.groupType = groupType;
        this.groupDesc = groupDesc;
        this.level = level;
        this.seq = seq;
        this.questionTotal = questionTotal;
        this.questionScore = questionScore;
    }

    public QuestionGroup() {
    }

    public QuestionGroup(Long questionTypeId, String groupName, Integer groupType, String groupDesc, Integer level) {
        this.questionTypeId = questionTypeId;
        this.groupName = groupName;
        this.groupType = groupType;
        this.groupDesc = groupDesc;
        this.level = level;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

//	public Long getId() {
//		return id;
//	}

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

//
//	public void setId(Long id) {
//		this.id = id;
//	}

    public Collection<Question> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(Collection<Question> questionList) {
        this.questionList = questionList;
    }

    public Long getQuestionTypeId() {

        return questionTypeId;
    }

    public void setQuestionTypeId(Long questionTypeId) {
        this.questionTypeId = questionTypeId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    public String getGroupDesc() {
        return groupDesc;
    }

    public void setGroupDesc(String groupDesc) {
        this.groupDesc = groupDesc;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getQuestionTotal() {
        return questionTotal;
    }

    public void setQuestionTotal(Integer questionTotal) {
        this.questionTotal = questionTotal;
    }

    public Double getQuestionScore() {
        return questionScore;
    }

    public void setQuestionScore(Double questionScore) {
        this.questionScore = questionScore;
    }
}
