package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;


public class QuestionTopic extends DataEntity<QuestionTopic> {

    public String getAnalysisText() {
        return analysisText;
    }

    public void setAnalysisText(String analysisText) {
        this.analysisText = analysisText;
    }

    public interface ScoreRule {
        int TOTAL = 0;
        int PART = 1;
        int DEDUCTION = 2;
    }

    public interface Type {
        Integer SINGLE_CHOICE = 0;
        Integer MULTI_CHOICE = 1;
        Integer UNCERTAIN_CHOICE = 2;
        Integer DETERMINE = 3;
        Integer FILL = 4;
        Integer ESSAY = 5;
    }

    private static final long serialVersionUID = 1L;
    //	private Long id;
    private Long qId;
    private Integer seq;
    private Integer qtype;
    private Integer scoreRule;
    private Double score;
    private String content;
    private String answerOption;
    private String answerText;
    private String analysisText;

    /*非数据库对应属性*/
    private List<QuestionOptions> optionList;


    public Long getQId() {
        return qId;
    }

    public void setQId(Long qId) {
        this.qId = qId;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getQtype() {
        return qtype;
    }

    public void setQtype(Integer qtype) {
        this.qtype = qtype;
    }

    public Integer getScoreRule() {
        return scoreRule;
    }

    public void setScoreRule(Integer scoreRule) {
        this.scoreRule = scoreRule;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAnswerOption() {
        return answerOption;
    }

    public void setAnswerOption(String answerOption) {
        this.answerOption = answerOption;
    }

    public String getAnswerText() {
        return answerText;
    }

    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }

    public List<QuestionOptions> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<QuestionOptions> optionList) {
        this.optionList = optionList;
    }
}
