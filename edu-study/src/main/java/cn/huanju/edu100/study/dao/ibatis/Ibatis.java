package cn.huanju.edu100.study.dao.ibatis;

import com.ibatis.sqlmap.client.SqlMapClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;


/**
 * <AUTHOR>
 *
 */
public class Ibatis extends cn.huanju.edu100.dao.ibatis.SqlMapClientBase {
	private static final Logger logger = LoggerFactory.getLogger(Ibatis.class);

	public Ibatis() {
	}

	public List<?> queryForList(SqlMapClient client, String id) throws SQLException {
		return client.queryForList(id);
	}

	public List<?> queryForList(SqlMapClient client, String id, Object parameterObject) throws SQLException {
		return client.queryForList(id, parameterObject);
	}

	protected int update(SqlMapClient client, String id, Object parameterObject) throws SQLException {
		int rows = -1;
		try {
			rows = client.update(id, parameterObject);
		} catch (SQLException e) {
			logger.error("update" + e.getMessage());
			throw e;
		}
		return rows;
	}

	protected Object queryForObject(SqlMapClient client, String id) throws SQLException {
		try {
			Object obj = client.queryForObject(id);
			return obj;
		} catch (SQLException e) {
			logger.error("update" + e.getMessage());
			throw e;
		}
	}

	protected Object queryForObject(SqlMapClient client, String id, Object parameterObject) throws SQLException {
		Object obj = client.queryForObject(id, parameterObject);
		return obj;
	}


	protected Object insertMaster(SqlMapClient client, String id, Object parameterObject) throws SQLException {
		try {
			Object obj = client.insert(id, parameterObject);
			return obj;
		} catch (SQLException e) {
			throw e;
		}
	}

	protected int delete(SqlMapClient client, int dataSourceIndex, String id, Object parameterObject) throws SQLException {
		int ret = client.delete(id, parameterObject);
		return ret;
	}

}
