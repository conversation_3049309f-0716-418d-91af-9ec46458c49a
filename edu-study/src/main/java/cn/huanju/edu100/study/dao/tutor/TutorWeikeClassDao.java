/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorWeikeClass;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 微课班DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public interface TutorWeikeClassDao extends CrudDao<TutorWeikeClass> {

    TutorWeikeClass getByTaskId(Long taskId) throws DataAccessException;

    TutorWeikeClass getBySectionId(Long sectionId) throws DataAccessException;

    List<TutorWeikeClass> listByIds(List<Long> idList) throws DataAccessException;
}