package cn.huanju.edu100.study.mapper.useranswer;

import cn.huanju.edu100.study.model.useranswer.UserAutoRemoveErrorQuestionConfigPO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

@DS("default-ds")
@Mapper
public interface UserAutoRemoveErrorQuestionConfigMapper extends BaseMapper<UserAutoRemoveErrorQuestionConfigPO> {
}
