package cn.huanju.edu100.study.repository;

import cn.huanju.edu100.study.mapper.question.SubjectiveQuestionAICorrectingLogMapper;
import cn.huanju.edu100.study.model.question.SubjectiveQuestionAICorrectingLogPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqwx.study.dto.SubjectiveQuestionAICorrectingLogDTO;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class SubjectiveQuestionAiCorrectingRepository extends ServiceImpl<SubjectiveQuestionAICorrectingLogMapper, SubjectiveQuestionAICorrectingLogPO> {

    /**
     * 获取课后作业主观题AI批改日志
     * @param topicId 主题id
     * @param questionId 题目id
     * @param uid 用户id
     * @param userAnswerId 用户答案id
     * @param limit 限制条数
     * @return 日志列表
     * @Deprecated 试卷主观题ai批阅需求，请使用 {@link #getLogList(Long, Long, Long, Long, Integer)}
     */
    @Deprecated(since = "试卷主观题ai批阅需求", forRemoval = true)
    public List<SubjectiveQuestionAICorrectingLogPO> getSubjectiveQuestionAiCorrectingLog(Long topicId, Long questionId, Long uid, Long userAnswerId, Integer limit) {

        LambdaQueryWrapper<SubjectiveQuestionAICorrectingLogPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(uid), SubjectiveQuestionAICorrectingLogPO::getUid, uid);
        queryWrapper.eq(Objects.nonNull(topicId), SubjectiveQuestionAICorrectingLogPO::getTopicId, topicId);
        queryWrapper.eq(Objects.nonNull(questionId), SubjectiveQuestionAICorrectingLogPO::getQuestionId, questionId);
        if (Objects.nonNull(userAnswerId)) {
            queryWrapper.eq(SubjectiveQuestionAICorrectingLogPO::getUserAnswerId, userAnswerId.toString());
        }
        if (limit != null && limit > 0){
            queryWrapper.last("limit " + limit);
        }
        queryWrapper.orderByDesc(SubjectiveQuestionAICorrectingLogPO::getUpdateDate);

        return getBaseMapper().selectList(queryWrapper);
    }

    /**
     * 获取指定题目来源的主观题AI批改日志
     * @param uid 用户id
     * @param userAnswerId 用户答案id
     * @param questionId 题目id
     * @param topicId 主题id
     * @param questionSource 题目来源，参考 {@link SubjectiveQuestionAICorrectingLogDTO.QuestionSource}
     * @return 日志列表
     */
    public List<SubjectiveQuestionAICorrectingLogPO> getLogList(Long uid, Long userAnswerId, Long questionId,
                                                                Long topicId, Integer questionSource) {
        LambdaQueryWrapper<SubjectiveQuestionAICorrectingLogPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(uid), SubjectiveQuestionAICorrectingLogPO::getUid, uid);
        queryWrapper.eq(Objects.nonNull(userAnswerId), SubjectiveQuestionAICorrectingLogPO::getUserAnswerId, userAnswerId.toString());
        queryWrapper.eq(Objects.nonNull(questionId), SubjectiveQuestionAICorrectingLogPO::getQuestionId, questionId);
        queryWrapper.eq(Objects.nonNull(topicId), SubjectiveQuestionAICorrectingLogPO::getTopicId, topicId);
        queryWrapper.eq(Objects.nonNull(questionSource), SubjectiveQuestionAICorrectingLogPO::getQuestionSource, questionSource);
        queryWrapper.orderByDesc(SubjectiveQuestionAICorrectingLogPO::getUpdateDate);

        return getBaseMapper().selectList(queryWrapper);
    }
    public List<SubjectiveQuestionAICorrectingLogPO> getLogList(@NonNull List<Long> userAnswerIdList, @NonNull Integer questionSource) {
        LambdaQueryWrapper<SubjectiveQuestionAICorrectingLogPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(SubjectiveQuestionAICorrectingLogPO::getUserAnswerId, userAnswerIdList.stream().map(id -> id.toString()).toList());
        queryWrapper.eq(SubjectiveQuestionAICorrectingLogPO::getQuestionSource, questionSource);
        queryWrapper.orderByDesc(SubjectiveQuestionAICorrectingLogPO::getUpdateDate);

        return getBaseMapper().selectList(queryWrapper);
    }

    public Long addLog(SubjectiveQuestionAICorrectingLogPO po) {
        // 使用 MySQl 自动更新时间
        po.setCreateDate(null).setUpdateDate(null);
        getBaseMapper().insert(po);
        return po.getId();
    }

    public boolean updateLog(SubjectiveQuestionAICorrectingLogPO po) {
        // 使用 MySQl 自动更新时间
        po.setCreateDate(null).setUpdateDate(null);
        return getBaseMapper().updateById(po) > 0;
    }
}
