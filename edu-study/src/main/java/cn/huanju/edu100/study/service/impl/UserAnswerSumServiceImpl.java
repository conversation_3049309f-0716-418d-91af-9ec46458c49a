package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.client.SubjectiveQuestionAiCorrectingService;
import cn.huanju.edu100.study.dao.UserAnswerDao;
import cn.huanju.edu100.study.dao.UserAnswerDetailDao;
import cn.huanju.edu100.study.dao.UserAnswerSumDao;
import cn.huanju.edu100.study.dao.UserHomeWorkAnswerDao;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.homework.comment.UserHomeworkComment;
import cn.huanju.edu100.study.model.paper.UserAnswerCommentPO;
import cn.huanju.edu100.study.model.util.UserAnswerUtils;
import cn.huanju.edu100.study.repository.UserAnswerCommentRepository;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserAnswerSumService;
import cn.huanju.edu100.study.service.UserQuestionService;
import cn.huanju.edu100.study.service.homework.comment.NormalCommentContent;
import cn.huanju.edu100.study.service.homework.comment.PhotoCommentContent;
import cn.huanju.edu100.study.service.homework.comment.UserHomeworkCommentService;
import cn.huanju.edu100.study.service.homework.count.UserAnswerSumCountService;
import cn.huanju.edu100.study.service.homework.extend.UserAnswerSumExtendService;
import cn.huanju.edu100.study.service.impl.answer.*;
import cn.huanju.edu100.study.service.impl.syncAnswer.UserBoxExerciseSubmitProxy;
import cn.huanju.edu100.study.service.impl.syncAnswer.UserHomeWorkSubmitProxy;
import cn.huanju.edu100.study.service.impl.syncAnswer.UserPaperSubmitProxy;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.stustamp.constant.Consts;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.JSONUtils;
import cn.huanju.edu100.util.upload.OssUtil;
import com.google.common.collect.Lists;
import com.hqwx.cloud.stream.StreamEventPublisher;
import com.hqwx.study.dto.UserAnswerComment;
import com.hqwx.study.dto.UserAnswerSumDTO;
import com.hqwx.study.dto.query.UserHomeworkCommentListQuery;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import com.hqwx.study.vo.UserAnswerErrorQuestionVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户答案大题详情Service
 *
 * <AUTHOR>
 * @version 2015-05-12
 */
@Service
public class UserAnswerSumServiceImpl extends BaseServiceImpl<UserAnswerSumDao, UserAnswerSum> implements UserAnswerSumService {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
	private CompatableRedisClusterClient answerCompatableRedisClusterClient;

    @Autowired
    private UserHomeWorkSubmitProxy userHomeWorkSubmitProxy;

    @Autowired
    private UserBoxExerciseSubmitProxy userBoxExerciseSubmitProxy;

    @Autowired
    private UserPaperSubmitProxy userPaperSubmitProxy;

    @Autowired
    private UserAnswerDetailDao userAnswerDetailDao;

    @Autowired
    private UserAnswerDao userAnswerDao;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private UserQuestionService userQuestionService;

	@Autowired
	private UserHomeWorkAnswerDao userHomeWorkAnswerDao;

	@Autowired
	private UserAnswerSumExtendService userAnswerSumExtendService;

	@Autowired
	private UserAnswerSumCountService userAnswerSumCountService;

	@Autowired
	private StreamEventPublisher userAnswerPublisher;

	@Autowired
	private UserAnswerCommentRepository userAnswerCommentRepository;

	@Autowired
	private UserHomeworkCommentService userHomeworkCommentService;

	@Autowired
	private SubjectiveQuestionAiCorrectingService subjectiveQuestionAiCorrectingService;

	@Value("${spring.cloud.stream.topics.user-answer-paper}")
	private String topicUserAnswerPaper;

	@Value("${spring.cloud.stream.topics.user-answer-homework}")
	private String topicUserAnswerHomework;

	@Value("${spring.cloud.stream.topics.user-answer-paper-yqp}")
	private String topicUserAnswerPaperYqp;

	@Value("${spring.cloud.stream.topics.user-answer-homework-yqp}")
	private String topicUserAnswerHomeworkYqp;

	private static int expTime = 6*60*60;
    @Override
	public List<UserAnswerSum> findAnswerSumAndDetail(Long uid,Long userAnswerId, List<Long> questionIdList)
			throws DataAccessException {
    	/**先从redis里获取，这里只是为了应对刚提交完的试卷，还在请求队列中，未入DB，但缓存数据已经入了redis*/
    	/**这里使用强获取数据型的get方法*/
    	String userPaperAnswerStr = answerCompatableRedisClusterClient.get(RedisConsts.getUserPaperAnswerKey(uid, userAnswerId),true);

    	if (StringUtils.isNotBlank(userPaperAnswerStr)) {
    		logger.info("findAnswerSumAndDetail catch redis uid:{},userAnswerId:{}:userPaperAnswerStr:{}",uid,userAnswerId,null == userPaperAnswerStr ? 0 : userPaperAnswerStr.length());
    		UserAnswer userPaperAnswer = GsonUtil.getGenericGson().fromJson(userPaperAnswerStr, UserAnswer.class);
    		List<UserAnswerDetail> userAnswerDetails = userPaperAnswer.getAnswerDetail();
    		Map<Long, UserAnswerSum> transMap = new HashMap<Long, UserAnswerSum>();

    		for (UserAnswerDetail userAnswerDetail : userAnswerDetails) {
				if (!transMap.containsKey(userAnswerDetail.getQuestionId())) {//如果不存在
					List<UserAnswerDetail> details = new ArrayList<UserAnswerDetail>();
					details.add(userAnswerDetail);

					UserAnswerSum sum = new UserAnswerSum();
					sum.setQuestionId(userAnswerDetail.getQuestionId());
					sum.setUid(uid);
					sum.setUserAnswerId(userAnswerId);
					sum.setScore(userAnswerDetail.getScore());
					sum.setState(UserAnswerSum.State.DONE);
					sum.setAnswerDetail(details);

					transMap.put(userAnswerDetail.getQuestionId(), sum);

				}else {//如果存在
					UserAnswerSum sum = transMap.get(userAnswerDetail.getQuestionId());
					Double score = 0D;

					List<UserAnswerDetail> details = sum.getAnswerDetail();
					details.add(userAnswerDetail);

					for (UserAnswerDetail detail : details) {
						score += detail.getScore();
					}
					sum.setScore(score);
					sum.setAnswerDetail(details);
				}
			}
    		if (transMap.size()>0) {
                // 查询是否有批阅信息，如果有，则校准主观题得分和正确性
//                // 注意：在计算isRight之前进行校准
//                if (UserAnswer.State.COMMENTED.equals(userPaperAnswer.getState())) {
//                    // 获取批阅信息，并传入试卷ID
//                    try {
//                        correctPaperSubjectiveQuestionScore(uid, userAnswerId, new ArrayList<>(transMap.values()), userPaperAnswer.getPaperId());
//                    } catch (Exception e) {
//                        logger.error("correctSubjectiveQuestionScore error", e);
//                    }
//                }
//
    			//循环每个UserAnswerSum，判断出每道题到底是对还是错
    			Iterator transMapIterator = transMap.entrySet().iterator();
    			while (transMapIterator.hasNext()) {
    	            Map.Entry<Long, UserAnswerSum> answerSumEntry = (Map.Entry<Long, UserAnswerSum>)transMapIterator.next();
    	            UserAnswerSum sum = answerSumEntry.getValue();

    	            List<UserAnswerDetail> details = sum.getAnswerDetail();

    	            Integer isQuestionRight = UserAnswerUtils.getIsQuestionRight(details);

	                sum.setIsRight(isQuestionRight);
    	        }

    			List<UserAnswerSum> sumList = new ArrayList<UserAnswerSum>();
    			sumList.addAll(transMap.values());
				return sumList;
			}
    	}else {
    		List<UserAnswerSum> userAnswerSums = dao.findAnswerSum(uid,userAnswerId,questionIdList);
        	if (userAnswerSums!=null && userAnswerSums.size() >0) {
                // 查询用户答案记录
                UserAnswer userAnswer = null;
                try {
                    Map<String, Object> qryParam = new HashMap<String, Object>();
                    qryParam.put("id", userAnswerId);
                    qryParam.put("uid", uid);
                    userAnswer = userAnswerDao.getShardingById(qryParam);
                } catch (Exception e) {
                    logger.error("get UserAnswer error", e);
                }

                // 获取答题详情
                for (UserAnswerSum userAnswerSum : userAnswerSums) {
                    UserAnswerDetail detail = new UserAnswerDetail();
                    detail.setSumId(userAnswerSum.getId());
                    detail.setUid(uid);
                    List<UserAnswerDetail> details = userAnswerDetailDao.findAllList(detail);
                    userAnswerSum.setAnswerDetail(details);
                }
                
                // 如果试卷状态是已批阅，则校准主观题得分和正确性
//                // 注意：在计算isRight之前进行校准
//                if (userAnswer != null && UserAnswer.State.COMMENTED.equals(userAnswer.getState())) {
//                    try {
//                        correctPaperSubjectiveQuestionScore(uid, userAnswerId, userAnswerSums, userAnswer.getPaperId());
//                    } catch (Exception e) {
//                        logger.error("correctSubjectiveQuestionScore error", e);
//                    }
//                }
                
                // 计算每道题的isRight
                for (UserAnswerSum userAnswerSum : userAnswerSums) {
                    List<UserAnswerDetail> details = userAnswerSum.getAnswerDetail();
                    if (details!=null && details.size()>0) {
						Integer isQuestionRight = UserAnswerUtils.getIsQuestionRight(details);
                        userAnswerSum.setIsRight(isQuestionRight);
                    } else {
                        userAnswerSum.setIsRight(UserAnswerDetail.IsRight.WRONG);
                    }
                }
                
                return userAnswerSums;
    		}
		}

		return null;
	}

    /**
     * 校准主观题得分和正确性
     * 
     * @param comments 批阅信息
     * @param userAnswerSumList 答案总分列表
     * @param paperId 试卷ID
     */
    private void correctPaperSubjectiveQuestionScore(List<UserAnswerComment> comments, List<UserAnswerSum> userAnswerSumList, Long paperId) {

        // 按题目ID分组评论
        Map<Long, List<UserAnswerComment>> questionCommentMap = comments.stream()
                .filter(comment -> comment.getQuestionId() != null && comment.getTopicId() != null
                        && Integer.valueOf(1).equals(comment.getState()))
                .collect(Collectors.groupingBy(UserAnswerComment::getQuestionId));
        
        // 获取题目在试卷中的满分
        Map<Long, QuestionGroupRelation> questionScoreMap = null;
		Map<Long,Question> questionMap =  new HashMap<>();
		if (paperId != null) {
            List<Long> questionIds = userAnswerSumList.stream()
                    .map(UserAnswerSum::getQuestionId)
                    .collect(Collectors.toList());
            try {
                questionScoreMap = knowledgeResource.getQuestionScoresInPaper(paperId, questionIds);
            } catch (Exception e) {
                logger.error("getQuestionScoresInPaper error", e);
            }
			List<Question> questionList = knowledgeResource.getQuestionByIds(questionIds);
			if (CollectionUtils.isNotEmpty(questionList)) {
				questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Question -> Question, (key1, key2) -> key2));
			}
        }



        // 校准每个题目的分数和正确性
        for (UserAnswerSum userAnswerSum : userAnswerSumList) {
            Long questionId = userAnswerSum.getQuestionId();
            List<UserAnswerComment> questionComments = questionCommentMap.get(questionId);
			if (Objects.isNull(questionComments)) {
                questionComments = new ArrayList<>();
            }

            // 更新每个小题的分数和正确性
            if (CollectionUtils.isNotEmpty(userAnswerSum.getAnswerDetail())) {
                for (UserAnswerDetail detail : userAnswerSum.getAnswerDetail()) {
                    for (UserAnswerComment comment : questionComments) {
                        if (Objects.equals(detail.getTopicId(), comment.getTopicId())) {
                            // 更新小题分数
                            if (comment.getScore() != null) {
                                detail.setScore(comment.getScore());
                            }

                            // 更新小题正确性
                            updateDetailIsRight(detail, comment, questionScoreMap, questionId);
                            break;
                        }
                    }
					if (detail.getIsRight() == UserAnswerDetail.IsRight.CORRECTING) {
						//没有改到的，可能是未作答的，判断下
						List<String> answerList = JSONUtils.parseArray(detail.getAnswerStr(),String.class);
						boolean flag = false;
						for (String answer : answerList) {
							if (StringUtils.isNotBlank(answer)) {
								flag = true;
								break;
							}
						}
						if (!flag) {
							detail.setIsRight(UserAnswerDetail.IsRight.NOT_ANSWER);
						} else {
							Question question = questionMap.get(questionId);
							if (question != null && question.getQtype() != null
									&& (Objects.equals(question.getQtype(), cn.huanju.edu100.study.util.Consts.QType.QType6.getCode())
									|| Objects.equals(question.getQtype(), cn.huanju.edu100.study.util.Consts.QType.QType7.getCode()))) {
								//案例题的子题如果是客观题
								for (QuestionTopic topic : question.getTopicList()) {
									if (Objects.equals(topic.getId(), detail.getTopicId())) {
										if (!subjectiveQuestionAiCorrectingService.isSubjectiveQuestion(topic.getQtype())) {
											//客观题未批阅
											AbstractAnswerJudger answerJudger = getAnswerJudger(topic.getQtype());
											int isRight = answerJudger.judge(topic, answerList.toArray(new String[0]));
											//Double score = answerJudger.calculateScore(topic, answerDetailItem.getAnswer(),paperId);
											Double topicScore = answerJudger.calculateScores(topic, answerList.toArray(new String[0]),paperId,questionScoreMap);
											detail.setIsRight(isRight);
											detail.setScore(topicScore);
										}
									}
								}
							} else {
								detail.setScore(0d);
								//这种情况应该是异常了，没有批改到这个小题
								logger.warn("correctPaperSubjectiveQuestionScore error, some topic not corrected, uid:{}, userAnswerId:{}, questionId:{}, topicId:{}", userAnswerSum.getUid(), userAnswerSum.getUserAnswerId(), questionId, detail.getTopicId());
							}
						}
					}
                }

				// 更新分数
				userAnswerSum.setScore(userAnswerSum.getAnswerDetail().stream().mapToDouble(UserAnswerDetail::getScore).sum());
            }
        }
    }
	/**
	 * 校准主观题得分和正确性
	 *
	 * @param comments 批改详情
	 * @param userAnswerSumList 答案总分列表
	 */
	private void correctHomeworkSubjectiveQuestionScore(List<UserHomeworkComment> comments, List<UserAnswerSum> userAnswerSumList) {

		// 按题目ID分组评论
		Map<Long, List<UserHomeworkComment>> questionCommentMap = comments.stream()
				.filter(comment -> comment.getQuestionId() != null && comment.getTopicId() != null
						&& Integer.valueOf(1).equals(comment.getStatus()))
				.collect(Collectors.groupingBy(UserHomeworkComment::getQuestionId));

		Map<Long,Question> questionMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(userAnswerSumList)) {
			List<Long> questionIds = userAnswerSumList.stream()
					.map(UserAnswerSum::getQuestionId)
					.collect(Collectors.toList());
			List<Question> questionList = knowledgeResource.getQuestionByIds(questionIds);
			if (CollectionUtils.isNotEmpty(questionList)) {
				questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Question -> Question, (key1, key2) -> key2));
			}
		}

		// 校准每个题目的分数和正确性
		for (UserAnswerSum userAnswerSum : userAnswerSumList) {
			Long questionId = userAnswerSum.getQuestionId();
			List<UserHomeworkComment> questionComments = questionCommentMap.get(questionId);
			if (CollectionUtils.isEmpty(questionComments)) {
				questionComments = new ArrayList<>();
			}

			// 更新每个小题的分数和正确性
			if (CollectionUtils.isNotEmpty(userAnswerSum.getAnswerDetail())) {
				for (UserAnswerDetail detail : userAnswerSum.getAnswerDetail()) {
					for (UserHomeworkComment comment : questionComments) {
						if (Objects.equals(detail.getTopicId(), comment.getTopicId())) {
							// 更新小题分数
							if (comment.getScore() != null) {
								detail.setScore(comment.getScore());
							} else {
								detail.setScore(0.0d);
							}

							// 更新小题正确性,作业没有办法获取到题目满分，所以只能根据comment中的题目分数来判断正确性
							if (comment.getCorrectMethod() == 3) {//老师批阅-拍照上传
								if (StringUtils.isNotBlank(comment.getComment()) && (comment.getComment().startsWith("http://") || comment.getComment().startsWith("https://"))) {
									String content = OssUtil.getContentDataFromOss(comment.getComment());
									List<PhotoCommentContent> photoCommentContents = JSONUtils.parseArray(content, PhotoCommentContent.class);
									if (CollectionUtils.isNotEmpty(photoCommentContents)) {
										Double totalScore = 0.0d;
										for (PhotoCommentContent photoCommentContent : photoCommentContents) {
											for (PhotoCommentContent.RelationQuestion relationQuestion : photoCommentContent.getRelationQuestion()) {
												totalScore += relationQuestion.getSmallQuestionTotalScore();
											}
										}
										if (detail.getScore() < 0.001) {
											detail.setIsRight(UserAnswerDetail.IsRight.WRONG);
										} else if (totalScore - detail.getScore() < 0.001) {
											detail.setIsRight(UserAnswerDetail.IsRight.RIGHT);
										} else {
											detail.setIsRight(UserAnswerDetail.IsRight.HALF_RIGHT);
										}
									} else {
										detail.setIsRight(UserAnswerDetail.IsRight.RIGHT);
										logger.warn("photoCommentContents is empty, comment: {}", comment);
									}
								} else {
									detail.setIsRight(UserAnswerDetail.IsRight.RIGHT);
									logger.warn("photoCommentContents is empty, comment: {}", comment);
								}
							} else {
								//如果content是json就解析
								if (StringUtils.isNotBlank(comment.getComment()) && (comment.getComment().startsWith("http://") || comment.getComment().startsWith("https://"))) {
									String content = OssUtil.getContentDataFromOss(comment.getComment());
									NormalCommentContent normalCommentContent = JSONUtils.parseObject(content, NormalCommentContent.class);
									if (normalCommentContent != null) {
										Double totalScore = normalCommentContent.getTotalScore();
										if (detail.getScore() < 0.001) {
											detail.setIsRight(UserAnswerDetail.IsRight.WRONG);
										} else if (totalScore - detail.getScore() < 0.001) {
											detail.setIsRight(UserAnswerDetail.IsRight.RIGHT);
										} else {
											detail.setIsRight(UserAnswerDetail.IsRight.HALF_RIGHT);
										}
									} else {
										detail.setIsRight(UserAnswerDetail.IsRight.RIGHT);
										logger.warn("normalCommentContent is null, comment: {}", comment);
									}
								}
							}
							break;
						}
					}
					if (detail.getIsRight() == UserAnswerDetail.IsRight.CORRECTING) {
						//没有改到的，可能是未作答的，判断下
						List<String> answerList = JSONUtils.parseArray(detail.getAnswerStr(),String.class);
						boolean flag = false;
						for (String answer : answerList) {
							if (StringUtils.isNotBlank(answer)) {
								flag = true;
								break;
							}
						}
						if (!flag) {
							detail.setIsRight(UserAnswerDetail.IsRight.NOT_ANSWER);
						} else {
							Question question = questionMap.get(questionId);
							if (question != null && question.getQtype() != null && (
									Objects.equals(question.getQtype(), cn.huanju.edu100.study.util.Consts.QType.QType6.getCode())
									|| Objects.equals(question.getQtype(), cn.huanju.edu100.study.util.Consts.QType.QType7.getCode()))) {
								//案例题的子题如果是客观题
								for (QuestionTopic topic : question.getTopicList()) {
									if (Objects.equals(topic.getId(), detail.getTopicId())) {
										if (!subjectiveQuestionAiCorrectingService.isSubjectiveQuestion(topic.getQtype())) {
											//客观题未批阅
											AbstractAnswerJudger answerJudger = getAnswerJudger(topic.getQtype());
											int isRight = answerJudger.judge(topic, answerList.toArray(new String[0]));
											Double topicScore = answerJudger.calculateScore(topic, answerList.toArray(new String[0]),null);
											detail.setIsRight(isRight);
											detail.setScore(topicScore);
										}
									}
								}
							} else {
								//这种情况应该是异常了，没有批改到这个小题
								logger.warn("correctHomeworkSubjectiveQuestionScore error, some topic not corrected,uid:{} userAnswerId:{}, questionId:{}, topicId:{}", userAnswerSum.getUid(), userAnswerSum.getUserHomeworkId(), questionId, detail.getTopicId());
							}
						}
					}
				}
				// 更新分数
				userAnswerSum.setScore(userAnswerSum.getAnswerDetail().stream().mapToDouble(UserAnswerDetail::getScore).sum());
			}
		}
	}
    
    /**
     * 根据批阅信息更新小题的正确性
     * 
     * @param detail 答题详情
     * @param comment 批阅信息
     * @param questionScoreMap 题目满分信息
     * @param questionId 题目ID
     */
    private void updateDetailIsRight(UserAnswerDetail detail, UserAnswerComment comment, 
                                    Map<Long, QuestionGroupRelation> questionScoreMap, Long questionId) {
        // 如果无分数，保持原状态
        if (comment.getScore() == null) {
            return;
        }
        
        // 未作答状态保持不变
        if (detail.getIsRight() != null && detail.getIsRight() == UserAnswerDetail.IsRight.NOT_ANSWER) {
            return;
        }
        
        // 获取题目满分
        Double fullScore = null;
        if (questionScoreMap != null && questionScoreMap.containsKey(questionId)) {
            // 从试卷题目关系中获取子题满分
            QuestionGroupRelation relation = questionScoreMap.get(questionId);
            String topicScoreJson = relation.getTopicScore();
            // topicScore的格式为 {"128331":2.0}，其中key是topicId
            try {
                if (StringUtils.isNotBlank(topicScoreJson)) {
                    // 将JSON解析为Map
                    Map<String, Double> topicScoreMap = GsonUtil.getGenericGson().fromJson(topicScoreJson, 
                        new com.google.gson.reflect.TypeToken<Map<String, Double>>(){}.getType());
                    
                    // 根据topicId获取满分
                    String topicIdStr = String.valueOf(detail.getTopicId());
                    if (topicScoreMap.containsKey(topicIdStr)) {
                        fullScore = topicScoreMap.get(topicIdStr);
                    }
                }
            } catch (Exception e) {
                logger.error("Parse topicScore error: " + topicScoreJson, e);
            }
        }
        
        // 根据得分判断正确性
        // 如果0分就是错误
        if (comment.getScore() < 0.001) {
            detail.setIsRight(UserAnswerDetail.IsRight.WRONG);
        } 
        // 如果满分就是正确
        else if (fullScore != null && Math.abs(comment.getScore() - fullScore) < 0.001) {
            detail.setIsRight(UserAnswerDetail.IsRight.RIGHT);
        } 
        // 如果大于0但不是满分就是半对
        else if (comment.getScore() > 0.001) {
            detail.setIsRight(UserAnswerDetail.IsRight.HALF_RIGHT);
        }
    }

    @Override
	public List<UserAnswerSum> findHomeworkSumAndDetail(Long uid,Long userHomeworkId, List<Long> questionIdList)
			throws DataAccessException {
    	/**先从redis里获取，这里只是为了应对刚提交完的作业，还在请求队列中，未入DB，但缓存数据已经入了redis*/
    	/**这里使用强获取数据型的get方法*/
    	String userHomeWorkAnswerStr = answerCompatableRedisClusterClient.get(RedisConsts.getUserHomeWorkAnswerKey(uid, userHomeworkId),true);

    	if (StringUtils.isNotBlank(userHomeWorkAnswerStr)) {
    		logger.info("findHomeworkSumAndDetail catch redis uid:{},userHomeworkId:{}:userHomeWorkAnswerStr:{}",uid,userHomeworkId,userHomeWorkAnswerStr);
    		UserHomeWorkAnswer userHomeWorkAnswer = GsonUtil.getGenericGson().fromJson(userHomeWorkAnswerStr, UserHomeWorkAnswer.class);
    		List<UserAnswerDetail> userAnswerDetails = userHomeWorkAnswer.getAnswerDetail();
    		Map<Long, UserAnswerSum> transMap = new HashMap<Long, UserAnswerSum>();

    		for (UserAnswerDetail userAnswerDetail : userAnswerDetails) {
				if(StringUtils.isBlank(userAnswerDetail.getAnswerStr()) && null != userAnswerDetail.getAnswer() && userAnswerDetail.getAnswer().length>0){
					userAnswerDetail.setAnswerStr(GsonUtil.toJson(userAnswerDetail.getAnswer()));
				}
				if (!transMap.containsKey(userAnswerDetail.getQuestionId())) {//如果不存在
					List<UserAnswerDetail> details = new ArrayList<UserAnswerDetail>();
					details.add(userAnswerDetail);

					UserAnswerSum sum = new UserAnswerSum();
					sum.setQuestionId(userAnswerDetail.getQuestionId());
					sum.setUid(uid);
					sum.setUserHomeworkId(userHomeworkId);
					sum.setScore(userAnswerDetail.getScore());
					sum.setState(UserAnswerSum.State.DONE);
					sum.setAnswerDetail(details);

					transMap.put(userAnswerDetail.getQuestionId(), sum);

				}else {//如果存在
					UserAnswerSum sum = transMap.get(userAnswerDetail.getQuestionId());
					Double score = 0D;

					List<UserAnswerDetail> details = sum.getAnswerDetail();
					details.add(userAnswerDetail);

					for (UserAnswerDetail detail : details) {
						if(detail.getScore() != null){
							score += detail.getScore();
						}
					}
					sum.setScore(score);
					sum.setAnswerDetail(details);
				}
			}

    		if (transMap.size()>0) {
    			//循环每个UserAnswerSum，判断出每道题到底是对还是错
    			Iterator transMapIterator = transMap.entrySet().iterator();
    			while (transMapIterator.hasNext()) {
    	            Map.Entry<Long, UserAnswerSum> answerSumEntry = (Map.Entry<Long, UserAnswerSum>)transMapIterator.next();
    	            UserAnswerSum sum = answerSumEntry.getValue();

    	            List<UserAnswerDetail> details = sum.getAnswerDetail();

					Integer isQuestionRight = UserAnswerUtils.getIsQuestionRight(details);

	                sum.setIsRight(isQuestionRight);
    	        }

    			List<UserAnswerSum> sumList = new ArrayList<UserAnswerSum>();
    			sumList.addAll(transMap.values());
				return sumList;
			}

//    		if (transMap.size()>0) {
//    			List<UserAnswerSum> sumList = new ArrayList<UserAnswerSum>();
//    			sumList.addAll(transMap.values());
//				return sumList;
//			}

		}else {
			List<UserAnswerSum> userAnswerSums = dao.findHomeworkSum(uid, userHomeworkId, questionIdList);
	    	if (userAnswerSums!=null && userAnswerSums.size() >0) {
                List<UserAnswerDetail> detailList = userAnswerDetailDao.findByUserHomeworkId(uid, userHomeworkId);
                if (CollectionUtils.isNotEmpty(detailList)) {
                    Map<Long, List<UserAnswerDetail>> sumDetailListMap = detailList.stream().collect(
                            Collectors.groupingBy(UserAnswerDetail::getSumId));
                    for (UserAnswerSum userAnswerSum : userAnswerSums) {
                        List<UserAnswerDetail> details = sumDetailListMap.get(userAnswerSum.getId());
                        userAnswerSum.setAnswerDetail(details);
                        if (details != null && details.size() > 0) {
							Integer isQuestionRight = UserAnswerUtils.getIsQuestionRight(details);

                            userAnswerSum.setIsRight(isQuestionRight);
                        } else {
                            userAnswerSum.setIsRight(UserAnswerDetail.IsRight.WRONG);
                        }
                    }
                    return userAnswerSums;
                }
            }
		}

		return null;
	}

	@Override
	public List<UserAnswerSum> findHomeworkSumAndDetailList(Long uid, List<Long> userHomeworkIds) throws DataAccessException {
		List<UserAnswerSum> sumList = new ArrayList<UserAnswerSum>();
		for(Long userHomeworkId: userHomeworkIds){
			/**先从redis里获取，这里只是为了应对刚提交完的作业，还在请求队列中，未入DB，但缓存数据已经入了redis*/
			/**这里使用强获取数据型的get方法*/
			String userHomeWorkAnswerStr = answerCompatableRedisClusterClient.get(RedisConsts.getUserHomeWorkAnswerKey(uid, userHomeworkId),true);

			if (StringUtils.isNotBlank(userHomeWorkAnswerStr)) {
				logger.info("findHomeworkSumAndDetailList catch redis uid:{},userHomeworkId:{}:userHomeWorkAnswerStr:{}",uid,userHomeworkId,userHomeWorkAnswerStr);
				UserHomeWorkAnswer userHomeWorkAnswer = GsonUtil.getGenericGson().fromJson(userHomeWorkAnswerStr, UserHomeWorkAnswer.class);
				List<UserAnswerDetail> userAnswerDetails = userHomeWorkAnswer.getAnswerDetail();
				Map<String, UserAnswerSum> transMap = new HashMap<String, UserAnswerSum>();

				for (UserAnswerDetail userAnswerDetail : userAnswerDetails) {
					if (!transMap.containsKey(userAnswerDetail.getUserHomeworkId()+"-"+userAnswerDetail.getQuestionId())) {//如果不存在
						List<UserAnswerDetail> details = new ArrayList<UserAnswerDetail>();
						details.add(userAnswerDetail);

						UserAnswerSum sum = new UserAnswerSum();
						sum.setQuestionId(userAnswerDetail.getQuestionId());
						sum.setUid(uid);
						sum.setUserHomeworkId(userHomeworkId);
						sum.setScore(userAnswerDetail.getScore());
						sum.setState(UserAnswerSum.State.DONE);
						sum.setAnswerDetail(details);

						transMap.put(userAnswerDetail.getUserHomeworkId()+"-"+userAnswerDetail.getQuestionId(), sum);

					}else {//如果存在
						UserAnswerSum sum = transMap.get(userAnswerDetail.getUserHomeworkId()+"-"+userAnswerDetail.getQuestionId());
						Double score = 0D;

						List<UserAnswerDetail> details = sum.getAnswerDetail();
						details.add(userAnswerDetail);

						for (UserAnswerDetail detail : details) {
							if(detail.getScore() != null){
								score += detail.getScore();
							}
						}
						sum.setScore(score);
						sum.setAnswerDetail(details);
					}
				}

				if (transMap.size()>0) {
					//循环每个UserAnswerSum，判断出每道题到底是对还是错
					Iterator transMapIterator = transMap.entrySet().iterator();
					while (transMapIterator.hasNext()) {
						Map.Entry<Long, UserAnswerSum> answerSumEntry = (Map.Entry<Long, UserAnswerSum>)transMapIterator.next();
						UserAnswerSum sum = answerSumEntry.getValue();

						List<UserAnswerDetail> details = sum.getAnswerDetail();
						Integer isQuestionRight = UserAnswerUtils.getIsQuestionRight(details);
						sum.setIsRight(isQuestionRight);
					}
					sumList.addAll(transMap.values());
				}
			}else {
				logger.info("findHomeworkSumAndDetailList redis not exist to sql select uid:{},userHomeworkId:{}",uid,userHomeworkId);
				List<UserAnswerSum> userAnswerSums = dao.findAnswerSumAnswerOrHomework(uid,userHomeworkId,null);
				if (userAnswerSums!=null && userAnswerSums.size() >0) {
					List<Long> sumIdList = userAnswerSums.stream().map(UserAnswerSum::getId).distinct().collect(Collectors.toList());
					List<UserAnswerDetail> detailList = userAnswerDetailDao.getUserAnswerDetailBySumIdList(uid, sumIdList);
					if (CollectionUtils.isNotEmpty(detailList)) {
						Map<Long, List<UserAnswerDetail>> sumDetailListMap = detailList.stream().collect(
								Collectors.groupingBy(UserAnswerDetail::getSumId));
						for (UserAnswerSum userAnswerSum : userAnswerSums) {
							List<UserAnswerDetail> details = sumDetailListMap.get(userAnswerSum.getId());
							userAnswerSum.setAnswerDetail(details);
							if (details != null && details.size() > 0) {
								Integer isQuestionRight = UserAnswerUtils.getIsQuestionRight(details);

								userAnswerSum.setIsRight(isQuestionRight);
							} else {
								userAnswerSum.setIsRight(UserAnswerDetail.IsRight.WRONG);
							}
						}
						sumList.addAll(userAnswerSums);
					}
				}
			}
		}
		return  sumList;
	}




	private double calcTotalScore(List<UserAnswerDetail> detailList) {
		double totalScore = 0.0;
		for(UserAnswerDetail userAnswerDetail: detailList) {
			if(userAnswerDetail.getScore() != null && userAnswerDetail.getScore() > 0) {
				totalScore += userAnswerDetail.getScore().doubleValue();
			}
		}
		return  totalScore;
	}

    /**
     * 1.get old answerSums,answerDetails
     * 2.get question include type,answer,score
     * 3.judge answer is right or wrong ,calculate score
     * 4.calculate answer sum info , ready for insert or update
     * 5.insert or update detail
     * 6.if has error answer , insert or update error paper and detail
     * 7.update question and paper statistic info
     *
     * */
    @Override
//    @Transactional(propagation = Propagation.REQUIRED)
    public List<UserAnswerDetail> submit(UserAnswer userAnswer) throws DataAccessException {

    	//标识一下是否是试卷答题
    	Long paperId = userAnswer.getPaperId();

    	//云私塾pro的巩固练习,不计算分,并且试卷也不是普通试卷,所以设置成0
		//题库5.0 入学测评试卷也不计分
    	if (userAnswer.getObjType() != null &&
                ( userAnswer.getObjType() == UserAnswer.ObjType.AL_CONSOLIDATION 	// 巩固练习
                	|| userAnswer.getObjType() == UserAnswer.ObjType.AL_ASSESSMENT	// 入学测评
					|| userAnswer.getObjType() == UserAnswer.ObjType.AL_CHAPTER_REVIEW // 章节复习
					|| userAnswer.getObjType() == UserAnswer.ObjType.TK_ENTERSCHOOLTEST)){ // 题库5.0 入学测评
    	    paperId = 0L;
        }
        List<UserAnswerDetail> answerDetails = userAnswer.getAnswerDetail();

        List<Long> idList = new ArrayList<Long>();
        for (UserAnswerDetail answerDetail : answerDetails) {
        	answerDetail.setUserAnswerId(userAnswer.getId());
			answerDetail.setAnswerStr(GsonUtil.toJson(answerDetail.getAnswer()));
            if (!idList.contains(answerDetail.getQuestionId())) {
                idList.add(answerDetail.getQuestionId());
            }
        }

        //根据提交过来的答题详情，取出对应的题目, 06-25先用knowledge服务取数据,等redis切换后再修改
        List<Question> questionList = knowledgeResource.getQuestionByIds(idList);
        if (questionList.size()<=0) {
			throw new DataAccessException(""+Constants.SYS_ERROR,"getQuestionByIds return null ,param:"+GsonUtil.toJson(idList));
		}

		//map中的key是question的id，不是topic的id。其实方法的三个参数中只有paperId是真正有用的参数
		Map<Long,QuestionGroupRelation> map = getTopicIdQuestionMap(paperId,questionList,answerDetails);

		//增加判断当前提交是需要进行批阅的逻辑
		int needAiCorrecting = subjectiveQuestionAiCorrectingService.isPaperNeedAiCorrecting(userAnswer);
		//如果需要主观题批阅，在提交后，主观题先不给分，批阅完成后再给
      	//这里先循环判断出正确与否，然后返回。
        for (Question question : questionList) {
        	Collection<QuestionTopic> topics = question.getTopicList();
            if (CollectionUtils.isNotEmpty(topics)) {
            	for (QuestionTopic topic : topics) {
                    for (UserAnswerDetail answerDetailItem : answerDetails) {
                        if (topic.getId().equals(answerDetailItem.getTopicId())) {
							AbstractAnswerJudger answerJudger = getAnswerJudger(topic.getQtype());
							int isRight = answerJudger.judge(topic, answerDetailItem.getAnswer());
							//如果当前题目是主观题，并且需要批阅，先不给分，并且设置isRight为批阅中
							if(needAiCorrecting > 0 && subjectiveQuestionAiCorrectingService.isSubjectiveQuestion(question.getQtype())) {
								logger.info("ai correcting: questionId:{},qType:{},topicId:{}",answerDetailItem.getQuestionId(),question.getQtype(),answerDetailItem.getTopicId());
								answerDetailItem.setScore((double) 0);
								if (isRight == UserAnswerDetail.IsRight.NOT_ANSWER) {
									answerDetailItem.setIsRight(UserAnswerDetail.IsRight.NOT_ANSWER);
								} else {
									answerDetailItem.setIsRight(UserAnswerDetail.IsRight.CORRECTING);
								}
								continue;
							}


							//Double score = answerJudger.calculateScore(topic, answerDetailItem.getAnswer(),paperId);
                            Double score = answerJudger.calculateScores(topic, answerDetailItem.getAnswer(),paperId,map);
                            answerDetailItem.setIsRight(isRight);
                            //简答题自评分,云私塾逻辑和题库逻辑做区分
							if(userAnswer.getObjType() != UserAnswer.ObjType.dailyPractice && userAnswer.getObjType() != UserAnswer.ObjType.questionBox) {
								answerDetailItem.setScore(score);
							}else{
								if(topic.getQtype() != null && topic.getQtype() == 5){
									//1.未做---算未做
									//2.做了---不打分---算错误
									//3.做了---打0分----算错误
									//4.做了--打非0分---算正确
									if(answerDetailItem.getScore() != null && answerDetailItem.getScore() > 0 && isRight != UserAnswerDetail.IsRight.NOT_ANSWER){
										answerDetailItem.setIsRight(UserAnswerDetail.IsRight.RIGHT);
									} else if (isRight == UserAnswerDetail.IsRight.NOT_ANSWER) {
										answerDetailItem.setScore((double) 0);
									} else {
										if (answerDetailItem.getIsRight() == UserAnswerDetail.IsRight.NOT_ANSWER) {
											//未做不会有打分，继续为未做。
											answerDetailItem.setScore((double) 0);//未做，得分为0
										} else {
											//没有自评分设定0分，错误。
											answerDetailItem.setIsRight(UserAnswerDetail.IsRight.WRONG);
											answerDetailItem.setScore((double) 0);
										}
									}
								}else {
									answerDetailItem.setScore(score);
								}
							}
                            break;
                        }
                    }
            	}
            }
        }
		userAnswer.setScore(calcTotalScore(answerDetails));

		// 用户提交做题才发布通知
		if(userAnswer.getSubmitReason() == com.hqwx.study.Constants.UserAnswerSubmitReason.UserSubmit) {
			Long schId = userAnswer.getSchId();
			if (schId != null && schId.equals(cn.huanju.edu100.study.util.Consts.SchId.YQP)) {//云企培机构的数据才进行操作
				userAnswerPublisher.publishData(topicUserAnswerPaperYqp, userAnswer);
			} else {
				userAnswerPublisher.publishData(topicUserAnswerPaper, userAnswer);
			}
		} else {
			logger.info("No user submit data, not publish user answer! uid={}, goodsId={}, categoryId={}, productId={}",
					userAnswer.getUid(), userAnswer.getGoodsId(), userAnswer.getCategoryId(), userAnswer.getProductId());
		}

		try {
			//先把结果同步写redis,设置缓存有效期为2分钟---》增加为5分钟吧
            answerCompatableRedisClusterClient.setex(RedisConsts.getUserPaperAnswerKey(userAnswer.getUid(), userAnswer.getId()),300, GsonUtil.getGenericGson().toJson(userAnswer));

		} catch (Exception e) { //添加对redis缓存异常处理避免影响流程
			logger.error("sty_UserAnswerPaper submit uid:{},userAnswerId:{},Exception.", userAnswer.getUid(), userAnswer.getId(), e);
		}
		//剩余的事情，则丢到一个线程队列里去计算和入库
        userPaperSubmitProxy.submitPaper(userAnswer, questionList);

        return answerDetails;

    }

	/**
	 * 根据试卷id，题集，答案获取题目分数map
	 * @param paperId
	 * @param questionList
	 * @param answerDetails
	 * @return
	 */
	private Map<Long,QuestionGroupRelation>  getTopicIdQuestionMap(Long paperId,List<Question> questionList, List<UserAnswerDetail> answerDetails) {
		Map<Long,QuestionGroupRelation> map = new HashMap<Long, QuestionGroupRelation>();
		List<Long> qid = new ArrayList<Long>();//topicList
		for (Question question : questionList) {
			Collection<QuestionTopic> topics = question.getTopicList();
			if (CollectionUtils.isNotEmpty(topics)) {
				for (QuestionTopic topic : topics) {
					for (UserAnswerDetail answerDetailItem : answerDetails) {
						if (topic.getId().equals(answerDetailItem.getTopicId())) {
							qid.add(topic.getId());
						}
					}
				}
			}
		}

		try {
			map = knowledgeResource.getQuestionScoresInPaper(paperId,qid);//这里qid传入并没有什么用
		} catch (DataAccessException e) {
			e.printStackTrace();
		}

		return map;
	}

	@Override
	public List<UserAnswerDetail> submitHomeWork(UserHomeWorkAnswer userHomeWorkAnswer) throws DataAccessException {

        List<UserAnswerDetail> answerDetails = userHomeWorkAnswer.getAnswerDetail();

        List<Long> idList = new ArrayList<Long>();
        for (UserAnswerDetail answerDetail : answerDetails) {
        	answerDetail.setUserHomeworkId(userHomeWorkAnswer.getId());
            if (!idList.contains(answerDetail.getQuestionId())) {
                idList.add(answerDetail.getQuestionId());
            }
        }

        //根据提交过来的答题详情，取出对应的题目, 06-25先用knowledge服务取数据,等redis切换后再修改
        List<Question> questionList = knowledgeResource.getQuestionByIds(idList);

        if (questionList.size()<=0) {
			throw new DataAccessException(""+Constants.SYS_ERROR,"getQuestionByIds return null ,param:"+GsonUtil.toJson(idList));
		}

        //增加判断当前提交是需要进行批阅的逻辑
        int needAiCorrecting = subjectiveQuestionAiCorrectingService.isHomeworkNeedAiCorrecting(userHomeWorkAnswer);
        //如果需要主观题批阅，在提交后，主观题先不给分，批阅完成后再给
        //这里先循环判断出正确与否，然后返回。
        for (Question question : questionList) {
        	Collection<QuestionTopic> topics = question.getTopicList();
            if (CollectionUtils.isNotEmpty(topics)) {
            	for (QuestionTopic topic : topics) {
                    for (UserAnswerDetail answerDetailItem : answerDetails) {
						if (topic.getId().equals(answerDetailItem.getTopicId())) {
							AbstractAnswerJudger answerJudger = getAnswerJudger(topic.getQtype());
							int isRight = answerJudger.judge(topic, answerDetailItem.getAnswer());
							//如果当前题目是主观题，并且需要批阅，先不给分，并且设置isRight为批阅中
							if(needAiCorrecting > 0 && subjectiveQuestionAiCorrectingService.isSubjectiveQuestion(question.getQtype())) {
								answerDetailItem.setScore((double) 0);
								if (isRight == UserAnswerDetail.IsRight.NOT_ANSWER) {
									answerDetailItem.setIsRight(UserAnswerDetail.IsRight.NOT_ANSWER);
								} else {
									answerDetailItem.setIsRight(UserAnswerDetail.IsRight.CORRECTING);
								}
								continue;
							}

                            Double score = answerJudger.calculateScore(topic, answerDetailItem.getAnswer(),null);
                            answerDetailItem.setIsRight(isRight);
                            answerDetailItem.setScore(score);
                        }
                    }
            	}
            }
        }
		userHomeWorkAnswer.setScore(calcTotalScore(answerDetails));

		// 用户提交的做题才发布通知
		if(userHomeWorkAnswer.getSubmitReason() == com.hqwx.study.Constants.UserAnswerSubmitReason.UserSubmit) {
			Long schId = userHomeWorkAnswer.getSchId();
			if (schId != null && schId.equals(cn.huanju.edu100.study.util.Consts.SchId.YQP)) {//云企培机构的数据才进行操作
				userAnswerPublisher.publishData(topicUserAnswerHomeworkYqp, userHomeWorkAnswer);
			} else {
				userAnswerPublisher.publishData(topicUserAnswerHomework, userHomeWorkAnswer);
			}
		} else {
			logger.info("No user submit data, not publish homework data! uid={}, goodsId={}, categoryId={}, productId={}",
					userHomeWorkAnswer.getUid(), userHomeWorkAnswer.getGoodsId(), userHomeWorkAnswer.getCategoryId(),
					userHomeWorkAnswer.getProductId());
		}

        //先把结果同步写redis,设置缓存有效期为2分钟---》增加为5分钟吧
        answerCompatableRedisClusterClient.setex(RedisConsts.getUserHomeWorkAnswerKey(userHomeWorkAnswer.getUid(), userHomeWorkAnswer.getId()),300, GsonUtil.getGenericGson().toJson(userHomeWorkAnswer));
        //存储作业额外信息
		if (StringUtils.isNotBlank(userHomeWorkAnswer.getTagStr())){
			UserAnswerSumExtend userAnswerSumExtend = new UserAnswerSumExtend();
			userAnswerSumExtend.setAnswerId(userHomeWorkAnswer.getId());
			userAnswerSumExtend.setTagStr(userHomeWorkAnswer.getTagStr());
			userAnswerSumExtend.setType(userHomeWorkAnswer.getExtendType());
			userAnswerSumExtend.setUid(userHomeWorkAnswer.getUid());
			userAnswerSumExtendService.save(userAnswerSumExtend);
		}
		if (userHomeWorkAnswer.getIsCount() != null && Consts.YES_OR_NO.YES.equals(userHomeWorkAnswer.getIsCount())){
			UserAnswerSumCount userAnswerSumCount = new UserAnswerSumCount();
			userAnswerSumCount.setUid(userHomeWorkAnswer.getUid());
			userAnswerSumCount.setLessonId(userHomeWorkAnswer.getObjId());
			userAnswerSumCount.setUserHomeworkAnswerId(userHomeWorkAnswer.getId());
			userAnswerSumCount.setHomeworkId(userHomeWorkAnswer.getHomeworkId());
			userAnswerSumCountService.save(userAnswerSumCount);
		}
        //剩余的事情，则丢到一个线程队列里去计算和入库
        userHomeWorkSubmitProxy.submitHomeWork(userHomeWorkAnswer, questionList);

        return answerDetails;

	}

    @Override
	public List<UserAnswerDetail> submitBoxExercise(UserExerciseAnswer userExerciseAnswer,Integer source) throws DataAccessException {

        List<UserAnswerDetail> answerDetails = userExerciseAnswer.getAnswerDetail();

        List<Long> idList = new ArrayList<Long>();
        for (UserAnswerDetail answerDetail : answerDetails) {
        	answerDetail.setUserHomeworkId(userExerciseAnswer.getId());
            if (!idList.contains(answerDetail.getQuestionId())) {
                idList.add(answerDetail.getQuestionId());
            }
        }

        //根据提交过来的答题详情，取出对应的题目, 06-25先用knowledge服务取数据,等redis切换后再修改
        List<Question> questionList = knowledgeResource.getQuestionByIds(idList);

        if (questionList.isEmpty()) {
			throw new DataAccessException(""+Constants.SYS_ERROR,"getQuestionByIds return null ,param:"+GsonUtil.toJson(idList));
		}

        //这里先循环判断出正确与否，然后返回。
        for (Question question : questionList) {
        	Collection<QuestionTopic> topics = question.getTopicList();
            if (CollectionUtils.isNotEmpty(topics)) {
            	for (QuestionTopic topic : topics) {
                    for (UserAnswerDetail answerDetailItem : answerDetails) {
                        if (topic.getId().equals(answerDetailItem.getTopicId())) {
                            AbstractAnswerJudger answerJudger = getAnswerJudger(topic.getQtype());
                            int isRight = answerJudger.judge(topic, answerDetailItem.getAnswer());
                            Double score = answerJudger.calculateScore(topic, answerDetailItem.getAnswer(),null);
                            answerDetailItem.setIsRight(isRight);
                            answerDetailItem.setScore(score);
                            break;
                        }
                    }
            	}
            }
        }
		userExerciseAnswer.setScore(calcTotalScore(answerDetails));

		if(userExerciseAnswer.getSubmitReason() == com.hqwx.study.Constants.UserAnswerSubmitReason.UserSubmit) {
			Long schId = userExerciseAnswer.getSchId();
			if (schId != null && schId.equals(cn.huanju.edu100.study.util.Consts.SchId.YQP)) {//云企培机构的数据才进行操作
				userAnswerPublisher.publishData(topicUserAnswerHomeworkYqp, userExerciseAnswer);
			} else {
				userAnswerPublisher.publishData(topicUserAnswerHomework, userExerciseAnswer);
			}
		} else {
			logger.info("No user submit data, not publish exercise answer! uid={}, goodsId={}, categoryId={}, productId={}",
					userExerciseAnswer.getUid(), userExerciseAnswer.getGoodsId(),
					userExerciseAnswer.getCategoryId(), userExerciseAnswer.getProductId());
		}

        //先把结果同步写redis,设置缓存有效期为2分钟
        answerCompatableRedisClusterClient.setex(RedisConsts.getUserHomeWorkAnswerKey(userExerciseAnswer.getUid(), userExerciseAnswer.getId()),120, GsonUtil.getGenericGson().toJson(userExerciseAnswer));

        userBoxExerciseSubmitProxy.submitExercise(userExerciseAnswer, questionList,source);

        return answerDetails;
	}

    /**
     * 错题作答
     * */
    @Override
	public HashMap<Long, Object> submitErrorQuestion(List<UserErrorAnswer> userErrorAnswerList) throws DataAccessException {
		HashMap<Long, Object> ret = new HashMap<Long, Object>();

		//提炼questionId
		List<Long> questionIds = new ArrayList<Long>();
		for (UserErrorAnswer userErrorAnswer : userErrorAnswerList) {
			questionIds.add(userErrorAnswer.getQuestionId());
		}

		//获得questionList
		List<Question> questionList = knowledgeResource.getQuestionByIds(questionIds);

        if (questionList==null || questionList.size()<=0) {
			throw new DataAccessException(""+Constants.SYS_ERROR,"getQuestionByIds return null ,param:"+GsonUtil.toJson(questionList));
		}

        //提炼topicMap
        HashMap<Long, QuestionTopic> topicMap = new HashMap<Long, QuestionTopic>();

        for (Question question : questionList) {
        	Collection<QuestionTopic> topics = question.getTopicList();
            if (CollectionUtils.isNotEmpty(topics)) {
            	for (QuestionTopic topic : topics) {
            		topicMap.put(topic.getId(), topic);
            	}
            }
        }
        List<UserErrorAnswer> rightAnswers = new ArrayList<UserErrorAnswer>();

        //判断对错
        if (topicMap.size()>0) {
        	for (UserErrorAnswer userErrorAnswer : userErrorAnswerList) {
        		QuestionTopic topic = topicMap.get(userErrorAnswer.getTopicId());

        		UserAnswerDetail answerDetail = new UserAnswerDetail();
        		answerDetail.setUid(userErrorAnswer.getUid());
        		answerDetail.setQuestionId(userErrorAnswer.getQuestionId());
        		answerDetail.setTopicId(userErrorAnswer.getTopicId());
        		answerDetail.setAnswer(userErrorAnswer.getAnswer());

        		if (topic!=null) {
        			AbstractAnswerJudger answerJudger = getAnswerJudger(topic.getQtype());
                    int isRight = answerJudger.judge(topic, userErrorAnswer.getAnswer());
                    answerDetail.setIsRight(isRight);

                    if (isRight == UserAnswerDetail.IsRight.RIGHT) {
                    	if (userErrorAnswer.getUserErrorId()!=null && userErrorAnswer.getUserErrorId()!=0L) {
                    		rightAnswers.add(userErrorAnswer);
						}
					}
                    ret.put(userErrorAnswer.getTopicId(), answerDetail);
				}
    		}
		}

        //清除掉错题表中的记录
        List<UserErrorQuestion> errorList = new ArrayList<UserErrorQuestion>();
        if (rightAnswers.size()>0) {
			for (UserErrorAnswer rightAnswer : rightAnswers) {
				errorList.add(new UserErrorQuestion(rightAnswer.getUserErrorId()));
			}
			userQuestionService.removeUserErrorQuestionList(errorList);
		}

		return ret;
	}



	@Override
	public Page<UserAnswerSum> adminstudygetUserAnswerSumfindPage(Page page, UserAnswerSum userAnswerSum) throws DataAccessException {
		page.setList(this.dao.findList(page,userAnswerSum)); /*使用原有的*/
		return page;
	}

	@Override
	public List<UserAnswerDetail> findAnswerSumQuestion(Map<String, Object> params) throws DataAccessException {
		return this.dao.findAnswerSumQuestion(params);
	}

    @Override
    public List<UserAnswerDetail> getQuestionListByAnswerId(Map<String, Object> params) throws DataAccessException {
		return this.dao.getQuestionListByAnswerId(params);
    }

    @Override
    public List<UserAnswerDetail> getAnswerDetailList(UserAnswerDetail detail) throws DataAccessException {
		return userAnswerDetailDao.findAllList(detail);
    }

    @Override
    public List<UserAnswerDetail> getQuestionListByHomeworkAnswerId(Map<String, Object> params) throws DataAccessException {
		return this.dao.getQuestionListByHomeworkAnswerId(params);
    }

    @Override
    public Integer countQuestionListByHomeworkAnswerId(Map<String, Object> params) throws DataAccessException {
		return this.dao.countQuestionListByHomeworkAnswerId(params);
    }

    @Override
    public Integer countQuestionListByAnswerId(Map<String, Object> params) throws DataAccessException {
		return this.dao.countQuestionListByAnswerId(params);
    }

    @Override
	public boolean delete(UserAnswerSum userAnswerSum) throws DataAccessException {
		return this.dao.delete(userAnswerSum);
	}

	@Override
	public List<UserAnswerSum> getUserAllQuestion(UserAnswerSum userAnswerSum) throws DataAccessException {
		return this.dao.findUserAllQuestion(userAnswerSum);
	}

	@Override
	public List<UserAnswerHistory> getUserAnswerHistoryPage(Long uid, Integer pageSize, Integer pageNo) throws DataAccessException {
    	Map param = new HashMap();
    	param.put("uid",uid);
		param.put("pageSize",pageSize);
		param.put("pageNo",pageNo);
		List<UserAnswerHistory> list = dao.findUserAllQuestionHistory(param);
		if(CollectionUtils.isEmpty(list)){
			return list;
		}
		//设置category_id ,detail
		List<Long> qids = new ArrayList<Long>();
		for(UserAnswerHistory history: list){
			qids.add(history.getQuestion_id());
		}
		List<Question> questionList = knowledgeResource.getQuestionByIds(qids);
		Map<Long,Long> isRightMap = new HashMap<Long, Long>();
		for(Question question : questionList){
			isRightMap.put(question.getId(),question.getCategoryId());
		}
		for(final UserAnswerHistory history: list){
			history.setCategory_id(isRightMap.get(history.getQuestion_id()));
		}

		return list;
	}

	@Override
	public Map getUserAnswerDetailByIdAndUid(Long id, Long uid,Integer type) throws DataAccessException {
    	Map result = new HashMap();
    	Double score = 0d;
    	Double rightRate = 0d;
    	Integer rightCount = 0;
		Integer qCount = 0;
		List<PaperUserAnswerDetail> list = new ArrayList<PaperUserAnswerDetail>();
		if(type == 1) {
			list = dao.findUserAnswerDetailByIdAndUid(id, uid);
		}else {
			list = dao.findUserAnswerHomeworkDetailByIdAndUid(id, uid);
		}
		if(CollectionUtils.isNotEmpty(list)){
			for(PaperUserAnswerDetail detail : list){
				score = detail.getTotalScore();
				qCount++;
				if(detail.getIsRight() == 2){
					rightCount++;
				}
			}
			rightRate = new BigDecimal(rightCount).divide(new BigDecimal(qCount),2,BigDecimal.ROUND_HALF_UP).doubleValue();
		}

		result.put("score",score);
		result.put("rightRate",rightRate);
		result.put("qCount",qCount);
		return result;
	}

	@Override
	public List<AlPaperReport> getUserAnswerListByUidAndDate(Long uid, Integer type, Long categoryId,Date startTime, Date endTime,List<Long> resIds) throws DataAccessException {
		List<AlPaperReport> reports = new ArrayList<AlPaperReport>();
    	if(type == 1){//试卷
			reports = dao.getUserAnswerListByUidAndDate(uid,categoryId,startTime,endTime);
			if(CollectionUtils.isNotEmpty(reports)){
				for(AlPaperReport report : reports){
					//设置得分和正确率
					Long userAnswerId = report.getUserAnswerId();
					Map map = getUserAnswerDetailByIdAndUid(userAnswerId,report.getUid(),1);
					if(map !=null){
						report.setScore((Double)map.get("score"));
						report.setRightRate((Double)map.get("rightRate"));
						report.setStudyCount(Integer.parseInt(map.get("qCount")+""));
					}
				}
			}
		}else{//作业
			reports = dao.getUserAnswerHomeworkListByUidAndDate(uid,categoryId,startTime,endTime,resIds);
			if(CollectionUtils.isNotEmpty(reports)){
				for(AlPaperReport report : reports){
					//设置得分和正确率
					Long userAnswerId = report.getUserAnswerId();
					Map map = getUserAnswerDetailByIdAndUid(userAnswerId,report.getUid(),2);
					if(map !=null){
						report.setScore((Double)map.get("score"));
						report.setRightRate((Double)map.get("rightRate"));
						report.setStudyCount(Integer.parseInt(map.get("qCount")+""));
					}
				}
			}
		}
		return reports;
	}

	@Override
	public List<UserAnswerSumDTO> findAllList(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException {
		UserAnswerSum userAnswerSum = new UserAnswerSum();
		BeanUtils.copyProperties(userAnswerSumDTO,userAnswerSum);
		List<UserAnswerSum> userAnswerSumList = dao.findAllList(userAnswerSum);
		List<UserAnswerSumDTO> resultList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(userAnswerSumList)){
			userAnswerSumList.stream().forEach(answerSum -> {
				UserAnswerSumDTO result = new UserAnswerSumDTO();
				BeanUtils.copyProperties(answerSum,result);
				resultList.add(result);
			});
		}
		return resultList;
	}

	@Override
	public List<UserAnswerDetail> getQuestionListByAnswerIds(Map<String, Object> params) throws DataAccessException {
		return this.dao.getQuestionListByAnswerIds(params);
	}

	private List<UserAnswerSum> setDetails(Long uid, List<UserAnswerSum> answerSums) throws DataAccessException {
        int i = 0;
        for (UserAnswerSum userAnswer : answerSums) {
            UserAnswerDetail detail = new UserAnswerDetail();
            detail.setSumId(userAnswer.getId());
			detail.setUid(uid);
            userAnswer.setAnswerDetail(userAnswerDetailDao.findAllList(detail));
            answerSums.set(i++, userAnswer);
        }
        return answerSums;
    }

    private static AbstractAnswerJudger[] judgers = new AbstractAnswerJudger[] {
            new SingleChoiceAnswerJudger(),//0单选
            new MultiChoiceAnswerJudger(),//1多选
            new UncertainChoiceAnswerJudger(),//2不定项选择
            new DetermineAnswerJudger(),//3判断题
            new FillAnswerJudger(),//4填空题
            new SubjectiveAnswerJudger(),//5简答题
    };

	private AbstractAnswerJudger getAnswerJudger(Integer questionType) {
		if (questionType == null) {
			questionType = cn.huanju.edu100.study.util.Consts.QType.QType5.getCode();
		} else {
			if (questionType == cn.huanju.edu100.study.util.Consts.QType.QType20.getCode().intValue()) {
				questionType = cn.huanju.edu100.study.util.Consts.QType.QType0.getCode();
			} else if (questionType == cn.huanju.edu100.study.util.Consts.QType.QType21.getCode().intValue()) {
				questionType = cn.huanju.edu100.study.util.Consts.QType.QType4.getCode();
			} else if (questionType >= cn.huanju.edu100.study.util.Consts.QType.QType5.getCode().intValue()) {
				questionType = cn.huanju.edu100.study.util.Consts.QType.QType5.getCode();
			}
		}
		return judgers[questionType];
	}

	@Override
	public List<UserAnswerSumDTO> findListByGoodsId(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException {
		if (userAnswerSumDTO.getUid() == null) {
			return null;
		}
		UserHomeWorkAnswer item = new UserHomeWorkAnswer();
		item.setUid(userAnswerSumDTO.getUid());
		item.setGoodsId(userAnswerSumDTO.getGoodsId());
		List<UserHomeWorkAnswer> userHomeWorkAnswerList = userHomeWorkAnswerDao.findList(item);
		if (CollectionUtils.isEmpty(userHomeWorkAnswerList)) {
			return null;
		}
		List<Long> userHomeworkIds = userHomeWorkAnswerList.stream().map(UserHomeWorkAnswer::getId).collect(Collectors.toList());
		List<UserAnswerSum> userAnswerSumList = dao.findAnswerSum(userAnswerSumDTO.getUid(), userHomeworkIds, null);
		List<UserAnswerSumDTO> resultList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(userAnswerSumList)) {
			userAnswerSumList.stream().forEach(answerSum -> {
				UserAnswerSumDTO result = new UserAnswerSumDTO();
				BeanUtils.copyProperties(answerSum, result);
				resultList.add(result);
			});
		}
		return resultList;
	}

	@Override
	public List<UserAnswerSumDTO> findListByGoodsIdAndProductId(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException {
		if (userAnswerSumDTO.getUid() == null) {
			return null;
		}
		UserHomeWorkAnswer item = new UserHomeWorkAnswer();
		item.setUid(userAnswerSumDTO.getUid());
		item.setGoodsId(userAnswerSumDTO.getGoodsId());
		item.setProductId(userAnswerSumDTO.getProductId());
		List<UserHomeWorkAnswer> userHomeWorkAnswerList = userHomeWorkAnswerDao.findList(item);
		if (CollectionUtils.isEmpty(userHomeWorkAnswerList)) {
			return null;
		}
		List<Long> userHomeworkIds = userHomeWorkAnswerList.stream().map(UserHomeWorkAnswer::getId).collect(Collectors.toList());
		List<UserAnswerSum> userAnswerSumList = dao.findAnswerSum(userAnswerSumDTO.getUid(), userHomeworkIds, null);
		List<UserAnswerSumDTO> resultList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(userAnswerSumList)) {
			userAnswerSumList.stream().forEach(answerSum -> {
				UserAnswerSumDTO result = new UserAnswerSumDTO();
				BeanUtils.copyProperties(answerSum, result);
				resultList.add(result);
			});
		}
		return resultList;
	}

	private List<Long> getEffectQuestionIdList(List<Long> totalQuestionIds){
		List<Question> allQuestionList = knowledgeResource.getQuestionByIds(totalQuestionIds);
		if(CollectionUtils.isEmpty(allQuestionList)){
			return Lists.newArrayList();
		}
		return allQuestionList.stream()
				.filter(question -> question.getState()!=null
						&&question.getState().equals(1)
						&&question.getProTagRelation()!=null
						&&!question.getProTagRelation().contains("9")
						&&!question.getProTagRelation().contains("10"))
				.map(Question::getId).distinct().collect(Collectors.toList());
	}

	@Override
	public List<UserAnswerSumDTO> findAlSubmitQuestionList(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException {
		List<UserAnswerSumDTO> resultList = new ArrayList<>();
		Long uid=userAnswerSumDTO.getUid();
		if (uid == null) {
			return null;
		}
		UserHomeWorkAnswer item = new UserHomeWorkAnswer();
		item.setUid(uid);
		item.setGoodsId(userAnswerSumDTO.getGoodsId());
		item.setProductId(userAnswerSumDTO.getProductId());
		item.setObjType(userAnswerSumDTO.getHomeworkAnswerObjType());
		//查询课后作业已作答记录列表
		List<UserHomeWorkAnswer> userHomeWorkAnswerList = userHomeWorkAnswerDao.findList(item);
		if (CollectionUtils.isEmpty(userHomeWorkAnswerList)) {
			return null;
		}
		List<Long> userHomeworkIds = userHomeWorkAnswerList.stream().map(UserHomeWorkAnswer::getId).collect(Collectors.toList());
		Map param = new HashMap();
		param.put("uid",uid);
		param.put("userHomeworkIds",userHomeworkIds);
		//根据课后作业作答ID集合，获取所有已作答的题目列表
		List<UserAnswerSum> userAnswerSumList=dao.findAlSubmitQuestionList(param);
		if (CollectionUtils.isNotEmpty(userAnswerSumList)) {
			//所有已作答的题目ID集合
			List<Long> questionIds = userAnswerSumList.stream().map(UserAnswerSum::getQuestionId).collect(Collectors.toList());
			//根据已作答题目ID集合，获取最近一次提交作答明细记录列表
			List<UserAnswerSum> lastSumList=this.dao.getLastUserAnswerSumAndDetailByQuestionIds(uid,questionIds);
			//作答正确的明细列表
			List<UserAnswerSum> lastRightSumList=lastSumList.stream(). filter(answerSum -> answerSum.getIsRight() != null && answerSum.getIsRight()==2).collect(Collectors.toList());
			Map<String,UserAnswerSum> lastRightSumMap=new HashMap<>();
			if(CollectionUtils.isNotEmpty(lastRightSumList)){
				for(UserAnswerSum sum:lastRightSumList){
					lastRightSumMap.put(sum.getId()+"-"+sum.getQuestionId(),sum);
				}
			}
			userAnswerSumList.stream().forEach(answerSum -> {
				UserAnswerSumDTO result = new UserAnswerSumDTO();
				//标记题目是否为最近一次作答且正确
				if(lastRightSumMap.containsKey(answerSum.getId()+"-"+answerSum.getQuestionId())){
					result.setIsLastRight(1);
				}
				BeanUtils.copyProperties(answerSum, result);
				resultList.add(result);
			});
		}
		return resultList;
	}

	@Override
	public List<UserAnswerSumDTO> findAlSubmitQuestionListByPaperAndHomework(UserAnswerSumDTO userAnswerSumDTO) throws DataAccessException {
		List<UserAnswerSumDTO> resultList = new ArrayList<>();
		Long uid = userAnswerSumDTO.getUid();
		Long goodsId = userAnswerSumDTO.getGoodsId();
		Long productId = userAnswerSumDTO.getProductId();
		List<Long> questionIdList = userAnswerSumDTO.getQuestionIdList();
		if (uid == null) {
			return resultList;
		}
		if(CollectionUtils.isNotEmpty(questionIdList)){
			return findAlSubmitQuestionListByQuestionIdList(uid, questionIdList);
		}else{
			return findAlSubmitQuestionListByGoodsId(uid, goodsId, productId);
		}
	}

	private List<UserAnswerSumDTO> findAlSubmitQuestionListByQuestionIdList(Long uid, List<Long> questionIdList) throws DataAccessException {
		List<UserAnswerSumDTO> resultList = new ArrayList<>();
		Map param = new HashMap();
		param.put("uid",uid);
		param.put("questionIdList",questionIdList);
		//根据课后作业作答ID集合，获取所有已作答的题目列表
		List<UserAnswerSum> userAnswerSumList = dao.findAlSubmitQuestionList(param);
		if (CollectionUtils.isNotEmpty(userAnswerSumList)) {
			//所有已作答的题目ID集合
			List<Long> questionIds = userAnswerSumList.stream().map(UserAnswerSum::getQuestionId).distinct().collect(Collectors.toList());
			//根据已作答题目ID集合，获取最近一次提交作答明细记录列表
			List<UserAnswerSum> lastSumList=this.dao.getLastUserAnswerSumAndDetailByQuestionIds(uid,questionIds);
			//作答正确的明细列表
			List<UserAnswerSum> lastRightSumList=lastSumList.stream(). filter(answerSum -> answerSum.getIsRight() != null && answerSum.getIsRight()==2).collect(Collectors.toList());
			Map<String,UserAnswerSum> lastRightSumMap=new HashMap<>();
			if(CollectionUtils.isNotEmpty(lastRightSumList)){
				for(UserAnswerSum sum:lastRightSumList){
					lastRightSumMap.put(sum.getId()+"-"+sum.getQuestionId(),sum);
				}
			}
			userAnswerSumList.forEach(answerSum -> {
				UserAnswerSumDTO result = new UserAnswerSumDTO();
				//标记题目是否为最近一次作答且正确
				if(lastRightSumMap.containsKey(answerSum.getId()+"-"+answerSum.getQuestionId())){
					result.setIsLastRight(1);
				}
				BeanUtils.copyProperties(answerSum, result);
				resultList.add(result);
			});
		}
		return resultList;
	}
	private List<UserAnswerSumDTO> findAlSubmitQuestionListByGoodsId(Long uid, Long goodsId, Long productId)throws DataAccessException {
		List<UserAnswerSumDTO> resultList = new ArrayList<>();

		List<Long> userHomeworkIds = Lists.newArrayList();
		List<Long> userAnswerIds = Lists.newArrayList();

		UserHomeWorkAnswer item = new UserHomeWorkAnswer();
		item.setUid(uid);
		item.setGoodsId(goodsId);
		item.setProductId(productId);
		List<UserHomeWorkAnswer> userHomeWorkAnswerList = userHomeWorkAnswerDao.findList(item);
		if (CollectionUtils.isNotEmpty(userHomeWorkAnswerList)) {
			userHomeworkIds.addAll(userHomeWorkAnswerList.stream().map(UserHomeWorkAnswer::getId).toList());
		}

		UserAnswer userAnswerParam = new UserAnswer();
		userAnswerParam.setUid(uid);
		userAnswerParam.setGoodsId(goodsId);
		userAnswerParam.setProductId(productId);
		List<UserAnswer> userAnswerList = userAnswerDao.findList(userAnswerParam);
		if (CollectionUtils.isNotEmpty(userAnswerList)) {
			userAnswerIds.addAll(userAnswerList.stream().map(UserAnswer::getId).toList());
		}

		if(CollectionUtils.isEmpty(userHomeworkIds) && CollectionUtils.isEmpty(userAnswerIds)){
			return resultList;
		}

		Map param = new HashMap();
		param.put("uid",uid);
		param.put("userHomeworkIds",userHomeworkIds);
		param.put("userAnswerIds",userAnswerIds);
		//根据课后作业作答ID集合，获取所有已作答的题目列表
		List<UserAnswerSum> userAnswerSumList = dao.findAlSubmitQuestionListByPaperOrHomework(param);
		if (CollectionUtils.isNotEmpty(userAnswerSumList)) {
			//所有已作答的题目ID集合
			List<Long> questionIds = userAnswerSumList.stream().map(UserAnswerSum::getQuestionId).collect(Collectors.toList());
			//根据已作答题目ID集合，获取最近一次提交作答明细记录列表
			List<UserAnswerSum> lastSumList=this.dao.getLastUserAnswerSumAndDetailByQuestionIdsV2(uid,questionIds);
			//作答正确的明细列表
			List<UserAnswerSum> lastRightSumList=lastSumList.stream(). filter(answerSum -> answerSum.getIsRight() != null && answerSum.getIsRight()==2).collect(Collectors.toList());
			Map<String,UserAnswerSum> lastRightSumMap=new HashMap<>();
			if(CollectionUtils.isNotEmpty(lastRightSumList)){
				for(UserAnswerSum sum:lastRightSumList){
					lastRightSumMap.put(sum.getId()+"-"+sum.getQuestionId(),sum);
				}
			}
			userAnswerSumList.forEach(answerSum -> {
				UserAnswerSumDTO result = new UserAnswerSumDTO();
				//标记题目是否为最近一次作答且正确
				if(lastRightSumMap.containsKey(answerSum.getId()+"-"+answerSum.getQuestionId())){
					result.setIsLastRight(1);
				}
				BeanUtils.copyProperties(answerSum, result);
				resultList.add(result);
			});
		}
		return resultList;
	}

	@Override
	public List<UserAnswerErrorQuestionVo> findErrorPaperAnswerSumDetailGroupByAnswerId(Long uid, List<Long> answerIdIdList) throws DataAccessException {
		return dao.findErrorPaperAnswerSumDetailGroupByAnswerId(uid,answerIdIdList);
	}

	@Override
	public List<UserAnswerErrorQuestionVo> findErrorHomeworkAnswerSumDetailGroupByAnswerId(Long uid, List<Long> answerIdIdList) throws DataAccessException {
		return dao.findErrorHomeworkAnswerSumDetailGroupByAnswerId(uid,answerIdIdList);
	}

	//根据批阅结果校准题目正确性和得分，前端接口，参数uid，作答id
	@Override
	public void correctSubjectiveResult(Long uid, Long userAnswerId, Integer questionSource) {
		if (uid == null || userAnswerId == null || questionSource == null) {
			return;
		}
		if (questionSource == 1) {
			Map<String, Object> qryParam = new HashMap<String, Object>();
			qryParam.put("id", userAnswerId);
			qryParam.put("uid", uid);
			UserAnswer userAnswer = null;
			try {
				userAnswer = userAnswerDao.getShardingById(qryParam);
			} catch (DataAccessException e) {
				logger.error("get UserAnswer error", e);
			}
			if (userAnswer == null || userAnswer.getPaperId() == null) {
				logger.error("get UserAnswer error, userAnswer is null");
				return;
			}

            List<UserAnswerSum> userAnswerSums = null;
            try {
                userAnswerSums = dao.findAnswerSum(uid, userAnswerId, null);
            } catch (DataAccessException e) {
                logger.error("findAnswerSum error", e);
            }
            if (CollectionUtils.isEmpty(userAnswerSums)) {
				return;
			}
			// 获取答题详情
			for (UserAnswerSum userAnswerSum : userAnswerSums) {
				UserAnswerDetail detail = new UserAnswerDetail();
				detail.setSumId(userAnswerSum.getId());
				detail.setUid(uid);
                List<UserAnswerDetail> details = null;
                try {
                    details = userAnswerDetailDao.findAllList(detail);
                } catch (DataAccessException e) {
                    logger.error("findAllList UserAnswerDetail error", e);
                }
                userAnswerSum.setAnswerDetail(details);
			}
			// 获取批阅信息
			List<UserAnswerComment> comments = userAnswerCommentRepository.selectByAnswerId(uid, userAnswerId);
			if (CollectionUtils.isEmpty(comments)) {
				return;
			}
//			setPicInfo(comments);
			correctPaperSubjectiveQuestionScore(comments, userAnswerSums, userAnswer.getPaperId());
			Double totalScore = 0d;
			//遍历userAnswerSum，更新数据库
			for (UserAnswerSum userAnswerSum : userAnswerSums) {
				//遍历detail
				for (UserAnswerDetail detail : userAnswerSum.getAnswerDetail()) {
					//更新到数据库，只更新isRight和score
					UserAnswerDetail updateDetail = new UserAnswerDetail();
					updateDetail.setId(detail.getId());
					updateDetail.setUid(uid);
					updateDetail.setIsRight(detail.getIsRight());
					updateDetail.setScore(detail.getScore());
					try {
						userAnswerDetailDao.updateSharding(updateDetail);
					} catch (DataAccessException e) {
						logger.error("update UserAnswerDetail error", e);
					}
				}
				//更新userAnswerSum的isRight和score
				UserAnswerSum updateUserAnswerSum = new UserAnswerSum();
				updateUserAnswerSum.setId(userAnswerSum.getId());
				updateUserAnswerSum.setUid(uid);
				updateUserAnswerSum.setScore(userAnswerSum.getScore());
				totalScore += userAnswerSum.getScore();
				try {
					dao.updateSharding(updateUserAnswerSum);
				} catch (DataAccessException e) {
					logger.error("update UserAnswerSum error", e);
				}
			}
			//更新userAnswer的score
			UserAnswer updateUserAnswer = new UserAnswer();
			updateUserAnswer.setId(userAnswerId);
			updateUserAnswer.setUid(uid);
			updateUserAnswer.setScore(totalScore);
			updateUserAnswer.setState(3);
			updateUserAnswer.setCommentTime(new Date());
			try {
				userAnswerDao.updateSharding(updateUserAnswer);
				answerCompatableRedisClusterClient.del(RedisConsts.getUserPaperAnswerKey(userAnswer.getUid(), userAnswer.getId()));
			} catch (DataAccessException e) {
				logger.error("update UserAnswer error", e);
			}
		} else if (questionSource == 0) {//作业
			Map<String, Object> qryParam = new HashMap<String, Object>();
			qryParam.put("id", userAnswerId);
			qryParam.put("uid", uid);
			UserHomeWorkAnswer userHomeWorkAnswer = null;
			try {
				userHomeWorkAnswer = userHomeWorkAnswerDao.getShardingById(qryParam);
			} catch (DataAccessException e) {
				logger.error("get UserHomeWorkAnswer error", e);
			}
			if (userHomeWorkAnswer == null || userHomeWorkAnswer.getHomeworkId() == null) {
				logger.error("get UserHomeWorkAnswer error, userHomeWorkAnswer is null");
				return;
			}
			List<UserAnswerSum> userAnswerSums = null;
			try {
				userAnswerSums = dao.findAnswerSumAnswerOrHomework(uid, userAnswerId, null);
			} catch (DataAccessException e) {
				logger.error("findAnswerSum error", e);
			}
			if (CollectionUtils.isEmpty(userAnswerSums)) {
				return;
			}
			// 获取答题详情
			for (UserAnswerSum userAnswerSum : userAnswerSums) {
				UserAnswerDetail detail = new UserAnswerDetail();
				detail.setSumId(userAnswerSum.getId());
				detail.setUid(uid);
				List<UserAnswerDetail> details = null;
				try {
					details = userAnswerDetailDao.findAllList(detail);
				} catch (DataAccessException e) {
					logger.error("findAllList UserAnswerDetail error", e);
				}
				userAnswerSum.setAnswerDetail(details);
			}
			// 获取批阅信息
			UserHomeworkCommentListQuery query = new UserHomeworkCommentListQuery();
			query.setUid(uid);
			query.setAnswerId(userAnswerId);
			List<UserHomeworkComment> comments = userHomeworkCommentService.getUserHomeworkCommentList(query);
			if (CollectionUtils.isEmpty(comments)) {
				return;
			}
			//作业没有拍照上传
//			setPicInfo(comments);
			correctHomeworkSubjectiveQuestionScore(comments, userAnswerSums);
			//遍历userAnswerSum，更新数据库
			for (UserAnswerSum userAnswerSum : userAnswerSums) {
				//遍历detail
				for (UserAnswerDetail detail : userAnswerSum.getAnswerDetail()) {
					//更新到数据库，只更新isRight和score
					UserAnswerDetail updateDetail = new UserAnswerDetail();
					updateDetail.setId(detail.getId());
					updateDetail.setUid(uid);
					updateDetail.setIsRight(detail.getIsRight());
					updateDetail.setScore(detail.getScore());
					try {
						userAnswerDetailDao.updateSharding(updateDetail);
					} catch (DataAccessException e) {
						logger.error("update UserAnswerDetail error", e);
					}
				}
				//更新userAnswerSum的isRight和score
				UserAnswerSum updateUserAnswerSum = new UserAnswerSum();
				updateUserAnswerSum.setId(userAnswerSum.getId());
				updateUserAnswerSum.setUid(uid);
				updateUserAnswerSum.setScore(userAnswerSum.getScore());
				try {
					dao.updateSharding(updateUserAnswerSum);
				} catch (DataAccessException e) {
					logger.error("update UserAnswerSum error", e);
				}
			}
			//更新userHomeWorkAnswer的score
			UserHomeWorkAnswer updateUserHomeWorkAnswer = new UserHomeWorkAnswer();
			updateUserHomeWorkAnswer.setId(userAnswerId);
			updateUserHomeWorkAnswer.setUid(uid);
			//作业没有分数，这里不变
//			updateUserHomeWorkAnswer.setScore(userHomeWorkAnswer.getScore());
			updateUserHomeWorkAnswer.setState(3);
			updateUserHomeWorkAnswer.setCommentTime(new Date());
			try {
				userHomeWorkAnswerDao.updateSharding(updateUserHomeWorkAnswer);
				answerCompatableRedisClusterClient.del(RedisConsts.getUserHomeWorkAnswerKey(userHomeWorkAnswer.getUid(), userHomeWorkAnswer.getId()));
			} catch (DataAccessException e) {
				logger.error("update UserHomeWorkAnswer error", e);
			}
		}
	}

	//如果用户提交的是图片，需要获取图片的宽和高
	private void setPicInfo(List<UserAnswerComment> comments) {
		if (CollectionUtils.isEmpty(comments)) {
			return;
		}
		for (UserAnswerComment comment : comments) {
			if (comment.getComment() == null || !Objects.equals(comment.getCorrectMethod() , 3)) {
				continue;
			}
			if (StringUtils.isBlank(comment.getComment()) || !comment.getComment().startsWith("http://") && !comment.getComment().startsWith("https://")) {
				continue;
			}
			String content = OssUtil.getContentDataFromOss(comment.getComment());
			List<PhotoCommentContent> photoCommentContents = JSONUtils.parseArray(content, PhotoCommentContent.class);
			if (CollectionUtils.isEmpty(photoCommentContents)) {
				continue;
			}
			for (PhotoCommentContent photoCommentContent : photoCommentContents) {
				if (photoCommentContent.getImgUrl() == null) {
					continue;
				}
				String[] picInfo = getPicInfo(photoCommentContent.getImgUrl());
				if (picInfo == null || picInfo.length != 2) {
					continue;
				}
				photoCommentContent.setImgWidth(Integer.valueOf(picInfo[0]));
				photoCommentContent.setImgHeight(Integer.valueOf(picInfo[1]));
			}
			//更新
			try {
				UserAnswerCommentPO po = new UserAnswerCommentPO();
				po.setId(comment.getId());
				String url = OssUtil.uploadJsFileByOss(JSONUtils.toJsonString(photoCommentContents), OssUtil.getFileName(comment.getComment()));
				po.setComment(url);
				userAnswerCommentRepository.updateById(po);
			} catch (Exception e) {
				logger.error("update UserAnswerComment error", e);
			}
		}
	}

	//根据url获取图片的宽和高
	private String[] getPicInfo(String imageUrl) {
		if (StringUtils.isBlank(imageUrl)) {
			return null;
		}
		String[] result = new String[2];
		try {
			URL url = new URL(imageUrl);
			BufferedImage image = ImageIO.read(url);

			if (image != null) {
				int width = image.getWidth();
				int height = image.getHeight();
				result[0] = String.valueOf(width);
				result[1] = String.valueOf(height);
			}
		} catch (Exception e) {
			logger.error("getPicInfo error", e);
		}
		return result;
	}
}
