package cn.huanju.edu100.study.model.paper;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hqwx.study.dto.UserAnswerComment;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/18 11:34
 * @description
 */
@Data
@Accessors(chain = true)
@TableName(value = "user_answer_comment")
public class UserAnswerCommentPO extends UserAnswerComment {
    @TableId(type= IdType.AUTO)
    private Long id;
}
