package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorCategoryDao;
import cn.huanju.edu100.study.dao.tutor.TutorStudentCategoryDao;
import cn.huanju.edu100.study.model.tutor.TutorCategory;
import cn.huanju.edu100.study.model.tutor.TutorStudentCategory;
import cn.huanju.edu100.study.service.tutor.TutorCategoryService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 个性化考试Service
 * <AUTHOR>
 * @version 2016-01-12
 */
@Service
public class TutorCategoryServiceImpl extends BaseServiceImpl<TutorCategoryDao, TutorCategory> implements TutorCategoryService {

    @Autowired
    private TutorStudentCategoryDao tutorStudentCategoryDao;

    @Autowired
    private TutorCategoryDao tutorCategoryDao;

    @Override
    public List<TutorCategory> getCategoryByUid(Long uid, Long goodsId) throws DataAccessException {

        if (uid == null) {
            return null;
        }

        TutorStudentCategory tutorStudentCategory = new TutorStudentCategory();
        tutorStudentCategory.setUid(uid);
        tutorStudentCategory.setGoodsId(goodsId);
        List<TutorStudentCategory> tutorStudentCategoryList = tutorStudentCategoryDao.findList(tutorStudentCategory);
        if (CollectionUtils.isEmpty(tutorStudentCategoryList)) {
            return null;
        }

        List<Long> categoryIdList = new ArrayList<Long>();
        Map<Long, TutorStudentCategory> tMap = new HashMap<Long, TutorStudentCategory>();
        for (TutorStudentCategory caStudentCategory : tutorStudentCategoryList) {
            if (caStudentCategory.getEndTime() != null && caStudentCategory.getEndTime().after(new Date())) {
                categoryIdList.add(caStudentCategory.getSecondCategory());
                if (tMap.get(caStudentCategory.getSecondCategory()) == null) {
                    tMap.put(caStudentCategory.getSecondCategory(), caStudentCategory);
                }else {
                    TutorStudentCategory tCategory = tMap.get(caStudentCategory.getSecondCategory());
                    if (caStudentCategory.getEndTime().after(tCategory.getEndTime())) {
                        tCategory.setEndTime(caStudentCategory.getEndTime());
                    }
                    if (caStudentCategory.getStartTime().before(tCategory.getStartTime())) {
                        tCategory.setStartTime(caStudentCategory.getStartTime());
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(categoryIdList)) {
            return null;
        }

        List<TutorCategory> categoryList = tutorCategoryDao.getCategoryByIdList(categoryIdList);
        if (CollectionUtils.isEmpty(categoryList)) {
            return null;
        }

        List<TutorCategory> currentCategories = new ArrayList<TutorCategory>();
        List<Long> curIdList = new ArrayList<Long>();
        for (TutorCategory tutorCategory : categoryList) {
            if (curIdList.contains(tutorCategory.getCategoryId())
                    || tutorCategory.getEndTime().before(new Date())) {
                continue;
            }
            TutorStudentCategory tutorStudentCategory2 = tMap.get(tutorCategory.getCategoryId());
            if (tutorStudentCategory2 != null) {
                tutorCategory.setInvalidStartTime(tutorStudentCategory2.getStartTime());
                tutorCategory.setInvalidEndTime(tutorStudentCategory2.getEndTime());
                tutorCategory.setGoodsId(tutorStudentCategory2.getGoodsId());
            }
            currentCategories.add(tutorCategory);
            curIdList.add(tutorCategory.getCategoryId());
        }
        return currentCategories;
    }

}
