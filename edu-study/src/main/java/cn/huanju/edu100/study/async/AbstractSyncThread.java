package cn.huanju.edu100.study.async;

import java.util.Collection;

public abstract class AbstractSyncThread<T> implements Runnable {
	private final Collection<T> list;

	public AbstractSyncThread(Collection<T> list) {
		this.list = list;
	}

	protected Collection<T> getList() {
		return list;
	}

	protected abstract void process(Collection<T> list);

	@Override
	public void run() {
		process(list);
	}
}
