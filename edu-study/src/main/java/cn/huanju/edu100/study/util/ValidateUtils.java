package cn.huanju.edu100.study.util;

import java.util.List;
import java.util.Map;

/**验证工具类*/
public class ValidateUtils {
	/**判断Object是否为空*/
	public static boolean isEmpty(Object obj){
		if(obj==null)	return true;
		
		return false;
	}
	/**判断Map是否为空*/
	public static boolean isEmpty(Map map){
		if(map==null || map.isEmpty())	return true;
		
		return false;
	}
	/**判断List是否为空*/
	public static boolean isEmpty(List list){
		if(list==null || list.isEmpty())	return true;
		
		return false;
	}
	/**判断Long[]是否为空*/
	public static boolean isEmpty(long[] array){
		if(array==null || array.length==0)	return true;
		
		return false;
	}
	/**判断Long是否有值，即不为null,且值大于0*/
	public static boolean isEmpty(Long value){
		if(value==null || value<=0)	return true;
		
		return false;
	}
	
	/**判断String是否有值，即不为null,且不是空字符串*/
	public static boolean isEmpty(String str){
		if(null==str || 0==str.trim().length()) return true;
		
		return false;
	}
	
}
