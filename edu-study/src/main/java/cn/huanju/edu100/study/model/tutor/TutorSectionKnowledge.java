package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 章节知识点Entity
 * <AUTHOR>
 * @version 2017-12-28
 */
public class TutorSectionKnowledge extends DataEntity<TutorSectionKnowledge> {
	
	private static final long serialVersionUID = 1L;
	private Long weikeId;		// 微课id
	private Long chapterId;		// 章id
	private Long sectionId;		// 节id
	private Long knowledgeId;		// knowledge_id
	private String ip;		// ip
	
	public TutorSectionKnowledge() {
		super();
	}

	public TutorSectionKnowledge(Long id){
		super(id);
	}

	public Long getWeikeId() {
		return weikeId;
	}

	public void setWeikeId(Long weikeId) {
		this.weikeId = weikeId;
	}
	
	public Long getChapterId() {
		return chapterId;
	}

	public void setChapterId(Long chapterId) {
		this.chapterId = chapterId;
	}
	
	public Long getSectionId() {
		return sectionId;
	}

	public void setSectionId(Long sectionId) {
		this.sectionId = sectionId;
	}
	
	public Long getKnowledgeId() {
		return knowledgeId;
	}

	public void setKnowledgeId(Long knowledgeId) {
		this.knowledgeId = knowledgeId;
	}
	
	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}
	
}