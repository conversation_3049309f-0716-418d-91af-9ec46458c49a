/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorSectionTaskDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.study.util.CountUtils;
import cn.huanju.edu100.util.IdUtils;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 章节任务表DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public class TutorSectionTaskIbatisImpl extends CrudIbatisImpl2<TutorSectionTask> implements
		TutorSectionTaskDao {

	public TutorSectionTaskIbatisImpl() {
		super("TutorSectionTask");
	}

	@Override
	public List<CountModel> listTaskNum(List<Long> sectionIdList, Integer type) throws DataAccessException {
		if (CollectionUtils.isEmpty(sectionIdList)) {
			logger.error("illegal param, sectionIdList is empty");
			throw new DataAccessException("illegal param, sectionIdList is empty");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("sectionIdList", sectionIdList);
			param.put("type", type);
			List<CountModel>  resutlLit = sqlMap.queryForList(namespace.concat(".listTaskNum"), param);
			return resutlLit;
		} catch (SQLException e) {
			logger.error("listTaskNum {} SQLException. sectionIdList:{}, type:{}", namespace, sectionIdList, type, e);
			throw new DataAccessException("listTaskNum SQLException error" + e.getMessage());
		}
	}

	@Override
	public Map<Long, Integer> listWKTaskNum(List<Long> weikeIdList, Integer type) throws DataAccessException {
		if (CollectionUtils.isEmpty(weikeIdList)) {
			logger.error("illegal param, weikeIdList is empty");
			throw new DataAccessException("illegal param, weikeIdList is empty");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("weikeIdList", weikeIdList);
			param.put("type", type);
			List<CountModel>  resutlLit = sqlMap.queryForList(namespace.concat(".listWKTaskNum"), param);
			return CountUtils.getCountMap(resutlLit);
		} catch (SQLException e) {
			logger.error("listWKTaskNum {} SQLException. sectionIdList:{}, type:{}", namespace, weikeIdList, type, e);
			throw new DataAccessException("listWKTaskNum SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<TutorSectionTask> findListByParam(TutorSectionTask search) throws DataAccessException {
		if (null == search || (!IdUtils.isValid(search.getSectionId()) && CollectionUtils.isEmpty(search.getIdList()))) {
			logger.error("illegal param, sectionId or idList is null");
			throw new DataAccessException( "illegal param, sectionId or idList is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("type", search.getType());
			param.put("idList", search.getIdList());
			param.put("sectionId", search.getSectionId());
			List<TutorSectionTask>  resutlLit = sqlMap.queryForList(namespace.concat(".findListByParam"), param);
			return resutlLit;
		} catch (SQLException e) {
			logger.error("findListByParam {} SQLException.tutorSectionTask:{}", namespace, search, e);
			throw new DataAccessException("findListByParam SQLException error" + e.getMessage());
		}


		//return null;
	}
}
