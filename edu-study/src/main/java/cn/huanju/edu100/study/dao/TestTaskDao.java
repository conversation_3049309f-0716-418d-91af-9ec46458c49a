/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.TestTask;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 测评任务学习DAO接口
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface TestTaskDao extends CrudDao<TestTask> {
	/**根据任务ID集查找到对应的Evaluatetask结果集*/
	public List<TestTask> findListByTaskIdList(List<Long> taskIdList) throws DataAccessException;
}
