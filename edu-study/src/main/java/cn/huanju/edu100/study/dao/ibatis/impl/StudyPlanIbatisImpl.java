/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.StudyPlanDao;
import cn.huanju.edu100.study.model.PlanPhase;
import cn.huanju.edu100.study.model.StudyPlan;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 学习计划DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudyPlanIbatisImpl extends CrudIbatisImpl2<StudyPlan> implements StudyPlanDao {

    public StudyPlanIbatisImpl() {
        super("StudyPlan");
    }

    @Override
    public Collection<StudyPlan> qryStudyPlansByGid(Long gid) throws DataAccessException,BusinessException {
        if (gid == null) {
            logger.error("list {} error, parameter gid is null,gid:{}", namespace, gid);
            throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,"list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("gid", gid);
            return (Collection<StudyPlan>) sqlMap.queryForList("StudyPlan.qryStudyPlansByGid", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.gid:{}", namespace, gid, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public Collection<PlanPhase> queryPlanPhasesByPids(String pids) throws DataAccessException,BusinessException {
        if (pids == null) {
            logger.error("list {} error, parameter pids is null,gid:{}", namespace, pids);
            throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,"list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();

            return (Collection<PlanPhase>) sqlMap.queryForList("StudyPlan.qryPlanPhasesByPids", pids);
        } catch (SQLException e) {
            logger.error("list {} SQLException.pids:{}", namespace, pids, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

}
