package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserVirtualHomeworkDao;
import cn.huanju.edu100.study.model.questionBox.VirtualHomework;
import cn.huanju.edu100.study.model.questionBox.VirtualHomeworkDetail;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserVirtualHomeworkIbatisImpl extends CrudIbatisImpl2<VirtualHomework> implements UserVirtualHomeworkDao{

	public UserVirtualHomeworkIbatisImpl() {
		super("VirtualHomework");
	}

	@SuppressWarnings("unchecked")
    @Override
    public VirtualHomework get(long id,long uid) throws DataAccessException {
        if (id <= 0) {
            logger.error("get {} error, parameter id is null,id:{}", namespace, id);
            throw new DataAccessException("get error,id is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            int tbidx = super.getUserProfileTbIdx(uid);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("id", id);
            param.put("tbidx", tbidx);
            return (VirtualHomework) sqlMap.queryForObject(namespace.concat(".get"), param);
        } catch (SQLException e) {
            logger.error("get SQLException.id:{}", id, e);
            throw new DataAccessException("get SQLException error" + e.getMessage());
        }
    }

    @Override
    @Transactional(value ="virtual_transactionShardingManager", readOnly = false)
    public long insert(VirtualHomework entity) throws DataAccessException {
        if (entity == null) {
            logger.error("addVirtualHomework {} error, parameter is null", namespace);
            throw new DataAccessException("addVirtualHomework {} error,param is null", namespace);
        }
        SqlMapClient sqlMap = super.getMaster();
        try {
        	int tbidx = super.getUserProfileTbIdx(entity.getUid().longValue());
        	entity.setTbidx(tbidx);
            Object id = sqlMap.insert(namespace.concat(".insert"), entity);
            if (id != null && id instanceof Long) {
                return (Long) id;
            } else {
                return 0;
            }
        } catch (SQLException e) {
            logger.error("insertVirtualHomework {} SQLException.content:{}", namespace, (new Gson()).toJson(entity), e);
            throw new DataAccessException("add " + namespace + " SQLException fail.");
        }

    }

	@Override
	@Transactional(value ="virtual_transactionShardingManager", readOnly = false)
	public Integer insertDetailBatch(List<VirtualHomeworkDetail> details,Long uid)
			throws DataAccessException {
		if (details==null || details.size() <=0 ) {
            logger.error("{} param error, insertDetailBatch parameter is empty,VirtualHomeworkDetailList:{}", namespace, details);
            throw new DataAccessException("insertDetailBatch param error,VirtualHomeworkDetailList is empty");
        }
    	try{
    		SqlMapClient sqlMap = super.getMaster();
    		int tbidx = super.getUserProfileTbIdx(uid);
    		for (VirtualHomeworkDetail virtualHomeworkDetail : details) {
    			virtualHomeworkDetail.setTbidx(tbidx);
			}
    		HashMap<String, Object> param = new HashMap<String, Object>();
    		param.put("tbidx", tbidx);
    		param.put("list", details);
    		return sqlMap.update(namespace.concat(".insertDetailBatchBatch"), param);

    	}catch(SQLException e){
    		logger.error("insertDetailBatch {} SQLException.content:{}", namespace, (new Gson()).toJson(details), e);
            throw new DataAccessException("insertDetailBatch SQLException fail.exception:"+e.getMessage());
    	}catch(Exception e){
    		throw new DataAccessException(e);
    	}
	}

	@Override
	public List<VirtualHomeworkDetail> getHomeWorkDetails(Long homeworkId,
			Long uid,Integer elementType) throws DataAccessException {
		if (homeworkId == null || uid == null || homeworkId <= 0 || uid <= 0 ) {
            logger.error("getHomeWorkDetails error, parameter homeworkId:{}，uid:{}, elementType:{}", homeworkId,uid,elementType);
            throw new DataAccessException("getHomeWorkDetails error,homeworkId or uid or elementType is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            int tbidx = super.getUserProfileTbIdx(uid);
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("homeworkId", homeworkId);
            param.put("uid", uid);
            param.put("tbidx", tbidx);
            if (elementType != null && elementType != -1) {
            	param.put("elementType", elementType);
			}
            return sqlMap.queryForList(namespace.concat(".getHomeWorkDetails"), param);
        } catch (SQLException e) {
        	logger.error("getHomeWorkDetails error, parameter homeworkId:{}，uid:{}, elementType:{}", homeworkId,uid,elementType);
            throw new DataAccessException("getHomeWorkDetails SQLException error" + e.getMessage());
        }
	}

    @Override
    public List<VirtualHomework> findList(Map<String, Object> param) throws DataAccessException {
        if (param == null || param.get("uid") == null ) {
            logger.error("findListForceIndex error, parameter or uid is null, param:{}", GsonUtil.toJson(param));
            throw new DataAccessException("findListForceIndex error,parameter or uid is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Long uid = (Long)param.get("uid");
            int tbidx = super.getUserProfileTbIdx(uid);
            param.put("tbidx", tbidx);
            return sqlMap.queryForList(namespace.concat(".findListForceIndex"), param);
        } catch (SQLException e) {
            logger.error("findListForceIndex error, parameter param:{}", GsonUtil.toJson(param), e);
            throw new DataAccessException("findListForceIndex SQLException error" + e.getMessage());
        }
    }

    @Override
    public List<VirtualHomework> findByParam(Map<String, Object> param) throws DataAccessException {
        if (param == null || (param.get("tbidx") == null && param.get("uid") == null)) {
            logger.error("findByParam error, parameter or tbidx and uid is null, param:{}", GsonUtil.toJson(param));
            throw new DataAccessException("findByParam error,parameter or tbidx and uid is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            if (param.get("tbidx") == null) {
                Long uid = (Long) param.get("uid");
                int tbidx = super.getUserProfileTbIdx(uid);
                param.put("tbidx", tbidx);
            }
            return sqlMap.queryForList(namespace.concat(".findList"), param);
        } catch (SQLException e) {
            logger.error("findByParam error, parameter param:{}", GsonUtil.toJson(param), e);
            throw new DataAccessException("findByParam SQLException error" + e.getMessage());
        }
    }

    @Override
    public boolean deleteDetailByHomeworkId(Long homeWorkId, Long uid) throws DataAccessException {
        if (homeWorkId <= 0L || uid <= 0L) {
            this.logger.error("deleteDetailByHomeworkId {} error, parameter id is null.", this.namespace);
            return false;
        } else {
            try {
                SqlMapClient sqlMap = super.getMaster();
                Map<String, Object> param = new HashMap();
                int tbidx = super.getUserProfileTbIdx(uid);
                param.put("homeworkId", homeWorkId);
                param.put("tbidx", tbidx);
                int row = sqlMap.delete(this.namespace.concat(".deleteDetailByHomeworkId"), param);
                return row >= 1;
            } catch (SQLException e) {
                this.logger.error("deleteDetailByHomeworkId {} SQLException.content:{}", new Object[]{this.namespace, homeWorkId, e});
                throw new DataAccessException("deleteDetailByHomeworkId " + this.namespace + " SQLException fail.");
            }
        }
    }

    @Override
    public boolean delete(long id, long uid) throws DataAccessException {
        if (id <= 0L || uid <= 0L) {
            this.logger.error("delete {} error, parameter id or uid is null.", this.namespace);
            return false;
        } else {
            try {
                SqlMapClient sqlMap = super.getMaster();
                Map<String, Object> param = new HashMap();
                int tbidx = super.getUserProfileTbIdx(uid);
                param.put("tbidx", tbidx);
                param.put("id", id);
                int row = sqlMap.delete(this.namespace.concat(".delete"), param);
                return row >= 1;
            } catch (SQLException var6) {
                this.logger.error("delete {} SQLException.content:{}", new Object[]{this.namespace, id, var6});
                throw new DataAccessException("delete " + this.namespace + " SQLException fail.");
            }
        }
    }
}
