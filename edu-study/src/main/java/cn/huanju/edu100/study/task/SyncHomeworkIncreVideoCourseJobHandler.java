package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.service.UserAnswerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 增量-课件作业（产品排课）信息同步至作业管理里
 */
@Service
public class SyncHomeworkIncreVideoCourseJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SyncHomeworkIncreVideoCourseJobHandler.class);
    @Autowired
    private UserAnswerService userAnswerService;

    @XxlJob("SyncHomeworkIncreVideoCourseJobHandler")
    public ReturnT<String> execute(String param) throws Exception {
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("SyncHomeworkIncreVideoCourseJobHandler 分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);
        if (zoneIndex == 0) {
            logger.info("------SyncHomeworkIncreVideoCourseJobHandler start------");
            Boolean rs = userAnswerService.syncHomeworkIncreVideoCourse();
            logger.info("------SyncHomeworkIncreVideoCourseJobHandler end------rs:{}", rs);
        }
        return ReturnT.SUCCESS;
    }

}
