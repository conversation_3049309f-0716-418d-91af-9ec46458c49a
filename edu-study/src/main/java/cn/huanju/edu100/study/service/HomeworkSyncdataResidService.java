package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.model.HomeworkSyncdataResid;

import java.util.List;

public interface HomeworkSyncdataResidService {

    Boolean saveHomeworkSyncdataResid(HomeworkSyncdataResid homeworkSyncdataResid) throws DataAccessException;

    List<HomeworkSyncdataResid> findListByType(int videoCourse) throws DataAccessException;

    List<HomeworkSyncdataResid> getDifferenceResid(Integer productType) throws DataAccessException;

}
