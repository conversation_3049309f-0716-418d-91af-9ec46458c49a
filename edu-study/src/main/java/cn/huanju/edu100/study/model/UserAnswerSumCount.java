package cn.huanju.edu100.study.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName(value = "user_answer_sum_count",autoResultMap = true)
public class UserAnswerSumCount {
    @TableId(type= IdType.AUTO)
    private Long id;
    private Long uid;
    private Long lessonId;
    private Long userHomeworkAnswerId;
    private Long homeworkId;
}
