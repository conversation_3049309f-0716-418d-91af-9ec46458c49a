package cn.huanju.edu100.study.model.questionBox;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 题库盒子物品Entity
 * <AUTHOR>
 * @version 2015-08-04
 */
public class QuestionboxItems extends DataEntity<QuestionboxItems> {
	
	private static final long serialVersionUID = 1L;
	private Long boxId;		// box_id
	private Long itemId;		// item_id
	private Integer itemType;		// item_type
	private Integer isLock;		// is_lock
	private Long lockId;		// lock_id
	private Integer state;		// state
	
	
	public QuestionboxItems() {
		super();
	}

	public QuestionboxItems(Long id){
		super(id);
	}

	public Long getBoxId() {
		return boxId;
	}

	public void setBoxId(Long boxId) {
		this.boxId = boxId;
	}

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public Integer getItemType() {
		return itemType;
	}

	public void setItemType(Integer itemType) {
		this.itemType = itemType;
	}

	public Integer getIsLock() {
		return isLock;
	}

	public void setIsLock(Integer isLock) {
		this.isLock = isLock;
	}

	public Long getLockId() {
		return lockId;
	}

	public void setLockId(Long lockId) {
		this.lockId = lockId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

}