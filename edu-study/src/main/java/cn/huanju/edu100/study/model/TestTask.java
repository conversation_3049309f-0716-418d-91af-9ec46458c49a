/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 测评任务学习Entity
 * <AUTHOR>
 * @version 2015-05-15
 */
public class TestTask extends DataEntity<TestTask> {
	
	private static final long serialVersionUID = 1L;
	private Long taskId;		// task_id
	private Long qId;			// 题目ID
	private String title;		// title
	private String purpose;		// purpose
	
	public TestTask() {
		super();
	}

	public TestTask(Long id){
		super(id);
	}

	public Long getTaskId() {
		return taskId;
	}
	
	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}
	
	public Long getqId() {
		return qId;
	}

	public void setqId(Long qId) {
		this.qId = qId;
	}

	public String getTitle() {
		return title;
	}
		public void setTitle(String title) {
		this.title = title;
	}
	public String getPurpose() {
		return purpose;
	}
		public void setPurpose(String purpose) {
		this.purpose = purpose;
	}
}