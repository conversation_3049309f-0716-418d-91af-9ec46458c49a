package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.StudyTaskPushResDao;
import cn.huanju.edu100.study.model.StudyTaskPushRes;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.util.EduStringUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StudyTaskPushResIbatisImpl extends
		CrudIbatisImpl2<StudyTaskPushRes> implements StudyTaskPushResDao {

	public StudyTaskPushResIbatisImpl() {
		super("StudyTaskPushRes");
	}

	@Override
	public List<StudyTaskPushRes> findListByTaskIdList(List<Long> taskIdList,
			Long uid) throws DataAccessException {
		if (ValidateUtils.isEmpty(taskIdList) || null == uid) {
			logger.error(
					"findListByTaskIdList {} error, parameter taskIdList or uid is empty,taskIdList:{},uid:{}",
					namespace, taskIdList, uid);
			throw new DataAccessException(
					"findListByTaskIdList error,parameter error");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			String idList = EduStringUtils.getUniqueIds(taskIdList);
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("taskIds", idList);
			param.put("uid", uid);
			return (List<StudyTaskPushRes>) sqlMap.queryForList(
					"StudyTaskPushRes.findListByTaskIdList", param);
		} catch (SQLException e) {
			logger.error("findListByTaskIdList {} SQLException taskId:{}",
					namespace, taskIdList, e);
			throw new DataAccessException("get SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public void insertPushResList(List<StudyTaskPushRes> studyTaskPushResList)
			throws DataAccessException {
		if (ValidateUtils.isEmpty(studyTaskPushResList)) {
			logger.error(
					"insertList {} error, parameter studyTaskPushResList or uid is empty,studyTaskPushResList:{}",
					namespace, studyTaskPushResList);
			throw new DataAccessException(
					"insertPushResList error,parameter error");
		}
		SqlMapClient sqlMap = super.getMaster();
		try {
			sqlMap.insert("StudyTaskPushRes.insertPushResList", studyTaskPushResList);
		} catch (SQLException e) {
			logger.error(
					"insertPushResList {} SQLException studyTaskPushResList:{}",
					namespace, studyTaskPushResList, e);
			throw new DataAccessException("get SQLException error"
					+ e.getMessage());
		}
	}

}
