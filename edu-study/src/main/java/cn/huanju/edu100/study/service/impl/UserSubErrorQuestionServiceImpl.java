package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.UserCorrectQuestionDao;
import cn.huanju.edu100.study.dao.UserSubErrorQuestionDao;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.service.UserSubErrorQuestionService;
import com.hqwx.study.dto.query.UserErrorAndCorrectQuestionQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户分表错题记录Service
 *
 * <AUTHOR>
 * @version 2021-09-08
 */
@Service
public class UserSubErrorQuestionServiceImpl extends BaseServiceImpl<UserSubErrorQuestionDao, UserSubErrorQuestion> implements UserSubErrorQuestionService {

    @Autowired
    private UserCorrectQuestionDao userCorrectQuestionDao;

    @Override
    public void saveSubErrorQuestion(UserSubErrorQuestion userSubErrorQuestion) throws DataAccessException {
        if (userSubErrorQuestion == null || userSubErrorQuestion.getUid() == null || userSubErrorQuestion.getUid() == 0
                || userSubErrorQuestion.getQuestionId() == null || userSubErrorQuestion.getQuestionId() == 0
                || userSubErrorQuestion.getTopicId() == null || userSubErrorQuestion.getTopicId() == 0) {
            throw new DataAccessException("save error uid or question_id not found");
        }
//        UserSubErrorQuestion oldErrorQuestion = dao.get(userSubErrorQuestion);
//        if (oldErrorQuestion != null) {
//            userSubErrorQuestion.setId(oldErrorQuestion.getId());
//            userSubErrorQuestion.setErrorTime(oldErrorQuestion.getErrorTime() + 1);
//        }
        saveSharding(userSubErrorQuestion);
    }

    @Override
    public List<UserErrorHomeworkProdBeanDto> findUserErrorHomeWorkProdBean(Long uid, List<Long> productIds, Long goodsId, Long categoryId) throws DataAccessException {
        if (CollectionUtils.isEmpty(productIds) || uid == null) {
            logger.info("findUserErrorHomeWorkProdBean illegal param, uid or productIds is empty");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("productIds", productIds);
        params.put("goodsId", goodsId);
        params.put("categoryId", categoryId);
        List<UserErrorHomeworkLessonQuestionBean> lessonBeanList = dao.findUserErrorHomeWorkLessonIds(params);
        if (CollectionUtils.isEmpty(lessonBeanList)) {
            return null;
        }
        Map<String, Set<Long>> lessonQuestionIdMap = new HashMap<>();
        for (UserErrorHomeworkLessonQuestionBean userErrorHomeworkLessonQuestionBean : lessonBeanList) {
            Long productId = userErrorHomeworkLessonQuestionBean.getProductId();
            Long lessonId = userErrorHomeworkLessonQuestionBean.getLessonId();
            Long questionId = userErrorHomeworkLessonQuestionBean.getQuestionId();
            if (productId != null && lessonId != null && questionId != null) {
                String key = productId + "_" + lessonId;
                Set<Long> questionIdList = lessonQuestionIdMap.get(key);
                if (questionIdList == null) {
                    questionIdList = new HashSet<>();
                }
                questionIdList.add(questionId);
                lessonQuestionIdMap.put(key, questionIdList);
            }
        }
        List<UserErrorHomeworkProdBeanDto> prodBeanList = new ArrayList<>();
        Map<Long, Map<Long, Set<Long>>> tmpMap = new HashMap<>();
        for (String key : lessonQuestionIdMap.keySet()) {
            Set<Long> questionIdList = lessonQuestionIdMap.get(key);
            String[] keyArr = key.split("_");
            Long productId = Long.parseLong(keyArr[0]);
            Long lessonId = Long.parseLong(keyArr[1]);
            Map<Long, Set<Long>> longListMap = tmpMap.get(productId);
            if (longListMap == null) {
                longListMap = new HashMap<>();
                longListMap.put(lessonId, questionIdList);
                tmpMap.put(productId, longListMap);
            } else {
                Set<Long> questionIdListTmp = longListMap.get(lessonId);
                if (questionIdListTmp != null && !questionIdListTmp.isEmpty()) {
                    questionIdList.addAll(questionIdListTmp);
                }
                longListMap.put(lessonId, questionIdList);
                tmpMap.put(productId, longListMap);
            }
        }
        for (Long productId : tmpMap.keySet()) {
            Map<Long, Set<Long>> lessonQIdMap = tmpMap.get(productId);
            UserErrorHomeworkProdBeanDto prodBean = new UserErrorHomeworkProdBeanDto();
            prodBean.setProductId(productId);
            Set<Long> lessonIdList = new HashSet<>();
            List<Long> questionIdList = new ArrayList<>();
            for (Long lessonId : lessonQIdMap.keySet()) {
                Set<Long> qIdList = lessonQIdMap.get(lessonId);
                lessonIdList.add(lessonId);
                questionIdList.addAll(new ArrayList<>(qIdList));
            }
            prodBean.setErrorLessonIds(new ArrayList<>(lessonIdList));
            prodBean.setErrorCount(questionIdList.size());
            prodBeanList.add(prodBean);
        }
        return prodBeanList;
    }

    @Override
    public List<UserErrorHomeworkLessonBeanDto> findUserErrorHomeWorkQuestionBean(Long uid, Long productId, List<Long> lessonIds) throws DataAccessException {
        if (CollectionUtils.isEmpty(lessonIds) || uid == null) {
            logger.info("findUserErrorHomeWorkQuestionBean illegal param, uid or lessonIdList is empty");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("lessonIds", lessonIds);
        params.put("productId", productId);
        List<UserErrorHomeworkLessonQuestionBean> lessonBeanList = dao.findUserErrorHomeWorkQuestionIds(params);
        if (CollectionUtils.isEmpty(lessonBeanList)) {
            return null;
        }
        Map<Long, List<UserErrorHomeworkLessonQuestionBean>> errorQuestionBeanMaps =
                lessonBeanList.stream().collect(Collectors.groupingBy(UserErrorHomeworkLessonQuestionBean::getLessonId));
        if (errorQuestionBeanMaps == null || errorQuestionBeanMaps.size() <= 0) {
            return null;
        }
        List<UserErrorHomeworkLessonBeanDto> lessonQuestionBeanList = new ArrayList<>();
        for (Map.Entry<Long, List<UserErrorHomeworkLessonQuestionBean>> entry : errorQuestionBeanMaps.entrySet()) {
            UserErrorHomeworkLessonBeanDto prodLessonBean = new UserErrorHomeworkLessonBeanDto();
            prodLessonBean.setProductId(productId);
            prodLessonBean.setLessonId(entry.getKey());
            List<UserErrorHomeworkLessonQuestionBean> questionBeans = entry.getValue();
            if (CollectionUtils.isNotEmpty(questionBeans)) {
                Set<Long> questionIds = questionBeans.stream().map(UserErrorHomeworkLessonQuestionBean::getQuestionId).collect(Collectors.toSet());
                prodLessonBean.setQuestionIds(new ArrayList<>(questionIds));
                prodLessonBean.setErrorCount((long) questionIds.size());
            }
            lessonQuestionBeanList.add(prodLessonBean);
        }

        return lessonQuestionBeanList;
    }

    @Override
    public List<UserErrorEPaperProdBeanRes> findUserErrorEpaperQuestionBean(Long uid, List<Long> productIds, Long goodsId, Long categoryId) throws DataAccessException {
        if (CollectionUtils.isEmpty(productIds) || uid == null) {
            logger.info("findUserErrorEpaperQuestionBean illegal param, uid or productIds is empty");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("productIds", productIds);
        params.put("goodsId", goodsId);
        params.put("categoryId", categoryId);
        List<UserErrorEpaperQuestion> epaperQuestionIds = dao.findUserErrorEpaperQuestionIds(params);
        if (CollectionUtils.isEmpty(epaperQuestionIds)) {
            return null;
        }
        Map<Long, List<UserErrorEpaperQuestion>> errorQuestionBeanMaps =
                epaperQuestionIds.stream().collect(Collectors.groupingBy(UserErrorEpaperQuestion::getProductId));
        if (errorQuestionBeanMaps == null || errorQuestionBeanMaps.size() <= 0) {
            return null;
        }
        List<UserErrorEPaperProdBeanRes> prodBeanList = new ArrayList<>();
        for (Map.Entry<Long, List<UserErrorEpaperQuestion>> entry : errorQuestionBeanMaps.entrySet()) {
            UserErrorEPaperProdBeanRes prodBean = new UserErrorEPaperProdBeanRes();
            prodBean.setProductId(entry.getKey());
            List<UserErrorEpaperQuestion> questionBeans = entry.getValue();
            if (CollectionUtils.isNotEmpty(questionBeans)) {
                Set<Long> questionIds = questionBeans.stream().map(UserErrorEpaperQuestion::getQuestionId).collect(Collectors.toSet());
                prodBean.setErrorQuestionIds(new ArrayList<>(questionIds));
                prodBean.setErrorCount(questionIds.size());
            }
            prodBeanList.add(prodBean);
        }
        return prodBeanList;
    }

    @Override
    public Long getUserErrorQuestionCountByUidAndCategory(Long uid, Long categoryId, Long goodsId) throws DataAccessException {
        if (categoryId == null || uid == null) {
            logger.info("getUserErrorQuestionCountByUidAndCategory illegal param, uid or categoryId is empty");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("categoryId", categoryId);
        params.put("goodsId", goodsId);
        return dao.getUserErrorQuestionCountByUidAndCategory(params);
    }

    @Override
    public UserSubErrorQuestion getByParam(Long uid, Long goodsId,Long categoryId, Long productId, Long lessonId, Long questionId, Long topicId) throws DataAccessException {
        UserSubErrorQuestion userSubErrorQuestion = new UserSubErrorQuestion();
        userSubErrorQuestion.setUid(uid);
        userSubErrorQuestion.setCategoryId(categoryId);
//        userSubErrorQuestion.setGoodsId(goodsId);
//        userSubErrorQuestion.setProductId(productId);
        userSubErrorQuestion.setQuestionId(questionId);
//        if(IdUtils.isValid(lessonId)){
//            userSubErrorQuestion.setLessonId(lessonId);
//        }
        userSubErrorQuestion.setTopicId(topicId);
        return dao.get(userSubErrorQuestion);
    }

    @Override
    public boolean removeErrorQuestionByCategory(Long uid, Long categoryId, Long goodsId, Long questionId,Integer moreThanTimes,Integer lessThanTimes,String startDate,String endDate, Integer sourceType) throws DataAccessException {
        if (categoryId == null || uid == null) {
            logger.info("removeErrorQuestionByCategory illegal param, uid or categoryId is empty");
            return false;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("categoryId", categoryId);
        params.put("goodsId", goodsId);
        params.put("questionId", questionId);
        params.put("moreThanTimes", moreThanTimes);
        params.put("lessThanTimes", lessThanTimes);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("sourceType", sourceType);

        if (Objects.nonNull(questionId)) { //指定题目删除时，特殊处理
            UserSubErrorQuestion userSubErrorQuestion = new UserSubErrorQuestion();
            userSubErrorQuestion.setUid(uid);
            userSubErrorQuestion.setQuestionId(questionId);
            return dao.removeUserSubErrorQuestion(userSubErrorQuestion);
        }
        if(Objects.nonNull(moreThanTimes) || Objects.nonNull(lessThanTimes)){
            return dao.removeErrorQuestionByCategoryGroupByQuestion(params);
        }
        return dao.removeErrorQuestionByCategory(params);
    }

    @Override
    public boolean removeUserSubErrorQuestion(UserSubErrorQuestion userSubErrorQuestion) throws DataAccessException{
        if(userSubErrorQuestion==null|| userSubErrorQuestion.getUid()==null || userSubErrorQuestion.getQuestionId()==null){
            logger.info("removeUserSubErrorQuestion illegal param, uid or questionId is empty");
            return false;
        }
        return dao.removeUserSubErrorQuestion(userSubErrorQuestion);
    }

    @Override
    public boolean removeUserSubErrorQuestion(UserSubErrorQuestion userSubErrorQuestion, Integer soft) throws DataAccessException{
        if(userSubErrorQuestion==null|| userSubErrorQuestion.getUid()==null || userSubErrorQuestion.getQuestionId()==null){
            logger.info("removeUserSubErrorQuestion illegal param, uid or questionId is empty");
            return false;
        }
        if (soft == null || soft == 0) {
            return dao.removeUserSubErrorQuestion(userSubErrorQuestion);
        } else {
            return dao.removeUserSubErrorQuestionSoft(userSubErrorQuestion);
        }
    }

    @Override
    public List<UserQuestionCountVo> getUserErrorQuestionCountByQtype(Long uid, Long categoryId, Long goodsId) throws DataAccessException {
        if (uid == null) {
            logger.info("getUserErrorQuestionCountByQtype illegal param, uid is empty");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("categoryId", categoryId);
        params.put("goodsId", goodsId);
        return dao.getUserErrorQuestionCountByQtype(params);
    }

    @Override
    public List<UserErrorEpaperQuestion> getUserErrorQuestionListGroupByQtype(Long uid, Long categoryId, Long goodsId) throws DataAccessException {
        if (uid == null) {
            logger.info("getUserErrorQuestionListGroupByQtype illegal param, uid is empty");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("categoryId", categoryId);
        params.put("goodsId", goodsId);
        return dao.getUserErrorQuestionListGroupByQtype(params);
    }


    @Override
    public List<Long> findUserErrorQuestionIdsByCategoryAndGoodsId(Long uid, Long categoryId, Long goodsId,String startDate,String endDate, Integer sourceType) throws DataAccessException {
        if (uid == null) {
            logger.info("findUserErrorQuestionIdsByCategoryAndGoodsId illegal param, uid is empty");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("categoryId", categoryId);
        params.put("goodsId", goodsId);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("sourceType", sourceType);
        return dao.findUserErrorQuestionIdsByCategoryAndGoodsId(params);
    }

    @Override
    public List<Long> findAnswerIdByCategoryAndGoodsId(Long uid, Long categoryId, Long goodsId, String startDate, String endDate, Integer sourceType) throws DataAccessException {
        if (uid == null) {
            logger.info("findAnswerIdByCategoryAndGoodsId illegal param, uid is empty");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("categoryId", categoryId);
        params.put("goodsId", goodsId);
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        params.put("sourceType", sourceType);
        return dao.findAnswerIdByCategoryAndGoodsId(params);
    }

    @Override
    public List<UserSubErrorQuestion> getUserSubErrorQuestionList(Long uid, Long goodsId, Long categoryId, Date startTime, Date endTime, Integer noNeedLimit) throws DataAccessException {
        return dao.getUserSubErrorQuestionList(uid, goodsId, categoryId, startTime, endTime, noNeedLimit);
    }

    @Override
    public Integer countUserSubErrorQuestionList(Long uid, Long goodsId, Long categoryId, Date startTime, Date endTime) throws DataAccessException {
        return dao.countUserSubErrorQuestionList(uid, goodsId, categoryId, startTime, endTime);
    }

    @Override
    public Integer getUserErrorQuestionCount(UserErrorAndCorrectQuestionQuery query)throws DataAccessException {
        return (int)dao.groupByCount(query);
    }

    @Override
    public Page<UserSubErrorQuestion> findGroypByPage(UserErrorAndCorrectQuestionQuery query) throws DataAccessException {
        Page<UserSubErrorQuestion> page = new Page<UserSubErrorQuestion>();
        page.setFrom(query.getFrom());
        page.setPageSize(query.getRows());
        page.setOrderBy(" createDate ");
        List<Long> notInQuestionList = userCorrectQuestionDao.getUserCorrectQuestionIdList(query);
        query.setNotInQuestionList(notInQuestionList);
        List<UserSubErrorQuestion> results = dao.findGroupByList(page, query);

        Page<UserSubErrorQuestion> result = new Page<>();
        result.setList(results);
        result.setCount(dao.groupByCount(query));
        result.setPageSize(page.getPageSize());
        result.setPageNo(page.getFrom() / page.getPageSize() + 1);
        return result;
    }
}
