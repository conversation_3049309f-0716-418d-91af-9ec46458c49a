package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;
import com.hqwx.study.entity.UserAnswerDetail;

import java.util.Date;
import java.util.List;

/**
 * 用户答题Entity
 * 
 * <AUTHOR>
 * @version 2015-05-12
 */
public class UserTaskAnswer extends DataEntity<UserTaskAnswer> {

    private static final long serialVersionUID = 8358643429967580770L;

    private Long groupId; // 学习组id
    private Long taskId; // 任务id
    private Long type; // 任务类型（主动获取，被动推送）
    private Integer studyDuration;// 学习时长

    private Long uid; // uid
    private Long paperId; // paper_id
    private Integer paperType; // 试卷类型:0作业 ，1练习题，2正式考试 ，3模拟考试
    private Double score; // score
    private Long usetime; // usetime
    private Long answerNum; // 当前答题数
    private Date startTime; // 用户开始答卷时间
    private Date endTime; // 用户提交试卷时间
    private String comment; // 老师评语
    private Integer state; // 状态，0未开始 1进行中 2已交卷 3已评卷
    private Integer source; // 记录来源（0：普通购买，1：个性化服务，2：题库试卷 101:微课班试卷）

    private Long parentObjId; // 来源父id(目前段落作业重做需要讲id)
    private Long objId; // 记录来源id
    private Integer objType; // 对于作业来说，objType字段意思如下：（0：讲作业，1：段落作业，2：微课作业，3：个性化作业，4：新录播课作业，5：题库作业）
                             // 对于试卷来说，objType字段意思如下：（0：普通购买试卷记录，1：个性化任务试卷记录，2：题库试卷记录 101：微课班试卷）

    private Long wkSectionId;// 微课节id

    private List<UserAnswerDetail> answerDetail;

    private Integer isSubmit;
    private Long mClassId; // m_class_id

    private String appid;   //app的类型 所属终端，web、PC客户端、环球网校APP、快题库、建造师题库…、快题库小程序
    private String platForm;	// app平台ios android

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Long getUsetime() {
        return usetime;
    }

    public void setUsetime(Long usetime) {
        this.usetime = usetime;
    }

    public Long getAnswerNum() {
        return answerNum;
    }

    public void setAnswerNum(Long answerNum) {
        this.answerNum = answerNum;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public Integer getObjType() {
        return objType;
    }

    public void setObjType(Integer objType) {
        this.objType = objType;
    }

    public List<UserAnswerDetail> getAnswerDetail() {
        return answerDetail;
    }

    public void setAnswerDetail(List<UserAnswerDetail> answerDetail) {
        this.answerDetail = answerDetail;
    }

    public Integer getIsSubmit() {
        return isSubmit;
    }

    public void setIsSubmit(Integer isSubmit) {
        this.isSubmit = isSubmit;
    }

    public Long getmClassId() {
        return mClassId;
    }

    public void setmClassId(Long mClassId) {
        this.mClassId = mClassId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public Integer getStudyDuration() {
        return studyDuration;
    }

    public void setStudyDuration(Integer studyDuration) {
        this.studyDuration = studyDuration;
    }

    public Long getParentObjId() {
        return parentObjId;
    }

    public void setParentObjId(Long parentObjId) {
        this.parentObjId = parentObjId;
    }

    public Long getWkSectionId() {
        return wkSectionId;
    }

    public void setWkSectionId(Long wkSectionId) {
        this.wkSectionId = wkSectionId;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPlatForm() {
        return platForm;
    }

    public void setPlatForm(String platForm) {
        this.platForm = platForm;
    }
}