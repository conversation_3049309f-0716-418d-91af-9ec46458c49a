package cn.huanju.edu100.study.model;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 大类考试科目Entity
 *
 * <AUTHOR>
 * @version 2018-04-13
 */
public class Category extends DataEntity<Category> {

    private static final long serialVersionUID = 1L;
    private Long parentId;        // parent_id
	private String parentIds;        // parent_ids
    private String name;        // name
    private String description;        // description
    private Integer type;        // 公共和专业课
    private String passRatio;        // pass_ratio
    private Integer sort;        // sort
    private String alias;       // 别名
    private String ip;        // ip
    private Integer level;

    private String signUpUrl;	//报名URL
    
    private Integer isDisplay;	//是否显示，0：不显示，1：显示

	public Category() {
        super();
    }

    public Category(Long id) {
        super(id);
    }

    public Category(Long parentId, String parentIds, String name, String description, Integer type, String passRatio, Integer sort, String ip, Integer level) {
        this.parentId = parentId;
        this.parentIds = parentIds;
        this.name = name;
        this.description = description;
        this.type = type;
        this.passRatio = passRatio;
        this.sort = sort;
        this.ip = ip;
        this.level = level;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Integer getIsDisplay() {
		return isDisplay;
	}

	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}

	/**
 	 * @return the parentId
 	 */
 	public Long getParentId() {
 		return parentId;
 	}

 	/**
 	 * @param parentId the parentId to set
 	 */
 	public void setParentId(Long parentId) {
 		this.parentId = parentId;
 	}

   

   

    public String getParentIds() {
        return parentIds;
    }

    public void setParentIds(String parentIds) {
        this.parentIds = parentIds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPassRatio() {
        return passRatio;
    }

    public void setPassRatio(String passRatio) {
        this.passRatio = passRatio;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getSignUpUrl() {
        return signUpUrl;
    }

    public void setSignUpUrl(String signUpUrl) {
        this.signUpUrl = signUpUrl;
    }
}