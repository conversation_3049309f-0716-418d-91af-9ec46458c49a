package cn.huanju.edu100.study.model.al;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;


/**
 * @description
 * <AUTHOR>
 * @version
 * @date 2019-07-25
 */
public class StudyPath extends DataEntity<StudyPath> {
    private static final long serialVersionUID = 1L;
    /**
     * 大类
     */
    private Long firstCategory;

    /**
     * 考试
     */
    private Long secondCategory;

    /**
     * 科目
     */
    private Long categoryId;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 1:基础阶段,2:加强阶段,3:冲刺阶段
     */
    private Integer stage;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 教学方式
     * 1	普通学习
     * 3	章节测验
     * 4	小节测验
     * 5	阶段测验
     * 6	真题速攻
     * 7	模考一
     * 8	模考二
     * 9	模考三
     * 10	模考四
     * 11	模考五
     * 12	考题预测一
     * 13	考题预测二
     * 14	考题预测三
     * 15	考题预测四
     * 16	考题预测五
     * 17	小节总结
     * 18	章节总结
     * 19	阶段总结
     * 20	识图要点
     * 21	学科导学
     * 22	课程引导
     */
    private Integer studyMethod;

    /**
     * 资源类型：1 录播 2 资料 3 试卷
     */
    private Integer resourceType;

    /**
     * 对应资源id
     */
    private Long resourceId;

    /**
     * 反馈配置自定义提示语内容
     */
    private String feedbackContent;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 状态 0禁用,1启用
     */
    private Integer status;

    /**
     * 作业列表
     */
    private List<StudyPathQuestion> questionList;

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public String getFeedbackContent() {
        return feedbackContent;
    }

    public void setFeedbackContent(String feedbackContent) {
        this.feedbackContent = feedbackContent;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public List<StudyPathQuestion> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<StudyPathQuestion> questionList) {
        this.questionList = questionList;
    }

    public Integer getStudyMethod() {
        return this.studyMethod;
    }

    public void setStudyMethod(Integer studyMethod) {
        this.studyMethod = studyMethod;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}