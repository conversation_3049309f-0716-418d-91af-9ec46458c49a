package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.UserQuestionLog;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;
import java.util.List;

/**
 * 用户问题解析反馈日志
 */
public interface UserQuestionLogService
        extends BaseService<UserQuestionLog> {

//	public void updateLogCache(final UserQuestionLog userQuestionLog);
//	public UserQuestionLog getFromCache(final Long uid, final Long questionId, final Long clsId, final Long lessonId, final String strDt)throws DataAccessException;
//	public UserQuestionLog getFromCacheDb(final Long uid, final Long questionId, final Long clsId, final Long lessonId, final String strDt)throws DataAccessException;

//	public Long getUpdateTime(final String field);
//	public Long getLengthFromCache(final Long uid, final Long questionId, final Long clsId, final Long lessonId, final String strDt);
	/**
	 * @param userQuestionLog
	 * @return
	 */
	Long addUserQuestionLog(UserQuestionLog userQuestionLog) throws DataAccessException,BusinessException;

	/**
	 * @param uid
	 * @param questionId
	 * @return
	 */
	Collection<UserQuestionLog> queryUserQuestionLogsByUidQuestionId(Long uid,
            Long questionId) throws DataAccessException,BusinessException;

	/**
	 * 
	 * @param uid
	 * @param questionId
	 * @return
	 * @throws DataAccessException
	 */
	List<UserQuestionLog>  getLastUserQuestionLog(long uid, List<Long> questionIds)
			throws DataAccessException,BusinessException;

	void insertBatch(List<UserQuestionLog> userQuestionLogs) throws DataAccessException;



}