/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.expression;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.expression.ExpressionGroupDao;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.model.expression.ExpressionGroup;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 规则组DAO接口
 * <AUTHOR>
 * @version 2016-05-23
 */
public class ExpressionGroupIbatisImpl extends CrudIbatisImpl2<ExpressionGroup> implements
		ExpressionGroupDao {

	public ExpressionGroupIbatisImpl() {
		super("ExpressionGroup");
	}

    @Override
    public List<ExpressionGroup> findListByParam(List<Long> idList, Integer type) throws DataAccessException {

        if (CollectionUtils.isEmpty(idList)) {
            logger.error("findListByParam {} error, idList is empty,entity", namespace);
            throw new DataAccessException(String.format("findListByParam %s error, idList is empty,entity", namespace));
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = Maps.newHashMap();
            param.put("idList", idList);
            if (null != type) {
                param.put("type", type);
            }
            return sqlMap.queryForList(namespace.concat(".findListByParam"), param);
        } catch (SQLException e) {
            logger.error("findListByParam {} SQLException.idList:{}", namespace, idList, e);
            throw new DataAccessException(String.format(
                    "findListByParam %s SQLException error, idList :%s, exception :%s", namespace, idList,
                    e.getMessage()));
        }
    }

    @Override
    public List<ExpressionGroup> getExpressionGroups(Map<String, Object> params) throws DataAccessException {
        if (params == null) {
            logger.error("getExpressionGroups {} error, params is empty", namespace);
            throw new DataAccessException(String.format("getExpressionGroups %s error is empty", namespace));
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            return sqlMap.queryForList(namespace.concat(".findList"), params);
        } catch (SQLException e) {
            logger.error("getExpressionGroups {} SQLException", namespace, e);
            throw new DataAccessException(String.format(
                    "getExpressionGroups %s SQLException :%s", namespace,
                    e.getMessage()));
        }
    }

}
