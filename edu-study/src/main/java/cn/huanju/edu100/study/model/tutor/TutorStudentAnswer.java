package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 做题记录Entity
 * 
 * <AUTHOR>
 * @version 2016-01-19
 */
public class TutorStudentAnswer extends DataEntity<TutorStudentAnswer> {

    private static final long serialVersionUID = 1L;
    private String classes; // classes
    private Long taskId; // task_id
    private Long groupId; // group_id
    private Long planeId; // plane_id
    private Long phaseId; // phase_id
    private Long unitId; // unitId
    private Long firstCategory;     // 所属大类
    private Long secondCategory;        // 所属考试
    private Long categoryId; // category_id
    private Long uid; // uid
    private Integer type; // type
    private Long studyDuration; // study_duration
    private Integer objType; // obj_type
    private Long objId; // obj_id
    private Long parentObjId; // 父载体id
    private Long answerNum; // answer_num
    private Long wrongNum; // wrong_num
    private Integer source;// 0:任务 1：微课

    private String appid;   //app的类型 所属终端，web、PC客户端、环球网校APP、快题库、建造师题库…、快题库小程序
    private String platForm;	// app平台ios android

    public TutorStudentAnswer() {
        super();
    }

    public TutorStudentAnswer(Long id) {
        super(id);
    }

    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getPlaneId() {
        return planeId;
    }

    public void setPlaneId(Long planeId) {
        this.planeId = planeId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getStudyDuration() {
        return studyDuration;
    }

    public void setStudyDuration(Long studyDuration) {
        this.studyDuration = studyDuration;
    }

    public Integer getObjType() {
        return objType;
    }

    public void setObjType(Integer objType) {
        this.objType = objType;
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public Long getAnswerNum() {
        return answerNum;
    }

    public void setAnswerNum(Long answerNum) {
        this.answerNum = answerNum;
    }

    public Long getWrongNum() {
        return wrongNum;
    }

    public void setWrongNum(Long wrongNum) {
        this.wrongNum = wrongNum;
    }

    public Long getParentObjId() {
        return parentObjId;
    }

    public void setParentObjId(Long parentObjId) {
        this.parentObjId = parentObjId;
    }
    
    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPlatForm() {
        return platForm;
    }

    public void setPlatForm(String platForm) {
        this.platForm = platForm;
    }
}