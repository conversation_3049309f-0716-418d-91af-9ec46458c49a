/**
 * Copyright (c) 2011 duowan.com.
 * All Rights Reserved.
 * This program is the confidential and proprietary information of
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.exception.DataAccessException;

public interface IGenericThriftResource {

    public static final int WEB_APP_ID = 1; // web传1
    public static final int WWW_APP_ID = 2; // 官网组 传2
    public static final int WAP_APP_ID = 3;// 移动端 传3
    public static final int YY_APP_ID = 4; // YY教育 传4
    public static final int PLUGIN_APP_ID = 5;// 互动 传 5
    public static final int ENTRY_APP_ID = 6;// 进入频道 传 6
    public static final int NIC_APP_ID = 7;// 菜单 传 7
    public static final int TV_APP_ID = 8;// TV 传 8
    public static final int PC_APP_ID = 9;// C++服务端 传 9

    // response generalSchoolThriftMethodInvoke(Map<String, Object> param, int
    // appid, String clientIp, String methodName)
    // throws DataAccessException;
    //
    // response generalProfileThriftMethodInvoke(Map<String, Object> param, int
    // appid, String clientIp, String methodName)
    // throws DataAccessException;

    response generalKnowledgeThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException;

    response generalGoodsThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException;

    response generalAnalyseThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException;

    response generalHqUserThriftMethodInvoke(String jsonParam, int appid, String clientIp, Long schId, Long pschId, String methodName)
            throws DataAccessException;

	cn.huanju.edu100.thrift.response generalKnowledgeThriftMethodInvoke(String jsonParam, int appid, String clientIp, Long schId, Long pschId,
			String methodName)throws DataAccessException;

    response generalStustampThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException;

    response generalSearchThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException;

    response generalMemThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException;

    response generalMemThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName, Long schId) throws DataAccessException;
}
