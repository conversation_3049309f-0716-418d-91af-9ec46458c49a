package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.QuestionAnswerDetail;
import cn.huanju.edu100.study.model.QuestionAnswerStaticGroup;
import cn.huanju.edu100.study.model.QuestionAnswerStatics;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.model.QuestionAnswerStaticsCount;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2015-05-14
 */

public interface QuestionAnswerStaticsDao  extends CrudDao<QuestionAnswerStatics>{
    long countQuestionAnswer(long questionId) throws DataAccessException;

    long countRightAnswer(long questionId) throws DataAccessException;

    List<QuestionAnswerStaticsCount> countRightAnswerByIds(List<Long> questionIds) throws DataAccessException;

    long countWrongAnswer(long questionId) throws DataAccessException;

	Integer addQuestionAnswerDetail(QuestionAnswerDetail questionAnswerDetail)throws DataAccessException;

	Integer addQuestionAnswerDetailBatch(List<QuestionAnswerDetail> questionAnswerDetail)throws DataAccessException;

	List<QuestionAnswerStaticGroup> findGroupByQuestionId()throws DataAccessException;

	Integer refashQuestionAnswerTotal(Long questionId, Integer num)throws DataAccessException;

	Integer isExsitByUidAndQuestionId(QuestionAnswerDetail questionAnswerDetail)throws DataAccessException;

	Integer updateQuestionAnswerDetailBatch(List<QuestionAnswerDetail> updateList)throws DataAccessException;
}
