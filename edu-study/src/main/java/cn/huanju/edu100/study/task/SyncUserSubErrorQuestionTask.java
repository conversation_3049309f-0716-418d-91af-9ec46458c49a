package cn.huanju.edu100.study.task;

import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.UserErrorQuestion;
import cn.huanju.edu100.study.model.UserStudyResult;
import cn.huanju.edu100.study.model.UserSubErrorQuestion;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.resource.StustampResource;
import cn.huanju.edu100.study.service.UserQuestionService;
import cn.huanju.edu100.study.service.UserSubErrorQuestionService;
import cn.huanju.edu100.util.DateUtils;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * Created by zhanghong on 2021/10/8.
 */
@Service
public class SyncUserSubErrorQuestionTask {

    Logger logger = LoggerFactory.getLogger(SyncUserSubErrorQuestionTask.class);

    private static final int PAGE_COUNT = 800;

    @Autowired
    private UserQuestionService userQuestionService;

    @Autowired
    private UserSubErrorQuestionService userSubErrorQuestionService;


    @Autowired
    private StustampResource stustampResource;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    private static final String SYNC_REDIS_KEY = "sync_user_sub_question_task";

    private static final ExecutorService es = Executors.newFixedThreadPool(8);

    private Semaphore semaphore = new Semaphore(8);

    public void startSync(Integer startIndexParam, Long uidParam) {
        //设定 每晚凌晨进行同步数据至新表，一次读取2000条数据 且执行时间要在5点钟之前
        String endSyncTime = "05:00:00";
        int readCount = 0;
        int startIndex = 0; //从redis中获取

        long startCurrentTime = System.currentTimeMillis();
        logger.info("start Time is:{}",startCurrentTime);

        try {
            String startIndexString = compatableRedisClusterClient.get(SYNC_REDIS_KEY);
            logger.info("redis startIndexString is :{}", startIndexString);
            if (StringUtils.isNotEmpty(startIndexString)) {
                startIndex = Integer.parseInt(startIndexString);
            }
            Date startTime = DateUtils.parseDateTimeSec("2020-01-01 00:00:00");
            if (startIndexParam != null) {
                startIndex = startIndexParam;
            }

            while (true) {
//                if (timeCompare(endSyncTime)) {
//                    break;
//                }
                logger.info("start index is :{}", startIndex);
//                if (startIndex > 100000) {
//                    break;
//                }

                //这里对数据库中的数据进行读取
                Map<String, Object> params = new HashMap<>();
                if (uidParam != null && uidParam > 0) {
                    params.put("uid", uidParam);
                }
                params.put("startId", 56936177L);
                params.put("endId",148325200L);
                params.put("from", startIndex);
                params.put("pageSize", PAGE_COUNT);
                params.put("orderBy", "id asc");

                semaphore.acquire();
                List<UserErrorQuestion> userErrorQuestionList = new ArrayList<>();
                try {
                    userErrorQuestionList = userQuestionService.findHomeworkErrorQuestionList(params);
                } catch (DataAccessException e) {
                    e.printStackTrace();
                }
                if (CollectionUtils.isNotEmpty(userErrorQuestionList)) {
                    readCount = userErrorQuestionList.size();
                }
                if (readCount <= 0) {
                    logger.info("current circle readCount is 0");
                    break;
                }
                logger.info("current circle readCount is :{} and maxid is :{}", readCount, GsonUtil.getGenericGson().toJson(userErrorQuestionList.get(readCount - 1)));

                List<UserErrorQuestion> currentCircleErrorQuestion = userErrorQuestionList;
                es.execute(new Runnable() {
                    @Override
                    public void run() {
                        //获取错题集map
                        Map<Long, List<UserErrorQuestion>> userErrorQuestionMap
                                = currentCircleErrorQuestion.stream().collect(Collectors.groupingBy(UserErrorQuestion::getUid));
                        Map<Long, List<Long>> userLessonMap = new HashMap<>();
                        for (Map.Entry<Long, List<UserErrorQuestion>> entrySet : userErrorQuestionMap.entrySet()) {
                            List<UserErrorQuestion> userErrorQuestions = entrySet.getValue();
                            List<Long> lessonIds = userErrorQuestions.stream().filter(userErrorQuestion -> userErrorQuestion.getLessonId() != null).map(UserErrorQuestion::getLessonId).collect(Collectors.toList());
                            userLessonMap.put(entrySet.getKey(), lessonIds);
                        }

                        //根据lessonIdMap 进行请求 并拼接返回值 写入新的错题数据分表
                        for (Map.Entry<Long, List<Long>> entrySet : userLessonMap.entrySet()) {
                            Long uid = entrySet.getKey();
                            List<Long> lessonIdList = entrySet.getValue();
                            if (CollectionUtils.isEmpty(lessonIdList)) {
                                continue;
                            }
                            List<UserStudyResult> studyResults = stustampResource.findUserStudyResultLessonListByLessonIds(uid, lessonIdList);

                            logger.info("get user study result size is :{}", studyResults == null ? 0 : studyResults.size());
                            if (CollectionUtils.isNotEmpty(studyResults)) {
                                List<UserSubErrorQuestion> userSubErrorQuestionList = new ArrayList<>();
                                Map<Long, UserStudyResult> lessonIdResultMap = studyResults.stream().collect(Collectors.toMap(UserStudyResult::getLessonId, UserStudyResult -> UserStudyResult,
                                        (key1, key2) -> key2));
                                List<UserErrorQuestion> userErrorQuestions = userErrorQuestionMap.get(uid);
                                List<Long> questionIds = new ArrayList<>();
                                for (UserErrorQuestion userErrorQuestion : userErrorQuestions) {
                                    questionIds.add(userErrorQuestion.getQuestionId());
                                }
                                List<Question> questionList = knowledgeResource.getQuestionByIds(questionIds);
                                Map<Long, Question> questionMap = new HashMap<>();
                                if (CollectionUtils.isNotEmpty(questionList)) {
                                    questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Question -> Question, (key1, key2) -> key2));
                                }

                                for (UserErrorQuestion userErrorQuestion : userErrorQuestions) {
                                    UserSubErrorQuestion userSubErrorQuestion = new UserSubErrorQuestion();
                                    UserStudyResult userStudyResult = lessonIdResultMap.get(userErrorQuestion.getLessonId());
                                    if (userStudyResult == null) {
                                        logger.info("current UserErrorQuestion get result is null:{}", GsonUtil.toJson(userErrorQuestion));
                                        continue;
                                    }

                                    userSubErrorQuestion.setUid(uid);
                                    userSubErrorQuestion.setProductId(userStudyResult.getProductId());
                                    userSubErrorQuestion.setGoodsId(userStudyResult.getGoodsId());

                                    userSubErrorQuestion.setLessonId(userErrorQuestion.getLessonId());
                                    userSubErrorQuestion.setQuestionId(userErrorQuestion.getQuestionId());
                                    userSubErrorQuestion.setTopicId(userErrorQuestion.getTopicId());
                                    userSubErrorQuestion.setLastErrorAnswer(userErrorQuestion.getLastErrorAnswer());
                                    userSubErrorQuestion.setLastErrorTime(userErrorQuestion.getLastErrorTime());
                                    userSubErrorQuestion.setCreateDate(userErrorQuestion.getCreateDate());

                                    Question question = questionMap.get(userErrorQuestion.getQuestionId());
                                    if (question != null) {
                                        userSubErrorQuestion.setCategoryId(question.getCategoryId());
                                        userSubErrorQuestion.setQtype(question.getQtype());
                                    }
                                    userSubErrorQuestionList.add(userSubErrorQuestion);
                                    logger.info("====add userSubError:{}", GsonUtil.toJson(userSubErrorQuestion));
                                }

                                logger.info("userSubErrorQuestionList size is :{}", userSubErrorQuestionList.size());
                                try {
                                    if (CollectionUtils.isNotEmpty(userSubErrorQuestionList)) {
                                        for (UserSubErrorQuestion userSubErrorQuestion : userSubErrorQuestionList) {
                                            userSubErrorQuestionService.saveSubErrorQuestion(userSubErrorQuestion);
                                        }
                                    }
                                } catch (Exception ex) {
                                    logger.info("insert subQuestion error:{}", ex);
                                }

                            }
                        }

                        semaphore.release();
                    }
                });

                //更新redis缓存
                int updateStartReadCount = startIndex + readCount;
                compatableRedisClusterClient.set(SYNC_REDIS_KEY, String.valueOf(updateStartReadCount));
                logger.info("syncUserSubErrorQuestion updateStartReadCount is :{}", updateStartReadCount);
                if (readCount < PAGE_COUNT) {
                    break;
                }

                startIndex += PAGE_COUNT;

                //一次查询中间间隔2s左右
                Thread.sleep(2000);
            }
        } catch (Exception ex) {
            logger.info("syncUserSubErrorQuestion error :{}", ex);
        }

        long elapsed = (System.currentTimeMillis() - startCurrentTime);
        logger.info("SyncUserSubErrorQuestionTask elapsed is:{}",elapsed);
    }


    public static void main(String[] args) {
        String time = "05:00:00";
        System.out.println(timeCompare(time));
    }

    /**
     * 判断当前时间是否大于某个时点
     *
     * @param time HH:mm:ss
     * @return
     */
    public static boolean timeCompare(String time) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime localTime = LocalTime.parse(time, dtf);
        return LocalTime.now().isAfter(localTime);
    }
}
