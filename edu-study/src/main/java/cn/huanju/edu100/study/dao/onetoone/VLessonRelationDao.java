/**
 * 
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.onetoone.VLessonRelation;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 直播课节对应关系表DAO接口
 * <AUTHOR>
 * @version 2017-11-22
 */
public interface VLessonRelationDao extends CrudDao<VLessonRelation> {
    public List<VLessonRelation> findListByLiveLessonIdList(List<Long> liveLessonIdList, Integer type) throws DataAccessException ;


	
}