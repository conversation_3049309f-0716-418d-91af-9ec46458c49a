package cn.huanju.edu100.study.service.impl.expression;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.cache.DataCache;
import cn.huanju.edu100.study.dao.ibatis.expression.ExpressionGroupDao;
import cn.huanju.edu100.study.model.expression.ExpressionGroup;
import cn.huanju.edu100.study.service.expression.ExpressionGroupService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 规则组Service
 *
 * <AUTHOR>
 * @version 2016-05-23
 */
@Service
public class ExpressionGroupServiceImpl extends BaseServiceImpl<ExpressionGroupDao, ExpressionGroup> implements
        ExpressionGroupService {

    @Autowired
    private DataCache daCache;

    @Override
    public List<ExpressionGroup> findListByParam(Set<Long> idSet, Integer type) throws DataAccessException {
        if (CollectionUtils.isEmpty(idSet)) {
            logger.error("findListByParam fail, idSet is null");
            return null;
        }

        List<Long> idList = Lists.newArrayList();
        idList.addAll(idSet);

        List<ExpressionGroup> expressionGroups = daCache.getExpressionGroupByIds(idList, type);
        if (!CollectionUtils.isEmpty(expressionGroups)) {
            return expressionGroups;
        }
        return dao.findListByParam(idList, type);
    }

}
