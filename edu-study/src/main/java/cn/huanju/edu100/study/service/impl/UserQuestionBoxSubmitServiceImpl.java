package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.model.ChapterSection;
import cn.huanju.edu100.study.model.QuestionKnowledge;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserQuestionBoxNewChapterService;
import cn.huanju.edu100.study.service.UserQuestionBoxService;
import cn.huanju.edu100.study.service.UserQuestionBoxSubmitService;
import cn.huanju.edu100.study.util.MapUtil;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

@Service
public class UserQuestionBoxSubmitServiceImpl implements UserQuestionBoxSubmitService {

	private static Logger logger = LoggerFactory.getLogger(UserQuestionBoxSubmitServiceImpl.class);


    @Autowired
    private UserQuestionBoxNewChapterService userQuestionBoxNewChapterService;

    /**
     * 用户做过的题入redis缓存
     * @param uid
     * @param teachBookId 教材id
     * @param boxId 题库id
     * @param questionIds 题目id列表
     * */
    @Override
    public void cachePaperDoneQuestion(Long uid, Long teachBookId, Long boxId, List<Long> questionIds) throws DataAccessException{
        userQuestionBoxNewChapterService.cachePaperDoneQuestion(uid, null, boxId, questionIds);
    }

	/**
	 * 用户错题入redis缓存
	 * @param uid
     * @param teachBookId 教材id
     * @param boxId 题库id
	 * @param questionIds 题目id列表
     * @param topicIds 答错的子题id列表
	 * */
	@Override
	public void cachePaperWrongQuestion(Long uid, Long teachBookId, Long boxId, List<Long> questionIds, List<Long> topicIds) throws DataAccessException{
        userQuestionBoxNewChapterService.cachePaperWrongQuestion(uid, null, boxId, questionIds, topicIds);
	}

}
