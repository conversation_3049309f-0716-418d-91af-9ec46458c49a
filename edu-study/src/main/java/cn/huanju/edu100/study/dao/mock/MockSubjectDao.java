/**
 * 
 */
package cn.huanju.edu100.study.dao.mock;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.mock.MockSubject;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;


/**
 * 模考科目DAO接口
 * <AUTHOR>
 * @version 2018-04-08
 */
public interface MockSubjectDao extends CrudDao<MockSubject> {
	List<MockSubject> qryByMockExamId(Long mockExamId) throws DataAccessException;

    List<MockSubject> findMockSubjectListByMockId(Long mockExamId) throws DataAccessException;
}