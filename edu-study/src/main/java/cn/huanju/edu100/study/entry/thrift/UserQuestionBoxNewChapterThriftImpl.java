package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.UserVirtualHomeworkDao;
import cn.huanju.edu100.study.model.TeachingBook;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserBrushQuestionInfoService;
import cn.huanju.edu100.study.service.UserQuestionBoxNewChapterService;
import cn.huanju.edu100.study.service.UserQuestionBoxService;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import com.hqwx.study.dto.query.UserQuestionQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 题库5.0章节练习
 * */
@Component
public class UserQuestionBoxNewChapterThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(UserQuestionBoxNewChapterThriftImpl.class);
    static Gson gson = GsonUtil.getGson();
    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;
    @Autowired
    private UserBrushQuestionInfoService userBrushQuestionInfoService;
    @Autowired
    private UserQuestionBoxNewChapterService userQuestionBoxNewChapterService;
    @Autowired
    private KnowledgeResource knowledgeResource;
    @Autowired
    private UserQuestionBoxService userQuestionBoxService;
    @Autowired
    @Qualifier("userVirtualHomeworkDao")
    private UserVirtualHomeworkDao userVirtualHomeworkDao;


    public response sty_syncUserQuestionLog(request req) throws BusinessException{
        String entry = "sty_syncUserQuestionLog";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserQuestionQuery param = GsonUtil.getGson().fromJson(req.getMsg(), UserQuestionQuery.class);
            Long uid = param.getUid();
            Long categoryId = param.getCategoryId();
            if (!IdUtils.isValid(uid)) {
                logger.error("{} parameter is invalid:{}", entry, req.getMsg());
                throw new BusinessException("parameter is invalid");
            }
            userQuestionBoxNewChapterService.syncUserQuestionLog(uid, categoryId);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(true, req.getAppid()));
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_userQuestionLogIsSync(request req) throws BusinessException{
        String entry = "sty_userQuestionLogIsSync";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserQuestionQuery param = GsonUtil.getGson().fromJson(req.getMsg(), UserQuestionQuery.class);
            Long uid = param.getUid();
            Long categoryId = param.getCategoryId();
            if (!IdUtils.isValid(uid)) {
                logger.error("{} parameter is invalid:{}", entry, req.getMsg());
                throw new BusinessException("parameter is invalid");
            }
            boolean result = userQuestionBoxNewChapterService.isUserQuestionLogSync(uid, categoryId);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 章节练习进度重置
     * param uid
     * param categoryId
     * param teach_book_id
     * */
    public response sty_resetNewChapterPractice(request req) throws BusinessException{
        String entry = "sty_resetNewChapterPractice";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        //根据uid 科目id 查询当前用户redis中 缓存的做过的题目 qBox_d_151169110_469 hash 存储结构
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || (Double)param.get("uid")<=0
                    || param.get("boxId") == null || (Double)param.get("boxId")<=0
                    || param.get("teach_book_id") == null || (Double)param.get("teach_book_id")<=0
            ) {
                logger.error("{} sty_resetNewChapterPractice parameter lose, uid or boxId or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_resetNewChapterPractice parameter lose, uid or boxId or teach_book_id is null.");
            }else {
                Long uid = ((Double) param.get("uid")).longValue() ;
                Long boxId = ((Double) param.get("boxId")).longValue() ;
                Long newBookId = ((Double)param.get("teach_book_id")).longValue() ;
                Long oldBookId = null;
                logger.info("sty_resetNewChapterPractice uid:{} ,boxId:{} begin", uid,boxId);
                TeachingBook oldBook = userQuestionBoxService.getTeachBookByBoxId(boxId);
                if (oldBook != null) {
                    oldBookId = oldBook.getId();
                }

                Boolean result = userQuestionBoxNewChapterService.resetNewChapterPractice(uid, boxId, newBookId);
                //同时清楚旧题库的章节练习记录
                if (result){
                    result = userQuestionBoxService.resetChapterPractice(uid, boxId, oldBookId);
                }

                if (result) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg("sty_resetChapterPractice success!");
                } else {
                    res.setCode(Constants.SYS_ERROR);
                    res.setErrormsg("sty_resetChapterPractice error!");
                }
                logger.info("sty_resetNewChapterPractice uid:{} ,boxId:{} end", uid,boxId);
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
