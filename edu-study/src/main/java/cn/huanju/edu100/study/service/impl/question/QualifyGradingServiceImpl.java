package cn.huanju.edu100.study.service.impl.question;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.question.QualifyGradingDao;
import cn.huanju.edu100.study.model.question.QualifyGrading;
import cn.huanju.edu100.study.service.question.QualifyGradingService;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 排位赛段位信息Service
 * <AUTHOR>
 * @version 2018-04-25
 */
@Service
public class QualifyGradingServiceImpl extends BaseServiceImpl<QualifyGradingDao, QualifyGrading> implements QualifyGradingService {
    private static Logger logger = LoggerFactory.getLogger(QualifyGradingServiceImpl.class);
    private static Gson gson = GsonUtil.getGson();

    @Autowired
    private QualifyGradingDao qualifyGradingDao;

    @Override
    public List<QualifyGrading> getByCateIdAndUid(Long secondCategory, Long categoryId, Long uid) throws DataAccessException {
        return qualifyGradingDao.getByCateIdAndUid(secondCategory, categoryId, uid);
    }

    @Override
    public List<QualifyGrading> getLatestByUids(List<Long> uids) throws DataAccessException {
        return qualifyGradingDao.getLatestByUids(uids);
    }
}
