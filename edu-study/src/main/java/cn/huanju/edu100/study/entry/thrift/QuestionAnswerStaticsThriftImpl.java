package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.QuestionAnswerStatics;
import cn.huanju.edu100.study.model.question.QuestionTopicStatistic;
import cn.huanju.edu100.study.service.QuestionAnswerStaticsService;
import cn.huanju.edu100.study.service.question.QuestionTopicStatisticService;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import com.hqwx.study.dto.query.QuestionTopicStatisticQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2015-05-14
 */
@Component
public class QuestionAnswerStaticsThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(QuestionAnswerStaticsThriftImpl.class);
    private static Gson gson = GsonUtil.getGson();
    @Autowired
    private QuestionAnswerStaticsService service;
    @Autowired
    private QuestionTopicStatisticService questionTopicStatisticService;

    /**
     * 根据questionId统计答卷情况
     * @param req 试卷ID：paperId
     * @return
     * @throws BusinessException
     */
    public response sty_staticQuestionAnswer(request req) throws BusinessException {
        String entry = "sty_staticQuestionAnswer";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("question_id") == null) {
                logger.error("{} fail.parameter id is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter question_id is null.");
            }
            if (!(param.get("question_id") instanceof Double)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter question_id is not number.");
            }
            long questionId = param.get("question_id").longValue();

            QuestionAnswerStatics statics = service.staticQuestionAnswer(questionId);

            if (null==statics) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_staticQuestionAnswer return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(statics,req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 根据paperIdList统计答卷情况
     * @param req 题目ID：questionId
     * @return
     * @throws BusinessException
     */
    public response sty_staticQuestionAnswerBatch(request req) throws BusinessException {
        String entry = "sty_staticQuestionAnswerBatch";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {
			}.getType();
			Collection<Long> Ids = gson.fromJson(req.getMsg(), type);
            if (Ids == null || Ids.size()<=0) {
                logger.error("{} fail.parameter id is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter questionId is null.");
            }
            if (Ids.size()>200) {
            	logger.error("{} fail.parameter id is to much.", entry);
                throw new BusinessException(Constants.SYS_ERROR, "parameter id is to much.");
			}

            Map<Long,QuestionAnswerStatics> statics = service.staticQuestionAnswerBatch(Ids);

            if (null==statics || statics.size()==0) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_staticQuestionAnswerBatch return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(statics,req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getQuestionTopicStatisticList(request req) throws BusinessException {
        String entry = "sty_getQuestionTopicStatisticList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<QuestionTopicStatisticQuery>() {
            }.getType();
            QuestionTopicStatisticQuery query = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            if (query == null || CollectionUtils.isEmpty(query.getTopicIdList())) {
                logger.error("{} fail.param is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "param is null.");
            }
            List<QuestionTopicStatistic> rsList = questionTopicStatisticService.getQuestionTopicStatisticList(query);
            if (CollectionUtils.isEmpty(rsList)) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg(entry + " return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.getGenericGson().toJson(rsList));
            }
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

}
