package cn.huanju.edu100.study.model;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 白名单列表Entity
 * <AUTHOR>
 * @version 2015-10-27
 */
public class WhiteList extends DataEntity<WhiteList> {
	
	private static final long serialVersionUID = 1L;
	private Integer type;		// 名单白类型
	private Long objId;		// 名单白关联id
	private Long extObjId;		// 名单白关联拓展id
	
	public WhiteList() {
		super();
	}

	public WhiteList(Long id){
		super(id);
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public Long getObjId() {
		return objId;
	}

	public void setObjId(Long objId) {
		this.objId = objId;
	}
	
	public Long getExtObjId() {
		return extObjId;
	}

	public void setExtObjId(Long extObjId) {
		this.extObjId = extObjId;
	}
	
}