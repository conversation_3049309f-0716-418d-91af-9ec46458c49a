package cn.huanju.edu100.study.kafka.studyerrorinfo;

import cn.huanju.edu100.study.model.UserSubErrorQuestion;
import cn.huanju.edu100.study.model.UserSubErrorQuestionDto;
import cn.huanju.edu100.study.service.UserSubErrorQuestionService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.GsonUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
public class KafkaConsumerStudyErrorInfoReverseService {

    @Autowired
    private UserSubErrorQuestionService userSubErrorQuestionService;

    @KafkaListener(containerFactory = "studyErrorInfoReverseListenerContainerFactory", topics = {"${kafka.consumer.studyerrorinforeverse.topic}"}, groupId = "${kafka.consumer.studyerrorinforeverse.groupId}", autoStartup = "true")
    public void consume(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        consumerStudyErrorInfoReverse(records);
        ack.acknowledge();
    }

    private void consumerStudyErrorInfoReverse(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> item : records) {
            try {
                String message = item.value();
                log.info("consumerStudyErrorInfoReverse data:{} ", message);
                UserSubErrorQuestionDto errorQuestionDto = JSONObject.parseObject(message, UserSubErrorQuestionDto.class);
                Integer optType = errorQuestionDto.getOptType();
                if (optType != null && optType == Consts.CollectOptType.NO_COLLECT_QUESTION) {
                    Long uid = errorQuestionDto.getUid();
                    Long questionId = errorQuestionDto.getQuestionId();
                    if (uid == null || questionId == null) {
                        continue;
                    }
                    UserSubErrorQuestion userSubErrorQuestion = new UserSubErrorQuestion();
                    userSubErrorQuestion.setUid(uid);
                    userSubErrorQuestion.setQuestionId(questionId);
                    userSubErrorQuestionService.removeUserSubErrorQuestion(userSubErrorQuestion);
                }
            } catch (Exception e) {
                log.error("consumerStudyErrorInfoReverse fail! data:{} ", GsonUtil.getGenericGson().toJson(item), e);
            }

        }
    }

}
