/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.onetoone.VStudentLessonCountDao;
import cn.huanju.edu100.study.model.onetoone.VStudentLessonCount;
import cn.huanju.edu100.util.GsonUtil;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学员课时记录DAO接口
 * <AUTHOR>
 * @version 2016-04-19
 */
public class VStudentLessonCountIbatisImpl extends CrudIbatisImpl2<VStudentLessonCount> implements
		VStudentLessonCountDao {

	public VStudentLessonCountIbatisImpl() {
		super("VStudentLessonCount");
	}

    @Override
    public Integer deCreaseLessonCount(VStudentLessonCount vlessonCount) throws DataAccessException {
        if (vlessonCount == null) {
            logger.error("deCreaseLessonCount {} error, parameter vlessonCount is empty, entity:{}",
                    namespace, GsonUtil.toJson(vlessonCount));
            throw new DataAccessException(String.format("getCompleteCountByClsId %s error, parameter vlessonCount is empty, entity: %s",
                    namespace, GsonUtil.toJson(vlessonCount)));
        }

        try {
            Map<String, Object> params = new HashMap<String, Object>();
            List<Long> uidList = new ArrayList<Long>();
            uidList.add(vlessonCount.getUid());
            params.put("uid", vlessonCount.getUid());
            if (vlessonCount.getLessonCount() != null) {
                params.put("lessonCount", vlessonCount.getLessonCount());
            }
            if (vlessonCount.getUseCount() != null) {
                params.put("useDecrease", vlessonCount.getLessonCount());
            }
            params.put("productId", vlessonCount.getProductId());
            SqlMapClient sqlMap = super.getMaster();
            return (Integer)sqlMap.update(namespace.concat(".updateLessonCount"), params);
        } catch (SQLException e) {
            logger.error("deCreaseLessonCount {} SQLException. vlessonCount:{}", namespace, GsonUtil.toJson(vlessonCount), e);
            throw new DataAccessException(String.format("deCreaseLessonCount SQLException error :%s", e.getMessage()));
        }
    }

}
