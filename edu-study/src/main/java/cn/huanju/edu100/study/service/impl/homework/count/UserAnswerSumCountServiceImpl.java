package cn.huanju.edu100.study.service.impl.homework.count;

import cn.huanju.edu100.study.mapper.homework.count.UserAnswerSumCountMapper;

import cn.huanju.edu100.study.model.UserAnswerSumCount;
import cn.huanju.edu100.study.service.homework.count.UserAnswerSumCountService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class UserAnswerSumCountServiceImpl extends ServiceImpl<UserAnswerSumCountMapper, UserAnswerSumCount> implements UserAnswerSumCountService {

}
