package cn.huanju.edu100.study.dao.calculate;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

public interface TikuLastChapterExerciseDao extends CrudDao<TikuLastChapterExercise> {

    TikuLastChapterExercise getByUidBoxId(Long uid,Long boxId) throws DataAccessException;
    TikuLastChapterExercise getByUidBoxIds(Long uid, List<Long> boxIds) throws DataAccessException;


}
