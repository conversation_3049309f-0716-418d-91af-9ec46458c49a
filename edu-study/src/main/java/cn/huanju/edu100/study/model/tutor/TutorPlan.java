package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 班主任个性化计划Entity
 * <AUTHOR>
 * @version 2016-01-14
 */
public class TutorPlan extends DataEntity<TutorPlan> {

	private static final long serialVersionUID = 1L;
	private String classes;		// 班别
	private Long groupId;		// 分组id
	private Long firstCategory;		// 所属大类
	private Long secondCategory;		// 所属考试
	private Long categoryId;		// 科目
	private String name;		// 计划标题
	private Date startTime;		// 开始时间
	private Date endTime;		// 结束时间
	private String ip;		// ip

	public TutorPlan() {
		super();
	}

	public TutorPlan(Long id){
		super(id);
	}

	public String getClasses() {
		return classes;
	}

	public void setClasses(String classes) {
		this.classes = classes;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public Long getFirstCategory() {
		return firstCategory;
	}

	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

}
