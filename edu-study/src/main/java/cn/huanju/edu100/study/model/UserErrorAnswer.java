package cn.huanju.edu100.study.model;

import cn.huanju.edu100.util.GsonUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户错题提交bean
 * */
public class UserErrorAnswer {
	private Long userErrorId;		//对应着错题记录里的id
	private Long uid;
	private Long questionId;
	private Long topicId;
	private Long objId;
	private Integer objType;
	private String[] answer;		// 用户文本答案
	private String answerStr;		// 用户文本答案


	public Long getUserErrorId() {
		return userErrorId;
	}

	public void setUserErrorId(Long userErrorId) {
		this.userErrorId = userErrorId;
	}

	public String[] getAnswer() {
		return answer;
	}

	public void setAnswer(String[] answer) {
		this.answer = answer;
	}

	public String getAnswerStr() {
		return answerStr;
	}

	public void setAnswerStr(String answerStr) {
		if (StringUtils.isNotBlank(answerStr)) {
			setAnswer(GsonUtil.getGson().from<PERSON>son(answerStr, String[].class));
		}
		this.answerStr = answerStr;
	}

	public Long getUid() {
		return uid;
	}
	public void setUid(Long uid) {
		this.uid = uid;
	}
	public Long getQuestionId() {
		return questionId;
	}
	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}
	public Long getTopicId() {
		return topicId;
	}
	public void setTopicId(Long topicId) {
		this.topicId = topicId;
	}
	public Long getObjId() {
		return objId;
	}
	public void setObjId(Long objId) {
		this.objId = objId;
	}
	public Integer getObjType() {
		return objType;
	}
	public void setObjType(Integer objType) {
		this.objType = objType;
	}


}
