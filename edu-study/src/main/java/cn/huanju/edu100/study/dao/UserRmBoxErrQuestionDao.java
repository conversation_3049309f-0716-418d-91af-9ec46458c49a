/**
 *
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.UserRmBoxErrQuestionLog;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 用户移除题库错题日志DAO接口
 *
 * <AUTHOR>
 * @version 2016-05-26
 */
public interface UserRmBoxErrQuestionDao extends CrudDao<UserRmBoxErrQuestionLog> {

    public int insertlogUserBoxRmQuestionBatch(List<UserRmBoxErrQuestionLog> logs, Long uid) throws DataAccessException;

    public int updatelogUserBoxRmQuestionBatch(List<UserRmBoxErrQuestionLog> logs, Long uid) throws DataAccessException;

}
