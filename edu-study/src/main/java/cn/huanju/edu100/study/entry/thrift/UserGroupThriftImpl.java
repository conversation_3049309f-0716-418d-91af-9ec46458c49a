package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.StudyReportDetail;
import cn.huanju.edu100.study.model.UserGroup;
import cn.huanju.edu100.study.service.StudyTaskService;
import cn.huanju.edu100.study.service.UserGroupService;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
@Component
public class UserGroupThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(UserGroupThriftImpl.class);
    private static Gson gson = GsonUtil.getGson();
    @Autowired
    private UserGroupService userGroupService;
    @Autowired
    private StudyTaskService studyTaskService;

    public response sty_getGroupsByUid(request req) throws BusinessException {
        String entry = req.getMsg();
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null) {
                logger.error("{} fail.parameter uid is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is null.");
            }
            if (!(param.get("uid") instanceof Double)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is not number.");
            }
            long uid = param.get("uid").longValue();
            Collection<UserGroup> userGroupList = userGroupService.getGroupsByUid(uid);
            if (CollectionUtils.isEmpty(userGroupList)) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getGroupByUid return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(userGroupList, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getGroupById(request req) throws BusinessException {
        String entry = req.getMsg();
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("id") == null) {
                logger.error("{} fail.parameter id is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter id is null.");
            }
            if (!(param.get("id") instanceof Double)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter id is not number.");
            }
            Long id = param.get("id").longValue();

            UserGroup userGroup = userGroupService.getGroupById(id);
            if (userGroup == null) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getGroupById return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(userGroup, req.getAppid()));
            }

            List<StudyReportDetail> answerDetail = new ArrayList<StudyReportDetail>();
            StudyReportDetail detail = new StudyReportDetail();
            detail.setAnswerIds("[1,2,3,4,5]");
            detail.setAnswerNum(5);
            detail.setWrongIds("[1,2,3]");
            detail.setWrongNum(3);
            detail.setKnowledgeId(2l);
            detail.setTaskId(28l);
            answerDetail.add(detail);
            studyTaskService.studentAnswerSubmit(4l, 28l, 10902273l, 1, 0, 1000, answerDetail);
            // studyTaskService.studyLogDataTransferAction();
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

}
