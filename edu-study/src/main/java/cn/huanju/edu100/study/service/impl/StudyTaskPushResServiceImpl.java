package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.StudyTaskPushResDao;
import cn.huanju.edu100.study.model.StudyTaskPushRes;
import cn.huanju.edu100.study.service.StudyTaskPushResService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StudyTaskPushResServiceImpl extends
		BaseServiceImpl<StudyTaskPushResDao, StudyTaskPushRes> implements
		StudyTaskPushResService {
	@Override
	public List<StudyTaskPushRes> findListByTaskIdList(List<Long> taskIdList, Long uid)	throws DataAccessException {
		return dao.findListByTaskIdList(taskIdList, uid);
	}
}
