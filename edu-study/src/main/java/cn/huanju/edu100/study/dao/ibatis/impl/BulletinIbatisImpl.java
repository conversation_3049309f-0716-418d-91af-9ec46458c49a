/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.BulletinDao;
import cn.huanju.edu100.study.model.Bulletin;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网校公告DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public class BulletinIbatisImpl extends CrudIbatisImpl2<Bulletin> implements
		BulletinDao {

	public BulletinIbatisImpl() {
		super("Bulletin");
	}

	@Override
	public Collection<Bulletin> list(Long schId, Integer type) throws DataAccessException {
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> params = new HashMap<String, Object>();
            if (schId != null) {
                params.put("schId", schId);
            }
            if (type != null) {
                params.put("type", type);
            }
			return (Collection<Bulletin>) sqlMap
					.queryForList("Bulletin.getBulletinList", params);
		} catch (SQLException e) {
			logger.error("getBulletinList SQLException.", e);
			throw new DataAccessException("getBulletinList SQLException error");
		}
	}

    @Override
    public List<Bulletin> getBulletinListByIds(List<Long> bulletinIdList, Long schId) throws DataAccessException {
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("bulletinIdList", bulletinIdList);
            if (schId != null) {
                params.put("schId", schId);
            }
            return (List<Bulletin>) sqlMap
                    .queryForList("Bulletin.getBulletinListByIds", params);
        } catch (SQLException e) {
            logger.error("getBulletinList SQLException.", e);
            throw new DataAccessException("getBulletinList SQLException error");
        }
    }

    @Override
    public List<Bulletin> getBulletins(Map<String, Object> params) throws DataAccessException {
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<Bulletin>) sqlMap
                    .queryForList("Bulletin.findList", params);
        } catch (SQLException e) {
            logger.error("getBulletins SQLException.", e);
            throw new DataAccessException("getBulletins SQLException error");
        }
    }

}
