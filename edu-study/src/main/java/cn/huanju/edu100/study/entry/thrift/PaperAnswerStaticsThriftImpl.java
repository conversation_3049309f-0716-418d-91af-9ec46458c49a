package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.PaperAnswerStatics;
import cn.huanju.edu100.study.service.PaperAnswerStaticsService;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 2015-05-14
 */
@Component
public class PaperAnswerStaticsThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(PaperAnswerStaticsThriftImpl.class);
    private static Gson gson = GsonUtil.getGson();
    @Autowired
    private PaperAnswerStaticsService service;

    /**
     * 根据questionId统计答题情况
     * @param req 题目ID：questionId
     * @return
     * @throws BusinessException
     */
    public response sty_staticPaperAnswer(request req) throws BusinessException {
        String entry = "sty_staticPaperAnswer";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("paper_id") == null) {
                logger.error("{} fail.parameter id is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter paperId is null.");
            }
            if (!(param.get("paper_id") instanceof Double)) {
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter paperId is not number.");
            }
            long paperId = param.get("paper_id").longValue();

            PaperAnswerStatics statics = service.staticPaperAnswer(paperId);

            if (null==statics) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_staticPaperAnswer return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(statics,req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
