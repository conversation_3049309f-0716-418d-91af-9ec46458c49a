package cn.huanju.edu100.study.model.solution;

import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.upload.OssUtil;
import com.alibaba.nacos.api.utils.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 用户答疑问题实体
 * 
 * */
public class SolutionQuestion extends DataEntity<SolutionQuestion> {
	private static final long serialVersionUID = 1L;
	private Long pid;
	private String categoryIdCode;
	private Long firstCategory;
	private Long secondCategory;
	private Long categoryId;
	private Integer courseId;
	private Integer lessonId;
	private Integer teachBookId;
	private Integer chapterId;
	private Integer knowledgeId;
	private List<Long> knowledgeIds;
	private Long goodsId;
	private Long questionId;
	private String title;
	private String content;
	private String contentText;
	private Long userId;
	private String userName;
	private Integer status;
	private Integer isFrozen;
	private String source;
	private String device;
	private String ip;
	private Long createdTime;
	private Long updatedTime;
	private Integer tag;
	private Integer collectionNum;
	private Integer views;
	private Integer isComplained;
	private Double hotOrderValue;
	private Integer isBest;
	private Integer likeNum;
	private Integer teacherId;
	private String teacherName;
	private QuestionAnswer questionAnswer;
	private List<QuestionAnswer> questionAnswerList;
	private List<SolutionQuestion> questionAgainList;
	private Integer haveCollected;
	private String categoryIds;
	private Integer isAl;
	private Long productId;
	private Long pathId;
	private Integer position;
	private Integer questionType;
	private Long paperId;
	private Long answerId;
	private Integer sourceType;
	private String extendInfo;
	private Integer isPublish;
	private Long resourceId;
	/**
	 * 是否ai回复，0人工 1ai回复中 2ai已回复 3已人工校验
	 */
	private Integer isAiAnswer;

	/**
	 * 是否可以点击AI老师头像申请使用数字人(1-是 0-否)
	 */
	private Integer isCanUseVirtualTeacher ;

	/**
	 * 是否为流式回复
	 */
	private Integer isStream ;

	private Date changeAiToManualTime ;

    private List<Integer> tagList;

	//会话ID
	private String conversationId;

	//消息ID
	private String messageId ;

	public String getExtendInfo() {
		return extendInfo;
	}

	public void setExtendInfo(String extendInfo) {
		this.extendInfo = extendInfo;
	}

	public Long getPid() {
		return pid;
	}

	public void setPid(Long pid) {
		this.pid = pid;
	}

	public String getCategoryIdCode() {
		return categoryIdCode;
	}

	public void setCategoryIdCode(String categoryIdCode) {
		this.categoryIdCode = categoryIdCode;
	}

	public Long getFirstCategory() {
		return firstCategory;
	}

	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getCourseId() {
		return courseId;
	}

	public void setCourseId(Integer courseId) {
		this.courseId = courseId;
	}

	public Integer getLessonId() {
		return lessonId;
	}

	public void setLessonId(Integer lessonId) {
		this.lessonId = lessonId;
	}

	public Integer getTeachBookId() {
		return teachBookId;
	}

	public void setTeachBookId(Integer teachBookId) {
		this.teachBookId = teachBookId;
	}

	public Integer getChapterId() {
		return chapterId;
	}

	public void setChapterId(Integer chapterId) {
		this.chapterId = chapterId;
	}

	public Integer getKnowledgeId() {
		return knowledgeId;
	}

	public void setKnowledgeId(Integer knowledgeId) {
		this.knowledgeId = knowledgeId;
	}

	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = OssUtil.bs2UrlConvertToOssUrl(content);
	}

	public String getContentText() {
		return contentText;
	}

	public void setContentText(String contentText) {
		this.contentText = contentText;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getIsFrozen() {
		return isFrozen;
	}

	public void setIsFrozen(Integer isFrozen) {
		this.isFrozen = isFrozen;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Long createdTime) {
		this.createdTime = createdTime;
	}

	public Long getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(Long updatedTime) {
		this.updatedTime = updatedTime;
	}

	public Integer getTag() {
		return tag;
	}

	public void setTag(Integer tag) {
		this.tag = tag;
	}

	public Integer getCollectionNum() {
		return collectionNum;
	}

	public Integer getViews() {
		return views;
	}

	public void setViews(Integer views) {
		this.views = views;
	}

	public Integer getIsComplained() {
		return isComplained;
	}

	public void setIsComplained(Integer isComplained) {
		this.isComplained = isComplained;
	}

	public Double getHotOrderValue() {
		return hotOrderValue;
	}

	public void setHotOrderValue(Double hotOrderValue) {
		this.hotOrderValue = hotOrderValue;
	}

	public void setCollectionNum(Integer collectionNum) {
		this.collectionNum = collectionNum;
	}

	public Integer getIsBest() {
		return isBest;
	}

	public void setIsBest(Integer isBest) {
		this.isBest = isBest;
	}

	public Integer getLikeNum() {
		return likeNum;
	}

	public void setLikeNum(Integer likeNum) {
		this.likeNum = likeNum;
	}

	public Integer getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Integer teacherId) {
		this.teacherId = teacherId;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public QuestionAnswer getQuestionAnswer() {
		return questionAnswer;
	}

	public void setQuestionAnswer(QuestionAnswer questionAnswer) {
		this.questionAnswer = questionAnswer;
	}

	public List<SolutionQuestion> getQuestionAgainList() {
		return questionAgainList;
	}

	public void setQuestionAgainList(List<SolutionQuestion> questionAgainList) {
		this.questionAgainList = questionAgainList;
	}

	public List<QuestionAnswer> getQuestionAnswerList() {
		return questionAnswerList;
	}

	public void setQuestionAnswerList(List<QuestionAnswer> questionAnswerList) {
		this.questionAnswerList = questionAnswerList;
	}

	public Integer getHaveCollected() {
		return haveCollected;
	}

	public void setHaveCollected(Integer haveCollected) {
		this.haveCollected = haveCollected;
	}

	public String getCategoryIds() {
		return categoryIds;
	}

	public void setCategoryIds(String categoryIds) {
		this.categoryIds = categoryIds;
	}

	public Integer getIsAl() {
		return isAl;
	}

	public void setIsAl(Integer isAl) {
		this.isAl = isAl;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public Long getPathId() {
		return pathId;
	}

	public void setPathId(Long pathId) {
		this.pathId = pathId;
	}

	public Integer getPosition() {
		return position;
	}

	public void setPosition(Integer position) {
		this.position = position;
	}

	public Integer getQuestionType() {
		return questionType;
	}

	public void setQuestionType(Integer questionType) {
		this.questionType = questionType;
	}

	public Long getPaperId() {
		return paperId;
	}

	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}

	public Long getAnswerId() {
		return answerId;
	}

	public void setAnswerId(Long answerId) {
		this.answerId = answerId;
	}

	public Integer getSourceType() {
		return sourceType;
	}

	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}

	public Integer getIsPublish() {
		return isPublish;
	}

	public void setIsPublish(Integer isPublish) {
		this.isPublish = isPublish;
	}

	public Long getResourceId() {
		return resourceId;
	}

	public void setResourceId(Long resourceId) {
		this.resourceId = resourceId;
	}

	public Integer getSourceTypeBySource(String source){
		if(StringUtils.isEmpty(source)){
			return 0;
		}
		switch (source){
			case Consts.QUESTION_RESOURCE.UCENTER:
			case Consts.QUESTION_RESOURCE.APP:
				return Consts.QUESTION_RESOURCE_TYPE.TEACH_MATERIAL;
			case Consts.QUESTION_RESOURCE.TIKU:
			case Consts.QUESTION_RESOURCE.QUESTION:
				return Consts.QUESTION_RESOURCE_TYPE.TEST_PAPER;
			case Consts.QUESTION_RESOURCE.PLAYER:
			case Consts.QUESTION_RESOURCE.RECORD:
			case Consts.QUESTION_RESOURCE.LIVE:
				return Consts.QUESTION_RESOURCE_TYPE.COURSE;
			case Consts.QUESTION_RESOURCE.APP_YSS:
			case Consts.QUESTION_RESOURCE.UC_YSS:
				return Consts.QUESTION_RESOURCE_TYPE.YSS;
		}
		return 0;
	}

	public Integer getIsAiAnswer() {
		return isAiAnswer;
	}

	public void setIsAiAnswer(Integer isAiAnswer) {
		this.isAiAnswer = isAiAnswer;
	}

	public Integer getIsCanUseVirtualTeacher() {
		return isCanUseVirtualTeacher;
	}

	public void setIsCanUseVirtualTeacher(Integer isCanUseVirtualTeacher) {
		this.isCanUseVirtualTeacher = isCanUseVirtualTeacher;
	}

	public Integer getIsStream() {
		return isStream;
	}

	public void setIsStream(Integer isStream) {
		this.isStream = isStream;
	}

	public Date getChangeAiToManualTime() {
		return changeAiToManualTime;
	}

	public void setChangeAiToManualTime(Date changeAiToManualTime) {
		this.changeAiToManualTime = changeAiToManualTime;
	}

	public List<Long> getKnowledgeIds() {
		return knowledgeIds;
	}

	public void setKnowledgeIds(List<Long> knowledgeIds) {
		this.knowledgeIds = knowledgeIds;
	}

	public List<Integer> getTagList() {
		return tagList;
	}

	public void setTagList(List<Integer> tagList) {
		this.tagList = tagList;
	}

	public String getConversationId() {
		return conversationId;
	}

	public void setConversationId(String conversationId) {
		this.conversationId = conversationId;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}
}
