package cn.huanju.edu100.study.dao.ibatis.impl.solution;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.solution.QuestionAnswerLikeDao;
import cn.huanju.edu100.study.model.solution.QuestionAnswerLike;
import cn.huanju.edu100.util.GsonUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;

public class QuestionAnswerLikeIbatisImpl extends CrudIbatisImpl2<QuestionAnswerLike> implements QuestionAnswerLikeDao {
    public QuestionAnswerLikeIbatisImpl() {super("QuestionAnswerLike");}

    @Override
    public boolean deleteAnswerLike(HashMap<String, Long> paramsMap) throws DataAccessException {
        if (paramsMap == null) {
            logger.error("deleteAnswerLike {} error, parameter is null", namespace);
            throw new DataAccessException("deleteAnswerLike {} error,param is null", namespace);
        }
        SqlMapClient sqlMap = super.getMaster();
        try {
            int row = sqlMap.delete(namespace + ".deleteAnswerLike", paramsMap);
            return row>=1;
        } catch (SQLException e) {
            logger.error("deleteAnswerLike {} SQLException.content:{}", namespace, GsonUtils.toJson(paramsMap), e);
            throw new DataAccessException("deleteAnswerLike {} " + namespace + " SQLException fail." + e.getMessage());
        }
    }

    @Override
    public QuestionAnswerLike findUserAnswerLikeByAnswerIdAndUid(HashMap<String, Long> paramsMap) throws DataAccessException {
        if (paramsMap == null) {
            logger.error("findUserAnswerLikeByAnswerIdAndUid {} error, parameter is null", namespace);
            throw new DataAccessException("findUserAnswerLikeByAnswerIdAndUid {} error,param is null", namespace);
        }
        try {
            SqlMapClient sqlMap = super.getSlave();

            return (QuestionAnswerLike) sqlMap.queryForObject(namespace + ".findUserAnswerLikeByAnswerIdAndUid", paramsMap);
        } catch (SQLException e) {
            logger.error("findUserAnswerLikeByAnswerIdAndUid {} error, parameter is null,queryParam:{}", namespace, GsonUtils.toJson(paramsMap), e);
            throw new DataAccessException("findUserAnswerLikeByAnswerIdAndUid SQLException error" + e.getMessage());
        }
    }
}
