package cn.huanju.edu100.study.service.impl.syncAnswer;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.*;
import cn.huanju.edu100.study.event.HomeworkSubmittedEvent;
import cn.huanju.edu100.study.kafka.question.KafkaMsgProducerService;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.goods.Goods;
import cn.huanju.edu100.study.model.goods.Product;
import cn.huanju.edu100.study.model.homework.Homework;
import cn.huanju.edu100.study.model.homework.trainbrush.UserTrainBrushQuestionResult;
import cn.huanju.edu100.study.model.homework.trainbrush.UserTrainBrushTaskResult;
import cn.huanju.edu100.study.model.lesson.LessonForStatsDTO;
import cn.huanju.edu100.study.model.lesson.LessonForStatsQuery;
import cn.huanju.edu100.study.model.util.UserAnswerUtils;
import cn.huanju.edu100.study.resource.GoodsCommonService;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.*;
import cn.huanju.edu100.study.service.homework.trainbrush.UserTrainBrushQuestionResultService;
import cn.huanju.edu100.study.service.homework.trainbrush.UserTrainBrushTaskResultService;
import cn.huanju.edu100.study.service.question.ErrorQuestionService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.study.util.ResourceTypeEnum;
import cn.huanju.edu100.study.util.ThreadPoolFactoryUtil;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hqwx.study.dto.UserAnswerSumMsgDTO;
import com.hqwx.study.dto.UserAutoRemoveErrorQuestionConfigDTO;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 用户作业提交proxy
 *
 * */
@Component
public class UserHomeWorkSubmitProxy{
	private Logger logger = LoggerFactory.getLogger(getClass());

	private static int numOfThreads = 2;// 默认的线程数
	private static int cpuNums = Runtime.getRuntime().availableProcessors();

	private static ThreadPoolExecutor executor = ThreadPoolFactoryUtil.createDefaultPool("userHomeWorkSubmitThreadPool");
	public final static int cachedQueueMaxSize = 6 * 10000; // 缓存待入库的学生答题队列的最大size

	public int batchNum = 1000; // 默认每次批量get的记录条数
	public long period = 1000; // 默认每次批量get的最长时间周期(单位为毫秒)

	public static LinkedBlockingQueue<UserHomeWorkCachedBean> userHomeWork_Queue = new LinkedBlockingQueue<UserHomeWorkCachedBean>(
			cachedQueueMaxSize);

	@Value("${kafka.studyerror.topic}")
	private String studyerrorTopic;
	@Value("${kafka.studyquestion.topic}")
	private String studyquestionTopic;
	@Autowired
	@Qualifier("questionStaticProducerService")
	private KafkaMsgProducerService questionStaticProducerService;
	@Autowired
	private UserAnswerSumDao userAnswerSumDao;
	@Autowired
    private UserHomeWorkAnswerDao userHomeWorkAnswerDao;
	@Autowired
	private UserAnswerDetailDao userAnswerDetailDao;
	@Autowired
	private UserQuestionService userQuestionService;
	@Autowired
	private QuestionAnswerStaticsService questionAnswerStaticsService;
	@Autowired
	private ErrorQuestionService errorQuestionService;

	@Autowired
	private UserDoneRecordDao userDoneRecordDao;
	@Autowired
	private GoodsResource goodsResource;
	@Autowired
	private UserSubErrorQuestionService userSubErrorQuestionService;
	@Autowired
	private UserCorrectQuestionService userCorrectQuestionService;
	@Autowired
	private KnowledgeResource knowledgeResource;
	@Autowired
	private UserSubErrorQuestionRemoveService userSubErrorQuestionRemoveService;
	@Autowired
	private GoodsCommonService goodsCommonService;
	@Resource
	private UserTrainBrushQuestionResultService userTrainBrushQuestionResultService;
	@Resource
	private UserTrainBrushTaskResultService userTrainBrushTaskResultService;

	@Autowired
	private UserStudyPropDao userStudyPropDao;

	@Autowired
	private ApplicationContext applicationContext;

	private static ExecutorService es = ThreadPoolFactoryUtil.createDefaultPool("homeWorkSyncQLogThreadPool");

	@Scheduled(cron = "0/3 * * * * ?")
	public void process() {//每3s钟把队列里的batchNum条提交数据取出来执行
		if (!userHomeWork_Queue.isEmpty()) {
			long startTime = System.currentTimeMillis(); // 本次批量get的开始时间

			while(true){
				List<UserHomeWorkCachedBean> list = new ArrayList<UserHomeWorkSubmitProxy.UserHomeWorkCachedBean>();
				List<Future<?>> futureList = new ArrayList<>();
				userHomeWork_Queue.drainTo(list, batchNum);
				for (UserHomeWorkCachedBean userHomeWorkCachedBean : list) {
					Future<?> result= executor.submit(userHomeWorkCachedBean);
					futureList.add(result);
				}
				int sucCount = 0;
				int failCount = 0;
				for(Future<?> result : futureList){
					try {
						result.get(60, TimeUnit.SECONDS);
						sucCount++;
					}catch (Exception e){
						logger.error("submitHomeWork userHomeWorkCachedBean process submit exception:",e);
						failCount++;
					}
				}
				logger.info("submitHomeWork userHomeWorkCachedBean process submit success:{} failed:{}", sucCount, failCount);

				long endTime = System.currentTimeMillis();
				if (userHomeWork_Queue.isEmpty() || (endTime - startTime) >= period) {
					break;
				}
			}
			logger.info("userHomeWork_Queue after process size:{}",userHomeWork_Queue.size());
		}
	}

	public void submitHomeWork(UserHomeWorkAnswer userHomeWorkAnswer, List<Question> questions) {
		try {
//			long startTime = System.currentTimeMillis();
			userHomeWork_Queue.put(new UserHomeWorkCachedBean(userHomeWorkAnswer.getUid(),userHomeWorkAnswer.getId(),userHomeWorkAnswer,questions));
//			long endTime = System.currentTimeMillis();
//			logger.info("userHomeWork_Queue submitHomeWork:time:{}",endTime - startTime);
//			logger.info("userHomeWork_Queue after submitHomeWork size:{}",userHomeWork_Queue.size());
		} catch (InterruptedException e) {
			logger.error("submitHomeWork syn2Queue InterruptedException .",e);
		}
//		executor.submit(new UserHomeWorkCachedBean(userHomeWorkAnswer.getUid(),userHomeWorkAnswer.getId(),userHomeWorkAnswer,questions));
	}


//	public void submitHomeWork(UserHomeWorkAnswer userHomeWorkAnswer, List<Question> questions) {
//		executor.submit(new UserHomeWorkCachedBean(userHomeWorkAnswer.getUid(),userHomeWorkAnswer.getId(),userHomeWorkAnswer,questions));
//	}

	class UserHomeWorkCachedBean implements Callable<Void>{
		private Long uid;
		private Long userHomeWorkId;

		private UserHomeWorkAnswer userHomeWorkAnswer;
		private List<Question> questions;

		public UserHomeWorkCachedBean(Long uid,Long userHomeWorkId,UserHomeWorkAnswer userHomeWorkAnswer,List<Question> questions) {
			this.uid = uid;
			this.userHomeWorkId = userHomeWorkId;
			this.userHomeWorkAnswer = userHomeWorkAnswer;
			this.questions = questions;
		}

		public Long getUid() {
			return uid;
		}
		public void setUid(Long uid) {
			this.uid = uid;
		}
		public Long getUserHomeWorkId() {
			return userHomeWorkId;
		}
		public void setUserHomeWorkId(Long userHomeWorkId) {
			this.userHomeWorkId = userHomeWorkId;
		}
		public UserHomeWorkAnswer getUserHomeWorkAnswer() {
			return userHomeWorkAnswer;
		}
		public void setUserHomeWorkAnswer(UserHomeWorkAnswer userHomeWorkAnswer) {
			this.userHomeWorkAnswer = userHomeWorkAnswer;
		}
		public List<Question> getQuestions() {
			return questions;
		}
		public void setQuestions(List<Question> questions) {
			this.questions = questions;
		}

		@Override
		public Void call() throws Exception {
			logger.info("start execute UserHomeWorkCachedBean, uid:{},userHomeWorkId:{} ",uid, userHomeWorkAnswer.getId());
			try {
				executeUserHomeWorkCachedBean();
			}catch (Exception e){
				logger.error("execute UserHomeWorkCachedBean, uid:{},userHomeWorkId:{} error:", uid, userHomeWorkAnswer.getId(), e);
			}

			logger.info("end execute UserHomeWorkCachedBean, uid:{},userHomeWorkId:{}",uid, userHomeWorkAnswer.getId());
			return null;
		}

		private void executeUserHomeWorkCachedBean() throws DataAccessException {
			List<QuestionAnswerDetail> questionAnswerDetailList = new ArrayList<QuestionAnswerDetail>();
			Collection<UserAnswerDetail> answerDetails = userHomeWorkAnswer.getAnswerDetail();

			//如果是课后作业 需要对科目，考试，考试大类进行区分 或者课前练习
			if(userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON ||
					userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.ORDINARY_VIDEO_QUESTION_WORK){
				Long productId = userHomeWorkAnswer.getProductId();
				if(IdUtils.isValid(productId)){
					List<Long> prodIdList = Lists.newArrayList();
					prodIdList.add(productId);
					Map<Long, Product> productIdMap = goodsResource.getProductsByIdList(prodIdList);
					Product product = productIdMap.get(productId);
					if (product != null) {
						userHomeWorkAnswer.setFirstCategoryId(product.getFirstCategory());
						userHomeWorkAnswer.setSecondCategoryId(product.getSecondCategory());
						if(userHomeWorkAnswer.getCategoryId() == null || userHomeWorkAnswer.getCategoryId()<=0){
							userHomeWorkAnswer.setCategoryId(product.getCategoryId());
						}
					}
					if(product != null && !IdUtils.isValid(userHomeWorkAnswer.getHomeworkId())){
						Integer homeworkType = Consts.HomeworkConfigType.ORDINARY_HOMEWORK;
						if(userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.ORDINARY_VIDEO_QUESTION_WORK){
							homeworkType = Consts.HomeworkConfigType.PRECLASS_HOMEWORK;
						}
						Homework homework = goodsResource.getHomeworkByObjAndTypeInfo(userHomeWorkAnswer.getObjId(),product.getType(),homeworkType);
						if(homework!=null){
							userHomeWorkAnswer.setHomeworkId(homework.getId());
						}
					}
				}
			}

			if(UserHomeWorkAnswer.HomeWorkType.CUSTOM_PAPER_WORK == userHomeWorkAnswer.getObjType()){
				List<UserAnswerDetail> answerDetailList = userHomeWorkAnswer.getAnswerDetail();
				if(CollectionUtils.isNotEmpty(answerDetailList)){
					userHomeWorkAnswer.setQuestionId(answerDetailList.get(0).getQuestionId());
				}else{
					logger.error("submitHomeWork CUSTOM_PAPER_WORK why answerDetailList is empty, userHomeWorkAnswer:{}",userHomeWorkAnswer);
				}
			}

			Integer isAl = null;//是否云私塾 0否 1是
			if (UserHomeWorkAnswer.HomeWorkType.DAILY_WORK == userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.STUDY_WORK == userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.REVIEW_WORK == userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.PARAGRAPH_WORK == userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.AL_CHAPTER_WORK == userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.AL_CUSTOM_WORK == userHomeWorkAnswer.getObjType()
			    	|| UserHomeWorkAnswer.HomeWorkType.AL_KNOWLEDGE_WORK== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.AL_DIAGNOSTIC_TEST==userHomeWorkAnswer.getObjType()
			    	|| UserHomeWorkAnswer.HomeWorkType.PRE_CLASS_HOMEWORK== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.STUDY_WORK_MIX== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.STUDY_WORK_ENHANCE== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.AL_TRAIN_BRUSH_QUESTION == userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.FORGETTING_CONSOLIDATE_REVIEW== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.EXEMPLARY_ANALOGOUS== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.SPECIAL_EXERCISE== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.HIGH_FREQUENCY_ERROR_QUESTION== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.CUSTOM_QUESTION== userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.LESSON_QUESTION== userHomeWorkAnswer.getObjType()
			) {
				isAl = 1;
			}
			if (UserHomeWorkAnswer.HomeWorkType.SPECIAL_EXERCISE== userHomeWorkAnswer.getObjType() && Objects.isNull(userHomeWorkAnswer.getCategoryId())) {
				userHomeWorkAnswer.setCategoryId(userHomeWorkAnswer.getObjId());
			}

			for (Question question : questions) {
				//初始化答题记录Bean
				QuestionAnswerDetail questionAnswerDetail = new QuestionAnswerDetail();
				questionAnswerDetail.setUid(userHomeWorkAnswer.getUid());
				questionAnswerDetail.setQuestionId(question.getId());
				questionAnswerDetail.setSchId(1L);
				questionAnswerDetail.setAnswerTime(new Date());

	            List<UserAnswerSum> oldAnswerSums = findByUserHomeworkIdWithDetails(uid, userHomeWorkAnswer.getId(), question.getId());
	            UserAnswerSum answerSum;
				UserAnswerSum oldUserAnswerSum = null;//旧的UserAnswerSum记录
	            if (oldAnswerSums.size() == 0) {
	                answerSum = new UserAnswerSum();
	                answerSum.setUid(userHomeWorkAnswer.getUid());
	                answerSum.setUserHomeworkId(userHomeWorkAnswer.getId());
	                answerSum.setQuestionId(question.getId());
	                answerSum.setScore(0d);
	                answerSum.setState(UserAnswerSum.State.DONE);
					if (isAl != null) {
						answerSum.setIsAl(isAl);
					}
//                    Long id = userAnswerSumDao.insert(answerSum);
//                    answerSum.setId(id);// 灰度期间两边都插入
	                userAnswerSumDao.insertSharding(answerSum, answerSum.getClass());
	            } else {
	                answerSum = oldAnswerSums.get(0);
					oldUserAnswerSum = oldAnswerSums.get(0);
	            }
	            Collection<QuestionTopic> topics = question.getTopicList();
				List<UserAnswerDetail> answerDetailListTmp = Lists.newArrayList();//云企培2.0 临时暂存一下答题详情
	            if (CollectionUtils.isNotEmpty(topics)) {
	                double sumTotalScore = 0;
	                int emptyCount = 0;//记录一道大题里未作答的小题数量
	                for (QuestionTopic topic : topics) {
	                    UserAnswerDetail answerDetail = null;
	                    for (UserAnswerDetail answerDetailItem : answerDetails) {
	                        if (topic.getId().equals(answerDetailItem.getTopicId())) {
	                            answerDetail = answerDetailItem;
	                            break;
	                        }
	                    }
	                    if (answerDetail == null) {
	                        continue;
	                    }
						setIdUserAnswerDetail(oldUserAnswerSum, answerDetail);

						answerDetail.setAnswerStr(GsonUtil.toJson(answerDetail.getAnswer()));
	                    int isRight = answerDetail.getIsRight();
	                    Double score = answerDetail.getScore();
	                    answerDetail.setUid(userHomeWorkAnswer.getUid());
	                    answerDetail.setSumId(answerSum.getId());
	                    sumTotalScore += score;

						if (answerDetail.getId() != null && answerDetail.getId()!=0) {
							userAnswerDetailDao.updateSharding(answerDetail);
						} else {
							answerDetail.setId(null);
//                            Long id = userAnswerDetailDao.insert(answerDetail);
//                            answerDetail.setId(id);// TODO:灰度期间两边都插入
							userAnswerDetailDao.insertSharding(answerDetail, answerDetail.getClass());
						}

						answerDetail.setUserHomeworkId(userHomeWorkAnswer.getId());

						try{
							/*错题处理*/
							if (isAl != null && isAl == 1) {//云私塾
								this.addUserErrorQuestionRecordAl(topic, isRight, answerDetail);
							} else if (userHomeWorkAnswer.getObjType() != null && userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.AL_TRAIN_BRUSH_QUESTION) {
								Long goodsId = userHomeWorkAnswer.getGoodsId();
								if (goodsId != null && goodsId.longValue() > 0) {//这个判断目前只能写在外层
									this.addUserErrorQuestionRecordAl(topic, isRight, answerDetail);
								}
							} else {
								this.addUserErrorQuestionRecord(topic, isRight, answerDetail);
							}
						}catch (Exception e){
							logger.error("添加错题失败！param:{} ", GsonUtil.toJson(answerDetail));
						}
						answerDetailListTmp.add(answerDetail);
					}

					Integer isQuestionRight = UserAnswerUtils.getIsQuestionRight(answerDetailListTmp);
					if (isQuestionRight != UserAnswerDetail.IsRight.NOT_ANSWER) {
						questionAnswerDetail.setState(isQuestionRight);
						questionAnswerDetailList.add(questionAnswerDetail);
					}
					if (answerSum.getScore().doubleValue() != sumTotalScore /**|| emptyCount > 0*/ ) {
						//重新算分，每次提交都假定前台把每个小题的id都传过来了
						answerSum.setScore(sumTotalScore);
						userAnswerSumDao.updateSharding(answerSum);
					}
                    Long schId = userHomeWorkAnswer.getSchId();
					if (schId != null && schId.equals(Consts.SchId.YQP)) {//云企培机构的数据才进行操作
						sendMsgUserQuestion(answerSum.getId(), answerSum.getQuestionId(), isQuestionRight, answerDetailListTmp, question.getQtype());
					}
				}
			}
			//用户提交的时候才计算正确率
			if (userHomeWorkAnswer.getState() == UserAnswer.State.SUBMITTED) {
				//添加正确率计算,保留三位小数
				DecimalFormat decimalFormat = new DecimalFormat("0.000");
				DecimalFormat decimalFormat2 = new DecimalFormat("0.0000");
				long rightCount = questionAnswerDetailList.stream().filter(x -> x.getState() == UserAnswerDetail.IsRight.RIGHT).count();
				String accuracy = decimalFormat2.format(Double.valueOf(rightCount) / questions.size());
				BigDecimal accuracyBigDecimal=new BigDecimal(accuracy);
				//向上取3位小数点
				accuracy=decimalFormat.format(accuracyBigDecimal.setScale(3,BigDecimal.ROUND_UP));
				Map<String, String> map = Maps.newHashMap();
				map.put("accuracy", accuracy);
				Long relationId = userHomeWorkAnswer.getRelationId();
				if (relationId != null && relationId>0) {
					map.put("relationId", relationId.toString());
				}
				userHomeWorkAnswer.setComment(GsonUtil.toJson(map));
				long answerNum = questionAnswerDetailList.size();
				userHomeWorkAnswer.setAnswerNum(answerNum);
				userHomeWorkAnswerDao.updateSharding(userHomeWorkAnswer);

				/**新增学习属性记录*/
				this.addUserStudyPropRecord(questionAnswerDetailList);

				/*如果是刷题任务类型 则进行刷题记录做统计*/
				if(UserHomeWorkAnswer.HomeWorkType.AL_TRAIN_BRUSH_QUESTION == userHomeWorkAnswer.getObjType()){
					addUserTrainBrushQuesionResult(userHomeWorkAnswer, uid, accuracy, questionAnswerDetailList);
					addUserTrainBrushTaskResult(userHomeWorkAnswer, uid, accuracy,questionAnswerDetailList);
				}
			}
			/**新增做题记录*/
			if ((isAl != null && isAl == 1)) {//云私塾
				this.addUserDoneRecordAl(isAl);
			}/* else if (userHomeWorkAnswer.getObjType() != null && userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.AL_TRAIN_BRUSH_QUESTION) {
				this.addUserDoneRecordAl(0);
			} */else {
				this.addUserDoneRecord(questions);
			}

			syncQuestionLog(questionAnswerDetailList);
			applicationContext.publishEvent(new HomeworkSubmittedEvent(this, userHomeWorkAnswer));
		}

		private void addUserCorrectQuestionRecord(QuestionTopic topic, UserAnswerDetail answerDetail)throws Exception {
			Integer doneSource = userHomeWorkAnswer.getDoneSource();
			if (doneSource == null || doneSource != UserHomeWorkAnswer.DoneSource.AlError) {//错题本纠错才插入
				return;
			}
			UserSubErrorQuestion userSubErrorQuestion = userSubErrorQuestionService.getByParam(uid, userHomeWorkAnswer.getGoodsId(),userHomeWorkAnswer.getCategoryId(),null,
					null,answerDetail.getQuestionId(),answerDetail.getTopicId());
			if(Objects.nonNull(userSubErrorQuestion)){//错题本有记录才插入
				addUserCorrectQuestion(answerDetail,topic.getQtype());
			}
		}

		private void addUserCorrectQuestion(UserAnswerDetail answerDetail, Integer topicQuestionType) throws Exception{
			Date now = new Date();
			UserCorrectQuestion userCorrectQuestion = new UserCorrectQuestion();
			userCorrectQuestion.setUid(uid);
			Long productId = userHomeWorkAnswer.getProductId();
			userCorrectQuestion.setProductId(productId);
			userCorrectQuestion.setCategoryId(userHomeWorkAnswer.getCategoryId());
			userCorrectQuestion.setGoodsId(userHomeWorkAnswer.getGoodsId());
			userCorrectQuestion.setLessonId(userHomeWorkAnswer.getObjId());
			userCorrectQuestion.setQuestionId(answerDetail.getQuestionId());
			userCorrectQuestion.setTopicId(answerDetail.getTopicId());
			userCorrectQuestion.setAnswerId(userHomeWorkAnswer.getId());
			userCorrectQuestion.setLastAnswerId(userHomeWorkAnswer.getId());
			userCorrectQuestion.setQtype(topicQuestionType);
			userCorrectQuestion.setUpdateDate(now);
			userCorrectQuestion.setCreateDate(now);
			userCorrectQuestionService.saveUserCorrectQuestion(userCorrectQuestion);
		}

		private void removeUserCorrectQuestion(UserHomeWorkAnswer userHomeWorkAnswer,UserAnswerDetail answerDetail) {
			Map<String, Object> params = new HashMap<>();
			params.put("uid", uid);
			params.put("categoryId", userHomeWorkAnswer.getCategoryId());
//			params.put("goodsId", userHomeWorkAnswer.getGoodsId());
			params.put("questionId", answerDetail.getQuestionId());
			try{
				userCorrectQuestionService.removeCorrectQuestionByCategory(params);
			}catch (Exception e){
				logger.error("removeUserCorrectQuestionByCategory error", e);
			}
		}

		private void sendMsgUserQuestion(Long answerSumId, Long questionId, Integer isQuestionRight, List<UserAnswerDetail> answerDetailListTmp, Integer qtype) {
			try {
				if (isQuestionRight == null || isQuestionRight == UserAnswerDetail.IsRight.NOT_ANSWER) {
					logger.info("sendMsgUserQuestion isQuestionRight is null");
					return;
				}
				Long goodsId = userHomeWorkAnswer.getGoodsId();
				if (goodsId == null || goodsId.longValue() <= 0) {
					return;
				}
				Goods goods = goodsResource.getGoodsById(goodsId);
				if (goods == null) {
					return;
				}
				Integer sourceFlag = goods.getSourceFlag();
				Long tenantId = goods.getTenantId();
				if (sourceFlag == null) {
					logger.info("sendMsgUserQuestion 非自建课程. ");
					return;
				}
				if (tenantId == null) {
					logger.info("sendMsgUserQuestion 非自建课程. 没有tenantId值。");
					return;
				}
				Long uid = userHomeWorkAnswer.getUid();
				Long schId = userHomeWorkAnswer.getSchId();
				Long productId = userHomeWorkAnswer.getProductId();
				Integer objType = userHomeWorkAnswer.getObjType();
				Long objId = userHomeWorkAnswer.getObjId();
				Long relationId = userHomeWorkAnswer.getRelationId();
				UserAnswerSumMsgDTO dto = new UserAnswerSumMsgDTO();
				dto.setUserAnswerSumId(answerSumId);
				dto.setQuestionId(questionId);
				dto.setQuestionType(qtype);
				dto.setUid(uid);
				dto.setIsRight(isQuestionRight);
				dto.setSchId(schId);
				dto.setGoodsId(goodsId);
				dto.setProductId(productId);
				dto.setObjId(objId);
				dto.setObjType(objType);
				dto.setRelationId(relationId);
				dto.setCreateDate(new Date());
				dto.setType(1);// 0-试卷 1-作业
				dto.setTenantId(tenantId);
				dto.setGoodsSourceFlag(sourceFlag);
				if (CollectionUtils.isNotEmpty(answerDetailListTmp)) {
					String answerStr = answerDetailListTmp.get(0).getAnswerStr();
					dto.setAnswer(answerStr);
				}
				String msg = GsonUtil.getGenericGson().toJson(dto);
				questionStaticProducerService.sndMesForTemplate(studyquestionTopic, msg, "", null, studyquestionTopic);
			} catch (Exception e) {
				logger.error("sendMsgUserQuestion fail. ", e);
			}
		}

		/**
		 * 刷题记录进入刷题统计表
		 */
		private void addUserTrainBrushQuesionResult(UserHomeWorkAnswer userHomeWorkAnswer, Long uid, String accuracy, List<QuestionAnswerDetail> questionAnswerDetailList){
			Date createDate = userHomeWorkAnswer.getCreateDate();
			if(createDate == null){
				createDate = new Date();
			}
			UserTrainBrushQuestionResult brushQuestionResult = new UserTrainBrushQuestionResult();
			brushQuestionResult.setUid(uid);
			brushQuestionResult.setWxGroupId(userHomeWorkAnswer.getObjId());
			brushQuestionResult.setWxBrushTaskId(userHomeWorkAnswer.getTaskId());
			if(StringUtils.isNotEmpty(accuracy)){
				brushQuestionResult.setAccuracy(Double.valueOf(accuracy));
			}
			brushQuestionResult.setUsetime(userHomeWorkAnswer.getUsetime());
			brushQuestionResult.setAnswerNum(userHomeWorkAnswer.getAnswerNum());
			brushQuestionResult.setStartTime(userHomeWorkAnswer.getStartTime());
			brushQuestionResult.setEndTime(userHomeWorkAnswer.getEndTime());
			brushQuestionResult.setState(userHomeWorkAnswer.getState());
			brushQuestionResult.setCreateDate(createDate);
			brushQuestionResult.setAppid(userHomeWorkAnswer.getAppid());
			brushQuestionResult.setPlatForm(userHomeWorkAnswer.getPlatForm());
			brushQuestionResult.setDelFlag(0);
			brushQuestionResult.setUserAnswerHomeworkId(userHomeWorkAnswer.getId());
			if(CollectionUtils.isNotEmpty(questionAnswerDetailList)){
				String answerQuestionIds = questionAnswerDetailList.stream().map(QuestionAnswerDetail::getQuestionId)
						.filter(Objects::nonNull).distinct().map(String::valueOf).collect(Collectors.joining(","));
				brushQuestionResult.setAnswerQuestionIds(answerQuestionIds);

				List<QuestionAnswerDetail> rightQuestionAnswerDetailList =
						questionAnswerDetailList.stream().filter(x -> x.getState() == UserAnswerDetail.IsRight.RIGHT).collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(rightQuestionAnswerDetailList)){
					String rightAnswerQuestionIds = rightQuestionAnswerDetailList.stream().map(QuestionAnswerDetail::getQuestionId)
							.filter(Objects::nonNull).distinct().map(String::valueOf).collect(Collectors.joining(","));
					brushQuestionResult.setRightQuestionIds(rightAnswerQuestionIds);
				}
			}

			userTrainBrushQuestionResultService.insert(brushQuestionResult);
		}

		private void addUserTrainBrushTaskResult(UserHomeWorkAnswer userHomeWorkAnswer, Long uid, String accuracy,List<QuestionAnswerDetail> questionAnswerDetailList){
			Date createDate = userHomeWorkAnswer.getCreateDate();
			if(createDate == null){
				createDate = new Date();
			}
			UserTrainBrushTaskResult userTrainBrushTaskResult = new UserTrainBrushTaskResult();
			userTrainBrushTaskResult.setUid(uid);
			userTrainBrushTaskResult.setGroupId(userHomeWorkAnswer.getObjId());
			userTrainBrushTaskResult.setTaskId(userHomeWorkAnswer.getTaskId());
			userTrainBrushTaskResult.setCreateDate(createDate);
			userTrainBrushTaskResult.setUpdateDate(createDate);
			userTrainBrushTaskResult.setTaskType(UserTrainBrushTaskResult.TaskTypeEnum.QUESTION.getValue());
			Date startTime = userHomeWorkAnswer.getStartTime();
			Date endTime = userHomeWorkAnswer.getEndTime();
			if(startTime != null && endTime != null){
				long duration = endTime.getTime() - startTime.getTime();
				int durationInSeconds = (int) TimeUnit.MILLISECONDS.toSeconds(duration);
				userTrainBrushTaskResult.setStudyDuration(durationInSeconds);
			}
			if(CollectionUtils.isNotEmpty(questionAnswerDetailList)){
				Integer completeNum = 0;
				Integer submitNum = 0;
				StringBuilder correctIdsBuilder = new StringBuilder();
				StringBuilder allIdsBuilder = new StringBuilder();

				for (QuestionAnswerDetail detail : questionAnswerDetailList) {
					if (detail == null) continue;
					Long detailQuestionId = detail.getQuestionId();
					Integer detailState = detail.getState();

					if (detailQuestionId == null || detailState == null) continue;

					if (allIdsBuilder.length() > 0) {
						allIdsBuilder.append(",");
					}
					allIdsBuilder.append(detailQuestionId);
					submitNum ++;
					if (detailState == UserAnswerDetail.IsRight.RIGHT) {
						completeNum++;
						if (correctIdsBuilder.length() > 0) {
							correctIdsBuilder.append(",");
						}
						correctIdsBuilder.append(detailQuestionId);
					}
				}
				/**
				 * complete_ids 正确题目id
				 * submit_ids  提交题目id
				 * accuracy 正确率，
				 * complete_num 正确题目数
				 * submit_num 提交题目数
				 */
				if(StringUtils.isNotEmpty(accuracy)){
					userTrainBrushTaskResult.setAccuracy(Double.valueOf(accuracy));
				}
				userTrainBrushTaskResult.setCompleteIds(correctIdsBuilder.toString());
				userTrainBrushTaskResult.setCompleteNum(completeNum);
				userTrainBrushTaskResult.setSubmitIds(allIdsBuilder.toString());
				userTrainBrushTaskResult.setSubmitNum(submitNum);
				userTrainBrushTaskResult.setCompleteType(3);
				userTrainBrushTaskResult.setLastStudyTime(createDate);
			}
			List<UserTrainBrushTaskResult> userTrainBrushTaskResultList = userTrainBrushTaskResultService.getByParams(userTrainBrushTaskResult);
			if(CollectionUtils.isEmpty(userTrainBrushTaskResultList)){
				userTrainBrushTaskResultService.insert(userTrainBrushTaskResult);
			}else{
				userTrainBrushTaskResult.setId(userTrainBrushTaskResultList.get(0).getId());
				userTrainBrushTaskResult.setCreateDate(userTrainBrushTaskResultList.get(0).getCreateDate());
				userTrainBrushTaskResultService.updateById(userTrainBrushTaskResult);
			}
		}

		/**
		 * 答题数据进入错题集
		 */
		private void addUserErrorQuestionRecord(QuestionTopic topic, int isRight, UserAnswerDetail answerDetail) throws Exception {
			//update by linyl 20160301
			if(userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON ||
					(userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.KNOWLEDGE_WORK
							&& IdUtils.isValid(userHomeWorkAnswer.getObjId()))){
				handleStudyCenterQuestionAnswer(topic,isRight,answerDetail);
			} else if (userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.M_CLASS
					|| UserHomeWorkAnswer.HomeWorkType.CUSTOM_WORK == userHomeWorkAnswer.getObjType()
					|| UserHomeWorkAnswer.HomeWorkType.ORDINARY_VIDEO_QUESTION_WORK == userHomeWorkAnswer.getObjType()) {
				return;
			} else if (topic.getQtype() != Consts.Question_QType.TIAN_KONG && topic.getQtype() != Consts.Question_QType.WEN_DA) {
				dealWithErrorQuestion(topic,isRight,answerDetail);
			}
		}

		private void removeUserErrorQuestion(UserAnswerDetail answerDetail) throws DataAccessException {
			UserErrorQuestion errorQuestion = new UserErrorQuestion();
			if (userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON) {
				errorQuestion.setLessonId(userHomeWorkAnswer.getObjId());
			} else if (userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.PARAGRAPH) {
				errorQuestion.setParagraphId(userHomeWorkAnswer.getObjId());
			}
			errorQuestion.setObjId(userHomeWorkAnswer.getObjId());
			errorQuestion.setObjType(userHomeWorkAnswer.getObjType());
			errorQuestion.setUid(userHomeWorkAnswer.getUid());
			errorQuestion.setQuestionId(answerDetail.getQuestionId());
			errorQuestion.setLastErrorTime(new Date());
			errorQuestion.setLastErrorAnswer(Arrays.toString(answerDetail.getAnswer()));
			errorQuestion.setTopicId(answerDetail.getTopicId());
			userQuestionService.removeErrorQuestion(errorQuestion);
		}

		private void addUserErrorQuestionRecordAl(QuestionTopic topic, int isRight, UserAnswerDetail answerDetail) throws Exception {//增加云私塾的错题信息
			if (UserHomeWorkAnswer.HomeWorkType.AL_CUSTOM_WORK == userHomeWorkAnswer.getObjType()) {
				return;
			}
			if (topic.getQtype() != Consts.Question_QType.TIAN_KONG && topic.getQtype() != Consts.Question_QType.WEN_DA) {
				dealWithErrorQuestion(topic, isRight, answerDetail);
			}

		}

		private void dealWithErrorQuestion(QuestionTopic topic, int isRight, UserAnswerDetail answerDetail) throws Exception {
			if (isRight == UserAnswerDetail.IsRight.WRONG || isRight == UserAnswerDetail.IsRight.HALF_RIGHT) {//填空题和简答题不判分，故不计入错题中 && 微课做题也不计入错题集

				//20250523新逻辑，需要做对指定（用户设置）次数，才能移除
				userSubErrorQuestionRemoveService.decreaseRightTimes(uid, answerDetail.getTopicId());
				addUserSubErrorQuestion(answerDetail, topic.getQtype());
				addUserErrorQuestion(answerDetail);//提交作业时保存错题； 双写操作，等app端逐渐升级上来，这个代码可注释掉
			}
			else if (isRight == UserAnswerDetail.IsRight.RIGHT) {
				Long rightTimes = userSubErrorQuestionRemoveService.increaseRightTimes(uid, answerDetail.getTopicId());
				this.addUserCorrectQuestionRecord(topic, answerDetail);


				//20250523新逻辑，需要做对指定（用户设置）次数，才能移除
				Integer doneSource = userHomeWorkAnswer.getDoneSource();
				Integer isOneErrorList = userHomeWorkAnswer.getIsOnErrorList();
				if ((Objects.equals(doneSource, UserHomeWorkAnswer.DoneSource.AlError)||Objects.equals(isOneErrorList,1))){
					UserAutoRemoveErrorQuestionConfigDTO config = errorQuestionService.getUserAutoRemoveErrorQuestionConfig(uid);
					 if (config.getAutoRemoveErrorQuestionFlag() == 1 && (Objects.isNull(config.getRightTimes())?1:config.getRightTimes()) <= rightTimes) {
						removeUserSubErrorQuestion(answerDetail);
						removeUserErrorQuestion(answerDetail);//双删操作，等app端逐渐升级上来，这个代码可注释掉
					}
				}

			}
		}

		private void removeUserSubErrorQuestion(UserAnswerDetail answerDetail) throws DataAccessException {
			UserSubErrorQuestion userSubErrorQuestion = new UserSubErrorQuestion();
			userSubErrorQuestion.setUid(userHomeWorkAnswer.getUid());
			userSubErrorQuestion.setQuestionId(answerDetail.getQuestionId());
			userSubErrorQuestion.setTopicId(answerDetail.getTopicId());
			userSubErrorQuestionService.removeUserSubErrorQuestion(userSubErrorQuestion);
			Long schId = userHomeWorkAnswer.getSchId();
			if (schId != null && schId.equals(Consts.SchId.YQP)) {//云企培机构的数据才进行操作
				userSubErrorQuestion.setGoodsId(userHomeWorkAnswer.getGoodsId());
				userSubErrorQuestion.setProductId(userHomeWorkAnswer.getProductId());
				userSubErrorQuestion.setLessonId(userHomeWorkAnswer.getObjId());
				userSubErrorQuestion.setCategoryId(userHomeWorkAnswer.getCategoryId());
				userSubErrorQuestion.setAnswerId(userHomeWorkAnswer.getId());
				sendMsgUserSubErrorQuestion(userSubErrorQuestion, Consts.AddErrorQuestionOptType.NO, schId);
			}
		}

		private void addUserErrorQuestion(UserAnswerDetail answerDetail) throws DataAccessException {
			UserErrorQuestion errorQuestion = new UserErrorQuestion();
			if (userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON) {
				errorQuestion.setLessonId(userHomeWorkAnswer.getObjId());
			} else if (userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.PARAGRAPH) {
				errorQuestion.setParagraphId(userHomeWorkAnswer.getObjId());
			}
			errorQuestion.setObjId(userHomeWorkAnswer.getObjId());
			errorQuestion.setObjType(userHomeWorkAnswer.getObjType());
			errorQuestion.setGoodsId(userHomeWorkAnswer.getGoodsId());
			errorQuestion.setProductId(userHomeWorkAnswer.getProductId());
			errorQuestion.setUid(userHomeWorkAnswer.getUid());
			errorQuestion.setQuestionId(answerDetail.getQuestionId());
			errorQuestion.setLastErrorTime(new Date());
			errorQuestion.setLastErrorAnswer(Arrays.toString(answerDetail.getAnswer()));
			errorQuestion.setTopicId(answerDetail.getTopicId());
			userQuestionService.saveErrorQuestion(errorQuestion);
		}

		/**
		 * 保存学习属性记录
		 * @param questionAnswerDetailList
		 */
		private void addUserStudyPropRecord(List<QuestionAnswerDetail> questionAnswerDetailList) throws DataAccessException {
			if (userHomeWorkAnswer.getHomeworkId() == null){
				return;
			}
			Homework homework = goodsResource.getHomeworkById(userHomeWorkAnswer.getHomeworkId());
			if ( homework != null
					&& homework.getProductType().equals(Consts.ProductType.PRODUCT_SCHEDULE)
					&& userHomeWorkAnswer.getState() == UserAnswer.State.SUBMITTED){

				//查询课节
				LessonForStatsQuery lessonForStatsQuery = new LessonForStatsQuery();
				lessonForStatsQuery.setId(homework.getObjId());
				//只查课程表排课的课节
				List<LessonForStatsDTO> lessons = goodsCommonService.getLessons(lessonForStatsQuery);
				if (CollectionUtils.isEmpty(lessons)){
					return;
				}
				LessonForStatsDTO lessonForStatsDTO = lessons.get(0);
				//课节类型
				String relationType = lessonForStatsDTO.getRelationType();

				//作业课节
				if (StringUtils.equals(ResourceTypeEnum.LESSON_WORK.getTypeCode(),relationType)) {
					if (CollectionUtils.isEmpty(userHomeWorkAnswer.getAnswerDetail())){
						return;
					}
					List<String[]> picList = userHomeWorkAnswer.getAnswerDetail().stream().map(UserAnswerDetail::getAnswer).collect(Collectors.toList());
					int picCount = 0;
					if (CollectionUtils.isNotEmpty(picList)){
						for (String[] picArr : picList) {
							if (picArr == null || picArr.length ==0){
								continue;
							}
							for (String picJson : picArr) {
								if (StringUtils.isNotBlank(picJson)){
									picCount++;
								}
							}
						}
					}

					//作业课节上传过图片即获得3颗星
					int lessonWorkScore = picCount > 0 ? 3 : 0;

					this.saveUserStudyPropRecord(lessonForStatsDTO.getId(),relationType,lessonWorkScore);

				} else {

					//做完试卷获得1个星星，试卷正确率为100%获得1个星星
					long rightCount = questionAnswerDetailList.stream().filter(x -> x.getState() == com.hqwx.study.entity.UserAnswerDetail.IsRight.RIGHT).count();
					int lessonWorkScore = 1;
					if (rightCount >= questions.size()){
						lessonWorkScore = lessonWorkScore + 1;
					}

					this.saveUserStudyPropRecord(lessonForStatsDTO.getId(),relationType,lessonWorkScore);
				}
			}
		}

		private void saveUserStudyPropRecord(Long lessonId, String lessonType, int lessonWorkScore) throws DataAccessException {
			UserStudyProp userStudyProp = userStudyPropDao.selectOneMaster(uid, lessonId, lessonType);
			logger.info("userStudyPropDao.selectOneMaster id:{}",Optional.ofNullable(userStudyProp).map(UserStudyProp::getId).orElse(null));
			if (userStudyProp == null){
				UserStudyProp userStudyPropNew = new UserStudyProp();
				userStudyPropNew.setUid(uid);
				userStudyPropNew.setProductId(userHomeWorkAnswer.getProductId());
				userStudyPropNew.setLessonId(lessonId);
				userStudyPropNew.setGoodsId(userHomeWorkAnswer.getGoodsId());
				userStudyPropNew.setLessonWorkScore(lessonWorkScore);
				userStudyPropNew.setLessonType(lessonType);
				userStudyPropNew.setUpdateDate(new Date());
				long id = userStudyPropDao.insertSharding(userStudyPropNew, userStudyPropNew.getClass());
				logger.info("userStudyPropDao.insertSharding id:{}",id);
			} else {
				userStudyProp.setLessonWorkScore(lessonWorkScore);
				userStudyProp.setUpdateDate(new Date());
				boolean result = userStudyPropDao.updateSharding(userStudyProp);
				logger.info("userStudyPropDao.updateSharding result:{}",result);
			}
		}


		private void handleStudyCenterQuestionAnswer(QuestionTopic topic,int isRight,UserAnswerDetail answerDetail ) throws Exception {
			if (topic.getQtype() != Consts.Question_QType.WEN_DA) {
				if (isRight == UserAnswerDetail.IsRight.WRONG || isRight == com.hqwx.study.entity.UserAnswerDetail.IsRight.HALF_RIGHT) {//填空题和简答题不判分，故不计入错题中 && 微课做题也不计入错题集
					userSubErrorQuestionRemoveService.decreaseRightTimes(uid, answerDetail.getTopicId());
					//提交作业时保存错题
					addUserErrorQuestion(answerDetail);

					//新增错题至学习中心错题库
					if(userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON ||
							userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.KNOWLEDGE_WORK ||
							userHomeWorkAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.BOX_HOMEWORK
					){
						addUserSubErrorQuestion(answerDetail,topic.getQtype());
					}
				}else if (isRight == UserAnswerDetail.IsRight.RIGHT ){
					Long rightTimes = userSubErrorQuestionRemoveService.increaseRightTimes(uid, answerDetail.getTopicId());
					this.addUserCorrectQuestionRecord(topic,answerDetail);

					//20250523新逻辑，需要做对指定（用户设置）次数，才能移除
					Integer doneSource = userHomeWorkAnswer.getDoneSource();
					Integer isOneErrorList = userHomeWorkAnswer.getIsOnErrorList();
					if ((Objects.equals(doneSource, UserHomeWorkAnswer.DoneSource.AlError)||Objects.equals(isOneErrorList,1))){
						UserAutoRemoveErrorQuestionConfigDTO config = errorQuestionService.getUserAutoRemoveErrorQuestionConfig(uid);
						if (config.getAutoRemoveErrorQuestionFlag() == 1 && (Objects.isNull(config.getRightTimes())?1:config.getRightTimes()) <= rightTimes) {
							removeUserSubErrorQuestion(answerDetail);
							removeUserErrorQuestion(answerDetail);//双删操作，等app端逐渐升级上来，这个代码可注释掉
						}
					}
				}
			}
		}

		private void addUserSubErrorQuestion(UserAnswerDetail answerDetail, Integer topicQuestionType) throws Exception{
			Date nowDate = new Date();
			UserSubErrorQuestion userSubErrorQuestion = new UserSubErrorQuestion();
			userSubErrorQuestion.setUid(uid);
			Long productId = userHomeWorkAnswer.getProductId();
			userSubErrorQuestion.setProductId(productId);
			if (IdUtils.isValid(productId)) {
				List<Long> prodIdList = Lists.newArrayList();
				prodIdList.add(productId);
				Map<Long, Product> productIdMap = goodsResource.getProductsByIdList(prodIdList);
				if (productIdMap != null && productIdMap.get(productId) != null) {
					userSubErrorQuestion.setProductType(productIdMap.get(productId).getType());
				}
			}
			userSubErrorQuestion.setCategoryId(userHomeWorkAnswer.getCategoryId());
			userSubErrorQuestion.setGoodsId(userHomeWorkAnswer.getGoodsId());
			userSubErrorQuestion.setLessonId(userHomeWorkAnswer.getObjId());
			userSubErrorQuestion.setQuestionId(answerDetail.getQuestionId());
			userSubErrorQuestion.setTopicId(answerDetail.getTopicId());
			userSubErrorQuestion.setAnswerId(userHomeWorkAnswer.getId());
			userSubErrorQuestion.setLastErrorTime(nowDate);
			userSubErrorQuestion.setLastErrorAnswer(Arrays.toString(answerDetail.getAnswer()));
			userSubErrorQuestion.setQtype(topicQuestionType);
			userSubErrorQuestion.setCreateDate(nowDate);
//			logger.info("ready to saveSubErrorQuestion :{}",GsonUtil.toJson(userSubErrorQuestion));
			userSubErrorQuestionService.saveSubErrorQuestion(userSubErrorQuestion);

			//移除已纠正的错题
			removeUserCorrectQuestion(userHomeWorkAnswer,answerDetail);

			Long schId = userHomeWorkAnswer.getSchId();
			if (schId != null && schId.equals(Consts.SchId.YQP)) {//云企培机构的数据才进行操作
				sendMsgUserSubErrorQuestion(userSubErrorQuestion, Consts.AddErrorQuestionOptType.YES, schId);
			}
		}

		private void sendMsgUserSubErrorQuestion(UserSubErrorQuestion userSubErrorQuestion, Integer optType, Long schId) {
			UserSubErrorQuestionDto dto = new UserSubErrorQuestionDto();
			BeanUtils.copyProperties(userSubErrorQuestion, dto);
			dto.setSchId(schId);
			dto.setSource(7);//作业
			dto.setOptType(optType);
			try {
				String msg = GsonUtil.getGenericGson().toJson(dto);
				questionStaticProducerService.sndMesForTemplate(studyerrorTopic, msg, "", null, studyerrorTopic);
			} catch (Exception e) {
				logger.error("send UserSubErrorQuestion fail. ", e);
			}
		}

		private void addUserDoneRecord(List<Question> questions) throws DataAccessException {
			if (userHomeWorkAnswer.getObjType() == null) {
				return;
			}
			if (UserHomeWorkAnswer.HomeWorkType.LESSON != userHomeWorkAnswer.getObjType()
					&& UserHomeWorkAnswer.HomeWorkType.KNOWLEDGE_WORK != userHomeWorkAnswer.getObjType()
					&& UserHomeWorkAnswer.HomeWorkType.AL_TRAIN_BRUSH_QUESTION != userHomeWorkAnswer.getObjType()
					&& UserHomeWorkAnswer.HomeWorkType.SCHEDULE_KNOWLEDGE_WORK != userHomeWorkAnswer.getObjType()) {
				return;
			}
			Integer objType = getObjTypeVal();
			UserDoneRecord userDoneRecord = new UserDoneRecord();
			userDoneRecord.setUid(uid);
			userDoneRecord.setObjType(objType);
			userDoneRecord.setObjId(userHomeWorkAnswer.getObjId());// lesson_id
			userDoneRecord.setRelAnswerId(userHomeWorkAnswer.getId());
			Long productId = userHomeWorkAnswer.getProductId();
			userDoneRecord.setFirstCategoryId(userHomeWorkAnswer.getFirstCategoryId());
			userDoneRecord.setSecondCategoryId(userHomeWorkAnswer.getSecondCategoryId());
			userDoneRecord.setCategoryId(userHomeWorkAnswer.getCategoryId());
			if (userDoneRecord.getCategoryId() != null && userDoneRecord.getSecondCategoryId() == null) {
				Category category = knowledgeResource.getCategoryInfoById(userDoneRecord.getCategoryId());
				if (category != null) {
					userDoneRecord.setSecondCategoryId(category.getParentId());
				}
			}
			userDoneRecord.setProductId(productId);
			userDoneRecord.setGoodsId(userHomeWorkAnswer.getGoodsId());
			userDoneRecord.setQuestionNum(questions.size());
			userDoneRecord.setState(userHomeWorkAnswer.getState());
			userDoneRecord.setIsNewCourse(0);
			if (userHomeWorkAnswer.getRelationId() != null && userHomeWorkAnswer.getRelationId()>0) {// 新课程表的
				userDoneRecord.setRelationId(userHomeWorkAnswer.getRelationId());
				userDoneRecord.setIsNewCourse(1);
			}
            if (UserHomeWorkAnswer.HomeWorkType.SCHEDULE_KNOWLEDGE_WORK == userHomeWorkAnswer.getObjType()) {// 新课程表的
                userDoneRecord.setIsNewCourse(1);
            }
			Integer isOnErrorList = userHomeWorkAnswer.getIsOnErrorList();//是否是错题集的题目 0否 1是
			if (isOnErrorList != null && isOnErrorList == 1) {
				userDoneRecord.setErrorListType(userHomeWorkAnswer.getErrorListType());
				userDoneRecord.setErrorType(2);
				if (userHomeWorkAnswer.getErrorType() != null) {
					userDoneRecord.setErrorType(userHomeWorkAnswer.getErrorType());
				}
			}
			Integer isOnCollectList = userHomeWorkAnswer.getIsOnCollectList();//是否是收藏夹的题目 0否 1是
			if (isOnCollectList != null && isOnCollectList == 1) {
				userDoneRecord.setErrorListType(userHomeWorkAnswer.getErrorListType());
				userDoneRecord.setErrorType(userHomeWorkAnswer.getErrorType());
			}

			//符合条件的，需要存题目id集合
			if (Consts.DONE_RECORD_OBJ_TYPE.KNOWLEDGE_WORK == objType
					|| Consts.DONE_RECORD_OBJ_TYPE.SCHEDULE_KNOWLEDGE_WORK == objType
					|| Consts.DONE_RECORD_OBJ_TYPE.ERROR == objType
//					|| Consts.DONE_RECORD_OBJ_TYPE.AL_TRAIN_BRUSH_QUESTION == objType
					|| Consts.DONE_RECORD_OBJ_TYPE.COLLECT == objType) {
				List<String> questionIdList = new ArrayList<>();
				for (Question question : questions) {
					questionIdList.add(question.getId().toString());
				}
				userDoneRecord.setErrorQuestionIds(String.join(",", questionIdList));
			}
			this.setUserDoneRecordAccuracy(questions, userDoneRecord);
			userDoneRecord.setIsAl(0);
			userDoneRecord.setStudyPathId(0L);
			userDoneRecordDao.insertSharding(userDoneRecord);
		}

		private Integer getObjTypeVal() {
			Integer objType = Consts.DONE_RECORD_OBJ_TYPE.HOMEWORK;//录播的课后作业
			if (UserHomeWorkAnswer.HomeWorkType.KNOWLEDGE_WORK == userHomeWorkAnswer.getObjType()) {
				objType = Consts.DONE_RECORD_OBJ_TYPE.KNOWLEDGE_WORK;
			} else if (UserHomeWorkAnswer.HomeWorkType.SCHEDULE_KNOWLEDGE_WORK == userHomeWorkAnswer.getObjType()) {
				objType = Consts.DONE_RECORD_OBJ_TYPE.SCHEDULE_KNOWLEDGE_WORK;
			} else if (UserHomeWorkAnswer.HomeWorkType.ORDINARY_VIDEO_QUESTION_WORK == userHomeWorkAnswer.getObjType()){
				objType = Consts.DONE_RECORD_OBJ_TYPE.ORDINARY_VIDEO_PRE_CLASS_HOMEWORK;
			}
//			else if (UserHomeWorkAnswer.HomeWorkType.AL_TRAIN_BRUSH_QUESTION == userHomeWorkAnswer.getObjType()){
//				objType = Consts.DONE_RECORD_OBJ_TYPE.AL_TRAIN_BRUSH_QUESTION;
//				return objType;
//			}
			Integer isOnErrorList = userHomeWorkAnswer.getIsOnErrorList();//是否是错题集的题目 0否 1是
			if (isOnErrorList != null && isOnErrorList == 1) {
				objType = Consts.DONE_RECORD_OBJ_TYPE.ERROR;
			}
			Integer isOnCollectList = userHomeWorkAnswer.getIsOnCollectList();//是否是收藏夹的题目 0否 1是
			if (isOnCollectList != null && isOnCollectList == 1) {
				objType = Consts.DONE_RECORD_OBJ_TYPE.COLLECT;
			}
			return objType;
		}

		private void setUserDoneRecordAccuracy(List<Question> questions, UserDoneRecord userDoneRecord) {
			int answerNum = 0;
			int rightNum = 0;
			Collection<UserAnswerDetail> answerDetails = userHomeWorkAnswer.getAnswerDetail();
			Map<Long, List<UserAnswerDetail>> questionUserAnswerDetailMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(answerDetails)) {
				for (UserAnswerDetail userAnswerDetail : answerDetails) {
					Long questionId = userAnswerDetail.getQuestionId();
					List<UserAnswerDetail> userAnswerDetailList = questionUserAnswerDetailMap.get(questionId);
					if (CollectionUtils.isEmpty(userAnswerDetailList)) {
						userAnswerDetailList = new ArrayList<>();
					}
					userAnswerDetailList.add(userAnswerDetail);
					questionUserAnswerDetailMap.put(questionId, userAnswerDetailList);
				}
			}
			for (Long questionId : questionUserAnswerDetailMap.keySet()) {
				List<UserAnswerDetail> userAnswerDetailList = questionUserAnswerDetailMap.get(questionId);
				int notAnswerCount = 0;
				int rightAnswerCount = 0;
				for (UserAnswerDetail detail : userAnswerDetailList) {
					int isRight = detail.getIsRight();
					if (isRight == UserAnswerDetail.IsRight.NOT_ANSWER) {
						notAnswerCount++;
					} else if (isRight == UserAnswerDetail.IsRight.RIGHT) {
						rightAnswerCount++;
					}
				}
				if (notAnswerCount != userAnswerDetailList.size()) {//若未答错数不等于子题数，说明回答了题目
					answerNum++;
				}
				if (rightAnswerCount == userAnswerDetailList.size()) {
					rightNum++;
				}
			}
			userDoneRecord.setAnswerNum(answerNum);
			DecimalFormat decimalFormat = new DecimalFormat("0.0");
			String accuracy = decimalFormat.format(Double.valueOf(rightNum) / questions.size() * 100);
			userDoneRecord.setAccuracy(accuracy);
			userDoneRecord.setRightNum(rightNum);
		}

		private void addUserDoneRecordAl(Integer isAl) throws DataAccessException {
			if (UserHomeWorkAnswer.HomeWorkType.AL_CUSTOM_WORK == userHomeWorkAnswer.getObjType()) {
				return;
			}
			Integer doneSource = userHomeWorkAnswer.getDoneSource();
			if (doneSource == null || doneSource == 0) {
				return;
			}
			UserDoneRecord userDoneRecord = new UserDoneRecord();
			userDoneRecord.setUid(uid);
			if(UserHomeWorkAnswer.DoneSource.SPECIAL_PRACTICE == doneSource){
				userDoneRecord.setObjType(Consts.DONE_RECORD_OBJ_TYPE.SPECIAL_PRACTICE);
			} else if (UserHomeWorkAnswer.DoneSource.HighFrequencyErrorQuestion == doneSource){
				userDoneRecord.setObjType(Consts.DONE_RECORD_OBJ_TYPE.HighFrequencyErrorQuestion);
			}else if (UserHomeWorkAnswer.DoneSource.CustomQuestion == doneSource){
				userDoneRecord.setObjType(Consts.DONE_RECORD_OBJ_TYPE.CustomQuestion);
			}else if (UserHomeWorkAnswer.DoneSource.ExemplaryAnalogous == doneSource){
				userDoneRecord.setObjType(Consts.DONE_RECORD_OBJ_TYPE.ExemplaryAnalogous);
			}else if (UserHomeWorkAnswer.DoneSource.LessonQuestion == doneSource){
				userDoneRecord.setObjType(Consts.DONE_RECORD_OBJ_TYPE.LessonQuestion);
			}else{
				userDoneRecord.setObjType(doneSource);
			}

			userDoneRecord.setObjId(userHomeWorkAnswer.getObjId());
			userDoneRecord.setRelAnswerId(userHomeWorkAnswer.getId());
			Long productId = userHomeWorkAnswer.getProductId();
			userDoneRecord.setFirstCategoryId(userHomeWorkAnswer.getFirstCategoryId());
			userDoneRecord.setSecondCategoryId(userHomeWorkAnswer.getSecondCategoryId());
			userDoneRecord.setCategoryId(userHomeWorkAnswer.getCategoryId());
			if (userDoneRecord.getCategoryId() != null && userDoneRecord.getSecondCategoryId() == null) {
				Category category = knowledgeResource.getCategoryInfoById(userDoneRecord.getCategoryId());
				if (category != null) {
					userDoneRecord.setSecondCategoryId(category.getParentId());
				}
			}
			userDoneRecord.setProductId(productId);
			userDoneRecord.setGoodsId(userHomeWorkAnswer.getGoodsId());
			userDoneRecord.setQuestionNum(questions.size());
			userDoneRecord.setState(userHomeWorkAnswer.getState());
			userDoneRecord.setIsNewCourse(0);
			userDoneRecord.setErrorType(userHomeWorkAnswer.getErrorType());
			List<String> questionIdList = new ArrayList<>();
			for (Question question : questions) {
				questionIdList.add(question.getId().toString());
			}
			userDoneRecord.setErrorQuestionIds(String.join(",", questionIdList));
			this.setUserDoneRecordAccuracy(questions, userDoneRecord);
			userDoneRecord.setIsAl(isAl);
			userDoneRecord.setStudyPathId(userHomeWorkAnswer.getStudyPathId());
			if (UserHomeWorkAnswer.HomeWorkType.AL_TRAIN_BRUSH_QUESTION == userHomeWorkAnswer.getObjType()) {
				userDoneRecord.setRelationId(userHomeWorkAnswer.getTaskId());
			}
			if (UserHomeWorkAnswer.HomeWorkType.SPECIAL_EXERCISE == userHomeWorkAnswer.getObjType()) {
				userDoneRecord.setRelationId(userHomeWorkAnswer.getHomeworkId());
			}
			userDoneRecordDao.insertSharding(userDoneRecord);
		}

		public List<UserAnswerSum> findByUserHomeworkIdWithDetails(Long uid, Long userHomeWorkId, Long questionId) throws DataAccessException {
	        UserAnswerSum answerSum = new UserAnswerSum();
	        answerSum.setUserHomeworkId(userHomeWorkId);
	        answerSum.setQuestionId(questionId);
			answerSum.setUid(uid);
	        List<UserAnswerSum> answerSums = userAnswerSumDao.findAllList(answerSum);
	        answerSums = setDetails(uid, answerSums);
	        return answerSums;
	    }

		private List<UserAnswerSum> setDetails(Long uid, List<UserAnswerSum> answerSums) throws DataAccessException {
	        int i = 0;
	        for (UserAnswerSum userAnswer : answerSums) {
	            UserAnswerDetail detail = new UserAnswerDetail();
	            detail.setSumId(userAnswer.getId());
				detail.setUid(uid);
	            userAnswer.setAnswerDetail(userAnswerDetailDao.findAllList(detail));
	            answerSums.set(i++, userAnswer);
	        }
	        return answerSums;
	    }

		private void syncQuestionLog(List<QuestionAnswerDetail> questionAnswerDetailList){
	    	final List<QuestionAnswerDetail> list = questionAnswerDetailList;
	    	es.execute(new Runnable() {
	            @Override
	            public void run() {
	            	try {
						questionAnswerStaticsService.recordQuestionAnswerInfoBatch(list);
					} catch (DataAccessException e) {
						logger.error(e.getMessage());
					}
	            }
	        });
	    }
	}

	/**
	 * 设置的userAnswerDetail的id
	 */
	private void setIdUserAnswerDetail(UserAnswerSum oldUserAnswerSum, UserAnswerDetail userAnswerDetail) {
		if (oldUserAnswerSum == null || CollectionUtils.isEmpty(oldUserAnswerSum.getAnswerDetail())) {
			return;
		}
		for (UserAnswerDetail answerDetailItem : oldUserAnswerSum.getAnswerDetail()) {
			if (userAnswerDetail.getTopicId().equals(answerDetailItem.getTopicId())) {
				userAnswerDetail.setId(answerDetailItem.getId());
				break;
			}
		}
	}
}
