package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.UserAnswerSumDao;
import cn.huanju.edu100.study.event.PaperSubmittedEvent;
import cn.huanju.edu100.study.model.Paper;
import cn.huanju.edu100.study.model.PaperSubmitStatisticInfo;
import cn.huanju.edu100.study.model.UserAnswerSum;
import cn.huanju.edu100.study.repository.PaperSubmitCompareInfoRepository;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.PaperService;
import cn.huanju.edu100.study.util.PaperSubmitStatisticRule;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.JSONUtils;
import com.google.common.collect.Lists;
import com.hqwx.study.dto.PaperSubmitCompareInfo;
import com.hqwx.study.dto.UserAnswerSumDTO;
import com.hqwx.study.dto.query.UserPaperAnswerQuery;
import com.hqwx.study.entity.UserAnswer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/20 17:43
 * @description
 */
@Slf4j
@Qualifier("paperService")
@Service
public class PaperServiceImpl extends BaseServiceImpl<UserAnswerSumDao, UserAnswerSum> implements PaperService {

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private PaperSubmitCompareInfoRepository paperSubmitCompareInfoRepository;


    /**
     * 监听试卷提交成功的事件，更新统计信息
     * @param event
     */
    @EventListener
    protected void onPaperSubmittedEvent(PaperSubmittedEvent event){
        UserAnswer userAnswer = event.getUserAnswer();
        if(userAnswer == null) {
            log.warn("onPaperSubmittedEvent, paper user answer is null! {}", JSONUtils.toJsonString(event));
            return;
        }
        if(userAnswer.getPaperId() == null || userAnswer.getEventType() == null) {
            log.warn("onPaperSubmittedEvent, exists null parameter: paperId={}, eventType={}",
                     userAnswer.getPaperId(), userAnswer.getEventType());
            return;
        }

        Paper paper = knowledgeResource.getPaperInfoById(userAnswer.getPaperId());
        if(paper == null) {
            log.warn("onPaperSubmittedEvent, cannot find paper for paperId={}", userAnswer.getPaperId());
            return;
        }

        if(userAnswer.getEventType() == 1){
            paperSubmitCompareInfoRepository.increaseSubmitCount(userAnswer.getPaperId(), userAnswer.getScore(), paper.getPaperScore());
        }else if(userAnswer.getEventType() == 2){
            paperSubmitCompareInfoRepository.increaseSubmitAccuracyCount(userAnswer.getPaperId(), getPaperAccuracy(userAnswer));
        }

    }

    private Double getPaperAccuracy(UserAnswer userAnswer){
        Double paperAccuracy = 0d;
        String comment = userAnswer.getComment();
        if(StringUtils.isEmpty(comment)){
            return paperAccuracy;
        }
        Map<String, String> commentMap  = GsonUtil.getGson().fromJson(comment, Map.class);
        String accuracy = commentMap.get("accuracy");
        if(StringUtils.isBlank(accuracy)){
            return paperAccuracy;
        }
        return Double.parseDouble(accuracy)* 100;
    }

    @Override
    public PaperSubmitCompareInfo getPaperSubmitCompareInfo(Long paperId, Double score, Double totalScore, Double paperAccuracy) {
        PaperSubmitCompareInfo paperSubmitCompareInfo = new PaperSubmitCompareInfo();
        paperSubmitCompareInfo.setPaperId(paperId);
        paperSubmitCompareInfo.setScore(score);
        paperSubmitCompareInfo.setTotalScore(totalScore);
        paperSubmitCompareInfo.setPaperAccuracy(paperAccuracy);

        if(totalScore == null) {
            log.warn("getPaperSubmitCompareInfo missing total score, query by paperId {}", paperId);
            Paper paper = knowledgeResource.getPaperInfoById(paperId);
            if(paper != null) {
                paperSubmitCompareInfo.setTotalScore(paper.getPaperScore());
            } else {
                throw new RuntimeException("Missing paper total score and query null paper info " + paperId);
            }
        }

        Map<Integer, PaperSubmitStatisticInfo> scoreIndex2StatisticInfo = paperSubmitCompareInfoRepository.getPaperScoreIndex2StatisticInfo(paperId);

        PaperSubmitCompareInfo result = PaperSubmitStatisticRule.statistic(paperSubmitCompareInfo, scoreIndex2StatisticInfo);
        log.info("getPaperSubmitCompareInfo: scoreIndex2StatisticInfo={}, result={}", JSONUtils.toJsonString(scoreIndex2StatisticInfo), JSONUtils.toJsonString(result));
        return result;
    }

    static private List<UserAnswerSumDTO> toUserAnswerSumDTOList(List<UserAnswerSum> list) {
        List<UserAnswerSumDTO> resultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.stream().forEach(answerSum -> {
                UserAnswerSumDTO result = new UserAnswerSumDTO();
                BeanUtils.copyProperties(answerSum, result);
                resultList.add(result);
            });
        }
        return resultList;
    }

    @Override
    public List<UserAnswerSumDTO> getPaperAnswerSummaryList(Long uid, List<Long> paperIdList) {
        List<UserAnswerSum> list = null;
        try {
            list = dao.findPaperAnswerSummaryList(uid, paperIdList);
        } catch (DataAccessException e) {
            throw new RuntimeException(e);
        }
        return toUserAnswerSumDTOList(list);
    }


    @Override
    public List<UserAnswerSumDTO> getPaperLastAnswerSummary(Long uid, List<Long> paperIdList, Integer objType, Integer needDetail) {
        List<UserAnswerSum> detailList = null;
        try {
            List<Integer> objTypeList = new ArrayList<>();
            if (Objects.nonNull(objType)) {
                //云私塾机考特殊处理
                if (objType == UserAnswer.ObjType.simulationExamYSS) {
                    objTypeList.add(UserAnswer.ObjType.simulationExamYSS);
                    objTypeList.add(UserAnswer.ObjType.AL_PAPER);
                } else {
                    objTypeList.add(objType);
                }
            }
            List<UserAnswerSum> userAnswerIdInfoList = dao.findUserPaperLastAnswerId(uid, paperIdList, objTypeList, null, null, null);
            if(needDetail == null || needDetail != 0) {
                // 需要详情
                if (CollectionUtils.isNotEmpty(userAnswerIdInfoList)) {
                    List<Long> userAnswerIdList = userAnswerIdInfoList.stream()
                            .map(UserAnswerSum::getUserAnswerId).collect(Collectors.toList());
                    detailList = dao.findPaperAnswerSumDetail(uid, userAnswerIdList);
                }
            }
        } catch (DataAccessException e) {
            throw new RuntimeException(e);
        }
        return toUserAnswerSumDTOList(detailList);
    }

    @Override
    public List<UserAnswerSumDTO> getPaperLastAnswerSummaryNew(UserPaperAnswerQuery query) {
        Long uid = query.getUid();
        List<Long> paperIdList = query.getPaperIdList();
        if (Objects.isNull(uid) || CollectionUtils.isEmpty(paperIdList)) {
            return new ArrayList<>();
        }

        Integer objType = query.getObjType();
        List<UserAnswerSum> detailList = null;
        try {
            List<Integer> objTypeList = new ArrayList<>();
            if (Objects.nonNull(objType)) {
                //云私塾机考特殊处理
                if (objType == UserAnswer.ObjType.simulationExamYSS) {
                    objTypeList.add(UserAnswer.ObjType.simulationExamYSS);
                    objTypeList.add(UserAnswer.ObjType.AL_PAPER);
                } else {
                    objTypeList.add(objType);
                }
            }
            List<UserAnswerSum> userAnswerIdInfoList = dao.findUserPaperLastAnswerId(uid, paperIdList, objTypeList, query.getGoodsId(), query.getProductId(), query.getPaperType());
            if(query.getNeedDetail() == null || query.getNeedDetail() != 0) {
                // 需要详情
                if (CollectionUtils.isNotEmpty(userAnswerIdInfoList)) {
                    List<Long> userAnswerIdList = userAnswerIdInfoList.stream()
                            .map(UserAnswerSum::getUserAnswerId).collect(Collectors.toList());
                    detailList = dao.findPaperAnswerSumDetail(uid, userAnswerIdList);
                }
            }
        } catch (DataAccessException e) {
            throw new RuntimeException(e);
        }
        return toUserAnswerSumDTOList(detailList);
    }

}
