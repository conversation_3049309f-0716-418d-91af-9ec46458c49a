package cn.huanju.edu100.study.service.onetoone;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.onetoone.VClasses;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Set;

/**
 * 面授班级Service
 * <AUTHOR>
 * @version 2016-04-12
 */
public interface VClassesService extends BaseService<VClasses> {

    List<VClasses> findListByIds(Set<Long> ids) throws DataAccessException;

    boolean isNew(Long clsId) throws DataAccessException;

	List<VClasses> findListByIds(Set<Long> clsIds, List<Long> schIds) throws DataAccessException;

	List<VClasses> findListByIds(Set<Long> clsIds, Long schId) throws DataAccessException;

	List<VClasses> findListByTeacherUid(Long teacherUid, Integer from, Integer rows, Integer lessonType,
            Integer classStatus) throws DataAccessException;
}
