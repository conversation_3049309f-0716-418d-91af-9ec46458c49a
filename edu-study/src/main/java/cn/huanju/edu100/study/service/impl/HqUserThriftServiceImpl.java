package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.study.model.UserAccount;
import cn.huanju.edu100.study.service.IGenericThriftServer;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.Reflections;
import com.google.common.collect.Maps;
import com.hqwx.thrift.client.thrift.ThriftClientFactory;
import com.hqwx.thrift.client.thrift.ThriftClientWrapper;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.util.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static cn.huanju.edu100.util.InternalServiceUtil.buildRequest;

@Slf4j
@Lazy
@Component
public class HqUserThriftServiceImpl implements IGenericThriftServer{
	private static Map<String, Method> methodMap = new ConcurrentHashMap<String, Method>();

	@Autowired
	@Qualifier("hqUserClientFactory")
	private ThriftClientFactory<cn.huanju.edu100.thrift.edu_user.Iface> clientFactory;

	public Map<Long, UserAccount> getUserAccounts(List<Long> uidList) {
		Map<String, Object> param = Maps.newHashMap();
		param.put("idList", uidList);
		param.put("schId", 2);
		param.put("optUser", 1);
		param.put("appId", 218L);
		try {
			ThriftClientWrapper<cn.huanju.edu100.thrift.edu_user.Iface> client = clientFactory.createClient();
			response response = client.getClient().new_user_listUserInfoByUids(
					buildRequest("127.0.0.1", 218, 2, 14, 1,
							GsonUtil.getGenericGson().toJson(param)));
			if (response != null && response.getCode() == 0 && org.apache.commons.lang3.StringUtils.isNotBlank(response.getMsg())) {
				java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAccount>>() {
				}.getType();
				List<UserAccount> userAccounts = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
				if (null == userAccounts || userAccounts.isEmpty()) {
					return null;
				} else {
					Map<Long, UserAccount> userAccountMap = Maps.newHashMap();
					for (UserAccount account : userAccounts) {
						userAccountMap.put(account.getUid(), account);
					}
					return userAccountMap;
				}
			}
		} catch (Exception e) {
			log.error("getUserAccounts fail, uidList :{}", uidList, e);
		}
		return Collections.emptyMap();
	}
	@Override
	public response generalThriftMethodInvoke(Map<String, Object> param,
											  int appid, int clientIp, String methodName)
			throws DataAccessException {
		ThriftClientWrapper<cn.huanju.edu100.thrift.edu_user.Iface> client = null;
		response res = new response();
		Class<?> clazz = Reflections.getClassGenricType(this.getClass());
		try {
			client = clientFactory.createClient();
			request req = new request();
			req.setAppid(appid);
			req.setClient_ip(clientIp);
			req.setCodetype(1);
			req.setMsg(GsonUtils.toJson(param));
			Method method = methodMap.get(methodName);
			if (method == null) {
				method = clazz.getMethod(methodName, request.class);
				if (method != null) {
					methodMap.put(methodName, method);
				}
			}
			if (method != null) {
				res = (response) method.invoke(client.getClient(), req);
			}
		} catch (Exception e) {
			log.error("invoke {} ThriftServer method {} param:{} exception.",
					clazz.getSimpleName(), methodName, GsonUtils.toJson(param),
					e);
			throw new DataAccessException(e.getMessage());
		} finally {
			if (client != null) {
				client.close();
			}
		}
		return res;
	}

	@Override
	public response generalThriftMethodInvoke(Collection<Long> param,
											  int appid, int clientIp, String methodName)
			throws DataAccessException {
		ThriftClientWrapper<cn.huanju.edu100.thrift.edu_user.Iface> client = null;
		response res = new response();
		Class<?> clazz = Reflections.getClassGenricType(this.getClass());
		try {
			client = clientFactory.createClient();
			request req = new request();
			req.setAppid(appid);
			req.setClient_ip(clientIp);
			req.setCodetype(1);
			req.setMsg(GsonUtils.toJson(param));
			Method method = clazz.getMethod(methodName, request.class);
			if (method != null) {
				res = (response) method.invoke(client.getClient(), req);
			}
		} catch (Exception e) {
			log.error("invoke {} ThriftServer method {} param:{} exception.",
					clazz.getSimpleName(), methodName, GsonUtils.toJson(param),
					e);
			throw new DataAccessException(e.getMessage());
		} finally {
			if (client != null) {
				client.close();
			}
		}
		return res;
	}
}
