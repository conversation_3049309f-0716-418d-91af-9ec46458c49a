package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.util.upload.OssUtil;

/**
 * 资源类任务Entity
 * <AUTHOR>
 * @version 2016-01-18
 */
public class TutorTaskResource extends DataEntity<TutorTaskResource> {
	
	private static final long serialVersionUID = 1L;
	private Integer type;		// type
	private Integer patten;		// patten
	private String content;		// content
	private Long videoId;		// 录播id
	private String bak;		// bak
	
	public TutorTaskResource() {
		super();
	}

	public TutorTaskResource(Long id){
		super(id);
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getPatten() {
		return patten;
	}

	public void setPatten(Integer patten) {
		this.patten = patten;
	}
	
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = OssUtil.bs2UrlConvertToOssUrl(content);
	}
	
	public String getBak() {
		return bak;
	}

	public void setBak(String bak) {
		this.bak = bak;
	}

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }
	
}