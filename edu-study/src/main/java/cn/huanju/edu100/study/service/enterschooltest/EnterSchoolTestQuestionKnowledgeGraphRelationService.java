package cn.huanju.edu100.study.service.enterschooltest;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.enterschooltest.EnterSchoolTestQuestionKnowledgeGraphRelation;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

public interface EnterSchoolTestQuestionKnowledgeGraphRelationService extends BaseService<EnterSchoolTestQuestionKnowledgeGraphRelation> {

    /**
     * 批量插入
     *
     * @param
     * @return
     * @throws DataAccessException
     */
    public long insertBatch(List<EnterSchoolTestQuestionKnowledgeGraphRelation> enterSchoolTestQuestionKnowledgeGraphRelations) throws DataAccessException;
}
