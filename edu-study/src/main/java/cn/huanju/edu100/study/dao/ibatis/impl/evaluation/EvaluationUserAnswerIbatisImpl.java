package cn.huanju.edu100.study.dao.ibatis.impl.evaluation;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.evaluation.EvaluationUserAnswerDao;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.model.evaluation.EvaluationUserAnswer;
import com.hqwx.study.dto.EvaluationUserAnswerDTO;
import com.hqwx.study.dto.query.EvaluationUserAnswerQuery;
import com.ibatis.sqlmap.client.SqlMapClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.sql.SQLException;
import java.util.List;


public class EvaluationUserAnswerIbatisImpl extends CrudIbatisImpl2<EvaluationUserAnswer> implements EvaluationUserAnswerDao {

    public EvaluationUserAnswerIbatisImpl() {
        super("EvaluationUserAnswer");
    }

    @Override
    public void insertBatch(List<EvaluationUserAnswer> list) throws DataAccessException {
        try {
            if (CollectionUtils.isEmpty(list)) {
                String tip = "insertBatch error, param is empty";
                logger.error(tip);
                throw new DataAccessException(tip);
            }
            SqlMapClient sqlMap = super.getMaster();
            sqlMap.insert(namespace.concat(".insertBatch"), list);
        } catch (SQLException e) {
            logger.error("insertBatch SQLException. list:{}", list, e);
            throw new DataAccessException("insertBatch SQLException error");
        }

    }

    @Override
    public Integer getEvaluationUserAnswerCount(EvaluationUserAnswerQuery query) throws DataAccessException {
        if (query == null || query.getUid() == null) {
            String tip = "getEvaluationUserAnswerCount error, param is empty";
            logger.error(tip);
            throw new DataAccessException(tip);
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Integer ret = (Integer) sqlMap.queryForObject(namespace.concat(".getEvaluationUserAnswerCount"), query);
            if (ret == null) {
                ret = 0;
            }
            return ret;
        } catch (SQLException e) {
            logger.error("getEvaluationUserAnswerCount SQLException. query:{}", query, e);
            throw new DataAccessException("getEvaluationUserAnswerCount SQLException error");
        }
    }

    @Override
    public List<EvaluationUserAnswerDTO> getEvaluationUserAnswerList(EvaluationUserAnswerQuery query) throws DataAccessException {
        if (query == null || query.getUid() == null) {
            String tip = "getEvaluationUserAnswerList error, param is empty";
            logger.error(tip);
            throw new DataAccessException(tip);
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            List<EvaluationUserAnswerDTO> ret = (List<EvaluationUserAnswerDTO>) sqlMap.queryForList(namespace.concat(".getEvaluationUserAnswerList"), query);
            return ret;
        } catch (SQLException e) {
            logger.error("getEvaluationUserAnswerList SQLException. query:{}", query, e);
            throw new DataAccessException("getEvaluationUserAnswerList SQLException error");
        }
    }

}
