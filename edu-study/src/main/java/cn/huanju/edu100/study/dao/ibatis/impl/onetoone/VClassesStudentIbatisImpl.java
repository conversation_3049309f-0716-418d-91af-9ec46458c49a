/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.onetoone.VClassesStudentDao;
import cn.huanju.edu100.study.model.QueryParam;
import cn.huanju.edu100.study.model.common.ResultCount;
import cn.huanju.edu100.study.model.onetoone.VClassesStudent;
import cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 面授学员班级DAO接口
 * <AUTHOR>
 * @version 2016-04-12
 */
public class VClassesStudentIbatisImpl extends CrudIbatisImpl2<VClassesStudent> implements
		VClassesStudentDao {

	public VClassesStudentIbatisImpl() {
		super("VClassesStudent");
	}

    @Override
    public List<VClassesStudent> findListByQueryParam(QueryParam param, Integer unFinish) throws DataAccessException {

        if (null == param || !IdUtils.isValid(param.getUid()) || CollectionUtils.isEmpty(param.getIdList())) {
            logger.error("findListByQueryParam fail, illegal param, queryParam :{}", GsonUtil.toJson(param));
            throw new DataAccessException(String.format("findListByQueryParam fail, illegal param, queryParam :%s", GsonUtil.toJson(param)));
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> map = Maps.newHashMap();
            map.put("uid", param.getUid());
            map.put("clsIds", param.getIdList());
            map.put("status", 0);// 正常的
            if (null != unFinish) {
                map.put("unFinish", unFinish);
            }
            return sqlMap.queryForList(namespace.concat(".findListByQueryParam"), map);
        } catch (SQLException e) {
            logger.error("findListByQueryParam {} SQLException. queryParam:{}", namespace, GsonUtil.toJson(param), e);
            throw new DataAccessException(String.format("findListByQueryParam SQLException error :%s", e.getMessage()));
        }
    }

    @Override
    public List<ResultCount> getStudentCountByClsIds(List<Long> clsIds) throws DataAccessException {

        if (CollectionUtils.isEmpty(clsIds)) {
            logger.error("getStudentCountByClsIds fail, clsIds is empty");
            throw new DataAccessException("getStudentCountByClsIds fail, clsIds is empty");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> map = Maps.newHashMap();
            map.put("clsIds", clsIds);
            return sqlMap.queryForList(namespace.concat(".getStudentCountGroupByClsIds"), map);
        } catch (SQLException e) {
            logger.error("getStudentCountByClsIds {} SQLException. queryParam:{}", namespace, GsonUtil.toJson(clsIds), e);
            throw new DataAccessException(String.format("getStudentCountByClsIds SQLException error :%s", e.getMessage()));
        }
    }


    @Override
    public int addLessonCount(VClassesStudentOrder vClassesStudentOrder) throws DataAccessException {
        if (vClassesStudentOrder == null || vClassesStudentOrder.getClsId() == null
                || vClassesStudentOrder.getLessonCount() == null || vClassesStudentOrder.getUid() == null) {
            logger.error("addLessonCount fail, illegal param{}", GsonUtil.toJson(vClassesStudentOrder));
            throw new DataAccessException(String.format("addLessonCount fail, illegal param :%s", GsonUtil.toJson(vClassesStudentOrder)));
        }

        try {
            SqlMapClient sqlMap = super.getMaster();
            return sqlMap.update(namespace.concat(".addLessonCount"), vClassesStudentOrder);
        } catch (SQLException e) {
            logger.error("addLessonCount {} SQLException. queryParam:{}", namespace, GsonUtil.toJson(vClassesStudentOrder), e);
            throw new DataAccessException(String.format("addLessonCount SQLException error :%s", e.getMessage()));
        }
    }

    @Override
    public int updateStatus(VClassesStudent vClassesStudent) throws DataAccessException {
        if (vClassesStudent == null || vClassesStudent.getLogId() == null
                || vClassesStudent.getProductId() == null) {
            logger.error("updateStatus fail, illegal param{}", GsonUtil.toJson(vClassesStudent));
            throw new DataAccessException(String.format("updateStatus fail, illegal param :%s", GsonUtil.toJson(vClassesStudent)));
        }

        try {
            SqlMapClient sqlMap = super.getMaster();
            return sqlMap.update(namespace.concat(".updateStatus"), vClassesStudent);
        } catch (SQLException e) {
            logger.error("updateStatus {} SQLException. queryParam:{}", namespace, GsonUtil.toJson(vClassesStudent), e);
            throw new DataAccessException(String.format("updateStatus SQLException error :%s", e.getMessage()));
        }
    }

}
