package cn.huanju.edu100.study.dao.mock;


import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.mock.TikuMockLearningAdvice;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
*
* <AUTHOR> duxiulei
* @Description :TikuMockLearningAdviceDao
* @Date : 2020/8/20
*
*/
public interface TikuMockLearningAdviceDao extends CrudDao<TikuMockLearningAdvice> {

    
    /**
    *
    * <AUTHOR> duxiulei
    * @Description :qryByMockExamIdAndMockSubjectId
    * @Date : 2020/8/20
    *
    */
    List<TikuMockLearningAdvice> qryByMockExamIdAndMockSubjectId(Long mockExamId, Long mockSubjectId) throws DataAccessException;


}
