package cn.huanju.edu100.study.dao.solution;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.solution.QuestionAnswerLike;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.HashMap;


public interface QuestionAnswerLikeDao extends CrudDao<QuestionAnswerLike> {
    boolean deleteAnswerLike(HashMap<String, Long> paramsMap) throws DataAccessException;
    QuestionAnswerLike findUserAnswerLikeByAnswerIdAndUid(HashMap<String, Long> paramsMap) throws DataAccessException;
}
