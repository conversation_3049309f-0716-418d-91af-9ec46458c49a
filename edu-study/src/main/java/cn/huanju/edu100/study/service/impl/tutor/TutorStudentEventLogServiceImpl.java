package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorStudentEventLogDao;
import cn.huanju.edu100.study.dao.tutor.TutorTaskDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentEventLog;
import cn.huanju.edu100.study.model.tutor.TutorStudentEventLogListWrap;
import cn.huanju.edu100.study.model.tutor.TutorTaskDto;
import cn.huanju.edu100.study.service.tutor.TutorStudentEventLogService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 用户关键事件记录Service
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
@Service
public class TutorStudentEventLogServiceImpl extends BaseServiceImpl<TutorStudentEventLogDao, TutorStudentEventLog>
        implements TutorStudentEventLogService {

    @Autowired
    private TutorTaskDao tutorTaskDao;

    /**
     * 展示用户关键事件列表
     */
    @Override
    public TutorStudentEventLogListWrap listKeyEventLogByUidType(Map<String, Object> params) throws DataAccessException {

        TutorStudentEventLogListWrap eventLogWrap = new TutorStudentEventLogListWrap();
        if (params == null || params.get("uid") == null || params.get("type") == null || params.get("from") == null
                || params.get("row") == null) {
            return eventLogWrap;
        }

        // 计算总数
        Page<TutorStudentEventLog> page = new Page<TutorStudentEventLog>();
        TutorStudentEventLog tutorStudentEventLogEntity = new TutorStudentEventLog();
        tutorStudentEventLogEntity.setUid((Long) params.get("uid"));
        tutorStudentEventLogEntity.setType((Integer) params.get("type"));
        tutorStudentEventLogEntity.setSecondCategory(params.get("secondCategory") == null ? null : (Long) params
                .get("secondCategory"));
        tutorStudentEventLogEntity.setCategoryId(params.get("categoryId") == null ? null : (Long) params
                .get("categoryId"));
        if (params.get("total") == null) {
            eventLogWrap.setTotal(this.dao.findListCount(page, tutorStudentEventLogEntity).longValue());
        } else {
            eventLogWrap.setTotal((Long) params.get("total"));
        }

        // 用户关键事件列表
        page.setFrom(((Long) params.get("from")).intValue());
        page.setPageSize(((Long) params.get("row")).intValue());
        List<TutorStudentEventLog> list = this.dao.findList(page, tutorStudentEventLogEntity);

        batchSetTaskTitle(list);
        eventLogWrap.setList(list);
        return eventLogWrap;
    }

    private void batchSetTaskTitle(List<TutorStudentEventLog> list) throws DataAccessException {
        Set<Long> taskIdSet = new HashSet<Long>();
        for (TutorStudentEventLog tutorStudentEventLog : list) {
            if (null != tutorStudentEventLog.getTaskId()) {
                taskIdSet.add(tutorStudentEventLog.getTaskId());
            }
        }
        Map<String, Object> taskParams = new HashMap<String, Object>();
        taskParams.put("idList", Lists.newArrayList(taskIdSet));
        List<TutorTaskDto> tutorTaskDtoList = tutorTaskDao.getTutorTasks(taskParams);
        Map<Long, TutorTaskDto> tutorTaskMap = new HashMap<Long, TutorTaskDto>();
        for (TutorTaskDto tutorTaskDto : tutorTaskDtoList) {
            tutorTaskMap.put(tutorTaskDto.getId(), tutorTaskDto);
        }
        for (TutorStudentEventLog tutorStudentEventLog : list) {
            TutorTaskDto tutorTaskDto = tutorTaskMap.get(tutorStudentEventLog.getTaskId());
            if (tutorTaskDto == null) {
                continue;
            }
            tutorStudentEventLog.setTaskTitle(tutorTaskDto.getTitle());
        }
    }
}
