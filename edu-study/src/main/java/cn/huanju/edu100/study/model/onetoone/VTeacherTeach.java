package cn.huanju.edu100.study.model.onetoone;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 留学老师授课内容Entity
 * <AUTHOR>
 * @version 2016-09-09
 */
public class VTeacherTeach extends DataEntity<VTeacherTeach> {
	
	private static final long serialVersionUID = 1L;
	private Long teacherId;		// teacher_id
	private Long teachId;		// 教授内容
	
	public VTeacherTeach() {
		super();
	}

	public VTeacherTeach(Long id){
		super(id);
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	
	public Long getTeachId() {
		return teachId;
	}

	public void setTeachId(Long teachId) {
		this.teachId = teachId;
	}
	
}