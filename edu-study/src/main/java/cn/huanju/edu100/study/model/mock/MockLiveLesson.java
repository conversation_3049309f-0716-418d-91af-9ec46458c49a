package cn.huanju.edu100.study.model.mock;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 模考活动关联直播课节
 * <AUTHOR>
 */
public class MockLiveLesson extends DataEntity<MockLiveLesson> {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Long mockExamId;		// 所属模考活动id
	private Long productId;		// 产品id
	private String title;
	private Long secondCategory;		// 所属考试id
	private Long categoryId;		// 科目id
	private Integer sortNum;		// 排序值

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getMockExamId() {
		return mockExamId;
	}

	public void setMockExamId(Long mockExamId) {
		this.mockExamId = mockExamId;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}
}
