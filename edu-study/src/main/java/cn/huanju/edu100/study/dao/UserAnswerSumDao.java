/**
 *
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.vo.UserAnswerErrorQuestionVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户答案大题详情DAO接口
 * <AUTHOR>
 * @version 2015-05-12
 */
public interface UserAnswerSumDao extends CrudDao<UserAnswerSum> {

	List<UserAnswerSum> findAnswerSum(Long uid, Long userAnswerId, List<Long> questionIdList) throws DataAccessException;

	List<UserAnswerSum> findAnswerSumAnswerOrHomework(Long uid, Long userAnswerId, List<Long> questionIdList) throws DataAccessException;

	List<UserAnswerSum> findAnswerSum(Long uid, List<Long> userHomeworkIds, List<Long> questionIdList) throws DataAccessException;

	List<UserAnswerSum> findAnswerSumByMClass(Long uid, Long mClassId, List<Long> questionIdList) throws DataAccessException;

	List<UserAnswerSum> findHomeworkSum(Long uid, Long userHomeworkId, List<Long> questionIdList) throws DataAccessException;

	List<UserAnswerDetail> findAnswerSumQuestion(Map<String, Object> params) throws DataAccessException;

	List<UserAnswerDetail> getQuestionListByAnswerId(Map<String, Object> params) throws DataAccessException;

	Integer countWrongNumByAnswerId(Long uid,Long answerId) throws DataAccessException;

	Integer countWrongNumByHomeworkId(Long uid,Long homeworkId) throws DataAccessException;

    boolean delete(UserAnswerSum userAnswerSum) throws DataAccessException;

	List<UserAnswerSum> findUserAllQuestion(UserAnswerSum userAnswerSum) throws DataAccessException;

	List<UserAnswerHistory> findUserAllQuestionHistory(Map param) throws DataAccessException;

	Map<Long,Integer> getIsRightMapBySumId(List<Long> sum_ids,Long uid) throws DataAccessException;

    List<PaperUserAnswerDetail> findUserAnswerDetailByIdAndUid(Long id, Long uid) throws DataAccessException;

    List<UserAnswerDetail> getQuestionListByHomeworkAnswerId(Map<String, Object> params) throws DataAccessException;

	List<UserAnswerDetail> getAnswerDetailsByHomeworkAnswerIds(Map<String, Object> params) throws DataAccessException;

	List<UserAnswerDetail> getAnswerDetailsByAnswerIds(Map<String, Object> params) throws DataAccessException;

	Integer countQuestionListByHomeworkAnswerId(Map<String, Object> params) throws DataAccessException;

	List<PaperUserAnswerDetail> findUserAnswerHomeworkDetailByIdAndUid(Long id, Long uid) throws DataAccessException;

    List<AlPaperReport> getUserAnswerListByUidAndDate(Long uid, Long categoryId, Date startTime, Date endTime)throws DataAccessException;

	List<AlPaperReport> getUserAnswerHomeworkListByUidAndDate(Long uid, Long categoryId, Date startTime, Date endTime,List<Long> resIds)throws DataAccessException;

	Integer countQuestionListByAnswerId(Map<String, Object> params) throws DataAccessException;

	List<Map<String, Object>> findUserAnswerPaperCompleteCount(Long uid, List<Long> paperIds) throws DataAccessException;

	List<Map<String, Object>> findUserAnswerSumTotalCount(Long uid, List<Long> answerIds) throws DataAccessException;

	Map<String, String> findUserHomeworkCompleteCount(Long uid, List<Long> questionIds, Date startTime, Date endTime, Long goodsId) throws DataAccessException;

	List<Long> findUserHomeworkCompleteQuestionIds(Long uid, List<Long> questionIds, Date startTime, Date endTime, Long goodsId) throws DataAccessException;

	List<UserAnswerSum> getLastUserAnswerSumByQuestionIds(Long uid, List<Long> questionIdList) throws DataAccessException;

    List<UserAnswerSum> getLastUserAnswerSumAndDetailByQuestionIds(Long uid, List<Long> questionIdList) throws DataAccessException;

	List<UserAnswerSum> getLastUserAnswerSumAndDetailByQuestionIdsV2(Long uid, List<Long> questionIdList) throws DataAccessException;

	List<Map<String, Object>> findStudyCenterLastHomeworkCompleteCount(Long uid, List<Long> userHomeworkIds) throws DataAccessException;

	List<UserAnswerSum> findStudyCenterLastHomeworkSumList(Long uid, List<Long> userHomeworkIds) throws DataAccessException;

	List<UserAnswerDetail> getQuestionListByAnswerIds(Map<String, Object> params) throws DataAccessException;

	List<UserAnswerSum> findAlSubmitQuestionList(Map<String, Object> params) throws DataAccessException;

	List<UserAnswerSum> findAlSubmitQuestionListByPaperOrHomework(Map<String, Object> params) throws DataAccessException;

	List<UserAnswerSum> findPaperAnswerSummaryList(Long uid, List<Long> paperIdList) throws DataAccessException;

	/**
	 * 查询用户指定试卷最新的做答id
	 * @param uid 用户id
	 * @param paperIdList 试卷id 列表
	 * @return 只有试卷 id、最新作答id 有效
	 * @throws DataAccessException
	 */
	List<UserAnswerSum> findUserPaperLastAnswerId(Long uid, List<Long> paperIdList, List<Integer> objTypeList, Long goodsId, Long productId, Integer paperType) throws DataAccessException;

	/**
	 * 查询用户指定作答id 的答题明细
	 * @param uid
	 * @param userAnswerIdList
	 * @return
	 * @throws DataAccessException
	 */
	List<UserAnswerSum> findPaperAnswerSumDetail(Long uid, List<Long> userAnswerIdList) throws DataAccessException;

	List<UserAnswerErrorQuestionVo> findErrorPaperAnswerSumDetailGroupByAnswerId(Long uid, List<Long> answerIdIdList) throws DataAccessException;

	List<UserAnswerErrorQuestionVo> findErrorHomeworkAnswerSumDetailGroupByAnswerId(Long uid, List<Long> answerIdIdList) throws DataAccessException;
}
