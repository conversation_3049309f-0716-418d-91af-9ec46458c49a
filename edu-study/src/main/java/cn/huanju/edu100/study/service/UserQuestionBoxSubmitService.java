package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

public interface UserQuestionBoxSubmitService {

	/**
	 * 用户做过的题入redis缓存
	 * @param uid
     * @param teachBookId 教材id
     * @param boxId 题库id
	 * @param questionIds 题目id列表
	 * */
	void cachePaperDoneQuestion(Long uid, Long teachBookId, Long boxId, List<Long> questionIds) throws DataAccessException;

	/**
	 * 用户错题入redis缓存
	 * @param uid
     * @param teachBookId 教材id
     * @param boxId 题库id
	 * @param questionIds 题目id列表
	 * @param topicIds 答错的子题id列表
	 * */
	void cachePaperWrongQuestion(Long uid, Long teachBookId, Long boxId, List<Long> questionIds, List<Long> topicIds) throws DataAccessException;


}
