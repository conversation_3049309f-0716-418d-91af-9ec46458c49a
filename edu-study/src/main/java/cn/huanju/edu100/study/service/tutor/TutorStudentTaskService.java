package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorStudentTask;
import cn.huanju.edu100.study.model.tutor.TutorTaskDto;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Map;

/**
 * 学生任务Service
 * <AUTHOR>
 * @version 2016-01-18
 */
public interface TutorStudentTaskService extends BaseService<TutorStudentTask> {

    Map<Long, TutorTaskDto> listPhaseByUidAndCategoryList(Map<String, Object> params) throws DataAccessException;

    boolean updateTaskStatusByUidTaskId(TutorStudentTask tutorStudentTask) throws DataAccessException;

    boolean updateTaskStatusByUidLessonId(Long uid, String classes, Long lessonId, Integer status) throws DataAccessException;

    TutorStudentTask getStudentTaskByTidAndUid(TutorStudentTask task) throws DataAccessException;

    TutorStudentTask getLastTask(Long uid, Long unitId, Long phaseId, Long categoryId) throws DataAccessException;
}