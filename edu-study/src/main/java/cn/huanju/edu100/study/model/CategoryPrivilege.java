package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 考试科目特权信息Entity
 * <AUTHOR>
 * @version 2015-06-02
 */
public class CategoryPrivilege extends DataEntity<CategoryPrivilege> {

	private static final long serialVersionUID = 1L;
	private Long secondCategory;		// second_category
	private Long firstCategory;		// first_category
	private Long categoryId;		// category_id
	private Integer type;		// 0:个性化服务,1:考试不过全额退费或第二年免费重学,2:考试不过终身免费重学直至通过考试，3:24小时在线答疑，4:互动直播答疑，5:终身免费重学,6:考试不过全额退费
	private Date startTime;		// start_time
	private Date endTime;		// end_time
	private Long goodsId;		// goods_id
	private Long serviceId;		// service_id

	public CategoryPrivilege() {
		super();
	}

	public CategoryPrivilege(Long id){
		super(id);
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getFirstCategory() {
		return firstCategory;
	}

	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public Long getServiceId() {
		return serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}

}
