/**
 *
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.onetoone.VMaterial;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 留学资料DAO接口
 * <AUTHOR>
 * @version 2016-12-12
 */
public interface VMaterialDao extends CrudDao<VMaterial> {


    List<VMaterial> findListByParam(Map<String, Object> params) throws DataAccessException;

    boolean insertBatch(Collection<VMaterial> materials) throws DataAccessException;
}
