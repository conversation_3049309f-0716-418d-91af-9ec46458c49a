/**
 * 
 */
package cn.huanju.edu100.study.dao.mock;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.mock.MockApply;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 模考考生报名DAO接口
 * <AUTHOR>
 * @version 2018-04-08
 */
public interface MockApplyDao extends CrudDao<MockApply> {
    public List<MockApply> qryByMockExamId(Long mockExamId, Long uid) throws DataAccessException;
    public List<MockApply> findMockApplyListByMockId(Long mockExamId, Long number, Long uid) throws DataAccessException;
    public List<MockApply> getMockUseTimeRank(Long mockExamId,Long uid)throws DataAccessException;
    MockApply qryByUserAnswerId(Long userAnswerId, Long uid) throws DataAccessException;

    Long countMockApplyByParam(Map<String, Object> param) throws DataAccessException;
}