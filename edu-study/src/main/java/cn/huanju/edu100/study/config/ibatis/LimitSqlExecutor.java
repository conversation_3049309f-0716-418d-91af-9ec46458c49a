package cn.huanju.edu100.study.config.ibatis;

import cn.huanju.edu100.study.config.ibatis.limit.NoLimit;
import com.ibatis.sqlmap.engine.execution.SqlExecutor;
import com.ibatis.sqlmap.engine.mapping.statement.MappedStatement;
import com.ibatis.sqlmap.engine.mapping.statement.RowHandlerCallback;
import com.ibatis.sqlmap.engine.scope.StatementScope;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.constraints.Digits;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class LimitSqlExecutor extends SqlExecutor {

    private int defaultLimit = 10000;    // 默认分页大小
    private String databaseType;       // 数据库类型（mysql/oracle）

    private static final List<String> noLimitMethods = List.of("findAlSubmitQuestionListByPaperOrHomework",
            "findAlSubmitQuestionList",
            "getLastUserAnswerSumAndDetailByQuestionIds",
            "findAlSubmitQuestionListByPaperOrHomework");

    @Override
    public void executeQuery(
            StatementScope request,
            Connection conn,
            String sql,
            Object[] parameters,
            int skipResults,
            int maxResults,
            RowHandlerCallback callback
    ) throws SQLException {
        // 若 SQL 未包含分页关键字，自动追加 LIMIT
        if (!hasLimit(sql) && !hasNoLimitAnnotation(request.getStatement())) {
            String limit = System.getProperty("mybatis.query.default-limit");
            sql = addLimit(sql, NumberUtils.isDigits(limit) ? Integer.parseInt(limit) : defaultLimit);
        }
        super.executeQuery(request, conn, sql, parameters, skipResults, maxResults, callback);
    }

    // 判断 SQL 是否已含分页语法
    private boolean hasLimit(String sql) {
        return sql.toLowerCase().contains("limit")
                || sql.toLowerCase().contains("rownum");
    }


    // 检查方法是否有 @NoLimit 注解
    private boolean hasNoLimitAnnotation(MappedStatement mappedStatement) {
        String methodId = mappedStatement.getId();
        int lastDot = methodId.lastIndexOf(".");
        String className = methodId.substring(0, lastDot);
        String methodName = methodId.substring(lastDot + 1);
        return noLimitMethods.contains(methodName);
//        Class<?> mapperClass = null;
//        try {
//            mapperClass = Class.forName(className);
//            Method method = mapperClass.getMethod(methodName, getMethodParameterTypes(mappedStatement));
//            return method.isAnnotationPresent(NoLimit.class);
//        } catch (ClassNotFoundException e) {
//            e.printStackTrace();
//        } catch (NoSuchMethodException e) {
//            e.printStackTrace();
//        }
//        return false;
    }

    // 获取方法参数类型
    private Class<?>[] getMethodParameterTypes(MappedStatement mappedStatement)
            throws ClassNotFoundException, NoSuchMethodException {

        String methodId = mappedStatement.getId();
        int lastDotIndex = methodId.lastIndexOf(".");
        String className = methodId.substring(0, lastDotIndex);  // 获取接口全限定类名
        String methodName = methodId.substring(lastDotIndex + 1); // 获取方法名

        // 加载 Mapper 接口类
        Class<?> mapperInterface = Class.forName(className);

        // 遍历接口方法，匹配方法名（MyBatis 不允许方法重载，因此方法名唯一）
        for (Method method : mapperInterface.getMethods()) {
            if (method.getName().equals(methodName)) {
                return method.getParameterTypes();
            }
        }

        throw new NoSuchMethodException("Method '" + methodName + "' not found in " + className);
    }

    // 动态追加分页语法
    private String addLimit(String sql, int limit) {
        if (limit < 1000){
            limit = 1000;
        }
        if ("mysql".equalsIgnoreCase(databaseType)) {
            return sql.contains(";") ? sql.replace(";", " LIMIT " + limit + ";") : sql + " LIMIT " + limit;
        } else if ("oracle".equalsIgnoreCase(databaseType)) {
            return "SELECT * FROM (" + sql + ") WHERE ROWNUM <= " + limit;
        }
        return sql;
    }

    // Setter 方法
    public void setDefaultLimit(int defaultLimit) {
        this.defaultLimit = defaultLimit;
    }

    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }
}