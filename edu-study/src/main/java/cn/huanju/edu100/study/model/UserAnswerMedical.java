package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用户答题记录【医卫类科目的全真机考的试卷且为模考电子试卷】
 */
@Data
public class UserAnswerMedical extends DataEntity<UserAnswerMedical> {

	private static final long serialVersionUID = 1L;
	private Long uid;			// uid
//	private String unickname;	// 用户昵称
	private Long ePaperId;		// 电子试卷id
	private Long paperId;		// 试卷id
	private Double score;		// 得分
	private Integer isPass;		// 是否及格 1是 0否
	private String accuracy;	// 正确率
	private Date submitTime;	// 提交时间
	private Long lessonId;		// 课节id

}
