package cn.huanju.edu100.study.service.solution;

import cn.huanju.edu100.study.model.PageModel;
import cn.huanju.edu100.study.model.solution.QuestionRecommend;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.HashMap;


public interface QuestionRecommendService {
    PageModel getUserHotQuestionList(QuestionRecommend questionEntry, Integer from,Integer rows) throws DataAccessException;
    PageModel getManageHotQuestionList(QuestionRecommend questionEntry, Integer from,Integer rows) throws DataAccessException;
    Long insertQuestionInfo(HashMap<String, Object> entryMap) throws DataAccessException;
    boolean updateQuestionRecommend(HashMap<String, Object> entryMap) throws DataAccessException;
}
