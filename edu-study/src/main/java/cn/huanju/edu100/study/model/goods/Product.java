package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.persistence.model.HqFiledAnnotation;

/**
 * 产品的公共抽象信息Entity
 * 
 * <AUTHOR>
 * @version 2015-05-16
 */
public class Product extends DataEntity<Product> implements Cloneable {

	private static final long serialVersionUID = 1L;
	private String name; // name
	private Integer type; // 0,表示录播课程，1.表示图书，2，表示试卷,3 表示直播课程 4服务
	private Long firstCategory; // first_category
	private Long secondCategory; // second_category
	private Long categoryId; // category_id
	private String description; // description
	private Integer status; // 产品状态
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String bPic; // b_pic
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String mPic; // m_pic
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String sPic; // s_pic
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String bak1; // bak1
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String bak2; // bak2
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String bak3; // bak3
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String ip; // ip
	private Long goodsId;// 返回用商品ID
	private Double price;
	private Double showPriority;		// 显示优先级
	private int buyType;
	private Integer isNew;// 0:旧课程，1：新课程
	private Integer isPublic;// 0:非公共产品，1：公共产品
	private Double realCostPrice;		// 成本价格
	/**
	 * 购买类型
	 */
	public static interface BuyType {
		/**
		 * 属于商品内可选的
		 */
		int SELECT = 0;
		/**
		 * 属于额外加购的
		 */
		int EXTRA = 1;
		/**
		 * 属于赠送的
		 */
		int GIFT = 2;
		/**
		 * 属于套餐内默认的
		 */
		int DEFAULT = 3;
	}

	public int getBuyType() {
		return buyType;
	}

	public void setBuyType(int buyType) {
		this.buyType = buyType;
	}

	public Product() {
		super();
	}

	public Product(Long id) {
		super(id);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Long getFirstCategory() {
		return firstCategory;
	}

	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getBPic() {
		return bPic;
	}

	public void setBPic(String bPic) {
		this.bPic = bPic;
	}

	public String getMPic() {
		return mPic;
	}

	public void setMPic(String mPic) {
		this.mPic = mPic;
	}

	public String getSPic() {
		return sPic;
	}

	public void setSPic(String sPic) {
		this.sPic = sPic;
	}

	public String getBak1() {
		return bak1;
	}

	public void setBak1(String bak1) {
		this.bak1 = bak1;
	}

	public String getBak2() {
		return bak2;
	}

	public void setBak2(String bak2) {
		this.bak2 = bak2;
	}

	public String getBak3() {
		return bak3;
	}

	public void setBak3(String bak3) {
		this.bak3 = bak3;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Double getShowPriority() {
		return showPriority;
	}

	public void setShowPriority(Double showPriority) {
		this.showPriority = showPriority;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public Double getPrice() {
		return price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	public Integer getIsNew() {
		return isNew;
	}

	public void setIsNew(Integer isNew) {
		this.isNew = isNew;
	}

	/**
	 * 是否为有实物的商品
	 * 
	 * @return
	 */
//	public boolean isRealProduct() {
//		return Consts.ProductType.BOOK == this.type
//				|| Consts.ProductType.REAL_PAPER == this.type;
//	}

    public Integer getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Integer isPublic) {
        this.isPublic = isPublic;
    }

	public Double getRealCostPrice() {
		return realCostPrice;
	}

	public void setRealCostPrice(Double realCostPrice) {
		this.realCostPrice = realCostPrice;
	}
}