package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.UserRmBoxErrQuestionDao;
import cn.huanju.edu100.study.model.UserRmBoxErrQuestionLog;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserQBoxRmErrQuestionService;
import cn.huanju.edu100.study.service.UserQuestionBoxService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.DateUtil;
import cn.huanju.edu100.exception.DataAccessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class UserQBoxRmErrQuestionServiceImpl extends BaseServiceImpl<UserRmBoxErrQuestionDao, UserRmBoxErrQuestionLog> implements
		UserQBoxRmErrQuestionService {

	private static Logger logger = LoggerFactory.getLogger(UserQBoxRmErrQuestionServiceImpl.class);

	@Autowired
    private KnowledgeResource knowledgeResource;

	@Autowired
	private UserQuestionBoxService userQuestionBoxService;

	/**
	 * 用户主动移除题库中的错题
	 *
	 * */
	@Override
	public void updateUserRmErrQuestionLog(Long uid, Long boxId, Long bookId, Long objId,
			Integer objType, List<Long> questionIds) throws DataAccessException {
		List<UserRmBoxErrQuestionLog> logsAdd = new ArrayList<UserRmBoxErrQuestionLog>();
		List<UserRmBoxErrQuestionLog> logsUpdate = new ArrayList<UserRmBoxErrQuestionLog>();
		List<Long> validQids = new ArrayList<Long>();

		// 1.判断当前questionIds集合里，哪些是需要更新，哪些需要新增
		UserRmBoxErrQuestionLog param = new UserRmBoxErrQuestionLog();
		param.setUid(uid);
		param.setBoxId(boxId);
		param.setBookId(bookId);
		param.setObjId(objId);
		param.setObjType(objType);

		for (Long questionId : questionIds) {
			param.setQuestionId(questionId);
			List<UserRmBoxErrQuestionLog> logsDB = dao.findList(param);

			if (logsDB == null || logsDB.isEmpty()) {//该类别下，用户无该错题的移除记录，新增
				logsAdd.add(param);
			}else{//更新
				for (UserRmBoxErrQuestionLog log : logsDB) {
					logsUpdate.add(log);
				}
			}
		}

		// 2.更新用户的移除记录
		int addCnt = 0;
		int uptCnt = 0;
		if (!logsAdd.isEmpty()) {
			for (UserRmBoxErrQuestionLog userRmBoxErrQuestionLog : logsAdd) {
				validQids.add(userRmBoxErrQuestionLog.getQuestionId());
				userRmBoxErrQuestionLog.setRemoveType(Consts.User_Box_Err_Question_Rm_Type.Initiative);
			}
			addCnt = dao.insertlogUserBoxRmQuestionBatch(logsAdd, uid);
		}

		if (!logsUpdate.isEmpty()) {
			for (UserRmBoxErrQuestionLog userRmBoxErrQuestionLog : logsUpdate) {
				validQids.add(userRmBoxErrQuestionLog.getQuestionId());
				userRmBoxErrQuestionLog.setRemoveType(Consts.User_Box_Err_Question_Rm_Type.Initiative);
			}
			uptCnt = dao.updatelogUserBoxRmQuestionBatch(logsUpdate, uid);
		}

		logger.info("updateUserRmErrQuestionLog total:{},addCnt:{},uptCnt:{}",questionIds.size(),addCnt,uptCnt);
		// 3.处理redis缓存的移除
		if (addCnt + uptCnt > 0 && validQids.size() > 0) {//这里判断一下数据量
			// 处理redis缓存数据
			userQuestionBoxService.removeUserWrongQuestion(uid, boxId, validQids);
		}else {
			throw new DataAccessException("updateUserRmErrQuestionLog error, DB operate faild!");
		}

	}

	/**
	 * 用户主动移除题库错题（批量obj_id）
	 * */
	@Override
	public void updateUserRmErrQuestionLogBatchObj(Long uid, Long boxId,
			Long bookId, List<Long> objIds, Integer objType, Long questionId)
			throws DataAccessException {
		List<UserRmBoxErrQuestionLog> logsAdd = new ArrayList<UserRmBoxErrQuestionLog>();
		List<UserRmBoxErrQuestionLog> logsUpdate = new ArrayList<UserRmBoxErrQuestionLog>();
		List<Long> validQids = new ArrayList<Long>();
		validQids.add(questionId);

		// 1.判断当前objIds集合里，哪些是需要更新，哪些需要新增
		UserRmBoxErrQuestionLog param = new UserRmBoxErrQuestionLog();
		param.setUid(uid);
		param.setBoxId(boxId);
		param.setBookId(bookId);
		param.setQuestionId(questionId);
		param.setObjType(objType);

		for (Long objId : objIds) {
			param.setObjId(objId);
			List<UserRmBoxErrQuestionLog> logsDB = dao.findList(param);

			if (logsDB == null || logsDB.isEmpty()) {//该类别下，用户无该错题的移除记录，新增
				logsAdd.add(param);
			}else{//更新
				for (UserRmBoxErrQuestionLog log : logsDB) {
					logsUpdate.add(log);
				}
			}
		}

		// 2.更新用户的移除记录
		int addCnt = 0;
		int uptCnt = 0;
		if (!logsAdd.isEmpty()) {
			for (UserRmBoxErrQuestionLog userRmBoxErrQuestionLog : logsAdd) {
				userRmBoxErrQuestionLog.setRemoveType(Consts.User_Box_Err_Question_Rm_Type.Initiative);
			}
			addCnt = dao.insertlogUserBoxRmQuestionBatch(logsAdd, uid);
		}

		if (!logsUpdate.isEmpty()) {
			for (UserRmBoxErrQuestionLog userRmBoxErrQuestionLog : logsUpdate) {
				userRmBoxErrQuestionLog.setRemoveType(Consts.User_Box_Err_Question_Rm_Type.Initiative);
			}
			uptCnt = dao.updatelogUserBoxRmQuestionBatch(logsUpdate, uid);
		}

		logger.info("updateUserRmErrQuestionLogBatchObj total:{},addCnt:{},uptCnt:{}",objIds.size(),addCnt,uptCnt);
		// 3.处理redis缓存的移除
		if (addCnt + uptCnt > 0 && validQids.size() > 0) {//这里判断一下数据量
			// 处理redis缓存数据
			userQuestionBoxService.removeUserWrongQuestion(uid, boxId, validQids);
		}else {
			throw new DataAccessException("updateUserRmErrQuestionLogBatchObj error, DB operate faild!");
		}
	}

	/**
	 * 用户主动移除已消灭题库错题（批量obj_id）
	 * */
	@Override
	public void updateWipeOutRmErrQuestionLogBatchObj(Long uid, Long boxId,
			Long bookId, List<Long> objIds, Integer objType, Long questionId) throws DataAccessException {
		List<UserRmBoxErrQuestionLog> logsAdd = new ArrayList<UserRmBoxErrQuestionLog>();
		List<UserRmBoxErrQuestionLog> logsUpdate = new ArrayList<UserRmBoxErrQuestionLog>();
		List<Long> validQids = new ArrayList<Long>();
		validQids.add(questionId);

		// 1.判断当前objIds集合里，哪些是需要更新，哪些需要新增
		UserRmBoxErrQuestionLog param = new UserRmBoxErrQuestionLog();
		param.setUid(uid);
		param.setBoxId(boxId);
		param.setBookId(bookId);
		param.setQuestionId(questionId);
		param.setObjType(objType);

		for (Long objId : objIds) {
			param.setObjId(objId);
			List<UserRmBoxErrQuestionLog> logsDB = dao.findList(param);

			if (logsDB == null || logsDB.isEmpty()) {//该类别下，用户无该错题的移除记录，新增
				logsAdd.add(param);
			}else{//更新
				for (UserRmBoxErrQuestionLog log : logsDB) {
					logsUpdate.add(log);
				}
			}
		}

		// 2.更新用户的移除记录
		int addCnt = 0;
		int uptCnt = 0;
		if (!logsAdd.isEmpty()) {
			for (UserRmBoxErrQuestionLog userRmBoxErrQuestionLog : logsAdd) {
				userRmBoxErrQuestionLog.setRemoveType(Consts.User_Box_Err_Question_Rm_Type.Wipeout);
			}
			addCnt = dao.insertlogUserBoxRmQuestionBatch(logsAdd, uid);
		}

		if (!logsUpdate.isEmpty()) {
			for (UserRmBoxErrQuestionLog userRmBoxErrQuestionLog : logsUpdate) {
				userRmBoxErrQuestionLog.setRemoveType(Consts.User_Box_Err_Question_Rm_Type.Wipeout);
			}
			uptCnt = dao.updatelogUserBoxRmQuestionBatch(logsUpdate, uid);
		}

		logger.info("updateWipeOutRmErrQuestionLogBatchObj total:{},addCnt:{},uptCnt:{}",objIds.size(),addCnt,uptCnt);
		// 3.处理redis缓存的移除
		if (addCnt + uptCnt > 0 && validQids.size() > 0) {//这里判断一下数据量
			// 处理redis缓存数据
			userQuestionBoxService.removeWipeOutWrongQuestion(uid, boxId, validQids);
		}else {
			throw new DataAccessException("updateWipeOutRmErrQuestionLogBatchObj error, DB operate faild!");
		}
	}


	/**
	 * 题库5.0用户主动移除已消灭题库错题（批量obj_id）
	 * */
	@Override
	public void updateNewWipeOutRmErrQuestionLogBatchObj(Long uid, Long boxId,
													  Long bookId, List<Long> objIds, Integer objType, Long questionId) throws DataAccessException {
		List<UserRmBoxErrQuestionLog> logsAdd = new ArrayList<UserRmBoxErrQuestionLog>();
		List<UserRmBoxErrQuestionLog> logsUpdate = new ArrayList<UserRmBoxErrQuestionLog>();
		List<Long> validQids = new ArrayList<Long>();
		validQids.add(questionId);

		// 1.判断当前objIds集合里，哪些是需要更新，哪些需要新增
		UserRmBoxErrQuestionLog param = new UserRmBoxErrQuestionLog();
		param.setUid(uid);
		param.setBoxId(boxId);
		param.setBookId(bookId);
		param.setQuestionId(questionId);
		param.setObjType(objType);

		for (Long objId : objIds) {
			param.setObjId(objId);
			List<UserRmBoxErrQuestionLog> logsDB = dao.findList(param);

			if (logsDB == null || logsDB.isEmpty()) {//该类别下，用户无该错题的移除记录，新增
				logsAdd.add(param);
			}else{//更新
				for (UserRmBoxErrQuestionLog log : logsDB) {
					logsUpdate.add(log);
				}
			}
		}

		// 2.更新用户的移除记录
		int addCnt = 0;
		int uptCnt = 0;
		if (!logsAdd.isEmpty()) {
			for (UserRmBoxErrQuestionLog userRmBoxErrQuestionLog : logsAdd) {
				userRmBoxErrQuestionLog.setRemoveType(Consts.User_Box_Err_Question_Rm_Type.Wipeout);
			}
			addCnt = dao.insertlogUserBoxRmQuestionBatch(logsAdd, uid);
		}

		if (!logsUpdate.isEmpty()) {
			for (UserRmBoxErrQuestionLog userRmBoxErrQuestionLog : logsUpdate) {
				userRmBoxErrQuestionLog.setRemoveType(Consts.User_Box_Err_Question_Rm_Type.Wipeout);
			}
			uptCnt = dao.updatelogUserBoxRmQuestionBatch(logsUpdate, uid);
		}

		logger.info("updateNewWipeOutRmErrQuestionLogBatchObj total:{},addCnt:{},uptCnt:{}",objIds.size(),addCnt,uptCnt);
		// 3.处理redis缓存的移除
		if (addCnt + uptCnt > 0 && validQids.size() > 0) {//这里判断一下数据量
			// 处理redis缓存数据
			userQuestionBoxService.removeNewWipeOutWrongQuestion(uid, boxId, validQids);
		}else {
			throw new DataAccessException("updateNewWipeOutRmErrQuestionLogBatchObj error, DB operate faild!");
		}
	}

	/**
	 * 判断用户该错题在作答时间t1之后是否有主动移除过？
	 * true：有过主动移除记录
	 * false：无主动移除记录
	 * */
	@Override
	public Boolean judgeUserQuestionIsRemove(Long uid, Long boxId, Long bookId,
			Long objId, Integer objType, Long questionId, Date answerDate)
			throws DataAccessException {
		// TODO Auto-generated method stub

		// 1.将用户该题该类别下的移除记录取出来
		UserRmBoxErrQuestionLog param = new UserRmBoxErrQuestionLog();
		param.setUid(uid);
		param.setBoxId(boxId);
		param.setBookId(bookId);
		param.setObjId(objId);
		param.setObjType(objType);
		param.setQuestionId(questionId);
		List<UserRmBoxErrQuestionLog> logs = dao.findList(param);
		for (UserRmBoxErrQuestionLog log : logs) {
			Date removeDate = log.getRemoveDate();
			int compare = DateUtil.compare(answerDate, removeDate);
			if (compare > 0) {
				return true;
			}
		}

		return false;
	}

}
