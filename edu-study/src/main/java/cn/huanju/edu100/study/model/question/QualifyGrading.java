package cn.huanju.edu100.study.model.question;


import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 * 排位赛段位信息Entity
 * <AUTHOR>
 * @version 2018-04-25
 */
public class QualifyGrading extends DataEntity<QualifyGrading> {
	
	private static final long serialVersionUID = 1L;
	private Long uid;		// 用户uid
	private Long secondCategory;		// 考试id
	private Long categoryId;		// 科目id
	private Double grading;		// 段位
	private Double viceGrading;		// 副段位
	private Double starNum;		// 星数
	private Long maxWinNum;		// 历史最高连胜数
	private Long lastWinNum;		// 上次连胜数
	private Integer isLastWin;		// 上次是否连胜（0否，1是）
	private Long challegeNum;		// 累计挑战数
	private Long answerNum;		// 累计答题数
	private Long correctNum;		// 累计正确数
	private Double accuracy;		// 累计正确率
	private Integer season;     //赛季
	private String extInfo;

	private Long ranking;	//排名

	private List<Long> uids;	//用户ids，查询使用
	
	public QualifyGrading() {
		super();
	}

	public QualifyGrading(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}
	
	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}
	
	public Double getGrading() {
		return grading;
	}

	public void setGrading(Double grading) {
		this.grading = grading;
	}
	
	public Double getViceGrading() {
		return viceGrading;
	}

	public void setViceGrading(Double viceGrading) {
		this.viceGrading = viceGrading;
	}
	
	public Double getStarNum() {
		return starNum;
	}

	public void setStarNum(Double starNum) {
		this.starNum = starNum;
	}
	
	public Long getMaxWinNum() {
		return maxWinNum;
	}

	public void setMaxWinNum(Long maxWinNum) {
		this.maxWinNum = maxWinNum;
	}
	
	public Long getLastWinNum() {
		return lastWinNum;
	}

	public void setLastWinNum(Long lastWinNum) {
		this.lastWinNum = lastWinNum;
	}
	
	public Integer getIsLastWin() {
		return isLastWin;
	}

	public void setIsLastWin(Integer isLastWin) {
		this.isLastWin = isLastWin;
	}
	
	public Long getChallegeNum() {
		return challegeNum;
	}

	public void setChallegeNum(Long challegeNum) {
		this.challegeNum = challegeNum;
	}
	
	public Long getAnswerNum() {
		return answerNum;
	}

	public void setAnswerNum(Long answerNum) {
		this.answerNum = answerNum;
	}
	
	public Long getCorrectNum() {
		return correctNum;
	}

	public void setCorrectNum(Long correctNum) {
		this.correctNum = correctNum;
	}
	
	public Double getAccuracy() {
		return accuracy;
	}

	public void setAccuracy(Double accuracy) {
		this.accuracy = accuracy;
	}

	public Long getRanking() {
		return ranking;
	}

	public void setRanking(Long ranking) {
		this.ranking = ranking;
	}

	public List<Long> getUids() {
		return uids;
	}

	public void setUids(List<Long> uids) {
		this.uids = uids;
	}

	public Integer getSeason() {
		return season;
	}

	public void setSeason(Integer season) {
		this.season = season;
	}

	public String getExtInfo() {
		return extInfo;
	}

	public void setExtInfo(String extInfo) {
		this.extInfo = extInfo;
	}
}