package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.RecordcourseDao;
import cn.huanju.edu100.study.model.Recordcourse;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.util.EduStringUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;

public class RecordcourseIbatisImpl extends CrudIbatisImpl2<Recordcourse> implements RecordcourseDao {

	public RecordcourseIbatisImpl() {
		super("Recordcourse");
	}

	@Override
	public List<Recordcourse> findListByTaskIdList(List<Long> taskIdList) throws DataAccessException {
		if (ValidateUtils.isEmpty(taskIdList)) {
			logger.error("findListByTaskId {} error, parameter taskIdList is empty,taskIdList:{}", namespace, taskIdList);
			throw new DataAccessException("findListByTaskId error,parameter error");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			String idList = EduStringUtils.getUniqueIds(taskIdList);
			return (List<Recordcourse>) sqlMap.queryForList("Recordcourse.findListByTaskIdList", idList);
		} catch (SQLException e) {
			logger.error("findListByTaskId {} SQLException taskIdList:{}", namespace, taskIdList, e);
			throw new DataAccessException("get SQLException error" + e.getMessage());
		}
	}

}
