package cn.huanju.edu100.study.model.homework.papercorrection;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "paper_correction_sum",autoResultMap = true)
public class PaperCorrectionSum {
    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    private Long  id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * uid
     */
    private Long uid;
    /**
     * 商品id
     */
    private Long goodsId;
    /**
     * 服务id
     */
    private Long serviceId;
    /**
     * 总次数
     */
    private Integer total;
    /**
     * 已使用次数
     */
    private Integer usedTotal;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
