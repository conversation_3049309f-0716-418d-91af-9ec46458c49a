package cn.huanju.edu100.study.model.goods;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 一层一层包含的结构
 * <AUTHOR>
 *
 */
public class ProdAndThirdOfGoods implements Serializable{

	/**
	 *
	 */
	private static final long serialVersionUID = -2352204378433075906L;

	private Long goodsId;

	private Long skuId;

	/**
	 * 当前商品包含的产品的重量和
	 *   即productList里的重量和
	 * 单位 克
	 *
	 */
	private Integer curWeight;
	/**
	 * 当前商品包含的实体商品数目
	 */
	private Integer curRealNum = 0;
	private Goods goods;
	/**
	 * 类型 必选、可选、配件、赠品
	 */
	private Integer buyType;

	/**
	 * 该商品包含的产品信息
	 */
	private List<Product> productList = new ArrayList<Product>();
	/**
	 * content pair中包含的商品、套餐的产品、第三方商品信息。
	 * 目前没有分开的要求，如果要分开，这块请重构
	 * 可选商品、配件等可以goodsId重复的时候才需要重构
	 */
	private Map<String, ProdAndThirdOfGoods> subGoodsInfo = new HashMap<String, ProdAndThirdOfGoods>();

	public ProdAndThirdOfGoods(){

	}
	public ProdAndThirdOfGoods(final Goods goods){
		if(null != goods) {
			this.goodsId = goods.getId();
			this.goods = goods;
		}
	}

	public Integer getCurRealNum() {
		return curRealNum;
	}

	public void setCurRealNum(Integer curRealNum) {
		this.curRealNum = curRealNum;
	}

	public Goods getGoods() {
		return goods;
	}

	public void setGoods(Goods goods) {
		this.goods = goods;
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public List<Product> getProductList() {
		return productList;
	}

	public Integer getCurWeight() {
		return curWeight;
	}

	public void setCurWeight(Integer curWeight) {
		this.curWeight = curWeight;
	}

	public static String getGoodsKey(final Long goodsId, final Long skuId){
		StringBuffer buf = new StringBuffer();
		buf.append(goodsId);
		if(null != skuId){
			buf.append("_").append(skuId);
		}

		return buf.toString();
	}
	public String getGoodsKey(){
		return ProdAndThirdOfGoods.getGoodsKey(this.getGoodsId(), this.getSkuId());
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public Integer getBuyType() {
		return buyType;
	}

	public void setBuyType(Integer buyType) {
		this.buyType = buyType;
	}

	public void setProductList(List<Product> productList) {
		this.productList = productList;
	}

	public Map<String, ProdAndThirdOfGoods> getSubGoodsInfo() {
		return subGoodsInfo;
	}

	public void setSubGoodsInfo(Map<String, ProdAndThirdOfGoods> subGoodsInfo) {
		this.subGoodsInfo = subGoodsInfo;
	}
}
