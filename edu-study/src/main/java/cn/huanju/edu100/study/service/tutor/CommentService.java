package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.dto.CommentStarGroup;
import cn.huanju.edu100.study.model.dto.MyComment;
import cn.huanju.edu100.study.model.tutor.Comment;
import cn.huanju.edu100.study.model.tutor.CommentElement;
import cn.huanju.edu100.study.model.tutor.CommentThumb;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.model.tutor.ReplyCommentQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 评价Service
 * <AUTHOR>
 * @version 2017-12-06
 */
public interface CommentService extends BaseService<Comment> {

    CommentElement getElement(Long goodsGroupId, Long goodsId, Long productId, Long objId, Integer objType) throws DataAccessException;

    Long submitComment(CommentElement commentElement) throws BusinessException,DataAccessException;
    /**
     * @description 直播间提交评价
     * <AUTHOR>
     * @time 2019年1月11日 下午4:49:11
     * */
    Long submitCommentForLive(CommentElement commentElement) throws BusinessException,DataAccessException;


    List<Comment> findListByObj(Long uid, Long objId, Integer objType, Integer queryType, int from, int rows, Long schId) throws DataAccessException;

    List<Comment> findCommentByCommentId(Long commentId) throws DataAccessException;

    List<Comment> findListByObjNew(CommentElement commentElement) throws DataAccessException;

    List<Comment> findListByGoodsGroupId(Long goodsGroupId, Integer objType, int from, int rows,Integer queryType,List<Integer> objTypeList) throws DataAccessException;

    List<Comment> findListByGoodsId(List<Long> goodsIdList, Integer objType, int from, int rows,Integer queryType,List<Integer> objTypeList) throws DataAccessException;

    int queryCommentCount(CommentElement commentElement) throws DataAccessException;

    boolean inCreThumbUpNum(Long id, Long uid, String ip) throws DataAccessException;

    Map<String, Object> findListByUid(Long uid, Integer objType, Collection<Long> idList, Long schId) throws DataAccessException;

    /**
     * @description 根据map类型参数获取评论列表
     * <AUTHOR>
     * @time 2018年8月14日 下午3:05:53
     * */
    List<Comment> findListByMap( Map<String, Object> params) throws DataAccessException;
    /**
     * @description 根据map类型参数获取评论数量
     * <AUTHOR>
     * @time 2018年8月14日 下午3:05:53
     * */
    public int queryCommentCount(Map<String, Object> param) throws DataAccessException;

    List<CommentStarGroup> findStarByGroup(Map<String, Object> param) throws DataAccessException;

    int updateBatch(Map<String, Object> param) throws DataAccessException;

    int queryCommentCountParam(Map<String, Object> param) throws DataAccessException;

    List<Comment> findCommentListByIdList(Map<String, Object> param) throws DataAccessException;

    Boolean hasSensitive(String content);

    Boolean reviewContent(Long uid, String content)  throws DataAccessException ;

    List<MyComment> findMyComments(Long uid, Integer objType, Integer queryType, int from, int rows,
            Long schId,Long categoryId) throws DataAccessException;
    boolean thumbCancelTumb(Long id, Long uid) throws DataAccessException;


    List<CommentThumb> findUserThumbUpList(Long uid, List<Long> commentIdList) throws DataAccessException;

    List<Comment> getCommentReplyListByComments(List<Comment> belongCommentList) throws DataAccessException;

    Comment getReplyByCommentId(ReplyCommentQuery replyCommentQuery) throws DataAccessException;

    Comment sty_queryReplyCommentByReplyId(ReplyCommentQuery replyCommentQuery) throws DataAccessException;

}
