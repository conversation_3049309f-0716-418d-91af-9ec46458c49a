package cn.huanju.edu100.study.config.ibatis.encrypt;

import cn.huanju.edu100.util.Sm4Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

@Slf4j
@MappedTypes({String.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class SM4EncryptNameTypeHandler extends BaseTypeHandler {

    public final String NAME_KEY = "40E4FF75C0515D0B131036DB232C7EA9";

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object o, JdbcType jdbcType) throws SQLException {
        if(o != null && !StringUtils.hasText(o.toString())){
            ps.setString(i, (String) o);
        }
        if(o instanceof String) {
            ps.setString(i, Sm4Util.encryptEcb(NAME_KEY,(String) o));
        }else{
            ps.setString(i, Sm4Util.encryptEcb(NAME_KEY,String.valueOf(o)));
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try{
            return Sm4Util.decryptEcb(NAME_KEY,rs.getString(columnName));
        }catch (Exception e) {
            return rs.getString(columnName);
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, int i) throws SQLException {
        try{
            return Sm4Util.decryptEcb(NAME_KEY,rs.getString(i));
        }catch (Exception e) {
            return rs.getString(i);
        }
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int i) throws SQLException {
        try{
            return Sm4Util.decryptEcb(NAME_KEY,cs.getString(i));
        }catch (Exception e) {
            return cs.getString(i);
        }
    }

    public static void main(String[] args) throws Exception {

        String value = "1234abcdjfdhjkafdyusdf";
        String a = Sm4Util.encryptEcb(value);
        System.out.println(a);
        String b = Sm4Util.decryptEcb(a);
        System.out.println(b);
    }
}
