package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 知识点通过率Entity
 * 
 * <AUTHOR>
 * @version 2016-01-20
 */
public class TutorStudentKnowledgeCount extends DataEntity<TutorStudentKnowledgeCount> {

    private static final long serialVersionUID = 1L;
    private String classes; // classes
    private Long knowledgeId; // knowledge_id
    private Long firstCategory; // first_category
    private Long secondCategory; // second_category
    private Long categoryId; // category_id
    private Double passed; // passed

    private String knowledgeName; // 知识点名

    private Long lessonId; // 课件id
    private Long taskId; // task_id
    private Integer result; // 知识点掌握状态

    public TutorStudentKnowledgeCount() {
        super();
    }

    public TutorStudentKnowledgeCount(Long id) {
        super(id);
    }

    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

    public Long getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(Long knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Double getPassed() {
        return passed;
    }

    public void setPassed(Double passed) {
        this.passed = passed;
    }

    public String getKnowledgeName() {
        return knowledgeName;
    }

    public void setKnowledgeName(String knowledgeName) {
        this.knowledgeName = knowledgeName;
    }

    public Long getLessonId() {
        return lessonId;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

}