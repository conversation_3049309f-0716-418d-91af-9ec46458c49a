package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.RecordcourseDao;
import cn.huanju.edu100.study.model.Recordcourse;
import cn.huanju.edu100.study.service.RecordcourseService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RecordcourseServiceImpl extends BaseServiceImpl<RecordcourseDao, Recordcourse> implements RecordcourseService {
	@Override
	public List<Recordcourse> findListByTaskIdList(List<Long> taskIdList) throws DataAccessException {
		return dao.findListByTaskIdList(taskIdList);
	}
}
