package cn.huanju.edu100.study.model.homework.trainbrush;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Created by zhanghong on 2023/2/2.
 */
@Data
@TableName(value = "user_train_brush_question_result",autoResultMap = true)
public class UserTrainBrushQuestionResult {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 用户uid
     */
    private Long uid;


    /**
     * 当前刷题所在群组id
     */
    private Long wxGroupId;


    /**
     * 当前刷题任务id
     */
    private Long wxBrushTaskId;

    /**
     *  准确率
     */
    private Double accuracy;


    /**
     * 作答消耗时间
     */
    private Long usetime;


    /**
     * 当前答题数
     */
    private Long answerNum;


    /**
     * 用户开始答卷时间
     */
    private Date startTime;


    /**
     * 用户提交试卷时间
     */
    private Date endTime;

    /**
     * 状态，0未开始 1进行中 2已交卷 3已评卷
     */
    private Integer state;


    /**
     * 创建时间
     */
    private Date createDate;


    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 所属终端
     */
    private String platForm;

    /**
     * 所属终端
     */
    private String appid;

    /**
     * 删除标识 0正常 1删除
     */
    private Integer delFlag;


    /**
     * 用户刷题记录id
     */
    private Long userAnswerHomeworkId;

    /**
     * 用户本次刷题questionIds
     */
    private String answerQuestionIds;

    /**
     * 用户答对答题ids,逗号分隔
     */
    private String rightQuestionIds;
}
