package cn.huanju.edu100.study.service.impl.expression;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.cache.DataCache;
import cn.huanju.edu100.study.dao.expression.ExpressionMemberDao;
import cn.huanju.edu100.study.model.expression.ExpressionMember;
import cn.huanju.edu100.study.service.expression.ExpressionMemberService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 规则关联Service
 *
 * <AUTHOR>
 * @version 2016-05-23
 */
@Service
public class ExpressionMemberServiceImpl extends BaseServiceImpl<ExpressionMemberDao, ExpressionMember> implements
        ExpressionMemberService {

    @Autowired
    private DataCache dataCache;

    @Override
    public List<ExpressionMember> findListByParam(Set<Long> ruleIdSet, Set<Long> groupIdSet, Integer ruleType) throws DataAccessException {

        if (CollectionUtils.isEmpty(ruleIdSet) && CollectionUtils.isEmpty(groupIdSet)) {
            logger.error("findListByParam fail, param is all null");
            throw new DataAccessException("findListByParam fail, param is all null");
        }

        List<Long> ruleIdList = null;
        if (CollectionUtils.isNotEmpty(ruleIdSet)) {
            ruleIdList = Lists.newArrayList();
            ruleIdList.addAll(ruleIdSet);
        }

        List<Long> groupIdList = null;
        if (CollectionUtils.isNotEmpty(groupIdSet)) {
            groupIdList = Lists.newArrayList();
            groupIdList.addAll(groupIdSet);
        }

        List<ExpressionMember> expressionMembers = new ArrayList<ExpressionMember>();
        if (!CollectionUtils.isEmpty(ruleIdList)) {
            expressionMembers.addAll(dataCache.getExpressionMemberByRuleIdList(ruleIdList, ruleType));
        }
        if (!CollectionUtils.isEmpty(groupIdList)) {
            expressionMembers.addAll(dataCache.getExpressionMemberByGroupList(groupIdList));
        }
        //return dao.findListByParam(ruleIdList, groupIdList, ruleType);
        return expressionMembers;
    }

}
