/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorUserVideoLogDao;
import cn.huanju.edu100.study.model.tutor.TutorUserVideoLog;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 学员观看记录DAO接口
 * <AUTHOR>
 * @version 2016-01-20
 */
public class TutorUserVideoLogIbatisImpl extends CrudIbatisImpl2<TutorUserVideoLog> implements
		TutorUserVideoLogDao {

	public TutorUserVideoLogIbatisImpl() {
		super("TutorUserVideoLog");
	}

    @Override
    public List<TutorUserVideoLog> getTutorUserVideoLog(Map<String, Object> params)
            throws DataAccessException {
        if (params == null) {
            return null;
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorUserVideoLog>) sqlMap
                    .queryForList("TutorUserVideoLog.findList", params);
        } catch (SQLException e) {
            logger.error("getTutorUserVideoLog SQLException.", e);
            throw new DataAccessException("getTutorUserVideoLog SQLException error");
        }
    }


    @Override
    public Long insertTutorUserVideoLog(TutorUserVideoLog tutorUserVideoLog) throws DataAccessException {
        try {
            // 灰度期间两边都插入

            SqlMapClient mainSqlMap = super.getMaster();
            Long newId =  (Long) mainSqlMap.insert(super.namespace +".insert", tutorUserVideoLog);

            SqlMapClient sqlMap = super.getShardingMaster();
            tutorUserVideoLog.setId(newId);
            sqlMap.insert(super.namespace + ".insertSharding", tutorUserVideoLog);


            return newId;
        } catch (SQLException e) {
            logger.error("insertTutorUserVideoLog SQLException.uid:{}", tutorUserVideoLog, e);
            throw new DataAccessException("insertTutorUserVideoLog SQLException error");
        }
    }

    @Override
    public boolean updateTutorUserVideoLog(TutorUserVideoLog tutorUserVideoLog) throws DataAccessException {
        try {
            // 灰度期间两边都更新
            SqlMapClient sqlMap = super.getMaster();

            sqlMap.update(super.namespace + ".update", tutorUserVideoLog);


            SqlMapClient mainSqlMap = super.getShardingMaster();
            mainSqlMap.update(super.namespace + ".update", tutorUserVideoLog);

            return true;
        } catch (SQLException e) {
            logger.error("updateTutorUserVideoLog SQLException.uid:{}", tutorUserVideoLog, e);
            throw new DataAccessException("updateTutorUserVideoLog SQLException error");
        }
    }
    @Override
    public List<TutorUserVideoLog> findByTaskListAndUid(String classes, Long uid, List<Long> taskIdList)
            throws DataAccessException {
        if (StringUtils.isBlank(classes) || uid == null || uid == 0 || CollectionUtils.isEmpty(taskIdList)){
            logger.error("findByTaskListAndUid param classes or uid or phaseId is empty , classes:{}, uid:{},taskIdList:{}",classes,uid,GsonUtil.toJson(taskIdList));
            throw new DataAccessException("findByTaskListAndUid param classes or uid or taskIdList is empty ");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String,Object> params = Maps.newHashMap();
            params.put("classes",classes);
            params.put("uid",uid);
            params.put("taskIdList",taskIdList);
            return (List<TutorUserVideoLog>) sqlMap
                    .queryForList("TutorUserVideoLog.findByTaskListAndUid", params);
        } catch (SQLException e) {
            logger.error("findByTaskListAndUid SQLException.", e);
            throw new DataAccessException("findByTaskListAndUid SQLException error");
        }
    }
}
