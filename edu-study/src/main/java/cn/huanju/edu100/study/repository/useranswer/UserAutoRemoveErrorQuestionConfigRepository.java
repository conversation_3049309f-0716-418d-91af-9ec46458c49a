package cn.huanju.edu100.study.repository.useranswer;

import cn.huanju.edu100.study.mapper.useranswer.UserAutoRemoveErrorQuestionConfigMapper;
import cn.huanju.edu100.study.model.useranswer.UserAutoRemoveErrorQuestionConfigPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

@Repository
public class UserAutoRemoveErrorQuestionConfigRepository extends ServiceImpl<UserAutoRemoveErrorQuestionConfigMapper,UserAutoRemoveErrorQuestionConfigPO> {
    public UserAutoRemoveErrorQuestionConfigPO getOneByUid(Long uid) {
        if (uid == null) {
            return null;
        }
        LambdaQueryWrapper<UserAutoRemoveErrorQuestionConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAutoRemoveErrorQuestionConfigPO::getUid, uid);
        queryWrapper.last(" limit 1");
        return getBaseMapper().selectOne(queryWrapper);
    }
}
