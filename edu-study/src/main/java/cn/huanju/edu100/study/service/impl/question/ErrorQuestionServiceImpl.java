package cn.huanju.edu100.study.service.impl.question;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.UserHistoryErrorQuestionAddLog;
import cn.huanju.edu100.study.model.UserSubErrorQuestion;
import cn.huanju.edu100.study.model.paper.UserErrorQuestionToPdfTaskPO;
import cn.huanju.edu100.study.repository.UserErrorQuestionToPdfTaskRepository;
import cn.huanju.edu100.study.repository.question.UserHistoryErrorQuestionAddLogRepository;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserSubErrorQuestionRemoveService;
import cn.huanju.edu100.study.service.UserSubErrorQuestionService;
import cn.huanju.edu100.study.service.question.ErrorQuestionService;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.study.util.ThreadPoolFactoryUtil;
import cn.huanju.edu100.study.util.pdf.PdfUtil;
import cn.huanju.edu100.thrift.client.dto.ChapterSectionItemDTO;
import cn.huanju.edu100.util.DateUtil;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.JSONUtils;
import com.hqwx.study.dto.UserAutoRemoveErrorQuestionConfigDTO;
import com.hqwx.study.dto.UserCategoryHistoryErrorQuestionInfo;
import com.hqwx.study.dto.UserErrorQuestionToPdfTaskDTO;
import com.hqwx.study.dto.command.UserErrorQuestionToPdfTaskCommand;
import com.hqwx.study.dto.query.OpenAutoRemoveQuery;
import com.hqwx.study.dto.query.UserErrorQuestionToPdfTaskQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ErrorQuestionServiceImpl implements ErrorQuestionService {

    private Logger logger = LoggerFactory.getLogger(getClass());
    private static final Date HISTORY_START_TIME = DateUtil.stringToDate("2022-11-01","yyyy-MM-dd");
    private static final Date HISTORY_END_TIME = DateUtil.stringToDate("2023-12-01","yyyy-MM-dd");
    @Autowired
    private CompatableRedisClusterClient answerCompatableRedisClusterClient;

    @Autowired
    private UserHistoryErrorQuestionAddLogRepository userHistoryErrorQuestionAddLogRepository;

    @Autowired
    private UserSubErrorQuestionService userSubErrorQuestionService;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private UserErrorQuestionToPdfTaskRepository userErrorQuestionToPdfTaskRepository;

    @Autowired
    private UserSubErrorQuestionRemoveService userSubErrorQuestionRemoveService;

    private static final ExecutorService executorService =
            ThreadPoolFactoryUtil.createDefaultPool("error-question-thread-pool");

    /**
     * 打开、关闭错题自动移除，state1打开，0关闭
     * @Date 14:09 2021/8/11
     **/
    @Override
    public void openAutoRemoveErrorQuestion(OpenAutoRemoveQuery query) {
        if (Objects.isNull(query.getState()) || Objects.isNull(query.getUid()) || Objects.isNull(query.getCategoryId())) {
            logger.error("openAutoRemoveErrorQuestion error:{}", GsonUtil.getGenericGson().toJson(query));
            return;
        }

        String key = RedisConsts.getUserErrorQuestionTimeHashKey(query.getUid(), query.getCategoryId());
        if (query.getState().equals(1)) {
            //field  使用 questionId，这里用0只是标记一个状态
            answerCompatableRedisClusterClient.hset(key,"0","0");
        } else {
            answerCompatableRedisClusterClient.del(key);
        }
    }

    /**
     * 如果用户打开了自动移除错题，将增加一次答对次数
     * @Date 14:10 2021/8/11
     **/
    @Override
    public Long increaseRightTimes(Long uid, Long categoryId, Long topicId) {
        String key = RedisConsts.getUserErrorQuestionTimeHashKey(uid ,categoryId);
        if (isAutoRemoveOpened(uid, categoryId)) {
            return answerCompatableRedisClusterClient.hincrBy(key, topicId.toString(), 1L);
        }
        return 0L;
    }

    /**
     * 如果用户打开了自动移除错题，将重置答对次数为0（因为业务上要求连续答对，所以这里不是减少答对次数，而是  直接重置）
     * @Date 14:10 2021/8/11
     **/
    @Override
    public void decreaseRightTimes(Long uid, Long categoryId, Long topicId) {
        String key = RedisConsts.getUserErrorQuestionTimeHashKey(uid ,categoryId);
        if (isAutoRemoveOpened(uid, categoryId)) {
            //这里不做减少，直接重置为0
            answerCompatableRedisClusterClient.hset(key,topicId.toString(),"0");
        }
    }

    /**
     * 获取用户是否开启了自动移除错题
     * @Date 14:10 2021/8/11
     **/
    @Override
    public Boolean isAutoRemoveOpened(Long uid, Long categoryId) {
        String key = RedisConsts.getUserErrorQuestionTimeHashKey(uid, categoryId);
        Boolean exist = answerCompatableRedisClusterClient.hexists(key, "0");
        return Objects.isNull(exist) ? false : exist;
    }

    @Override
    public List<UserCategoryHistoryErrorQuestionInfo> haveHistoryErrorQuestion(Long uid, Long goodsId, List<Long> categoryIds) {
        List<UserCategoryHistoryErrorQuestionInfo> result = new ArrayList<>();
        //先查记录表
        List<UserHistoryErrorQuestionAddLog> logs = userHistoryErrorQuestionAddLogRepository.selectByUidAndGoodsIdAndCategoryId(uid, goodsId, null);
        Map<Long, UserHistoryErrorQuestionAddLog> logMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(logs)) {
            logMap = logs.stream().collect(Collectors.toMap(UserHistoryErrorQuestionAddLog::getCategoryId, Function.identity(), (o,n)->n));
        }
        for (Long categoryId : categoryIds) {
            UserHistoryErrorQuestionAddLog log = logMap.get(categoryId);
            if (Objects.nonNull(log)) {
                UserCategoryHistoryErrorQuestionInfo info = new UserCategoryHistoryErrorQuestionInfo();
                info.setCategoryId(categoryId);
                info.setGoodsId(goodsId);
                info.setUid(uid);
                info.setCount(Objects.nonNull(log.getStatus()) && log.getStatus() == 1 ? 0 : log.getCount());
                result.add(info);
            } else {
                //查询错题记录表
                List<Long> questionIdList = getUserHistoryErrorQuestionForDb(uid,goodsId,categoryId);
                UserHistoryErrorQuestionAddLog addLog = new UserHistoryErrorQuestionAddLog();
                addLog.setUid(uid);
                addLog.setGoodsId(goodsId);
                addLog.setCategoryId(categoryId);
                addLog.setCount(CollectionUtils.isNotEmpty(questionIdList) ? questionIdList.size() : 0);
                addLog.setQuestions(JSONUtils.toJsonString(questionIdList));
                addLog.setStatus(0);
                userHistoryErrorQuestionAddLogRepository.insert(addLog);
                UserCategoryHistoryErrorQuestionInfo info = new UserCategoryHistoryErrorQuestionInfo();
                info.setCategoryId(categoryId);
                info.setGoodsId(goodsId);
                info.setUid(uid);
                info.setCount(addLog.getCount());
                result.add(info);
            }
        }
        return result;
    }

    @Override
    public Boolean moveHistoryErrorQuestion(Long uid, Long goodsId, Long categoryId) {
        //先查记录表
        List<UserHistoryErrorQuestionAddLog> logs = userHistoryErrorQuestionAddLogRepository.selectByUidAndGoodsIdAndCategoryId(uid, goodsId, categoryId);
        if (CollectionUtils.isNotEmpty(logs)) {
            UserHistoryErrorQuestionAddLog log = logs.get(0);
            //更新记录表
            log.setStatus(1);
            userHistoryErrorQuestionAddLogRepository.updateById(log);
            String questions = log.getQuestions();
            if (StringUtils.isNotBlank(questions)) {
                List<Long> questionIdList = JSONUtils.parseArray(questions, Long.class);
                return moveHistoryErrorQuestionReal(uid, goodsId, categoryId, questionIdList);
            } else {
                return true;
            }
        } else {
            //查询错题记录表
            List<Long> questionIdList = getUserHistoryErrorQuestionForDb(uid,goodsId,categoryId);
            UserHistoryErrorQuestionAddLog addLog = new UserHistoryErrorQuestionAddLog();
            addLog.setUid(uid);
            addLog.setGoodsId(goodsId);
            addLog.setCategoryId(categoryId);
            addLog.setCount(CollectionUtils.isNotEmpty(questionIdList) ? questionIdList.size() : 0);
            addLog.setQuestions(JSONUtils.toJsonString(questionIdList));
            addLog.setStatus(1);
            userHistoryErrorQuestionAddLogRepository.insert(addLog);
            return moveHistoryErrorQuestionReal(uid, goodsId, categoryId, questionIdList);
        }
    }
    private boolean moveHistoryErrorQuestionReal(Long uid,Long goodsId, Long categoryId, List<Long> questionIdList) {
        if (CollectionUtils.isEmpty(questionIdList)) {
            return false;
        }
        //查询用户的历史错题记录
        List<UserSubErrorQuestion> errorQuestionList = null;
        try {
            errorQuestionList = userSubErrorQuestionService.getUserSubErrorQuestionList(uid,null,categoryId, HISTORY_START_TIME, HISTORY_END_TIME, null);
        } catch (DataAccessException e) {
            logger.error("get user history error question catch exception", e);
        }
        Map<Long, UserSubErrorQuestion> questionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(errorQuestionList)) {
            questionMap = errorQuestionList.stream().filter(x->questionIdList.contains(x.getQuestionId())).collect(Collectors.toMap(UserSubErrorQuestion::getQuestionId,Function.identity(), (o,n)->n));
        }
        //查询用户当前商品下的错题记录
        List<UserSubErrorQuestion> currentErrorQuestionList = null;
        try {
            currentErrorQuestionList = userSubErrorQuestionService.getUserSubErrorQuestionList(uid, goodsId, categoryId, HISTORY_START_TIME, null,null);
        } catch (DataAccessException e) {
            logger.error("get current error question list catch exception", e);
        }
        if (CollectionUtils.isNotEmpty(currentErrorQuestionList)) {
            List<Long> currentErrorQuestionIds = currentErrorQuestionList.stream().map(UserSubErrorQuestion::getQuestionId).distinct().collect(Collectors.toList());
            for (Long currentErrorQuestionId : currentErrorQuestionIds) {
                questionMap.remove(currentErrorQuestionId);
            }
        }
        //剩下的就是要写入错题表
        for (UserSubErrorQuestion userSubErrorQuestion : questionMap.values()) {
            UserSubErrorQuestion insertEntity = new UserSubErrorQuestion();
            insertEntity.setUid(uid);
            insertEntity.setGoodsId(goodsId);
            insertEntity.setCategoryId(categoryId);
            insertEntity.setQuestionId(userSubErrorQuestion.getQuestionId());
            insertEntity.setQtype(userSubErrorQuestion.getQtype());
            insertEntity.setTopicId(userSubErrorQuestion.getTopicId());
            insertEntity.setLastErrorTime(userSubErrorQuestion.getLastErrorTime());
            insertEntity.setLastErrorAnswer(userSubErrorQuestion.getLastErrorAnswer());
            insertEntity.setAnswerId(userSubErrorQuestion.getAnswerId());
            //来源设置成-1 迁移
            insertEntity.setSourceType(-1);
            insertEntity.setCreateDate(new Date());
            try {
                userSubErrorQuestionService.saveSubErrorQuestion(insertEntity);
            } catch (DataAccessException e) {
                logger.error("saveSubErrorQuestion catch exception",e);
            }
        }

        return true;
    }
    @Override
    public List<Long> getHistoryErrorQuestion(Long uid, Long goodsId, Long categoryId) {
        List<Long> result = new ArrayList<>();
        List<UserHistoryErrorQuestionAddLog> logs = userHistoryErrorQuestionAddLogRepository.selectByUidAndGoodsIdAndCategoryId(uid, goodsId, categoryId);
        if (CollectionUtils.isNotEmpty(logs)) {
            UserHistoryErrorQuestionAddLog log = logs.get(0);
            String questions = log.getQuestions();
            if (StringUtils.isNotBlank(questions)) {
                result = JSONUtils.parseArray(questions, Long.class);
            }
        } else {
            //查询错题记录表
            List<Long> questionIdList = getUserHistoryErrorQuestionForDb(uid,goodsId,categoryId);
            UserHistoryErrorQuestionAddLog addLog = new UserHistoryErrorQuestionAddLog();
            addLog.setUid(uid);
            addLog.setGoodsId(goodsId);
            addLog.setCategoryId(categoryId);
            addLog.setCount(CollectionUtils.isNotEmpty(questionIdList) ? questionIdList.size() : 0);
            addLog.setQuestions(JSONUtils.toJsonString(questionIdList));
            addLog.setStatus(0);
            userHistoryErrorQuestionAddLogRepository.insert(addLog);
            result.addAll(questionIdList);
        }
        return result;
    }
    private List<Long> getUserHistoryErrorQuestionForDb(Long uid, Long goodsId, Long categoryId) {
        List<Long> result = new ArrayList<>();
        List<UserSubErrorQuestion> errorQuestionList = null;
        try {
            errorQuestionList = userSubErrorQuestionService.getUserSubErrorQuestionList(uid,null,categoryId, HISTORY_START_TIME, HISTORY_END_TIME,null);
        } catch (DataAccessException e) {
            logger.error("get user history error question catch exception", e);
        }
        if (CollectionUtils.isNotEmpty(errorQuestionList)) {
            Set<Long> questionIds = new HashSet<>();
            List<Long> errorQuestionIdList = errorQuestionList.stream().map(UserSubErrorQuestion::getQuestionId).distinct().collect(Collectors.toList());
            List<ChapterSectionItemDTO> chapterSectionItemDTOList = knowledgeResource.getChapterTreeWithKnowledge(categoryId, errorQuestionIdList);
            if (CollectionUtils.isNotEmpty(chapterSectionItemDTOList)) {
                for (ChapterSectionItemDTO chapterSectionItemDTO : chapterSectionItemDTOList) {
                    getChapterQuestionList(chapterSectionItemDTO, questionIds);
                }
            }
            //过滤掉关闭的题目
            if (CollectionUtils.isNotEmpty(questionIds)) {
                List<cn.huanju.edu100.study.model.Question> questions = knowledgeResource.getQuestionByIds(new ArrayList<>(questionIds));
                if (CollectionUtils.isNotEmpty(questions)) {
                    result.addAll(questions.stream().filter(x->x.getState() == 1).map(Question::getId).toList());
                }
            }
        }
        return result;
    }
    private void getChapterQuestionList(ChapterSectionItemDTO chapterSectionItemDTO, Set<Long> questionIds) {
        if (Objects.isNull(chapterSectionItemDTO)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(chapterSectionItemDTO.getQuestionIds())) {
            questionIds.addAll(chapterSectionItemDTO.getQuestionIds());
        }
        if (CollectionUtils.isNotEmpty(chapterSectionItemDTO.getChildrenList())) {
            for (ChapterSectionItemDTO sectionItemDTO : chapterSectionItemDTO.getChildrenList()) {
                getChapterQuestionList(sectionItemDTO, questionIds);
            }
        }
    }

    @Override
    public UserErrorQuestionToPdfTaskDTO createUserErrorQuestionToPdfTask(UserErrorQuestionToPdfTaskCommand cmd)  {
        //参数校验
        if (cmd.getUid() == null || CollectionUtils.isEmpty(cmd.getErrorQuestionIds())) {
            logger.error("createUserErrorQuestionToPdfTask parameter lose, uid or errorQuestionIds is null.");
            return null;
        }
        UserErrorQuestionToPdfTaskPO userErrorQuestionToPdfTaskPO = new UserErrorQuestionToPdfTaskPO();
        userErrorQuestionToPdfTaskPO.setUid(cmd.getUid());
        userErrorQuestionToPdfTaskPO.setGoodsId(cmd.getGoodsId());
        userErrorQuestionToPdfTaskPO.setProductId(cmd.getProductId());
        userErrorQuestionToPdfTaskPO.setStatus(1);
        userErrorQuestionToPdfTaskPO.setErrorQuestionIds(JSONUtils.toJsonString(cmd.getErrorQuestionIds()));
        userErrorQuestionToPdfTaskPO.setTaskId(UUID.randomUUID().toString().replace("-", ""));
        userErrorQuestionToPdfTaskRepository.save(userErrorQuestionToPdfTaskPO);
        //异步生成pdf
        executorService.execute(() -> {
            handleErrorQuestionToPdfTask(userErrorQuestionToPdfTaskPO);
        });
        //返回task
        UserErrorQuestionToPdfTaskDTO userErrorQuestionToPdfTaskDTO = new UserErrorQuestionToPdfTaskDTO();
        BeanUtils.copyProperties(userErrorQuestionToPdfTaskPO, userErrorQuestionToPdfTaskDTO);
        return userErrorQuestionToPdfTaskDTO;

    }

    private void handleErrorQuestionToPdfTask(UserErrorQuestionToPdfTaskPO userErrorQuestionToPdfTaskPO) {
        if (Objects.isNull(userErrorQuestionToPdfTaskPO) || Objects.isNull(userErrorQuestionToPdfTaskPO.getId()) || StringUtils.isBlank(userErrorQuestionToPdfTaskPO.getTaskId())) {
            logger.error("handleErrorQuestionToPdfTask param invalid:{}", JSONUtils.toJsonString(userErrorQuestionToPdfTaskPO));
            return;
        }
        if (Objects.isNull(userErrorQuestionToPdfTaskPO.getUid()) || StringUtils.isBlank(userErrorQuestionToPdfTaskPO.getErrorQuestionIds())) {
            logger.error("handleErrorQuestionToPdfTask error param uid or errorQuestionIds is null");
            userErrorQuestionToPdfTaskPO.setStatus(-1);
            userErrorQuestionToPdfTaskPO.setErrorMsg("handleErrorQuestionToPdfTask error param uid or errorQuestionIds is null");
            userErrorQuestionToPdfTaskRepository.updateById(userErrorQuestionToPdfTaskPO);
            return;
        }
        List<Long> errorQuestionIdList = JSONUtils.parseArray(userErrorQuestionToPdfTaskPO.getErrorQuestionIds(), Long.class);
        if (CollectionUtils.isEmpty(errorQuestionIdList)) {
            userErrorQuestionToPdfTaskPO.setStatus(-1);
            userErrorQuestionToPdfTaskPO.setErrorMsg("handleErrorQuestionToPdfTask error:errorQuestionIds is empty");
            userErrorQuestionToPdfTaskRepository.updateById(userErrorQuestionToPdfTaskPO);
            return;
        }
        List<Question> questionList = knowledgeResource.getQuestionByIds(errorQuestionIdList);
        if (CollectionUtils.isEmpty(questionList)) {
            userErrorQuestionToPdfTaskPO.setStatus(-1);
            userErrorQuestionToPdfTaskPO.setErrorMsg("handleErrorQuestionToPdfTask error:questionList is empty");
            userErrorQuestionToPdfTaskRepository.updateById(userErrorQuestionToPdfTaskPO);
            return;
        }

        try {
            long start = System.currentTimeMillis();
            String url = PdfUtil.generatePdfForQuestions(questionList, userErrorQuestionToPdfTaskPO.getUid());
            logger.info("handleErrorQuestionToPdfTask for task:{} cost:{} ms", userErrorQuestionToPdfTaskPO.getTaskId(), System.currentTimeMillis() - start);
            userErrorQuestionToPdfTaskPO.setStatus(2);
            userErrorQuestionToPdfTaskPO.setDownloadUrl(url);
            userErrorQuestionToPdfTaskRepository.updateById(userErrorQuestionToPdfTaskPO);
        }catch (Exception e) {
            logger.error("handleErrorQuestionToPdfTask error:{}", e.getMessage());
            userErrorQuestionToPdfTaskPO.setStatus(-1);
            userErrorQuestionToPdfTaskPO.setErrorMsg(StringUtils.left(e.getMessage(),200));
            userErrorQuestionToPdfTaskRepository.updateById(userErrorQuestionToPdfTaskPO);
        }

    }
    @Override
    public UserErrorQuestionToPdfTaskDTO getUserErrorQuestionToPdfTaskResult(UserErrorQuestionToPdfTaskQuery query) {
        //参数校验
        if (Objects.isNull(query) || Objects.isNull(query.getUid()) || StringUtils.isBlank(query.getTaskId())) {
            logger.error("getUserErrorQuestionToPdfTaskResult parameter lose, uid or taskId is null.");
            return null;
        }
        var po = userErrorQuestionToPdfTaskRepository.findOne(query);
        if (Objects.isNull(po)) {
            logger.error("getUserErrorQuestionToPdfTaskResult error:{}", "task not found");
            return null;
        }
        UserErrorQuestionToPdfTaskDTO userErrorQuestionToPdfTaskDTO = new UserErrorQuestionToPdfTaskDTO();
        BeanUtils.copyProperties(po, userErrorQuestionToPdfTaskDTO);
        userErrorQuestionToPdfTaskDTO.setPdfUrl(po.getDownloadUrl());
        return userErrorQuestionToPdfTaskDTO;
    }

    @Override
    public UserAutoRemoveErrorQuestionConfigDTO getUserAutoRemoveErrorQuestionConfig(Long uid) {
        return userSubErrorQuestionRemoveService.getAutoRemoveConfig(uid);
    }
}
