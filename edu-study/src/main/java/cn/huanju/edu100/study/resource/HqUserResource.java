/*
 * Copyright (c) 2011 duowan.com.
 * All Rights Reserved.
 * This program is the confidential and proprietary information of
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.study.model.UserAccount;
import cn.huanju.edu100.study.resource.impl.core.ThriftReturnCode;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.IdUtils;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
@Component
public class HqUserResource {

    public static final int APP_ID = 7;

    @Autowired
    private IGenericThriftResource genericThriftResource;

    private static final Logger LOG = LoggerFactory.getLogger(HqUserResource.class);

    /**
     * 根据用户uid列表查询
     *
     * @param
     * @return
     */
    public Map<Long, UserAccount> getUserInfoMapByUids(List<Long> idList, Long schId, String optUser) {

        if (CollectionUtils.isEmpty(idList) || !IdUtils.isValid(schId) || StringUtils.isBlank(optUser)) {
            LOG.error("getUserInfoMapByUids fail, illegal param, idList :{}, schId :{}, optUser :{}", idList, schId,
                    optUser);
            return Collections.emptyMap();
        }

        Map<String, Object> param = Maps.newHashMap();
        param.put("idList", idList);
        param.put("schId", schId);
        param.put("optUser", optUser);
        String paramStr = GsonUtil.getGenericGson().toJson(param);

        try {
            LOG.info("getUserInfoMapByUids start, param is:{}",param);
            response response = genericThriftResource.generalHqUserThriftMethodInvoke(paramStr, APP_ID,
                    Consts.Code.CLIENT_IP, schId, 14l, "new_user_listUserInfoByUids");
            if (response != null && response.getCode() == ThriftReturnCode.SUCCESS.getType()
                    && StringUtils.isNotBlank(response.getMsg())) {
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAccount>>() {
                }.getType();
                List<UserAccount> userAccounts = GsonUtil.getGenericGson().fromJson(response.getMsg(), type);
                if (null == userAccounts || userAccounts.isEmpty()) {
                    LOG.info("getUserInfoMapByUids can get user info, param is:{}",param);
                    return null;
                } else {
                    Map<Long, UserAccount> userAccountMap = Maps.newHashMap();
                    for (UserAccount account : userAccounts) {
                        userAccountMap.put(account.getUid(), account);
                    }
                    LOG.info("getUserInfoMapByUids can get user info, result is:{}",GsonUtil.toJson(userAccountMap));
                    return userAccountMap;
                }
            } else {
                LOG.error("getUserInfoMapByUids error response :{}", response);
            }

        } catch (Exception e) {
            LOG.error("[getUserInfoMapByUids] error param:{}", param, e);
        }

        return Collections.emptyMap();
    }
}
