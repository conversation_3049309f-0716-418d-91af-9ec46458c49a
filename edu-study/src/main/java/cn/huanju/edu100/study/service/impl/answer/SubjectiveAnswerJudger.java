package cn.huanju.edu100.study.service.impl.answer;

import cn.huanju.edu100.study.model.QuestionGroupRelation;
import cn.huanju.edu100.study.model.QuestionTopic;
import com.hqwx.study.entity.UserAnswerDetail;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/5/18.
 */
public class SubjectiveAnswerJudger extends AbstractAnswerJudger {

    @Override
    public int judge(QuestionTopic topic, String[] answer) {
    	//主观题判断修改，只要作答就认为对。update by linyl 20160301
    	if (answer ==null ||answer.length==0) {
        	return UserAnswerDetail.IsRight.NOT_ANSWER;
        }
    	int empCnt = 0;
        for (String answerStr : answer) {
			if ("".equals(answerStr)) {
				empCnt++;
			}
		}
        if (empCnt == answer.length) {
        	return UserAnswerDetail.IsRight.NOT_ANSWER;
		}
        return UserAnswerDetail.IsRight.RIGHT;
    }

    @Override
    public double calculateScore(QuestionTopic topic, String[] answer,Long paperId) {
    	//主观题得分修改，不论对错都不计分。update by linyl 20160301
    	//return 0;

    	// 主观题，只要作答，就给分。update by linyl 20160401
    	Double score = getScore(topic,paperId);

        return judge(topic,answer) ==  UserAnswerDetail.IsRight.RIGHT ? score : 0;
    }

    @Override
    public double calculateScores(QuestionTopic topic, String[] answer,Long paperId,Map<Long,QuestionGroupRelation> map) {
        //主观题得分修改，不论对错都不计分。update by linyl 20160301
        //return 0;

        // 主观题，只要作答，就给分。update by linyl 20160401
        Double score = getScores(topic,paperId,map);

        return judge(topic,answer) ==  UserAnswerDetail.IsRight.RIGHT ? score : 0;
    }
}
