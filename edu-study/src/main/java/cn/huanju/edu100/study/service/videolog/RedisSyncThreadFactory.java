package cn.huanju.edu100.study.service.videolog;

import cn.huanju.edu100.study.async.SyncThreadFactory;
import cn.huanju.edu100.study.service.UserVideoLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
public class RedisSyncThreadFactory<T> implements SyncThreadFactory<T> {
	@Autowired
	private UserVideoLogService userVideoLogService;

	@Override
	public RedisSyncThread<T> newSyncThread(Collection<T> list) {
		return new RedisSyncThread<T>(userVideoLogService, list);
	}

}
