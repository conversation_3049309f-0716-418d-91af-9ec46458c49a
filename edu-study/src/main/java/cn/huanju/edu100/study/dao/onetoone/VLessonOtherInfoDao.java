/**
 *
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.onetoone.VLessonOtherInfo;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 直播/面授课节其他信息DAO接口
 * <AUTHOR>
 * @version 2016-12-16
 */
public interface VLessonOtherInfoDao extends CrudDao<VLessonOtherInfo> {


    List<VLessonOtherInfo> findListByLessonIds(List<Long> lessonIds) throws DataAccessException;
}
