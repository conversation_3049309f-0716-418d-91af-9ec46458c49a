/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.Recordcourse;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 录播课程学习Service
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface RecordcourseService extends BaseService<Recordcourse> {

	List<Recordcourse> findListByTaskIdList(List<Long> taskIdList) throws DataAccessException;

}
