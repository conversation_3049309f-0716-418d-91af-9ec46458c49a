package cn.huanju.edu100.study.mapper.homework.comment;

import cn.huanju.edu100.study.model.tutor.Comment;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/26 09:53
 * @description
 */
@Mapper
public interface CommentMapper extends BaseMapper<Comment> {

    /**
     * 分页查询评论信息
     * @param page 分页参数
     * @param queryWrapper 查询参数
     * @return 评论信息分页列表
     */
    @Select("SELECT " +
            " a.id AS id," +
            " a.uid AS uid," +
            " a.nick_name AS nickName," +
            " a.comment_element_id AS commentElementId," +
            " a.content AS content," +
            " a.star AS star," +
            " a.teacher_star AS teacherStar," +
            " a.thumb_up_num AS thumbUpNum," +
            " a.status AS status," +
            " a.is_stick AS isStick," +
            " a.create_date AS createDate," +
            " a.create_by AS createBy," +
            " a.update_date AS updateDate," +
            " a.update_by AS updateBy," +
            " a.del_flag AS delFlag," +
            " a.reply_content AS replyContent," +
            " a.is_read AS isRead," +
            " a.platform AS platform," +
            " a.level AS level," +
            " a.source AS source," +
            " b.obj_id AS objId," +
            " b.obj_type AS objType," +
            " b.obj_name AS objName, " +
            " b.goods_id AS goodsId, " +
            " b.goods_group_id AS goodsGroupId, " +
            " b.category_id AS categoryId, " +
            " b.product_id AS productId " +
            " FROM comment a join comment_element b on a.comment_element_id = b.id WHERE ${ew.sqlSegment}")
    IPage<Comment> selectWithPage(IPage<Comment> page, @Param(Constants.WRAPPER) Wrapper<Comment> queryWrapper);


    @Select("SELECT " +
            " a.id AS id," +
            " a.uid AS uid" +
            " FROM comment a WHERE ${ew.sqlSegment}")
    List<Comment> selectCommentInfoWithPage(@Param(Constants.WRAPPER) Wrapper<Comment> queryWrapper);

    @Select("SELECT " +
            " distinct b.obj_id AS objId" +
            " FROM comment a join comment_element b on a.comment_element_id = b.id WHERE ${ew.sqlSegment}")
    List<Long> getNoteQuestionList(@Param(Constants.WRAPPER) QueryWrapper<Comment> queryWrapper);
}