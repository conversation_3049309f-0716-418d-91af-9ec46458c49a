package cn.huanju.edu100.study.model;

import java.util.List;

/**
 * Created by zhanghong on 2021/9/9.
 */

public class UserErrorHomeworkProdBeanDto {

    private Long productId;     //产品id

    private Integer productType;    //产品类型

    private String productName;     //产品名称

    private int errorCount; //当前产品的课后作业错题数量

    private List<Long> errorLessonIds;   //所有包含课后作业的讲ids

    private Long lessonId;  //对应的讲id

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public int getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(int errorCount) {
        this.errorCount = errorCount;
    }

    public List<Long> getErrorLessonIds() {
        return errorLessonIds;
    }

    public void setErrorLessonIds(List<Long> errorLessonIds) {
        this.errorLessonIds = errorLessonIds;
    }
}
