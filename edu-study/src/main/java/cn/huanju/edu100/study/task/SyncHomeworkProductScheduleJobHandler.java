package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.service.UserAnswerService;
import com.hqwx.goods.dto.HomeWorkSyncDataDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 课件作业（新课程表排课）信息同步至作业管理里
 */
@Service
public class SyncHomeworkProductScheduleJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SyncHomeworkProductScheduleJobHandler.class);
    @Autowired
    private UserAnswerService userAnswerService;

    @XxlJob("SyncHomeworkProductScheduleJobHandler")
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("------SyncHomeworkProductScheduleJobHandler start------");
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);

        logger.info("SyncHomeworkProductScheduleJobHandler 当前分片:{},时间:{}", zoneIndex, System.currentTimeMillis());
        boolean rs = userAnswerService.syncHomeworkProductSchedule(null, zoneIndex, zoneTotal, null, null);
        logger.info("SyncHomeworkProductScheduleJobHandler 开始等待其他分片器完成！当前分片:{},rs:{}", zoneIndex, rs);

        boolean completeFlag = false;//完成标识
        while (!completeFlag) {
            for (int i = 0; i < zoneTotal; i++) {
                HomeWorkSyncDataDTO homeWorkSyncDataDTO = userAnswerService.getRedisHomeworkSyncdataZoneProductSchedule(i);
                if (homeWorkSyncDataDTO == null || homeWorkSyncDataDTO.getStatus() == 0) {
                    break;
                }
                if (i == zoneTotal - 1) {//所有的分区都完成任务；更新HOMEWORK_SYNCDATA_PROD_KEY的status
                    userAnswerService.updateStatusHomeworkSyncdataRedisProductSchedule(1);
                    completeFlag = true;
                    logger.info("SyncHomeworkProductScheduleJobHandler 各个分片已执行完毕，当前分片:{}", zoneIndex);
                }
            }

            if (userAnswerService.getIsNoCanContinue()) {
                logger.error("SyncHomeworkProductScheduleJobHandler 时间已到，程序终止！当前分片:{}", zoneIndex);
                completeFlag = true;
            }

            Thread.sleep(5 * 1000);//5秒
        }
        logger.info("------SyncHomeworkProductScheduleJobHandler end------");
        return ReturnT.SUCCESS;
    }


}
