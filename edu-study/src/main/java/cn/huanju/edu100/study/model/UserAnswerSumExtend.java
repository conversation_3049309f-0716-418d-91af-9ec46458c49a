package cn.huanju.edu100.study.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "user_answer_sum_extend",autoResultMap = true)
public class UserAnswerSumExtend {
    @TableId(type= IdType.AUTO)
    private Long id;
    private Long uid;
    private Long answerId;
    private String tagStr;
    private Integer type;
}
