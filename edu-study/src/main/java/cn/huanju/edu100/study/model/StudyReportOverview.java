/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 学习报告总览Entity
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudyReportOverview extends DataEntity<StudyReportOverview> {
	
	private static final long serialVersionUID = 1L;
	private Long uid;
	private Integer taskCount;			//共完成任务数
	private Long videoTime;		//观看视频时长(分钟)
	
	public StudyReportOverview() {
		super();
	}

	public StudyReportOverview(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Integer getTaskCount() {
		return taskCount;
	}

	public void setTaskCount(Integer taskCount) {
		this.taskCount = taskCount;
	}

	public Long getVideoTime() {
		return videoTime;
	}

	public void setVideoTime(Long videoTime) {
		this.videoTime = videoTime;
	}
}