package cn.huanju.edu100.study.model.config;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 首页背景图配置Entity
 * <AUTHOR>
 * @version 2018-07-09
 */
public class HomeImageConfig extends DataEntity<HomeImageConfig> {

	private static final long serialVersionUID = 1L;
	private Long secondCategory;		// 考试id
	private String url;		// 背景图url
	private String dynamicLabel;		// 动态标签，用英文逗号分隔全选为[1,2,3,4,5,6]，值表示选择了1每日一练，2章节练习，3真题模考，4刷题挑战，5万人模考，6错题本，如果没勾选返回0。值为[0,0,0,0,0,0]。
	private Integer isDefault;		// 选择默认图(0否，1是)
	private Integer status;		// 状态（1：已生效，0/空：未生效）
	private Date createTime;		// 创建时间
	private Date updateTime;		// 更新时间

	public HomeImageConfig() {
		super();
	}

	public HomeImageConfig(Long id){
		super(id);
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getDynamicLabel() {
		return dynamicLabel;
	}

	public void setDynamicLabel(String dynamicLabel) {
		this.dynamicLabel = dynamicLabel;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}

}
