package cn.huanju.edu100.study.resource.feigncall.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("chatgpt具体参数")
@Data
public class InputItems {

    @ApiModelProperty("系统提示语")
    private String systemPrompt;

    @ApiModelProperty(value = "问题具体内容")
    private String message;

    @ApiModelProperty(value = "问题回答严谨度0-2的小数越小越严谨")
    private Double temperature;

    @ApiModelProperty(value = "用户唯一标识")
    private String user;

    @ApiModelProperty(value = "应用")
    private String application;

    @ApiModelProperty("modelName 模型名称 gpt-3.5-turbo gpt-4 ")
    private String  modelName="gpt-3.5-turbo";
}
