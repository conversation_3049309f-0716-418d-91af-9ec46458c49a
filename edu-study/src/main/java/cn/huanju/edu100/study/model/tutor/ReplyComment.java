package cn.huanju.edu100.study.model.tutor;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReplyComment {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long uid;		// uid
    private String nickName;		// 用户昵称
    private Long commentElementId;		// 评价元素id
    private String content;		// 内容
    private Float star;		// 星星数
    private Float teacherStar;//老师打分
    private Integer status;		// 状态， 0:未审核 1：审核不通过 2：审核通过

    private Integer thumbFlag;//0,没点赞，1点赞
    private Integer thumbUpNum;// 点赞数
    private Integer isStick;// 是否置顶 0：否 1:是
    private String replyContent;   //回复内容

    private Integer isRead; //是否已读：0未读，1已读
    private Long objId;		// 对象id，obj_type为0，表示讲id，obj_type为1，表示课节id，obj_type为2，表示题目id， obj_type为3，表示云私塾id obj_type为4，表示每日一练
    private Integer objType;	// 对象类型 0：录播课，1：直播课，2：题目，3:云私塾  4：每日一练
    private String objName; //对象名称
    private String platform;	//评论提交所属终端
    private Date createDate;
    private Integer replyNum;

    private List<ReplyComment> replyCommentList;

    // -- V3.9.1 学习笔记需要因业务需求新增以下字段 --
    /**
     * 评论回复所属评论id，即一级评论id
     */
    private Long belongCommentId;

    /**
     * 该评论所回复的评论id，有可能是一级评论，也有可能是二级评论
     */
    private Long replyCommentId;

    /**
     * 当前回复对应的评论id(comment表的id)
     */
    private Long commentId;

    /**
     * 当前回复的更新时间
     */
    private Date updateDate;
}
