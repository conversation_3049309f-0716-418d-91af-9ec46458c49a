package cn.huanju.edu100.study.service.impl.enterschooltest;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.enterschooltest.EnterSchoolTestQuestionDao;
import cn.huanju.edu100.study.model.enterschooltest.EnterSchoolTestQuestion;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestQuestionService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 入学测评 题目 Service
 * <AUTHOR>
 * @version 2021-04-27
 */
@Service
public class EnterSchoolTestQuestionServiceImpl extends BaseServiceImpl<EnterSchoolTestQuestionDao, EnterSchoolTestQuestion> implements EnterSchoolTestQuestionService {

    @Override
    public long insertBatch(List<EnterSchoolTestQuestion> enterSchoolTestQuestions) throws DataAccessException {
        return dao.insertBatch(enterSchoolTestQuestions);
    }
}
