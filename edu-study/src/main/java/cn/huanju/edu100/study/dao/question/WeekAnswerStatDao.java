/**
 *
 */
package cn.huanju.edu100.study.dao.question;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.question.WeekAnswerStat;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 用户周答题统计DAO接口
 * <AUTHOR>
 * @version 2018-06-08
 */
public interface WeekAnswerStatDao extends CrudDao<WeekAnswerStat> {

    public List<WeekAnswerStat> findMaxWeekNum() throws DataAccessException;

    public List<WeekAnswerStat> findByWeekNum(Long boxId, Long teachBookId, Long uid, Long startWeekNum, Long endWeekNum) throws DataAccessException;
}