package cn.huanju.edu100.study.service.onetoone;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.onetoone.VRoom;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Set;

/**
 * 面授课室Service
 * <AUTHOR>
 * @version 2016-04-12
 */
public interface VRoomService extends BaseService<VRoom> {

    List<VRoom> findListByIds(Set<Long> ids) throws DataAccessException;

}
