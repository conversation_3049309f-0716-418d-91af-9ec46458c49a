package cn.huanju.edu100.study.dao.ibatis.impl.report;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.report.TikuForecastScoreDao;
import cn.huanju.edu100.study.model.report.TikuForecastScore;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class TikuForecastScoreIbatisImpl extends CrudIbatisImpl2<TikuForecastScore> implements TikuForecastScoreDao {

    public TikuForecastScoreIbatisImpl(){
        super("tikuForecastScore");
    }


    @Override
    public Double calculateForecastScore(Long uid, Long boxId, Long categoryId) throws DataAccessException {
        if (uid == null || uid <= 0 ) {
            logger.error("calculateForecastScore error, parameter uid is empty or less than 0,uid:{}", uid);
            throw new DataAccessException("calculateForecastScore error,parameter uid is empty or less than 0");
        }
        if (boxId == null || boxId <= 0 ) {
            logger.error("calculateForecastScore error, parameter boxId is empty or less than 0,boxId:{}", boxId);
            throw new DataAccessException("calculateForecastScore error,parameter boxId is empty or less than 0");
        }
        if (categoryId == null || categoryId <= 0 ) {
            logger.error("calculateForecastScore error, parameter categoryId is empty or less than 0,categoryId:{}", categoryId);
            throw new DataAccessException("calculateForecastScore error,parameter boxId is empty or less than 0");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", uid);
            param.put("boxId", boxId);
            param.put("categoryId", categoryId);
            Double score =  (Double) sqlMap.queryForObject(namespace.concat(".calculateForecastScore"), param);
            return score;
        } catch (SQLException e) {
            logger.error("calculateForecastScore SQLException.uid:{}:", uid, e);
            throw new DataAccessException("calculateForecastScore SQLException error" + e.getMessage());
        }

    }
}
