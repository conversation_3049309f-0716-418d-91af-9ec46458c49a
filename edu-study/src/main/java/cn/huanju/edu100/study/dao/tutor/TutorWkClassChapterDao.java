/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorWkClassChapter;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 微课班章节DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public interface TutorWkClassChapterDao extends CrudDao<TutorWkClassChapter> {

    List<TutorWkClassChapter> findListByParam(Long wkClassId, List<Long> parentIdList, List<Long> idList) throws DataAccessException;
}