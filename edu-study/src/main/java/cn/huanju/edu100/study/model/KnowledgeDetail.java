/**
 * Copyright (c) 2011 duowan.com. 
 * All Rights Reserved.
 * This program is the confidential and proprietary information of 
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.model;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class KnowledgeDetail {
    private long knowledgeId; // 知识点id
    private long quesCount; // 题目数
    private long wrongCount; // 错题数
    private List<QuestionVo> wrongQues = new ArrayList<QuestionVo>(); // 错题实体(要根据qid去重)
    private List<Long> taskIds = new ArrayList<Long>();

    public long getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(long knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public long getQuesCount() {
        return quesCount;
    }

    public void setQuesCount(long quesCount) {
        this.quesCount = quesCount;
    }

    public long getWrongCount() {
        return wrongCount;
    }

    public void setWrongCount(long wrongCount) {
        this.wrongCount = wrongCount;
    }

    public List<QuestionVo> getWrongQues() {
        return wrongQues;
    }

    public void setWrongQues(List<QuestionVo> wrongQues) {
        this.wrongQues = wrongQues;
    }

    public List<Long> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<Long> taskIds) {
        this.taskIds = taskIds;
    }

}
