/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.CommentThumbDao;
import cn.huanju.edu100.study.model.tutor.CommentThumb;
import cn.huanju.edu100.util.GsonUtil;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评论点赞DAO接口
 * <AUTHOR>
 * @version 2018-03-06
 */
public class CommentThumbIbatisImpl extends CrudIbatisImpl2<CommentThumb> implements
		CommentThumbDao {

	public CommentThumbIbatisImpl() {
		super("CommentThumb");
	}

	@Override
	public List<CommentThumb> findUserThumbUpList(Long uid, List<Long> commentIdList) throws DataAccessException {
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("commentIdList", commentIdList);
			return  sqlMap.queryForList(namespace.concat(".findUserThumbUpList"), param);
		} catch (SQLException e) {
			logger.error("findUserThumbUpList SQLException.uid:{},questionBoxId:{}", uid, GsonUtil.toJson(commentIdList), e);
			throw new DataAccessException("findUserThumbUpList SQLException error" + e.getMessage());
		}
	}
}
