package cn.huanju.edu100.study.util;

import cn.huanju.edu100.study.model.CountModel;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by xusenc on 18-1-9.
 */
public class CountUtils {

    public static Map<Long, Integer> getCountMap(List<CountModel> list) {

        if (CollectionUtils.isNotEmpty(list)) {
            Map<Long, Integer> map = Maps.newHashMap();
            for (CountModel countModel : list) {
                map.put(countModel.getId(), countModel.getNum());
            }

            return map;
        }

        return Collections.emptyMap();
    }
}
