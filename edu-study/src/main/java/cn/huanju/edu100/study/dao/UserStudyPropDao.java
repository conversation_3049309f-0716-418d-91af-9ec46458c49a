package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.UserStudyProp;

/**
 * Created by levyx on 2022/10/24.
 */
public interface UserStudyPropDao extends CrudDao<UserStudyProp> {

    /**
     * @param
     * @return
     */
    long insertUserStudyPropLog(UserStudyProp userStudyProp) throws DataAccessException;

    UserStudyProp selectOneMaster(Long uid, Long lessonId, String relationType) throws DataAccessException;

    int updateUserStudyProp(UserStudyProp userStudyProp) throws DataAccessException;
}
