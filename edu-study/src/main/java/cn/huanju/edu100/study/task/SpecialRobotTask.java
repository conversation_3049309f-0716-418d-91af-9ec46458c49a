package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.resource.feigncall.AdminFeignClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 历史题库学习数据清理，只清理已经迁移的学员的
 */
@Service
public class SpecialRobotTask {
    private static final Logger logger = LoggerFactory.getLogger(SpecialRobotTask.class);
    @Autowired
    private AdminFeignClient adminFeignClient;

    @XxlJob("robotDealChannelByTask")
    public ReturnT<String> robotDealChannelByTask(String param) throws Exception {
        logger.info("robotDealChannelByTask start");
        adminFeignClient.robotDealChannelByTask();
        logger.info("robotDealChannelByTask end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("robotDealProjectByTask")
    public ReturnT<String> robotDealProjectByTask(String param) throws Exception {
        logger.info("robotDealProjectByTask start");
        adminFeignClient.robotDealProjectByTask();
        logger.info("robotDealProjectByTask end");
        return ReturnT.SUCCESS;
    }
}

