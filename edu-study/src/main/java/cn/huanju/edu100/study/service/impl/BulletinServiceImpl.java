package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.cache.DataCache;
import cn.huanju.edu100.study.dao.BulletinDao;
import cn.huanju.edu100.study.dao.BulletinRelateDao;
import cn.huanju.edu100.study.dao.WhiteListDao;
import cn.huanju.edu100.study.model.Bulletin;
import cn.huanju.edu100.study.model.WhiteList;
import cn.huanju.edu100.study.service.BulletinService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
 * 网校公告Service
 * <AUTHOR>
 * @version 2015-05-15
 */
@Service
public class BulletinServiceImpl extends BaseServiceImpl<BulletinDao, Bulletin> implements BulletinService {

    private static Logger logger = LoggerFactory.getLogger(BulletinServiceImpl.class);
    @Autowired
    private BulletinRelateDao bulletinRelateDao;
    @Autowired
    private WhiteListDao whiteListDao;
    @Autowired
    private DataCache dataCache;

    @Override
	public Collection<Bulletin> listBulletinByType(Long uid, Long schId, Integer type) throws DataAccessException {
        Collection<Bulletin> bulletins = dao.list(schId, type);
        if (CollectionUtils.isEmpty(bulletins)) {
            return bulletins;
        }

        return filterByWhiteList(uid, bulletins);
	}

    private Collection<Bulletin> filterByWhiteList(Long uid, Collection<Bulletin> bulletins) throws DataAccessException {
        if (uid != null) {
            WhiteList whiteList = new WhiteList();
            whiteList.setObjId(uid);
            List<WhiteList> whiteLists = whiteListDao.findList(whiteList);
            if (CollectionUtils.isEmpty(whiteLists)) {
                return bulletins;
            }

            List<Long> whiteBulletinIds = new ArrayList<Long>();
            for (WhiteList whiteList2 : whiteLists) {
                whiteBulletinIds.add(whiteList2.getExtObjId());
            }
            List<Bulletin> newBulletins = new ArrayList<Bulletin>();
            for (Bulletin bulletin : bulletins) {
                if (!whiteBulletinIds.contains(bulletin.getId())) {
                    newBulletins.add(bulletin);
                }
            }
            return newBulletins;
        }else {
            return bulletins;
        }
    }

    @Override
    public Collection<Bulletin> getBulletinListByIds(String ids, String categoryIds, Long uid, Long shcId)
            throws DataAccessException {

        List<Long> bulletinIds = new ArrayList<Long>();
        if (StringUtils.isNotBlank(ids) || StringUtils.isNotBlank(categoryIds)) {
            String[] goodsRelateIds = ids.split(",");
            String[] categoryRelateIds = categoryIds.split(",");
            List<String> goodsRelateIdList = new ArrayList<String>();
            List<String> caRelateIdList = new ArrayList<String>();
            if (goodsRelateIds != null && goodsRelateIds.length > 0) {
                goodsRelateIdList.addAll(Arrays.asList(goodsRelateIds));
            }
            if (categoryRelateIds != null && categoryRelateIds.length > 0) {
                caRelateIdList.addAll(Arrays.asList(categoryRelateIds));
            }
            if (!CollectionUtils.isEmpty(goodsRelateIdList)) {
                bulletinIds.addAll(bulletinRelateDao.getBulletinRelateByRelateIds(goodsRelateIdList, Consts.Bulletin_Type.GOODS));
            }
            if (!CollectionUtils.isEmpty(caRelateIdList)) {
                bulletinIds.addAll(bulletinRelateDao.getBulletinRelateByRelateIds(caRelateIdList, Consts.Bulletin_Type.CATEGORY));
            }
        }
        if (CollectionUtils.isEmpty(bulletinIds)) {
            return null;
        }

        List<Bulletin> bulletins = dao.getBulletinListByIds(bulletinIds, shcId);
        logger.info("getBulletinListByIds bulletins:{}", GsonUtil.toJson(bulletins));
        if (CollectionUtils.isEmpty(bulletins)) {
            return bulletins;
        }

        return filterByWhiteList(uid, bulletins);
    }

    @Override
    public boolean cancelBulletin(Long uid, String[] bulletinIds)
            throws DataAccessException {
        if (uid == null || bulletinIds == null || bulletinIds.length == 0) {
            return false;
        }

        WhiteList whiteList = new WhiteList();
        whiteList.setObjId(uid);
        List<WhiteList> whiteLists = whiteListDao.findList(whiteList);

        List<String> whiteIdList = new ArrayList<String>();
        if (!CollectionUtils.isEmpty(whiteLists)) {
            for (WhiteList wList : whiteLists) {
                whiteIdList.add(wList.getExtObjId().toString());
            }
        }

        List<WhiteList> insertList = new ArrayList<WhiteList>();
        for (String bulletinId : bulletinIds) {
            if (!whiteIdList.contains(bulletinId)) {
                WhiteList whiteList2 = new WhiteList();
                whiteList2.setObjId(uid);
                whiteList2.setType(0);
                whiteList2.setExtObjId(NumberUtils.toLong(bulletinId));
                insertList.add(whiteList2);
            }
        }

        if (!CollectionUtils.isEmpty(insertList)) {
            whiteListDao.insertBatch(insertList);
        }
        return true;
    }

    @Override
    public Collection<Bulletin> findListByIds(Set<Long> bulletinIdSet, Long schId, Long uid)
            throws DataAccessException {

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(bulletinIdSet)) {
            logger.error("findListByIds fail, bulletinIdList is empty");
            throw new DataAccessException("findListByIds fail, bulletinIdList is empty");
        }

        List<Long> bulletinIdList = null;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(bulletinIdSet)) {
            bulletinIdList = new ArrayList<Long>();
            bulletinIdList.addAll(bulletinIdSet);
        }

        List<Bulletin> bulletins = dataCache.getBulletinByIds(bulletinIdList);
        if (CollectionUtils.isEmpty(bulletins)) {
            bulletins = dao.getBulletinListByIds(bulletinIdList, schId);
        }

        if (CollectionUtils.isEmpty(bulletins) || uid == null) {
            return bulletins;
        }

        //过滤取消的公告
        WhiteList whiteList = new WhiteList();
        whiteList.setObjId(uid);
        whiteList.setType(0);
        List<WhiteList> whiteLists = whiteListDao.findList(whiteList);
        if (CollectionUtils.isEmpty(whiteLists)) {
            return bulletins;
        }

        List<Long> whiteIds = Lists.newArrayList();
        for (WhiteList wList : whiteLists) {
            whiteIds.add(wList.getExtObjId());
        }
        for (Bulletin bulletin : bulletins) {
            if (whiteIds.contains(bulletin.getId())) {
                bulletin.setIsShow(Consts.Bulletin_Is_Show.NO);
            }
        }
        return bulletins;
    }

}
