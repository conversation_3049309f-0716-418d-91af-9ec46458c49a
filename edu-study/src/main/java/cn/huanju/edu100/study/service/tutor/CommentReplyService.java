package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.study.model.tutor.CommentReply;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hqwx.study.dto.query.CommentReplyQuery;

import java.util.List;

public interface CommentReplyService extends IService<CommentReply> {

    List<CommentReply> getCommentReplyList(CommentReplyQuery commentReplyQuery);

    Integer selectCount(CommentReplyQuery commentReplyQuery);
}
