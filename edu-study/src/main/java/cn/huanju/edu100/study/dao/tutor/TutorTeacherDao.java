/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorTeacher;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 班主任DAO接口
 * <AUTHOR>
 * @version 2016-01-12
 */
public interface TutorTeacherDao extends CrudDao<TutorTeacher> {

    List<TutorTeacher> findListByTuids(List<Long> idList) throws DataAccessException;

}
