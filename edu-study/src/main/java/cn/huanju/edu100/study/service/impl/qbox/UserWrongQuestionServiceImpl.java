package cn.huanju.edu100.study.service.impl.qbox;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.qbox.UserWrongQuestionDao;
import cn.huanju.edu100.study.model.questionBox.UserWrongQuestion;
import cn.huanju.edu100.study.service.qbox.UserWrongQuestionService;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.IdWorker;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 18/10/30
 */
@Service
public class UserWrongQuestionServiceImpl extends BaseServiceImpl<UserWrongQuestionDao, UserWrongQuestion> implements UserWrongQuestionService {

    final static IdWorker idworker=new IdWorker(0,6);
    @Override
    public Long save(UserWrongQuestion userWrongQuestion) throws DataAccessException {
        if (userWrongQuestion.getUid() == null || userWrongQuestion.getQuestionBoxId() == null ||
                StringUtils.isBlank(userWrongQuestion.getKey())) {
            throw  new DataAccessException("UserWrongQuestion save error, property uid or questionBoxId or key is empty, uid:"
                    + userWrongQuestion.getUid()+",questionBoxId:"+ userWrongQuestion.getQuestionBoxId()
                    +",key:"+ userWrongQuestion.getKey());
        }
        UserWrongQuestion exist = dao.getFromMasterDbByUidQboxIdAndKey(userWrongQuestion.getUid(), userWrongQuestion.getQuestionBoxId(), userWrongQuestion.getKey());
        if (exist == null) {
            //CommonSelfIdGenerator idGenerator = new CommonSelfIdGenerator();
            userWrongQuestion.setId(idworker.nextId());
            dao.insert(userWrongQuestion);
        } else {
            userWrongQuestion.setId(exist.getId());
            dao.update(userWrongQuestion);
        }
        return userWrongQuestion.getId();
    }

    @Override
    public UserWrongQuestion getByUidQboxIdAndKey(Long uid, Long qboxId, String key) throws DataAccessException {
        return dao.getByUidQboxIdAndKey(uid, qboxId, key);
    }

    @Override
    public List<UserWrongQuestion> getByUidQboxIdAndKeyLike(Long uid, Long qboxId, String key) throws DataAccessException {
        return dao.getByUidQboxIdAndKeyLike(uid,qboxId,key);
    }

    @Override
    public List<UserWrongQuestion> getByUidQboxIdAndKeyLikeFromMaster(Long uid, Long qboxId, String key) throws DataAccessException {
        return dao.getByUidQboxIdAndKeyLikeFromMaster(uid,qboxId,key);
    }

    @Override
    public List<UserWrongQuestion> getByUidQboxIdListAndKeyLike(Long uid, List<Long> qboxIds, String key) throws DataAccessException {
        return dao.getByUidQboxIdListAndKeyLike(uid,qboxIds,key);
    }

    @Override
    public List<UserWrongQuestion> getByUidAndQboxId(Long uid, Long qboxId) throws DataAccessException {
        return dao.getByUidAndQboxId(uid, qboxId);
    }

    @Override
    public List<UserWrongQuestion> getByUidAndBoxIds(Long uid, List<Long> boxIds) throws DataAccessException {
        return dao.getByUidAndBoxIds(uid, boxIds);
    }

    @Override
    public List<String> getKeysByUidAndQboxId(Long uid, Long qboxId) throws DataAccessException {
        return dao.getKeysByUidAndQboxId(uid, qboxId);
    }

    @Override
    public void batchSave(Long uid, Long questionBoxId, List<UserWrongQuestion> userWrongQuestionList) throws DataAccessException {
        if (uid == null || questionBoxId == null) {
            throw  new DataAccessException("UserWrongQuestion save error, property uid or qboxIdis empty, uid:"
                    +uid+",questionBoxId:"+questionBoxId);
        }
        logger.debug("UserWrongQuestion batchSave for param, uid:{}, questionBoxId:{},userWrongQuestionList:{}", uid, questionBoxId, GsonUtil.toJson(userWrongQuestionList));
        //移除不一致的
        Iterator<UserWrongQuestion> iterator = userWrongQuestionList.iterator();
        while (iterator.hasNext()) {
            UserWrongQuestion userWrongQuestion = iterator.next();
            if (!uid.equals(userWrongQuestion.getUid()) || !questionBoxId.equals(userWrongQuestion.getQuestionBoxId()) ) {
                iterator.remove();
            } else {
                userWrongQuestion.setId(idworker.nextId());
            }
        }
        List<UserWrongQuestion> list = dao.getByUidAndQboxId(uid,questionBoxId);
        Map<String,Long> keyIdMap = Maps.newHashMap();
        for (UserWrongQuestion userWrongQuestion : list) {
            keyIdMap.put(userWrongQuestion.getKey(),userWrongQuestion.getId());
        }
        for (UserWrongQuestion userWrongQuestion: userWrongQuestionList) {
            String key = userWrongQuestion.getKey();
            Long id = keyIdMap.get(key);
            if (null != id) {
                userWrongQuestion.setId(id);
            }
        }
        logger.info("UserWrongQuestion insertBatchNew for userWrongQuestionList:{}", GsonUtil.toJson(userWrongQuestionList));
        dao.insertBatchNew(userWrongQuestionList);
        return;
    }
}
