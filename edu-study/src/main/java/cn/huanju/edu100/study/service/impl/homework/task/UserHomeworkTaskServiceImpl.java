package cn.huanju.edu100.study.service.impl.homework.task;

import cn.huanju.edu100.study.mapper.homework.task.UserHomeworkTaskMapper;
import cn.huanju.edu100.study.mapper.homework.user.LessonHomeworkUserMapper;
import cn.huanju.edu100.study.model.homework.task.UserHomeworkTask;
import cn.huanju.edu100.study.model.homework.user.LessonHomeworkUser;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.service.homework.task.UserHomeworkTaskService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqwx.study.dto.UserHomeworkMaxStateTaskDTO;
import com.hqwx.study.dto.query.UserHomeworkMaxStateTaskQuery;
import com.hqwx.study.dto.query.UserHomeworkTaskQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class UserHomeworkTaskServiceImpl extends ServiceImpl<UserHomeworkTaskMapper, UserHomeworkTask> implements UserHomeworkTaskService {

    private static Logger logger = LoggerFactory.getLogger(UserHomeworkTaskServiceImpl.class);


    @Resource
    UserHomeworkTaskMapper userHomeworkTaskMapper;

    @Resource
    LessonHomeworkUserMapper lessonHomeworkUserMapper;


    @Override
    public List<UserHomeworkTask> getUserHomeworkTaskList(UserHomeworkTaskQuery query) {
        QueryWrapper<UserHomeworkTask> queryWrapper = getQueryWrapper(query);
        if (query.getLessonId() != null){
            queryWrapper.eq("lesson_id", query.getLessonId());
        }
        return userHomeworkTaskMapper.selectList(queryWrapper);
    }


    @Override
    public IPage<UserHomeworkTask> getUserHomeworkTaskPage(UserHomeworkTaskQuery query) {
        Page<UserHomeworkTask> page = new Page<>(query.getPageNo(), query.getPageSize());
        IPage<UserHomeworkTask> iPage = userHomeworkTaskMapper.getTaskPage(page, query);
        return iPage;
    }

    @Override
    public boolean updateUserHomeworkTaskAnswer(Long uid, Long goodsId, Long productId, Long lessonId, Long homeworkId, Long answerId) {
        List<UserHomeworkTask> tasks = getUserHomeworkTasksByGoodsIdAndHomeworkId(uid, goodsId, productId, homeworkId);
        if (CollectionUtils.isNotEmpty(tasks)){
            UserHomeworkTask lastUserHomeworkTask = tasks.get(0);
            lastUserHomeworkTask.setAnswerId(answerId);
            lastUserHomeworkTask.setAnswerTime(new Date());
            lastUserHomeworkTask.setStatus(Consts.UserHomeworkTaskStatus.COMMIT);
            return updateById(lastUserHomeworkTask);
        } else {
            QueryWrapper<LessonHomeworkUser> queryWrapper = new QueryWrapper<>();
            if (goodsId == null && productId == null){
                logger.error("updateUserHomeworkTaskAnswer uid:{} goodsId:{} productId:{}", uid, goodsId, productId);
                return false;
            }
            if (goodsId != null){
                queryWrapper.eq("goods_id", goodsId);
            }
            queryWrapper.eq("uid", uid);
            queryWrapper.eq("product_id", productId);
            LessonHomeworkUser lessonHomeworkUser = lessonHomeworkUserMapper.selectOne(queryWrapper);
            if (lessonHomeworkUser == null){
                logger.info("insert new lessonHomeworkUser");
                lessonHomeworkUser = new LessonHomeworkUser();
                lessonHomeworkUser.setUid(uid);
                if (goodsId == null){
                    logger.error("updateUserHomeworkTaskAnswer uid:{} goodsId:{} productId:{}", uid, goodsId, productId);
                    return false;
                }
                lessonHomeworkUser.setGoodsId(goodsId);
                lessonHomeworkUser.setProductId(productId);
                lessonHomeworkUser.setCreateDate(new Date());
                lessonHomeworkUserMapper.insert(lessonHomeworkUser);
            }
            UserHomeworkTask userHomeworkTask = new UserHomeworkTask();
            userHomeworkTask.setUid(uid);
            userHomeworkTask.setGoodsId(lessonHomeworkUser.getGoodsId());
            userHomeworkTask.setProductId(lessonHomeworkUser.getProductId());
            userHomeworkTask.setLessonId(lessonId);
            userHomeworkTask.setHomeworkId(homeworkId);
            userHomeworkTask.setAnswerId(answerId);
            userHomeworkTask.setAnswerTime(new Date());
            userHomeworkTask.setStatus(Consts.UserHomeworkTaskStatus.COMMIT);
            return save(userHomeworkTask);
        }
    }

    @Override
    public boolean updateUserHomeworkTaskComment(Long uid, Long goodsId, Long productId, Long homeworkId, Long commentId, Double score) {
        List<UserHomeworkTask> tasks = getUserHomeworkTasksByGoodsIdAndHomeworkId(uid, goodsId, productId, homeworkId);
        if (CollectionUtils.isNotEmpty(tasks)){
            UserHomeworkTask lastUserHomeworkTask = tasks.get(0);
            lastUserHomeworkTask.setCommentId(commentId);
            lastUserHomeworkTask.setScore(score);
            lastUserHomeworkTask.setCommentTime(new Date());
            lastUserHomeworkTask.setStatus(Consts.UserHomeworkTaskStatus.COMMENT);
            return updateById(lastUserHomeworkTask);
        }
        return false;
    }

    private List<UserHomeworkTask> getUserHomeworkTasksByGoodsIdAndHomeworkId(Long uid, Long goodsId,Long productId, Long homeworkId) {
        UserHomeworkTaskQuery userHomeworkTaskQuery = new UserHomeworkTaskQuery();
        userHomeworkTaskQuery.setGoodsId(goodsId);
        userHomeworkTaskQuery.setProductId(productId);
        userHomeworkTaskQuery.setHomeworkId(homeworkId);
        userHomeworkTaskQuery.setUid(uid);
        List<UserHomeworkTask> tasks = getUserHomeworkTaskList(userHomeworkTaskQuery);
        return tasks;
    }


    private QueryWrapper<UserHomeworkTask> getQueryWrapper(UserHomeworkTaskQuery query) {
        QueryWrapper<UserHomeworkTask> queryWrapper = new QueryWrapper<>();
        if (query.getGoodsId() != null){
            queryWrapper.eq("goods_id", query.getGoodsId());
        }

        if (query.getProductId() != null){
            queryWrapper.eq("product_id", query.getProductId());
        }

        if (IdUtils.isValid(query.getUid())){
            queryWrapper.eq("uid", query.getUid());
        }
        if (query.getHomeworkId() != null && query.getHomeworkId()!=0){
            queryWrapper.eq("homework_id", query.getHomeworkId());
        }
        if (CollectionUtils.isNotEmpty(query.getUidList())){
            queryWrapper.in("uid", query.getUidList());
        }
        if(query.getStatus() != null){
            queryWrapper.eq("status", query.getStatus());
        }
        return queryWrapper;
    }

    @Override
    public Integer getCountSubmitCustomHomework(UserHomeworkTaskQuery query) {
        return userHomeworkTaskMapper.getCountSubmitCustomHomework(query);
    }

    @Override
    public List<UserHomeworkMaxStateTaskDTO> getMaxStateUserHomeworkTaskList(UserHomeworkMaxStateTaskQuery query) {
        return userHomeworkTaskMapper.getMaxStateUserHomeworkTaskList(query);
    }
}
