/**
 *
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.onetoone.VFeedback;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 留学反馈/报告DAO接口
 * <AUTHOR>
 * @version 2016-12-12
 */
public interface VFeedbackDao extends CrudDao<VFeedback> {

    List<VFeedback> findListByParam(Map<String, Object> params) throws DataAccessException;

    boolean insertBatch(Collection<VFeedback> feedbacks) throws DataAccessException;

    boolean updateBatch(List<VFeedback> vFeedbacks) throws DataAccessException;

    List<CountModel> listFeedbackCountByLessonIds(List<Long> lessonIds, Integer type) throws DataAccessException;

    Integer findListCountByParam(Map<String, Object> params) throws DataAccessException;
}
