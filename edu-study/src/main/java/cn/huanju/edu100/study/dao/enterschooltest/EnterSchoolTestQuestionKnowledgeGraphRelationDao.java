/**
 *
 */
package cn.huanju.edu100.study.dao.enterschooltest;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.enterschooltest.EnterSchoolTestQuestionKnowledgeGraphRelation;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 用户入学测评 题目和知识点关联 DAO接口
 * <AUTHOR>
 * @version 2021-04-27
 */
public interface EnterSchoolTestQuestionKnowledgeGraphRelationDao extends CrudDao<EnterSchoolTestQuestionKnowledgeGraphRelation> {
    /**
     * 批量插入
     *
     * @param
     * @return
     * @throws DataAccessException
     */
    public long insertBatch(List<EnterSchoolTestQuestionKnowledgeGraphRelation> enterSchoolTestQuestionKnowledgeGraphRelations) throws DataAccessException;
}