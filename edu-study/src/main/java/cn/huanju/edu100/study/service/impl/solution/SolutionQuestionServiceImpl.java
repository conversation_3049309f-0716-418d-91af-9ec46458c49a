package cn.huanju.edu100.study.service.impl.solution;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.solution.QuestionAnswerDao;
import cn.huanju.edu100.study.dao.solution.QuestionAnswerLikeDao;
import cn.huanju.edu100.study.dao.solution.QuestionCollectDao;
import cn.huanju.edu100.study.dao.solution.SolutionQuestionDao;
import cn.huanju.edu100.study.mapper.solution.SolutionQuestionAiAnswerLogDetailMapper;
import cn.huanju.edu100.study.mapper.solution.SolutionQuestionAiAnswerLogMapper;
import cn.huanju.edu100.study.model.Category;
import cn.huanju.edu100.study.model.PageModel;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.QuestionTopic;
import cn.huanju.edu100.study.model.solution.*;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.resource.PromptResource;
import cn.huanju.edu100.study.resource.feigncall.AIApiFeign;
import cn.huanju.edu100.study.resource.feigncall.dto.AIResponse;
import cn.huanju.edu100.study.resource.feigncall.dto.InputItems;
import cn.huanju.edu100.study.service.UserQuestionBoxService;
import cn.huanju.edu100.study.service.solution.SolutionQuestionService;
import cn.huanju.edu100.study.service.tutor.CommentService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.JSONUtils;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.util.upload.OssUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqwx.study.dto.SolutionQuestionDTO;
import com.hqwx.study.dto.command.AiStreamAnswerCompleteCommand;
import com.hqwx.study.dto.command.SolutionQuestionAddCommand;
import com.hqwx.study.dto.command.SolutionQuestionToManualCmd;
import com.hqwx.study.dto.query.QuestionAnswerQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class SolutionQuestionServiceImpl extends BaseServiceImpl<SolutionQuestionDao, SolutionQuestion> implements SolutionQuestionService {

    @Autowired
    private QuestionAnswerDao questionAnswerDao;

    @Autowired
    private QuestionAnswerLikeDao questionAnswerLikeDao;

    @Autowired
    private QuestionCollectDao questionCollectDao;

    @Autowired
    private CommentService commentService;

    @Autowired
    JedisPool alMasterPool;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Resource
    private AIApiFeign apiFeign;

    @Resource
    private PromptResource promptResource;

    @Autowired
    private UserQuestionBoxService userQuestionBoxService;

    @Autowired
    private SolutionQuestionAiAnswerLogMapper solutionQuestionAiAnswerLogMapper;

    @Autowired
    private SolutionQuestionAiAnswerLogDetailMapper solutionQuestionAiAnswerLogDetailMapper;

    @Autowired
    @Qualifier("KafkaTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(5,
            new BasicThreadFactory.Builder().namingPattern("ai-solution-question-%d").daemon(true).build());



    @Override
    public PageModel getUserQuestionList(SolutionQuestion questionEntry, Integer from, Integer rows) throws DataAccessException {

        Page<SolutionQuestion> page = new Page<SolutionQuestion>();
        int total = dao.findListCount(page, questionEntry);
        PageModel pageModel = new PageModel();
        pageModel.setTotal(total);
        page.setFrom(from);
        page.setPageSize(rows);
        page.setOrderBy("updatedTime desc");

        // 获取答疑问题列表
        List<SolutionQuestion> listQuestions = dao.findList(page, questionEntry);
        if (CollectionUtils.isEmpty(listQuestions)) {
            return null;
        }

        // 匹配回复列表结构
        listQuestions = questionAnswerMatch(listQuestions, "");
        pageModel.setList(listQuestions);
        return pageModel;
    }

    @Override
    public SolutionQuestion getQuestionInfoById(Long questionId, Long uid) throws DataAccessException {
        if (questionId == null || questionId <= 0L) {
            return null;
        }
        HashMap<String, Long> queryParam = new HashMap<String, Long>();
        queryParam.put("questionId", questionId);
        // 获取问题详情信息
        SolutionQuestion questionAnswerInfo = dao.findUserQuestionInfoById(queryParam);
        if (questionAnswerInfo == null) {
            return null;
        }
        StringBuilder qids = new StringBuilder();
        HashMap<String, Long> qidMap = new HashMap<String, Long>();
        qidMap.put("pid", questionId);
        List<SolutionQuestion> questionList = dao.findUserQuestionAnswerListByPid(qidMap);
        if (CollectionUtils.isNotEmpty(questionList)) {
            boolean comma = false;
            for (SolutionQuestion questionItem : questionList) {
                if (comma) {
                    qids.append(",");
                }
                if (questionItem.getId() != null) {
                    qids.append(questionItem.getId().toString());
                    comma = true;
                }
            }
        }
        if (questionAnswerInfo.getId() != null) {
            if (qids.length() > 0) {
                qids.append(",");
            }
            qids.append(questionAnswerInfo.getId().toString());
        }

        List<Long> answerIds = new ArrayList<Long>();
        // 获取问题的回复信息
        List<QuestionAnswer> listQuestionAnswers = questionAnswerDao.findUserAnswerListByQids(qids.toString());
        Map<Long, QuestionAnswer> mapListQuestionAnswer = new HashMap<Long, QuestionAnswer>();
        if (CollectionUtils.isNotEmpty(listQuestionAnswers)) {
            for (QuestionAnswer answers : listQuestionAnswers) {
                if (answers.getId() != null) {
                    if (answers.getIsRead() != 1) {
                        answerIds.add(answers.getId());
                    }
                    mapListQuestionAnswer.put(answers.getQuestionId(), answers);
                }
            }
        }
        // 把回复拼接到问题回复属性
        if (questionAnswerInfo.getId() != null && mapListQuestionAnswer.get(questionAnswerInfo.getId()) != null) {
            questionAnswerInfo.setQuestionAnswer(mapListQuestionAnswer.get(questionAnswerInfo.getId()));
        }

        this.canUserVirtualTeacher(questionAnswerInfo);

        // 追问的问题和回复拼接到问题追问属性列表
        if (CollectionUtils.isNotEmpty(questionList)) {
            for (SolutionQuestion questionItem : questionList) {
                if (questionItem.getId() != null && mapListQuestionAnswer.get(questionItem.getId()) != null) {
                    questionItem.setQuestionAnswer(mapListQuestionAnswer.get(questionItem.getId()));
                }
                this.canUserVirtualTeacher(questionItem);
            }
            questionAnswerInfo.setQuestionAgainList(questionList);
        }

        if (!ValidateUtils.isEmpty(uid)) {
            // 判断用户是否收藏此问题
            HashMap<String, Long> collectMap = new HashMap<String, Long>();
            collectMap.put("questionId", questionAnswerInfo.getId());
            collectMap.put("uid", uid);
            QuestionCollect questionCollect = questionCollectDao.findUserCollectByQuestionIdAndUid(collectMap);
            if (questionCollect == null) {
                questionAnswerInfo.setHaveCollected(0);
            } else {
                questionAnswerInfo.setHaveCollected(1);
            }

            // 判断用户是否点赞此回复
            if (questionAnswerInfo.getQuestionAnswer() != null) {
                HashMap<String, Long> likeMap = new HashMap<String, Long>();
                likeMap.put("answerId", questionAnswerInfo.getQuestionAnswer().getId());
                likeMap.put("uid", uid);
                QuestionAnswerLike questionAnswerLike = questionAnswerLikeDao.findUserAnswerLikeByAnswerIdAndUid(likeMap);
                if (questionAnswerLike == null) {
                    questionAnswerInfo.getQuestionAnswer().setHavaLiked(0);
                } else {
                    questionAnswerInfo.getQuestionAnswer().setHavaLiked(1);
                }
            }

            // 自己查看自己提问的问题回复需要更新回复阅读状态
            if (questionAnswerInfo.getUserId().longValue() == uid.longValue()) {
                if (CollectionUtils.isNotEmpty(answerIds)) {
                    Long updatedTime = Calendar.getInstance().getTimeInMillis();
                    QuestionAnswer questionAnswer = new QuestionAnswer();
                    for (Long aid : answerIds) {
                        questionAnswer.setId(aid);
                        questionAnswer.setIsRead(1);
                        questionAnswer.setUpdatedTime(updatedTime/1000);
                        questionAnswerDao.update(questionAnswer);
                    }
                }
            }
        }

        // 更新点击数、浏览量 +1
        HashMap<String, Long> viewMap = new HashMap<String, Long>();
        viewMap.put("id", questionId);
        dao.updateViews(viewMap);
        return questionAnswerInfo;
    }

    private void canUserVirtualTeacher(SolutionQuestion questionItem) {
        List<Integer> exceptList = Arrays.asList(Consts.SOLUTION_QUESTION_IS_AI_ANSWER.CHANGE_AI_TO_MANUAL,
                Consts.SOLUTION_QUESTION_IS_AI_ANSWER.MANUAL,
                Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_ANSWERING);
        if (exceptList.contains(questionItem.getIsAiAnswer())){
            questionItem.setIsCanUseVirtualTeacher(0);
        } else if (questionItem.getIsAiAnswer() == Consts.SOLUTION_QUESTION_IS_AI_ANSWER.MANUAL_CHECKED){
            if (Objects.nonNull(questionItem.getQuestionAnswer()) && questionItem.getQuestionAnswer().getUserId() == 0){
                questionItem.setIsCanUseVirtualTeacher(1);
            } else {
                questionItem.setIsCanUseVirtualTeacher(0);
            }
        }else {
            questionItem.setIsCanUseVirtualTeacher(1);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED)
    public Long insertQuestionInfo(HashMap<String, Object> entryMap) throws DataAccessException {
        if (entryMap == null ) {
            return null;
        }
        Integer answerType = (Integer)entryMap.get("answerType");
        Long pid = (Long)entryMap.get("pid");
        if (pid > 0L) {
            // 检查追问的主问题是否存在
            HashMap<String, Long> queryParam = new HashMap<String, Long>();
            queryParam.put("questionId", pid);
            SolutionQuestion questionAnswerInfo = dao.findUserQuestionInfoById(queryParam);
            if (questionAnswerInfo == null) {
                return null;
            }
        }
        SolutionQuestion questionEntry = getQuestionEntry(entryMap, pid);
        String questionContent = null;
        //如果指定人工回复时，跳过ai回复
        if (Objects.nonNull(answerType) && answerType == 2) {
            questionEntry.setIsAiAnswer(Consts.SOLUTION_QUESTION_IS_AI_ANSWER.MANUAL);
        } else {
            Map<String, Object> contentMap = (Map<String, Object>) JSONUtils.parseObject(questionEntry.getContent(), Map.class);
            questionContent = (String) contentMap.get("text");
            if (StringUtils.isBlank(questionContent)) {
                questionContent = questionEntry.getTitle();
            } else if ((pid == null || pid <=0 ) && !StringUtils.containsIgnoreCase(questionContent, questionEntry.getTitle())) {//AI追问内容不添加父标题
                questionContent = questionEntry.getTitle() + "， " + questionContent;
            }
            Integer isAiAnswer = checkIsAiAnswer(questionEntry, questionContent, contentMap);
            logger.info("question:【{}】,isAiAnswer is {}", questionEntry.getContentText(),isAiAnswer);
            questionEntry.setIsAiAnswer(isAiAnswer);
        }
        Long id = dao.insert(questionEntry);

        this.addToAiAnswerLog(id,questionEntry.getIsAiAnswer(),null);

        //进行ai回复的处理 (AI流式回复不走这个逻辑)
        if (questionEntry.getIsStream() == null || questionEntry.getIsStream() == 0){
            dealForAiAnswer(id, questionEntry, questionContent);
        }
        pushQuestionEntryToAI(questionEntry);

        if (!ValidateUtils.isEmpty(pid)) {
            // 追问需要更新改主问题status为2 追问状态
            SolutionQuestion parent = new SolutionQuestion();
            parent.setUpdatedTime(System.currentTimeMillis()/1000);
            parent.setId(pid);
            parent.setStatus(Consts.SOLUTION_QUESTION_STATUS.CROSS_QUESTION);
            parent.setUserId(questionEntry.getUserId());
            dao.update(parent);
        }

        this.asyncProcessFailureSolutionQuestion(id);

        return id;
    }

    /**
     * 处理已经等待5分钟仍然是AI回复中的数据
     * <AUTHOR>
     */
    private void asyncProcessFailureSolutionQuestion(Long id) {

        executorService.schedule(() -> {
            try {
                SolutionQuestion solutionQuestion = dao.get(id);
                if (solutionQuestion.getIsAiAnswer() == Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_ANSWERING){
                    int isAiAnswer = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_EXCEPTION_TO_MANUAL;
                    solutionQuestion.setIsAiAnswer(isAiAnswer);
                    dao.update(solutionQuestion);

                    this.updateAiAnswerLog(id,isAiAnswer,null,"AI回复超时，程序自动判定为AI异常转人工");

                }
            } catch (DataAccessException e) {
                logger.error("获取SolutionQuestion异常,id:{}" ,id, e);
            }
        },5,TimeUnit.MINUTES);

    }

    /**
     * 判断是否ai回复
     * @return Integer
     */
    private Integer checkIsAiAnswer(SolutionQuestion question, String questionContent, Map<String,Object>  contentMap) {
        int result = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_OVER_TO_MANUAL;
        if (question != null) {
            boolean haveImage = false;
            // 增加图片答疑，由japi（restful）来控制 answerType 字段,这里用户问题中带图一律放行
//            List<String> images = (List<String>)contentMap.get("images");
//            if (CollectionUtils.isNotEmpty(images)) {
//                for (String image : images) {
//                    if (StringUtils.isNotBlank(image)) {
//                        haveImage = true;
//                        logger.info("question:【{}】 答疑内容含有图片",question.getContentText());
//                        break;
//                    }
//                }
//            }

            if (question.getQuestionId()!=null && question.getQuestionId() > 0){
                haveImage = this.isQuestionHaveImage(question.getQuestionId());
            }

            //没有图片等网络存储数据时可以ai回复
            if (!haveImage) {
                result = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_ANSWERING;
            } else {
                logger.info("question:【{}】 答疑问题包含图片",question.getContentText());
            }

            List<Long> newAiCategoryIdList = promptResource.getNewAiCategoryIdList();
            if (CollectionUtils.isNotEmpty(newAiCategoryIdList) && Objects.nonNull(question.getCategoryId())){
                if (!newAiCategoryIdList.contains(question.getCategoryId())){
                    logger.info("question:【{}】 所属类目未配置开启AI答疑",question.getContentText());
                    result = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_OVER_TO_MANUAL;
                }
            }

            //用户提问限制（10次/天/科目/人）   20241219-去掉限制wy
//            Long userId = question.getUserId();
//            Long categoryId = question.getCategoryId();
//            Map<String, Object> queryParam = new HashMap<>();
//            queryParam.put("userId",userId);
//            queryParam.put("categoryId",categoryId);
//            queryParam.put("startTime", DateUtil.beginDay(new Date()).getTime()/1000);
//            try {
//                Integer countSoFar = dao.countSoFar(queryParam);
//                if (countSoFar >= 10 && !(Objects.equals(userId,100001320L) || Objects.equals(userId,11498706L))){ //IOS庞辉君那边调试，临时给这个测试账号加个后门
//                    result = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_OVER_TO_MANUAL;
//                    logger.info("question:【{}】 userId：{},categoryId:{} 用户该科目下当天答疑次数已超过10次",question.getContentText(), userId, categoryId);
//                }
//            } catch (DataAccessException e) {
//                logger.warn("solutionQuestion countSoFar error",e);
//            }

        }
        return result;
    }

    public boolean isQuestionHaveImage(Long questionId) {
        List<Question> questionList = knowledgeResource.getQuestionByIds(Arrays.asList(questionId));
        if (CollectionUtils.isEmpty(questionList)){
            return false;
        }
        Question question = questionList.get(0);
        String content = question.getContent();
        Integer isMulti = question.getIsMulti();

        Pattern pattern = Pattern.compile("<img[^>]+>", Pattern.CASE_INSENSITIVE);

        //题目信息
        if (StringUtils.isNotBlank(content) && isMulti != null && isMulti > 0){
            String material = null;
            if (StringUtils.startsWith(question.getContent(),"http://") || StringUtils.startsWith(question.getContent(),"https://")) {
                material = StringEscapeUtils.unescapeHtml4(OssUtil.getContentDataFromOss(question.getContent()));
            } else {
                material = StringEscapeUtils.unescapeHtml4(question.getContent());
            }
            if (pattern.matcher(material).find()){
                return true;
            }
        }

        List<QuestionTopic> topicList = question.getTopicList();
        if (CollectionUtils.isNotEmpty(topicList)){
            for (QuestionTopic topic : topicList) {
                String topicContent = StringEscapeUtils.unescapeHtml4(topic.getContent());
                if (pattern.matcher(topicContent).find()){
                    return true;
                }
            }
        }

        return false;
    }

    private void dealForAiAnswer(Long questionId, SolutionQuestion question, String questionContent) {
        if (Objects.nonNull(question.getIsAiAnswer())
                && question.getIsAiAnswer() == Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_ANSWERING
        ) {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    Long pid = question.getPid();
                    Long secondCategory = question.getSecondCategory();

                    String background = userQuestionBoxService.getQuestionInfoInStringByIdNew(question.getQuestionId(),question.getTitle(),questionContent);

                    Map<String, Object> contentMap = new HashMap<>();
                    //同一个父问题保持上下文
                    Map<String, Object> res = getAnswerFromAi(question.getUserId() + "" + (Objects.nonNull(pid) && pid > 0 ? pid : questionId), questionContent, secondCategory, background,question.getQuestionId());

                    boolean success = false;
                    String answer = null;
                    if (Objects.nonNull(res.get("code")) && 0 == (Integer) res.get("code")) {
                        answer = (String) res.get("msg");
                        success = true;

                        contentMap.put("images", new ArrayList<>());
                        contentMap.put("audios", new ArrayList<>());
                        contentMap.put("text", answer);
                    } else {
                        //ai返回unknown时记录一下
                        if (Objects.nonNull(res.get("code")) && -1 == (Integer) res.get("code")) {
                            contentMap.put("images", new ArrayList<>());
                            contentMap.put("audios", new ArrayList<>());
                            contentMap.put("text", (String) res.get("msg"));
                        }
                    }

                    aiAnswerComplete(answer,success,questionId,pid, question.getUserId(),contentMap);
                }



            });
        } /*else if (Objects.nonNull(question.getIsAiAnswer())
                && question.getIsAiAnswer() == Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_OVER_TO_MANUAL) {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    updateAiAnswerLog(questionId,Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_OVER_TO_MANUAL,null);
                }
            });
        }*/
    }

    private void aiAnswerComplete(String answer, boolean success, Long questionId, Long pid, Long userId, Map<String, Object> contentMap) {
        AiStreamAnswerCompleteCommand command = new AiStreamAnswerCompleteCommand();
        command.setAnswer(answer);
        command.setSuccess(success);
        command.setQuestionId(questionId);
        command.setPid(pid);
        command.setUid(userId);
        command.setContentMap(contentMap);

        this.aiAnswerComplete(command);
    }

    @Override
    public void aiAnswerComplete(AiStreamAnswerCompleteCommand command) {
        String answer = command.getAnswer();
        boolean success = command.isSuccess();
        Long questionId = command.getQuestionId();
        Long pid = command.getPid();
        Long uid = command.getUid();
        Map<String, Object> contentMap = command.getContentMap();

        int isAiAnswer = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_ANSWERING;
        int status = Consts.SOLUTION_QUESTION_STATUS.NO_ANSWER;

        if (success) {
            answer = StringUtils.replace(answer, "\n", "<br/>");
//            answer = "<p>学员您好，您的问题回复如下：</p><p>" + answer + "</p><p>祝您学习愉快！</p>";
            //创建SolutionAnswer
            QuestionAnswer insert = new QuestionAnswer();
            insert.setPid(pid);
            insert.setQuestionId(questionId);

            insert.setContent(JSONUtils.toJsonString(contentMap));
            insert.setContentText(answer);
            insert.setIsBest(0);
            insert.setIsRead(0);
            insert.setUserId(0L);
            insert.setUserName("AI老师");
            insert.setLikeNum(0);
            insert.setCreatedTime(System.currentTimeMillis() / 1000);
            insert.setUpdatedTime(System.currentTimeMillis() / 1000);
            try {
                questionAnswerDao.insert(insert);
                isAiAnswer = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_ANSWERED;
                status = Consts.SOLUTION_QUESTION_STATUS.ANSWERED;
            } catch (Exception e) {
                logger.error("write solution answer catch exception", e);
                //异常时转人工
                isAiAnswer = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_EXCEPTION_TO_MANUAL;
            }
        } else {
            //失败时转人工
            isAiAnswer = Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_EXCEPTION_TO_MANUAL;

        }
        //更新SolutionQuestion
        SolutionQuestion updateQuestion = new SolutionQuestion();
        updateQuestion.setId(questionId);
        updateQuestion.setIsAiAnswer(isAiAnswer);
        updateQuestion.setStatus(status);
        updateQuestion.setUserId(uid);
        updateQuestion.setUpdatedTime(System.currentTimeMillis() / 1000);
        try {
            dao.update(updateQuestion);

            this.updateAiAnswerLog(questionId,isAiAnswer, JSONUtils.toJsonString(contentMap),null);
        } catch (DataAccessException e) {
            logger.error("update solution question catch exception, id :{}", questionId, e);
        }

        // 追问被ai回复后，需要更新主问题status为1 已完成
        if (status == Consts.SOLUTION_QUESTION_STATUS.ANSWERED && !ValidateUtils.isEmpty(pid)) {
            SolutionQuestion parent = new SolutionQuestion();
            parent.setUpdatedTime(System.currentTimeMillis() / 1000);
            parent.setId(pid);
            parent.setStatus(Consts.SOLUTION_QUESTION_STATUS.ANSWERED);
            parent.setUserId(uid);
            try {
                dao.update(parent);
            } catch (DataAccessException e) {
                logger.error("update parent solution question catch exception, pid:{}", pid, e);
            }
        }
    }

    @Override
    public boolean deleteSolutionAnswerById(Long id) throws DataAccessException {
        return questionAnswerDao.delete(id);
    }

    @Override
    public Long addSolutionQuestion(SolutionQuestionAddCommand params) throws DataAccessException {
        SolutionQuestion solutionQuestion = this.convertParamToPO(params);

        long id = dao.insert(solutionQuestion);
        this.pushQuestionEntryToAI(solutionQuestion);
        return id;
    }

    @Override
    public boolean solutionQuestionToManual(SolutionQuestionToManualCmd params) throws DataAccessException, BusinessException {
        SolutionQuestion question = null;
        Long questionId = params.getQuestionId();
        Long uid = params.getUid();
        try {
            question = dao.get(questionId);
        } catch (Exception e) {
            logger.error("获取答疑信息失败",e);
        }

        if (Objects.isNull(question)) {
            throw new BusinessException(Constants.SYS_ERROR, "答疑不存在");
        }

        if (Objects.isNull(question.getUserId()) || !Objects.equals(question.getUserId(), uid)) {
            throw new BusinessException(Constants.SYS_ERROR, "这不是您的问题！");
        }
        //问题修改成未回复
        SolutionQuestion update = new SolutionQuestion();
        update.setUpdatedTime(System.currentTimeMillis() / 1000);
        update.setId(questionId);
        update.setIsAiAnswer(Consts.SOLUTION_QUESTION_IS_AI_ANSWER.CHANGE_AI_TO_MANUAL);
        update.setStatus(Consts.SOLUTION_QUESTION_STATUS.NO_ANSWER);
        update.setUserId(question.getUserId());
        update.setChangeAiToManualTime(new Date());
        try {
            dao.update(update);
            updateAiAnswerLog(questionId, Consts.SOLUTION_QUESTION_IS_AI_ANSWER.CHANGE_AI_TO_MANUAL,null,null);

            return true;
        } catch (DataAccessException e) {
            logger.error("solutionQuestionToManual catch exception, pid:{}", questionId, e);
        }
        return false;
    }

    private SolutionQuestion convertParamToPO(SolutionQuestionAddCommand params) {
        SolutionQuestion solutionQuestion = new SolutionQuestion();

        solutionQuestion.setConversationId(params.getConversationId());
        solutionQuestion.setMessageId(params.getMessageId());

        solutionQuestion.setExtendInfo(Optional.ofNullable(params.getExtendInfo()).orElse("{}"));
        solutionQuestion.setPid(Optional.ofNullable(params.getPid()).orElse(0L));
        solutionQuestion.setCategoryIdCode(params.getCategoryIdCode());
        solutionQuestion.setFirstCategory(params.getFirstCategory());
        solutionQuestion.setSecondCategory(params.getSecondCategory());
        solutionQuestion.setCategoryId(params.getCategoryId());

        solutionQuestion.setCourseId(Optional.ofNullable(params.getCourseId()).orElse(0));
        solutionQuestion.setLessonId(Optional.ofNullable(params.getLessonId()).orElse(0));
        solutionQuestion.setTeachBookId(Optional.ofNullable(params.getTeachBookId()).orElse(0));

        solutionQuestion.setChapterId(Optional.ofNullable(params.getChapterId()).orElse(0));
        solutionQuestion.setKnowledgeId(Optional.ofNullable(params.getKnowledgeId()).orElse(0));
        solutionQuestion.setQuestionId(Optional.ofNullable(params.getQuestionId()).orElse(0L));

        solutionQuestion.setGoodsId(Optional.ofNullable(params.getGoodsId()).orElse(0L));
        solutionQuestion.setTitle(params.getTitle());
        solutionQuestion.setContent(params.getContent());
        solutionQuestion.setContentText(params.getContentText());

        solutionQuestion.setPosition(Optional.ofNullable(params.getPosition()).orElse(0));

        solutionQuestion.setUserId(params.getUserId());
        solutionQuestion.setUserName(params.getUserName());
        solutionQuestion.setSource(params.getSource());

        solutionQuestion.setStatus(params.getStatus());
        solutionQuestion.setIsFrozen(params.getIsFrozen());
        solutionQuestion.setDevice(params.getDevice());
        solutionQuestion.setIp(params.getIp());

        solutionQuestion.setCreatedTime(Optional.ofNullable(params.getCreatedTime()).orElse(System.currentTimeMillis()/1000));
        solutionQuestion.setUpdatedTime(Optional.ofNullable(params.getUpdatedTime()).orElse(System.currentTimeMillis()/1000));

        solutionQuestion.setTag(params.getTag());
        solutionQuestion.setViews(params.getViews());
        solutionQuestion.setIsComplained(params.getIsComplained());
        solutionQuestion.setIsAl(Optional.ofNullable(params.getIsAl()).orElse(0));

        solutionQuestion.setProductId(Optional.ofNullable(params.getProductId()).orElse(0L));
        solutionQuestion.setQuestionType(params.getQuestionType());
        solutionQuestion.setPathId(Optional.ofNullable(params.getPathId()).orElse(0L));
        solutionQuestion.setPaperId(Optional.ofNullable(params.getPaperId()).orElse(0L));
        solutionQuestion.setAnswerId(Optional.ofNullable(params.getAnswerId()).orElse(0L));

        solutionQuestion.setSourceType(params.getSourceType());
        solutionQuestion.setResourceId(Optional.ofNullable(params.getResourceId()).orElse(0L));
        solutionQuestion.setIsPublish(Optional.ofNullable(params.getIsPublish()).orElse(0));
        solutionQuestion.setIsAiAnswer(params.getIsAiAnswer());
        solutionQuestion.setIsStream(params.getIsStream());
        solutionQuestion.setChangeAiToManualTime(params.getChangeAiToManualTime());

        solutionQuestion.setSchId(params.getSchId());

        if (StringUtils.isNotBlank(solutionQuestion.getSource())) {
            solutionQuestion.setSourceType(solutionQuestion.getSourceTypeBySource(solutionQuestion.getSource()));
            if (solutionQuestion.getSource().equals(Consts.QUESTION_RESOURCE.UC_YSS) || solutionQuestion.getSource().equals(Consts.QUESTION_RESOURCE.APP_YSS)) {//答疑优化V3.4.2版本后，舍弃使用，uc_yss、app_yss归为ucenter
                solutionQuestion.setSource(Consts.QUESTION_RESOURCE.UCENTER);
            }
        }
        if (solutionQuestion.getIsAl() != null && solutionQuestion.getIsAl() == 1) {//云私塾
            solutionQuestion.setSourceType(Consts.QUESTION_RESOURCE_TYPE.YSS);
        }
        if (solutionQuestion.getIsAl() == null || solutionQuestion.getIsAl() == 0) {
            if (solutionQuestion.getSourceType() != null && solutionQuestion.getSourceType() == Consts.QUESTION_RESOURCE_TYPE.YSS) {//云私塾
                solutionQuestion.setIsAl(1);
            }
        }
        return solutionQuestion;
    }

    private void addToAiAnswerLog(Long questionId, Integer isAiAnswer, String content) {
        SolutionQuestionAiAnswerLog entity = new SolutionQuestionAiAnswerLog();
        entity.setQuestionId(questionId);
        entity.setIsAiAnswer(isAiAnswer);
        entity.setAiAnswerContent(content);
        entity.setCreatedTime(System.currentTimeMillis()/1000);
        entity.setUpdatedTime(System.currentTimeMillis()/1000);

        solutionQuestionAiAnswerLogMapper.insert(entity);

        //插入明细
        SolutionQuestionAiAnswerLogDetail logDetail = new SolutionQuestionAiAnswerLogDetail();
        logDetail.setQuestionId(questionId);
        logDetail.setLogId(entity.getId());
        logDetail.setIsAiAnswer(isAiAnswer);
        logDetail.setAnswerContent(content);
        logDetail.setCreatedTime(new Date());
        solutionQuestionAiAnswerLogDetailMapper.insert(logDetail);
    }

    private void updateAiAnswerLog(Long questionId, Integer isAiAnswer, String content, String remark) {
        LambdaQueryWrapper<SolutionQuestionAiAnswerLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SolutionQuestionAiAnswerLog::getQuestionId, questionId);
        SolutionQuestionAiAnswerLog log = solutionQuestionAiAnswerLogMapper.selectOne(wrapper);
        if (Objects.nonNull(log) && Objects.nonNull(log.getId())) {
            SolutionQuestionAiAnswerLog entity = new SolutionQuestionAiAnswerLog();
            entity.setId(log.getId());
            entity.setIsAiAnswer(isAiAnswer);
            entity.setUpdatedTime(System.currentTimeMillis()/1000);
            entity.setAiAnswerContent(content);

            solutionQuestionAiAnswerLogMapper.updateById(entity);

            //插入明细
            SolutionQuestionAiAnswerLogDetail logDetail = new SolutionQuestionAiAnswerLogDetail();
            logDetail.setQuestionId(questionId);
            logDetail.setLogId(entity.getId());
            logDetail.setIsAiAnswer(isAiAnswer);
            logDetail.setCreatedTime(new Date());
            logDetail.setAnswerContent(content);
            logDetail.setRemark(remark);
            solutionQuestionAiAnswerLogDetailMapper.insert(logDetail);
        } else {
            logger.error("cannot find ai answer log for question with id:{}", questionId);
        }

    }

    /**
     * 请求AI接口回答问题，并写入SolutionAnswer
     * @param
     * @return
     */
    private Map<String,Object> getAnswerFromAi(String key, String content, Long secondCategory, String background,Long questionId) {
        Map<String, Object> result = new HashMap<>();
        //获取考试名称
        String secondCategoryName = "";
        if (secondCategory != null && secondCategory != -1) {
            Category category = knowledgeResource.getCategoryInfoById(secondCategory);
            if (category != null) {
                secondCategoryName = category.getName();
            }
        }

        InputItems query = new InputItems();
        query.setUser(Objects.nonNull(key)? String.valueOf(key) : "");
        try {
            //获取prompt
            String prompt = null;
            if (questionId != null && questionId > 0){
                prompt = promptResource.getPromptForSolutionQuestion(3L,secondCategoryName, content,background);
                query.setSystemPrompt(prompt);
                query.setMessage(background);
            } else {
                prompt = promptResource.getPromptForSolutionQuestion(1L,secondCategoryName, content, background);
                query.setMessage(prompt);
            }
            query.setTemperature(0.2d);
            query.setApplication("user_qa");
//            query.setModelName("gpt-4");

            logger.debug("ai request questionKey is {},req:{}", key,GsonUtil.getGenericGson().toJson(query));
            AIResponse aiResponse = apiFeign.getAnswerFromChatGPT(query);
            logger.debug("ai response is {}", JSONUtils.toJsonString(aiResponse));
            if (aiResponse != null && "200".equals(aiResponse.getCode())) {
                String output = (String) aiResponse.getOutObj();
                if (StringUtils.containsIgnoreCase(output, "unknown")) {
                    result.put("code", -1);
                    result.put("msg", output);
                } else {
                    result.put("code", 0);
                    result.put("msg", output);
                }
            } else {
                result.put("code", -2);
                result.put("msg", "调用失败了");
            }
        } catch (Exception e) {
            logger.error("get ai answer catch exception", e);
            result.put("code", -2);
            result.put("msg", "调用失败了");
        }
        return result;
    }

    private SolutionQuestion getQuestionEntry(HashMap<String, Object> entryMap, Long pid) {
        String title = (String) entryMap.get("title");
        Long firstCategory = (Long) entryMap.get("firstCategory");
        Long secondCategory = (Long) entryMap.get("secondCategory");
        Long categoryId = (Long) entryMap.get("categoryId");
        String contentText = (String) entryMap.get("contentText");
        SolutionQuestion questionEntry = new SolutionQuestion();
        questionEntry.setPid(pid);
        questionEntry.setUserId((Long) entryMap.get("userId"));
        questionEntry.setUserName((String) entryMap.get("userName"));
        questionEntry.setCategoryIdCode((String) entryMap.get("categoryIdCode"));
        questionEntry.setFirstCategory(firstCategory);
        questionEntry.setSecondCategory(secondCategory);
        questionEntry.setCategoryId(categoryId);
        questionEntry.setCourseId((Integer) entryMap.get("courseId"));
        questionEntry.setLessonId((Integer) entryMap.get("lessonId"));
        questionEntry.setTeachBookId((Integer) entryMap.get("teachBookId"));
        questionEntry.setChapterId((Integer) entryMap.get("chapterId"));
        questionEntry.setKnowledgeId((Integer) entryMap.get("knowledgeId"));
        questionEntry.setQuestionId((Long) entryMap.get("questionId"));
        questionEntry.setGoodsId((Long) entryMap.get("goodsId"));
        questionEntry.setTitle(title);
        questionEntry.setContent((String) entryMap.get("content"));
        questionEntry.setContentText(contentText);
        questionEntry.setSource((String) entryMap.get("source"));
        questionEntry.setDevice((String) entryMap.get("device"));
        questionEntry.setIp((String) entryMap.get("ip"));
        questionEntry.setIsAl((Integer) entryMap.get("isAl"));
        Long productId = null == entryMap.get("productId") ? 0L : (Long) entryMap.get("productId");
        questionEntry.setProductId(productId);
        questionEntry.setSchId((Long) entryMap.get("schId"));
        questionEntry.setPathId(null == entryMap.get("pathId") ? 0L : (Long) entryMap.get("pathId"));
        questionEntry.setPosition(null == entryMap.get("position") ? 0 : (Integer) entryMap.get("position"));
        questionEntry.setQuestionType(null == entryMap.get("questionType") ? 0 : (Integer) entryMap.get("questionType"));
        questionEntry.setPaperId(null == entryMap.get("paperId") ? 0L : (Long) entryMap.get("paperId"));
        questionEntry.setAnswerId(null == entryMap.get("answerId") ? 0L : (Long) entryMap.get("answerId"));
        questionEntry.setExtendInfo(entryMap.get("extendInfo") == null ? null : entryMap.get("extendInfo").toString());
        questionEntry.setIsPublish(entryMap.get("isPublish") == null ? 0 : (Integer) entryMap.get("isPublish"));
        questionEntry.setResourceId(entryMap.get("resourceId") == null ? null : (Long) entryMap.get("resourceId"));
        questionEntry.setIsStream(entryMap.get("isStream") == null ? 0 : (Integer) entryMap.get("isStream"));
        if (StringUtils.isNotBlank(questionEntry.getSource())) {
            questionEntry.setSourceType(questionEntry.getSourceTypeBySource(questionEntry.getSource()));
            if (questionEntry.getSource().equals(Consts.QUESTION_RESOURCE.UC_YSS) || questionEntry.getSource().equals(Consts.QUESTION_RESOURCE.APP_YSS)) {//答疑优化V3.4.2版本后，舍弃使用，uc_yss、app_yss归为ucenter
                questionEntry.setSource(Consts.QUESTION_RESOURCE.UCENTER);
            }
        }
        if (questionEntry.getIsAl() != null && questionEntry.getIsAl() == 1) {//云私塾
            questionEntry.setSourceType(Consts.QUESTION_RESOURCE_TYPE.YSS);
        }
        if (questionEntry.getIsAl() == null || questionEntry.getIsAl() == 0) {
            if (questionEntry.getSourceType() != null && questionEntry.getSourceType() == Consts.QUESTION_RESOURCE_TYPE.YSS) {//云私塾
                questionEntry.setIsAl(1);
            }
        }
        return questionEntry;
    }

    /**
     * 推送
     * @param questionEntry
     */
    private void pushQuestionEntryToAI(SolutionQuestion questionEntry) {
        try {
            Long uid = questionEntry.getUserId() == null ? 0L : questionEntry.getUserId();
            Map<String, Object> map = new HashMap();
            map.put("type", "user_question_answer");
            map.put("state", 1);
            {
                Map attrsMap = new HashMap();
                attrsMap.put("uid", uid);
                attrsMap.put("goods_id", questionEntry.getGoodsId() == null ? 0L : questionEntry.getGoodsId());
                attrsMap.put("category_id", questionEntry.getCategoryId() == null ? 0L : questionEntry.getCategoryId());
                attrsMap.put("product_id", questionEntry.getProductId() == null ? 0L : questionEntry.getProductId());
                attrsMap.put("knowledge_id", questionEntry.getKnowledgeId() == null ? 0L : questionEntry.getKnowledgeId());
                map.put("attrs", attrsMap);
            }

            String data = GsonUtil.toJson(map);
            logger.info("pushQuestionEntryToAI: " + data);
            final String topic = "hq_intelligence_education_user_question_answer_p20";
            final String key = uid.toString();
            kafkaTemplate.send(topic, key, data);

        } catch (Exception e) {
            logger.error("user_question_answer push failed", e);
        } finally {
        }
    }

    @Override
    public List<SolutionQuestion> getQuestionListByIds(ArrayList<Long> questionIds) throws DataAccessException {
        if (CollectionUtils.isEmpty(questionIds)) {
            return null;
        }
        StringBuilder qidsBuffer = new StringBuilder();
        boolean comma = false;
        for (Long qid : questionIds) {
            if (comma) {
                qidsBuffer.append(",");
            }
            if (qid != null) {
                qidsBuffer.append(qid.toString());
                comma = true;
            }
        }

        String qidsStr = qidsBuffer.toString();
        List<SolutionQuestion> questionList = dao.findUserQuestionListByIds(qidsStr);

        // 匹配回复列表结构
        questionList = questionAnswerMatch(questionList, qidsStr);
        return questionList;
    }

    @Override
    public List<SolutionQuestion> getQuestionListByIdsOrderById(ArrayList<Long> questionIds) throws DataAccessException {
        if (CollectionUtils.isEmpty(questionIds)) {
            return null;
        }
        StringBuilder qidsBuffer = new StringBuilder();
        boolean comma = false;
        for (Long qid : questionIds) {
            if (comma) {
                qidsBuffer.append(",");
            }
            if (qid != null) {
                qidsBuffer.append(qid.toString());
                comma = true;
            }
        }

        String qidsStr = qidsBuffer.toString();
        List<SolutionQuestion> questionList = dao.findUserQuestionListByIdsOrderById(qidsStr);

        // 匹配回复列表结构
        questionList = questionAnswerMatch(questionList, qidsStr);
        return questionList;
    }

    @Override
    public PageModel getUserHotQuestionList(HashMap<String, Object> queryParam) throws DataAccessException {
        // 获取答疑问题列表
        List<SolutionQuestion> listQuestions = dao.findUserHotQuestionList(queryParam);
        if (CollectionUtils.isEmpty(listQuestions)) {
            return null;
        }

        PageModel pageModel = new PageModel();
        int total = dao.findUserHotQuestionListCount(queryParam);
        pageModel.setTotal(total);

        // 匹配回复列表结构
        listQuestions = questionAnswerMatch(listQuestions, "");
        pageModel.setList(listQuestions);

        return pageModel;
    }

    /**
     * 组装提问和答复的结构
     * @param listQuestions
     * @return
     * @throws DataAccessException
     */
    private List<SolutionQuestion> questionAnswerMatch(List<SolutionQuestion> listQuestions, String qidsStr) throws DataAccessException {
        if (CollectionUtils.isEmpty(listQuestions)) {
            return null;
        }
        boolean comma = false;
        String questionIds;
        if (ValidateUtils.isEmpty(qidsStr)) {
            StringBuilder qids = new StringBuilder();
            for (SolutionQuestion question : listQuestions) {
                if (comma) {
                    qids.append(",");
                }
                if (question.getId() != null) {
                    qids.append(question.getId().toString());
                    comma = true;
                }
            }
            questionIds = qids.toString();
        } else {
            questionIds = qidsStr;
        }

        // 通过问题列表id查询对应的回复列表
        List<QuestionAnswer> listQuestionAnswers = questionAnswerDao.findUserAnswerListByQids(questionIds);
        Map<Long, List<QuestionAnswer>> mapListQuestionAnswer = new HashMap<Long, List<QuestionAnswer>>();
        Long questionId;
        if (CollectionUtils.isNotEmpty(listQuestionAnswers)) {
            for (QuestionAnswer answers : listQuestionAnswers) {
                if (answers.getId() != null) {
                    questionId = ValidateUtils.isEmpty(answers.getPid()) ? answers.getQuestionId() : answers.getPid();
                    if (mapListQuestionAnswer.get(questionId) != null) {
                        List<QuestionAnswer> questionAnswerList = mapListQuestionAnswer.get(questionId);
                        questionAnswerList.add(answers);
                        mapListQuestionAnswer.put(questionId, questionAnswerList);
                    } else {
                        List<QuestionAnswer> questionAnswerList = new ArrayList<QuestionAnswer>();
                        questionAnswerList.add(answers);
                        mapListQuestionAnswer.put(questionId, questionAnswerList);
                    }

                }
            }
        }

        // 把问题和回复配对
        for (SolutionQuestion question : listQuestions) {
            if (question.getId() != null && mapListQuestionAnswer.get(question.getId()) != null) {
                question.setQuestionAnswerList(mapListQuestionAnswer.get(question.getId()));
            }
        }
        /*
        Map<Long, QuestionAnswer> mapListQuestionAnswer = new HashMap<Long, QuestionAnswer>();
        if (CollectionUtils.isNotEmpty(listQuestionAnswers)) {
            for (QuestionAnswer answers : listQuestionAnswers) {
                if (answers.getId() != null) {
                    mapListQuestionAnswer.put(answers.getQuestionId(), answers);
                }
            }
        }

        // 把问题和回复配对
        for (Question question : listQuestions) {
            if (question.getId() != null && mapListQuestionAnswer.get(question.getId()) != null) {
                question.setQuestionAnswer(mapListQuestionAnswer.get(question.getId()));
            }
        }
        */

        return listQuestions;
    }

    /**
     * 更新搜索索引
     * @param
     */
    /*private void addSolutionQuestionInfo(final HashMap<String, Object> searchMap) {
        executor.execute(new Runnable() {
            @Override
            public void run() {
                searchResource.addSolutionQuestionInfo(searchMap);
            }
        });
    }*/

    @Override
    public List<QuestionAnswer> getUserQuestionAnswerList(QuestionAnswerQuery query) throws DataAccessException {
        SolutionQuestion questionEntry = new SolutionQuestion();
        Date startTime=query.getStartTime();
        questionEntry.setUserId(query.getUid());
        questionEntry.setGoodsId(query.getGoodsId());
        //questionEntry.setProductId(query.getProductId());
        questionEntry.setCategoryId(query.getCategoryId());
        List<SolutionQuestion> questionList=dao.findList(questionEntry);
        if (CollectionUtils.isEmpty(questionList)) {
            return null;
        }else{
            List<Long> qidList=questionList.stream().map(SolutionQuestion::getId).collect(Collectors.toList());
            List<QuestionAnswer> listQuestionAnswers = questionAnswerDao.findUserAnswerListByQidListAndStartTime(qidList,startTime);
            return listQuestionAnswers;
        }
    }

    @Override
    public void changeQuestionAnswerType(Long uid, Long questionId, Integer answerType) throws DataAccessException, BusinessException {
        if (answerType == 2) {
            changeQuestionAnswerTypeToHuman(uid, questionId);
        } else {
            logger.error("暂不支持转AI回复");
        }
    }

    @Override
    public SolutionQuestionDTO getSolutionQuestionBriefById(Long id) throws DataAccessException {
        SolutionQuestion solutionQuestion = dao.getSolutionQuestionBriefByIdWithMaster(id);
        if (Objects.isNull(solutionQuestion)){
            return null;
        }
        SolutionQuestionDTO solutionQuestionDTO = new SolutionQuestionDTO();
        BeanUtils.copyProperties(solutionQuestion, solutionQuestionDTO);
        return solutionQuestionDTO;
    }

    private void changeQuestionAnswerTypeToHuman(Long uid, Long questionId) throws DataAccessException, BusinessException {
        SolutionQuestion question = null;
        try {
            question = dao.get(questionId);
        } catch (Exception e) {
            logger.error("获取答疑信息失败",e);
        }

        if (Objects.isNull(question)) {
            throw new BusinessException(Constants.SYS_ERROR, "答疑不存在");
        }

        if (Objects.isNull(question.getUserId()) || !Objects.equals(question.getUserId(), uid)) {
            throw new BusinessException(Constants.SYS_ERROR, "这不是您的问题！");
        }
        if (!Objects.equals(question.getStatus(),Consts.SOLUTION_QUESTION_STATUS.ANSWERED)) {
            throw new BusinessException(Constants.SYS_ERROR, "问题还没有回复，不能转人工！");
        }
        if (!Objects.equals(question.getIsAiAnswer(), Consts.SOLUTION_QUESTION_IS_AI_ANSWER.AI_ANSWERED)) {
            throw new BusinessException(Constants.SYS_ERROR, "问题不是AI回复！");
        }
        if (Objects.nonNull(question.getPid()) && question.getPid() > 0) {
            //父问题不为空时，要求父问题状态是已回复，该问题是追问中的最后一个
            HashMap<String, Long> qidMap = new HashMap<>();
            qidMap.put("pid", question.getPid());
            List<SolutionQuestion> brotherList = dao.findUserQuestionAnswerListByPid(qidMap);
            if (CollectionUtils.isEmpty(brotherList)) {
                throw new BusinessException(Constants.SYS_ERROR, "答疑不存在");
            } else {
                if (!Objects.equals(questionId,brotherList.get(0).getId())) {
                    throw new BusinessException(Constants.SYS_ERROR, "不是最后一次追问，无法转人工");
                }
            }
            SolutionQuestion parent = dao.get(question.getPid());
            if (Objects.isNull(parent)) {
                throw new BusinessException(Constants.SYS_ERROR, "父问题不存在");
            }
            if (!Objects.equals(parent.getStatus(), Consts.SOLUTION_QUESTION_STATUS.ANSWERED)) {
                throw new BusinessException(Constants.SYS_ERROR, "父问题不是已回复");
            }
            //子问题修改成未回复
            SolutionQuestion update = new SolutionQuestion();
            update.setUpdatedTime(System.currentTimeMillis() / 1000);
            update.setId(questionId);
            update.setIsAiAnswer(Consts.SOLUTION_QUESTION_IS_AI_ANSWER.CHANGE_AI_TO_MANUAL);
            update.setStatus(Consts.SOLUTION_QUESTION_STATUS.NO_ANSWER);
            update.setUserId(question.getUserId());
            update.setChangeAiToManualTime(new Date());
            try {
                dao.update(update);
                updateAiAnswerLog(questionId, Consts.SOLUTION_QUESTION_IS_AI_ANSWER.CHANGE_AI_TO_MANUAL,null,"学员手动转人工");
            } catch (DataAccessException e) {
                logger.error("update parent solution question catch exception, pid:{}", questionId, e);
            }
            //父问题修改成追问中
            SolutionQuestion parentUpdate = new SolutionQuestion();
            parentUpdate.setUpdatedTime(System.currentTimeMillis() / 1000);
            parentUpdate.setId(question.getPid());
            parentUpdate.setStatus(Consts.SOLUTION_QUESTION_STATUS.CROSS_QUESTION);
            parentUpdate.setUserId(question.getUserId());
            try {
                dao.update(parentUpdate);
            } catch (DataAccessException e) {
                logger.error("update parent solution question catch exception, pid:{}", question.getPid(), e);
            }
        } else {
            //问题修改成未回复
            SolutionQuestion update = new SolutionQuestion();
            update.setUpdatedTime(System.currentTimeMillis() / 1000);
            update.setId(questionId);
            update.setIsAiAnswer(Consts.SOLUTION_QUESTION_IS_AI_ANSWER.CHANGE_AI_TO_MANUAL);
            update.setStatus(Consts.SOLUTION_QUESTION_STATUS.NO_ANSWER);
            update.setUserId(question.getUserId());
            update.setChangeAiToManualTime(new Date());
            try {
                dao.update(update);
                updateAiAnswerLog(questionId, Consts.SOLUTION_QUESTION_IS_AI_ANSWER.CHANGE_AI_TO_MANUAL,null,null);
            } catch (DataAccessException e) {
                logger.error("update parent solution question catch exception, pid:{}", questionId, e);
            }
        }


    }

    @Override
    public SolutionQuestionDTO getSolutionQuestionByMessageId(String messageId) throws DataAccessException {
        SolutionQuestion solutionQuestion = dao.getSolutionQuestionByMessageIdWithMaster(messageId);
        if (Objects.isNull(solutionQuestion)){
            return null;
        }
        SolutionQuestionDTO solutionQuestionDTO = new SolutionQuestionDTO();
        BeanUtils.copyProperties(solutionQuestion, solutionQuestionDTO);
        return solutionQuestionDTO;
    }

    @Override
    public Long getSolutionQuestionCount(Long uid, Long categoryId) {
        SolutionQuestion questionEntry = new SolutionQuestion();
        questionEntry.setUserId(uid);
        questionEntry.setCategoryId(categoryId);
        int total = 0;
        try {
            total = dao.findListCount(new Page<>(), questionEntry);
        } catch (DataAccessException e) {
            logger.error("getSolutionQuestionCount catch exception, uid:{}, categoryId:{}", uid, categoryId, e);
        }
        return (long) total;
    }
}
