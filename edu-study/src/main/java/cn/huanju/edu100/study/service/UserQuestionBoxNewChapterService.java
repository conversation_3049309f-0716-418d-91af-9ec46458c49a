package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.model.ChapterSection;
import cn.huanju.edu100.study.model.Question;
import cn.huanju.edu100.study.model.TeachingBook;
import cn.huanju.edu100.study.model.UserGenerateExerciseAnswer;
import cn.huanju.edu100.study.model.questionBox.VirtualHomework;

import java.util.*;

public interface UserQuestionBoxNewChapterService {


	/**
	 * 根据策略随机抽取X道题目
	 * @param num
	 * @param randomType
	 * @param objType
	 * @param objId
	 * @param qTypes (在objType的前提下，增加题型的子过滤项)
	 * @param teachBookId
	 * @param boxId
	 * @param uid
	 * */
	VirtualHomework getRamdonBoxQuestionList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType,
											 List<Integer> qTypes,Integer randomType, Integer num) throws DataAccessException;

	/**
	 * 获取用户已经做过的题目
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * */
	List<Long> getUserAnswerBoxQuestionInfo(Long uid, Long boxId,
											Long teachBookId, Long objId, Integer objType)throws DataAccessException;

	/**
	 * 获取用户已经做过的题目
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objType
	 * */
	Map<Long, List<Long>> getUserAnswerBoxQuestionInfoBatch(Long uid, Long boxId,
                                                            Long teachBookId, List<Long> objIds, Integer objType)throws DataAccessException;

	/**
	 * 获取周用户已经做过的题目
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * @param reportWeekNum
	 * */
	List<Long> getWeekAnswerBoxQuestionInfo(Long uid, Long boxId,
											Long teachBookId, Long objId, Integer objType, Long reportWeekNum)throws DataAccessException;

	/**
	 * 获取用户做错的题目
	 * @param uid
	 * @param boxId
	 * @param objId
	 * @param objType
	 * */
	List<Long> getUserWrongBoxQuestionInfo(Long uid, Long boxId,
										   Long teachBookId,Long objId,Integer objType)throws DataAccessException;

	Map<Long, List<Long>> getUserWrongBoxQuestionInfoBatch(Long uid, Long boxId,
                                                           Long teachBookId, List<Long> objIds, Integer objType)throws DataAccessException;

	/**
	 * 获取周用户做错的题目
	 * @param uid
	 * @param boxId
	 * @param objId
	 * @param objType
	 * @param reportWeekNum
	 * */
	List<Long> getWeekWrongBoxQuestionInfo(Long uid, Long boxId,
										   Long teachBookId,Long objId,Integer objType, Long reportWeekNum)throws DataAccessException;

	/**
	 * 获取用户已消灭做错的题目
	 * @param uid
	 * @param boxId
	 * @param objId
	 * @param objType
	 * */
	List<Long> getWipeOutWrongBoxQuestionInfo(Long uid, Long boxId,
											  Long teachBookId,Long objId,Integer objType)throws DataAccessException;

	/**
	 * 用户错题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 * @param topicIdList 答错的子题id列表
	 *
	 * */
	void cacheUserBoxWrongQuestion(Long uid, Long homeworkId, Long teachBookId,
								   Long boxId, List<Long> questionIds, List<Long> topicIdList) throws DataAccessException;

	/**
	 * 周用户错题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 * @param reportWeekNum 答题报告周计数
	 * */
	public void cacheWeekWrongQuestion(Long uid, Long homeworkId, Long teachBookId,
									   Long boxId, List<Long> questionIds, Long reportWeekNum) throws DataAccessException;

	/**
	 * 用户做过的题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param teachBookId 教材id
	 * @param questionIds 题目id列表
	 *
	 * */
	void cacheUserBoxDoneQuestion(Long uid, Long homeworkId, Long teachBookId,
								  Long boxId, List<Long> questionIds) throws DataAccessException;

	/**
	 * 周用户做题情况入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param doneIds 做过题目id列表
	 * @param wrongIds 做错题目id列表
	 *
	 * */
	public void cacheWeekQuestion(Long uid, Long homeworkId, Long teachBookId,
								  Long boxId, List<Long> doneIds, List<Long> wrongIds) throws DataAccessException;

	/**
	 * 周用户做过的题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param teachBookId 教材id
	 * @param questionIds 题目id列表
	 * @param reportWeekNum 答题报告周计数
	 * */
	void cacheWeekDoneQuestion(Long uid, Long homeworkId, Long teachBookId,
							   Long boxId, List<Long> questionIds, Long reportWeekNum) throws DataAccessException;


	/**
	 * 刷新用户最近一次做过的题目（）
	 * @param uid
	 * @param boxId
	 * @param questions
	 *
	 * */
	void refreshUserLatestAnswerQId(Long uid, Long boxId, Collection<Question> questions) throws DataAccessException;

	/**
	 * 移除用户的错题
	 * @param uid
	 * @param boxId 题库id
	 * @param questionIds 需要移除的题目ids
	 * */
	void removeUserWrongQuestion(Long uid, Long boxId, List<Long> questionIds) throws DataAccessException;

	void removeUserWrongQuestion(Long uid, Long boxId, List<Long> questionIds, boolean updateWrong) throws DataAccessException;
	/**
	 * 移除用户已消灭的错题
	 * @param uid
	 * @param boxId 题库id
	 * @param questionIds 需要移除的题目ids
	 * */
	void removeWipeOutWrongQuestion(Long uid, Long boxId, List<Long> questionIds) throws DataAccessException;

	/**
	 * 自动生成练习
	 * */
	Long GenerateBoxExercise(UserGenerateExerciseAnswer userExerciseAnswer) throws DataAccessException;


    void getOriQuestionIds(Set<Long> oriQuestionIds, Long boxId, Long teachBookId, Long objId, Integer objType, StringBuffer homeworkName) throws DataAccessException;

    /**
	 * 根据条件获取题库内的题目
	 * @param boxId
	 * @param teachBookId
	 * @param objType (0：所有，1：章节，2：知识点)
	 * @param objId
	 *
	 * */
	List<Long> getBoxQuestionIds(Long boxId, Long teachBookId, Long objId,
								 Integer objType) throws DataAccessException;

	Map<Long, List<Long>> getBoxQuestionIdsBatch(Long boxId, Long teachBookId, List<Long> objIds,
                                                 Integer objType) throws DataAccessException;


	/**
	 * 根据条件取用户做过的题目列表
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * @return
	 * @throws DataAccessException
	 */
	List<Long> getUserDoneQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType) throws DataAccessException;

	List<Long> getUserWrongQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType) throws DataAccessException;


	void saveUserDoneQuestions(Long uid, Long boxId, HashMap<String, String> questionIdMap,List<Long> questionIds) throws DataAccessException ;
	String getUserDoneQuestions(Long uid, Long boxId, String key) throws DataAccessException ;

    Map<String, String> dealUserBoxDoneQuestion4Qtype(Long uid, Long boxId, List<Long> questionIds)throws DataAccessException;

    HashMap<String, String> dealDoneChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds);

	void saveUserWrongQuestions(Long uid, Long boxId, HashMap<String, String> questionIdMap) throws DataAccessException ;
	String getUserWrongQuestions(Long uid, Long boxId, String key) throws DataAccessException ;

    Map<String, String> dealUserBoxWrongQuestion4Qtype(Long uid,Long teachBookId, Long boxId, List<Long> questionIds, List<Long> wrongTopicIds)throws DataAccessException;

    Map<String, String> dealWrongChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds);

	String getUserWipeOutWrongQuestions(Long uid, Long boxId, String key) throws DataAccessException;

	Map<String,String> getUserWipeOutWrongQuestionsAll(Long uid, Long boxId) throws DataAccessException;

	void saveUserWipeOutWrongQuestions(Long uid, Long boxId, HashMap<String, String> questionIdMap);

	/**
    *
    * <AUTHOR>
    * @Description :获取用户未分类的错题
    * @Date :
    *
    */
    List<Long> getUserWrongBoxQuestionUncategorized(Long uid, Long boxId) throws DataAccessException;

    /**
    *
    * <AUTHOR>
    * @Description :获取用户未分类已消灭错题
    * @Date :
    *
    */
    List<Long> getWipeOutWrongBoxQuestionUncategorized(Long uid, Long boxId) throws DataAccessException;

    /**
    *
    * <AUTHOR>
    * @Description :获取用户错题列表(按照题型分类)
    * @Date :
    *
    */
    Map<Integer, List<Long>> getUserWrongBoxQuestionAccordingToQType(Long uid, Long boxId) throws DataAccessException;

    /**
    *
    * <AUTHOR>
    * @Description :获取用户已消灭错题列表(按照题型分类)
    * @Date :
    *
    */
    Map<Integer, List<Long>> getWipeOutWrongBoxQuestionAccordingToQType(Long uid, Long boxId) throws DataAccessException;

	/**
	 * 重置章节做题
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @return
	 * @throws DataAccessException
	 */
    Boolean resetNewChapterPractice(Long uid, Long boxId, Long teachBookId) throws DataAccessException;


	VirtualHomework saveVirtualHomework( Long boxId,
										 Long uid,
										 Long objId,
										 Integer objType,
										 Integer randomType,
										 Long teachBookId,
										 String homeworkName,
										 List<Long> resultQuestionIds) throws DataAccessException;


	void cachePaperDoneQuestion(Long uid, Long teachBookId, Long boxId, List<Long> questionIds) throws DataAccessException;

	void cachePaperWrongQuestion(Long uid, Long teachBookId, Long boxId, List<Long> questionIds, List<Long> topicIds) throws DataAccessException;

	Map<String, String> getPaperWrongQuestionMapByType(Long uid, Long teachBookId, Long boxId, List<Long> questionIds, int type) throws DataAccessException;

	TeachingBook getBookByCategoryId(Long categoryId);

	/**
	 * 同步学员指定题库的学习记录
	 * @param uid
	 * @return
	 * @throws DataAccessException
	 */
	boolean syncUserQuestionLog(Long uid, Long categoryId) throws DataAccessException;

	boolean isUserQuestionLogSync(Long uid, Long categoryId) throws DataAccessException;
}
