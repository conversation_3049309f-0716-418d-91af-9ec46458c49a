package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.SleepUser.SleepUserPushState;
import cn.huanju.edu100.study.service.sleepUser.SleepUserService;
import cn.huanju.edu100.study.util.SleepUserConsts;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import com.hqwx.study.dto.query.sleepUser.SleepUserPushStateQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;
@Component
public class SleepUserStateThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(SleepUserStateThriftImpl.class);
    private static Gson genericGson = GsonUtil.getGenericGson();
    @Autowired
    private SleepUserService sleepUserService;

    public response sty_updateSleepStudyState(request req) throws BusinessException {
        String entry = "sty_updateSleepStudyState";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            SleepUserPushStateQuery params = genericGson.fromJson(req.getMsg(), SleepUserPushStateQuery.class);


            if (Objects.isNull(params.getId()) || StringUtils.isEmpty(params.getSource())) {
                logger.error("{} fail.paramerter stateId or source  is null or empty.", entry);
                throw new BusinessException(Constants.PARAM_LOSE, "fail.paramerter stateId or source   is null or empty..");
            }
            SleepUserPushState sleepUserPushState = new SleepUserPushState();
            sleepUserPushState.setId(params.getId());
            if ("push".equals(params.getSource())) {
                sleepUserPushState.setState(SleepUserConsts.state.push_successed);
            } else if ("msg".equals(params.getSource())) {
                sleepUserPushState.setState(SleepUserConsts.state.msg_click);
            }

            sleepUserService.update(sleepUserPushState);
            res.setCode(Constants.SUCCESS);
        } catch (BusinessException e) {
            throw e;
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
