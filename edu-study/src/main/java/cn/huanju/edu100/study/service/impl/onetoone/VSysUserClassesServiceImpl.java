package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.VSysUserClassesDao;
import cn.huanju.edu100.study.model.onetoone.VSysUserClasses;
import cn.huanju.edu100.study.service.onetoone.VSysUserClassesService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 班级督导Service
 *
 * <AUTHOR>
 * @version 2016-12-16
 */
@Service
public class VSysUserClassesServiceImpl extends BaseServiceImpl<VSysUserClassesDao, VSysUserClasses> implements
        VSysUserClassesService {

    @Override
    public Map<Long, VSysUserClasses> getClsId2VSysUserMap(List<Long> clsIds) throws DataAccessException {

        if (CollectionUtils.isEmpty(clsIds)) {
            logger.error("getClsId2VSysUserMap fail, clsIds is empty");
            return Collections.emptyMap();
        }

        List<VSysUserClasses> vSysUserClassesList = findListByParam(clsIds);
        if (CollectionUtils.isNotEmpty(vSysUserClassesList)) {
            Map<Long, VSysUserClasses> map = Maps.newHashMap();
            for (VSysUserClasses vSysUserClasses : vSysUserClassesList) {
                map.put(vSysUserClasses.getvClsId(), vSysUserClasses);
            }

            return map;
        } else {
            return Collections.emptyMap();
        }
    }

    @Override
    public List<VSysUserClasses> findListByParam(List<Long> clsIds) throws DataAccessException {

        if (CollectionUtils.isEmpty(clsIds)) {
            logger.error("findListByParam fail, clsIds is empty");
            return Collections.emptyList();
        }

        Map<String, Object> param = Maps.newHashMap();
        param.put("clsIds", clsIds);
        return dao.findListByParam(param);
    }

}
