package cn.huanju.edu100.study.service.solution;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.PageModel;
import cn.huanju.edu100.study.model.solution.SolutionQuestion;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.model.solution.QuestionAnswer;
import com.hqwx.study.dto.SolutionQuestionDTO;
import com.hqwx.study.dto.command.AiStreamAnswerCompleteCommand;
import com.hqwx.study.dto.command.SolutionQuestionAddCommand;
import com.hqwx.study.dto.command.SolutionQuestionToManualCmd;
import com.hqwx.study.dto.query.QuestionAnswerQuery;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public interface SolutionQuestionService {
    PageModel getUserQuestionList(SolutionQuestion questionEntry, Integer from, Integer rows) throws DataAccessException;
    SolutionQuestion getQuestionInfoById(Long questionId, Long uid) throws DataAccessException;
    List<SolutionQuestion> getQuestionListByIds(ArrayList<Long> questionIds) throws DataAccessException;
    List<SolutionQuestion> getQuestionListByIdsOrderById(ArrayList<Long> questionIds) throws DataAccessException;
    Long insertQuestionInfo(HashMap<String, Object> entryMap) throws DataAccessException;
    PageModel getUserHotQuestionList(HashMap<String, Object> queryParam) throws DataAccessException;
    List<QuestionAnswer> getUserQuestionAnswerList(QuestionAnswerQuery query) throws DataAccessException;

    void changeQuestionAnswerType(Long uid, Long questionId, Integer answerType) throws DataAccessException, BusinessException;

    SolutionQuestionDTO getSolutionQuestionBriefById(Long id) throws DataAccessException;

    void aiAnswerComplete(AiStreamAnswerCompleteCommand params) throws DataAccessException;

    boolean deleteSolutionAnswerById(Long id) throws DataAccessException;

    Long addSolutionQuestion(SolutionQuestionAddCommand params) throws DataAccessException;

    boolean solutionQuestionToManual(SolutionQuestionToManualCmd params) throws DataAccessException, BusinessException;

    SolutionQuestionDTO getSolutionQuestionByMessageId(String messageId) throws DataAccessException;

    Long getSolutionQuestionCount(Long uid, Long categoryId);
}
