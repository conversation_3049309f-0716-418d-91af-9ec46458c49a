package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.QuestionAnswerDetail;
import cn.huanju.edu100.study.model.QuestionAnswerStatics;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2015-05-14
 */

public interface QuestionAnswerStaticsService extends BaseService<QuestionAnswerStatics> {
    QuestionAnswerStatics staticQuestionAnswer(long questionId) throws DataAccessException;

    Map<Long,QuestionAnswerStatics> staticQuestionAnswerBatch(Collection<Long> questionIds)
			throws DataAccessException;

	List<QuestionAnswerDetail> getQuestionAnswerDetail2SyncDB(int batchNum,
			long period)throws DataAccessException;

	void syncRecordQuestionAnswerInfo(Long uid, Long questionId,
			Date answerDate, Integer state) throws DataAccessException;

	void recordQuestionAnswerInfoBatch(
			List<QuestionAnswerDetail> questionAnswerDetailList)
			throws DataAccessException;

	void drainToAllElement(List<QuestionAnswerDetail> data)
			throws DataAccessException;

	public Integer addQuestionAnswerDetailBatch(List<QuestionAnswerDetail> questionAnswerDetail)throws DataAccessException;
}
