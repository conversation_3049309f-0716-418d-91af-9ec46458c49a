package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.model.question.WeekAnswerStat;
import cn.huanju.edu100.study.service.UserQBoxRmErrQuestionService;
import cn.huanju.edu100.study.service.UserQuestionBoxNewChapterService;
import cn.huanju.edu100.study.service.UserQuestionBoxService;
import cn.huanju.edu100.study.service.question.WeekAnswerStatService;
import cn.huanju.edu100.study.util.CalUtils;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户周答题统计
 * Created by caijunhua on 2018/6/12.
 */
@Component
public class WeekAnswerStatThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(WeekAnswerStatThriftImpl.class);
    private static Gson gson = GsonUtil.getGson();
    @Autowired
    private WeekAnswerStatService weekAnswerStatService;
    @Autowired
    private UserQuestionBoxService userQuestionBoxService;
    @Autowired
    private UserQBoxRmErrQuestionService userQBoxRmErrQuestionService;
    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;
    @Autowired
    private UserQuestionBoxNewChapterService userQuestionBoxNewChapterService;

    private static Long expTime = 15*24*60*60L;//15天过期时间

    /**
     * 获取用户上周答题信息
     * @param req
     * @return response
     * @throws BusinessException
     */
    public response sty_getAnswerInfoLastWeek(request req) throws BusinessException {
        String entry = "sty_getAnswerInfoLastWeek";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("box_id") == null
                    || param.get("teach_book_id") == null || param.get("uid") == null) {
                logger.error("{} fail.parameter box_id or teach_book_id or uid is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter box_id or teach_book_id or uid is null.");
            }

            long boxId = param.get("box_id").longValue();
            long teachBookId = param.get("teach_book_id").longValue();
            long uid = param.get("uid").longValue();

            //获取答题报告周计数
            Map<String, String> map = getReportWeekValue(entry);
            /*tiku 4.9 当前周*/
            Long reportWeekNum = Long.parseLong(map.get("reportWeekNum"));
            //获取用户周答题排名key
            String reportWeekListKey = RedisConsts.REPORT_WEEK_LIST_KEY + reportWeekNum + "_" + boxId;
            //答题总人数
            Long totalHeadCount = 0L;

            WeekAnswerStat query = new WeekAnswerStat();
            query.setBoxId(boxId);
//            query.setTeachBookId(teachBookId);
            query.setUid(uid);
            query.setWeekNum(reportWeekNum);
            List<WeekAnswerStat> weekAnswerStatList = weekAnswerStatService.findList(query);
            WeekAnswerStat weekAnswerStat = new WeekAnswerStat(boxId, 0L, uid, reportWeekNum, 0L, 0L, 0L, 0D);

            //答题总人数
            totalHeadCount = compatableRedisClusterClient.zcard(reportWeekListKey);

            //全站最高答题量
            String reportWeekMaxAnswerKey = RedisConsts.REPORT_WEEK_MAX_ANSWER_KEY + reportWeekNum + "_" + boxId;
            String maxAnswerNumStr = compatableRedisClusterClient.get(reportWeekMaxAnswerKey);

            if(!CollectionUtils.isEmpty(weekAnswerStatList)) {
                weekAnswerStat = weekAnswerStatList.get(0);

                Double score = compatableRedisClusterClient.zscore(reportWeekListKey, uid+"");
                //设置排名字段
                if (score == null){ //如果分数不存在补充数据
                    //记录是否有需要补充的
                    logger.info("sty_getAnswerInfoLastWeek reportWeekListKey:{},uid:{}",reportWeekListKey, uid);
                    //计算分数
                    Double answerScore = CalUtils.mul(weekAnswerStat.getAnswerNum(), 1000);
                    Double accuracyScore = CalUtils.mul(weekAnswerStat.getAccuracy(), 100);
                    score = CalUtils.add(answerScore, accuracyScore);
                    Long zret = compatableRedisClusterClient.zadd(reportWeekListKey, score, uid+"");
                    Long expret = compatableRedisClusterClient.expire(reportWeekListKey, expTime.intValue());
                    logger.info("sty_getAnswerInfoLastWeek zadd reportWeekListKey:{},uid:{},score:{},zret:{},expret:{}", reportWeekListKey, uid, score, zret, expret);
                }
                Long ranking = compatableRedisClusterClient.zrevrank(reportWeekListKey, uid+"");
                if(null != ranking) {
                    weekAnswerStat.setRanking(ranking+1);
                }

                //已击败考友百分比
                if((null != ranking && ranking > 0) && (null != totalHeadCount && totalHeadCount > 0) ) { //排名大于第一名，100%-排名/总人数
                    Double defeatDiv = CalUtils.div(ranking+1, totalHeadCount, CalUtils.DEF_DIV_SCALE);
                    Double defeat = CalUtils.subtract(1, defeatDiv);
                    weekAnswerStat.setDefeat(defeat);
                } else {
                    weekAnswerStat.setDefeat(1D); //第一名，击败100%
                }
            }

            weekAnswerStat.setTotalHeadCount(totalHeadCount);
            //空值判断
            if(null != maxAnswerNumStr && !"".equals(maxAnswerNumStr)) {
                Long maxAnswerNum = Long.parseLong(maxAnswerNumStr);
                weekAnswerStat.setMaxAnswerNum(maxAnswerNum);
            }
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.toJson(weekAnswerStat, req.getAppid()));
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 获取用户周答题趋势
     * @param req
     * @return response
     * @throws BusinessException
     */
    public response sty_getAnswerTrendWeek(request req) throws BusinessException {
        String entry = "sty_getAnswerTrendWeek";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("box_id") == null
                    || param.get("teach_book_id") == null || param.get("uid") == null) {
                logger.error("{} fail.parameter box_id or teach_book_id or uid is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter box_id or teach_book_id or uid is null.");
            }

            long boxId = param.get("box_id").longValue();
            long teachBookId = param.get("teach_book_id").longValue();
            long uid = param.get("uid").longValue();

            //获取答题报告周计数
            Map<String, String> map = getReportWeekValue(entry);
            //Long reportWeekNum = Long.parseLong(map.get("reportWeekNum")); //测试本周情况使用
            Long reportWeekNum = Long.parseLong(map.get("reportWeekNum")) - 1; //正式查上一周
            //获取最近8周的数据
            Long startWeekNum = 0L;
            Long endWeekNum = reportWeekNum;
            if(endWeekNum > 8) {
                startWeekNum = endWeekNum - 8;
            }

            List<WeekAnswerStat> weekAnswerStatList = weekAnswerStatService.findByWeekNum(boxId, null, uid, startWeekNum, endWeekNum);

            if(CollectionUtils.isEmpty(weekAnswerStatList)) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getAnswerTrendWeek return null");
            } else {
                Map<String, Object> resMap = new HashMap<String, Object>();
                resMap.put("current_cal_week_num", reportWeekNum); //当前计数周次
                resMap.put("weekAnswerList", weekAnswerStatList);
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(resMap, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * (批量)获取用户上周已做错的题目总数
     * @param req
     * uid
     * boxId
     * teachBookId
     * objType(章节1/知识点2)
     * objIds(对应的业务ids)
     * @desc 当objType!=0时，校验boxId是否可用，此时objIds即为业务ids(章节ids,知识点ids)
     * */
    public response sty_batchWeekWrongBoxQuestionCnt(request req) throws BusinessException{
        String entry = "sty_batchWeekWrongBoxQuestionCnt";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || Double.valueOf(param.get("uid").toString())<=0
                    || param.get("obj_type") == null || Double.valueOf(param.get("obj_type").toString())<0
                    || param.get("teach_book_id") == null || param.get("obj_ids") == null ) {
                logger.error("{} sty_batchWeekWrongBoxQuestionCnt paramter lose, uid or box_id or obj_ids or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_batchWeekWrongBoxQuestionCnt paramter lose, uid or box_id or obj_ids or teach_book_id is null.");
            }else {
                Long uid = Double.valueOf(param.get("uid").toString()).longValue() ;
                Long boxId = Double.valueOf(param.get("box_id").toString()).longValue();
                Long teachBookId = Double.valueOf(param.get("teach_book_id").toString()).longValue();
                //（1：章节，2：知识点）
                Integer objType = Double.valueOf(param.get("obj_type").toString()).intValue();
                List<Long> objIds = gson.fromJson(param.get("obj_ids").toString(), new TypeToken<List<Long>>(){}.getType());

                if (objIds.size() > Constants.MAX_REQUEST) {
                    throw new BusinessException(Constants.PARAM_INVALID, "sty_batchWeekWrongBoxQuestionCnt paramter invalid, obj_ids is too much, the limit num is "+Constants.MAX_REQUEST);
                }else {
                    Map<Long, Object> result = new HashMap<Long, Object>();

                    //获取答题报告周计数
                    Map<String, String> map = getReportWeekValue(entry);
//                  Long reportWeekNum = Long.parseLong(map.get("reportWeekNum")); //测试本周情况使用
                    Long reportWeekNum = Long.parseLong(map.get("reportWeekNum")) - 1; //正式查上一周

                    for (Long objId : objIds) {
                        Long boxIdOri = null;
                        if (objType.intValue() == 0) {
                            boxIdOri = objId;
                        }else {
                            boxIdOri = boxId;
                        }

                        List<Long> userWrongQuestionList = userQuestionBoxService.getWeekWrongBoxQuestionInfo(uid, boxIdOri, teachBookId, objId, objType, reportWeekNum);

                        Integer objTypeOri = objType == 3 ? 0 : objType;//这里如果是按照题型来取的，就直接拿所有id去过滤就行了
                        List<Long> boxQuestionList = userQuestionBoxService.getBoxQuestionIds(boxIdOri, teachBookId, objId, objTypeOri);

//						//将当前总集合和用户的错题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户错题数的问题）
                        List<Long> oriUserWrongList = new ArrayList<Long>();
                        if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
                            oriUserWrongList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, userWrongQuestionList);
                        }else {
                            oriUserWrongList.addAll(userWrongQuestionList);
                        }

                        if (oriUserWrongList != null && oriUserWrongList.size()>0) {
                            result.put(objId, oriUserWrongList.size());
                        }else {
                            result.put(objId, 0);
                        }
                    }

                    if (!result.isEmpty()) {
                        res.setCode(Constants.SUCCESS);
                        res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                    }else {
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("sty_batchWeekWrongBoxQuestionCnt return null");
                    }
                }
            }
        }catch(BusinessException e){
            res = dataBusinessException(entry, req, e);
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }



    /**
     * (批量)获取用户上周已做过的题目总数
     * @param req
     * uid
     * boxId
     * teachBookId
     * objType(章节1/知识点2)
     * objIds(对应的业务ids)
     * @desc 当objType!=0时，校验boxId是否可用，此时objIds即为业务ids(章节ids,知识点ids)
     * */
    public response sty_batchWeekAnswerBoxQuestionCnt(request req) throws BusinessException{
        String entry = "sty_batchWeekAnswerBoxQuestionCnt";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || Double.valueOf(param.get("uid").toString())<=0
                    || param.get("obj_type") == null || Double.valueOf(param.get("obj_type").toString())<0
                    || param.get("teach_book_id") == null || param.get("obj_ids") == null ) {
                logger.error("{} sty_batchWeekAnswerBoxQuestionCnt paramter lose, uid or box_id or obj_ids or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_batchWeekAnswerBoxQuestionCnt paramter lose, uid or box_id or obj_ids or teach_book_id is null.");
            }else {
                Long uid = Double.valueOf(param.get("uid").toString()).longValue() ;
                Long boxId = Double.valueOf(param.get("box_id").toString()).longValue();
                Long teachBookId = Double.valueOf(param.get("teach_book_id").toString()).longValue();
                //（1：章节，2：知识点）
                Integer objType = Double.valueOf(param.get("obj_type").toString()).intValue();
                List<Long> objIds = gson.fromJson(param.get("obj_ids").toString(), new TypeToken<List<Long>>(){}.getType());

                if (objIds.size() > Constants.MAX_REQUEST) {
                    throw new BusinessException(Constants.PARAM_INVALID, "sty_batchWeekAnswerBoxQuestionCnt paramter invalid, obj_ids is too much, the limit num is "+Constants.MAX_REQUEST);
                }else {
                    Map<Long, Object> result = new HashMap<Long, Object>();

                    //获取答题报告周计数
                    Map<String, String> map = getReportWeekValue(entry);
//                  Long reportWeekNum = Long.parseLong(map.get("reportWeekNum")); //测试本周情况使用
                    Long reportWeekNum = Long.parseLong(map.get("reportWeekNum")) - 1; //正式查上一周

                    for (Long objId : objIds) {
                        Long boxIdOri = null;
                        if (objType.intValue() == 0) {
                            boxIdOri = objId;
                        }else {
                            boxIdOri = boxId;
                        }

                        List<Long> userDoneQuestionList = userQuestionBoxService.getWeekAnswerBoxQuestionInfo(uid, boxIdOri, teachBookId, objId, objType, reportWeekNum);
                        Integer objTypeOri = objType == 3 ? 0 : objType;//这里如果是按照题型来取的，就直接拿所有id去过滤就行了
                        List<Long> boxQuestionList = userQuestionBoxService.getBoxQuestionIds(boxIdOri, teachBookId, objId, objTypeOri);

                        //将当前总集合和用户的已做题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户已做题数的问题）
                        List<Long> oriUserDoneList = new ArrayList<Long>();
                        if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
                            oriUserDoneList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, userDoneQuestionList);
                        }else {
                            oriUserDoneList.addAll(userDoneQuestionList);
                        }

                        if (oriUserDoneList != null && oriUserDoneList.size()>0) {
                            result.put(objId, oriUserDoneList.size());
                        }
                    }

                    if (!result.isEmpty()) {
                        res.setCode(Constants.SUCCESS);
                        res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                    }else {
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("sty_batchWeekAnswerBoxQuestionCnt return null");
                    }
                }
            }
        }catch(BusinessException e){
            res = dataBusinessException(entry, req, e);
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }

    /**
     * 获取用户上周做错的题目(wrong)
     * @param req
     * uid
     * boxId
     * teach_book_id
     * objType(章节/知识点)
     * objId(对应的业务id)
     * */
    public response sty_getWeekWrongBoxQuestionInfo(request req) throws BusinessException{
        String entry = "sty_getWeekWrongBoxQuestionInfo";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Double> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || param.get("uid")<=0
                    || param.get("box_id") == null || param.get("box_id")<=0
                    || param.get("obj_type") == null || param.get("obj_type")<0
                    || param.get("teach_book_id") == null || param.get("teach_book_id") <= 0
                    || param.get("obj_id") == null ) {
                logger.error("{} sty_getWeekWrongBoxQuestionInfo paramter lose, uid or box_id or obj_id or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getWeekWrongBoxQuestionInfo paramter lose, uid or box_id or obj_id or teach_book_id is null.");
            }else {
                Long uid = param.get("uid").longValue() ;
                Long boxId = param.get("box_id").longValue() ;
                Long teachBookId = param.get("teach_book_id").longValue();
                //（1：章节，2：知识点）
                Integer objType = param.get("obj_type").intValue();
                Long objId = param.get("obj_id").longValue();

                //获取答题报告周计数
                Map<String, String> map = getReportWeekValue(entry);
//              Long reportWeekNum = Long.parseLong(map.get("reportWeekNum")); //测试本周情况使用
                Long reportWeekNum = Long.parseLong(map.get("reportWeekNum")) - 1; //正式查上一周

                List<Long> userWrongQuestionList = userQuestionBoxService.getWeekWrongBoxQuestionInfo(uid, boxId, teachBookId, objId, objType, reportWeekNum);

                Integer objTypeOri = objType == 3 ? 0 : objType;//这里如果是按照题型来取的，就直接拿所有id去过滤就行了
                List<Long> boxQuestionList = userQuestionBoxService.getBoxQuestionIds(boxId, teachBookId, objId, objTypeOri);

                //将当前总集合和用户的错题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户错题数的问题）
                List<Long> oriUserWrongList = new ArrayList<Long>();
                if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
                    oriUserWrongList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, userWrongQuestionList);
                }else {
                    oriUserWrongList.addAll(userWrongQuestionList);
                }

                if (oriUserWrongList != null && oriUserWrongList.size()>0) {
                    Map<String, Object> result = new HashMap<String, Object>();

                    result.put("question_ids", oriUserWrongList);
                    result.put("total", oriUserWrongList.size());
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_getWeekWrongBoxQuestionInfo return null");
                }
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }

    /**
     * (批量)获取用户已消灭错的题目总数
     * @param req
     * uid
     * boxId
     * teachBookId
     * objType(章节1/知识点2)
     * objIds(对应的业务ids)
     * @desc 当objType!=0时，校验boxId是否可用，此时objIds即为业务ids(章节ids,知识点ids)
     * */
    public response sty_batchWipeOutWrongBoxQuestionCnt(request req) throws BusinessException{
        String entry = "sty_batchWipeOutWrongBoxQuestionCnt";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || Double.valueOf(param.get("uid").toString())<=0
                    || param.get("obj_type") == null || Double.valueOf(param.get("obj_type").toString())<0
                    || param.get("teach_book_id") == null || param.get("obj_ids") == null ) {
                logger.error("{} sty_batchWipeOutWrongBoxQuestionCnt paramter lose, uid or box_id or obj_ids or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_batchWipeOutWrongBoxQuestionCnt paramter lose, uid or box_id or obj_ids or teach_book_id is null.");
            }else {
                Long uid = Double.valueOf(param.get("uid").toString()).longValue() ;
                Long boxId = Double.valueOf(param.get("box_id").toString()).longValue();
                Long teachBookId = Double.valueOf(param.get("teach_book_id").toString()).longValue();
                //（1：章节，2：知识点）
                Integer objType = Double.valueOf(param.get("obj_type").toString()).intValue();
                List<Long> objIds = gson.fromJson(param.get("obj_ids").toString(), new TypeToken<List<Long>>(){}.getType());

                if (objIds.size() > Constants.MAX_REQUEST) {
                    throw new BusinessException(Constants.PARAM_INVALID, "sty_batchWipeOutWrongBoxQuestionCnt paramter invalid, obj_ids is too much, the limit num is "+Constants.MAX_REQUEST);
                }else {
                    Map<Long, Object> result = new HashMap<Long, Object>();

//                    logger.info("sty_batchWipeOutWrongBoxQuestionCnt start:uid:{},boxId:{},teachBookId:{},objType:{},objIds:{}", uid, boxId, teachBookId, objType, GsonUtil.toJson(objIds));
                    for (Long objId : objIds) {
                        Long boxIdOri = null;
                        if (objType.intValue() == 0) {
                            boxIdOri = objId;
                        }else {
                            boxIdOri = boxId;
                        }
//                        logger.info("sty_batchWipeOutWrongBoxQuestionCnt for:uid:{},boxId:{},teachBookId:{},objType:{},objId:{}", uid, boxId, teachBookId, objType, objId);
                        List<Long> wipeOutWrongQuestionList = userQuestionBoxService.getWipeOutWrongBoxQuestionInfo(uid, boxIdOri, teachBookId, objId, objType);
//                        logger.info("sty_batchWipeOutWrongBoxQuestionCnt getlist:uid:{},boxId:{},teachBookId:{},objType:{},objId:{},wipeOutWrongQuestionList:{}",
//                                uid, boxId, teachBookId, objType, objId, GsonUtil.toJson(wipeOutWrongQuestionList));

                        Integer objTypeOri = objType == 3 ? 0 : objType;//这里如果是按照题型来取的，就直接拿所有id去过滤就行了
                        List<Long> boxQuestionList = userQuestionBoxService.getBoxQuestionIds(boxIdOri, teachBookId, objId, objTypeOri);

//						//将当前总集合和用户的错题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户错题数的问题）
                        List<Long> oriUserWrongList = new ArrayList<Long>();
                        if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
//                            logger.info("sty_batchWipeOutWrongBoxQuestionCnt boxQuestionList data:uid:{},boxId:{},teachBookId:{},objType:{},objId:{},objTypeOri:{},boxQuestionList:{}",
//                                    uid, boxId, teachBookId, objType, objId, objTypeOri, GsonUtil.toJson(boxQuestionList));
                            oriUserWrongList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, wipeOutWrongQuestionList);
                        }else {
                            oriUserWrongList.addAll(wipeOutWrongQuestionList);
                        }

                        if (oriUserWrongList != null && oriUserWrongList.size()>0) {
//                            logger.info("sty_batchWipeOutWrongBoxQuestionCnt oriUserWrongList:uid:{},boxId:{},teachBookId:{},objType:{},objId:{},objTypeOri:{},oriUserWrongList:{}",
//                                    uid, boxId, teachBookId, objType, objId, objTypeOri, GsonUtil.toJson(oriUserWrongList));
                            result.put(objId, oriUserWrongList.size());
                        }else {
                            result.put(objId, 0);
                        }
                    }
//                    logger.info("sty_batchWipeOutWrongBoxQuestionCnt result:uid:{},boxId:{},teachBookId:{},objType:{},result:{}",
//                            uid, boxId, teachBookId, objType, GsonUtil.toJson(result));

                    if (!result.isEmpty()) {
                        res.setCode(Constants.SUCCESS);
                        res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                    }else {
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("sty_batchWipeOutWrongBoxQuestionCnt return null");
                    }
                }
            }
        }catch(BusinessException e){
            res = dataBusinessException(entry, req, e);
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }

    /**
     * 获取用户已消灭做错的题目(wipeOut)
     * @param req
     * uid
     * boxId
     * teach_book_id
     * objType(章节/知识点)
     * objId(对应的业务id)
     * */
    public response sty_getWipeOutWrongBoxQuestionInfo(request req) throws BusinessException{
        String entry = "sty_getWipeOutWrongBoxQuestionInfo";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Double> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || param.get("uid")<=0
                    || param.get("box_id") == null || param.get("box_id")<=0
                    || param.get("obj_type") == null || param.get("obj_type")<0
                    || param.get("teach_book_id") == null || param.get("teach_book_id") <= 0
                    || param.get("obj_id") == null ) {
                logger.error("{} sty_getWipeOutWrongBoxQuestionInfo paramter lose, uid or box_id or obj_id or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getWipeOutWrongBoxQuestionInfo paramter lose, uid or box_id or obj_id or teach_book_id is null.");
            }else {
                Long uid = param.get("uid").longValue() ;
                Long boxId = param.get("box_id").longValue() ;
                Long teachBookId = param.get("teach_book_id").longValue();
                //（1：章节，2：知识点）
                Integer objType = param.get("obj_type").intValue();
                Long objId = param.get("obj_id").longValue();

                List<Long> userWrongQuestionList = userQuestionBoxService.getWipeOutWrongBoxQuestionInfo(uid, boxId, teachBookId, objId, objType);

                Integer objTypeOri = objType == 3 ? 0 : objType;//这里如果是按照题型来取的，就直接拿所有id去过滤就行了
                List<Long> boxQuestionList = userQuestionBoxService.getBoxQuestionIds(boxId, teachBookId, objId, objTypeOri);

                //将当前总集合和用户的错题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户错题数的问题）
                List<Long> oriUserWrongList = new ArrayList<Long>();
                if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
                    oriUserWrongList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, userWrongQuestionList);
                }else {
                    oriUserWrongList.addAll(userWrongQuestionList);
                }

                if (oriUserWrongList != null && oriUserWrongList.size()>0) {
                    Map<String, Object> result = new HashMap<String, Object>();

                    result.put("question_ids", oriUserWrongList);
                    result.put("total", oriUserWrongList.size());
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_getWipeOutWrongBoxQuestionInfo return null");
                }
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }

    /**
     * 用户对题库已消灭错题进行主动移除(批量obj_ids+单个question_id)
     * @param req
     * uid
     * box_id
     * teach_book_id
     * obj_ids (obj_type为0时，obj_id可不传)
     * obj_type (1：章节，2：知识点)--->不管是从章节还是从知识点上移除，都需要移除掉对应题目类型上的错题。
     * question_id (需要移除的题目Id集合，集合中的题目Id若不存在于当前的obj_id中的话，则不进行移除。)
     * */
    @Deprecated
    public response sty_rmErrWipeOutQuestionInBoxBatchObj(request req) throws BusinessException{
        String entry = "sty_rmErrWipeOutQuestionInBoxBatchObj";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || (Double)param.get("uid")<=0
                    || param.get("box_id") == null || (Double)param.get("box_id")<=0
                    || param.get("teach_book_id") == null || (Double)param.get("teach_book_id")<=0
                    || param.get("obj_type") == null || (Double)param.get("obj_type")<=0 || param.get("obj_ids") == null
                    || param.get("question_id") == null || (Double)param.get("question_id")<=0 ) {
                logger.error("{} sty_rmErrWipeOutQuestionInBoxBatchObj paramter lose, uid or box_id or book_id or obj_ids or obj_type or question_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_rmErrWipeOutQuestionInBoxBatchObj paramter lose, uid or box_id or book_id or obj_ids or obj_type or question_id is null.");
            }else {
                Long uid = ((Double)param.get("uid")).longValue() ;
                Long boxId = ((Double)param.get("box_id")).longValue() ;
                Long bookId = ((Double)param.get("teach_book_id")).longValue() ;
                Integer objType = ((Double)param.get("obj_type")).intValue();
                Long questionId = ((Double)param.get("question_id")).longValue();

                List<Long> objIds = new ArrayList<Long>();
                if (param.get("obj_ids") != null) {
                    objIds = gson.fromJson(param.get("obj_ids").toString(), new TypeToken<List<Long>>(){}.getType());
                }
                if (objIds.isEmpty()) {
                    res.setCode(Constants.PARAM_LOSE);
                    res.setErrormsg("sty_rmErrWipeOutQuestionInBoxBatchObj paramter lose, obj_ids is null.");
                }else if (objIds.size() > 30) {//这里需要限制一下每次移除的obj_id数量
                    res.setCode(Constants.PARAM_INVALID);
                    res.setErrormsg("sty_rmErrWipeOutQuestionInBoxBatchObj paramter invalid, obj_ids is too big. max size is 30!");
                }else {
                    userQBoxRmErrQuestionService.updateWipeOutRmErrQuestionLogBatchObj(uid,boxId,bookId,objIds,objType,questionId);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg("userRemoveErrQuestionInBox success!");
                }
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }



    /**
     * 题库5.0 用户对题库已消灭错题进行主动移除(批量obj_ids+单个question_id)
     * @param req
     * uid
     * box_id
     * teach_book_id
     * obj_ids (obj_type为0时，obj_id可不传)
     * obj_type (1：章节，2：知识点)--->不管是从章节还是从知识点上移除，都需要移除掉对应题目类型上的错题。
     * question_id (需要移除的题目Id集合，集合中的题目Id若不存在于当前的obj_id中的话，则不进行移除。)
     * */
    public response sty_rmErrWipeOutQuestionInBoxBatchObjNew(request req) throws BusinessException{
        String entry = "sty_rmErrWipeOutQuestionInBoxBatchObjNew";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || (Double)param.get("uid")<=0
                    || param.get("box_id") == null || (Double)param.get("box_id")<=0
                    || param.get("teach_book_id") == null || (Double)param.get("teach_book_id")<=0
                    || param.get("obj_type") == null || (Double)param.get("obj_type")<=0 || param.get("obj_ids") == null
                    || param.get("question_id") == null || (Double)param.get("question_id")<=0 ) {
                logger.error("{} sty_rmErrWipeOutQuestionInBoxBatchObjNew paramter lose, uid or box_id or book_id or obj_ids or obj_type or question_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_rmErrWipeOutQuestionInBoxBatchObjNew paramter lose, uid or box_id or book_id or obj_ids or obj_type or question_id is null.");
            }else {
                Long uid = ((Double)param.get("uid")).longValue() ;
                Long boxId = ((Double)param.get("box_id")).longValue() ;
                Long bookId = ((Double)param.get("teach_book_id")).longValue() ;
                Integer objType = ((Double)param.get("obj_type")).intValue();
                Long questionId = ((Double)param.get("question_id")).longValue();

                List<Long> objIds = new ArrayList<Long>();
                if (param.get("obj_ids") != null) {
                    objIds = gson.fromJson(param.get("obj_ids").toString(), new TypeToken<List<Long>>(){}.getType());
                }
                if (objIds.isEmpty()) {
                    res.setCode(Constants.PARAM_LOSE);
                    res.setErrormsg("sty_rmErrWipeOutQuestionInBoxBatchObjNew paramter lose, obj_ids is null.");
                }else if (objIds.size() > 30) {//这里需要限制一下每次移除的obj_id数量
                    res.setCode(Constants.PARAM_INVALID);
                    res.setErrormsg("sty_rmErrWipeOutQuestionInBoxBatchObjNew paramter invalid, obj_ids is too big. max size is 30!");
                }else {
                    userQBoxRmErrQuestionService.updateNewWipeOutRmErrQuestionLogBatchObj(uid,boxId,bookId,objIds,objType,questionId);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg("sty_rmErrWipeOutQuestionInBoxBatchObjNew success!");
                }
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 获取答题报告周计数
     * @param entry
     * @return
     * @throws DataAccessException
     * @throws BusinessException
     */
    public Map<String, String> getReportWeekValue(String entry) throws DataAccessException, BusinessException {
        String reportWeekValue = weekAnswerStatService.getReportWeekValue();

        if(null == reportWeekValue || "".equals(reportWeekValue)) {
            logger.error("{} fail.redis.parameter reportWeekValue is null.", entry);
            throw new BusinessException(Constants.PARAM_INVALID, "redis.paramerter reportWeekValue is null.");
        }

        Map<String, String> map = new HashMap<String, String>();
        String weekMonday = reportWeekValue.split(",")[1];
        String numStr = reportWeekValue.split(",")[0];
        map.put("weekMonday", weekMonday);
        map.put("reportWeekNum", numStr);
        return map;
    }

    /**
    *
    * <AUTHOR> duxiulei
    * @Description :根据uid、知识点、teach_book_id 获取用户本周做过题目数据
    * @Date : 2020/9/7
    *
    */
    public response sty_batchThisWeekDoneQuestionCnt(request req) throws BusinessException{
        String entry = "sty_batchThisWeekDoneQuestionCnt";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || Double.valueOf(param.get("uid").toString())<=0
                    || param.get("teach_book_id") == null || param.get("knowledge_ids") == null ) {
                logger.error("{} sty_batchThisWeekDoneQuestionCnt paramter lose, uid or box_id or knowledge_ids or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_batchThisWeekDoneQuestionCnt paramter lose, uid or box_id or knowledge_ids or teach_book_id is null.");
            }else {
                Long uid = Double.valueOf(param.get("uid").toString()).longValue() ;
                Long boxId = Double.valueOf(param.get("box_id").toString()).longValue();
                Long teachBookId = Double.valueOf(param.get("teach_book_id").toString()).longValue();
                //（1：章节，2：知识点）
                //Integer objType = Double.valueOf(param.get("obj_type").toString()).intValue();
                List<Long> knowledgeIds = gson.fromJson(param.get("knowledge_ids").toString(), new TypeToken<List<Long>>(){}.getType());

                if (knowledgeIds.size() > Constants.MAX_REQUEST) {
                    throw new BusinessException(Constants.PARAM_INVALID, "sty_batchThisWeekDoneQuestionCnt paramter invalid, knowledge_ids is too much, the limit num is "+Constants.MAX_REQUEST);
                }else {
                    Map<Long, Object> result = new HashMap<Long, Object>();

                    //获取答题报告周计数
                    Map<String, String> map = getReportWeekValue(entry);
                    Long reportWeekNum = Long.parseLong(map.get("reportWeekNum"));

                    for (Long knowledgeId : knowledgeIds) {
                        List<Long> userDoneQuestionList = userQuestionBoxService.getWeekAnswerBoxQuestionInfo(uid, boxId, teachBookId, knowledgeId, 2, reportWeekNum);

                        List<Long> boxQuestionList = userQuestionBoxService.getBoxQuestionIds(boxId, teachBookId, knowledgeId, 2);

                        //将当前总集合和用户的已做题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户已做题数的问题）
                        List<Long> oriUserDoneList = new ArrayList<Long>();
                        if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
                            oriUserDoneList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, userDoneQuestionList);
                        }else {
                            oriUserDoneList.addAll(userDoneQuestionList);
                        }

                        if (oriUserDoneList != null && oriUserDoneList.size()>0) {
                            result.put(knowledgeId, oriUserDoneList.size());
                        }
                    }

                    if (!result.isEmpty()) {
                        res.setCode(Constants.SUCCESS);
                        res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                    }else {
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("sty_batchThisWeekDoneQuestionCnt return null");
                    }
                }
            }
        }catch(BusinessException e){
            res = dataBusinessException(entry, req, e);
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }


    /**
    *
    * <AUTHOR> duxiulei
    * @Description :sty_batchThisWeekWrongQuestionCnt
    * @Date : 2020/9/8
    *
    */
    public response sty_batchThisWeekWrongQuestionCnt(request req) throws BusinessException{
        String entry = "sty_batchThisWeekWrongQuestionCnt";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || Double.valueOf(param.get("uid").toString())<=0
                   || param.get("teach_book_id") == null || param.get("knowledge_ids") == null ) {
                logger.error("{} sty_batchThisWeekWrongQuestionCnt paramter lose, uid or box_id or knowledge_ids or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_batchThisWeekWrongQuestionCnt paramter lose, uid or box_id or knowledge_ids or teach_book_id is null.");
            }else {
                Long uid = Double.valueOf(param.get("uid").toString()).longValue();
                Long boxId = Double.valueOf(param.get("box_id").toString()).longValue();
                Long teachBookId = Double.valueOf(param.get("teach_book_id").toString()).longValue();
                List<Long> knowledgeIds = gson.fromJson(param.get("knowledge_ids").toString(), new TypeToken<List<Long>>(){}.getType());

                if (knowledgeIds.size() > Constants.MAX_REQUEST) {
                    throw new BusinessException(Constants.PARAM_INVALID, "sty_batchThisWeekWrongQuestionCnt paramter invalid, knowledge_ids is too much, the limit num is "+Constants.MAX_REQUEST);
                }else {
                    Map<Long, Object> result = new HashMap<Long, Object>();

                    Map<String, String> map = getReportWeekValue(entry);
                    Long reportWeekNum = Long.parseLong(map.get("reportWeekNum"));

                    for (Long knowledgeId : knowledgeIds) {
                        List<Long> userWrongQuestionList = userQuestionBoxService.getWeekWrongBoxQuestionInfo(uid, boxId, teachBookId, knowledgeId, 2, reportWeekNum);
                        List<Long> boxQuestionList = userQuestionBoxService.getBoxQuestionIds(boxId, teachBookId, knowledgeId, 2);

                        List<Long> oriUserWrongList = new ArrayList<Long>();
                        if (boxQuestionList != null && !boxQuestionList.isEmpty()) {
                            oriUserWrongList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, userWrongQuestionList);
                        }else {
                            oriUserWrongList.addAll(userWrongQuestionList);
                        }

                        if (oriUserWrongList != null && oriUserWrongList.size()>0) {
                            result.put(knowledgeId, oriUserWrongList.size());
                        }else {
                            result.put(knowledgeId, 0);
                        }
                    }

                    if (!result.isEmpty()) {
                        res.setCode(Constants.SUCCESS);
                        res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                    }else {
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("sty_batchThisWeekWrongQuestionCnt return null");
                    }
                }
            }
        }catch(BusinessException e){
            res = dataBusinessException(entry, req, e);
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }



    /**
     * (批量)获取用户已消灭错的题目总数
     * @param req
     * uid
     * boxId
     * teachBookId
     * objType(章节1/知识点2)
     * objIds(对应的业务ids)
     * @desc 当objType!=0时，校验boxId是否可用，此时objIds即为业务ids(章节ids,知识点ids)
     * */
    public response sty_batchNewWipeOutWrongBoxQuestionCnt(request req) throws BusinessException{
        String entry = "sty_batchNewWipeOutWrongBoxQuestionCnt";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || Double.valueOf(param.get("uid").toString())<=0
                    || param.get("obj_type") == null || Double.valueOf(param.get("obj_type").toString())<0
                    || param.get("teach_book_id") == null || param.get("obj_ids") == null ) {
                logger.error("{} sty_batchNewWipeOutWrongBoxQuestionCnt paramter lose, uid or box_id or obj_ids or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_batchNewWipeOutWrongBoxQuestionCnt paramter lose, uid or box_id or obj_ids or teach_book_id is null.");
            }else {
                Long uid = Double.valueOf(param.get("uid").toString()).longValue() ;
                Long boxId = Double.valueOf(param.get("box_id").toString()).longValue();
                Long teachBookId = Double.valueOf(param.get("teach_book_id").toString()).longValue();
                //（1：章节，2：知识点）
                Integer objType = Double.valueOf(param.get("obj_type").toString()).intValue();
                List<Long> objIds = gson.fromJson(param.get("obj_ids").toString(), new TypeToken<List<Long>>(){}.getType());

                Map<Long, Object> result = new HashMap<Long, Object>();
                for (Long objId : objIds) {
                    Long boxIdOri = null;
                    if (objType.intValue() == 0) {
                        boxIdOri = objId;
                    }else {
                        boxIdOri = boxId;
                    }

                    List<Long> wipeOutWrongQuestionList = userQuestionBoxService.getNewWipeOutWrongBoxQuestionInfo(uid, boxIdOri, teachBookId, objId, objType);
                    logger.info("sty_batchNewWipeOutWrongBoxQuestionCnt wipeOutWrongQuestionList:{}",GsonUtil.toJson(wipeOutWrongQuestionList));

                    Integer objTypeOri = objType == 3 ? 0 : objType;//这里如果是按照题型来取的，就直接拿所有id去过滤就行了
                    List<Long> boxQuestionList = userQuestionBoxNewChapterService.getBoxQuestionIds(boxIdOri, teachBookId, objId, objTypeOri);
                    logger.info("sty_batchNewWipeOutWrongBoxQuestionCnt boxQuestionList:{}",GsonUtil.toJson(boxQuestionList));

//						//将当前总集合和用户的错题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户错题数的问题）
                    List<Long> oriUserWrongList = new ArrayList<Long>();
                    if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
                        oriUserWrongList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, wipeOutWrongQuestionList);
                        logger.info("sty_batchNewWipeOutWrongBoxQuestionCnt intersection:oriUserWrongList:{}",GsonUtil.toJson(oriUserWrongList));
                    }else {
                        oriUserWrongList.addAll(wipeOutWrongQuestionList);
                    }

                    if (oriUserWrongList != null && oriUserWrongList.size()>0) {
                        result.put(objId, oriUserWrongList.size());
                    }else {
                        result.put(objId, 0);
                    }
                }

                if (!result.isEmpty()) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_batchWipeOutWrongBoxQuestionCnt return null");
                }

            }
        }catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }

    /**
     * 获取用户已消灭做错的题目(wipeOut)
     * @param req
     * uid
     * boxId
     * teach_book_id
     * objType(章节/知识点)
     * objId(对应的业务id)
     * */
    public response sty_getNewWipeOutWrongBoxQuestionInfo(request req) throws BusinessException{
        String entry = "sty_getWipeOutWrongBoxQuestionInfo";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Double> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || param.get("uid")<=0
                    || param.get("box_id") == null || param.get("box_id")<=0
                    || param.get("obj_type") == null || param.get("obj_type")<0
                    || param.get("teach_book_id") == null || param.get("teach_book_id") <= 0
                    || param.get("obj_id") == null ) {
                logger.error("{} sty_getNewWipeOutWrongBoxQuestionInfo paramter lose, uid or box_id or obj_id or teach_book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getNewWipeOutWrongBoxQuestionInfo paramter lose, uid or box_id or obj_id or teach_book_id is null.");
            }else {
                Long uid = param.get("uid").longValue() ;
                Long boxId = param.get("box_id").longValue() ;
                Long teachBookId = param.get("teach_book_id").longValue();
                //（1：章节，2：知识点）
                Integer objType = param.get("obj_type").intValue();
                Long objId = param.get("obj_id").longValue();

                List<Long> wipeOutWrongQuestionList = userQuestionBoxService.getNewWipeOutWrongBoxQuestionInfo(uid, boxId, teachBookId, objId, objType);

                Integer objTypeOri = objType == 3 ? 0 : objType;//这里如果是按照题型来取的，就直接拿所有id去过滤就行了
                List<Long> boxQuestionList = new ArrayList<>();
                if (param.get("origin") !=null && param.get("obj_type").intValue() == Consts.QuestionOrigin.Shuye){
                    boxQuestionList = userQuestionBoxService.getShuyeQuestionIds(boxId, teachBookId, objId, objTypeOri, Consts.QuestionOrigin.Shuye);
                }else{
                    boxQuestionList = userQuestionBoxService.getNewBoxQuestionIds(boxId, teachBookId, objId, objTypeOri);
                }

                //将当前总集合和用户的错题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户错题数的问题）
                List<Long> oriUserWrongList = new ArrayList<Long>();
                if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
                    oriUserWrongList = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(boxQuestionList, wipeOutWrongQuestionList);
                }else {
                    oriUserWrongList.addAll(wipeOutWrongQuestionList);
                }

                if (oriUserWrongList != null && oriUserWrongList.size()>0) {
                    Map<String, Object> result = new HashMap<String, Object>();

                    result.put("questionIds", oriUserWrongList);
                    result.put("total", oriUserWrongList.size());
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_getNewWipeOutWrongBoxQuestionInfo return null");
                }
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }

}
