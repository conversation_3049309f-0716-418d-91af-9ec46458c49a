package cn.huanju.edu100.study.dao.evaluation;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.evaluation.PaperAssessmentScore;

public interface PaperAssessmentScoreDao extends CrudDao<PaperAssessmentScore> {

    int getAssessmentAnswerCountByPaperId(Long paperId) throws DataAccessException;

    int getAssessmentAnswerCountByPaperIdAndRightRate(Long paperId,Double rightRate) throws DataAccessException;
}
