/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserAnswerDetailDao;
import cn.huanju.edu100.util.JSONUtils;
import com.hqwx.study.entity.UserAnswerDetail;
import cn.huanju.edu100.util.GsonUtil;
import com.hqwx.study.entity.UserAnswerDetailDto;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户答案子题详情DAO接口
 * <AUTHOR>
 * @version 2015-05-12
 */
public class UserAnswerDetailIbatisImpl extends CrudIbatisImpl2<UserAnswerDetail> implements
		UserAnswerDetailDao {

	public UserAnswerDetailIbatisImpl() {
		super("UserAnswerDetail");
	}

	Logger logger = LoggerFactory.getLogger(UserAnswerIbatisImpl.class);

	@Override
	public List<UserAnswerDetail> findByPaperId(long uid, long paperId) throws DataAccessException {
		if (uid == 0 || paperId == 0) {
			logger.error("findByPaperId {} error, parameter id is null,uid:{} paperId:{}",
					namespace, uid,paperId);
			throw new DataAccessException("findByPaperId error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Long> param = new HashMap<String, Long>();
			param.put("uid", uid);
			param.put("paperId",paperId);
			return (List<UserAnswerDetail>) sqlMap.queryForList(namespace.concat(".findByPaperId"),param);
		} catch (SQLException e) {
			logger.error("findByPaperId {} SQLException uid:{} paperId:{}", namespace, uid,paperId, e);
			throw new DataAccessException("get SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserAnswerDetail> findByUserHomeworkId(Long uid,
			Long userHomeworkId) throws DataAccessException {
		if (uid==null || uid <= 0 || userHomeworkId == null || userHomeworkId <= 0) {
			logger.error("findByUserHomeworkId {} error, parameter id is null,uid:{} userHomeworkId:{}",
					namespace, uid,userHomeworkId);
			throw new DataAccessException("findByUserHomeworkId error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Long> param = new HashMap<String, Long>();
			param.put("uid", uid);
			param.put("userHomeworkId",userHomeworkId);
			return (List<UserAnswerDetail>) sqlMap.queryForList(namespace.concat(".findByUserHomeworkId"),param);
		} catch (SQLException e) {
			logger.error("findByPaperId {} SQLException uid:{} userHomeworkId:{}", namespace, uid,userHomeworkId, e);
			throw new DataAccessException("get SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserAnswerDetail> findByUserHomeworkIdList(Long uid, List<Long> userHomeworkIds) throws DataAccessException {
		if (uid==null || uid <= 0 || userHomeworkIds == null) {
			logger.error("findByUserHomeworkIdList {} error, parameter id is null,uid:{} userHomeworkIds:{}",
					namespace, uid,userHomeworkIds);
			throw new DataAccessException("findByUserHomeworkIdList error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("userHomeworkIdList",userHomeworkIds);
			return (List<UserAnswerDetail>) sqlMap.queryForList(namespace.concat(".findByUserHomeworkIdList"),param);
		} catch (SQLException e) {
			logger.error("findByUserHomeworkIdList {} SQLException uid:{} userHomeworkIds:{}", namespace, uid,userHomeworkIds, e);
			throw new DataAccessException("get SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserAnswerDetail> findLastQIdDetails(UserAnswerDetail param)
			throws DataAccessException {
		if (param==null || param.getUid()==null || param.getUid() <= 0 || param.getQuestionId() == null || param.getQuestionId() <= 0) {
			logger.error("findLastQIdDetails {} error, parameter :{}",
					namespace, GsonUtil.toJson(param));
			throw new DataAccessException("findLastQIdDetails error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<UserAnswerDetail>) sqlMap.queryForList(namespace.concat(".findLastQIdDetails"),param);
		} catch (SQLException e) {
			logger.error("findLastQIdDetails {} SQLException uid:{} userHomeworkId:{}", namespace, param.getUid(),param.getQuestionId(), e);
			throw new DataAccessException("findLastQIdDetails SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserAnswerDetail> getUserAnswerDetailByQuestions(Long uid, List<Long> questionIdList) throws DataAccessException {
		if (uid == null || questionIdList == null){
			logger.error("getUserAnswerDetailByQuestions {} error, parameter id is null,uid:{} questionId:{}",
					namespace, uid,questionIdList);
			throw new DataAccessException("getUserAnswerDetailByQuestions error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("uid", uid);
			params.put("questionIdList",questionIdList);
			return (List<UserAnswerDetail>) sqlMap.queryForList(namespace.concat(".getUserAnswerDetailByQuestions"),params);
		} catch (SQLException e) {
			logger.error("getUserAnswerDetailByQuestions {} SQLException uid:{} userHomeworkId:{}", namespace, uid,questionIdList, e);
			throw new DataAccessException("getUserAnswerDetailByQuestions SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<Long> findStudyCenterLastHomeworkAnswerDetailList(Long uid, List<Long> answerSumIdList) throws DataAccessException {
		if (uid == null || answerSumIdList == null){
			logger.error("findStudyCenterLastHomeworkAnswerDetailList {} error, parameter id is null,uid:{} answerSumIdList:{}",
					namespace, uid,answerSumIdList);
			throw new DataAccessException("findStudyCenterLastHomeworkAnswerDetailList error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("uid", uid);
			params.put("answerSumIdList",answerSumIdList);
			return (List<Long>) sqlMap.queryForList(namespace.concat(".findStudyCenterLastHomeworkAnswerDetailList"),params);
		} catch (SQLException e) {
			logger.error("findStudyCenterLastHomeworkAnswerDetailList {} SQLException uid:{} answerSumIdList:{}", namespace, uid,answerSumIdList, e);
			throw new DataAccessException("findStudyCenterLastHomeworkAnswerDetailList SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserAnswerDetailDto> getUserAnswerDetailByQuestionAndAnswer(Long uid, List<Long> answerSumIdList, Long questionId) throws DataAccessException {
		if (uid == null || CollectionUtils.isEmpty(answerSumIdList) || questionId == null){
			logger.error("getUserAnswerDetailByQuestions {} error, parameter id is null,uid:{} questionId:{}",
					namespace, uid, JSONUtils.toJsonString(answerSumIdList),questionId);
			throw new DataAccessException("getUserAnswerDetailByQuestions error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("uid", uid);
			params.put("answerSumIdList",answerSumIdList);
			params.put("questionId",questionId);
			return (List<UserAnswerDetailDto>) sqlMap.queryForList(namespace.concat(".getUserAnswerDetailByQuestionAndAnswer"),params);
		} catch (SQLException e) {
			logger.error("getUserAnswerDetailByQuestions {} SQLException uid:{} userHomeworkId:{}", namespace, uid,JSONUtils.toJsonString(answerSumIdList),questionId, e);
			throw new DataAccessException("getUserAnswerDetailByQuestions SQLException error"
					+ e.getMessage());
		}
	}

	@Override
	public List<UserAnswerDetail> getUserAnswerDetailBySumIdList(Long uid, List<Long> answerSumIdList) throws DataAccessException {
		if (uid == null || CollectionUtils.isEmpty(answerSumIdList) ){
			logger.error("getUserAnswerDetailByQuestions {} error, parameter id is null,uid:{}, answerSumIdList:{}",
					namespace, uid, JSONUtils.toJsonString(answerSumIdList));
			throw new DataAccessException("getUserAnswerDetailByQuestions error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("uid", uid);
			params.put("answerSumIdList",answerSumIdList);
			return (List<UserAnswerDetail>) sqlMap.queryForList(namespace.concat(".getUserAnswerDetailBySumIdList"),params);
		} catch (SQLException e) {
			logger.error("getUserAnswerDetailBySumIdList {} SQLException uid:{} answerSumIdList:{}", namespace, uid,JSONUtils.toJsonString(answerSumIdList), e);
			throw new DataAccessException("getUserAnswerDetailBySumIdList SQLException error"
					+ e.getMessage());
		}
	}
}
