package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.dao.impl.CrudIbatisImpl;
import cn.huanju.edu100.persistence.model.DataEntity;
import com.google.gson.Gson;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.shardingsphere.infra.hint.HintManager;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public class CrudIbatisImpl2<T extends DataEntity> extends CrudIbatisImpl<T> {
    public CrudIbatisImpl2(String namespace) {
        super(namespace);
    }

    @Override
    public T getFromMasterDb(long id) throws DataAccessException {
        try {
            forceWriteRoute();
            return super.getFromMasterDb(id);
        } finally {
            clearHint();
        }
    }

    @Override
    public long insert(T entity) throws DataAccessException {
        if (entity == null) {
            this.logger.error("add {} error, parameter is null", this.namespace);
            throw new DataAccessException("add {} error,param is null", this.namespace);
        }
        try {
            forceWriteRoute();
            SqlMapClient sqlMap = super.getMaster();
            Object id = sqlMap.insert(this.namespace.concat(".insert"), entity);
            return id instanceof Long ? (Long) id : 0L;
        } catch (SQLException ex) {
            this.logger.error("insert {} SQLException.content:{}", new Object[]{this.namespace, (new Gson()).toJson(entity), ex});
            throw new DataAccessException("add " + this.namespace + " SQLException fail.");
        } finally {
            clearHint();
        }
    }

    @Override
    public List<T> findListFromMatster(T entity) throws DataAccessException {
        if (entity == null) {
            this.logger.error("get {} error, parameter id is null", this.namespace);
            throw new DataAccessException("get error,id is null");
        }
        Map<String, Object> param = this.transBean2Map(entity);
        return queryForListFromMaster(this.namespace.concat(".findList"), param);

    }


    public <T> T queryForObjectFromMaster(String id, Object param) throws DataAccessException {
        try {
            forceWriteRoute();
            SqlMapClient sqlMap = super.getMaster();
            return (T) sqlMap.queryForObject(id, param);
        } catch (SQLException e) {
            logger.error(id + " SQLException param:{}", param, e);
            throw new DataAccessException(id + " SQLException error");
        } finally {
            clearHint();
        }
    }

    public <T> T queryForListFromMaster(String id, Object param) throws DataAccessException {
        try {
            forceWriteRoute();
            SqlMapClient sqlMap = super.getMaster();
            return (T) sqlMap.queryForList(id, param);
        } catch (SQLException e) {
            logger.error(id + "SQLException.detailIds:{}", param, e);
            throw new DataAccessException(id + " SQLException error");
        } finally {
            clearHint();
        }
    }


    @Override
    public SqlMapClient getMaster() {
        return super.getMaster();
    }

    @Override
    public SqlMapClient getShardingMaster() {
        return super.getShardingMaster();
    }

    private void forceWriteRoute() {
        HintManager.clear();
        HintManager instance = HintManager.getInstance();
        instance.setWriteRouteOnly();
    }

    private void clearHint() {
        HintManager.clear();
    }
}
