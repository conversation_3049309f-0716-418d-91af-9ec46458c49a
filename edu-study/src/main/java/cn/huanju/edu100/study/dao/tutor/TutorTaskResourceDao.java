/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorTaskResource;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 资源类任务DAO接口
 * <AUTHOR>
 * @version 2016-01-18
 */
public interface TutorTaskResourceDao extends CrudDao<TutorTaskResource> {

    List<TutorTaskResource> getByIdList(List<Long> taskIdList) throws DataAccessException;

}
