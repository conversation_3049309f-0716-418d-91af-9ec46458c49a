/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.mock;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.mock.MockLiveLessonDao;
import cn.huanju.edu100.study.model.mock.MockLiveLesson;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 模考关联直播课节DAO接口
 * <AUTHOR>
 */
public class MockLiveLessonIbatisImpl extends CrudIbatisImpl2<MockLiveLesson> implements
        MockLiveLessonDao {

	public MockLiveLessonIbatisImpl() {
		super("MockLiveLesson");
	}


    @Override
    public List<MockLiveLesson> getByMockId(Long mockExamId) throws DataAccessException{
	    if (mockExamId == null){
            logger.error("getByMockId error, parameter mockExamId:{}",mockExamId );
	        return null;
        }
	    try{
            SqlMapClient sqlMapClient = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("mockExamId",mockExamId);
            List<MockLiveLesson> result = (List<MockLiveLesson>) sqlMapClient.queryForList("MockLiveLesson.getByMockId",param);
            return result;
        }catch (SQLException e) {
            logger.error("getByMockId error, parameter mockExamId:{},error;{}",mockExamId,e);
            throw new DataAccessException("getPapersByBoxIdCount SQLException error");
        }
    }
}
