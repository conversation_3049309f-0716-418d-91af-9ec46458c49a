/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 答题明细表Entity
 * <AUTHOR>
 * @version 2015-05-14
 */
public class QuestionAnswerDetail extends DataEntity<QuestionAnswerDetail> {

	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long questionId;		// question_id
	private Date answerTime;		// answer_time
	private Integer state;		// 答题状态（0：错误，1：部分正确，2：正确，3：未作答）

	public QuestionAnswerDetail() {
		super();
	}

	public QuestionAnswerDetail(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}
	public void setUid(Long uid) {
		this.uid = uid;
	}
	public Long getQuestionId() {
		return questionId;
	}
	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}
	public Date getAnswerTime() {
		return answerTime;
	}
	public void setAnswerTime(Date answerTime) {
		this.answerTime = answerTime;
	}
//	@NotNull(message="答题状态（0：未答，1：正确，2：部分正确，3：错误）不能为空")
	public Integer getState() {
		return state;
	}
	public void setState(Integer state) {
		this.state = state;
	}

	@Override
	public QuestionAnswerDetail clone() {
		QuestionAnswerDetail detail = new QuestionAnswerDetail();
		detail.setUid(this.uid);
		detail.setQuestionId(this.questionId);
		detail.setAnswerTime(this.answerTime);
		detail.setState(this.state);
		detail.setId(this.id);
		return detail;
	}
}
