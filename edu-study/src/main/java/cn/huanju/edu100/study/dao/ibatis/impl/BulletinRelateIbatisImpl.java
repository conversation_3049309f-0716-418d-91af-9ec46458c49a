/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.BulletinRelateDao;
import cn.huanju.edu100.study.model.BulletinRelate;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 白名单列表DAO接口
 * <AUTHOR>
 * @version 2015-10-27
 */
public class BulletinRelateIbatisImpl extends CrudIbatisImpl2<BulletinRelate> implements
		BulletinRelateDao {

	public BulletinRelateIbatisImpl() {
		super("BulletinRelate");
	}

    @Override
    public List<Long> getBulletinRelateByRelateIds(List<String> ids, Integer type) throws DataAccessException {
        try {
            Map<String, Object> paramsMap = new HashMap<String, Object>();
            paramsMap.put("ids", ids);
            if (type != null) {
                paramsMap.put("type", type);
            }
            SqlMapClient sqlMap = super.getSlave();
            return sqlMap
                    .queryForList("BulletinRelate.getBulletinRelateByRelateIds", paramsMap);
        } catch (SQLException e) {
            logger.error("getBulletinRelateByRelateIds SQLException.", e);
            throw new DataAccessException("getBulletinRelateByRelateIds SQLException error");
        }
    }

}
