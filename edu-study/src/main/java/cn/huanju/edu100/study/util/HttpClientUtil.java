package cn.huanju.edu100.study.util;

import cn.huanju.edu100.stustamp.util.InternalServiceUtil;
import com.mchange.v2.uid.UidUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

public class HttpClientUtil {

    private static Logger log = LoggerFactory.getLogger(HttpClientUtil.class);
//
//
//    @SuppressWarnings("deprecation")
//    public static String doPost(HttpClient httpClient, String url, List<NameValuePair> params) {
//        /* 建立HTTPPost对象 */
//        HttpPost httpRequest = new HttpPost(url);
//        HttpEntity entity = null;
//        String strResult = null;
//        long start = System.currentTimeMillis();
//        try {
//            /* 添加请求参数到请求对象 */
//
//            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(params, HTTP.UTF_8);
//
//            httpRequest.setEntity(urlEncodedFormEntity);
//            /* 发送请求并等待响应 */
//            HttpResponse httpResponse = httpClient.execute(httpRequest);
//            /* 若状态码为200 ok */
//            if (httpResponse.getStatusLine().getStatusCode() == 200) {
//                entity = httpResponse.getEntity();
//                /* 读返回数据 */
//                strResult = EntityUtils.toString(entity);
//            }
//        } catch (Exception e) {
//            handleException(e, url);
//        } finally {
//            EntityUtils.consumeQuietly(entity);
//            httpRequest.releaseConnection();
//            log.info("request:{},params:{},resp:{},cost:{}", url, params, strResult, System.currentTimeMillis() - start);
//        }
//
//        return strResult;
//    }
//
//    /**
//     * 发送内容格式为json的post请求
//     *
//     * @param httpClient
//     * @param url
//     * @param jsonStr
//     * @return
//     */
//    public static String post(HttpClient httpClient, String url, String jsonStr) {
//
//        HttpPost post = new HttpPost(url);
//        StringEntity entity = new StringEntity(jsonStr, ContentType.APPLICATION_JSON);
//        entity.setContentEncoding("UTF-8");
//        entity.setContentType("application/json");// 发送json数据需要设置contentType
//        post.setEntity(entity);
//        String traceId = MDC.get("traceId");
//        if(StringUtils.isNotBlank(traceId)) {
//            post.addHeader("traceId", traceId);
//        }
//        long start = System.currentTimeMillis();
//        String result = null;
//
//        try {
//            HttpResponse resp = httpClient.execute(post);
//            int statusCode = resp.getStatusLine().getStatusCode();
//            if (HttpStatus.SC_OK == statusCode) {
//                result = EntityUtils.toString(resp.getEntity());
//            } else {
//                log.error("http request fail, statusLine :{}", resp.getStatusLine());
//            }
//        } catch (ClientProtocolException e) {
//            handleException(e, url);
//        } catch (IOException e) {
//            handleException(e, url);
//        } finally {
//            post.releaseConnection();
//            EntityUtils.consumeQuietly(entity);
//            log.info("request :{}, params :{}, resp :{}, cost :{}", url, jsonStr, result, System.currentTimeMillis()
//                    - start);
//        }
//
//        return result;
//    }
//
//    public static String post(String url, Map<String, String> params) throws ParseException, IOException {
//        DefaultHttpClient httpclient = new DefaultHttpClient();
//        String body = null;
//
//        log.info("create httppost:" + url);
//        HttpPost post = postForm(url, params);
//
//        body = invoke(httpclient, post);
//
//        httpclient.getConnectionManager().shutdown();
//
//        return body;
//    }
//
//    public static String get(HttpClient httpClient, String url) throws ParseException, IOException {
//        String body = null;
//
//        log.info("create http get:" + url);
//        HttpGet get = new HttpGet(url);
//        body = invoke(httpClient, get);
//
//        return body;
//    }
//
//    public static String get(String url) throws ParseException, IOException {
//        DefaultHttpClient httpclient = new DefaultHttpClient();
//        String body = null;
//
//        log.info("create http get:" + url);
//        HttpGet get = new HttpGet(url);
//
//        body = invoke(httpclient, get);
//
//        httpclient.getConnectionManager().shutdown();
//
//        return body;
//    }

    /**
     * url转MultipartFile
     * @param url
     * @return
     * @throws Exception
     */
    public static MultipartFile urlToMultipartFile(String url) throws Exception {
        File file = null;
        MultipartFile multipartFile = null;
        try {
            HttpURLConnection httpUrl = (HttpURLConnection) new URL(url).openConnection();
            httpUrl.connect();
            file = inputStreamToFile(httpUrl.getInputStream(), UUID.randomUUID().toString());

            multipartFile = fileToMultipartFile(file);
            httpUrl.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return multipartFile;
    }

    /**
     * inputStream 转 File
     * @param ins
     * @param name
     * @return
     * @throws Exception
     */
    private static File inputStreamToFile(InputStream ins, String name) throws Exception{
        File file = new File(System.getProperty("java.io.tmpdir") + File.separator + name);
        OutputStream os = new FileOutputStream(file);
        int bytesRead;
        int len = 8192;
        byte[] buffer = new byte[len];
        while ((bytesRead = ins.read(buffer, 0, len)) != -1) {
            os.write(buffer, 0, bytesRead);
        }
        os.close();
        ins.close();
        return file;
    }



    /**
     * file转multipartFile
     * @param file
     * @return
     */
    private static MultipartFile fileToMultipartFile(File file) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item=factory.createItem(file.getName(),"text/plain",true,file.getName());
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        try {
            FileInputStream fis = new FileInputStream(file);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new CommonsMultipartFile(item);
    }
//
//
//    private static String invoke(HttpClient httpclient,
//            HttpUriRequest httpost) throws ParseException, IOException {
//        HttpResponse response = sendRequest(httpclient, httpost);
//        String body = paseResponse(response);
//
//        return body;
//    }
//
//    private static String paseResponse(HttpResponse response) throws ParseException, IOException {
//        log.info("get response from http server..");
//        HttpEntity entity = response.getEntity();
//
//        log.info("response status: " + response.getStatusLine());
//        String charset = EntityUtils.getContentCharSet(entity);
//        log.info(charset);
//
//        String body = null;
//        body = EntityUtils.toString(entity);
//        log.info(body);
//
//        return body;
//    }
//
//    private static HttpResponse sendRequest(HttpClient httpclient,
//            HttpUriRequest httpost) throws ClientProtocolException, IOException {
//        log.info("execute post...");
//        String traceId = MDC.get("traceId");
//        if(StringUtils.isNotBlank(traceId)) {
//            httpost.addHeader("traceId", traceId);
//        }
//        return httpclient.execute(httpost);
//    }
//
//    private static HttpPost postForm(String url, Map<String, String> params) throws UnsupportedEncodingException{
//
//        HttpPost httpost = new HttpPost(url);
//        List<NameValuePair> nvps = new ArrayList <NameValuePair>();
//
//        Set<String> keySet = params.keySet();
//        for(String key : keySet) {
//            nvps.add(new BasicNameValuePair(key, params.get(key)));
//        }
//
//        log.info("set utf-8 form entity to httppost");
//        httpost.setEntity(new UrlEncodedFormEntity(nvps, HTTP.UTF_8));
//
//        return httpost;
//    }
//
//    /**
//     * @param e
//     */
//    private static void handleException(Exception e, String url) {
//        log.error("Error when get remote http content with http client.{}", url, e);
//    }
//
//    /**
//     * @param e
//     */
//    private static void handleException(Exception e) {
//        log.error("Error when get remote http content with http client.", e);
//    }
}
