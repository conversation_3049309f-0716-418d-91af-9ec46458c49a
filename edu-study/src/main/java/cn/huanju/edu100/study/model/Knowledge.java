package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Collection;

/**
 * 查询知识点相关信息Entity
 *
 * <AUTHOR>
 * @version 2015-05-09
 */
public class Knowledge extends DataEntity<Knowledge> {

	private static final long serialVersionUID = 1L;
	private String name; // name
	private Long parent_id; // parent_id
	private String parentIds; // parent_ids
	private Long firstCategory; // first_category
	private Long secondCategory; // second_category
	private Long categoryId; // category_id
	private Integer klevel; // 难度等级
	private Integer frequency; // frequency
	private Integer state; // 状态 1：已生效 0 ：未发布
	Collection<Resource> resources;

	/**
	 * @return the parent_id
	 */
	public Long getParent_id() {
		return parent_id;
	}

	/**
	 * @param parent_id
	 *            the parent_id to set
	 */
	public void setParent_id(Long parent_id) {
		this.parent_id = parent_id;
	}

	private String remark; // remark

	public Knowledge() {
		super();
	}

	public Knowledge(Long id) {
		super(id);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getParentIds() {
		return parentIds;
	}

	public void setParentIds(String parentIds) {
		this.parentIds = parentIds;
	}

	public Long getFirstCategory() {
		return firstCategory;
	}

	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getKlevel() {
		return klevel;
	}

	public void setKlevel(Integer klevel) {
		this.klevel = klevel;
	}

	public Integer getFrequency() {
		return frequency;
	}

	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Collection<Resource> getResources() {
		return resources;
	}

	public void setResources(Collection<Resource> resources) {
		this.resources = resources;
	}

}
