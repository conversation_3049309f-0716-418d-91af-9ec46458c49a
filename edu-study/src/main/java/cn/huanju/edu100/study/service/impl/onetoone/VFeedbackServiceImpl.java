package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.*;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.goods.Product;
import cn.huanju.edu100.study.model.onetoone.*;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.service.onetoone.VFeedbackService;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.util.DateUtils;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 留学反馈/报告Service
 * <AUTHOR>
 * @version 2016-12-12
 */
@Service
public class VFeedbackServiceImpl extends BaseServiceImpl<VFeedbackDao, VFeedback> implements VFeedbackService {


    @Autowired
    private VClassesDao vClassesDao;

    @Autowired
    private VLessonDao vLessonDao;

    @Autowired
    private VTeacherDao vTeacherDao;

    @Autowired
    private VSysUserClassesDao vSysUserClassesDao;

    @Autowired
    private GoodsResource goodsResource;


    @Override
    public List<VFeedback> findListByParam(Map<String, Object> params) throws DataAccessException {

        if (MapUtils.isEmpty(params)) {
            logger.error("findListByParam fail, emtpy params");
            throw new DataAccessException("empty param map");
        }

        return dao.findListByParam(params);
    }

    @Override
    public Integer findListCountByParam(Map<String, Object> params) throws DataAccessException {

        if (MapUtils.isEmpty(params)) {
            logger.error("findListCountByParam fail, emtpy params");
            throw new DataAccessException("empty param map");
        }

        return dao.findListCountByParam(params);
    }

    @Override
    public boolean insertBatch(Collection<VFeedback> feedbacks) throws DataAccessException {

        if (CollectionUtils.isEmpty(feedbacks)) {
            logger.error("insertBatch fail, empty feedbacks");
            throw new DataAccessException("insertBatch fail, empty feedbacks");
        }

        return dao.insertBatch(feedbacks);
    }

    @Override
    public boolean updateBatch(List<VFeedback> vFeedbacks) throws DataAccessException {

        if (CollectionUtils.isEmpty(vFeedbacks)) {
            logger.error("updateBatch fail, vFeedbacks is empty");
            throw new DataAccessException("updateBatch fail, vFeedbacks is empty");
        }

        return dao.updateBatch(vFeedbacks);
    }

    @Override
    public void buildProductInfo(List<VFeedback> vFeedbacks) throws DataAccessException {

        if (CollectionUtils.isNotEmpty(vFeedbacks)) {
            Set<Long> clsIdSet = Sets.newHashSet();
            for (VFeedback vFeedback : vFeedbacks) {
                if (IdUtils.isValid(vFeedback.getvClsId())) {
                    clsIdSet.add(vFeedback.getvClsId());
                }
            }

            List<Long> clsIdList = Lists.newArrayList();
            clsIdList.addAll(clsIdSet);
            List<VClasses> vClasses = vClassesDao.findListByIds(clsIdList);
            if (CollectionUtils.isNotEmpty(vClasses)) {
                Map<Long, Long> vClsId2ProdIdMap = Maps.newHashMap();
                Set<Long> productIdSet = Sets.newHashSet();
                for (VClasses cls : vClasses) {
                    vClsId2ProdIdMap.put(cls.getId(), cls.getProductId());
                    productIdSet.add(cls.getProductId());
                }

                List<Long> prodIdList = Lists.newArrayList();
                prodIdList.addAll(productIdSet);
                Map<Long, Product> productIdMap = goodsResource.getProductsByIdList(prodIdList);
                if (MapUtils.isNotEmpty(productIdMap)) {
                    for (VFeedback vFeedback : vFeedbacks) {
                        Long vClsId = vFeedback.getvClsId();
                        if (IdUtils.isValid(vClsId)) {
                            Long productId = vClsId2ProdIdMap.get(vClsId);
                            if (IdUtils.isValid(productId)) {
                                Product prod = productIdMap.get(productId);
                                if (null != prod) {
                                    vFeedback.setProductName(prod.getName());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void buildLessonInfo(List<VFeedback> vFeedbacks) throws DataAccessException {

        if (CollectionUtils.isNotEmpty(vFeedbacks)) {
            Set<Long> lessonIdSet = Sets.newHashSet();
            for (VFeedback vFeedback : vFeedbacks) {
                if (IdUtils.isValid(vFeedback.getvLessonId())) {
                    lessonIdSet.add(vFeedback.getvLessonId());
                }
            }

            if (CollectionUtils.isNotEmpty(lessonIdSet)) {
                List<Long> idList = Lists.newArrayList();
                idList.addAll(lessonIdSet);
                List<VLesson> vLessons = vLessonDao.findListByIds(idList);
                if (CollectionUtils.isNotEmpty(vLessons)) {
                    Map<Long, VLesson> lessonMap = Maps.newHashMap();
                    List<Long> teacherIdList = Lists.newArrayList();
                    for (VLesson vLesson : vLessons) {
                        lessonMap.put(vLesson.getId(), vLesson);
                        teacherIdList.add(vLesson.getTeacherUid());
                    }

                    Map<Long, VTeacher> teacherMap = Maps.newHashMap();
                    if (CollectionUtils.isNotEmpty(teacherIdList)) {
                        List<VTeacher> teachers = vTeacherDao.findListByIds(teacherIdList);
                        if (CollectionUtils.isNotEmpty(teachers)) {
                            for (VTeacher vTeacher : teachers) {
                                teacherMap.put(vTeacher.getId(), vTeacher);
                            }
                        }
                    }

                    for (VFeedback vFeedback : vFeedbacks) {
                        if (IdUtils.isValid(vFeedback.getvLessonId())) {
                            VLesson vLesson = lessonMap.get(vFeedback.getvLessonId());
                            if (null != vLesson) {
                                vFeedback.setvLessonName(vLesson.getName());
                                vFeedback.setLessonStartTime(vLesson.getStartTime());
                                vFeedback.setLessonEndTime(vLesson.getEndTime());
                                vFeedback.setTitle('[' + vLesson.getName() + ']'
                                        + DateUtils.format(vLesson.getStartTime(), "MM月dd日") + "-课时报告");
                                VTeacher teacher = teacherMap.get(vLesson.getTeacherUid());
                                if (null != teacher) {
                                    vFeedback.setTeacherName(teacher.getName());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void buildAllLessonInfo(List<VFeedback> vFeedbacks) throws DataAccessException {

        if (CollectionUtils.isNotEmpty(vFeedbacks)) {
            Set<Long> lessonIdSet = Sets.newHashSet();
            for (VFeedback vFeedback : vFeedbacks) {
                if (IdUtils.isValid(vFeedback.getvLessonId())) {
                    lessonIdSet.add(vFeedback.getvLessonId());
                }
            }

            if (CollectionUtils.isNotEmpty(lessonIdSet)) {
                List<Long> idList = Lists.newArrayList();
                idList.addAll(lessonIdSet);
                List<VLesson> vLessons = vLessonDao.findListByIds(idList);
                if (CollectionUtils.isNotEmpty(vLessons)) {
                    Map<Long, VLesson> lessonMap = Maps.newHashMap();
                    List<Long> teacherIdList = Lists.newArrayList();
                    List<Long> classesIdList = Lists.newArrayList();
                    for (VLesson vLesson : vLessons) {
                        lessonMap.put(vLesson.getId(), vLesson);
                        teacherIdList.add(vLesson.getTeacherUid());
                        classesIdList.add(vLesson.getClsId());
                    }

                    Map<Long, VTeacher> teacherMap = Maps.newHashMap();
                    if (CollectionUtils.isNotEmpty(teacherIdList)) {
                        List<VTeacher> teachers = vTeacherDao.findListByIds(teacherIdList);
                        if (CollectionUtils.isNotEmpty(teachers)) {
                            for (VTeacher vTeacher : teachers) {
                                teacherMap.put(vTeacher.getId(), vTeacher);
                            }
                        }

                        for (VLesson vLesson : vLessons) {
                            VTeacher vTeacher = teacherMap.get(vLesson.getTeacherUid());
                            vLesson.setvTeacher(vTeacher);
                        }
                    }

                    Map<Long, VClasses> classesMap = Maps.newHashMap();
                    if (CollectionUtils.isNotEmpty(teacherIdList)) {
                        List<VClasses> teachers = vClassesDao.findListByIds(classesIdList);
                        if (CollectionUtils.isNotEmpty(teachers)) {
                            for (VClasses vTeacher : teachers) {
                                classesMap.put(vTeacher.getId(), vTeacher);
                            }
                        }

                        for (VLesson vLesson : vLessons) {
                            VClasses vClasses = classesMap.get(vLesson.getClsId());
                            vLesson.setvClasses(vClasses);
                        }
                    }

                    for (VFeedback vFeedback : vFeedbacks) {
                        if (IdUtils.isValid(vFeedback.getvLessonId())) {
                            VLesson vLesson = lessonMap.get(vFeedback.getvLessonId());
                            vFeedback.setvLesson(vLesson);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void buildFeedbackCount(List<VLesson> vLessonList, Integer type) throws DataAccessException {

        if (CollectionUtils.isNotEmpty(vLessonList)) {
            List<Long> lessonIdList = Lists.newArrayList();
            for (VLesson vLesson : vLessonList) {
                lessonIdList.add(vLesson.getId());
            }

            List<CountModel> counts = dao.listFeedbackCountByLessonIds(lessonIdList, type);
            if (CollectionUtils.isNotEmpty(counts)) {
                Map<Long, Integer> feedbackCountMap = Maps.newHashMap();
                for (CountModel countModel : counts) {
                    feedbackCountMap.put(countModel.getId(), countModel.getNum());
                }

                for (VLesson vLesson : vLessonList) {
                    Integer num = feedbackCountMap.get(vLesson.getId());
                    if (num != null) {
                        vLesson.setFeedbackCount(num);
                    }
                }
            }

        }
    }

    @Override
    public void buildCreatorName(List<VFeedback> feedbacks) throws DataAccessException {

        if (CollectionUtils.isNotEmpty(feedbacks)) {
            Set<Long> clsIdSet = Sets.newHashSet();
            for (VFeedback vFeedback : feedbacks) {
                clsIdSet.add(vFeedback.getvClsId());
            }

            List<Long> clsIdList = Lists.newArrayList();
            clsIdList.addAll(clsIdSet);
            Map<Long, VSysUserClasses> clsId2VSysUserMap = vSysUserClassesDao.getClsId2VSysUser(clsIdList);
            if (MapUtils.isNotEmpty(clsId2VSysUserMap)) {
                for (VFeedback feedback : feedbacks) {
                    Long clsId = feedback.getvClsId();
                    VSysUserClasses sysUserClasses = clsId2VSysUserMap.get(clsId);
                    if (null != sysUserClasses) {
                        feedback.setCreatorName(sysUserClasses.getName());
                    }
                }
            }
        }
    }


}
