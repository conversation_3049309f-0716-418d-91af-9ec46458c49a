package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.stustamp.util.ThriftRequestHelper;
import com.hqwx.fund.client.HqwxFundClient;
import com.hqwx.fund.dto.enums.CertifyContractEnum;
import com.hqwx.fund.dto.request.ContractAccountInfoReq;
import com.hqwx.fund.dto.request.ContractAccountUpdateReq;
import com.hqwx.fund.dto.response.ContractAccountInfoDTO;
import com.hqwx.thrift.client.base.ThriftRequest;
import com.hqwx.thrift.client.base.ThriftResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class FundResource {

    private static final Logger log = LoggerFactory.getLogger(FundResource.class);

    @Resource
    private HqwxFundClient hqwxFundClient;

    public CertifyContractEnum updateContractAccountInfo(ContractAccountUpdateReq req) {
        ThriftRequest<ContractAccountUpdateReq> request = ThriftRequestHelper.prepareThriftRequest(req);
        ThriftResponse<CertifyContractEnum> resp = hqwxFundClient.updateContractAccountInfo(request);
        return resp.getMsg();
    }

    public ContractAccountInfoDTO getContractAccountInfo(Long buyOrderId) {
        ContractAccountInfoReq req = new ContractAccountInfoReq();
        req.setBuyOrderId(buyOrderId);
        ThriftRequest<ContractAccountInfoReq> request = ThriftRequestHelper.prepareThriftRequest(req);
        ThriftResponse<ContractAccountInfoDTO> resp = hqwxFundClient.getContractAccountInfo(request);
        return resp.getMsg();
    }
}
