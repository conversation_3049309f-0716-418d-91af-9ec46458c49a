/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.Recordcourse;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 录播课程学习DAO接口
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface RecordcourseDao extends CrudDao<Recordcourse> {
	/**根据任务ID集查找到对应的Recordcourse结果集*/
	public List<Recordcourse> findListByTaskIdList(List<Long> taskIdList) throws DataAccessException;
}
