package cn.huanju.edu100.study.service.user;

import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.dto.UserConfigDTO;

import java.util.List;

public interface UserConfigService {

    List<UserConfigDTO> findUserConfigByConfigKey(String configKey, Long uid) throws DataAccessException;

    void saveUserConfig(Long uid, String configKey, String configVal) throws DataAccessException;
}
