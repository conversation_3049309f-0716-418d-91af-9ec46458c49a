/**
 *
 */
package cn.huanju.edu100.study.dao.expression;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.expression.ExpressionMember;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 规则关联DAO接口
 *
 * <AUTHOR>
 * @version 2016-05-23
 */
public interface ExpressionMemberDao extends CrudDao<ExpressionMember> {

    List<ExpressionMember> findListByParam(List<Long> ruleIdList, List<Long> groupIdList, Integer type) throws DataAccessException;

}
