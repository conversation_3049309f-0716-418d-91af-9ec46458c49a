package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorStudentDao;
import cn.huanju.edu100.study.model.tutor.TutorStudent;
import cn.huanju.edu100.study.service.tutor.TutorStudentService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 个性化学员Service
 * <AUTHOR>
 * @version 2016-01-12
 */
@Service
public class TutorStudentServiceImpl extends BaseServiceImpl<TutorStudentDao, TutorStudent> implements TutorStudentService {

    @Override
    public TutorStudent getTutorStudentByUid(Long uid) throws DataAccessException {

        if (uid == null) {
            return null;
        }

        TutorStudent tutorStudent = new TutorStudent();
        tutorStudent.setUid(uid);
        List<TutorStudent> tutorStudentList = dao.findList(tutorStudent);
        if (CollectionUtils.isEmpty(tutorStudentList)) {
            return null;
        }

        return tutorStudentList.get(0);
    }

}
