package cn.huanju.edu100.study.service.impl.calculate;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.calculate.TikuQuestionCalculateDao;
import cn.huanju.edu100.study.model.calculate.TikuQuestionCalculate;
import cn.huanju.edu100.study.service.calculate.TikuQuestionCalculateService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

@Service
public class TikuQuestionCalculateServiceImpl extends BaseServiceImpl<TikuQuestionCalculateDao, TikuQuestionCalculate>
        implements TikuQuestionCalculateService {
    @Override
    public TikuQuestionCalculate getByUidSecondCategoryId(Long uid, Long secondCategoryId) throws DataAccessException {

        return dao.getByUidSecondCategory(uid, secondCategoryId);
    }

}
