package cn.huanju.edu100.study.config.rpc.leaf;

import cn.huanju.edu100.thrift.edu_leaf.Iface;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.GsonUtils;
import cn.huanju.edu100.util.IpConvert;
import com.hqwx.thrift.client.thrift.ThriftClientFactory;
import com.hqwx.thrift.client.thrift.ThriftClientWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LeafThriftService {
    private static final Logger log = LoggerFactory.getLogger(LeafThriftService.class);

    public static final int APP_ID = 7;

    @Autowired(required = false)
    @Qualifier("leafClientFactory")
    private ThriftClientFactory<Iface> leafClientFactory;

    public Long getIdBySegment(String clientIp, String key) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("key", key.toLowerCase());
        ThriftClientWrapper<Iface> leafClient = createClient();
        try {
            log.info("getIdBySegment start request:{}", GsonUtils.toJson(params));
            response response = leafClient.getClient().leaf_getIdBySegment(buildRequest(clientIp, GsonUtils.toDefaultJson(params)));
            log.info("getIdBySegment end response:{}", GsonUtils.toJson(response));
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                return NumberUtils.toLong(response.getMsg());
            } else {
                log.error("getIdBySegment param:{} errcode:{} errMsg:{}", GsonUtils.toDefaultJson(params),
                        response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            log.error("getIdBySegment failed: key = " + key, e);
        } finally {
            closeClient(leafClient);
        }
        return null;
    }

    // 获取client wrapper.
    private ThriftClientWrapper<Iface> createClient() {
        ThriftClientWrapper<Iface> client = null;
        try {
            client = leafClientFactory.createClient();
        } catch (Exception e) {
            log.error("getClient failed ", e);
            throw new RuntimeException(e);
        }
        return client;
    }

    private void closeClient(ThriftClientWrapper<Iface> client) {
        if (client != null) {
            client.close();
        }
    }

    public void setClientFactory(ThriftClientFactory<Iface> clientFactory) {
        this.leafClientFactory = clientFactory;
    }

    private request buildRequest(String clientIp, String msg) {
        request request = new request();
        request.setAppid(APP_ID);
        request.setClient_ip(IpConvert.ipToLong(clientIp));
        request.setCodetype(1);
        request.setMsg(msg);

        return request;
    }

    public List<Long> getIdsBySegment(String clientIp, String key, Integer count) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("key", key.toLowerCase());
        params.put("count", count);
        ThriftClientWrapper<Iface> leafClient = createClient();
        try {
            log.info("getIdsBySegment start request:{}", GsonUtils.toJson(params));
            response response = leafClient.getClient().leaf_getIdsBySegment(buildRequest(clientIp, GsonUtils.toDefaultJson(params)));
            log.info("getIdsBySegment end response:{}", GsonUtils.toJson(response));
            if (response != null && response.getCode() == 0 && StringUtils.isNotBlank(response.getMsg())) {
                return (List<Long>) GsonUtil.getGson().fromJson(response.getMsg(), new com.google.gson.reflect.TypeToken<List<Long>>() {
                }.getType());
            } else {
                log.error("getIdsBySegment param:{} errcode:{} errMsg:{}", GsonUtils.toDefaultJson(params),
                        response.getCode(), response.getErrormsg());
            }
        } catch (Exception e) {
            log.error("getIdsBySegment failed: key = " + key, e);
        } finally {
            closeClient(leafClient);
        }
        return null;
    }
}
