package cn.huanju.edu100.study.task;

import cn.huanju.edu100.redis.IRedisLockHandler;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.redis.cluster.lock.CompatableRedisClusterLockHandler;
import cn.huanju.edu100.study.model.tutor.TutorUserVideoLog;
import cn.huanju.edu100.study.service.tutor.TutorUserVideoLogService;
import cn.huanju.edu100.study.util.Constants;
import cn.huanju.edu100.study.util.NoneDevConditional;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Conditional(NoneDevConditional.class) // 非开发环境才注入该类
@Component
public class TutorUserVideoLogTask {
    Logger logger = LoggerFactory.getLogger(TutorUserVideoLogTask.class);

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    @Autowired
    private TutorUserVideoLogService tutorUserVideoLogService;

    @Autowired
    private JedisCluster localJedisCluster;

    private static int OVER_MINUTE = 2; // 2分钟往数据库写一次

    private static int OVER_MINUTE2 = 13;

    private int EXPIRE_TIME = 60 * 60 * 1;		// 1小时

//    @Scheduled(cron = "0/40 * * * * ?")
    @Scheduled(initialDelay = 30000, fixedRate = 40000)
    public void run() {
        try {
            logger.info("TutorUserVideoLogTask start.");
            //多线程竞争更新总表的分布式锁
            IRedisLockHandler lock = new CompatableRedisClusterLockHandler(localJedisCluster);//直接读单一的主redis
            if(lock.tryLock(RedisConsts.getTutorUserVideoLongKey(),15, TimeUnit.SECONDS)){//得到锁，则执行更新总表操作，获取锁的超时时间15s
                logger.info("TutorUserVideoLogTask enter");
                List<TutorUserVideoLog> lst = getOverDataFromRedis(OVER_MINUTE, OVER_MINUTE2);
                logger.info("TutorUserVideoLogTask enter.size:{}", lst.size());
                for (TutorUserVideoLog log : lst){
                    tutorUserVideoLogService.saveStudyVideoResult(log);
                    log.setDbUpdateTime(new Date());
                    saveDbUpdateTime(log.getKey(), log.getDbUpdateTime().getTime());
                }
            }
            lock.unLock(RedisConsts.getUserVideoLongKey());//解锁
            logger.info("TutorUserVideoLogTask end.");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("error", e);
        }
    }

    private String getDbUpdateKey(final String field){
        return "tu_v_log_db_t_" + field;
    }
    private Long getDbUpdateTime(final String field){
        String str = compatableRedisClusterClient.get(getDbUpdateKey(field));
        if(StringUtils.isNotBlank(str)){
            return Long.valueOf(str);
        }
        return null;
    }

    private Long getDataUpdateTime(final String field){
        String str = compatableRedisClusterClient.get("tutor_data_update_" + field);
        if(StringUtils.isNotBlank(str)){
            return Long.valueOf(str);
        }
        return null;
    }


    private void saveDbUpdateTime(final String field, final Long dbTime) throws DataAccessException {
        if(StringUtils.isNotBlank(field) && null != dbTime) {
            String strKey = getDbUpdateKey(field);
            compatableRedisClusterClient.setex(strKey,EXPIRE_TIME, String.valueOf(dbTime));
        }
    }


    /**
     * 获取缓存中多少秒都没有更新的数据
     * 如果一个用户的所有数据都没有更新了
     * 则从缓存中清除uid，下一次就不再更新数据库
     * @return
     */
    private List<TutorUserVideoLog> getOverDataFromRedis(final int minute, final  int overMinute){
        List<TutorUserVideoLog> lst = new ArrayList<TutorUserVideoLog>();
        TutorUserVideoLog log = null;
        try {
            Set<String> keyRedis = compatableRedisClusterClient.smembers(Constants.TUTOR_USER_VIDEO_LOG_KeyHqUserLog);
            Long dbSaveTime = null;
            Long dataUpdateTime = null;

            String[] data = null;
            if (keyRedis != null) {
                for (String field : keyRedis) {
//                    logger.info("enter: field:{}", field);

                    data = field.split("_");
                    if(data.length < 4){
                        logger.error("data error:{}", field);
                        compatableRedisClusterClient.srem(Constants.TUTOR_USER_VIDEO_LOG_KeyHqUserLog, field);    //删掉
                        continue;
                    }
                    dbSaveTime = getDbUpdateTime(field);
                    dataUpdateTime = getDataUpdateTime(field);
                    try {
                        log = tutorUserVideoLogService.getFromCache(Long.valueOf(data[0]), Long.valueOf(data[1]), Long.valueOf(data[2]), Long.valueOf(data[3]));
                    }catch(Exception e){
                        logger.error("get tutor user from cache error,:{}",field);
                        compatableRedisClusterClient.srem(Constants.TUTOR_USER_VIDEO_LOG_KeyHqUserLog, field);    //删掉
                        continue;
                    }
                    if(null == log){
                        compatableRedisClusterClient.srem(Constants.TUTOR_USER_VIDEO_LOG_KeyHqUserLog, field);    //删掉
                    }else{
                        if(null == dbSaveTime){ // 之前没有保存过，保存
                            lst.add(log);
                        }
                        else{
                            if(null == dataUpdateTime){
                                // 出错数据？没有更新时间
                                String recordKey = "tutor_data_update_" + field;
                                compatableRedisClusterClient.setex(recordKey, EXPIRE_TIME, String.valueOf(new Date().getTime()));	//记录数据更新时间
                                lst.add(log);
                            }else{
                                if(dbSaveTime - dataUpdateTime > overMinute * 60 * 1000){
                                    compatableRedisClusterClient.srem(Constants.TUTOR_USER_VIDEO_LOG_KeyHqUserLog, field);    //超时需要更新到数据库了，这个就删掉
//                            logger.info("{},clear in cache for update",srcField);
                                }else {
                                    if (new Date().getTime() - dbSaveTime > minute * 60 * 1000) { // 每minute分钟更新一次数据库
//                                logger.info("{},need to save in db", srcField);
                                        lst.add(log);
                                    }

                                }
                            }

                        }
                    }


                }
            }
        } catch (Exception ex) {
            logger.error("TutorUserVideoLogTask fail", ex);
        } finally {
        }
        return lst;
    }

    public void destroy() {
        // TODO Auto-generated method stub
    }

    public void init() {
        logger.info("TutorUserVideoLogTask init.");
    }
}
