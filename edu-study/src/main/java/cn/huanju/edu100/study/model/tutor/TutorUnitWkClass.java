package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 * 单元与微课班关联Entity
 * <AUTHOR>
 * @version 2017-12-28
 */
public class TutorUnitWkClass extends DataEntity<TutorUnitWkClass> {
	
	private static final long serialVersionUID = 1L;
	private Long unitId;		// unit_id
	private Long wkClassId;		// wk_class_id
	private Integer status;
	private List<Long> unitIdList;
	
	public TutorUnitWkClass() {
		super();
	}

	public TutorUnitWkClass(Long id){
		super(id);
	}

	public Long getUnitId() {
		return unitId;
	}

	public void setUnitId(Long unitId) {
		this.unitId = unitId;
	}
	
	public Long getWkClassId() {
		return wkClassId;
	}

	public void setWkClassId(Long wkClassId) {
		this.wkClassId = wkClassId;
	}

	public List<Long> getUnitIdList() {
		return unitIdList;
	}

	public void setUnitIdList(List<Long> unitIdList) {
		this.unitIdList = unitIdList;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
}