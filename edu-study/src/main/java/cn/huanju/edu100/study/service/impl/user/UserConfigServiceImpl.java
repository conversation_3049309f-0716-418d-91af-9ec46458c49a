package cn.huanju.edu100.study.service.impl.user;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.user.UserConfigDao;
import cn.huanju.edu100.study.model.user.UserConfig;
import cn.huanju.edu100.study.service.user.UserConfigService;
import com.google.common.collect.Lists;
import com.hqwx.study.dto.UserConfigDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class UserConfigServiceImpl extends BaseServiceImpl<UserConfigDao, UserConfig> implements UserConfigService {


    @Override
    public List<UserConfigDTO> findUserConfigByConfigKey(String configKey, Long uid) throws DataAccessException {
        UserConfig userConfig = new UserConfig();
        userConfig.setConfigKey(configKey);
        userConfig.setUid(uid);
        List<UserConfig> list = dao.findList(userConfig);
        List<UserConfigDTO> dtoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)){
            for (UserConfig config : list) {
                UserConfigDTO userConfigDTO = new UserConfigDTO();
                userConfigDTO.setId(config.getId());
                userConfigDTO.setConfigKey(config.getConfigKey());
                userConfigDTO.setConfigVal(config.getConfigVal());
                userConfigDTO.setUid(config.getUid());
                dtoList.add(userConfigDTO);
            }
        }
        return dtoList;
    }

    @Override
    public void saveUserConfig(Long uid, String configKey, String configVal) throws DataAccessException {
        List<UserConfigDTO> configList = this.findUserConfigByConfigKey(configKey, uid);
        if (CollectionUtils.isEmpty(configList)){
            UserConfig userConfig = new UserConfig();
            userConfig.setConfigKey(configKey);
            userConfig.setConfigVal(configVal);
            userConfig.setUid(uid);
            userConfig.setUpdateDate(new Date());
            dao.insert(userConfig);
        } else {
            UserConfigDTO userConfigDTO = configList.get(0);
            UserConfig userConfig = new UserConfig();
            userConfig.setId(userConfigDTO.getId());
            userConfig.setConfigVal(configVal);
            userConfig.setUpdateDate(new Date());
            dao.update(userConfig);
        }
    }
}
