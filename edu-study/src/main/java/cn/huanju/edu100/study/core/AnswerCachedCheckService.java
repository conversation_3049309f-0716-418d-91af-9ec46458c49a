package cn.huanju.edu100.study.core;

import cn.huanju.edu100.study.dao.QuestionAnswerStaticsDao;
import cn.huanju.edu100.study.entry.Service;
import cn.huanju.edu100.study.model.QuestionAnswerDetail;
import cn.huanju.edu100.study.service.QuestionAnswerStaticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AnswerCachedCheckService implements Service {
	private static final Logger LOG = LoggerFactory.getLogger(AnswerCachedCheckService.class);
	@Autowired
	private QuestionAnswerStaticsService staticsService;
	@Autowired
	private QuestionAnswerStaticsDao questionAnswerStaticsDao;

	@Override
	public void start() throws Exception {
		//不启动，只有在停止的时候，做下缓存检查
	}

	@Override
	public void stop() throws Exception {
		syncCachedData2DB();
	}

	private void syncCachedData2DB() {
		try {
			LOG.info("process shutdown, AnswerCachedCheckService start.....");

			List<QuestionAnswerDetail> questionAnswerDetailList = new ArrayList<QuestionAnswerDetail>();
			staticsService.drainToAllElement(questionAnswerDetailList);//将队列中剩余的元素都取出来，同步到DB中

			if (questionAnswerDetailList!=null && questionAnswerDetailList.size()>0) {

				LOG.info("process shutdown, AnswerCachedCheckService check, JVM cached has element, start sync2DB!");

				Map<String, QuestionAnswerDetail> newMap = new HashMap<String, QuestionAnswerDetail>();
				Map<String, QuestionAnswerDetail> updateMap = new HashMap<String, QuestionAnswerDetail>();

				List<QuestionAnswerDetail> newList = new ArrayList<QuestionAnswerDetail>();
				List<QuestionAnswerDetail> updateList = new ArrayList<QuestionAnswerDetail>();

				for (QuestionAnswerDetail questionAnswerDetail : questionAnswerDetailList) {
					Integer i = questionAnswerStaticsDao.isExsitByUidAndQuestionId(questionAnswerDetail);

					String key="-1";
					if (i!=null && i>0) {
						key = getKey(questionAnswerDetail.getUid(), questionAnswerDetail.getQuestionId());
						if (false == updateMap.containsKey(key)) {
							updateMap.put(key, questionAnswerDetail);//用map来过滤同一用户同一试题的重复记录
						}
					}else {
						key = getKey(questionAnswerDetail.getUid(), questionAnswerDetail.getQuestionId());
						if (false == newMap.containsKey(key)) {
							newMap.put(key, questionAnswerDetail);//用map来过滤同一用户同一试题的重复记录
						}
					}
				}
				//这里只是需要把缓存中的数据同步到DB即可
				newList.addAll(newMap.values());
				if (newList!=null && newList.size()>0) {
					Integer newDBNum = staticsService.addQuestionAnswerDetailBatch(newList);
					if (newDBNum<=0) {
						//TODO:.....
					}
				}

				updateList.addAll(updateMap.values());
				if (updateList!=null && updateList.size()>0) {
					Integer updateDBNum = questionAnswerStaticsDao.updateQuestionAnswerDetailBatch(updateList);
					if (updateDBNum<=0) {
						//TODO:.....
					}
				}
			}else {
				LOG.info("process shutdown, AnswerCachedCheckService check, JVM cached not element , good!");
			}
		} catch (Exception e) {
			LOG.error("process shutdown, AnswerCachedCheckService check, error!!",e);
		}

    }

	private String getKey(Long uid,Long questionId){
		StringBuilder sb = new StringBuilder();
		sb.append(uid);
		sb.append(":");
		sb.append(questionId);
		return sb.toString();
	}
}
