/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.StudentTaskDao;
import cn.huanju.edu100.study.model.StudentTask;
import cn.huanju.edu100.study.util.ValidateUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 学习计划DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudentTaskIbatisImpl extends CrudIbatisImpl2<StudentTask> implements StudentTaskDao {

    public StudentTaskIbatisImpl() {
        super("StudentTask");
    }

    @Override
    public StudentTask findLimitOneByTidAndUid(Long tid, Long uid) throws DataAccessException,BusinessException {
        if (ValidateUtils.isEmpty(tid)) {
            logger.error("param error, parameter tid is invalid,tid:{}", namespace, tid);
            throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,"param error,tid is invalid");
        }
        if (ValidateUtils.isEmpty(uid)) {
            logger.error("param error, parameter uid is invalid,uid:{}", namespace, uid);
            throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,"param error,uid is invalid");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("tid", tid);
            param.put("uid", uid);

            return (StudentTask) sqlMap.queryForObject("StudentTask.qryLimitOneByTidAndUid", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.tid:{} uid:{}", namespace, tid, uid, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public long insertAndGetGenerateId(StudentTask studentTask) throws DataAccessException {
        if (ValidateUtils.isEmpty(studentTask)) {
            logger.error("param error, parameter studentTask is null,studentTask:{}", namespace, studentTask);
            throw new DataAccessException("param error,studentTask is null");
        }

        try {
            SqlMapClient sqlMap = super.getMaster();// getSlave();
            Object id = sqlMap.insert("StudentTask.insertAndGetGenerateId", studentTask);
            if (id != null && id instanceof Long) {
                return (Long) id;
            } else {
                return 0;
            }
        } catch (SQLException e) {
            logger.error("list {} SQLException.studentTask:{}", namespace, studentTask, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public boolean updateState(Long id, Integer state) throws DataAccessException {
        if (ValidateUtils.isEmpty(id)) {
            logger.error("param error, parameter id is invalid,id:{}", namespace, id);
            throw new DataAccessException("param error,id is invalid");
        }
        if (ValidateUtils.isEmpty(state)) {
            logger.error("param error, parameter state is invalid,state:{}", namespace, state);
            throw new DataAccessException("param error,state is invalid");
        }

        try {
            SqlMapClient sqlMap = super.getMaster();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("id", id);
            param.put("state", state);

            int row = sqlMap.update("StudentTask.updateState", param);
            return row >= 1;
        } catch (SQLException e) {
            logger.error("list {} SQLException.id:{} state:{}", namespace, id, state, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }
}
