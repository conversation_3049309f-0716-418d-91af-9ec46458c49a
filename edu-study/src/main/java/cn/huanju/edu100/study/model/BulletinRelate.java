package cn.huanju.edu100.study.model;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 白名单列表Entity
 * <AUTHOR>
 * @version 2015-10-27
 */
public class BulletinRelate extends DataEntity<BulletinRelate> {
	
	private static final long serialVersionUID = 1L;
	private Long bulletinId;		// 告公id
	private Long relateId;		// 联关id
	
	public BulletinRelate() {
		super();
	}

	public BulletinRelate(Long id){
		super(id);
	}

	public Long getBulletinId() {
		return bulletinId;
	}

	public void setBulletinId(Long bulletinId) {
		this.bulletinId = bulletinId;
	}
	
	public Long getRelateId() {
		return relateId;
	}

	public void setRelateId(Long relateId) {
		this.relateId = relateId;
	}
	
}