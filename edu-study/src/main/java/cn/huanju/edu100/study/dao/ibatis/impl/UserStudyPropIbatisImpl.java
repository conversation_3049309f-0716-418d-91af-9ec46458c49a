package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserStudyPropDao;
import cn.huanju.edu100.study.model.UserStudyProp;
import cn.huanju.edu100.util.IdWorker;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by levyx on 2022/10/24.
 */
public class UserStudyPropIbatisImpl extends CrudIbatisImpl2<UserStudyProp> implements UserStudyPropDao {

    static final IdWorker idWorker = new IdWorker(0, 8);

    public UserStudyPropIbatisImpl() {
        super("UserStudyProp");
    }


    @Override
    public long insertUserStudyPropLog(UserStudyProp userStudyProp) throws DataAccessException {

        try {
            SqlMapClient sqlMap = super.getShardingMaster();

            Long newId = idWorker.nextId();

            userStudyProp.setId(newId);
            sqlMap.insert("UserStudyProp.insertSharding", userStudyProp);

            return newId;
        } catch (SQLException e) {
            logger.error("insert SQLException.uid:{}", userStudyProp, e);
            throw new DataAccessException("insert SQLException error");
        }
    }

    @Override
    public UserStudyProp selectOneMaster(Long uid, Long lessonId, String lessonType) throws DataAccessException {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("uid", uid);
        param.put("lessonType", lessonType);
        param.put("lessonId", lessonId);
        try {
            SqlMapClient sqlMap = super.getShardingMaster();
            return (UserStudyProp) sqlMap.queryForObject(
                    "UserStudyProp.selectOne", param);
        } catch (SQLException e) {
            logger.error("selectOne SQLException.param:{}", param, e);
            throw new DataAccessException("selectOne SQLException error");
        }
    }

    @Override
    public int updateUserStudyProp(UserStudyProp userStudyProp) throws DataAccessException {
        if(null == userStudyProp.getUid()){
            logger.error("updateUserStudyProp SQLException.uid null", userStudyProp);
            return -1;
        }
        try {

            SqlMapClient sqlMap = super.getShardingMaster();

            return sqlMap.update("UserStudyProp.updateUserStudyProp", userStudyProp);
        } catch (SQLException e) {
            logger.error("updateUserStudyProp SQLException.uid:{}", userStudyProp, e);
            throw new DataAccessException("updateUserStudyProp SQLException error");
        }
    }


}
