package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.persistence.model.HqFiledAnnotation;
import cn.huanju.edu100.study.util.Consts;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 商品服务Entity
 * 
 * <AUTHOR>
 * @version 2015-05-14
 */
public class Goods extends DataEntity<Goods> implements Cloneable{

	private static final long serialVersionUID = 1L;
	private String name; // name
	private String alias; // name
	private Long firstCategory; // first_category
	private Long secondCategory; // second_category
	private Long categoryId; // category_id
	private Integer type; // 0,表示录播课程，1.表示图书，2，表示试卷,3 表示直播课程
	private String keyWord; // key_word
	private Double oldPrice; // old_price
	private Double price; // price
	private Double costPrice; // 成本价
	private Double extPrice; // 额外价格，目前只有用在代报名的第三方支出价格
	private Double minPrice; //最低价格
	private Integer isAloneSale; // 0：否，1：是
	private Integer isCardBuy; // is_card_buy
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String bigPic; // big_pic
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String middlePic; // middle_pic
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String smallPic; // small_pic
	private String summary; // summary
	private Integer status; // 5：上架已拒绝 4：下架申请中 3：已下架 2：已上架 1：上架申请中 0：未上架
	private Date startTime; // start_time
	private Date endTime; // end_time
	private Long effectiveDays; // effective_days
	private Integer isShare; // is_share
	private Integer isShow; // 是否进app商城
	private Integer shareType; // share_type
	private String shareRatio; // share_ratio
	private Integer isMemberSales; // is_member_sales
	private Integer isPreStudy = 0;//是否支持试学 0：不支持，1：支持
	private Integer isFreeDelivery = 1;//是否免运费，0：免运费，1表示需要计算运费
	private Integer bMutiSalable = 1;   //是否可用多次购买，1：可用 0：不可用(产品中包含录播课、直播课)。
	private Integer oriWeight = 0;  // 商品实体商品的重量，包含不计算到运费的
	private Integer weight = 0;				// 商品下所有产品的重量和。 赠品不计重量，只计算必选的重量
	private Integer realNum = 0;				// 商品下所有产品的数目。 赠品不计，只计算必选的数目
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String content; // content
	@HqFiledAnnotation(filedGrpLevel = 3)
	private Date putonTime; // puton_time
	@HqFiledAnnotation(filedGrpLevel = 3)
	private Date pulloffTime; // pulloff_time
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String ip; // ip

	private Double realCostPrice;		// 成本价格
	private Double defaultRealCostPrice;		// 产品本身成本价格，可被套餐包含的
	private String statusList;		// 产品本身成本价格，可被套餐包含的

	private Integer relaType;// 关联商品类型
	private Integer selectType;//商品内容类型，0：可选，1必选
	private Collection<GoodsPairs> pairs;
	private Collection<GoodsProduct> products;
	private Collection<GoodsContent> contents;
	private Collection<GoodsRelate> relates;
	private Collection<Goods> goodsPairsList = Lists.newArrayList(); // 子表列表
	private Collection<Goods> goodsRelateList = Lists.newArrayList();
	private Collection<Goods> goodsContentList = Lists.newArrayList();
	private Collection<Product> goodsProductList = Lists.newArrayList();
	private Collection<GoodsExtCost> goodsExtCostList = Lists.newArrayList();
	private Map<String, String> goodsPropertesMap = new HashMap<String, String>();

	private Integer sourceFlag; //商品创建方式 1-产品排课 2-课程表排课 3-云私塾类型商品
	private Integer evaluationState;//入学测评试卷开关状态 0:关闭 1:打开
	private Long evaluationPaperId;//入学测评试卷资源id
	private Integer goodsCategory;//商品分类
	private Long tenantId;//网校id

	public Goods() {
		super();
	}

	public Goods(Long id) {
		super(id);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getFirstCategory() {
		return firstCategory;
	}

	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getWeight() {
		return weight;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	public Integer getRealNum() {
		return realNum;
	}

	public void setRealNum(Integer realNum) {
		this.realNum = realNum;
	}

	public String getKeyWord() {
		return keyWord;
	}

	public void setKeyWord(String keyWord) {
		this.keyWord = keyWord;
	}

	public Integer getOriWeight() {
		return oriWeight;
	}

	public void setOriWeight(Integer oriWeight) {
		this.oriWeight = oriWeight;
	}

	public Integer getbMutiSalable() {
		return bMutiSalable;
	}

	public void setbMutiSalable(Integer bMutiSalable) {
		this.bMutiSalable = bMutiSalable;
	}

	public Double getOldPrice() {
		return oldPrice;
	}

	public void setOldPrice(Double oldPrice) {
		this.oldPrice = oldPrice;
	}

	public Double getPrice() {
		return price;
	}

	public BigDecimal fetchPriceDecimal(){
		if(null == price){
			return BigDecimal.ZERO;
		}
		return BigDecimal.valueOf(price);
	}
	public void setPrice(Double price) {
		this.price = price;
	}

	public Integer getIsAloneSale() {
		return isAloneSale;
	}

	public void setIsAloneSale(Integer isAloneSale) {
		this.isAloneSale = isAloneSale;
	}

	public Integer getIsCardBuy() {
		return isCardBuy;
	}

	public void setIsCardBuy(Integer isCardBuy) {
		this.isCardBuy = isCardBuy;
	}

	public String getBigPic() {
		return bigPic;
	}

	public void setBigPic(String bigPic) {
		this.bigPic = bigPic;
	}

	public String getMiddlePic() {
		return middlePic;
	}

	public void setMiddlePic(String middlePic) {
		this.middlePic = middlePic;
	}

	public String getSmallPic() {
		return smallPic;
	}

	public void setSmallPic(String smallPic) {
		this.smallPic = smallPic;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Long getEffectiveDays() {
		return effectiveDays;
	}

	public void setEffectiveDays(Long effectiveDays) {
		this.effectiveDays = effectiveDays;
	}

	public Integer getIsShare() {
		return isShare;
	}

	public void setIsShare(Integer isShare) {
		this.isShare = isShare;
	}

	public Integer getShareType() {
		return shareType;
	}

	public void setShareType(Integer shareType) {
		this.shareType = shareType;
	}

	public String getShareRatio() {
		return shareRatio;
	}

	public void setShareRatio(String shareRatio) {
		this.shareRatio = shareRatio;
	}

	public Integer getIsMemberSales() {
		return isMemberSales;
	}

	public void setIsMemberSales(Integer isMemberSales) {
		this.isMemberSales = isMemberSales;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getPutonTime() {
		return putonTime;
	}

	public void setPutonTime(Date putonTime) {
		this.putonTime = putonTime;
	}

	public Date getPulloffTime() {
		return pulloffTime;
	}

	public void setPulloffTime(Date pulloffTime) {
		this.pulloffTime = pulloffTime;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Collection<Goods> getGoodsPairsList() {
		return goodsPairsList;
	}

	public void setGoodsPairsList(Collection<Goods> goodsPairsList) {
		this.goodsPairsList = goodsPairsList;
	}

	public Collection<Goods> getGoodsRelateList() {
		return goodsRelateList;
	}

	public void setGoodsRelateList(Collection<Goods> goodsRelateList) {
		this.goodsRelateList = goodsRelateList;
	}

	public Collection<Goods> getGoodsContentList() {
		return goodsContentList;
	}

	public void setGoodsContentList(Collection<Goods> goodsContentList) {
		this.goodsContentList = goodsContentList;
	}

	public Collection<Product> getGoodsProductList() {
		return goodsProductList;
	}

	public void setGoodsProductList(Collection<Product> goodsProductList) {
		this.goodsProductList = goodsProductList;
	}

	public Product getProductById(final Long prodId){
		if(null == prodId){
			return null;
		}
		if(CollectionUtils.isEmpty(goodsProductList)){
			return null;
		}
		for (Product product : goodsProductList) {
			if(prodId.equals(product.getId())){
				return product;
			}
		}
		return null;
	}
	public Integer getRelaType() {
		return relaType;
	}

	public void setRelaType(Integer relaType) {
		this.relaType = relaType;
	}

	public Collection<GoodsPairs> getPairs() {
		return pairs;
	}

	public void setPairs(Collection<GoodsPairs> pairs) {
		this.pairs = pairs;
	}

	public Collection<GoodsProduct> getProducts() {
		return products;
	}

	public void setProducts(Collection<GoodsProduct> products) {
		this.products = products;
	}

	public Collection<GoodsContent> getContents() {
		return contents;
	}

	public void setContents(Collection<GoodsContent> contents) {
		this.contents = contents;
	}

	public Collection<GoodsRelate> getRelates() {
		return relates;
	}

	public void setRelates(Collection<GoodsRelate> relates) {
		this.relates = relates;
	}

	/**
	 * 是否为有实物的商品
	 * @return
	 */
	public boolean isRealGood(){
		return Consts.ProductType.BOOK == this.type || Consts.ProductType.REAL_PAPER == this.type;
	}
	/**
	 * 是否可出售。
	 * 上架商品为可出售商品,下架申请和下架拒绝的都表示还在出售
	 * @desc
	 * <AUTHOR>
	 * @date 下午2:26:11
	 * @return
	 * @return 
	 * @throws
	*
	 */
	public boolean bAbleForSale(){
		return null != status && (Consts.GoodsStatus.ON_SALE == this.status 
				|| Consts.GoodsStatus.REQING_UNDER_SALE == this.status);
	}
	
	@Override  
    public Object clone() throws CloneNotSupportedException  
    {  
        return super.clone();  
    }

    public Integer getSelectType() {
        return selectType;
    }

    public void setSelectType(Integer selectType) {
        this.selectType = selectType;
    }

	public Double getMinPrice() {
		return minPrice;
	}

	public void setMinPrice(Double minPrice) {
		this.minPrice = minPrice;
	}

	public Integer getIsPreStudy() {
		return isPreStudy;
	}

	public void setIsPreStudy(Integer isPreStudy) {
		this.isPreStudy = isPreStudy;
	}

	public Integer getIsFreeDelivery() {
		return isFreeDelivery;
	}

	public void setIsFreeDelivery(Integer isFreeDelivery) {
		this.isFreeDelivery = isFreeDelivery;
	}

	/**
	 * 是否需要运费
	 * @return
	 */
	public boolean bNeedDevivery(){
		return null != this.isFreeDelivery && 0 == this.isFreeDelivery;
	}

    public Double getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(Double costPrice) {
        this.costPrice = costPrice;
    } 
    
    public void addProperties(final Collection<GoodsProperties> properties){
		if(CollectionUtils.isEmpty(properties)){
			return;
		}
		for (GoodsProperties property : properties) {
			goodsPropertesMap.put(property.getPropertyCode(), property.getPropertyValue());
		}
	}

	public String getPropertyValue(final String propertyCode){
		if(null == goodsPropertesMap || StringUtils.isBlank(propertyCode)){
			return null;
		}
		return goodsPropertesMap.get(propertyCode);
	}

	public Double getDefaultRealCostPrice() {
		return defaultRealCostPrice;
	}
	public Double fetchDefaultRealCostPrice() {
		if(null == defaultRealCostPrice){
			return 0d;
		}
		return defaultRealCostPrice;
	}

	public void setDefaultRealCostPrice(Double defaultRealCostPrice) {
		this.defaultRealCostPrice = defaultRealCostPrice;
	}

	public Double getRealCostPrice() {
		return realCostPrice;
	}

	public Double fetchRealCostPrice(){
		if(null == realCostPrice){
			return 0d;
		}

		return realCostPrice;
	}

	public void setRealCostPrice(Double realCostPrice) {
		this.realCostPrice = realCostPrice;
	}

	public Collection<GoodsExtCost> getGoodsExtCostList() {
		return goodsExtCostList;
	}

	public void setGoodsExtCostList(Collection<GoodsExtCost> goodsExtCostList) {
		this.goodsExtCostList = goodsExtCostList;
	}

	public Double getExtPrice() {
		return extPrice;
	}

	public void setExtPrice(Double extPrice) {
		this.extPrice = extPrice;
	}

    public String getStatusList() {
        return statusList;
    }

    public void setStatusList(String statusList) {
        this.statusList = statusList;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

	public Integer getEvaluationState() {
		return evaluationState;
	}

	public void setEvaluationState(Integer evaluationState) {
		this.evaluationState = evaluationState;
	}

	public Long getEvaluationPaperId() {
		return evaluationPaperId;
	}

	public void setEvaluationPaperId(Long evaluationPaperId) {
		this.evaluationPaperId = evaluationPaperId;
	}

	public Integer getGoodsCategory() {
		return goodsCategory;
	}

	public void setGoodsCategory(Integer goodsCategory) {
		this.goodsCategory = goodsCategory;
	}

	public Integer getSourceFlag() {
		return sourceFlag;
	}

	public void setSourceFlag(Integer sourceFlag) {
		this.sourceFlag = sourceFlag;
	}

	public Long getTenantId() {
		return tenantId;
	}

	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}
}