package cn.huanju.edu100.study.biz.enterschooltest;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.al.KnowledgeGraph;
import cn.huanju.edu100.study.model.enterschooltest.*;
import cn.huanju.edu100.study.model.questionBox.QuestionKnowledgeGraph;
import cn.huanju.edu100.study.model.questionBox.Questionbox;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserAnswerService;
import cn.huanju.edu100.study.service.UserAnswerSumService;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestPaperService;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestQuestionGroupService;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestQuestionKnowledgeGraphRelationService;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestQuestionService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 入学测评 流程组装-业务逻辑 类
 * zy
 * 2021-04-27 19：30
 */
@Component
public class EnterSchoolTestBiz {
    private static Logger logger = LoggerFactory.getLogger(EnterSchoolTestBiz.class);
    @Autowired
    private KnowledgeResource knowledgeResource;
    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;
    @Autowired
    private EnterSchoolTestPaperService enterSchoolTestPaperService;
    @Autowired
    private EnterSchoolTestQuestionService enterSchoolTestQuestionService;
    @Autowired
    private EnterSchoolTestQuestionGroupService enterSchoolTestQuestionGroupService;
    @Autowired
    private EnterSchoolTestQuestionKnowledgeGraphRelationService enterSchoolTestQuestionKnowledgeGraphRelationService;
    @Autowired
    private UserAnswerService userAnswerService;
    @Autowired
    private UserAnswerSumService userAnswerSumService;
    private final String ENTER_SCHOOL_PAPER = "enter_school_paper_%s_%s";

    /**
     * 出题
     *
     * @param uid
     * @param categoryId
     * @return
     * @throws DataAccessException
     */
    public EnterSchoolTestPaper assembleEnterSchoolePaper(Long uid, Long categoryId) throws DataAccessException {
        EnterSchoolTestPaper cpaper = null;
        String key = String.format(ENTER_SCHOOL_PAPER, uid, categoryId);
//        String paperStr = compatableRedisClusterClient.get(key);
//        if (StringUtils.isNotBlank(paperStr)) {
//            cpaper = GsonUtil.getGenericGson().fromJson(paperStr, EnterSchoolTestPaper.class);
//            return cpaper;
//        }

        //组卷 先找 后台配置的入学类型试卷
        List<Paper> papers = knowledgeResource.klg_getPaperListByCategoryId(categoryId,9);
        //组装试题 集合
        Set<Question> assembleQuestions = Sets.newHashSet();
        //入学测评试卷
        papers = papers.stream().filter(paper -> Objects.nonNull(paper.getType()) && paper.getType().equals(9) && paper.getState() == 1).collect(Collectors.toList());
        //取卷
        if (papers != null && papers.size() > 0) {
            if (papers.size() > 1) {
                //如果有多个有效的入学测评试卷打一条错误日志
                logger.error("assembleEnterSchoolePaper error exist more than one paper:{}", GsonUtil.toJson(papers));
            }
            cpaper = relationPaperAssembleAndSave(papers.get(0), assembleQuestions, uid, categoryId);
        } else {//未配置入学测评试卷 需要自己组卷
            cpaper = selfPaperAssembleAndSave(assembleQuestions, uid, categoryId);
        }
        //装题
        if (cpaper != null) {
            if (CollectionUtils.isNotEmpty(cpaper.getEnterSchoolTestQuestions())) {
                Map<Long, Question> questionMapping = assembleQuestions.stream().collect(Collectors.toMap(Question::getId, Function.identity(), (o, n) -> n));
                List<EnterSchoolTestQuestion> enterQuestions = cpaper.getEnterSchoolTestQuestions();

                List<Question> questions = enterQuestions.stream().map(enterQuestion -> questionMapping.get(enterQuestion.getQuestionId())).filter(Objects::nonNull).distinct().collect(Collectors.toList());

                cpaper.setQuestionList(questions);
                cpaper.setQuestionTotal(questions.size());
            }
           // compatableRedisClusterClient.setex(key, 60 * 60 * 24 * 30, GsonUtil.getGenericGson().toJson(cpaper));
        }
        return cpaper;
    }

    /**
     * 测评报告
     *
     * @param uid
     * @param paperId
     * @param userAnswerId
     * @return
     * @throws DataAccessException
     */
    public EnterSchoolTestReport enterSchoolTestReport(Long uid, Long paperId, Long userAnswerId) throws DataAccessException {
        //取试卷
        EnterSchoolTestPaper cpaper = enterSchoolTestPaperService.get(paperId);
        if (cpaper == null) {
            return null;
        }
        //取用户答题结果
        Map<String, Object> qryParam = new HashMap<String, Object>();
        qryParam.put("id", userAnswerId);
        qryParam.put("uid", uid);

        UserAnswer answer = userAnswerService.getShardingById(qryParam);
        if (answer == null || !answer.getPaperId().equals(paperId) || !answer.getUid().equals(uid) || answer.getState() < 2) {
            return null;
        }

        EnterSchoolTestReport report = new EnterSchoolTestReport();

        report.setPaperId(paperId);

        report.setUid(uid);

        report.setCategoryId(cpaper.getCategoryId());

        report.setUserAnswerId(userAnswerId);
        //答题耗时
        report.setTotalTimes(getMinute(answer));
        List<Long> orderQuestionIdList = Lists.newArrayList();
        //总题数
        report.setQuestionTotal(getQuestionCount(paperId, orderQuestionIdList));
        //查询用户答题详情
        List<UserAnswerSum> sums = userAnswerSumService.findAnswerSumAndDetail(uid, userAnswerId, null);
        //每道题答对答错的映射
        Map<Long, Integer> questionRightFlagMap = Maps.newHashMap();
        Map<Long, Integer> topicRightFlagMap = Maps.newHashMap();
        Map<Long,List<Long>> qidTidMapping = Maps.newHashMap();
        //总答对题数
        report.setRightQuestionTotal(getRightCount(sums, questionRightFlagMap,topicRightFlagMap,qidTidMapping));
        //封装错题集合 和 全部题目集合
        buildWrongQuestionIds(report, questionRightFlagMap,topicRightFlagMap, orderQuestionIdList);
        //知识点和题目列表的映射
        Map<Long, EnterSchoolTestKnowledgeRoport> knowledgeRoportMap = Maps.newHashMap();
        //组装知识点和题目列表的映射内容
        buildKnowledgeReports(report, paperId, knowledgeRoportMap, qidTidMapping);
        //组装学习建议
        report.setStudyAdvice(getStudyAdvice(cpaper, report.getRightQuestionTotal(), report.getAllTopicIds().size()-report.getNoTopicIds().size()));
        //组装学习建议内 的 推荐课程
        report.setGoodsId(0L);

        return report;
    }

    /**
     * 学习建议：
     * <p>
     * 0-50%：您的掌握程度较弱，建议您制定合理的计划与安排，回归课程、重抓基础、练习巩固，待掌握薄弱知识点后，再通过模拟考试与真题演练做系统检测。
     * <p>
     * 51%-80% ：您的掌握程度一般，建议您注重积累与运用，学习专题课程，进行知识点的查漏补缺，有针对性的进行复习，之后再进行综合测试。
     * <p>
     * 81-100%：您的掌握程度较好，建议您做归纳总结、巩固提高。可以利用“错题本”，不断纠正既往错误。扎实做好模拟训练，稳中求胜。
     */
    public String getStudyAdvice(EnterSchoolTestPaper cpaper, int rightCount, int allCount) {
        String advice = "";
        //当前为 自组卷，取默认文案，若为后台试卷，则需要调用接口去查
        if (cpaper.getRelationFlag() != null && cpaper.getRelationFlag() == 0) {
            if (rightCount <= 0 || allCount <= 0) {
                advice = "您的掌握程度较弱，建议您制定合理的计划与安排，回归课程、重抓基础、练习巩固，待掌握薄弱知识点后，再通过模拟考试与真题演练做系统检测。";
            } else {
                float bl = (float) rightCount / allCount;
                if (bl <= 0.5) {
                    advice = "您的掌握程度较弱，建议您制定合理的计划与安排，回归课程、重抓基础、练习巩固，待掌握薄弱知识点后，再通过模拟考试与真题演练做系统检测。";
                } else if (bl <= 0.8 && bl > 0.5) {
                    advice = "您的掌握程度一般，建议您注重积累与运用，学习专题课程，进行知识点的查漏补缺，有针对性的进行复习，之后再进行综合测试。";
                } else {
                    advice = "您的掌握程度较好，建议您做归纳总结、巩固提高。可以利用“错题本”，不断纠正既往错误。扎实做好模拟训练，稳中求胜。";
                }
            }
        }
        return advice;
    }

    /**
     * @param uid
     * @return  返回该用户没有做过入学测评的科目集合
     * @throws DataAccessException
     */
    public List<Long> checkUserEnterSchoolTestFlag(Long uid,List<Long> categoryIds) throws DataAccessException {

        String key = "ENTER_SCHOOL_TEST_SHOW_FLAG_" + uid;
        //取出不在需要展示的科目id
        Set<String> noNeedCategoryIds = compatableRedisClusterClient.smembers(key);
        if (CollectionUtils.isNotEmpty(noNeedCategoryIds)) {
            //移除掉不在需要展示的科目
            categoryIds.removeIf(categoryId->noNeedCategoryIds.contains(categoryId.toString()));
        }

        if (CollectionUtils.isEmpty(categoryIds)) {
            return categoryIds;
        }

        //查数据库过滤掉不需要展示的科目,获取到最终要展示的
        List<Long> lastRemain = checkEnterSchoolTestFlagDB(uid,categoryIds);
        if (categoryIds.size() != lastRemain.size()) {
            //移除掉最终需要展示的，剩下不需要展示的添加到集合
            categoryIds.removeAll(lastRemain);
            String[] shouldAdd = categoryIds.stream().map(String::valueOf).toArray(String[]::new);
            compatableRedisClusterClient.sadd(key, shouldAdd);
            // 缓存12小时
            compatableRedisClusterClient.expire(key, 60 * 60 * 12);
        }
        return lastRemain;
    }

    public List<Long> checkEnterSchoolTestFlagDB(Long uid,List<Long> categoryIds) {
        return categoryIds.parallelStream().filter(categoryId->{
            try {
                EnterSchoolTestPaper param = new EnterSchoolTestPaper();
                param.setCategoryId(categoryId);
                List<EnterSchoolTestPaper> papers = enterSchoolTestPaperService.findList(param);
                if (CollectionUtils.isEmpty(papers)) {
                    return true;
                }
                //检验是否已完成过入学测评
                UserAnswer queryAnswer = new UserAnswer();
                queryAnswer.setUid(uid);
                queryAnswer.setPaperIds(papers.stream().map(EnterSchoolTestPaper::getId).collect(Collectors.toList()));
                queryAnswer.setObjType(UserAnswer.ObjType.TK_ENTERSCHOOLTEST);
                List<UserAnswer> answers = userAnswerService.adminstudyfindList(queryAnswer);

                if (CollectionUtils.isEmpty(answers)) {
                    //如果没有做过入学测评
                    //科目下做过的（题库）题目数量不超过20
                    List<Questionbox> questionBoxList = knowledgeResource.getQuestionBoxList(categoryId);
                    if (CollectionUtils.isEmpty(questionBoxList)) {
                        return true;
                    }
                    List<Long> boxIds = questionBoxList.stream().map(Questionbox::getId).distinct().collect(Collectors.toList());

                    Long questionNum = userAnswerService.countTikuQuestionNum(uid, boxIds);
                    logger.info("checkEnterSchoolTestFlagDB questionNum:uid:{},categoryId:{},questionNum:{}",uid,categoryId,questionNum);
                    return !(questionNum >= 20);
                }
                return false;
            } catch (Exception e) {
                logger.error("checkEnterSchoolTestFlagDB error:uid:{},categoryIds:{},exception:", uid, categoryId, e);
                return false;
            }
        }).collect(Collectors.toList());
    }

    public void buildWrongQuestionIds(EnterSchoolTestReport report, Map<Long, Integer> questionRightFlagMap, Map<Long, Integer> topicRightFlagMap,List<Long> orderQuestionIdList) {
        List<Long> allQuestionIds = Lists.newArrayList();
        List<Long> wrongQuestionIds = Lists.newArrayList();
        List<Long> rightQuestionIds = Lists.newArrayList();
        List<Long> noQuestionIds = Lists.newArrayList();
        List<Long> allTopicIds = Lists.newArrayList();
        List<Long> wrongTopicIds = Lists.newArrayList();
        List<Long> rightTopicIds = Lists.newArrayList();
        List<Long> noTopicIds = Lists.newArrayList();
        for (Long qid : questionRightFlagMap.keySet()) {
            allQuestionIds.add(qid);
            if (questionRightFlagMap.get(qid) != null && questionRightFlagMap.get(qid) == 1) {
                wrongQuestionIds.add(qid);
            }
            if (questionRightFlagMap.get(qid) != null && questionRightFlagMap.get(qid) == 2) {
                rightQuestionIds.add(qid);
            }
            if (questionRightFlagMap.get(qid) != null && questionRightFlagMap.get(qid) == 0) {
                noQuestionIds.add(qid);
            }
        }

        topicRightFlagMap.forEach((topicId,flag)->{
            allTopicIds.add(topicId);
            if (flag == 1) {
                wrongTopicIds.add(topicId);
            } else if (flag == 2) {
                rightTopicIds.add(topicId);
            } else {
                noTopicIds.add(topicId);
            }
        });


        report.setNoQuestionIds(noQuestionIds);
        report.setRightQuestionIds(rightQuestionIds);
        sortQuestionId(orderQuestionIdList, allQuestionIds);
        report.setAllQuestionIds(allQuestionIds);
        report.setWrongQuestionIds(wrongQuestionIds);
        report.setAllTopicIds(allTopicIds);
        report.setWrongTopicIds(wrongTopicIds);
        report.setNoTopicIds(noTopicIds);
        report.setRightTopicIds(rightTopicIds);
    }

    public void buildKnowledgeReports(EnterSchoolTestReport report,Long paperId, Map<Long, EnterSchoolTestKnowledgeRoport> knowledgeRoportMap,
                                       Map<Long, List<Long>> qidTidMapping) throws DataAccessException {
        EnterSchoolTestQuestionKnowledgeGraphRelation queryRelation = new EnterSchoolTestQuestionKnowledgeGraphRelation();
        queryRelation.setPaperId(paperId);
        List<EnterSchoolTestQuestionKnowledgeGraphRelation> relations = enterSchoolTestQuestionKnowledgeGraphRelationService.findList(queryRelation);
        if (relations != null && relations.size() > 0) {
            List<Long> rightQuestionIds = report.getRightQuestionIds();
            List<Long> rightTopicIds = report.getRightTopicIds();
            List<Long> wrongQuestionIds = report.getWrongQuestionIds();
            List<Long> wrongTopicIds = report.getWrongTopicIds();
            for (EnterSchoolTestQuestionKnowledgeGraphRelation relation : relations) {
                EnterSchoolTestKnowledgeRoport knowledgeRoport = knowledgeRoportMap.get(relation.getKnowledgeGraphId());
                if (knowledgeRoport == null) {
                    knowledgeRoport = new EnterSchoolTestKnowledgeRoport();
                    knowledgeRoport.setKnowledgeGraphId(relation.getKnowledgeGraphId());
                    knowledgeRoport.setRightQuestionTotal(0);
                    knowledgeRoport.setAnsweredQuestionTotal(0);
                    knowledgeRoport.setRightTopicTotal(0);
                    knowledgeRoport.setAnsweredTopicTotal(0);
                    Set<Long> kqIds = Sets.newHashSet();
                    knowledgeRoport.setQuestionIds(kqIds);
                    //查询 知识点图谱的 名称 章名称 节名称
                    KnowledgeGraphChapterInfoVo infoVo = knowledgeResource.klg_getKnowledgeGraphOneChapterInfo(relation.getKnowledgeGraphId());
                    if (infoVo != null) {
                        knowledgeRoport.setKnowledgeName(infoVo.getKnowledgeGraphName());
                        knowledgeRoport.setChapterName(infoVo.getChapterName());
                        knowledgeRoport.setChildChapterName(infoVo.getChildChapterName());
                    }
                }
                Long questionId = relation.getQuestionId();
                knowledgeRoport.getQuestionIds().add(questionId);
                if (rightQuestionIds.contains(questionId)) {
                    knowledgeRoport.setRightQuestionTotal(knowledgeRoport.getRightQuestionTotal() + 1);
                }
                if (rightQuestionIds.contains(questionId) || wrongQuestionIds.contains(questionId)) {
                    knowledgeRoport.setAnsweredQuestionTotal(knowledgeRoport.getAnsweredQuestionTotal() + 1);
                }

                List<Long> topicIds = qidTidMapping.get(relation.getQuestionId());

                if (CollectionUtils.isNotEmpty(topicIds)) {
                    knowledgeRoport.getTopicIds().addAll(topicIds);
                    for (Long topicId : topicIds) {
                        if (rightTopicIds.contains(topicId)) {
                            knowledgeRoport.setRightTopicTotal(knowledgeRoport.getRightTopicTotal()+1);
                        }
                        if (rightTopicIds.contains(topicId) || wrongTopicIds.contains(topicId)) {
                            knowledgeRoport.setAnsweredTopicTotal(knowledgeRoport.getAnsweredTopicTotal() + 1);
                        }
                    }
                }

                knowledgeRoportMap.put(relation.getKnowledgeGraphId(), knowledgeRoport);
            }
        }

        report.setKnowledgeRoports(Lists.newArrayList(knowledgeRoportMap.values()));
    }

    public Integer getRightCount(List<UserAnswerSum> sums, Map<Long, Integer> questionRightFlagMap,
                                 Map<Long, Integer> TopicRightFlagMap,Map<Long,List<Long>> qidTidMapping) {
        int rightCount = 0, rightTopicCount = 0;
        if (CollectionUtils.isEmpty(sums)) {
            return rightTopicCount;
        }
        for (UserAnswerSum sum : sums) {
            Integer flag = 0;
            if (sum.getIsRight() != null) {
                if (sum.getIsRight() == UserAnswerDetail.IsRight.RIGHT) {
                    rightCount++;
                    flag = 2;
                } else if (sum.getIsRight() == UserAnswerDetail.IsRight.NOT_ANSWER) {
                    flag = 0;
                } else {
                    flag = 1;
                }
            }
            questionRightFlagMap.put(sum.getQuestionId(), flag);

            List<UserAnswerDetail> details = sum.getAnswerDetail();
            List<Long> tids = Lists.newArrayList();
            for (UserAnswerDetail detail:details) {
                Integer topicFlag = 0;
                if (Objects.nonNull(detail.getIsRight())) {
                    if (detail.getIsRight() == UserAnswerDetail.IsRight.RIGHT) {
                        rightTopicCount++;
                        topicFlag = 2;
                    } else if (detail.getIsRight() == UserAnswerDetail.IsRight.NOT_ANSWER) {
                        topicFlag = 0;
                    } else {
                        topicFlag = 1;
                    }
                }
                TopicRightFlagMap.put(detail.getTopicId(), topicFlag);
                tids.add(detail.getTopicId());
            }
            qidTidMapping.put(sum.getQuestionId(), tids);
        }
        return rightTopicCount;
    }

    public Integer getQuestionCount(Long paperId, List<Long> orderQuestionIdList) throws DataAccessException {
        int count = 0;
        EnterSchoolTestQuestion queryQuestion = new EnterSchoolTestQuestion();
        queryQuestion.setPaperId(paperId);
        List<EnterSchoolTestQuestion> questions = enterSchoolTestQuestionService.findList(queryQuestion);
        if (questions != null && questions.size() > 0) {
            count = questions.size();
            for (EnterSchoolTestQuestion question : questions) {
                orderQuestionIdList.add(question.getQuestionId());
            }
        }
        return count;
    }

    public Integer getMinute(UserAnswer answer) {
        if (answer.getUsetime() != null && answer.getUsetime() > 0L) {
            Long minute = answer.getUsetime() % 60 > 0 ? (answer.getUsetime() / 60 + 1L) : answer.getUsetime() / 60;
            return minute.intValue();
        }
        return 1;
    }


    /**
     * 自己组卷 并 存储
     *
     * @param assembleQuestions
     * @param uid
     * @param categoryId
     * @return
     */
    public EnterSchoolTestPaper selfPaperAssembleAndSave(Set<Question> assembleQuestions, Long uid, Long categoryId) throws DataAccessException {
        //试卷可复用 先查卷
        EnterSchoolTestPaper historyPaper = getHistoryPaper(categoryId, assembleQuestions,null);
        if (Objects.nonNull(historyPaper)) {
            return historyPaper;
        }
        //题目id 和 知识点图谱id集合 映射map
        Map<Long, List<Long>> questionKnowledgeMap = Maps.newHashMap();
        //组题
        assebleQuestionHandle(categoryId, assembleQuestions, questionKnowledgeMap);

        //组装试题组
        Map<String, List<EnterSchoolTestQuestion>> groupMap = buildTreeGroupMap(assembleQuestions);

        return enterSchoolTestPaperService.insertEnterSchoolTest(uid, categoryId, groupMap, questionKnowledgeMap);

    }

    public Map<Long, List<Long>> getKnowledgeGraphQuestionMap(Long categoryId, List<Long> questionIds) {
        List<QuestionKnowledgeGraph> vos = knowledgeResource.getQuestionKnowledgeGraphByIdList(null,questionIds);
        if (vos == null || vos.size() <= 0) {
            return Maps.newHashMap();
        }
        Map<Long, List<Long>> questionKnowledgeGraphIdsMap = Maps.newHashMap();
        for (QuestionKnowledgeGraph vo : vos) {
            List<Long> knowledgeGraphIds = questionKnowledgeGraphIdsMap.get(vo.getQuestionId());
            if (knowledgeGraphIds == null) {
                knowledgeGraphIds = Lists.newArrayList();
            }
            if (vo.getKnowledgeGraphId() != null && !knowledgeGraphIds.contains(vo.getKnowledgeGraphId())) {
                knowledgeGraphIds.add(vo.getKnowledgeGraphId());
            }
            questionKnowledgeGraphIdsMap.put(vo.getQuestionId(), knowledgeGraphIds);
        }
        return questionKnowledgeGraphIdsMap;
    }

    public List<Question> getQuestionByIds(List<EnterSchoolTestQuestion> questions) throws DataAccessException {
        List<Long> questionIds = Lists.newArrayList();
        for (EnterSchoolTestQuestion qn : questions) {
            questionIds.add(qn.getQuestionId());
        }
        return knowledgeResource.getQuestionByIds(questionIds);
    }

    /**
     * 后台已配置入学测评试卷 取id冗余到 入学测评试卷表 若有多条，目前取第一条
     *
     * @param paper
     * @return
     */
    public EnterSchoolTestPaper relationPaperAssembleAndSave(Paper paper, Set<Question> assembleQuestions, Long uid, Long categoryId) throws DataAccessException {

        EnterSchoolTestPaper cpaper = getHistoryPaper(categoryId, assembleQuestions, paper.getId());
        if (Objects.nonNull(cpaper)) {
            return cpaper;//如果已经有了入学测评，就不需要生成了
        }
        //题目id集合 为了取知识点用
        List<Long> questionIds = Lists.newArrayList();
        //原group map
        Map<String, QuestionGroup> groupSourceMap = Maps.newHashMap();
        //组装 题组map 装填题目id集合（主要目的）
        Map<String, List<EnterSchoolTestQuestion>> groupMap = buildRelationGroupMap(paper, assembleQuestions, questionIds, groupSourceMap);
        Map<Long, List<Long>> questionKnowledgeMap = getKnowledgeGraphQuestionMap(categoryId, questionIds);
        //存储
        return enterSchoolTestPaperService.insertRelationEnterSchoolTest(uid, paper.getId(), categoryId, groupMap, groupSourceMap, questionKnowledgeMap);
    }

    public EnterSchoolTestPaper getHistoryPaper(Long categoryId, Set<Question> assembleQuestions,Long adminPaperId) throws DataAccessException {
        EnterSchoolTestPaper queryPaper = new EnterSchoolTestPaper();
        queryPaper.setCategoryId(categoryId);
        queryPaper.setRelationPaperId(adminPaperId);
        queryPaper.getPage().setOrderBy("a.id desc");
        queryPaper.getPage().setPageSize(1);
        List<EnterSchoolTestPaper> papers = enterSchoolTestPaperService.findEnterSchoolTestPaperList(queryPaper);
        if (papers == null || papers.size() <= 0) {
            //无卷 直接返回
            return null;
        }
        //解析旧卷，判断是否和本次查出试卷一致，旧卷可能存在多套，按时间倒序筛选,如果paper的状态为关闭
        EnterSchoolTestPaper historyPaper = null;
        for (EnterSchoolTestPaper paper : papers) {
            Paper paperInfo = knowledgeResource.getPaperInfoById(paper.getRelationPaperId());
            if (paperInfo == null || paperInfo.getState() == Consts.PAPER_STATE.INVALID) {
                continue;
            }
            historyPaper = paper;
        }
        if (historyPaper == null) {
            return null;
        }

        EnterSchoolTestQuestion queryQuestion = new EnterSchoolTestQuestion();
        queryQuestion.setPaperId(historyPaper.getId());
        List<EnterSchoolTestQuestion> questions = enterSchoolTestQuestionService.findList(queryQuestion);
        //取题
        historyPaper.setEnterSchoolTestQuestions(questions);
        assembleQuestions.addAll(getQuestionByIds(questions));
        return historyPaper;
    }

    public Map<String, List<EnterSchoolTestQuestion>> buildRelationGroupMap(Paper paper, Set<Question> questions, List<Long> questionIds, Map<String, QuestionGroup> groupSourceMap) {
        //查询试卷下的试题信息
        List<QuestionGroup> questionGroups = knowledgeResource.klg_getQuestionByPaperId(paper.getId());
        if (questionGroups == null || questionGroups.size() <= 0) {
            return Maps.newHashMap();
        }
        Map<String, List<EnterSchoolTestQuestion>> groupMap = Maps.newHashMap();
        for (QuestionGroup group : questionGroups) {
            if (group == null || group.getQuestionList() == null || group.getQuestionList().size() <= 0) {
                continue;
            }
            questions.addAll(group.getQuestionList());
            //组装题组 map
            String groupMapKey = group.getId() + "";
            QuestionGroup sourcegroup = new QuestionGroup(null, group.getGroupName(), group.getGroupType(), null, null, group.getSeq(), group.getQuestionTotal(), null);
            groupSourceMap.put(groupMapKey, sourcegroup);
            List<EnterSchoolTestQuestion> groupQuestionList = groupMap.get(groupMapKey);
            if (groupQuestionList == null) {
                groupQuestionList = Lists.newArrayList();
            }
            for (Question question : group.getQuestionList()) {
                EnterSchoolTestQuestion enterSchoolTestQuestion = new EnterSchoolTestQuestion();
                enterSchoolTestQuestion.setQuestionId(question.getId());
                groupQuestionList.add(enterSchoolTestQuestion);
                questionIds.add(question.getId());
            }
            groupMap.put(groupMapKey, groupQuestionList);
        }
        return groupMap;
    }

    /**
     * 组装 排序后 的试题组
     * 并收集 试题id集合
     *
     * @param assembleQuestions
     * @return
     */
    public Map<String, List<EnterSchoolTestQuestion>> buildTreeGroupMap(Set<Question> assembleQuestions) {
        //题组map 兼顾题目组排序
        Map<String, List<EnterSchoolTestQuestion>> groupMap = Maps.newTreeMap();
        for (Question question : assembleQuestions) {
            //组装已选出试题的id集合 以备获取知识点
            //组装题组 map
            String groupMapKey = question.getQtype() + "";
            List<EnterSchoolTestQuestion> groupQuestionList = groupMap.get(groupMapKey);
            if (groupQuestionList == null) {
                groupQuestionList = Lists.newArrayList();
            }
            EnterSchoolTestQuestion enterSchoolTestQuestion = new EnterSchoolTestQuestion();
            enterSchoolTestQuestion.setQuestionId(question.getId());
            groupQuestionList.add(enterSchoolTestQuestion);
            groupMap.put(groupMapKey, groupQuestionList);
        }
        return groupMap;
    }

    /**
     * 兜底 出题流程
     * 组题流程，先根据知识点取题，当知识点取题不足15道时，再根据科目id直接取题
     *
     * @param categoryId
     * @param assembleQuestions
     */
    public void assebleQuestionHandle(Long categoryId, Set<Question> assembleQuestions, Map<Long, List<Long>> questionKnowledgeMap) {
        //已选择的题目id集合
//        List<Long> filterQuestionIds = Lists.newArrayList();
        //先查询知识点
        List<KnowledgeGraph> knowledgeGraphs = knowledgeResource.klg_getKnowledgeGraphByParam(categoryId);
        if (knowledgeGraphs != null && knowledgeGraphs.size() > 0) {
            int knowledgeGraphCount = 0;
            //随机选取5个知识点，小于等于5个 取全部
            for (KnowledgeGraph graph : knowledgeGraphs) {
                List<Long> assembleKnowledgeGraphs = Lists.newArrayList();
                assembleKnowledgeGraphs.add(graph.getId());
//            if (knowledgeGraphs.size() > 5) {
//                for (int i = 0; i < 5; i++) {
//                    int randomNum = RandomUtils.nextInt(knowledgeGraphs.size());
//                    KnowledgeGraph knowledgeGraph = knowledgeGraphs.get(randomNum);
//                    assembleKnowledgeGraphs.add(knowledgeGraph.getId());
//                    knowledgeGraphs.remove(knowledgeGraph);
//                }
//            } else {
//                for (KnowledgeGraph graph : knowledgeGraphs){
//                    assembleKnowledgeGraphs.add(graph.getId());
//                }
//            }

                //根据知识点 每个知识点随机抽取3道题 每个知识点下不足3道题，有几道取几道
                List<Question> questions = knowledgeResource.klg_getQuestionByKnowleldgeGraphIds(assembleKnowledgeGraphs, 3, categoryId);
                if (questions != null && questions.size() > 0) {
                    for (Question question : questions) {
//                        filterQuestionIds.add(question.getId());
                        List<Long> knowledgeGraphIds = questionKnowledgeMap.get(question.getId());
                        if (knowledgeGraphIds == null) {
                            knowledgeGraphIds = Lists.newArrayList();
                        }
                        knowledgeGraphIds.add(graph.getId());
                        questionKnowledgeMap.put(question.getId(), knowledgeGraphIds);
                    }
                    assembleQuestions.addAll(questions);
                    knowledgeGraphCount++;
                }
                if (knowledgeGraphCount >= 5 && assembleQuestions.size() >10) {
                    //取到5个知识点后，终止循环
                    break;
                }
            }
        }
        //如果上面根据知识点组装的题目 最终还是少于15道，则根据科目去题库随机取15到题（排除上面已选题目）
//        if (assembleQuestions.size()<15){
//            List<Question> directQuestions = knowledgeResource.klg_getQuestionByCategoryId(categoryId,15,filterQuestionIds);
//            if (directQuestions!=null && directQuestions.size()>0){
//                assembleQuestions.addAll(directQuestions);
//            }
//        }
    }

    public void sortQuestionId(List<Long> orderList, List<Long> targetList) {
        targetList.sort(((o1, o2) -> {
            int io1 = orderList.indexOf(o1);
            int io2 = orderList.indexOf(o2);
            return io1 - io2;
        }));
    }

    public void sortQuestion(List<EnterSchoolTestQuestion> orders, List<Question> targetList) {
        List<Long> orderList = getOrderList(orders);
        targetList.sort(((o1, o2) -> {
            int io1 = orderList.indexOf(o1.getId());
            int io2 = orderList.indexOf(o2.getId());
            return io1 - io2;
        }));
    }

    public List<Long> getOrderList(List<EnterSchoolTestQuestion> orders) {
        List<Long> orderList = Lists.newArrayList();
        for (EnterSchoolTestQuestion question : orders) {
            orderList.add(question.getQuestionId());
        }
        return orderList;
    }

}
