package cn.huanju.edu100.study.service.homework.comment;

import cn.huanju.edu100.study.model.homework.comment.UserHomeworkComment;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hqwx.study.dto.query.UserHomeworkCommentListQuery;
import com.hqwx.study.dto.query.UserHomeworkCommentQuery;

import java.util.List;

public interface UserHomeworkCommentService extends IService<UserHomeworkComment> {
    List<UserHomeworkComment> getLastHomeworkComment(UserHomeworkCommentQuery query);

    int insert(UserHomeworkComment userHomeworkComment);

    List<UserHomeworkComment> getUserHomeworkCommentList(UserHomeworkCommentListQuery query);
}
