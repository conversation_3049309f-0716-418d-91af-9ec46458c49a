/**
 * 
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.CommentElement;
import cn.huanju.edu100.exception.DataAccessException;

/**
 * 评价元素DAO接口
 * <AUTHOR>
 * @version 2017-12-06
 */
public interface CommentElementDao extends CrudDao<CommentElement> {

    CommentElement getByParam(CommentElement param) throws DataAccessException;
}