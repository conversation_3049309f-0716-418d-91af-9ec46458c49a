package cn.huanju.edu100.study.service.impl.expression;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.cache.DataCache;
import cn.huanju.edu100.study.dao.ibatis.expression.ExpressionRuleDao;
import cn.huanju.edu100.study.model.expression.ExpressionRule;
import cn.huanju.edu100.study.service.expression.ExpressionRuleService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 规则Service
 *
 * <AUTHOR>
 * @version 2016-05-23
 */
@Service
public class ExpressionRuleServiceImpl extends BaseServiceImpl<ExpressionRuleDao, ExpressionRule> implements
        ExpressionRuleService {

    @Autowired
    private DataCache dataCache;

    @Override
    public List<ExpressionRule> findListByParam(Set<Long> idSet, Integer type) throws DataAccessException {

        if (CollectionUtils.isEmpty(idSet)) {
            logger.error("findListByParam fail, idSet is null");
            return null;
        }

        List<Long> idList = Lists.newArrayList();
        idList.addAll(idSet);

        List<ExpressionRule> expressionRules = dataCache.getExpressionRuleByIds(idList, type);
        if (!CollectionUtils.isEmpty(expressionRules)) {
            return expressionRules;
        }
        return dao.findListByParam(idList, type);
    }

    @Override
    @Cacheable(value = "bulletinCache", key = "'expressionRule.'+methodName + #appKey + #schId")
    public List<ExpressionRule> findBySchAndAppKey(String appKey, Long schId) throws DataAccessException {
        if(StringUtils.isBlank(appKey)){
            return Collections.EMPTY_LIST;
        }
        ExpressionRule rule = new ExpressionRule();
        List<String> keyList = Lists.newArrayList();
        keyList.add(appKey);
        keyList.add("terminal_all");

        if (!schId.equals(1L)) {
            rule.setSchId(schId);
        }
        if (appKey.contains("app") || appKey.contains("tk")) {
            keyList.add("terminal_app");
        }else {
            keyList.add("web_all");
        }
        rule.setRightCompareValList(keyList);
        return dao.findList(rule);
    }

}
