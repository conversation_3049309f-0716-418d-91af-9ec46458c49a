package cn.huanju.edu100.study.model.tutor;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 老师回访记录Entity
 * <AUTHOR>
 * @version 2016-01-19
 */
public class TutorStudentFeedback extends DataEntity<TutorStudentFeedback> {

	private static final long serialVersionUID = 1L;
	private String classes;		// 班别
	private Long uid;		// uid
	private Integer type=0;		// type 0：个性化，1：一对一
	private String isShow;		// 是否前端显示
	private String isRemark;		// 是否是备注
	private Long teacherId;		// 老师id
	private Date feedbackDate;		// 回访时间
	private Long duration;		// 回访时长
	private String remark;		// 备注
	private String ip;		// ip

	public TutorStudentFeedback() {
		super();
	}

	public TutorStudentFeedback(Long id){
		super(id);
	}

	public String getClasses() {
		return classes;
	}

	public void setClasses(String classes) {
		this.classes = classes;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public String getIsShow() {
		return isShow;
	}

	public void setIsShow(String isShow) {
		this.isShow = isShow;
	}

	public String getIsRemark() {
		return isRemark;
	}

	public void setIsRemark(String isRemark) {
		this.isRemark = isRemark;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public Date getFeedbackDate() {
		return feedbackDate;
	}

	public void setFeedbackDate(Date feedbackDate) {
		this.feedbackDate = feedbackDate;
	}

	public Long getDuration() {
		return duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}
