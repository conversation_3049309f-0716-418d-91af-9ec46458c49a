package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorSectionTaskDao;
import cn.huanju.edu100.study.model.CountModel;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.study.service.tutor.TutorSectionTaskService;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.IdUtils;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 章节任务表Service
 * <AUTHOR>
 * @version 2017-12-28
 */
@Service
public class TutorSectionTaskServiceImpl extends BaseServiceImpl<TutorSectionTaskDao, TutorSectionTask> implements TutorSectionTaskService {

    @Override
    public Map<Long, Integer> getTaskNum(List<Long> sectionIdList, Integer type) throws BusinessException, DataAccessException {

        if (CollectionUtils.isEmpty(sectionIdList)) {
            logger.error("illegal param, sectionIdList is empty");
            throw new BusinessException(Constants.PARAM_INVALID, "illegal param, sectionIdList is empty");
        }

        List<CountModel> countModels = dao.listTaskNum(sectionIdList, type);
        if (CollectionUtils.isNotEmpty(countModels)) {
            Map<Long, Integer> map = Maps.newHashMap();
            for (CountModel countModel : countModels) {
                map.put(countModel.getId(), countModel.getNum());
            }

            return map;
        }

        return Collections.emptyMap();
    }

    @Override
    public Map<Long, Integer> getWKTaskNum(List<Long> weikeIdList, Integer type) throws BusinessException, DataAccessException {

        if (CollectionUtils.isEmpty(weikeIdList)) {
            logger.error("illegal param, weikeIdList is empty");
            throw new BusinessException(Constants.PARAM_INVALID, "illegal param, weikeIdList is empty");
        }

        return dao.listWKTaskNum(weikeIdList, type);
    }

    @Override
    public List<TutorSectionTask> findListByParam(TutorSectionTask search) throws BusinessException, DataAccessException {
        if (null == search || (!IdUtils.isValid(search.getSectionId()) && CollectionUtils.isEmpty(search.getIdList()))) {
            logger.error("illegal param, sectionId or idList is null");
            throw new BusinessException(Constants.PARAM_INVALID, "illegal param, sectionId or idList is null");
        }


        return dao.findListByParam(search);
    }
}
