package cn.huanju.edu100.study.dao.ibatis.impl.mock;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.mock.AdviceCourseLinkDao;
import cn.huanju.edu100.study.model.mock.AdviceCourseLink;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AdviceCourseLinkIbatisImpl extends CrudIbatisImpl2<AdviceCourseLink> implements AdviceCourseLinkDao {


    public AdviceCourseLinkIbatisImpl() {
        super("AdviceCourseLink");
    }

    @Override
    public List<AdviceCourseLink> getAdviceCourseLinkList(Long adviceId) throws DataAccessException {
        if (adviceId == null){
            return null;
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("adviceId", adviceId);
            List<AdviceCourseLink> result = (List<AdviceCourseLink>) sqlMap.queryForList("AdviceCourseLink.getAdviceCourseLinkList", param);
            return result;
        } catch (SQLException e) {
            logger.error("getAdviceCourseLinkList SQLException.adviceId:{}", adviceId, e);
            throw new DataAccessException("getAdviceCourseLinkList SQLException error");
        }
    }
}
