package cn.huanju.edu100.study.service.impl.homework.papercorrection;

import cn.huanju.edu100.study.mapper.homework.papercorrection.PaperCorrectionRecordMapper;
import cn.huanju.edu100.study.model.homework.papercorrection.CorrectionRecordCmd;
import cn.huanju.edu100.study.model.homework.papercorrection.CorrectionRecordQuery;
import cn.huanju.edu100.study.model.homework.papercorrection.PaperCorrectionRecord;
import cn.huanju.edu100.study.model.homework.papercorrection.PaperCorrectionSum;
import cn.huanju.edu100.study.service.homework.papercorrection.PaperCorrectionRecordService;
import cn.huanju.edu100.study.service.homework.papercorrection.PaperCorrectionSumService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PaperCorrectionRecordServiceImpl  extends ServiceImpl<PaperCorrectionRecordMapper, PaperCorrectionRecord> implements PaperCorrectionRecordService {

    @Autowired
    private PaperCorrectionSumService paperCorrectionSumService;

    @Override
    public PaperCorrectionRecord createAiCorrectionRecord(CorrectionRecordCmd cmd) {
        //先获取是否有记录
        CorrectionRecordQuery query = new CorrectionRecordQuery();
        query.setServiceId(cmd.getServiceId());
        query.setGoodsId(cmd.getGoodsId());
        query.setOrderId(cmd.getOrderId());
        query.setUid(cmd.getUid());
        PaperCorrectionSum paperCorrectionSum = paperCorrectionSumService.findPaperCorrectionSum(query);

        if(ObjectUtils.isNotEmpty(paperCorrectionSum) && paperCorrectionSum.getTotal() > 0 && paperCorrectionSum.getTotal() <= paperCorrectionSum.getUsedTotal()){
            //次数已经用完
            return null;
        }
        PaperCorrectionSum newSum = new PaperCorrectionSum();
        if(ObjectUtils.isNotEmpty(paperCorrectionSum)){
            //次数加1
            newSum.setUsedTotal(paperCorrectionSum.getUsedTotal() + 1);
            newSum.setId(paperCorrectionSum.getId());
            paperCorrectionSumService.updateById(newSum);

        } else {
            newSum.setOrderId(cmd.getOrderId());
            newSum.setServiceId(cmd.getServiceId());
            newSum.setUid(cmd.getUid());
            newSum.setGoodsId(cmd.getGoodsId());
            newSum.setTotal(cmd.getTotal());
            newSum.setUsedTotal(1);
            paperCorrectionSumService.save(newSum);
        }
        PaperCorrectionRecord newRecord = new PaperCorrectionRecord();
        newRecord.setType(cmd.getType());
        newRecord.setStatus(0);
        BeanUtils.copyProperties(cmd,newRecord);
        this.save(newRecord);
        return newRecord;
    }

    @Override
    public List<PaperCorrectionRecord> findCorrectionRecordList(CorrectionRecordQuery query) {
        LambdaQueryWrapper<PaperCorrectionRecord> wrapper = Wrappers.lambdaQuery(PaperCorrectionRecord.class);
        wrapper.eq(PaperCorrectionRecord::getUid,query.getUid());
        wrapper.eq(PaperCorrectionRecord::getOrderId,query.getOrderId());
        wrapper.eq(PaperCorrectionRecord::getGoodsId,query.getGoodsId());
        wrapper.eq(PaperCorrectionRecord::getServiceId,query.getServiceId());
        wrapper.orderByDesc(PaperCorrectionRecord::getId);
        return this.list(wrapper);
    }
}
