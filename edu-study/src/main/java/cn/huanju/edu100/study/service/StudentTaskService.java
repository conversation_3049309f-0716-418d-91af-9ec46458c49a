package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.StudentTask;
import cn.huanju.edu100.exception.DataAccessException;

/**
 * 学习计划Service
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface StudentTaskService extends BaseService<StudentTask> {

	/**
	 * 根据taskId和uid，查询记录
	 * @param tid
	 * @param uid
	 * @return
	 * @throws DataAccessException
	 */
	StudentTask getStudentTaskByTidAndUid(Long tid, Long uid) throws DataAccessException, BusinessException;

	/**
	 * 根据学生任务ID，更新state
	 */
	boolean updateState(Long id, Integer state) throws DataAccessException;

	/**
	 * 插入一条记录，并返回主键值
	 * @param studentTask
	 * @return
	 * @throws DataAccessException
	 */
	long insertAndGetGenerateId(StudentTask studentTask) throws DataAccessException;
}
