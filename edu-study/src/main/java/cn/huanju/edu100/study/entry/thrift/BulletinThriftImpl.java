/*
 * @(#) BulletinThriftImpl.java
 * Copyright(c) 欢聚时代科技有限公司
 */
/*
 * Copyright (C) 多玩游戏 ©2005-2012.
 *
 * @# BulletinThriftImpl.java
 *
 */
package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.Bulletin;
import cn.huanju.edu100.study.model.BulletinRule;
import cn.huanju.edu100.study.model.UserCategory;
import cn.huanju.edu100.study.model.expression.ExpressionGroup;
import cn.huanju.edu100.study.model.expression.ExpressionGroupDto;
import cn.huanju.edu100.study.model.expression.ExpressionMember;
import cn.huanju.edu100.study.model.expression.ExpressionRule;
import cn.huanju.edu100.study.resource.AnalyseResource;
import cn.huanju.edu100.study.service.BulletinRuleService;
import cn.huanju.edu100.study.service.BulletinService;
import cn.huanju.edu100.study.service.UserCategoryService;
import cn.huanju.edu100.study.service.expression.ExpressionGroupService;
import cn.huanju.edu100.study.service.expression.ExpressionMemberService;
import cn.huanju.edu100.study.service.expression.ExpressionRuleService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 *
 *
 * @version 1.0
 * <AUTHOR>
 * @time 2015年5月15日 下午3:12:05
 */
@Component
public class BulletinThriftImpl extends AbstractServiceThrift {
	private static Logger logger = LoggerFactory.getLogger(BulletinThriftImpl.class);
	@Autowired
	private BulletinService bulletinService;
	@Autowired
	private ExpressionRuleService expressionRuleService;
	@Autowired
	private ExpressionGroupService expressionGroupService;
	@Autowired
	private ExpressionMemberService expressionMemberService;
	@Autowired
	private BulletinRuleService bulletinRuleService;
	@Autowired
	private AnalyseResource analyseResource;
	@Autowired
	private UserCategoryService userCategoryService;
	private static Gson gson = GsonUtil.getGson();

	/**
	 * 用户中心 2.1.15 获得有效网校公告列表(范围是全部学员的公告)
	 *
	 * @param req
	 * @return
	 */
	public response sty_getBulletinList(request req)
			throws BusinessException {
		String entry = "sty_getBulletinList";
		long start = System.currentTimeMillis();
		enterValidator(entry, start, req);
		response res = new response();
		try {
			Collection<Bulletin> result = bulletinService.listBulletinByType(null, null, null);
			if (result != null) {
				res.setMsg(gson.toJson(result));
			} else {
				res.setCode(Constants.OBJ_NOT_EXISTS);
				res.setErrormsg("sty_getBulletinList return null");
			}
		} catch (DataAccessException e) {
			res = dataAccessException(entry, req, e);
		} catch (Exception e) {
			res = exception(entry, req, e);
		}
		endInfo(entry, res, start);
		return res;
	}

	/**
	 * 2.1.16 根据id获得网校公告详情
	 *
	 * @param req
	 * @return
	 */
	public response sty_getBulletinInfoById(request req)
			throws BusinessException {
		String entry = "sty_getBulletinInfoById";
		long start = System.currentTimeMillis();
		enterValidator(entry, start, req);
		response res = new response();
		try {
			Bulletin bulletin = gson.fromJson(req.getMsg(), Bulletin.class);
			if (bulletin == null || bulletin.getId() == null
					|| bulletin.getId() <= 0) {
				logger.error("{} fail.paramerter id is null or emtpty.", entry);
				throw new BusinessException(Constants.PARAM_INVALID,
						"paramerter id is null or emtpty.");
			}
			Bulletin result = (Bulletin) bulletinService.get(bulletin.getId());
			if (result != null) {
				res.setMsg(GsonUtil.toJson(result, req.getAppid()));
			} else {
				res.setCode(Constants.OBJ_NOT_EXISTS);
				res.setErrormsg("sty_getBulletinInfoById return null");
			}
		} catch (DataAccessException e) {
			res = dataAccessException(entry, req, e);
		} catch (Exception e) {
			res = exception(entry, req, e);
		}
		endInfo(entry, res, start);
		return res;

	}

	/**
	 * 查询用户公告列表
	 * 1. 查询所有范围的公告，适用于所有的用户（排除白名单取消的用户）
	 * 2. 查询满足所传商品id和考试（科目）id的公告
	 * 3. 如果用户没有买过课，查询注册未买课类别的公告
	 *
	 * @param req
	 * @return
	 * @throws BusinessException
	 */
    public response sty_getBulletinByIds(request req) throws BusinessException {
        String entry = "sty_getBulletinByIds";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);

            String goodsIds = params.get("goods_ids") == null ? "" : params.get("goods_ids").toString();
            String category_ids = params.get("category_ids") == null ? "" : params.get("category_ids").toString();
            Object uidObj = params.get("uid");
            Long uid = null;
            Long schId = req.getSchId();
            if (uidObj != null && Double.valueOf(uidObj.toString()) > 0) {
                uid = Double.valueOf(uidObj.toString()).longValue();
            }

            if (schId.equals(1l)) {
                schId = null;
            }
            Collection<Bulletin> result = new ArrayList<Bulletin>();
            //根据用户uid查询发布范围为所有的公告
            Collection<Bulletin> bulletinlist = bulletinService.listBulletinByType(uid, schId, Consts.Bulletin_Type.ALL);
            if (!CollectionUtils.isEmpty(bulletinlist)) {
                result.addAll(bulletinlist);
            }

            //根据商品id或者考试大类查询用户的公告
            Collection<Bulletin> buyBulletins = bulletinService.getBulletinListByIds(goodsIds, category_ids, uid, schId);
            if (!CollectionUtils.isEmpty(buyBulletins)) {
                result.addAll(buyBulletins);
            }

            //查询注册未买课类别的公告，商品或者考试id：用户还没有买商品
            if (uid != null && StringUtils.isBlank(category_ids)
                    && StringUtils.isBlank(goodsIds)) {
                Collection<Bulletin> regbulletinlist = bulletinService.listBulletinByType(uid, schId, Consts.Bulletin_Type.REGISTER);
                if (!CollectionUtils.isEmpty(regbulletinlist)) {
                    result.addAll(regbulletinlist);
                }
            }
            if (!CollectionUtils.isEmpty(result)) {
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
            } else {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getBulletinByIds return null");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_cancelBulletin(request req) throws BusinessException {
        String entry = "sty_cancelBulletin";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, String> bulletin = gson.fromJson(req.getMsg(), Map.class);
            if (bulletin == null || bulletin.get("ids") == null || bulletin.get("uid") == null) {
                logger.error("{} fail.paramerter uid or ids is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID,
                        "paramerter uid or ids is null or emtpty.");
            }

            String ids = bulletin.get("ids").toString();
            Object uidObj = bulletin.get("uid");
            Double uid = Double.valueOf(uidObj.toString());
            String[] idArray = ids.split(",");
            if (uid <= 0 || idArray == null) {
                logger.error("{} fail.paramerter uid or ids is invalid.", entry);
                throw new BusinessException(Constants.PARAM_INVALID,
                        "paramerter uid or ids is invalid.");
            }
            boolean result = bulletinService.cancelBulletin(uid.longValue(), idArray);
            if (result) {
                //res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                res.setCode(Constants.SUCCESS);
            } else {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_cancelBulletin return null");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 根据用户uid和app的key获取对应的公告列表
     * 先根据appKey检索出对应的规则，再根据express_member表找出对应expressionGroup
     * 接着再根据expressionGroup在bulletin_rule表中找出对应的actionRuleIds
     * 再用actionRuleIds在expression_group找到对应的规则组groups，再为每个规则组构造其包含的规则列表和规则组列表
     * 接着调analyse服务的analyse_validateByExpressionRuleAndUids接口验证规则组
     * 然后过滤出有效的groupId，然后再在bulletin_rule中找到bulletin_id
     * 最后根据获取的bulletinIds去bulletin表中找到有效的公告
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getBulletinsByUidAndAppKey(request req) throws BusinessException {

        String entry = "sty_getBulletinsByUidAndAppKey";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<String, Object>>() {
            }.getType();
            Map<String, Object> params = gson.fromJson(req.getMsg(), type);

            Object uidObj = params.get("uid");
            Object appKey = params.get("appKey");
            long schId = req.getSchId();
            if (!(appKey instanceof String) || schId <= 0) {
                logger.error("illegal param, uidObj :{}, appKey :{}, schId:{}", uidObj, appKey, schId);
                throw new BusinessException(Constants.PARAM_INVALID, String.format(
                        "illegal param, uidObj :%s, appKey :%s, schId : %s", uidObj, appKey, schId));
            }

            Long uid = null;
            if (uidObj != null) {
                uid = ((Double) uidObj).longValue();
            }
            String appKeyStr = (String) appKey;

            if (StringUtils.isBlank(appKeyStr) || !IdUtils.isValid(schId)) {
                logger.error("illegal param, uid :{}, appKeyStr :{}, schId :{}", uidObj, appKeyStr, schId);
                throw new BusinessException(Constants.PARAM_INVALID, String.format(
                        "illegal param, uid :%d, appKey :%s, schId : %s", uid, appKeyStr, schId));
            }

//            ExpressionRule rule = new ExpressionRule();
//            rule.setRightCompareVal(appKeyStr);
//            if (!schId.equals(1L)) {
//                rule.setSchId(schId);
//            }
//            if (appKeyStr.contains("app") || appKeyStr.contains("tk")) {
//                rule.setSource("terminal_app");
//            }else {
//                rule.setSource("web_all");
//            }
            List<ExpressionRule> rules = expressionRuleService.findBySchAndAppKey(appKeyStr, schId);
            if (CollectionUtils.isEmpty(rules)) {
                logger.error("there is no bulletin for uid :{}, appKey :{}, no expressionRule", uid, appKeyStr);
                throw new BusinessException(Constants.OBJ_NOT_EXISTS, String.format(
                        "there is no bulletin for uid :%s, appKey :%s", uid, appKeyStr));
            } else {
                // 根据appKeyStr检索出的规则rules和uid获取公告
                Collection<Bulletin> bulletins = getBulletinsByAppKeyRules(uid, appKeyStr, rules);
                if (CollectionUtils.isNotEmpty(bulletins)) {
                    res.setMsg(gson.toJson(bulletins));
                    res.setCode(Constants.SUCCESS);
                } else {
                    logger.error(
                            "there is no bulletin for uid :{}, appKey :{}, no bulletin",
                            uid, appKeyStr);
                    throw new BusinessException(Constants.OBJ_NOT_EXISTS, String.format(
                            "there is no bulletin for uid :%s, appKey :%s", uid, appKeyStr));
                }
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 根据appKey对应的规则appKeyRules和uid获取公告
     *
     * @param uid
     * @param appKeyStr
     * @param appKeyRules
     * @return
     * @throws DataAccessException
     * @throws BusinessException
     */
    private Collection<Bulletin> getBulletinsByAppKeyRules( Long uid, String appKeyStr, List<ExpressionRule> appKeyRules)
            throws DataAccessException, BusinessException {

        List<BulletinRule> bulletinRules = new ArrayList<BulletinRule>();
        Set<Long> ruleIdSet = Sets.newHashSet();
        for (ExpressionRule rule : appKeyRules) {
            ruleIdSet.add(rule.getId());
        }

        //直接包含的规则
        BulletinRule bulletinRule = new BulletinRule();
        bulletinRule.setTerminalRuleType(0);
        //bulletinRule.setActionRuleType(1);
        // 找出对应的公告规则bulletinRules
        List<BulletinRule> bList = bulletinRuleService.findListByParam(ruleIdSet, bulletinRule);
        if (!CollectionUtils.isEmpty(bList)) {
            bulletinRules.addAll(bList);
        }
        // 根据规则id集合找出 规则组与规则的关系members
        getBulletinRules(uid, appKeyStr, bulletinRules, ruleIdSet, 0);
        if (CollectionUtils.isEmpty(bulletinRules)) {

            logger.error("there is no bulletin for uid :{}, appKey :{}, no bulletinRule", uid, appKeyStr);
            throw new BusinessException(Constants.OBJ_NOT_EXISTS, String.format(
                    "there is no bulletin for uid :%s, appKey :%s", uid, appKeyStr));
        } else {
            // 兼容只有终端规则的情况
            Set<Long> noActionRuleIdList = Sets.newHashSet();
            List<BulletinRule> hasActionRules = new ArrayList<BulletinRule>();
            for (BulletinRule buRule : bulletinRules) {
                if (buRule.getActionRuleId() == null) {
                    noActionRuleIdList.add(buRule.getBulletinId());
                    continue;
                }
                //兼容没有登录的情况
                if (uid != null) {
                    hasActionRules.add(buRule);
                }
            }

            //没有用户筛选规则表示所有用户都满足
            List<Bulletin> bulletins = new ArrayList<Bulletin>();
            if (CollectionUtils.isNotEmpty(noActionRuleIdList)) {
                bulletins.addAll(bulletinService.findListByIds(noActionRuleIdList, null, uid));
            }
            if (CollectionUtils.isEmpty(hasActionRules)) {
                return bulletins;
            }
            // 根据公告规则bulletinRules和uid获取公告
            bulletins.addAll(getBulletinsByBulletinRules(uid, appKeyStr, hasActionRules));
            return bulletins;
        }
    }

    private void getBulletinRules(Long uid, String appKeyStr, List<BulletinRule> bulletinRules,
            Set<Long> ruleIdSet, Integer ruleType) throws DataAccessException, BusinessException {

        //没有找到则终止递归
        List<ExpressionMember> members = expressionMemberService.findListByParam(ruleIdSet, null, ruleType);
        if (CollectionUtils.isEmpty(members)) {
            return;
        } else {
            Set<Long> terminalGroupIdSet = Sets.newHashSet();
            for (ExpressionMember member : members) {
                terminalGroupIdSet.add(member.getGroupId());
            }

            BulletinRule bulletinRule = new BulletinRule();
            bulletinRule.setTerminalRuleType(1);
            //bulletinRule.setActionRuleType(1);
            // 找出对应的公告规则bulletinRules
            List<BulletinRule> bList = bulletinRuleService.findListByParam(terminalGroupIdSet, bulletinRule);
            if (!CollectionUtils.isEmpty(bList)) {
                bulletinRules.addAll(bList);
            }

            //递归寻找终端规则组
            getBulletinRules(uid, appKeyStr, bulletinRules, terminalGroupIdSet, 1);
        }
    }

    /**
     * 根据公告规则bulletinRules和uid获取公告
     *
     * @param uid
     * @param appKeyStr
     * @param bulletinRules
     * @return
     * @throws DataAccessException
     * @throws BusinessException
     */
    private Collection<Bulletin> getBulletinsByBulletinRules(Long uid, String appKeyStr,
            List<BulletinRule> bulletinRules) throws DataAccessException, BusinessException {

        Set<Long> groupIdSet = Sets.newHashSet();
        Set<Long> ruleIdSet = Sets.newHashSet();
        for (BulletinRule bulletinRule : bulletinRules) {
            if (bulletinRule.getActionRuleType().equals(Consts.Analyse_Rule_Type.GROUP)) {
                groupIdSet.add(bulletinRule.getActionRuleId());
            }else {
                ruleIdSet.add(bulletinRule.getActionRuleId());
            }
        }

        List<ExpressionGroup> groups = expressionGroupService.findListByParam(groupIdSet, 0);
        List<ExpressionRule> expressionRules = expressionRuleService.findListByParam(ruleIdSet, 0);
        if (CollectionUtils.isEmpty(groups) && CollectionUtils.isEmpty(expressionRules)) {
            logger.error(
                    "there is no bulletin for uid :{}, appKey :{}, no expression groups",
                    uid, appKeyStr);
            throw new BusinessException(Constants.OBJ_NOT_EXISTS, String.format(
                    "there is no bulletin for uid :%s, appKey :%s", uid, appKeyStr));
        } else {

            if (!CollectionUtils.isEmpty(groups)) {
                for(ExpressionGroup group : groups) {
                    buildExpressionGroupTree(group);
                }
            }

            return getBulletinsByCompleteGroups(uid, appKeyStr, bulletinRules, groups, expressionRules);
        }
    }

    private Collection<Bulletin> getBulletinsByCompleteGroups(Long uid, String appKeyStr,
            List<BulletinRule> bulletinRules, List<ExpressionGroup> groups, List<ExpressionRule> expressionRules)
                    throws BusinessException,
            DataAccessException {

        ExpressionGroupDto dto = new ExpressionGroupDto();
        dto.setUid(uid);
        dto.setExpressionGroups(groups);
        dto.setExpressionRules(expressionRules);
        Map<Integer, Map<Long, Boolean>> groupIdValidMap = analyseResource.validateUidByExpressionGroups(dto);
        if (null == groupIdValidMap || groupIdValidMap.isEmpty()) {
            logger.error(
                    "there is no bulletin for uid :{}, appKey :{}, no valid expression group",
                    uid, appKeyStr);
            throw new BusinessException(Constants.OBJ_NOT_EXISTS, String.format(
                    "there is no bulletin for uid :%s, appKey :%s", uid, appKeyStr));
        } else {
            Set<Long> validGroupSet = Sets.newHashSet();
            Set<Long> validRuleSet = Sets.newHashSet();
            for (Integer key : groupIdValidMap.keySet()) {
                Map<Long, Boolean> valid = groupIdValidMap.get(key);
                if (org.springframework.util.CollectionUtils.isEmpty(valid)) {
                    continue;
                }
                for (Long ruleId : valid.keySet()) {
                    if (key.equals(Consts.Analyse_Rule_Type.GROUP) && valid.get(ruleId)) {
                        validGroupSet.add(ruleId);
                    }

                    if (key.equals(Consts.Analyse_Rule_Type.OBJECT) && valid.get(ruleId)) {
                        validRuleSet.add(ruleId);
                    }
                }
            }

            if (CollectionUtils.isEmpty(validGroupSet) && CollectionUtils.isEmpty(validRuleSet)) {
                logger.error(
                        "there is no bulletin for uid :{}, appKey :{}, no valid group or rule when validateUidByExpressionGroups",
                        uid, appKeyStr);
                return CollectionUtils.EMPTY_COLLECTION;
            } else {
                return getValidBulletins(uid, appKeyStr, bulletinRules, validGroupSet, validRuleSet);
            }
        }
    }

    private Collection<Bulletin> getValidBulletins(Long uid, String appKeyStr, List<BulletinRule> bulletinRules,
            Set<Long> validGroupSet, Set<Long> validRuleSet) throws DataAccessException, BusinessException {

        Set<Long> bulletinSet = Sets.newHashSet();
        for (BulletinRule rule : bulletinRules) {
            if (validGroupSet.contains(rule.getActionRuleId())
                    && rule.getActionRuleType().equals(Consts.Analyse_Rule_Type.GROUP)) {
                bulletinSet.add(rule.getBulletinId());
            }
            if (validRuleSet.contains(rule.getActionRuleId())
                    && rule.getActionRuleType().equals(Consts.Analyse_Rule_Type.OBJECT)) {
                bulletinSet.add(rule.getBulletinId());
            }
        }

        if (CollectionUtils.isNotEmpty(bulletinSet)) {
            return bulletinService.findListByIds(bulletinSet, null, uid);
        } else {
            logger.error(
                    "there is no bulletin for uid :{}, appKey :{}, no valid bulletinId",
                    uid, appKeyStr);
            throw new BusinessException(Constants.OBJ_NOT_EXISTS, String.format(
                    "there is no bulletin for uid :%s, appKey :%s", uid, appKeyStr));
        }
    }

    private void buildExpressionGroupTree(ExpressionGroup group) throws BusinessException, DataAccessException {

        if (null != group) {

            Set<Long> set = Sets.newHashSet();
            set.add(group.getId());
            List<ExpressionMember> members = expressionMemberService.findListByParam(null, set, null);
            if (CollectionUtils.isEmpty(members)) {
                logger.error("buildExpressionGroupTree fail, find no expression member for expressionGruop :{}", group);
                throw new BusinessException(Constants.OBJ_NOT_EXISTS, String.format(
                         "buildExpressionGroupTree fail, find no expression member for expressionGruop :%s", group));
            }

            Set<Long> ruleIdSet = Sets.newHashSet();
            Set<Long> relateGroupIdSet = Sets.newHashSet();
            for (ExpressionMember member : members) {
                if (0 == member.getType()) {
                    ruleIdSet.add(member.getRuleId());
                } else {
                    relateGroupIdSet.add(member.getRuleId());
                }
            }

            List<ExpressionRule> rules = null;
            List<ExpressionGroup> groups = null;
            if (CollectionUtils.isNotEmpty(ruleIdSet)) {
                rules = expressionRuleService.findListByParam(ruleIdSet, 0);
                group.setExpressionRules(rules);
            }

            if (CollectionUtils.isNotEmpty(relateGroupIdSet)) {
                groups = expressionGroupService.findListByParam(relateGroupIdSet, 0);
                group.setExpressionGroups(groups);
                if (CollectionUtils.isNotEmpty(groups)) {
                    for (ExpressionGroup subGroup : groups) {
                        buildExpressionGroupTree(subGroup);
                    }
                }
            }
        }
    }

    public response sty_saveUserCategory(request req) throws BusinessException {
        String entry = "sty_saveUserCategory";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserCategory userCategory = gson.fromJson(req.getMsg(), UserCategory.class);
            if (userCategory == null || userCategory.getUid() == null
                    || userCategory.getSecondCategory() == null || StringUtils.isBlank(userCategory.getAppid())) {
                logger.error("{} fail.paramerter uid or second_category or appid is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID,
                        "paramerter uid or second_category or appid is null or emtpty.");
            }

            boolean result = userCategoryService.saveUserCategory(userCategory);
            if (result) {
                res.setMsg(GsonUtil.toJson(result));
                res.setCode(Constants.SUCCESS);
            } else {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_saveUserCategory return null");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
