package cn.huanju.edu100.study.model.onetoone;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 学员购买记录Entity
 * <AUTHOR>
 * @version 2016-04-19
 */
public class VStudentProduct extends DataEntity<VStudentProduct> {
	
	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private String classes;		// 班别
	private Integer type;		// 直播产品类型
	private Long goodsId;		// 商品id
	private Long productId;		// 产品id
	private Long orderId;		// 订单id
	private Long logId;
	private String name;		// 产品名称
	private Long lessonCount;		// 课时数量
    private Long leftCount;     // 剩余课时
    private Long useCount;      // 已分配课时
	private Long firstCategory;        // first_category
    private Long secondCategory;        // second_category
    private Long categoryId;      // category
	
	public VStudentProduct() {
		super();
	}

	public VStudentProduct(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public String getClasses() {
		return classes;
	}

	public void setClasses(String classes) {
		this.classes = classes;
	}
	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
	
	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public Long getLessonCount() {
		return lessonCount;
	}

	public void setLessonCount(Long lessonCount) {
		this.lessonCount = lessonCount;
	}

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getLeftCount() {
        return leftCount;
    }

    public void setLeftCount(Long leftCount) {
        this.leftCount = leftCount;
    }

    public Long getUseCount() {
        return useCount;
    }

    public void setUseCount(Long useCount) {
        this.useCount = useCount;
    }

}