package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Collection;

/**
 * 视频资源Entity
 * 
 * <AUTHOR>
 * @version 2015-07-29
 */
public class Lesson extends DataEntity<Lesson> {

	private static final long serialVersionUID = 1L;
	private Long firstCategory; // first_category
	private Long secondCategory; // second_category
	private Long categoryId; // category_id
	private String name; // name
	private String year; // year
	private Long uid; // 用户uid
	private Integer type; // 代表视频资源的类型,0表示宣传片，1表示名师访谈,2表示其他视频
	private Integer videoType; // 代表视频类型,0:大屏，1:小屏
	private Integer status; // 0表示未上传，1表示开始上传，2表示上传完成，3表示开始转码，4表示转码部分完成，5表示转码完成，6表示转码失败
	private Long size; // size
	private String url; // url
	private String sBucket; // s_bucket
	private String tBucket; // t_bucket
	private String filename; // filename
	private String transData; // trans_data
	private String length; // length
	private String jobId; // job_id
	private String uploadflag; // uploadflag
	private String bak1; // bak1
	private String bak2; // bak2
	private String ip; // ip
	private Integer classType; // class_type
	private Integer isLesson; // is_lesson
	private Integer close; // 是否已经关闭
	private String downloadUrl; // 下载地址

	private Collection<LessonAttachment> attachments;
	private Collection<LessonChapter> chapters;
	private Collection<LessonParagraph> paragraphs;
	private Collection<LessonQuestion> questions;
	private Collection<ResourceVideoTransdata> transDatas;
	
	private Collection<LessonSupplement> supplements;//课件补充点
	
	public Lesson() {
		super();
	}

	public Lesson(Long id) {
		super(id);
	}

	public Long getFirstCategory() {
		return firstCategory;
	}

	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getSize() {
		return size;
	}

	public void setSize(Long size) {
		this.size = size;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getSBucket() {
		return sBucket;
	}

	public void setSBucket(String sBucket) {
		this.sBucket = sBucket;
	}

	public String getTBucket() {
		return tBucket;
	}

	public void setTBucket(String tBucket) {
		this.tBucket = tBucket;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public String getTransData() {
		return transData;
	}

	public void setTransData(String transData) {
		this.transData = transData;
	}

	public String getLength() {
		return length;
	}

	public void setLength(String length) {
		this.length = length;
	}

	public String getJobId() {
		return jobId;
	}

	public void setJobId(String jobId) {
		this.jobId = jobId;
	}

	public String getUploadflag() {
		return uploadflag;
	}

	public void setUploadflag(String uploadflag) {
		this.uploadflag = uploadflag;
	}

	public String getBak1() {
		return bak1;
	}

	public void setBak1(String bak1) {
		this.bak1 = bak1;
	}

	public String getBak2() {
		return bak2;
	}

	public void setBak2(String bak2) {
		this.bak2 = bak2;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Integer getClassType() {
		return classType;
	}

	public void setClassType(Integer classType) {
		this.classType = classType;
	}

	public Integer getIsLesson() {
		return isLesson;
	}

	public void setIsLesson(Integer isLesson) {
		this.isLesson = isLesson;
	}

	public String getDownloadUrl() {
		return downloadUrl;
	}

	public void setDownloadUrl(String downloadUrl) {
		this.downloadUrl = downloadUrl;
	}

	public Integer getVideoType() {
		return videoType;
	}

	public void setVideoType(Integer videoType) {
		this.videoType = videoType;
	}

	public Collection<LessonAttachment> getAttachments() {
		return attachments;
	}

	public void setAttachments(Collection<LessonAttachment> attachments) {
		this.attachments = attachments;
	}

	public Collection<LessonChapter> getChapters() {
		return chapters;
	}

	public void setChapters(Collection<LessonChapter> chapters) {
		this.chapters = chapters;
	}

	public Collection<LessonParagraph> getParagraphs() {
		return paragraphs;
	}

	public void setParagraphs(Collection<LessonParagraph> paragraphs) {
		this.paragraphs = paragraphs;
	}

	public Collection<ResourceVideoTransdata> getTransDatas() {
		return transDatas;
	}

	public void setTransDatas(Collection<ResourceVideoTransdata> transDatas) {
		this.transDatas = transDatas;
	}

	public Collection<LessonQuestion> getQuestions() {
		return questions;
	}

	public void setQuestions(Collection<LessonQuestion> questions) {
		this.questions = questions;
	}

	public Collection<LessonSupplement> getSupplements() {
		return supplements;
	}

	public void setSupplements(Collection<LessonSupplement> supplements) {
		this.supplements = supplements;
	}

    public Integer getClose() {
        return close;
    }

    public void setClose(Integer close) {
        this.close = close;
    }

}