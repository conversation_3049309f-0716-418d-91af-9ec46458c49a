package cn.huanju.edu100.study.model.adminstudy;


import cn.huanju.edu100.study.util.Constants;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 商品的学习报告，有录播，直播，试卷，课后作业
 * @date 18/4/16
 */
public class StudyReportQuery {

    /**
     * 学习记录类型
     */
    public interface StudyLogType {
        /**
         * 录播课程
         */
        int VIDEO_COURSE = 1;
        /**
         * 电子试卷
         */
        int PAPER = 2;
        /**
         * 直播课程
         */
        int LIVE_COURSE = 3;
        /**
         * 课后作业
         */
        int HOME_WORK = 4;
    }
    private static final long serialVersionUID = 1L;

    private Long uid;		     // uid
    private String phone;
    private Long goodsId;        // 商品id
    private String goodsName;
    private Long firstCategory;
    private Long secondCategory;
    private Long categoryId;     // 科目
    private Long productId;
    private String productName;
    private Date startTime;
    private Date endTime;
    private Integer studyLogType;
    private Integer videoCourseRang; // 0 全部，1 仅往期版，2 仅最新版

    private Integer buyUserCount;

    //新规则启用时间
    private final String newRuleStartTime = Constants.USER_VIDEO_LOG_IN_GOODS_START_TIME;
    private List<Long> productIdList;
    private List<Long> lessonIdList;
    private List<Long> paperIdList;
    private List<Long> clsIdList;
    private List<Long> uidList;

    //分页
    private Integer from;
    private Integer pageSize;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getVideoCourseRang() {
        return videoCourseRang;
    }

    public void setVideoCourseRang(Integer videoCourseRang) {
        this.videoCourseRang = videoCourseRang;
    }

    public Integer getStudyLogType() {
        return studyLogType;
    }

    public void setStudyLogType(Integer studyLogType) {
        this.studyLogType = studyLogType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getNewRuleStartTime() {
        return newRuleStartTime;
    }

    public List<Long> getProductIdList() {
        return productIdList;
    }

    public void setProductIdList(List<Long> productIdList) {
        this.productIdList = productIdList;
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public List<Long> getLessonIdList() {
        return lessonIdList;
    }

    public void setLessonIdList(List<Long> lessonIdList) {
        this.lessonIdList = lessonIdList;
    }

    public List<Long> getPaperIdList() {
        return paperIdList;
    }

    public void setPaperIdList(List<Long> paperIdList) {
        this.paperIdList = paperIdList;
    }

    public List<Long> getClsIdList() {
        return clsIdList;
    }

    public void setClsIdList(List<Long> clsIdList) {
        this.clsIdList = clsIdList;
    }

    public List<Long> getUidList() {
        return uidList;
    }

    public void setUidList(List<Long> uidList) {
        this.uidList = uidList;
    }

    public Integer getBuyUserCount() {
        return buyUserCount;
    }

    public void setBuyUserCount(Integer buyUserCount) {
        this.buyUserCount = buyUserCount;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
