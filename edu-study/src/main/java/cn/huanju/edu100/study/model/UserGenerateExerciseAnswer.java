package cn.huanju.edu100.study.model;

import com.hqwx.study.entity.UserHomeWorkAnswer;

/**
 * 用户题库练习提交实体
 * desc：自动生成作业并提交
 * 
 * */
public class UserGenerateExerciseAnswer extends UserHomeWorkAnswer {
	private static final long serialVersionUID = 1L;
	
	private Long teachBookId;		// teachBookId
	private Long boxId;				// boxId
	private Long homeworkTypeId;	//作业类型id(章节id/知识点id)
	private Integer homeworkType;	//作业类型（0：所有，1：章节，2：知识点）
	private Integer homeworkModel;	//作业模式（0：未做试题，1：错误试题，2：全部试题,3：智能练习【未做+错误】）
	
	
	public Long getHomeworkTypeId() {
		return homeworkTypeId;
	}
	public void setHomeworkTypeId(Long homeworkTypeId) {
		this.homeworkTypeId = homeworkTypeId;
	}
	public Integer getHomeworkType() {
		return homeworkType;
	}
	public void setHomeworkType(Integer homeworkType) {
		this.homeworkType = homeworkType;
	}
	public Integer getHomeworkModel() {
		return homeworkModel;
	}
	public void setHomeworkModel(Integer homeworkModel) {
		this.homeworkModel = homeworkModel;
	}
	public Long getTeachBookId() {
		return teachBookId;
	}
	public void setTeachBookId(Long teachBookId) {
		this.teachBookId = teachBookId;
	}
	public Long getBoxId() {
		return boxId;
	}
	public void setBoxId(Long boxId) {
		this.boxId = boxId;
	}
	
	
}
