package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.al.KnowledgeGraph;
import cn.huanju.edu100.study.model.questionBox.QuestionKnowledgeGraph;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.upload.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/5 14:30
 */
@Slf4j
@Component
public class QuestionChatGptPromptBuilderService {


    @Resource
    private KnowledgeResource knowledgeResource;

    public String gptPromptQuestionBaseStr(Long questionId){
        List<Question> questionList = knowledgeResource.getQuestionByIds(List.of(questionId));
        if (CollectionUtils.isEmpty(questionList)){
            return StringUtils.EMPTY;
        }
        Question question = questionList.get(0);
        StringBuffer promptStr = new StringBuffer();
        String content = question.getContent();
        Integer isMulti = question.getIsMulti();

        //题目信息
        if (StringUtils.isNotBlank(content) && isMulti != null && isMulti > 0){
            String material = null;
            if (StringUtils.startsWith(question.getContent(),"http://") || StringUtils.startsWith(question.getContent(),"https://")) {
                material = replaceHtml(StringEscapeUtils.unescapeHtml4(OssUtil.getContentDataFromOss(question.getContent())));
            } else {
                material = replaceHtml(StringEscapeUtils.unescapeHtml4(question.getContent()));
            }
            promptStr.append( material);
        }

        //question_topic信息
        if (CollectionUtils.isNotEmpty(question.getTopicList())){
            int i = 0;
            for (QuestionTopic questionTopic : question.getTopicList()) {
                promptStr.append(this.getTopicStr(questionTopic, ++i));
            }
        }

        return promptStr.toString();
    }

    public String knowledgeAndCategoryInfo(Long questionId) {
        StringBuffer builder = new StringBuffer();
        List<QuestionKnowledgeGraph> questionKnowledgeGraphList = knowledgeResource.getQuestionKnowledgeGraphByIdList(null,Arrays.asList(questionId));
        if (CollectionUtils.isEmpty(questionKnowledgeGraphList)){
            return StringUtils.EMPTY;
        }
        List<Long> knowledgeGraphIds = questionKnowledgeGraphList.stream().map(QuestionKnowledgeGraph::getKnowledgeGraphId).collect(Collectors.toList());
        List<KnowledgeGraph> knowledgeGraphList = knowledgeResource.getKnowledgeGraphByIdList(knowledgeGraphIds);

        if (CollectionUtils.isEmpty(knowledgeGraphList)){
            return StringUtils.EMPTY;
        }

        for (KnowledgeGraph knowledgeGraph : knowledgeGraphList) {
            Category secondCategory = knowledgeResource.getCategoryInfoById(knowledgeGraph.getSecondCategory());
            Category category = knowledgeResource.getCategoryInfoById(knowledgeGraph.getCategoryId());

            StringBuffer categoryStr = new StringBuffer();
            if (secondCategory != null) {
                categoryStr.append("考试：").append(secondCategory.getName()).append(" >> ");
            }
            if (category != null) {
                categoryStr.append("科目：").append(category.getName());
            }

            List<ChapterSection> chapterSectionList = knowledgeResource.getChapterSectionItemByKnowledgeIdAndBookId(null, knowledgeGraph.getId());

            if (CollectionUtils.isNotEmpty(chapterSectionList)){
                StringBuffer chapterStr = new StringBuffer();
                for (ChapterSection chapterSection : chapterSectionList) {
                    chapterStr = new StringBuffer();
                    if (categoryStr!=null && !"".equals(categoryStr.toString())) {
                        chapterStr.append(categoryStr.toString()).append(" >> ");
                    }
                    chapterStr.append( this.getParentDesc(chapterSection));
                    chapterStr.append(knowledgeGraph.getName());
                    builder.append(chapterStr).append("\n");
                }
            } else {
                builder.append(knowledgeGraph.getName()).append("\n");
            }
        }

        return builder.toString();
    }

    private String getParentDesc(ChapterSection chapterSectionItem) {
        StringBuilder parentChapterSectionStr = new StringBuilder();
        if (chapterSectionItem == null){
            return parentChapterSectionStr.toString();
        }
        StringBuilder chapterSectionStr = new StringBuilder();
        if (chapterSectionItem.getParentId()!=null && chapterSectionItem.getParentId() > 0) {
            ChapterSection chapterSectionItemParent = null;
            try {
                chapterSectionItemParent = knowledgeResource.getChapterSectionItemById(chapterSectionItem.getParentId());
            } catch (DataAccessException e) {
                log.warn("getChapterSectionItemById error, param:{}",chapterSectionItem.getParentId());
            }
            return parentChapterSectionStr.append( this.getParentDesc(chapterSectionItemParent))
                    .append(chapterSectionItem.getName()).append("(id:").append(chapterSectionItem.getId()).append(")")
                    .append(" >> ").toString();
        } else {
            return chapterSectionStr.append(chapterSectionItem.getName()).append("(id:").append(chapterSectionItem.getId())
                    .append(")").append(" >> ").toString();
        }
    }

    private String getTopicStr(QuestionTopic topic,int index) {
        StringBuffer builder = new StringBuffer();
        if (Objects.isNull(topic)){
            return StringUtils.EMPTY;
        }
        builder.append("第").append(index).append("题:").append("\r\n");
        String content = replaceHtml(StringEscapeUtils.unescapeHtml4(topic.getContent()));
        builder.append(content).append("\r\n");

        if (Objects.equals(topic.getQtype(), Consts.QType.QType0.getCode())
                || Objects.equals(topic.getQtype(), Consts.QType.QType1.getCode())
                || Objects.equals(topic.getQtype(), Consts.QType.QType2.getCode())
                || Objects.equals(topic.getQtype(), Consts.QType.QType3.getCode())
        ) {
            if (CollectionUtils.isNotEmpty(topic.getOptionList())) {
                builder.append(" 选项:").append("\r\n");
                for (QuestionOptions options : topic.getOptionList()) {
                    builder.append(options.getSeq()).append(":").append(replaceHtml(StringEscapeUtils.unescapeHtml4(options.getContent()))).append("\r\n");
                }
            }
            builder.append("正确答案:").append(topic.getAnswerOption()).append("\r\n");
        } else if (Objects.equals(topic.getQtype(), Consts.QType.QType4.getCode())) {
            if (CollectionUtils.isNotEmpty(topic.getOptionList())) {
                builder.append(" 答案:").append("\r\n");
                for (QuestionOptions options : topic.getOptionList()) {
                    builder.append("  第").append(options.getSeq()).append("空:").append(replaceHtml(StringEscapeUtils.unescapeHtml4(options.getContent()))).append("\r\n");
                }
            }
        }

        // 添加解析
        if (Objects.nonNull(topic.getAnalysisText())) {
            String analysisText = null;
            if (StringUtils.startsWith(topic.getAnalysisText(),"http://") || StringUtils.startsWith(topic.getAnalysisText(),"https://")) {
                analysisText = replaceHtml(StringEscapeUtils.unescapeHtml4(OssUtil.getContentDataFromOss(topic.getAnalysisText())));
            } else {
                analysisText = replaceHtml(StringEscapeUtils.unescapeHtml4(topic.getAnalysisText()));
            }
            builder.append("参考解析:").append(analysisText).append("\r\n");
        }

        return builder.toString();
    }


    /**
     * 替换掉HTML标签方法
     */
    private static String replaceHtml(String html) {
        if (StringUtils.isBlank(html)) {
            return "";
        }
        String regEx = "<.+?>";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(html);
        String s = m.replaceAll("");
        return s;
    }
}
