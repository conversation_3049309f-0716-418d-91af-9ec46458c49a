package cn.huanju.edu100.study.service.mock;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.mock.MockApply;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 模考考生报名Service
 * <AUTHOR>
 * @version 2018-04-08
 */
public interface MockApplyService extends BaseService<MockApply> {
    public List<MockApply> qryByMockExamId(Long mockExamId, Long uid) throws DataAccessException;

    /**
    *
    * <AUTHOR> duxiulei
    * @Description :根据模考活动id获取各科目排行（前n名用户,如果传uid则带出uid用户排名）
    * @Date : 2020/8/10
    *
    */
    public List<MockApply> findMockApplyListByMockId(Long mockExamId,Long number,Long uid) throws DataAccessException;

    
    /**
    *
    * <AUTHOR> duxiulei
    * @Description :根据模考id、模考科目id获取报名人数
    * @Date : 2020/8/22
    *
    */
    public Long countMockApplyByMockIdAndSubjectId(Long mockExamId,Long mockSubjectId,Integer examStatus,Long isResit) throws DataAccessException;
    public Long countMockApplyByMockIdAndSubjectIdList(Long mockExamId, List<Long> mockSubjectId) throws DataAccessException;
    /**
    *
    * <AUTHOR> duxiulei
    * @Description :根据模考id、模考科目id获取交卷人数
    * @Date : 2020/8/24
    *
    */
    public Long countMockApplySubmitByMockIdAndSubjectId(Long mockExamId,Long mockSubjectId,Long isResit) throws DataAccessException;



    /**
    *
    * <AUTHOR> duxiulei
    * @Description :获取作答时间排名
    * @Date : 2020/8/26
    *
    */
    public List<MockApply> getMockUseTimeRank(Long mockExamId,Long uid) throws DataAccessException;
    public MockApply qryByUserAnswerId(Long userAnswerId, Long uid) throws DataAccessException;
}