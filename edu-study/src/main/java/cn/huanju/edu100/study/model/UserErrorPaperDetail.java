package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 用户错题详情Entity
 * <AUTHOR>
 * @version 2015-05-13
 */
public class UserErrorPaperDetail extends DataEntity<UserErrorPaperDetail> {

	private static final long serialVersionUID = 1L;
	private Long pId;		// p_id
	private Long uid;		// uid
	private Long questionId;		// 题目id
	private Long questionTopicId;		// 子题id
	private Double num;		// 用户得分
	private Date lastErrorTime;		// last_error_time
	private String lastErrorAnswer;		// last_error_answer

	public UserErrorPaperDetail() {
		super();
	}

	public UserErrorPaperDetail(Long pId, Long uid, Long questionId, Double num, Date lastErrorTime, String lastErrorAnswer) {
		this.pId = pId;
		this.uid = uid;
		this.questionId = questionId;
		this.num = num;
		this.lastErrorTime = lastErrorTime;
		this.lastErrorAnswer = lastErrorAnswer;
	}

	public Long getpId() {
		return pId;
	}

	public void setpId(Long pId) {
		this.pId = pId;
	}

	public Long getQuestionTopicId() {
		return questionTopicId;
	}

	public void setQuestionTopicId(Long questionTopicId) {
		this.questionTopicId = questionTopicId;
	}

	public UserErrorPaperDetail(Long id){
		super(id);
	}

	public Long getPId() {
		return pId;
	}

	public void setPId(Long pId) {
		this.pId = pId;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}

	public Double getNum() {
		return num;
	}

	public void setNum(Double num) {
		this.num = num;
	}

	public Date getLastErrorTime() {
		return lastErrorTime;
	}

	public void setLastErrorTime(Date lastErrorTime) {
		this.lastErrorTime = lastErrorTime;
	}

	public String getLastErrorAnswer() {
		return lastErrorAnswer;
	}

	public void setLastErrorAnswer(String lastErrorAnswer) {
		this.lastErrorAnswer = lastErrorAnswer;
	}

}
