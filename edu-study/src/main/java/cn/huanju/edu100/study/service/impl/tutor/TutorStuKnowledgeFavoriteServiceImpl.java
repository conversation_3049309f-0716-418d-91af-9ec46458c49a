package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorStuKnowledgeFavoriteDao;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.study.model.tutor.TutorStuKnowledgeFavorite;
import cn.huanju.edu100.study.service.tutor.TutorStuKnowledgeFavoriteService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 学生微课班知识点收藏Service
 *
 * <AUTHOR>
 * @version 2017-12-28
 */
@Service
public class TutorStuKnowledgeFavoriteServiceImpl extends BaseServiceImpl<TutorStuKnowledgeFavoriteDao, TutorStuKnowledgeFavorite> implements TutorStuKnowledgeFavoriteService {

    @Override
    public boolean collectWkKnow(TutorStuKnowledgeFavorite params) throws BusinessException, DataAccessException {

        Set<Long> kIdSet = Sets.newHashSet();
        for (Long kId : params.getKnowIdList()) {
            kIdSet.add(kId);
        }
        List<TutorStuKnowledgeFavorite> olds = dao.getByUidAndKIds(params.getUid(), params.getWeikeId(), params.getKnowIdList());

        if (CollectionUtils.isNotEmpty(olds)) {
            for (TutorStuKnowledgeFavorite favorite : olds) {
                kIdSet.remove(favorite.getKnowledgeId());
                if ((favorite.getDelFlag().equals("0") && params.getStatus() == Consts.Tutor_Wk_Know_Status.CANCEL)
                        || (favorite.getDelFlag().equals("1") && params.getStatus() == Consts.Tutor_Wk_Know_Status.COLLECT)) {
                    params.setUpdateBy(1L);
                    dao.updateStatus(favorite.getId(), params.getStatus());
                }
            }
        }

        Date date = new Date();
        List<TutorStuKnowledgeFavorite> inserts = Lists.newArrayList();
        if (params.getStatus() == Consts.Tutor_Wk_Know_Status.COLLECT && CollectionUtils.isNotEmpty(kIdSet)) {
            for (Long kId : kIdSet) {
                TutorStuKnowledgeFavorite favorite = new TutorStuKnowledgeFavorite();
                favorite.setKnowledgeId(kId);
                favorite.setUid(params.getUid());
                favorite.setWeikeId(params.getWeikeId());
                favorite.setStatus(params.getStatus());
                favorite.setCreateBy(1L);
                favorite.setUpdateBy(1L);
                favorite.setCreateDate(date);
                favorite.setUpdateDate(date);
                inserts.add(favorite);
            }

            dao.insertBatch(inserts);
        }
        return true;
    }

    @Override
    public List<TutorSectionTask> listCollectWkTask(Long uid, Long weikeId, Integer type) throws BusinessException, DataAccessException {
        if (!IdUtils.isValid(uid) || !IdUtils.isValid(weikeId)) {
            logger.error("param illegal, uid or weikeId is null");
            throw new BusinessException(Constants.PARAM_INVALID, "param illegal, uid or weikeId is null");
        }

        return dao.listCollectWkTask(uid, weikeId, type);
    }
}
