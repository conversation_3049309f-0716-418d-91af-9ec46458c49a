package cn.huanju.edu100.study.model.dto;

import java.util.Date;

/**
 * Created by zhanghong on 2021/9/7.
 *
 * 用户最近一次试卷答题情况统计
 */
public class UserAnswerLastInfo {

    private String answerId;

    private Long paperId;   //对应的试卷资源id

    private Long productId;  //对应的试卷产品id

    private Long finishCount;    //提交试卷的答题数量

    private String accuracy;

    private Date lastAnswerTime;    //上一次作答时间

    private Double scorce;  //上一次作答分数

    private Integer state;		// 状态，0未开始 1进行中 2已交卷 3已评卷

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public Long getFinishCount() {
        return finishCount;
    }

    public void setFinishCount(Long finishCount) {
        this.finishCount = finishCount;
    }

    public String getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(String accuracy) {
        this.accuracy = accuracy;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Date getLastAnswerTime() {
        return lastAnswerTime;
    }

    public void setLastAnswerTime(Date lastAnswerTime) {
        this.lastAnswerTime = lastAnswerTime;
    }

    public Double getScorce() {
        return scorce;
    }

    public void setScorce(Double scorce) {
        this.scorce = scorce;
    }

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
