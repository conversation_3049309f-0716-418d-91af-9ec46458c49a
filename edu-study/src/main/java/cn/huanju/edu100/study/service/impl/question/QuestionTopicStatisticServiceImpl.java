package cn.huanju.edu100.study.service.impl.question;

import cn.huanju.edu100.study.mapper.question.QuestionTopicStatisticMapper;
import cn.huanju.edu100.study.model.question.QuestionTopicStatistic;
import cn.huanju.edu100.study.service.question.QuestionTopicStatisticService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqwx.study.dto.query.QuestionTopicStatisticQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class QuestionTopicStatisticServiceImpl extends ServiceImpl<QuestionTopicStatisticMapper, QuestionTopicStatistic> implements QuestionTopicStatisticService {

    @Autowired
    QuestionTopicStatisticMapper questionTopicStatisticMapper;

    @Override
    public List<QuestionTopicStatistic> getQuestionTopicStatisticList(QuestionTopicStatisticQuery query) {
        if (query == null || CollectionUtils.isEmpty(query.getTopicIdList())) {
            return null;
        }
        LambdaQueryWrapper<QuestionTopicStatistic> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(QuestionTopicStatistic::getTopicId, query.getTopicIdList());
        List<QuestionTopicStatistic> questionTopicStatisticList = questionTopicStatisticMapper.selectList(wrapper);
        return questionTopicStatisticList;
    }

}