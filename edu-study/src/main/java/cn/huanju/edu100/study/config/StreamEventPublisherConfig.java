package cn.huanju.edu100.study.config;

import com.hqwx.cloud.stream.StreamEventPublisher;
import com.hqwx.cloud.stream.impl.StreamEventPublisherImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/8 16:14
 * @description
 */
@Configuration
public class StreamEventPublisherConfig {

    @Bean("userAnswerPublisher")
    public StreamEventPublisher userAnswerPublisher() {
        StreamEventPublisherImpl streamEventPublisher = new StreamEventPublisherImpl();
        return streamEventPublisher;
    }
}
