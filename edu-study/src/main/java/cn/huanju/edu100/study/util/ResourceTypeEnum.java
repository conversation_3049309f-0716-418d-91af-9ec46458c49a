package cn.huanju.edu100.study.util;

import org.apache.commons.lang3.StringUtils;

public enum ResourceTypeEnum {
    VIDEO_WARE(100, "video_ware", "视频课件"),
    VIDEO_ASSET(105, "video_asset", "视频资源"),
    VIDEO_FILE(110, "video_file", "视频文件(基础)"),
    LIVE_ROOM(160, "live_room", "直播教室"),
    LIVE_LESSON(165, "live_lesson", "直播课节"),
    LIVE_MOCK(170, "live_mock", "伪直播课节"),
    VOICE_LESSON(180, "voice_lesson", "语音课节"),
    QUESTION_BASE(200, "question_base", "题目"),
    QUESTION_BOX(210, "question_box", "题库"),
    LESSON_WORK(220, "lesson_work", "作业"),
    TEST_PAPER(230, "test_paper", "试卷"),
    ELEC_PAPER(232, "elec_paper", "电子试卷"),
    MATERIAL_BASE(250, "material_base", "资料"),
    MATERIAL_GROUP(252, "material_group", "资料组"),
    COURSE_SERVICE(260, "course_service", "课程服务"),
    COURSE_AGREEMENT(265, "course_agreement", "课程协议"),
    BOOK_BASE(270, "book_base", "纸制图书"),
    PRODUCT_THIRD(280, "product_third", "第三方产品"),
    KNOWLEDGE_POINT(300, "knowledge_point", "知识点"),
    KNOWLEDGE_GRAPH(310, "knowledge_graph", "知识图谱"),
    TEACH_BOOK(370, "teach_book", "教材"),
    TEACH_CHAPTER(372, "teach_chapter", "章节"),
    TEACHER_BASE(380, "teacher_base", "老师(通用)"),
    TEACHER_CLASS(380, "teacher_class", "班主任"),
    GOODS_SPU(600, "goods_spu", "商品"),
    GOODS_SKU(610, "goods_sku", "商品SKU"),
    GOODS_PACKAGE(630, "goods_package", "课程包"),
    GOODS_GIFT(650, "goods_gift", "商品赠品"),
    GOODS_MIX(652, "goods_mix", "商品配件"),
    COURSE_SCHEDULE(660, "course_schedule", "课程表"),
    COURSE_STAGE(670, "course_stage", "课程阶段"),
    CATEGORY_COMMON(920, "category_common", "后台类目");

    private int typeId;
    private String typeCode;
    private String typeName;

    private ResourceTypeEnum(int typeId, String typeCode, String typeName) {
        this.typeId = typeId;
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public static ResourceTypeEnum getByTypeCode(String typeCode) {
        if (StringUtils.isBlank(typeCode)) {
            return null;
        } else {
            ResourceTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                ResourceTypeEnum type = var1[var3];
                if (type.getTypeCode().equals(typeCode)) {
                    return type;
                }
            }

            return null;
        }
    }

    public int getTypeId() {
        return this.typeId;
    }

    public String getTypeCode() {
        return this.typeCode;
    }

    public String getTypeName() {
        return this.typeName;
    }
}