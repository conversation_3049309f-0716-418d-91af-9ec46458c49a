/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorTaskLessonDao;
import cn.huanju.edu100.study.model.tutor.TutorTaskLesson;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 录播课详情DAO接口
 * <AUTHOR>
 * @version 2016-01-18
 */
public class TutorTaskLessonIbatisImpl extends CrudIbatisImpl2<TutorTaskLesson> implements
		TutorTaskLessonDao {

	public TutorTaskLessonIbatisImpl() {
		super("TutorTaskLesson");
	}

    @Override
    public List<TutorTaskLesson> getByIdList(List<Long> taskIdList) throws DataAccessException {

        if (CollectionUtils.isEmpty(taskIdList)) {
            return null;
        }

        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("taskIdList", taskIdList);

            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorTaskLesson>) sqlMap
                    .queryForList("TutorTaskLesson.findList", params);
        } catch (SQLException e) {
            logger.error("getByIdList SQLException.", e);
            throw new DataAccessException("getByIdList SQLException error");
        }
    }

    @Override
    public List<TutorTaskLesson> findByClassesAndLessonId(String classes, Long lessonId) throws DataAccessException {
        if (StringUtils.isEmpty(classes) || lessonId == null) {
            logger.error("findByClassesAndLessonId error, parameter classes or lessonId is empty");
            throw new DataAccessException("findByClassesAndLessonId error, parameter classes or lessonId is empty");
        }
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("classes",classes);
            params.put("lessonId", lessonId);
            SqlMapClient sqlMap = super.getSlave();
            return (List<TutorTaskLesson>) sqlMap.queryForList("TutorTaskLesson.findByClassesAndLessonId", params);
        }catch (SQLException e) {
            logger.error("findByClassesAndLessonId SQLException",e);
            throw new DataAccessException("findByClassesAndLessonId SQLException error");
        }

    }

}
