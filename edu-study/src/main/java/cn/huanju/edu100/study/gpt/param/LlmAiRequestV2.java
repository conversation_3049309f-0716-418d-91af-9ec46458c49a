package cn.huanju.edu100.study.gpt.param;

import cn.huanju.edu100.util.JSONUtils;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * @author: zhangtong
 * @create: 2024/1/31 6:22 PM
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LlmAiRequestV2 implements Serializable {

    @ApiModelProperty( "用户唯一标识")
    private String user;

    @ApiModelProperty( "问题具体内容")
    private List<MessageV2> messages;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MessageV2 implements Serializable{
        private String name;
        @ApiModelProperty( "user,assistant,system,function")
        private String role;
        @ApiModelProperty( "消息内容,可以是string也可以是数组")
        private MessageContent content;
        private String toolCallId;
        @JsonAlias({"function_call","functionCall"})
        private FunctionCallV2 functionCall;
        public String getContentString() {
            return content.toString();
        }

        @Data
        @NoArgsConstructor
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonSerialize(using = ContentSerializer.class)
        @JsonDeserialize(using = ContentDeserialize.class)
        public static class MessageContent implements Serializable{
            private String value;
            private List<MessageListContent> list;

            public MessageContent(String value) {
                this.value = value;
            }

            public MessageContent(List<MessageListContent> list) {
                this.list = list;
            }

            @Override
            public String toString(){
                if(StringUtils.isNotBlank(value)){
                    return value;
                }else if(CollectionUtils.isNotEmpty(list)){
                    return JSONUtils.toJsonString(list);
                }else {
                    return null;
                }
            }
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class MessageListContent implements Serializable{
                private String type;
                private String text;
                @JsonAlias({"image_url","imageUrl"})
                private MessageImageUrl imageUrl;

                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                public static class MessageImageUrl implements Serializable{
                    private String url;
                    private String detail;
                }
            }

        }

        public static class ContentSerializer extends JsonSerializer<MessageContent> {
            @Override
            public void serialize(MessageContent o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                if(StringUtils.isNotBlank(o.getValue())){
                    jsonGenerator.writeString(o.getValue());
                }else if(CollectionUtils.isNotEmpty(o.getList())) {
                    jsonGenerator.writeObject(o.getList());
                }else{
                    jsonGenerator.writeObject(o.getValue());
                }
            }
        }

        public static class ContentDeserialize extends JsonDeserializer<MessageContent> {
            @Override
            public MessageContent deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
                if(jsonParser.getCurrentToken() == JsonToken.START_ARRAY){
                    return new MessageContent(jsonParser.getCodec().readValue(jsonParser,new TypeReference<List<MessageContent.MessageListContent>>(){}));
                }else{
                    return new MessageContent(jsonParser.getText());
                }
            }
        }
    }
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FunctionCallV2 implements Serializable {
        @JsonProperty("name")
        private String name;
        @JsonProperty("arguments")
        private String arguments;
    }

    @ApiModelProperty( "问题具体内容")
    private String provider = "openai";

    @Deprecated
    @ApiModelProperty( "用户自定义函数")
    private  List<FunctionV2> functions;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FunctionV2 implements Serializable {
        @JsonProperty("name")
        private String name;
        @JsonProperty("description")
        private String description;
        @JsonProperty("parameters")
        private ParametersV2 parameters;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ParametersV2 {
        @JsonProperty("type")
        private String type;
        @JsonProperty("properties")
        private Map properties;
        @JsonProperty("required")
        private List<String> required;
    }

    @ApiModelProperty( "seed")
    private Integer seed;
    @ApiModelProperty( "responseFormat Compatible with gpt-4-1106-preview and gpt-3.5-turbo-1106")
    private ResponseFormat responseFormat;
    @ApiModelProperty( "用户自定义工具")
    private  List<ToolV2> tools;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ToolV2 implements Serializable{
        private String type = "function";
        private FunctionV2 function;
    }

    @ApiModelProperty( "工具选择")
    @JsonAlias({"tool_choice","toolChoice"})
    private  Object toolChoice;

    @Deprecated
    @ApiModelProperty( "指定调用函数")
    private Object function_call;

    @ApiModelProperty( "要使用的采样温度，介于 0 和 2 之间。 较高的值意味着模型将承担更多风险。 对于更具创意的应用程序，请尝试将值设为 0.9，对于具有明确定义答案的应用程序，将值设为 0 (argmax sampling) 。 我们通常建议更改此设置或 top_p，但不要同时更改这两者。")
    private Double temperature;


    @ApiModelProperty( "application 项目名称")
//    @NotEmpty(message = "application 项目名称不能为空")
    private String application;
    @ApiModelProperty( "maxToken 请求生成文本最大长度，不设置走默认")
    private Integer maxToken;
    @ApiModelProperty( "会话")
//    @NotEmpty(message = "会话id不能为空")
    private String conversationId;

    @ApiModelProperty( """
     modelName模型名称 gpt-3.5-turbo,gpt-3.5-turbo,gpt-4-0125-preview,gpt-4-turbo,
     zhipu-glm-4,
     baidu-completions_pro,baidu-completions,
     mini-abab5.5-chat,mini-abab6-chat,
     ali-qwen-plus,ali-qwen-max,ali-qwen-vl-plus
     claude-3-opus-20240229,claude-3-sonnet-20240229,claude-3-haiku-20240307
    """)
    @JsonAlias({"model_name","modelName","model"})
    private String modelName="gpt-3.5-turbo-0613";

    @ApiModelProperty( "是否使用微软openAi 默认不开启false")
    private Boolean  useAuzreOpenAi=Boolean.FALSE;
    @JsonProperty("top_p")
    private Double topP;
    @ApiModelProperty( "要为每个提示生成的完成数。 注意：由于此参数会生成许多完成，因此可能会快速消耗你的令牌配额。 谨慎使用并确保对 max_tokens 和 stop 进行了合理的设置。")
    private Integer n;
    @ApiModelProperty( "介于 -2.0 和 2.0 之间的数字。 正值会根据它们到目前为止在文本中的现有频率来惩罚新令牌，从而降低模型逐字重复同一行的可能性。。")
    @JsonProperty("presence_penalty")
    private Double presencePenalty;
    @ApiModelProperty( "介于 -2.0 和 2.0 之间的数字。 正值会根据它们到目前为止在文本中的现有频率来惩罚新令牌，从而降低模型逐字重复同一行的可能性。")
    @JsonProperty("frequency_penalty")
    private Double frequencyPenalty;
    @ApiModelProperty( "修改指定令牌在完成中出现的可能性。 接受 json 对象，该对象将令牌（由其在 GPT tokenizer 中的令牌 ID 指定）映射到从 -100 到 100 的关联偏差。 可以使用此 tokenizer 工具（适用于 GPT-2 和 GPT-3）将文本转换为令牌 ID。 在数学上，采样之前会将偏差添加到由模型生成的 logit 中。 具体效果因模型而异，但 -1 和 1 之间的值会减少或增加选择的可能性；-100 或 100 等值会导致相关令牌的禁止或独占选择。 例如，可以传递 {\"50256\": -100} 以防止生成 <|endoftext|> 令牌。")
    @JsonProperty("logit_bias")
    private Map logitBias;

    private Boolean logprobs;
    private Integer topLogprobs;
}
