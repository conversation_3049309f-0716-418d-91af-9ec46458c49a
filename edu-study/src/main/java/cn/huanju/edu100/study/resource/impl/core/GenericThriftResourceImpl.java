package cn.huanju.edu100.study.resource.impl.core;

import cn.huanju.edu100.study.resource.AbstractThriftResource;
import cn.huanju.edu100.study.resource.IGenericThriftResource;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.ExceptionHelper;
import cn.huanju.edu100.util.IpConvert;
import cn.huanju.edu100.util.MaskClock;
import cn.huanju.edu100.util.TraceDIServiceLogBean;
import com.hqwx.thrift.client.thrift.ThriftClientFactory;
import com.hqwx.thrift.client.thrift.ThriftClientWrapper;

import cn.huanju.edu100.exception.DataAccessException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * This class is used for ...
 *
 * @version 1.0
 * <AUTHOR>
 * @time 2015-1-12 下午9:20:17
 */
@Component
public class GenericThriftResourceImpl extends AbstractThriftResource implements IGenericThriftResource {

    private static final Logger log = LoggerFactory.getLogger(GenericThriftResourceImpl.class);
    private static Logger lessImportLogger = LoggerFactory.getLogger("lessimport.logger.GenericThriftResourceImpl");
    private static final String knowledgeProcess = "edu-knowledge";
    private static final String goodsProcess = "edu-goods";
    private static final String toolsProcess = "edu-tools";
    private static final String analyseProcess = "edu-analyse";
    private static final String hqUserProcess = "edu-user";
    private static final String hqStstampProcess = "edu-stustamp";
    private static final String searchProcess = "edu-search";
    private static final String memProcess = "edu-member";

    private static final Long defaultSchId = 1L;

    @Autowired
    private ThriftClientFactory<cn.huanju.edu100.thrift.edu100_knowledge.Iface> knowledgeClientFactory;

    @Autowired
    private ThriftClientFactory<cn.huanju.edu100.thrift.edu100_goods.Iface> goodsClientFactory;

    @Autowired
    private ThriftClientFactory<cn.huanju.edu100.thrift.edu100_analyse.Iface> analyseClientFactory;

    @Autowired
    private ThriftClientFactory<cn.huanju.edu100.thrift.edu_user.Iface> hqUserClientFactory;

    @Autowired
    private ThriftClientFactory<cn.huanju.edu100.thrift.edu100_stustamp.Iface> hqStustampClientFactory;

    @Autowired
    private ThriftClientFactory<cn.huanju.edu100.thrift.edu100_search.Iface> hqSearchClientFactory;

    @Autowired
    private ThriftClientFactory<cn.huanju.edu100.thrift.edu100_mem.Iface> memClientFactory;

    @Override
    public response generalKnowledgeThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException {
        return generalKnowledgeThriftMethodInvoke(jsonParam, appid, clientIp, null, null, methodName);
    }

    @Override
	public response generalKnowledgeThriftMethodInvoke(String jsonParam,
			int appid, String clientIp, Long schId, Long pschId,
			String methodName) throws DataAccessException {
    	ThriftClientWrapper<cn.huanju.edu100.thrift.edu100_knowledge.Iface> client = null;

        long start = MaskClock.getCurtime();
        DataAccessException opException = null;
        String opResultCode = "-1";

        response res = new response();
        enter(methodName, jsonParam);
        try {
            client = knowledgeClientFactory.createClient();
            request req = getRequest(jsonParam, appid, clientIp);
            if (schId != null && schId > 0L) {
				req.setSchId(schId);
			}
			if (pschId != null && pschId > 0L) {
				req.setPschId(pschId);
			}
            Method method = cn.huanju.edu100.thrift.edu100_knowledge.Iface.class.getMethod(methodName, request.class);
            if (method != null) {
                res = (response) method.invoke(client.getClient(), req);
            }
            opResultCode = "" + res.getCode();
        } catch (Exception e) {
            log.error("invoke knowledge ThriftServer method {} param:{} exception.", methodName, jsonParam, e);
            opException = this.buildException("" + ThriftReturnCode.SYS_ERROR.getType(), methodName,
                    ExceptionHelper.getMessage4BackServLog(e));
            opResultCode = opException.getCode();

            throw opException;
        } finally {
            if (client != null) {
                client.close();
            }
            long deltatime = MaskClock.getCurtime() - start;
            this.diServiceLogService.traceDIServiceLog(new TraceDIServiceLogBean(-1L + "", methodName,
                    knowledgeProcess, "thrift", jsonParam, deltatime, opResultCode, opException, ""));
        }
        leave(methodName, res, start);
        return res;
	}
    @Override
    public response generalAnalyseThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException {
        ThriftClientWrapper<cn.huanju.edu100.thrift.edu100_analyse.Iface> client = null;

        long start = MaskClock.getCurtime();
        DataAccessException opException = null;
        String opResultCode = "-1";

        response res = new response();
        enterWithLessLog(methodName, jsonParam);
        try {
            client = analyseClientFactory.createClient();
            request req = getRequest(jsonParam, appid, clientIp);
            Method method = cn.huanju.edu100.thrift.edu100_analyse.Iface.class.getMethod(methodName, request.class);
            if (method != null) {
                res = (response) method.invoke(client.getClient(), req);
            }
            opResultCode = "" + res.getCode();
        } catch (Exception e) {
            lessImportLogger.error("invoke analyse ThriftServer method {} param:{} exception.", methodName, jsonParam, e);
            opException = this.buildException("" + ThriftReturnCode.SYS_ERROR.getType(), methodName,
                    ExceptionHelper.getMessage4BackServLog(e));
            opResultCode = opException.getCode();

            throw opException;
        } finally {
            if (client != null) {
                client.close();
            }
            long deltatime = MaskClock.getCurtime() - start;
            this.diServiceLogService.traceDIServiceLog(new TraceDIServiceLogBean(-1L + "", methodName,
                    analyseProcess, "thrift", jsonParam, deltatime, opResultCode, opException, ""));
        }
        leaveWithLessLog(methodName, res, start);
        return res;
    }

    @Override
    public response generalGoodsThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException {
        ThriftClientWrapper<cn.huanju.edu100.thrift.edu100_goods.Iface> client = null;

        long start = MaskClock.getCurtime();
        DataAccessException opException = null;
        String opResultCode = "-1";

        response res = new response();
        enter(methodName, jsonParam);
        try {
            client = goodsClientFactory.createClient();
            request req = getRequest(jsonParam, appid, clientIp);
            Method method = cn.huanju.edu100.thrift.edu100_goods.Iface.class.getMethod(methodName, request.class);
            if (method != null) {
                res = (response) method.invoke(client.getClient(), req);
            }
            opResultCode = "" + res.getCode();
        } catch (Exception e) {
            log.error("invoke goods ThriftServer method {} param:{} exception.", methodName, jsonParam, e);
            opException = this.buildException("" + ThriftReturnCode.SYS_ERROR.getType(), methodName,
                    ExceptionHelper.getMessage4BackServLog(e));
            opResultCode = opException.getCode();

            throw opException;
        } finally {
            if (client != null) {
                client.close();
            }
            long deltatime = MaskClock.getCurtime() - start;
            this.diServiceLogService.traceDIServiceLog(new TraceDIServiceLogBean(-1L + "", methodName, goodsProcess,
                    "thrift", jsonParam, deltatime, opResultCode, opException, ""));
        }
        leave(methodName, res, start);
        return res;
    }

    private void enter(String methodName, String paramStr) {
        if (log.isDebugEnabled()) {
            log.debug("enter {} parameter:{} ", methodName, paramStr);
        }
    }


    private void enterWithLessLog(String methodName, String paramStr) {
        if (lessImportLogger.isDebugEnabled()) {
            lessImportLogger.debug("enter {} parameter lenth:{} ", methodName, paramStr.length());
        }
    }

    private void leaveWithLessLog(String methodName, response res, long start) {
        if (lessImportLogger.isDebugEnabled()) {
            long elapsed = (MaskClock.getCurtime() - start);

            if (null == res.getMsg() || (null != res.getMsg() && res.getMsg().length() < 100)) {
                lessImportLogger.debug("end {} context{} elapsed:{},result {},errormsg:{} ", methodName, start, elapsed,
                        res.getMsg(), res.getErrormsg());
            } else {
                lessImportLogger.debug("end {} context{} elapsed:{},result length: {},errormsg:{} ", methodName, start, elapsed, res
                        .getMsg().length(), res.getErrormsg());
            }
        }
    }
    private void leave(String methodName, response res, long start) {
        if (log.isDebugEnabled()) {
            long elapsed = (MaskClock.getCurtime() - start);

            if (null == res.getMsg() || (null != res.getMsg() && res.getMsg().length() < 100)) {
                log.debug("end {} context{} elapsed:{},result {},errormsg:{} ", methodName, start, elapsed,
                        res.getMsg(), res.getErrormsg());
            } else {
                log.debug("end {} context{} elapsed:{},result length: {},errormsg:{} ", methodName, start, elapsed, res
                        .getMsg().length(), res.getErrormsg());
            }
        }
    }

    @Override
    public response generalHqUserThriftMethodInvoke(String jsonParam, int appid, String clientIp, Long schId, Long pschId, String methodName)
            throws DataAccessException {

        ThriftClientWrapper<cn.huanju.edu100.thrift.edu_user.Iface> client = null;
        long start = MaskClock.getCurtime();
        DataAccessException opException = null;
        String opResultCode = "-1";

        response res = new response();
        enter(methodName, jsonParam);
        try {
            client = hqUserClientFactory.createClient();
            request req = getRequest(jsonParam, appid, clientIp);
            if (schId != null && schId > 0L) {
                req.setSchId(schId);
            }
            if (pschId != null && pschId > 0L) {
                req.setPschId(pschId);
            }
            Method method = cn.huanju.edu100.thrift.edu_user.Iface.class.getMethod(methodName, request.class);
            if (method != null) {
                res = (response) method.invoke(client.getClient(), req);
            }
            opResultCode = "" + res.getCode();
        } catch (Exception e) {
            log.error("invoke hq user ThriftServer method {} param:{} exception.", methodName, jsonParam, e);
            opException = this.buildException("" + ThriftReturnCode.SYS_ERROR.getType(), methodName,
                    ExceptionHelper.getMessage4BackServLog(e));
            opResultCode = opException.getCode();

            throw opException;
        } finally {
            if (client != null) {
                client.close();
            }
            long deltatime = MaskClock.getCurtime() - start;
            this.diServiceLogService.traceDIServiceLog(new TraceDIServiceLogBean(-1L + "", methodName, hqUserProcess,
                    "thrift", jsonParam, deltatime, opResultCode, opException, ""));
        }
        leave(methodName, res, start);
        return res;
    }

    @Override
    public response generalStustampThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException {

        ThriftClientWrapper<cn.huanju.edu100.thrift.edu100_stustamp.Iface> client = null;

        long start = MaskClock.getCurtime();
        DataAccessException opException = null;
        String opResultCode = "-1";

        response res = new response();
        enter(methodName, jsonParam);
        try {
            client = hqStustampClientFactory.createClient();
            request req = getRequest(jsonParam, appid, clientIp);
            Method method = cn.huanju.edu100.thrift.edu100_stustamp.Iface.class.getMethod(methodName, request.class);
            if (method != null) {
                res = (response) method.invoke(client.getClient(), req);
            }
            opResultCode = "" + res.getCode();
        } catch (Exception e) {
//            e.printStackTrace();
            log.error("invoke stustamp ThriftServer method {} param:{} exception.", methodName, jsonParam, e);
            opException = this.buildException("" + ThriftReturnCode.SYS_ERROR.getType(), methodName,
                    ExceptionHelper.getMessage4BackServLog(e));
            opResultCode = opException.getCode();

            throw opException;
        } finally {
            if (client != null) {
                client.close();
            }
            long deltatime = MaskClock.getCurtime() - start;
            this.diServiceLogService.traceDIServiceLog(new TraceDIServiceLogBean(-1L + "", methodName, hqStstampProcess,
                    "thrift", jsonParam, deltatime, opResultCode, opException, ""));
        }
        leave(methodName, res, start);
        return res;
    }

    @Override
    public response generalSearchThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName) throws DataAccessException {
        ThriftClientWrapper<cn.huanju.edu100.thrift.edu100_search.Iface> client = null;

        long start = MaskClock.getCurtime();
        DataAccessException opException = null;
        String opResultCode = "-1";

        response res = new response();
        enter(methodName, jsonParam);
        try {
            client = hqSearchClientFactory.createClient();
            request req = getRequest(jsonParam, appid, clientIp);
            Method method = cn.huanju.edu100.thrift.edu100_search.Iface.class.getMethod(methodName, request.class);
            if (method != null) {
                res = (response) method.invoke(client.getClient(), req);
            }
            opResultCode = "" + res.getCode();
        } catch (Exception e) {
            log.error("invoke search ThriftServer method {} param:{} exception.", methodName, jsonParam, e);
            opException = this.buildException("" + ThriftReturnCode.SYS_ERROR.getType(), methodName,
                    ExceptionHelper.getMessage4BackServLog(e));
            opResultCode = opException.getCode();

            throw opException;
        } finally {
            if (client != null) {
                client.close();
            }
            long deltatime = MaskClock.getCurtime() - start;
            this.diServiceLogService.traceDIServiceLog(new TraceDIServiceLogBean(-1L + "", methodName, searchProcess,
                    "thrift", jsonParam, deltatime, opResultCode, opException, ""));
        }
        leave(methodName, res, start);
        return res;
    }

    @Override
    public response generalMemThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName)
            throws DataAccessException {
        return  generalMemThriftMethodInvoke( jsonParam,  appid,  clientIp, methodName,-1L);
    }

    @Override
    public response generalMemThriftMethodInvoke(String jsonParam, int appid, String clientIp, String methodName, Long schId)  throws DataAccessException{
        ThriftClientWrapper<cn.huanju.edu100.thrift.edu100_mem.Iface> client = null;

        long start = MaskClock.getCurtime();
        DataAccessException opException = null;
        String opResultCode = "-1";

        response res = new response();
        enter(methodName, jsonParam);
        try {
            client = memClientFactory.createClient();
            request req = getRequest(jsonParam, appid, clientIp, schId);
            Method method = cn.huanju.edu100.thrift.edu100_mem.Iface.class.getMethod(methodName, request.class);
            if (method != null) {
                res = (response) method.invoke(client.getClient(), req);
            }
            opResultCode = "" + res.getCode();
        } catch (Exception e) {
            log.error("invoke mem ThriftServer method {} param:{} exception.", methodName, jsonParam, e);
            opException = this.buildException("" + ThriftReturnCode.SYS_ERROR.getType(), methodName,
                    ExceptionHelper.getMessage4BackServLog(e));
            opResultCode = opException.getCode();

            throw opException;
        } finally {
            if (client != null) {
                client.close();
            }
            long deltatime = MaskClock.getCurtime() - start;
            this.diServiceLogService.traceDIServiceLog(new TraceDIServiceLogBean(-1L + "", methodName, memProcess,
                    "thrift", jsonParam, deltatime, opResultCode, opException, ""));
        }
        leave(methodName, res, start);
        return res;
    }

    private request getRequest(String jsonParam, int appid, String clientIp) {
        return getRequest(jsonParam, appid, clientIp, defaultSchId);
    }
    private request getRequest(String jsonParam, int appid, String clientIp, Long schId) {
        request req = new request();
        req.setAppid(appid);
        req.setClient_ip(IpConvert.ipToLong(clientIp));
        req.setCodetype(1);
        req.setSchId(schId);
        req.setMsg(jsonParam);
        req.setTraceId(MDC.get(Consts.LOG_PARAM.TRACE_ID));
        return req;
    }
}
