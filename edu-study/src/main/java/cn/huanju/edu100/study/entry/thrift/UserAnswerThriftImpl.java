package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.UserAnswerDetailDao;
import cn.huanju.edu100.study.dao.UserAnswerSumDao;
import cn.huanju.edu100.study.model.PageModel;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.dto.UserAnswerCount;
import cn.huanju.edu100.study.model.dto.UserAnswerHomeworkLastInfo;
import cn.huanju.edu100.study.model.dto.UserAnswerLastInfo;
import cn.huanju.edu100.study.model.homework.Homework;
import cn.huanju.edu100.study.model.tutor.TutorUserAnswerDto;
import cn.huanju.edu100.study.model.util.AnswerSummaryVo;
import cn.huanju.edu100.study.model.util.UserAnswerUtils;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.*;
import cn.huanju.edu100.study.service.evaluation.EvaluationUserAnswerService;
import cn.huanju.edu100.study.service.evaluation.EvaluationUserBaseAnswerDetailService;
import cn.huanju.edu100.study.service.homework.count.UserAnswerSumCountService;
import cn.huanju.edu100.study.service.question.ErrorQuestionService;
import cn.huanju.edu100.study.service.solution.SolutionQuestionService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.DateConverter;
import cn.huanju.edu100.study.util.ThreadPoolFactoryUtil;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.hqwx.study.dto.*;
import com.hqwx.study.dto.query.*;
import com.hqwx.study.dto.query.wxapp.UserAnswerDetailQuestionQuery;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserAnswerDetailDto;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import com.hqwx.study.vo.UserAnswerErrorQuestionVo;
import com.hqwx.study.vo.UserErrorAndCorrectQuestionCountVo;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
@Component
public class UserAnswerThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(UserAnswerThriftImpl.class);
    static Gson gson = GsonUtil.getGson();

    @Autowired
    private UserAnswerService userAnswerService;
    @Autowired
    private UserAnswerSumService userAnswerSumService;
    @Autowired
    private StudyTaskService studyTaskService;
    @Autowired
    private KnowledgeResource knowledgeResource;
    @Autowired
    private UserQuestionService userQuestionService;
    @Autowired
    private UserQuestionBoxService userQuestionBoxService;
    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;
    @Autowired
    private GoodsResource goodsResource;
    @Autowired
    private ErrorQuestionService errorQuestionService;
    @Autowired
    private UserAnswerMedicalService userAnswerMedicalService;
    @Autowired
    private UserDoneRecordService userDoneRecordService;
    @Autowired
    private UserCollectItemsService userCollectItemsService;
    @Autowired
    private UserAnswerSumCountService userAnswerSumCountService;
    @Autowired
    private EvaluationUserAnswerService evaluationUserAnswerService;
    @Autowired
    private EvaluationUserBaseAnswerDetailService evaluationUserBaseAnswerDetailService;
    @Autowired
    private SolutionQuestionService solutionQuestionService;

    @Autowired
    private UserAnswerDetailDao userAnswerDetailDao;
    @Autowired
    private UserAnswerSumDao userAnswerSumDao;

    private static ThreadPoolExecutor executor = ThreadPoolFactoryUtil.createDefaultPool("UserAnswerThriftImpl");

    public response sty_getPaperAnswerById(request req) throws BusinessException {
        String entry = "sty_getPaperAnswerById";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param.get("id") != null && param.get("id") > 0
                    && param.get("uid") != null && param.get("uid") > 0) {
                Map<String,Object> p1 = JSON.parseObject(req.getMsg(), Map.class);
                Object v1 = p1.get("id");
                Long userAnswerId = null;
                if (v1 instanceof Integer){
                    userAnswerId = Long.valueOf((Integer)v1);
                } else {
                    userAnswerId = (Long) v1;
                }
                Long uid = param.get("uid").longValue();

                UserAnswer userAnswer = userAnswerService.getUserAnswer(param.get("uid").longValue(), userAnswerId, true);
                if (userAnswer != null) {
                    //增加教学方式的返回
                    if (userAnswer.getObjType() != null && userAnswer.getObjType() == UserAnswer.ObjType.AL_PAPER){
                        if (userAnswer.getHasConsolidation() == null) {
                            userAnswer.setHasConsolidation(0);
                        }
                    }
                    res.code = Constants.SUCCESS;
                    res.setMsg(GsonUtil.toJson(userAnswer, req.getAppid()));
                } else {
                    logger.info("{} sty_getPaperAnswerById return null.", entry);
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_getPaperAnswerById return null");
                }

            } else {
                logger.error("{} sty_getPaperAnswerById parameter id or uid is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getPaperAnswerById parameter id or uid is null.");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getPaperAnswerByUid(request req) throws BusinessException {
        String entry = "sty_getPaperAnswerByUid";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);

            if (param.get("uid") != null && param.get("uid") > 0) {
                Long uid = param.get("uid").longValue();
                Long paperId = MapUtils.getLong(param, "paper_id");
                Long goodsId = MapUtils.getLong(param, "goods_id");
                Long productId = MapUtils.getLong(param, "product_id");
                Long objId = MapUtils.getLong(param, "obj_id");
                Integer objType = MapUtils.getInteger(param, "obj_type");
                Integer paperType = MapUtils.getInteger(param, "paper_type");
                Integer state = MapUtils.getInteger(param, "state");
                Integer isLatest = MapUtils.getInteger(param, "is_latest");//是否查最近的一次？0：否，1：是
                Integer source = MapUtils.getInteger(param, "source");//来源
                Integer doneMode = MapUtils.getInteger(param, "done_mode");//做题模式 1练习模式 2考试模式
                Long lessonId = MapUtils.getLong(param, "lesson_id");
                int from = MapUtils.getIntValue(param, "from", 0);
                int rows = MapUtils.getIntValue(param, "rows", 10);

                UserAnswer userAnswer = new UserAnswer(uid, paperId, paperType, state);
                if (source == null || source.intValue() == 0) {
                    userAnswer.setSource(null);
                    userAnswer.setObjTypes(List.of((long) UserAnswer.ObjType.simulationExamNormal, (long) UserAnswer.ObjType.buy));
                } else {
                    userAnswer.setSource(source);
                }
                userAnswer.setObjId(objId);
                if ((Objects.nonNull(objType) && objType==UserAnswer.ObjType.simulationExamYSS)
                        || (Objects.nonNull(source) && source == UserAnswer.ObjType.simulationExamYSS)) {
                    userAnswer.setSource(null);
                    userAnswer.setObjTypes(List.of((long) UserAnswer.ObjType.simulationExamYSS, (long) UserAnswer.ObjType.AL_PAPER));
                } else {
                    userAnswer.setObjType(objType);
                }
                userAnswer.setGoodsId(goodsId);
                userAnswer.setDoneMode(doneMode);
                userAnswer.setProductId(productId);
                userAnswer.setLessonId(lessonId);

                if(param.get("start_time") != null && param.get("start_time") > 0){
                    userAnswer.setStartTime(new Date(MapUtils.getLong(param, "start_time")));
                }
                if(param.get("end_time") != null && param.get("end_time") > 0){
                    userAnswer.setEndTime(new Date(MapUtils.getLong(param, "end_time")));
                }

                Page<UserAnswer> condition = new Page<UserAnswer>(from, rows);
                //最近一次答题记录
                if (isLatest != null && isLatest == 1) {
                    condition.setOrderBy("update_date desc");
                    condition.setFrom(0);
                    condition.setPageSize(1);
                    int total = 0;
                    Page<UserAnswer> userAnswers = userAnswerService.find(condition, userAnswer);
                    if (userAnswers.getList().size() > 0) {
                        total = userAnswers.getList().size();
                        //增加idString返回值
                        userAnswers.getList().stream().forEach(userAnswer1 -> userAnswer1.setIdStr(String.valueOf(userAnswer1.getId())));
                        PageModel<UserAnswer> returnUserAnswer = new PageModel<UserAnswer>(total, userAnswers.getList());
                        res.code = Constants.SUCCESS;
                        res.setMsg(GsonUtil.toJson(returnUserAnswer, req.getAppid()));
                    } else {
                        logger.info("{} getPaperAnswerByUid return null.", entry);
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("getPaperAnswerByUid return null");
                    }

                } else {
                    //is_latest ！= 1
                    int total = userAnswerService.findCount(condition, userAnswer);
                    if (total > 0) {
                        condition.setOrderBy("update_date desc");
                        condition.setFrom(from);
                        condition.setPageSize(rows);
                        Page<UserAnswer> userAnswers = userAnswerService.find(condition, userAnswer);

                        PageModel<UserAnswer> returnUserAnswer = new PageModel<UserAnswer>(total, userAnswers.getList());

                        if (returnUserAnswer.getList() == null || returnUserAnswer.getList().size() == 0) {
                            logger.info("{} getPaperAnswerByUid return null.", entry);
                            res.setCode(Constants.OBJ_NOT_EXISTS);
                            res.setErrormsg("getPaperAnswerByUid return null");
                        } else {
                            //增加逻辑处理题目总数和用户做对总数
                            List<CompletableFuture<Void>> futures = returnUserAnswer.getList().stream()
                                    .map(userAnswer1 -> CompletableFuture.runAsync(() -> {
                                        Long userAnswer1_id = userAnswer1.getId();
                                        // 异步获取试卷题目汇总
                                        try {
                                            List<UserAnswerSum> userAnswerSumList = userAnswerSumService.findAnswerSumAndDetail(uid, userAnswer1_id, null);

                                            if (userAnswerSumList != null && !userAnswerSumList.isEmpty()) {
                                                userAnswer1.setNum(Long.valueOf(userAnswerSumList.size()));

                                                // 使用流式计算总正确数
                                                int total_right = userAnswerSumList.stream()
                                                        .map(UserAnswerSum::getAnswerDetail)          // 提取详情列表
                                                        .filter(details -> details != null && !details.isEmpty())
                                                        .flatMap(List::stream)                         // 扁平化为单个流
                                                        .filter(detail -> detail.getIsRight() == UserAnswerDetail.IsRight.RIGHT)
                                                        .mapToInt(detail -> 1)
                                                        .sum();

                                                userAnswer1.setRight_count(Long.valueOf(total_right));
                                                userAnswer1.setIdStr(String.valueOf(userAnswer1_id));
                                            }
                                        }catch (Exception e){
                                            logger.error("findAnswerSumAndDetail error :",e);
                                        }
                                    }, executor))
                                    .collect(Collectors.toList());

                            // 等待所有异步任务完成
                            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();


//                            for (int i = 0; i < returnUserAnswer.getList().size(); i++) {
//
//                                UserAnswer userAnswer1 =  returnUserAnswer.getList().get(i);
//                                Long paperid = userAnswer1.getPaperId();
//                                Long userAnswer1_id = userAnswer1.getId();
//                                //获取试卷总题目数：
//                                List<UserAnswerSum> userAnswerSumList =  userAnswerSumService.findAnswerSumAndDetail(uid, userAnswer1_id, null);
//
//                                if (userAnswerSumList != null && userAnswerSumList.size()>0) {
//                                    userAnswer1.setNum(Long.valueOf(userAnswerSumList.size()));
//                                    int total_right = 0;
//                                    //计算做对题的数目（全对）
//                                    for (int j = 0; j < userAnswerSumList.size(); j++) {
//                                        List<UserAnswerDetail> userAnswerDetailList = userAnswerSumList.get(j).getAnswerDetail();
//                                        if (userAnswerDetailList != null && userAnswerDetailList.size() > 0) {
//                                            for (int k = 0; k < userAnswerDetailList.size(); k++) {
//                                                if (userAnswerDetailList.get(k).getIsRight() == UserAnswerDetail.IsRight.RIGHT) {
//                                                    total_right++;
//                                                }
//
//                                            }
//                                        }
//                                    }
//                                    userAnswer1.setRight_count(Long.valueOf(total_right));
//                                    userAnswer1.setIdStr(String.valueOf(userAnswer1.getId()));
//                                }
//
//                            }

                            res.code = Constants.SUCCESS;
                            res.setMsg(GsonUtil.toJson(returnUserAnswer, req.getAppid()));
                        }
                    } else {
                        logger.info("{} getPaperAnswerByUid return null.", entry);
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("getPaperAnswerByUid return null");
                    }
                }


            } else {
                logger.error("{} getPaperAnswerByUid need parameter uid.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("getPaperAnswerByUid need parameter uid");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getPaperRecordPage(request req) throws BusinessException {
        String entry = "sty_getPaperRecordPage";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            PaperStudyReportQuery param = GsonUtil.getGenericGson().fromJson(req.getMsg(), PaperStudyReportQuery.class);
            Long uid = param.getUid();
            Long productId = param.getProductId();
            Long categoryId = param.getCategoryId();
            Long goodsId = param.getGoodsId();
            int pageNo = param.getPageNo();
            int rows = param.getPageSize();
            Integer from = pageNo > 0 ? (pageNo - 1) * rows : 0;
            if (Objects.isNull(uid) ||Objects.isNull(categoryId)) {
                logger.error("{} sty_getPaperRecordPage param error.", entry);
            }
            UserAnswer userAnswer = new UserAnswer();
            userAnswer.setUid(uid);
            if(productId!=null && productId>0){
                userAnswer.setProductId(productId);
            }
            userAnswer.setObjType(201);
            userAnswer.setObjId(categoryId);
            userAnswer.setGoodsId(goodsId);
            Page<UserAnswer> condition = new Page<UserAnswer>(from, rows);
            condition.setOrderBy("create_date desc");
            condition.setFrom(from);
            condition.setPageSize(rows);
            Page<UserAnswer> userAnswers = userAnswerService.find(condition, userAnswer);

            List<UserAnswer> answers= userAnswers.getList();
            List<PaperStudyRecordDTO> list = answers.stream().map(answer -> {
                try {
                    Long answerId = answer.getId();
                    List<UserAnswerSum> userAnswerSumList = userAnswerSumService.findAnswerSumAndDetail(uid, answerId, null);

                    AnswerSummaryVo answerSummaryVo = UserAnswerUtils.buildSummary(userAnswerSumList);

                    PaperStudyRecordDTO paperStudyReportDTO = new PaperStudyRecordDTO();
                    BeanUtils.copyProperties(answer, paperStudyReportDTO);
                    paperStudyReportDTO.setRightCount(answerSummaryVo.getTotalRightNum());
                    paperStudyReportDTO.setErrorCount(answerSummaryVo.getTotalWrongNum());
                    paperStudyReportDTO.setTotalCount(answerSummaryVo.getTotalQuestionNum());
                    paperStudyReportDTO.setRightRate(answerSummaryVo.getRightRate().toString());
                    paperStudyReportDTO.setAnswerId(String.valueOf(answerId));
                    paperStudyReportDTO.setNoAnswerCount(answerSummaryVo.getNoAnswerNum());
                    return paperStudyReportDTO;
                } catch (DataAccessException e) {
                    logger.error("sty_getPaperRecordPage format error:", e);
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            com.hqwx.study.dto.PageModel<PaperStudyRecordDTO> returnUserAnswer = new com.hqwx.study.dto.PageModel<PaperStudyRecordDTO>((int)userAnswers.getCount(), list);
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.getGenericGson().toJson(returnUserAnswer));
        }catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response getPaperAnswerDetailByUid(request req) throws BusinessException {
        String entry = "sty_getPaperAnswerDetailByUid";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = (Map<String, Object>) JSON.parse(req.getMsg());
//            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param.get("user_answer_id") != null && MapUtils.getLong(param,"user_answer_id") > 0
                    && param.get("question_ids") != null /*&& param.get("uid") != null && ((Double) param.get("uid")) > 0*/) {
                Long userAnswerId = MapUtils.getLong(param, "user_answer_id");
                Long uid = MapUtils.getLong(param, "uid");

                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {
                }.getType();
                List<Long> questionIdList = gson.fromJson(param.get("question_ids").toString(), type);

                //支持只传user_answer_id获取试卷解答详情
//                if (questionIdList == null || questionIdList.size() <= 0) {
//                    logger.error("{} getPaperAnswerDetailByUid parameter lose", entry);
//                    throw new BusinessException(Constants.PARAM_LOSE, "paramerter lose question_ids is null.");
//                }
//
//                if (questionIdList.size() > 200) {
//                    logger.error("{} fail.paramerter is too many.", entry);
//                    throw new BusinessException(Constants.PARAM_INVALID, "paramerter is too many.");
//                }

                List<UserAnswerSum> useAnswerSums = userAnswerSumService.findAnswerSumAndDetail(uid, userAnswerId,
                        questionIdList);
                if (useAnswerSums != null && useAnswerSums.size() > 0) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(useAnswerSums, req.getAppid()));
                } else {
                    logger.info("{} getPaperAnswerDetailByUid return null.", entry);
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("getPaperAnswerDetailByUid return null");
                }

            } else {
                logger.error("{} getPaperAnswerDetailByUid parameter lose user_answer_id or question_ids or uid is null.",
                        entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("getPaperAnswerDetailByUid parameter lose user_answer_id or question_ids or uid is null.");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_userAnswerPaper(request req) throws BusinessException {
        String entry = "sty_userAnswerPaper";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAnswer userAnswer = gson.fromJson(req.getMsg(), UserAnswer.class);
            logger.info("------------userAnswerPaper.msg:"+res.getMsg());
            if (userAnswer == null || userAnswer.getUid() == null || userAnswer.getAnswerDetail() == null
                    || ((userAnswer.getEndTime() == null || userAnswer.getStartTime() == null) && userAnswer.getUsetime() == null) || userAnswer.getIsSubmit() == null
                    || userAnswer.getSource() == null || userAnswer.getPaperId() == null || userAnswer.getPaperId() <= 0L) {
                logger.error("{} sty_userAnswerPaper parameter lose. uid or paper_id or start_time or end_time or source or is_submit or answer_detail is null", req.getMsg());
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_userAnswerPaper parameter lose.uid or paper_id or start_time or end_time or source or is_submit or answer_detail is null");
            } else {
                if (userAnswer.getSchId() == null) {
                    userAnswer.setSchId(req.getSchId());
                }
                /*对不正常的startTime、endTime值进行处理*/
                Date minDate = new Date(cn.huanju.edu100.study.util.Constants.USER_ANSWER_MIN_TIME);
                if (userAnswer.getStartTime().before(minDate)) {
                    userAnswer.setStartTime(null);
                }
                if (userAnswer.getEndTime().before(minDate)) {
                    userAnswer.setEndTime(null);
                }
                /*对不正常的usetime值进行处理*/
                if (userAnswer.getUsetime() != null && (userAnswer.getUsetime().compareTo(0L) < 0 || userAnswer.getUsetime().compareTo(Consts.MaxUserAnswerTime.paperAnswerMaxSecondsTime)>0) ) {
                    userAnswer.setUsetime(0L);
                }

                List<UserAnswerDetail> details = null;
                if ((userAnswer.getSource() == UserAnswer.Source.questionBox) && (userAnswer.getObjId() == null || userAnswer.getObjType() == null || userAnswer.getObjType() != UserAnswer.ObjType.questionBox)) {
                    res.setCode(Constants.PARAM_LOSE);
                    res.setErrormsg("sty_userAnswerPaper parameter lose.objId or objType is null");
                } else if ((userAnswer.getSource() == UserAnswer.Source.questionBox)) {
                    //userAnswer.getSource()，php端赋的值，说明是题库试卷
                    details = userAnswerService.submit(userAnswer, 0);
//                    if (userAnswer.getIsSubmit() == 0) {
//                        Map<String, String> record = new HashMap<String, String>();
//                        record.put(Consts.User_Last_Practise_Type.Paper.toString(), userAnswer.getPaperId().toString());
//                        compatableRedisClusterClient.hmset(
//                                RedisConsts.getUserLastExerciseKey(userAnswer.getObjId(), userAnswer.getUid()), record);
//                    }

                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                } else if ((userAnswer.getSource() == UserAnswer.Source.dailyPractice)) {
                    userAnswer.setObjType(UserAnswer.ObjType.dailyPractice);
                    userAnswer.setSource(UserAnswer.Source.dailyPractice);
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                } else if ((userAnswer.getSource() == UserAnswer.Source.mockExam)) { //万人模考
                    userAnswer.setObjType(UserAnswer.ObjType.mockExam);
                    userAnswer.setSource(UserAnswer.Source.mockExam);
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                } else if ((userAnswer.getSource() == UserAnswer.Source.AL_PAPER)) {
                    userAnswer.setObjType(UserAnswer.ObjType.AL_PAPER);
                    userAnswer.setSource(UserAnswer.Source.AL_PAPER);
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                } else if ((userAnswer.getSource() == UserAnswer.Source.AL_CONSOLIDATION)) {
                    userAnswer.setObjType(UserAnswer.ObjType.AL_CONSOLIDATION);
                    userAnswer.setSource(UserAnswer.Source.AL_CONSOLIDATION);
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                } else if ((userAnswer.getSource() == UserAnswer.Source.AL_ASSESSMENT)) {
                    userAnswer.setObjType(UserAnswer.ObjType.AL_ASSESSMENT);
                    userAnswer.setSource(UserAnswer.Source.AL_ASSESSMENT);
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                }else if ((userAnswer.getSource() == UserAnswer.Source.AL_CHAPTER_REVIEW)) {
                    userAnswer.setObjType(UserAnswer.ObjType.AL_CHAPTER_REVIEW);
                    userAnswer.setSource(UserAnswer.Source.AL_CHAPTER_REVIEW);
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                } else if ((userAnswer.getSource() == UserAnswer.Source.TK_ENTERSCHOOLTEST)) {
                    userAnswer.setObjType(UserAnswer.ObjType.TK_ENTERSCHOOLTEST);
                    userAnswer.setSource(UserAnswer.Source.TK_ENTERSCHOOLTEST);
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                }else if ((userAnswer.getSource() == UserAnswer.Source.TK_MONTH_TEST)) {
                    //月月考
                    userAnswer.setObjType(UserAnswer.ObjType.TK_MONTH_TEST);
                    userAnswer.setSource(UserAnswer.Source.TK_MONTH_TEST);
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                }else {
                    if (userAnswer.getSource() == UserAnswer.ObjType.simulationExamNormal) {
                        userAnswer.setObjType(UserAnswer.ObjType.simulationExamNormal);
                        userAnswer.setSource(UserAnswer.ObjType.simulationExamNormal);
                    } else if (userAnswer.getSource() == UserAnswer.ObjType.simulationExamYSS) {
                        userAnswer.setObjType(UserAnswer.ObjType.simulationExamYSS);
                        userAnswer.setSource(UserAnswer.ObjType.simulationExamYSS);
                    } else  if (userAnswer.getSource() == UserAnswer.ObjType.simulationExamTK) {
                        userAnswer.setObjType(UserAnswer.ObjType.simulationExamTK);
                        userAnswer.setSource(UserAnswer.ObjType.simulationExamTK);
                    } else if(userAnswer.getSource() == UserAnswer.ObjType.preClassPaper) {
                        userAnswer.setObjType(UserAnswer.ObjType.preClassPaper);
                        // 课前做题 objId 为课节id
                        userAnswer.setLessonId(userAnswer.getObjId());
                    }else if(userAnswer.getSource() == UserAnswer.ObjType.AL_PRE_CLASS_EXERCISE) {
                        userAnswer.setObjType(UserAnswer.ObjType.AL_PRE_CLASS_EXERCISE);
                        userAnswer.setLessonId(userAnswer.getStudyPathId());
                    }else if(userAnswer.getSource() == UserAnswer.ObjType.CHAPTER_CONSOLIDATE_PAPER) {
                        userAnswer.setObjType(UserAnswer.ObjType.CHAPTER_CONSOLIDATE_PAPER);
                        userAnswer.setSource(UserAnswer.ObjType.CHAPTER_CONSOLIDATE_PAPER);
                    } else if(userAnswer.getSource() == UserAnswer.ObjType.ORDINARY_PRE_CLASS_EXERCISE_PAPER){
                        userAnswer.setObjType(UserAnswer.ObjType.ORDINARY_PRE_CLASS_EXERCISE_PAPER);
                        userAnswer.setSource(UserAnswer.ObjType.ORDINARY_PRE_CLASS_EXERCISE_PAPER);
                    }else if(userAnswer.getSource() == UserAnswer.ObjType.AL_TRAIN_BRUSH_PAPER){
                        userAnswer.setObjType(UserAnswer.ObjType.AL_TRAIN_BRUSH_PAPER);
                        userAnswer.setSource(UserAnswer.ObjType.AL_TRAIN_BRUSH_PAPER);
                    } else {
                        userAnswer.setObjType(UserAnswer.ObjType.buy);
                        userAnswer.setSource(UserAnswer.Source.buy);
                    }
                    details = userAnswerService.submit(userAnswer, 0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                }

            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    @Deprecated
    public response getUserErrPaper(request req) throws BusinessException {
        String entry = "sty_getUserErrPaper";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
//        try {
//            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
//            if (param.containsKey("uid")) {
//                long uid = param.get("uid").longValue();
//                int from = MapUtils.getIntValue(param, "from", 0);
//                int rows = MapUtils.getIntValue(param, "rows", 10);
//
//                UserErrorPaper errorPaper = new UserErrorPaper();
//                errorPaper.setUid(uid);
//                Page<UserErrorPaper> condition = new Page<UserErrorPaper>(from, rows, "last_error_time desc");
//                condition.setFrom(from);
//                condition.setPageSize(rows);
//                Page<UserErrorPaper> pagedErrorPapers = userErrorPaperService.find(condition, errorPaper);
//                List<UserErrorPaper> errorPapers = pagedErrorPapers.getList();
//
//                int errorPaperCount = userErrorPaperService.findCount(condition, errorPaper);
//
//                PageModel<UserErrorPaper> returnErrorPapers = new PageModel<UserErrorPaper>(errorPaperCount,
//                        errorPapers);
//
//                if (errorPapers == null || errorPapers.size() == 0) {
        logger.info("{} sty_getUserErrPaper has Deprecated.", entry);
        res.setCode(Constants.OBJ_NOT_EXISTS);
        res.setErrormsg("sty_getUserErrPaper has Deprecated");
//                } else {
//                    res.setCode(Constants.SUCCESS);
//                	res.setMsg(GsonUtil.toJson(returnErrorPapers,req.getAppid()));
//                }
//            } else {
//                logger.error("{} getUserErrPaper parameter lose", entry);
//                res.setCode(Constants.PARAM_LOSE);
//                res.setErrormsg("getUserErrPaper parameter lose");
//            }
//        } catch (DataAccessException e) {
//            res = dataAccessException(entry, req, e);
//        } catch (Exception e) {
//            res = exception(entry, req, e);
//        }
        endInfo(entry, res, start);
        return res;
    }

    @Deprecated
    public response getUserErrAnswerQuestion(request req) throws BusinessException {
        String entry = "sty_getUserErrAnswerQuestion";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
//        try {
//            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
//
//            if (ParamUtils.containsKeys(param, new String[] { "uid", "p_id" })) {
//                Long uid = param.get("uid").longValue();
//                Long pId = param.get("p_id").longValue();
//                Long questionId = MapUtils.getLong(param, "question_id");
//
//                List<UserErrorPaperDetail> paperDetails = errorPaperDetailService.findAll(uid, pId, questionId);
//
//                if (paperDetails == null || paperDetails.size() == 0) {
        logger.info("{} sty_getUserErrAnswerQuestion has Deprecated.", entry);
        res.setCode(Constants.OBJ_NOT_EXISTS);
        res.setErrormsg("sty_getUserErrAnswerQuestion has Deprecated!!!");
//                } else {
//                    res.setCode(Constants.SUCCESS);
//                	res.setMsg(GsonUtil.toJson(paperDetails,req.getAppid()));
//                }
//            } else {
//                logger.error("{} getUserErrAnswerQuestion parameter lose", entry);
//                res.setCode(Constants.PARAM_LOSE);
//                res.setErrormsg("getUserErrAnswerQuestion parameter lose");
//            }
//        } catch (DataAccessException e) {
//            res = dataAccessException(entry, req, e);
//        } catch (Exception e) {
//            res = exception(entry, req, e);
//        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserErrQuestionList(request req) throws BusinessException {
        String entry = "sty_getUserErrQuestionList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null || param.get("from") == null || param.get("rows") == null) {
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("param lost,uid or from or rows is null");
                return res;
            }

            long uid = param.get("uid").longValue();
            int from = MapUtils.getIntValue(param, "from", 0);
            int rows = MapUtils.getIntValue(param, "rows", 10);
            Integer isAl = MapUtils.getInteger(param, "is_al", null);

            UserErrorQuestion errorQuestion = new UserErrorQuestion();
            errorQuestion.setUid(uid);
            errorQuestion.setGoodsId(param.get("goods_id") != null ? param.get("goods_id").longValue()
                    : null);
            errorQuestion.setProductId(param.get("product_id") != null ? param.get("product_id").longValue()
                    : null);
            errorQuestion.setParagraphId(param.get("paragraph_id") != null ? param.get("paragraph_id").longValue()
                    : null);
            errorQuestion.setLessonId(param.get("lesson_id") != null ? param.get("lesson_id").longValue() : null);
            errorQuestion.setQuestionId(param.get("question_id") != null ? param.get("question_id").longValue() : null);
            errorQuestion.setIsAl(isAl);

            Page<UserErrorQuestion> condition = new Page<UserErrorQuestion>(from, rows, "last_error_time desc");
            condition.setFrom(from);
            condition.setPageSize(rows);
            Page<UserErrorQuestion> pagedQuestions = userQuestionService.find(condition, errorQuestion);
            List<UserErrorQuestion> errorQuestions = pagedQuestions.getList();

            int errorPaperCount = userQuestionService.findCount(condition, errorQuestion);

            PageModel<UserErrorQuestion> returnErrorQuestions = new PageModel<UserErrorQuestion>(errorPaperCount,
                    errorQuestions);

            if (errorQuestions == null || errorQuestions.size() == 0) {
                logger.info("{} sty_getUserErrQuestionList return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserErrQuestionList return null");
            } else {
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(returnErrorQuestions, req.getAppid()));
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 获取用户讲节/微课上最新一次的答题详情 TODO:
     */
    public response sty_getUserLatestLessonAnswer(request req) throws BusinessException {
        String entry = "sty_getUserLatestLessonAnswer";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param.get("uid") == null || param.get("uid") <= 0 || param.get("obj_id") == null
                    || param.get("obj_id") <= 0 || param.get("obj_type") == null || param.get("obj_type") < 0) {
                logger.error("{} sty_getUserLatestLessonAnswer parameter lose,uid or obj_id or obj_type is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserLatestLessonAnswer parameter lose,uid or obj_id or obj_type is null");
                return res;
            }

            Long uid = param.get("uid").longValue();
            Long obj_id = param.get("obj_id").longValue();
            Integer obj_type = param.get("obj_type").intValue();
            Long questionId = param.get("question_id") != null ? param.get("question_id").longValue() : null;
            Long taskId = param.get("task_id") != null ? param.get("task_id").longValue() : null;// add by
            // linyl，增加taskId，用于过滤是否获取的是云私塾里的记录

            List<Long> questionIdList = new ArrayList<Long>();
            if (questionId != null) {
                questionIdList.add(questionId);
            }

            List<UserAnswerSum> useAnswerSums = null;
            //获取用户最新的一次答题的user_answer
            UserHomeWorkAnswer userAnswer = userAnswerService.getLatestLessonHomeWork(uid, obj_id, obj_type, taskId);
            if (userAnswer != null) {
                Long userHomeWorkId = userAnswer.getId();
                useAnswerSums = userAnswerSumService.findHomeworkSumAndDetail(uid, userHomeWorkId, questionIdList);

            } else {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserLatestLessonAnswer return null");
                return res;
            }

            if (useAnswerSums != null && useAnswerSums.size() > 0) {
                for (UserAnswerSum useAnswerSum : useAnswerSums) {
                    if (useAnswerSum.getUserHomeworkId() != null) {
                        useAnswerSum.setUserHomeworkIdStr(useAnswerSum.getUserHomeworkId().toString());
                        if (useAnswerSum.getCreateDate() == null){
                            useAnswerSum.setCreateDate(userAnswer.getCreateDate());
                        }
                    }
                }
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(useAnswerSums, req.getAppid()));
            } else {
                logger.info("{} sty_getUserLatestLessonAnswer return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserLatestLessonAnswer return null");
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 新的个性化学习提交接口实现 TODO
     */
    public response sty_personalStudyAnswer(request req) throws BusinessException {
        String entry = "sty_personalStudyAnswer";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserTaskAnswer userTaskAnswer = gson.fromJson(req.getMsg(), UserTaskAnswer.class);

            if (userTaskAnswer == null
                    || userTaskAnswer.getUid() == null
                    || userTaskAnswer.getGroupId() == null
                    || userTaskAnswer.getTaskId() == null
                    || userTaskAnswer.getAnswerDetail() == null
                    || userTaskAnswer.getType() == null
                    || ((userTaskAnswer.getEndTime() == null || userTaskAnswer.getStartTime() == null) && userTaskAnswer
                    .getUsetime() == null)) {
                logger.error("{} sty_personalStudyAnswer parameter lose", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_personalStudyAnswer parameter lose");
            } else {
                userTaskAnswer.setSchId(req.getSchId());
                List<UserAnswerDetail> details = null;
                if (userTaskAnswer.getPaperId() == null || userTaskAnswer.getPaperId() <= 0) {//作业

                    /**对于作业来说，objType字段意思如下：（0：讲作业，1：段落作业，2：微课作业，3：个性化作业，4：新录播课作业，5：题库作业）*/

                    UserHomeWorkAnswer userHomeWorkAnswer = new UserHomeWorkAnswer();
                    userHomeWorkAnswer.setUid(userTaskAnswer.getUid());
                    userHomeWorkAnswer.setAnswerDetail(userTaskAnswer.getAnswerDetail());

                    if (userTaskAnswer.getmClassId() != null) {
                        userHomeWorkAnswer.setObjId(userTaskAnswer.getmClassId());
                        userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.M_CLASS);
                    } else if (userTaskAnswer.getObjType() != null
                            && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON) {
                        userHomeWorkAnswer.setObjId(userTaskAnswer.getObjId());
                        userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.LESSON);
                    } else if (userTaskAnswer.getObjType() != null
                            && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.PARAGRAPH) {
                        userHomeWorkAnswer.setObjId(userTaskAnswer.getObjId());
                        userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.PARAGRAPH);
                    } else if (userTaskAnswer.getObjType() != null
                            && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.COURSE_PARAGRAPH) {
                        userHomeWorkAnswer.setObjId(userTaskAnswer.getObjId());
                        userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.COURSE_PARAGRAPH);
                    } else {
                        userHomeWorkAnswer.setObjId(userTaskAnswer.getTaskId());
                        userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.PERSON_TASK);
                    }
                    userHomeWorkAnswer.setTaskId(userTaskAnswer.getTaskId());
                    userHomeWorkAnswer.setStartTime(userTaskAnswer.getStartTime());
                    userHomeWorkAnswer.setEndTime(userTaskAnswer.getEndTime());
                    userHomeWorkAnswer.setState(2);
                    userHomeWorkAnswer.setIsSubmit(1);
                    details = userAnswerService.submitHomeWork(userHomeWorkAnswer, 0);
                } else {//试卷
                    userTaskAnswer.setSchId(req.getSchId());
                    /**对于试卷来说，objType字段的意思如下：（0：普通购买试卷记录，1：个性化任务试卷记录，2：题库试卷记录）*/
                    if (userTaskAnswer.getSource() == null || userTaskAnswer.getSource() != UserAnswer.Source.personal) {
                        userTaskAnswer.setSource(UserAnswer.Source.personal);
                    }
                    UserAnswer userAnswer = new UserAnswer();
                    userAnswer.setSchId(req.getSchId());
                    ConvertUtils.register(new DateConverter(), Date.class);
                    BeanUtils.copyProperties(userTaskAnswer, userAnswer);

                    userAnswer.setIsSubmit(1);
                    userAnswer.setObjId(userTaskAnswer.getTaskId());
                    userAnswer.setObjType(UserAnswer.ObjType.personal);
                    details = userAnswerService.submit(userAnswer, 1);//这里是针对每个小题的回答情况
                }

                if (details != null && details.size() > 0) {
                    userAnswerService.submitPersonalTask(userTaskAnswer, details);
                }

                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(details, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 用户提交作业
     */
    public response sty_userAnswerHomework(request req) throws BusinessException {
        String entry = "sty_userAnswerHomework";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserHomeWorkAnswer userHomeWorkAnswer = gson.fromJson(req.getMsg(), UserHomeWorkAnswer.class);

            if (userHomeWorkAnswer == null
                    || userHomeWorkAnswer.getUid() == null || userHomeWorkAnswer.getUid() <= 0L
                    || userHomeWorkAnswer.getAnswerDetail() == null
                    || ((userHomeWorkAnswer.getEndTime() == null || userHomeWorkAnswer.getStartTime() == null) && userHomeWorkAnswer
                    .getUsetime() == null) || userHomeWorkAnswer.getObjId() == null
                    || userHomeWorkAnswer.getObjType() == null) {
                logger.error(
                        "{} sty_userAnswerHomework parameter lose. uid or start_time or end_time or answer_detail or obj_id or obj_type is null",
                        entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_userAnswerHomework parameter lose. uid or start_time or end_time or answer_detail or obj_id or obj_type is null");
            } else {
                userHomeWorkAnswer.setSchId(req.getSchId());
                if (userHomeWorkAnswer.getIsSubmit() == null) {
                    userHomeWorkAnswer.setIsSubmit(UserAnswer.State.SUBMITTED);
                }
                /*对不正常的startTime、endTime值进行处理*/
                Date minDate = new Date(cn.huanju.edu100.study.util.Constants.USER_ANSWER_MIN_TIME);
                if (userHomeWorkAnswer.getStartTime().before(minDate)) {
                    userHomeWorkAnswer.setStartTime(null);
                }
                if (userHomeWorkAnswer.getEndTime().before(minDate)) {
                    userHomeWorkAnswer.setEndTime(null);
                }
                /*对不正常的usetime值进行处理*/
                if (userHomeWorkAnswer.getUsetime() != null && userHomeWorkAnswer.getUsetime().compareTo(0L) < 0) {
                    userHomeWorkAnswer.setUsetime(0L);
                }
                List<UserAnswerDetail> details = userAnswerService.submitHomeWork(userHomeWorkAnswer,
                        userHomeWorkAnswer.getObjType());
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(details, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 获取用户的作业作答的情况
     */
    public response sty_getUserHomeWorkInfo(request req) throws BusinessException {
        String entry = "sty_getUserHomeWorkInfo";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null || param.get("uid") <= 0 || param.get("obj_id") == null
                    || param.get("obj_id") <= 0 || param.get("obj_type") == null || param.get("obj_type") < 0
                    || param.get("from") == null || param.get("from") < 0 || param.get("rows") == null
                    || param.get("rows") <= 0) {
                logger.error(
                        "{} sty_getUserHomeWorkInfo parameter lose,uid or obj_id or obj_type or from or rows is null",
                        entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeWorkInfo parameter lose,uid or obj_id or obj_type or from or rows is null");
                return res;
            }

            int from = MapUtils.getIntValue(param, "from", 0);
            int rows = MapUtils.getIntValue(param, "rows", 10);

            int total = userAnswerService.findUserHomeWorkInfoCount(param);
            if (total == 0) {
                logger.info("{} sty_getUserHomeWorkInfo return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserHomeWorkInfo return null");
            } else {

                List<UserHomeWorkAnswer> userAnswers = userAnswerService.findUserHomeWorkInfo(param, from, rows);

                if (userAnswers == null || userAnswers.size() == 0) {
                    logger.info("{} sty_getUserHomeWorkInfo return null.", entry);
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_getUserHomeWorkInfo return null");
                } else {
                    PageModel<UserHomeWorkAnswer> returnUserAnswer = new PageModel<UserHomeWorkAnswer>(total,
                            userAnswers);
                    res.code = Constants.SUCCESS;
                    res.setMsg(GsonUtil.toJson(returnUserAnswer, req.getAppid()));
                }
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getUserHomeWorkDetail(request req) throws BusinessException {
        String entry = "sty_getUserHomeWorkDetail";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
//            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            Map<String, Object> param = (Map<String, Object>) JSON.parse(req.getMsg());
            //TODO:
            if (param.get("user_homework_id") != null && MapUtils.getLong(param, "user_homework_id") > 0
                    /*&& param.get("uid") != null && ((Double) param.get("uid")) > 0*/) {
                Long userHomeworkId = MapUtils.getLong(param, "user_homework_id");
                Long uid = MapUtils.getLong(param, "uid");

                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {
                }.getType();
                List<Long> questionIdList = null;

                if (param.get("question_ids") != null) {
                    questionIdList = gson.fromJson(param.get("question_ids").toString(), type);
                }

                if (questionIdList == null || questionIdList.size() <= 0) {
//                	 logger.error("{} parameter lose", entry);
//                	 throw new BusinessException(Constants.PARAM_LOSE, "paramerter lose question_ids is null.");
                    questionIdList = new ArrayList<Long>();
                }

                if (questionIdList.size() > 200) {
                    logger.error("{} fail.paramerter is too many.", entry);
                    throw new BusinessException(Constants.PARAM_INVALID, "paramerter is too many.");
                }

                List<UserAnswerSum> useAnswerSums = userAnswerSumService.findHomeworkSumAndDetail(uid, userHomeworkId,
                        questionIdList);
                if (useAnswerSums != null && useAnswerSums.size() > 0) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(useAnswerSums, req.getAppid()));
                } else {
                    logger.info("{}  return null.", entry);
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_getUserHomeWorkDetail return null");
                }

            } else {
                logger.error("{} parameter lose user_answer_id or question_ids or uid is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeWorkDetail parameter lose user_answer_id or question_ids or uid is null.");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserHomeWorkDetailList(request req) throws BusinessException {
        String entry = "sty_getUserHomeWorkDetailList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = (Map<String, Object>) JSON.parse(req.getMsg());
            if (param == null || param.get("user_homework_ids") == null || param.get("uid") == null) {
                logger.error("{} sty_getUserHomeWorkDetailList parameter lose,user_homework_ids or uid is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeWorkDetailList parameter lose,user_homework_ids or uid is null");
                return res;
            }
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {
            }.getType();
            List<Long> user_homework_ids = null;
            if (param.get("user_homework_ids") != null) {
                user_homework_ids = gson.fromJson(param.get("user_homework_ids").toString(), type);
            }
            Long uid = MapUtils.getLong(param, "uid", null);

            List<UserAnswerSum> useAnswerSums = userAnswerSumService.findHomeworkSumAndDetailList(uid, user_homework_ids);
            if (useAnswerSums != null && useAnswerSums.size() > 0) {
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(useAnswerSums, req.getAppid()));
            } else {
                logger.info("{}  return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserHomeWorkDetailList return null");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 获取用户的作业作答的情况
     */
    public response sty_isDoHomeWorkById(request req) throws BusinessException {
        String entry = "sty_isDoHomeWorkById";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null || Double.valueOf(param.get("uid").toString()) <= 0 || param.get("obj_id") == null) {
                logger.error("{} param lose,uid or obj_id is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg(entry + " param lose,uid or obj_id is null");
                return res;
            }
            if (param.get("obj_type") == null && param.get("obj_type_list") == null) {
                logger.error("{} param lose, obj_type_list and obj_type is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg(entry + " param lose, obj_type_list and obj_type is null");
                return res;
            }

            List<Long> objIdList = ParamUtils.getLongList(param,"obj_id", false);
            Long uid = ParamUtils.getLong(param, "uid", false);
            Integer objType = ParamUtils.getInt(param, "obj_type", true);
            List<Integer> objTypes = ParamUtils.getIntegerList(param, "obj_type_list", true);
            Long goodsId = ParamUtils.getLong(param, "goods_id", true);

            Map<Long, Boolean> isDoMap = new HashMap<Long, Boolean>();

            HashMap<String, Object> queryParam = Maps.newHashMap();
            queryParam.put("objIdList", objIdList);
            queryParam.put("objType", objType);
            queryParam.put("objTypes", objTypes);
            queryParam.put("uid", uid);
            if(goodsId != null && goodsId>0){
                queryParam.put("goodsId",goodsId);
            }
            List<UserAnswerCount> userAnswerCountList = userAnswerService.findUserHomeWorkInfoCountBatch(queryParam);
            for(UserAnswerCount userAnswerCount : userAnswerCountList) {
                if (!Objects.isNull(userAnswerCount) && !Objects.isNull(userAnswerCount.getCount()) && userAnswerCount.getCount()>0) {
                    isDoMap.put(userAnswerCount.getObjId(), true);
                }
            }
            for (Long objId : objIdList) {
                if (!isDoMap.containsKey(objId)) {
                    isDoMap.put(objId, false);
                }
            }

            if (isDoMap.size() <= 0) {
                logger.info("{} return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_isDoHomeWorkById return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(isDoMap, req.getAppid()));
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 用户重新作答错误的题目
     */
    public response sty_userAnswerErrorQuestion(request req) throws BusinessException {
        String entry = "sty_userAnswerErrorQuestion";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserErrorAnswer>>() {
            }.getType();

            List<UserErrorAnswer> userErrorAnswerList = gson.fromJson(req.getMsg(), type);

            if (userErrorAnswerList == null || userErrorAnswerList.size() == 0) {
                logger.error("{} sty_userAnswerErrorQuestion parameter lose.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_userAnswerErrorQuestion parameter lose.");
            } else {
                HashMap<Long, Object> result = userAnswerService.submitErrorQuestion(userErrorAnswerList);

                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 用户作答题库练习 TODO:
     */
    public response sty_userAnswerBoxExercise(request req) throws BusinessException {
        String entry = "sty_userAnswerBoxExercise";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserExerciseAnswer userExerciseAnswer = gson.fromJson(req.getMsg(), UserExerciseAnswer.class);

            if (userExerciseAnswer == null
                    || userExerciseAnswer.getBoxId() == null || userExerciseAnswer.getBoxId() <= 0L
                    || userExerciseAnswer.getTeachBookId() == null || userExerciseAnswer.getTeachBookId() <= 0L
                    || userExerciseAnswer.getUid() == null || userExerciseAnswer.getUid() <= 0L
                    || userExerciseAnswer.getAnswerDetail() == null
                    || ((userExerciseAnswer.getEndTime() == null || userExerciseAnswer.getStartTime() == null) && userExerciseAnswer
                    .getUsetime() == null) || userExerciseAnswer.getObjId() == null
                    || userExerciseAnswer.getObjType() == null) {
                logger.error(
                        "{} sty_userAnswerBoxExercise parameter lose. uid or boxId or teachBookId or start_time or end_time or answer_detail or obj_id or obj_type is null",
                        entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_userAnswerBoxExercise parameter lose. uid or boxId or teachBookId or start_time or end_time or answer_detail or obj_id or obj_type is null");
            } else {
                userExerciseAnswer.setSchId(req.getSchId());
                if (userExerciseAnswer.getIsSubmit() == null) {
                    userExerciseAnswer.setIsSubmit(UserAnswer.State.SUBMITTED);
                }
                List<UserAnswerDetail> details = userAnswerService.submitExercise(userExerciseAnswer,
                        userExerciseAnswer.getObjType(), 1);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(details, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 用户作答题库题目（此接口针对于：需要将生成作业与提交作业融合在一起的场景）
     * uid				用户uid
     * teach_book_id	教材id
     * box_id			题库id
     * homeworkTypeId  作业类型id(章节id/知识点id)
     * homeworkType    作业类型（0：所有，1：章节，2：知识点）
     * homeworkModel   作业模式（0：未做试题，1：错误试题，2：全部试题,3：智能练习【未做+错误】）
     * start_time		答题开始时间
     * end_time		答题结束时间
     * answer_detail	UserAnswer	M	用户答案List
     * is_submit		Integer	M	0:保存，1:提交
     * <p/>
     * 首先生成作业，然后再进行作业提交
     */
    public response sty_userGenerateAndAnswerBoxExercise(request req) throws BusinessException {
        String entry = "sty_userGenerateAndAnswerBoxExercise";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserGenerateExerciseAnswer userGenerateExerciseAnswer = gson.fromJson(req.getMsg(), UserGenerateExerciseAnswer.class);

            if (userGenerateExerciseAnswer == null || userGenerateExerciseAnswer.getBoxId() == null || userGenerateExerciseAnswer.getTeachBookId() == null
                    || userGenerateExerciseAnswer.getHomeworkType() == null || userGenerateExerciseAnswer.getHomeworkTypeId() == null
                    || userGenerateExerciseAnswer.getHomeworkModel() == null || userGenerateExerciseAnswer.getAnswerDetail().isEmpty()
                    || userGenerateExerciseAnswer.getUid() == null || userGenerateExerciseAnswer.getAnswerDetail() == null
                    || ((userGenerateExerciseAnswer.getEndTime() == null || userGenerateExerciseAnswer.getStartTime() == null) && userGenerateExerciseAnswer.getUsetime() == null)
                    ) {
                logger.error("{} sty_userGenerateAndAnswerBoxExercise parameter lose. "
                        + "uid or boxId or teachBookId or start_time or end_time or answer_detail "
                        + "or homework_type or homework_type_id or homework_model is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_userGenerateAndAnswerBoxExercise parameter lose. "
                        + "uid or boxId or teachBookId or start_time or end_time or answer_detail "
                        + "or homework_type or homework_type_id or homework_model is null");
            } else {
                if (userGenerateExerciseAnswer.getIsSubmit() == null) {
                    userGenerateExerciseAnswer.setIsSubmit(UserAnswer.State.SUBMITTED);
                }

                // 1.首先根据提交上来的题目生成一份作业
                Long exerciseId = userQuestionBoxService.GenerateBoxExercise(userGenerateExerciseAnswer);

                if (exerciseId != null) {
                    // 2.针对该作业进行作业提交
                    UserExerciseAnswer userExerciseAnswer = new UserExerciseAnswer();
                    BeanUtils.copyProperties(userGenerateExerciseAnswer, userExerciseAnswer);

                    userExerciseAnswer.setObjId(exerciseId);
                    userExerciseAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.BOX_HOMEWORK);

                    List<UserAnswerDetail> details = userAnswerService.submitExercise(userExerciseAnswer,
                            userExerciseAnswer.getObjType(),0);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(details, req.getAppid()));
                } else {
                    res.setCode(Constants.SYS_ERROR);
                    res.setErrormsg("sty_userGenerateAndAnswerBoxExercise fail.GenerateExercise faild.");
                }

            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 根据作业记录id获取用户某份作业记录
     */
    public response sty_getUserHomeWorkRecodById(request req) throws BusinessException {
        String entry = "sty_getUserHomeWorkRecodById";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = (Map<String, Object>) JSON.parse(req.getMsg());
            if (param == null || param.get("user_homework_id") == null || param.get("uid") == null) {
                logger.error("{} sty_getUserHomeWorkRecodById parameter lose,user_homework_id or uid is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeWorkRecodById parameter lose,user_homework_id or uid is null");
                return res;
            }

            Long user_homework_id = new BigDecimal(param.get("user_homework_id").toString()).longValue();
            Long uid = new BigDecimal(param.get("uid").toString()).longValue();

            UserHomeWorkAnswer userAnswer = userAnswerService.getUserHomeWorkAnswerById(uid, user_homework_id);

            if (userAnswer == null) {
                logger.info("{} sty_getUserHomeWorkRecodById return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserHomeWorkRecodById return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(userAnswer, req.getAppid()));
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 根据作业记录id列表获取用户作业记录列表
     */
    public response sty_getUserHomeWorkRecodsByIdList(request req) throws BusinessException {
        String entry = "sty_getUserHomeWorkRecodsByIdList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = (Map<String, Object>) JSON.parse(req.getMsg());
            if (param == null || param.get("user_homework_ids") == null || param.get("uid") == null) {
                logger.error("{} sty_getUserHomeWorkRecodsByIdList parameter lose,user_homework_ids or uid is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeWorkRecodsByIdList parameter lose,user_homework_ids or uid is null");
                return res;
            }

            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<Long>>() {
            }.getType();
            List<Long> user_homework_ids = null;
            if (param.get("user_homework_ids") != null) {
                user_homework_ids = gson.fromJson(param.get("user_homework_ids").toString(), type);
            }
            Long uid = MapUtils.getLong(param, "uid", null);

            List<UserHomeWorkAnswer> userAnswers = userAnswerService.getUserHomeWorkAnswersByIdList(uid, user_homework_ids);

            if (userAnswers == null) {
                logger.info("{} sty_getUserHomeWorkRecodsByIdList return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserHomeWorkRecodsByIdList return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(userAnswers, req.getAppid()));
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 云私塾个性化段落作业提交
     */
    public response sty_tutor_submitHomeworkAnswer(request req) throws BusinessException {
        String entry = "sty_tutor_submitHomeworkAnswer";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserTaskAnswer userTaskAnswer = gson.fromJson(req.getMsg(), UserTaskAnswer.class);

            if (userTaskAnswer == null
                    || userTaskAnswer.getUid() == null
                    || userTaskAnswer.getGroupId() == null
                    || userTaskAnswer.getTaskId() == null
                    || userTaskAnswer.getAnswerDetail() == null
                    || userTaskAnswer.getType() == null
                    || ((userTaskAnswer.getEndTime() == null || userTaskAnswer.getStartTime() == null) && userTaskAnswer
                    .getUsetime() == null)) {
                logger.error("{} sty_tutor_submitHomeworkAnswer parameter lose", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_tutor_submitHomeworkAnswer parameter lose");
            } else {
                List<UserAnswerDetail> details = null;

                /** 对于作业来说，objType字段意思如下：（0：讲作业，1：段落作业，2：微课作业，3：个性化作业，4：新录播课作业，5：题库作业） */
                UserHomeWorkAnswer userHomeWorkAnswer = new UserHomeWorkAnswer();
                userHomeWorkAnswer.setUid(userTaskAnswer.getUid());
                userHomeWorkAnswer.setAnswerDetail(userTaskAnswer.getAnswerDetail());

                if (userTaskAnswer.getmClassId() != null) {
                    userHomeWorkAnswer.setObjId(userTaskAnswer.getmClassId());
                    userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.M_CLASS);
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON) {
                    userHomeWorkAnswer.setObjId(userTaskAnswer.getObjId());
                    userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.LESSON);
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.PARAGRAPH) {
                    userHomeWorkAnswer.setObjId(userTaskAnswer.getObjId());
                    userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.PARAGRAPH);
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.COURSE_PARAGRAPH) {
                    userHomeWorkAnswer.setObjId(userTaskAnswer.getObjId());
                    userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.COURSE_PARAGRAPH);
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.PERSON_TASK) {
                    userHomeWorkAnswer.setObjId(userTaskAnswer.getTaskId());
                    userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.PERSON_TASK);
                } else {
                    userHomeWorkAnswer.setObjId(userTaskAnswer.getTaskId());
                    userHomeWorkAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.RESOURCE_VIDEO);
                    userTaskAnswer.setObjType(UserHomeWorkAnswer.HomeWorkType.RESOURCE_VIDEO);
                }
                if(userHomeWorkAnswer.getHomeworkId()==null || userHomeWorkAnswer.getHomeworkId()<=0){
                    Homework homework = goodsResource.getHomeworkByObjInfo(userTaskAnswer.getTaskId(),Consts.ProductType.PRODUCT_ADAPTIVE_LEARNING);
                    if(homework!=null){
                        userHomeWorkAnswer.setHomeworkId(homework.getId());
                    }
                }

                userHomeWorkAnswer.setTaskId(userTaskAnswer.getTaskId());
                userHomeWorkAnswer.setStartTime(userTaskAnswer.getStartTime());
                userHomeWorkAnswer.setEndTime(userTaskAnswer.getEndTime());
                userHomeWorkAnswer.setState(2);
                userHomeWorkAnswer.setIsSubmit(1);
                userHomeWorkAnswer.setPlatForm(userTaskAnswer.getPlatForm());
                userHomeWorkAnswer.setAppid(userTaskAnswer.getAppid());
                details = userAnswerService.submitHomeWork(userHomeWorkAnswer, 0);
                TutorUserAnswerDto tutorUserAnswerDto = new TutorUserAnswerDto();
                ConvertUtils.register(new DateConverter(), Date.class);
                BeanUtils.copyProperties(userHomeWorkAnswer, tutorUserAnswerDto);
                tutorUserAnswerDto.setAnswerDetail(details);

                if (CollectionUtils.isNotEmpty(details)) {
                    userAnswerService.submitTutorPersonalTask(userTaskAnswer, details);
                }

                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(tutorUserAnswerDto, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 新的个性化试卷提交
     */
    public response sty_tutor_submitPaperAnswer(request req) throws BusinessException {
        String entry = "sty_tutor_submitPaperAnswer";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserTaskAnswer userTaskAnswer = gson.fromJson(req.getMsg(), UserTaskAnswer.class);

            if (userTaskAnswer == null
                    || userTaskAnswer.getPaperId() == null
                    || userTaskAnswer.getUid() == null
                    || userTaskAnswer.getGroupId() == null
                    || (userTaskAnswer.getTaskId() == null && userTaskAnswer.getWkSectionId() == null)
                    || userTaskAnswer.getAnswerDetail() == null
                    || userTaskAnswer.getType() == null
                    || ((userTaskAnswer.getEndTime() == null || userTaskAnswer.getStartTime() == null) && userTaskAnswer
                    .getUsetime() == null)) {
                logger.error("{} sty_tutor_submitPaperAnswer parameter lose", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_tutor_submitPaperAnswer parameter lose");
            } else {
                userTaskAnswer.setSchId(req.getSchId());
                List<UserAnswerDetail> details = null;

                /** 对于试卷来说，objType字段的意思如下：（0：普通购买试卷记录，1：个性化任务试卷记录，2：题库试卷记录） */
                if (null == userTaskAnswer.getSource()) {
                    userTaskAnswer.setSource(UserAnswer.Source.personal);
                }
                UserAnswer userAnswer = new UserAnswer();

                ConvertUtils.register(new DateConverter(), Date.class);
                BeanUtils.copyProperties(userTaskAnswer, userAnswer);

                userAnswer.setIsSubmit(userTaskAnswer.getIsSubmit() == null ? 0 : userTaskAnswer.getIsSubmit());
                userAnswer.setObjId(userTaskAnswer.getPaperId());
                if (null == userTaskAnswer.getObjType()) {
                    userAnswer.setObjType(UserAnswer.ObjType.personal);
                } else {
                    userAnswer.setObjType(userTaskAnswer.getObjType());
                }
                details = userAnswerService.submit(userAnswer, 1);// 这里是针对每个小题的回答情况

                TutorUserAnswerDto tutorUserAnswerDto = new TutorUserAnswerDto();
                ConvertUtils.register(new DateConverter(), Date.class);
                BeanUtils.copyProperties(userAnswer, tutorUserAnswerDto);
                userTaskAnswer.setScore(tutorUserAnswerDto.getScore());
                tutorUserAnswerDto.setAnswerDetail(details);

                if (userAnswer.getIsSubmit() > 0 && CollectionUtils.isNotEmpty(details)) {
                    userTaskAnswer.setObjId(userTaskAnswer.getPaperId());
                    if (null == userTaskAnswer.getObjType()) {
                        userTaskAnswer.setObjType(UserAnswer.ObjType.personal);
                    }
                    userAnswerService.submitTutorPersonalTask(userTaskAnswer, details);
                }

                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(tutorUserAnswerDto, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            logger.error("sty_tutor_submitPaperAnswer error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 新的个性化试卷提交
     */
    @Deprecated
    public response sty_testInfo(request req) throws BusinessException {
        String entry = "sty_testInfo";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        /**数据迁移，代码可删*/
        Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
        Integer index = ParamUtils.getInt(params, "index", true);
        Long uid = ParamUtils.getLong(params, "uid", true);
        Integer tbidx = ParamUtils.getInt(params, "tbidx", true);
        if (index != null) {
            //下面是数据迁移操作
            if (index == 1000) {
                userQuestionService.transferUserErrorQuestion();
            } else if(index == 1001){
                userCollectItemsService.transferUserCollectItems();
            } else if(index==1002){
                userCollectItemsService.transferUserCollectItems(uid,tbidx);
            } else if(index==1003){
                evaluationUserAnswerService.transferEvaluationUserAnswerList();
            } else if(index==1004){
                evaluationUserBaseAnswerDetailService.transferEvaluationUserBaseAnswerDetailList();
            }
        }

        res.setMsg("-2");
        endInfo(entry, res, start);
        return res;
    }
    /**
     * 获取完成的试卷数量
     */
    public response sty_getUserFinishedPaperCount(request req) throws BusinessException {
        String entry = "sty_getUserFinishedPaperCount";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long goodsId = ParamUtils.getLong(params, "goods_id", false);
            List<Long> paperIds = ParamUtils.getLongList(params, "paper_ids", false);
            Long count = userAnswerService.countUserFinishedPaperByGoodsIdAndPaperIds(uid, goodsId, paperIds);

            if (null == count) {
                count = 0L;
            }
            res.setCode(Constants.SUCCESS);
            res.setMsg(String.valueOf(count));
        } catch (Exception e) {
            logger.error("sty_getUserFinishedPaperCount error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 获取完成的试卷数量
     */
    public response sty_getUserHomeWorkAnswersForGoods(request req) throws BusinessException {
        String entry = "sty_getUserHomeWorkAnswersForGoods";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long goodsId = ParamUtils.getLong(params, "goods_id", false);
            List<Long> lessonIds = ParamUtils.getLongList(params, "lesson_ids",false);
            List<UserHomeWorkAnswer> result = userAnswerService.queryUserSubmitHomeworkQuestionsByGoodsIdAndLessonIds(uid, goodsId, lessonIds);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserHomeWorkAnswersForGoods error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 获取用户学习时长统计
     */
    public response sty_getUserStudyLengthStatistics(request req) throws BusinessException {
        String entry = "sty_getUserStudyLengthStatistics";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long objId = ParamUtils.getLong(params, "obj_id", false);
            Long productId = ParamUtils.getLong(params, "productId", true);
            String date = ParamUtils.getString(params, "date", false);
            UserStudyStatistics result = userAnswerService.queryStudyStatisticsByParam(uid, objId, productId, date);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserStudyLengthStatistics error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 获取完成的试卷数量
     */
    public response sty_getUserAnswersForGoods(request req) throws BusinessException {
        String entry = "sty_getUserAnswersForGoods";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long goodsId = ParamUtils.getLong(params, "goods_id", false);
            List<Long> paperIds = ParamUtils.getLongList(params, "paper_ids", false);
            List<UserAnswer> result = userAnswerService.queryUserFinishedPaperByGoodsIdAndPaperIds(uid, goodsId, paperIds);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswersForGoods error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 获取完成的试卷数量
     */
    public response sty_getUserSubmitHomeWorkQuestionCount(request req) throws BusinessException {
        String entry = "sty_getUserSubmitHomeWorkQuestionCount";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long goodsId = ParamUtils.getLong(params, "goods_id", false);
            List<Long> lessonIds = ParamUtils.getLongList(params, "lesson_ids",false);
            Long count = userAnswerService.countUserSubmitHomeworkQuestionsByGoodsIdAndLessonIds(uid, goodsId, lessonIds);

            if (null == count) {
                count = 0L;
            }
            res.setCode(Constants.SUCCESS);
            res.setMsg(String.valueOf(count));
        } catch (Exception e) {
            logger.error("sty_getUserSubmitHomeWorkQuestionCount error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
    public response sty_getUserHomeWorkCount(request req) throws BusinessException {
        String entry = "sty_getUserHomeWorkCount";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserHomeWorkAnswer params = gson.fromJson(req.getMsg(), UserHomeWorkAnswer.class);
            if(null == params.getUid() || null == params.getObjId() || params.getObjType() == null){
                logger.error("param uid or objId or objType is empty");
                throw new DataAccessException("param uid or objId or objType is empty");
            }
            Integer count = userAnswerService.countHomeWork(params);

            if (null == count) {
                count = 0;
            }
            res.setCode(Constants.SUCCESS);
            res.setMsg(String.valueOf(count));
        } catch (Exception e) {
            logger.error("sty_getUserHomeWorkCount error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserQuestionForAL(request req) throws BusinessException {
        String entry = "sty_getUserQuestionForAL";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserAnswerSum>() {
            }.getType();
            UserAnswerSum userAnswerSum = gson.fromJson(req.getMsg(), type);
            List<UserAnswerSum> result= userAnswerSumService.getUserAllQuestion(userAnswerSum);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserQuestionForAL error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;

    }

    public response sty_getUserAnswerHistoryPage(request req) throws BusinessException{
        String entry = "sty_getUserAnswerHistoryPage";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Integer pageSize = ParamUtils.getInt(params, "pageSize", true);
            Integer pageNo = ParamUtils.getInt(params, "pageNo", true);
            List<UserAnswerHistory> result= userAnswerSumService.getUserAnswerHistoryPage(uid, pageSize, pageNo);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswerHistoryPage error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;

    }

    public response sty_getUserAnswerDetailByIdAndUid(request req) throws BusinessException {
        String entry = "sty_getUserAnswerDetailByIdAndUid";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long id = ParamUtils.getLong(params, "id", true);
            Integer type = ParamUtils.getInt(params, "type", true);
            Map result= userAnswerSumService.getUserAnswerDetailByIdAndUid(id, uid, type);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswerHistoryPage error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;

    }

    public response sty_getUserAnswerListByUidAndDate(request req) throws BusinessException {
        String entry = "sty_getUserAnswerListByUidAndDate";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Date startTime = ParamUtils.getDateFromLong(params, "startTime", true);
            Date endTime = ParamUtils.getDateFromLong(params, "endTime", true);
            Integer type = ParamUtils.getInt(params, "type", true);
            Long categoryId = ParamUtils.getLong(params, "categoryId", false);
            List<Long> resIds=ParamUtils.getLongList(params, "resIds", true);
            List<AlPaperReport> result= userAnswerSumService.getUserAnswerListByUidAndDate(uid, type, categoryId, startTime, endTime, resIds);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswerHistoryPage error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;

    }

    public response sty_getPaperAnswerRightAndWrong(request req) throws BusinessException {
        String entry = "sty_getPaperAnswerRightAndWrong";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = (Map<String, Object>) JSON.parse(req.getMsg());

            Long uid = MapUtils.getLong(params, "uid");
            Long userAnswerId = MapUtils.getLong(params, "user_answer_id");

            UserAnswer userAnswer = new UserAnswer();
            List<UserAnswerSum> userAnswerSumList =  userAnswerSumService.findAnswerSumAndDetail(uid, userAnswerId, null);
            if (!org.springframework.util.CollectionUtils.isEmpty(userAnswerSumList)) {
                userAnswer.setNum(Long.valueOf(userAnswerSumList.size()));// num
                userAnswer.setId(userAnswerId);
                Long total_right = 0L;
                Long total_wrong = 0L;

                Map<Long, Long> map_qid_rw = new HashMap<Long, Long>();

                for (int j = 0; j < userAnswerSumList.size(); j++) {
                    List<UserAnswerDetail> userAnswerDetailList = userAnswerSumList.get(j).getAnswerDetail();
                    if (!org.springframework.util.CollectionUtils.isEmpty(userAnswerDetailList)) {
                        for (int k = 0; k < userAnswerDetailList.size(); k++) {
                            if (userAnswerDetailList.get(k).getIsRight() == UserAnswerDetail.IsRight.RIGHT) {
                                total_right++;
                                map_qid_rw.put(userAnswerDetailList.get(k).getQuestionId(),2L);
                            }
                            if (userAnswerDetailList.get(k).getIsRight() == UserAnswerDetail.IsRight.WRONG) {
                                total_wrong++;
                                map_qid_rw.put(userAnswerDetailList.get(k).getQuestionId(),0L);
                            }
                            if (userAnswerDetailList.get(k).getIsRight() == UserAnswerDetail.IsRight.NOT_ANSWER) {
                                map_qid_rw.put(userAnswerDetailList.get(k).getQuestionId(),3L);
                            }
                            if (userAnswerDetailList.get(k).getIsRight() == UserAnswerDetail.IsRight.HALF_RIGHT) {
                                map_qid_rw.put(userAnswerDetailList.get(k).getQuestionId(),1L);
                            }
                        }
                    }
                }
                userAnswer.setRight_count(total_right);
                userAnswer.setWrong_count(total_wrong);
                userAnswer.setMap_qid_rw(map_qid_rw);
            }
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(userAnswer, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswersForGoods error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getPaperStudyLengthByPaperId(request req) throws BusinessException {
        String entry = "sty_getPaperStudyLengthByPaperId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        Gson genericGson = GsonUtil.getGenericGson();
        try {
            Map<String, Object> param = genericGson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(param, "uid", true);
            Long paperId = ParamUtils.getLong(param, "paperId", true);
            Long productId = ParamUtils.getLong(param, "productId", true);
            if (Objects.isNull(uid) || Objects.isNull(paperId)) {
                logger.error("{} fail.parameter id is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid or paperId is null.");
            }

            Long userTime = userAnswerService.getPaperStudyLengthByPaperId(paperId, uid, productId);
            res.code = Constants.SUCCESS;
            res.setMsg(userTime.toString());
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserHomeWorkUseTime(request req) throws BusinessException {
        String entry = "sty_getUserHomeWorkUseTime";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        Gson genericGson = GsonUtil.getGenericGson();
        try {
            Map<String, Object> param = genericGson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(param, "uid", true);
            if (Objects.isNull(uid)) {
                logger.error("{} fail.parameter id is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid  is null.");
            }

            Long userTime = userAnswerService.getUserUseTime(uid);
            res.code = Constants.SUCCESS;
            res.setMsg(userTime.toString());
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserAnswerStudyReportByGoodsId(request req) throws BusinessException {
        String entry = "sty_getUserAnswerStudyReportByGoodsId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAnswerStudyReportQuery param = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserAnswerStudyReportQuery.class);
            Long uid = param.getUid();
            Long goodsId = param.getGoodsId();
            if (Objects.isNull(uid) || Objects.isNull(goodsId)) {
                logger.error("{} fail.parameter  uid or goodsId is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid or goodsId is null.");
            }

            UserAnswerStudyReportDTO result = userAnswerService.getUserAnswerStudyReportByGoodsId(uid, goodsId);
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.getGenericGson().toJson(result));
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserAnswerSumFindAllList(request req) throws BusinessException {
        String entry = "sty_getUserAnswerSumFindAllList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserAnswerSumDTO>() {
            }.getType();
            UserAnswerSumDTO userAnswerSumDTO = gson.fromJson(req.getMsg(), type);
            List<UserAnswerSumDTO> result = null;
            if (userAnswerSumDTO.getGoodsId() != null && userAnswerSumDTO.getProductId()==null) {
                result = userAnswerSumService.findListByGoodsId(userAnswerSumDTO);
            }else if(userAnswerSumDTO.getGoodsId() != null && userAnswerSumDTO.getProductId()!=null){
                result = userAnswerSumService.findListByGoodsIdAndProductId(userAnswerSumDTO);
            }else {
                result = userAnswerSumService.findAllList(userAnswerSumDTO);
            }
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswerSumFindAllList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
    /**
     * 云私塾移除错题接口
     */
    public response sty_removeErrorQuestion(request req) throws BusinessException {
        String entry = "sty_removeErrorQuestion";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserErrorQuestion>() {
            }.getType();
            UserErrorQuestion userErrorQuestion = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            userQuestionService.removeErrorQuestion(userErrorQuestion);
            res.setCode(Constants.SUCCESS);
            res.setMsg(String.valueOf(1));
        } catch (Exception e) {
            logger.error("sty_removeErrorQuestion error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    @Deprecated
    public response sty_openAutoRemoveErrorQuestion(request req) throws BusinessException {
        String entry = "sty_openAutoRemoveErrorQuestion";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<OpenAutoRemoveQuery>() {
            }.getType();
            OpenAutoRemoveQuery query = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            errorQuestionService.openAutoRemoveErrorQuestion(query);
            res.setCode(Constants.SUCCESS);
        } catch (Exception e) {
            logger.error("sty_openAutoRemoveErrorQuestion error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getIsAutoRemoveOpened(request req) throws BusinessException {
        String entry = "sty_getIsAutoRemoveOpened";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<BaseAlQuery>() {
            }.getType();
            BaseAlQuery query = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            Boolean isOpened = errorQuestionService.isAutoRemoveOpened(query.getUid(), query.getCategoryId());
            res.setCode(Constants.SUCCESS);
            res.setMsg(isOpened.toString());
        } catch (Exception e) {
            logger.error("sty_getIsAutoRemoveOpened error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserAnswerDetailByQuestions(request req) throws BusinessException {
        String entry = "sty_getUserAnswerDetailByQuestions";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserAnswerDetailQuery>() {
            }.getType();
            UserAnswerDetailQuery query = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            List<UserAnswerDetailDTO> result = userAnswerService.getUserAnswerDetailByQuestions(query.getUid(), query.getQuestionIdList());
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getIsAutoRemoveOpened error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getLastUserAnswerSumByQuestionIds(request req) throws BusinessException {
        String entry = "sty_getLastUserAnswerSumByQuestionIds";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserAnswerSumQuery>() {
            }.getType();
            UserAnswerSumQuery query = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            List<UserAnswerSumDTO> result = userAnswerService.getLastUserAnswerSumByQuestionIds(query.getUid(), query.getQuestionIdList());
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getIsAutoRemoveOpened error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserAnswerMedicalList(request req) throws BusinessException{
        String entry = "sty_getUserAnswerMedicalList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            Long uid = MapUtils.getLong(param, "uid", null);
            Integer isPass = MapUtils.getInteger(param, "isPass", null);
            Long ePaperId = MapUtils.getLong(param, "ePaperId", null);
            Long paperId = MapUtils.getLong(param, "paperId", null);
            Long lessonId = MapUtils.getLong(param, "lessonId", null);
            int from = MapUtils.getIntValue(param, "from", Constants.Page.from);
            int rows = MapUtils.getIntValue(param, "rows", Constants.Page.rows);
            UserAnswerMedical userAnswerMedical = new UserAnswerMedical();
            userAnswerMedical.setUid(uid);
            userAnswerMedical.setIsPass(isPass);
            userAnswerMedical.setEPaperId(ePaperId);
            userAnswerMedical.setPaperId(paperId);
            userAnswerMedical.setLessonId(lessonId);
            Page<UserAnswerMedical> result= userAnswerMedicalService.getUserAnswerMedicalList(userAnswerMedical, from, rows);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswerMedicalList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUidCountByEPaperId(request req) throws BusinessException{
        String entry = "sty_getUidCountByEPaperId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            Long ePaperId = MapUtils.getLong(param, "ePaperId", null);
            Long paperId = MapUtils.getLong(param, "paperId", null);
            Long lessonId = MapUtils.getLong(param, "lessonId", null);
            Integer result= userAnswerMedicalService.getUidCountByEPaperId(ePaperId, paperId, lessonId);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUidCountByEPaperId error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserAnswerMedicalListByIds(request req) throws BusinessException{
        String entry = "sty_getUserAnswerMedicalListByIds";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("ids") == null) {
                logger.error("{} sty_getUserAnswerMedicalListByIds return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserAnswerMedicalListByIds param lose ids is null");
            } else {
                Long[] ids = ParamUtils.getLongArray(param, "ids");
                List<UserAnswerMedical> userAnswerMedicalList = userAnswerMedicalService.getUserAnswerMedicalListByIds(ids);
                if (!org.springframework.util.CollectionUtils.isEmpty(userAnswerMedicalList)) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(userAnswerMedicalList,req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setMsg("sty_getUserAnswerMedicalListByIds return null");
                }
            }
        } catch (Exception e) {
            logger.error("sty_getUserAnswerMedicalListByIds error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getUserHomeworkLastAnswerInfo(request req) throws BusinessException{
        String entry = "sty_getUserHomeworkLastInfo";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null || param.get("lessonIds") == null) {
                logger.error("{} sty_getUserHomeworkLastAnswerInfo return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeworkLastAnswerInfo param lose lessonIds is null or uid is null");
            } else {
                Long uid = MapUtils.getLong(param, "uid", null);
                List<Long> lessonIds = ParamUtils.getLongList(param, "lessonIds", false);
                Long productId = ParamUtils.getLong(param, "productId", true);
                Long goodsId = ParamUtils.getLong(param, "goodsId", true);
                List<UserAnswerHomeworkLastInfo> userAnswerHomeworkLastInfos = userAnswerService.getUserHomeworkLastAnswerInfo(uid, lessonIds, productId, goodsId);
                if (CollectionUtils.isNotEmpty(userAnswerHomeworkLastInfos)) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(userAnswerHomeworkLastInfos,req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setMsg("sty_getUserHomeworkLastInfo return null");
                }
            }
        } catch (Exception e) {
            logger.error("sty_getUserHomeworkLastAnswerInfo error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getUserPaperAnswerLastInfo(request req) throws BusinessException{
        String entry = "sty_getUserPaperAnswerLastInfo";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null || param.get("paperIds") == null) {
                logger.error("{} sty_getUserPaperAnswerLastInfo return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserPaperAnswerLastInfo param lose lessonIds is null or uid is null");
            } else {
                Long uid = MapUtils.getLong(param, "uid", null);
                List<Long> paperIds = ParamUtils.getLongList(param, "paperIds", false);
                Long goodsId = ParamUtils.getLong(param,"goodsId",true);
                List<Integer> objTypeList = ParamUtils.getIntegerList(param,"objTypeList",true);
                Integer paperType = ParamUtils.getInt(param,"paperType",true);
                List<UserAnswerLastInfo> userAnswerLastInfoList = userAnswerService.getUserAnswerLastGroupByPaperId(uid, paperIds,goodsId, objTypeList, paperType);
                if (CollectionUtils.isNotEmpty(userAnswerLastInfoList)) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(userAnswerLastInfoList,req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setMsg("sty_getUserPaperAnswerLastInfo return null");
                }
            }
        } catch (Exception e) {
            logger.error("sty_getUserPaperAnswerLastInfo error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserDoneRecordVoList(request req) throws BusinessException{
        String entry = "sty_getUserDoneRecordVoList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserDoneRecordQuery param = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserDoneRecordQuery.class);
            if (param == null || param.getUid() == null) {
                logger.error("{} sty_getUserDoneRecordVoList return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserDoneRecordVoList param lose uid is null");
            } else {
                param.setFrom(param.getFrom() == null ? 0 : param.getFrom());
                param.setRows(param.getRows() == null ? 10 : param.getRows());
                PageModel<UserDoneRecordVo> userDoneRecordVoPage = userDoneRecordService.getUserDoneRecordVoList(param);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(userDoneRecordVoPage, req.getAppid()));
            }
        } catch (Exception e) {
            logger.error("sty_getUserDoneRecordVoList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserDoneRecordObjTypeList(request req) throws BusinessException{
        String entry = "sty_getUserDoneRecordObjTypeList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserDoneRecordQuery param = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserDoneRecordQuery.class);
            if (param == null || param.getUid() == null) {
                logger.error("{} sty_getUserDoneRecordObjTypeList return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserDoneRecordObjTypeList param lose uid is null");
            } else {
                List<UserDoneRecordObjTypeVo> list = userDoneRecordService.getUserDoneRecordObjTypeList(param);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(list, req.getAppid()));
            }
        } catch (Exception e) {
            logger.error("sty_getUserDoneRecordObjTypeList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserErrorAndCorrectQuestionCount(request req) throws BusinessException{
        String entry = "sty_getUserErrorAndCorrectQuestionCount";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserErrorAndCorrectQuestionQuery param = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserErrorAndCorrectQuestionQuery.class);
            if (param == null || param.getUid() == null) {
                logger.error("{} sty_getUserDoneRecordVoList return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserDoneRecordVoList param lose uid is null");
            } else {
                UserErrorAndCorrectQuestionCountVo userErrorAndCorrectQuestionCountVo = userAnswerService.getUserErrorAndCorrectQuestionCount(param);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(userErrorAndCorrectQuestionCountVo, req.getAppid()));
            }
        } catch (Exception e) {
            logger.error("sty_getUserDoneRecordVoList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserCorrectQuestion(request req) throws BusinessException{
        String entry = "sty_getUserCorrectQuestion";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserErrorAndCorrectQuestionQuery param = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserErrorAndCorrectQuestionQuery.class);
            if (param == null || param.getUid() == null) {
                logger.error("{} sty_getUserCorrectQuestion return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserCorrectQuestion param lose uid is null");
            } else {
                Page<UserCorrectQuestion> page = userAnswerService.getUserCorrectQuestion(param);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(page, req.getAppid()));
            }
        } catch (Exception e) {
            logger.error("sty_getUserCorrectQuestion error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserSubErrorQuestion(request req) throws BusinessException{
        String entry = "sty_getUserSubErrorQuestion";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserErrorAndCorrectQuestionQuery param = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserErrorAndCorrectQuestionQuery.class);
            if (param == null || param.getUid() == null) {
                logger.error("{} sty_getUserSubErrorQuestion return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserSubErrorQuestion param lose uid is null");
            } else {
                Page<UserSubErrorQuestion> page = userAnswerService.getUserSubErrorQuestion(param);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(page, req.getAppid()));
            }
        } catch (Exception e) {
            logger.error("sty_getUserSubErrorQuestion error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserHomeWorkAnswerById(request req) throws BusinessException{
        String entry = "sty_getUserHomeWorkAnswerById";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserHomeWorkAnswer param = gson.fromJson(req.getMsg(), UserHomeWorkAnswer.class);
            if (param == null || param.getUid() == null || param.getId() == null) {
                logger.error("{} sty_getUserHomeWorkAnswerById return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeWorkAnswerById param lose id is null or uid is null");
            } else {
                Long uid = param.getUid();
                Long id = param.getId();
                UserHomeWorkAnswer userHomeWorkAnswer = new UserHomeWorkAnswer();
                userHomeWorkAnswer.setId(id);
                userHomeWorkAnswer.setUid(uid);
                List<UserHomeWorkAnswer> userHomeWorkAnswerList = userAnswerService.getUserAnswerHomeworkList(userHomeWorkAnswer);
                if (CollectionUtils.isNotEmpty(userHomeWorkAnswerList)) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(userHomeWorkAnswerList.get(0),req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setMsg("sty_getUserHomeWorkAnswerById return null");
                }
            }
        } catch (Exception e) {
            logger.error("sty_getUserHomeWorkAnswerById error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_updateUserDoneRecord(request req) throws BusinessException{
        String entry = "sty_updateUserDoneRecord";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null ) {
                logger.error("{} sty_updateUserDoneRecord return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_updateUserDoneRecord param lose uid is null");
            } else {
                Long uid = MapUtils.getLong(param, "uid", null);
                Long categoryId = ParamUtils.getLong(param, "categoryId", true);
                Long secondCategoryId = ParamUtils.getLong(param, "secondCategoryId", true);
                Integer objType = ParamUtils.getInt(param, "objType", true);
                String delFlag = ParamUtils.getString(param, "delFlag", true);
                UserDoneRecord userDoneRecord = new UserDoneRecord();
                userDoneRecord.setUid(uid);
                userDoneRecord.setDelFlag("0");
                userDoneRecord.setObjType(objType);
                userDoneRecord.setCategoryId(categoryId);
                userDoneRecord.setSecondCategoryId(secondCategoryId);
                List<UserDoneRecord> userDoneRecordList = userDoneRecordService.findList(userDoneRecord);
                boolean updateFlag = false;
                if(CollectionUtils.isNotEmpty(userDoneRecordList)){
                    for(UserDoneRecord userDoneRecordTmp:userDoneRecordList){
                        if(delFlag!=null){
                            userDoneRecordTmp.setDelFlag(delFlag);
                        }
                        updateFlag = userDoneRecordService.update(userDoneRecordTmp);
                    }
                }
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(updateFlag,req.getAppid()));
            }
        } catch (Exception e) {
            logger.error("sty_updateUserDoneRecord error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
    public response sty_getUserHomeWorkAnswerByParam(request req) throws BusinessException{
        String entry = "sty_getUserHomeWorkAnswerByParam";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null ) {
                logger.error("{} sty_getUserHomeWorkAnswerByParam param lose uid is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeWorkAnswerByParam param lose uid is null");
            } else {
                Long uid = MapUtils.getLong(param, "uid", null);
                Long id = ParamUtils.getLong(param, "id", true);
                List<Long> idList = ParamUtils.getLongList(param, "idList", true);
                Long goodsId = ParamUtils.getLong(param, "goodsId", true);
                Long productId = ParamUtils.getLong(param, "productId", true);
                Long objId = ParamUtils.getLong(param, "objId", true);
                Integer objType = ParamUtils.getInt(param, "objType", true);
                Long startTime = ParamUtils.getLong(param, "startTime", true);
                Long endTime = ParamUtils.getLong(param, "endTime", true);
                Long homeworkId = ParamUtils.getLong(param, "homeworkId", true);
                List<Long> homeworkIdList = ParamUtils.getLongList(param, "homeworkIdList", true);

                UserHomeWorkAnswer userHomeWorkAnswer = new UserHomeWorkAnswer();
                userHomeWorkAnswer.setId(id);
                userHomeWorkAnswer.setIdList(idList);
                userHomeWorkAnswer.setUid(uid);
                userHomeWorkAnswer.setGoodsId(goodsId);
                userHomeWorkAnswer.setProductId(productId);
                userHomeWorkAnswer.setObjId(objId);
                userHomeWorkAnswer.setObjType(objType);
                userHomeWorkAnswer.setHomeworkId(homeworkId);
                userHomeWorkAnswer.setHomeworkIdList(homeworkIdList);
                if(startTime!=null){
                    userHomeWorkAnswer.setStartTime(new Date(startTime));
                }
                if(endTime!=null){
                    userHomeWorkAnswer.setEndTime(new Date(endTime));
                }
                List<UserHomeWorkAnswer> userHomeWorkAnswerList = userAnswerService.getUserAnswerHomeworkList(userHomeWorkAnswer);
                if (CollectionUtils.isNotEmpty(userHomeWorkAnswerList)) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(userHomeWorkAnswerList,req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setMsg("sty_getUserHomeWorkAnswerByParam return null");
                }
            }
        } catch (Exception e) {
            logger.error("sty_getUserHomeWorkAnswerByParam error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
    public response sty_getUserAnswerByParam(request req) throws BusinessException{
        String entry = "sty_getUserAnswerByParam";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null ) {
                logger.error("{} sty_getUserAnswerByParam param lose uid is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserAnswerByParam param lose uid is null");
            } else {
                Long uid = MapUtils.getLong(param, "uid", null);
                Long id = ParamUtils.getLong(param, "id", true);
                List<Long> idList = ParamUtils.getLongList(param, "idList", true);
                Long goodsId = ParamUtils.getLong(param, "goodsId", true);
                Long productId = ParamUtils.getLong(param, "productId", true);
                Long objId = ParamUtils.getLong(param, "objId", true);
                Integer objType = ParamUtils.getInt(param, "objType", true);
                Long startTime = ParamUtils.getLong(param, "startTime", true);
                Long endTime = ParamUtils.getLong(param, "endTime", true);
                Long paperId = ParamUtils.getLong(param, "paperId", true);
                List<Long> paperIdList = ParamUtils.getLongList(param, "paperIdList", true);

                UserAnswer userAnswer = new UserAnswer();
                userAnswer.setId(id);
                userAnswer.setAnswerIds(idList);
                userAnswer.setUid(uid);
                userAnswer.setGoodsId(goodsId);
                userAnswer.setProductId(productId);
                userAnswer.setObjId(objId);
                userAnswer.setObjType(objType);
                userAnswer.setPaperId(paperId);
                userAnswer.setPaperIds(paperIdList);
                if(startTime!=null){
                    userAnswer.setStartTime(new Date(startTime));
                }
                if(endTime!=null){
                    userAnswer.setEndTime(new Date(endTime));
                }
                List<UserAnswer> userAnswerList = userAnswerService.getUserAnswerList(userAnswer);
                if (CollectionUtils.isNotEmpty(userAnswerList)) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(userAnswerList,req.getAppid()));
                }else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setMsg("sty_getUserAnswerByParam return null");
                }
            }
        } catch (Exception e) {
            logger.error("sty_getUserAnswerByParam error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getPaperAnswerDetailsList(request req) throws BusinessException{
        String entry = "sty_getPaperAnswerDetailsList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAnswer params = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserAnswer.class);
            List<UserAnswerDetail> result = null;
            Map<String,Object> reqDetailParams = new HashMap<>();
            reqDetailParams.put("uid",params.getUid());
            if(CollectionUtils.isNotEmpty(params.getAnswerIds())){
                reqDetailParams.put("answerIds",params.getAnswerIds());
            } else {
                List<StudyDetails> list = userAnswerService.findStudyDetailsList(params);
                if(CollectionUtils.isNotEmpty(list)){
                    List<Long> answerIds = list.stream().map(StudyDetails::getId).collect(Collectors.toList());
                    reqDetailParams.put("answerIds",answerIds);
                }
            }
            result = userAnswerSumService.getQuestionListByAnswerIds(reqDetailParams);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getPaperAnswerDetailsList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_syncHomeworkVideoCourse(request req) throws BusinessException {
        String entry = "sty_syncHomeworkVideoCourse";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            SyncHomeworkDTO param = GsonUtil.getGenericGson().fromJson(req.getMsg(), SyncHomeworkDTO.class);
            List<Long> resIdList = param.getResIdList();
            Integer zoneIndex = param.getZoneIndex();
            Integer zoneTotal = param.getZoneTotal();
            Integer isClearRedis = param.getIsClearRedis();//是否清除相关的redis值 1是
            Integer isReadRedisInt = param.getIsReadRedis();//是否读取redis的线程的resList值做比较 1是（默认） 0否
            boolean isReadRedis = true;
            if (isReadRedisInt != null && isReadRedisInt == 0) {
                isReadRedis = false;
            }
            boolean rs = userAnswerService.syncHomeworkVideoCourse(resIdList, zoneIndex, zoneTotal, isClearRedis, isReadRedis);
            res.setCode(Constants.SUCCESS);
            res.setMsg(rs + "");
        } catch (Exception e) {
            logger.error("{} error! e:{}", entry, e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_syncHomeworkProductSchedule(request req) throws BusinessException {
        String entry = "sty_syncHomeworkProductSchedule";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            SyncHomeworkDTO param = GsonUtil.getGenericGson().fromJson(req.getMsg(), SyncHomeworkDTO.class);
            List<Long> resIdList = param.getResIdList();
            Integer zoneIndex = param.getZoneIndex();
            Integer zoneTotal = param.getZoneTotal();
            Integer isClearRedis = param.getIsClearRedis();//是否清除相关的redis值 1是
            Integer isReadRedisInt = param.getIsReadRedis();//是否读取redis的线程的resList值做比较 1是（默认） 0否
            boolean isReadRedis = true;
            if (isReadRedisInt != null && isReadRedisInt == 0) {
                isReadRedis = false;
            }
            boolean rs = userAnswerService.syncHomeworkProductSchedule(resIdList, zoneIndex, zoneTotal, isClearRedis, isReadRedis);
            res.setCode(Constants.SUCCESS);
            res.setMsg(rs + "");
        } catch (Exception e) {
            logger.error("{} error! e:{}", entry, e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_syncHomeworkProductAdaptiveLearning(request req) throws BusinessException {
        String entry = "sty_syncHomeworkProductAdaptiveLearning";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            SyncHomeworkDTO param = GsonUtil.getGenericGson().fromJson(req.getMsg(), SyncHomeworkDTO.class);
            List<Long> resIdList = param.getResIdList();
            Integer zoneIndex = param.getZoneIndex();
            Integer zoneTotal = param.getZoneTotal();
            Integer isClearRedis = param.getIsClearRedis();//是否清除相关的redis值 1是
            Integer isReadRedisInt = param.getIsReadRedis();//是否读取redis的线程的resList值做比较 1是（默认） 0否
            boolean isReadRedis = true;
            if (isReadRedisInt != null && isReadRedisInt == 0) {
                isReadRedis = false;
            }
            boolean rs = userAnswerService.syncHomeworkProductAdaptiveLearning(resIdList, zoneIndex, zoneTotal, isClearRedis, isReadRedis);
            res.setCode(Constants.SUCCESS);
            res.setMsg(rs + "");
        } catch (Exception e) {
            logger.error("{} error! e:{}", entry, e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_syncHomeworkIdVideoCourse(request req) throws BusinessException {
        String entry = "sty_syncHomeworkIdVideoCourse";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            SyncHomeworkDTO param = GsonUtil.getGenericGson().fromJson(req.getMsg(), SyncHomeworkDTO.class);
            List<Long> resIdList = param.getResIdList();
            Integer zoneIndex = param.getZoneIndex();
            Integer zoneTotal = param.getZoneTotal();
            Integer isClearRedis = param.getIsClearRedis();//是否清除相关的redis值 1是
            Integer isReadRedisInt = param.getIsReadRedis();//是否读取redis的线程的resList值做比较 1是（默认） 0否
            boolean isReadRedis = true;
            if (isReadRedisInt != null && isReadRedisInt == 0) {
                isReadRedis = false;
            }
            boolean rs = userAnswerService.syncHomeworkIdVideoCourse(resIdList, zoneIndex, zoneTotal, isClearRedis, isReadRedis);
            res.setCode(Constants.SUCCESS);
            res.setMsg(rs + "");
        } catch (Exception e) {
            logger.error("{} error! e:{}", entry, e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_syncHomeworkIdProductSchedule(request req) throws BusinessException {
        String entry = "sty_syncHomeworkIdProductSchedule";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            SyncHomeworkDTO param = GsonUtil.getGenericGson().fromJson(req.getMsg(), SyncHomeworkDTO.class);
            List<Long> resIdList = param.getResIdList();
            Integer zoneIndex = param.getZoneIndex();
            Integer zoneTotal = param.getZoneTotal();
            Integer isClearRedis = param.getIsClearRedis();//是否清除相关的redis值 1是
            Integer isReadRedisInt = param.getIsReadRedis();//是否读取redis的线程的resList值做比较 1是（默认） 0否
            boolean isReadRedis = true;
            if (isReadRedisInt != null && isReadRedisInt == 0) {
                isReadRedis = false;
            }
            boolean rs = userAnswerService.syncHomeworkIdProductSchedule(resIdList, zoneIndex, zoneTotal, isClearRedis, isReadRedis);
            res.setCode(Constants.SUCCESS);
            res.setMsg(rs + "");
        } catch (Exception e) {
            logger.error("{} error! e:{}", entry, e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_syncHomeworkIdProductAdaptiveLearning(request req) throws BusinessException {
        String entry = "sty_syncHomeworkIdProductAdaptiveLearning";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            SyncHomeworkDTO param = GsonUtil.getGenericGson().fromJson(req.getMsg(), SyncHomeworkDTO.class);
            List<Long> resIdList = param.getResIdList();
            Integer zoneIndex = param.getZoneIndex();
            Integer zoneTotal = param.getZoneTotal();
            Integer isClearRedis = param.getIsClearRedis();//是否清除相关的redis值 1是
            Integer isReadRedisInt = param.getIsReadRedis();//是否读取redis的线程的resList值做比较 1是（默认） 0否
            boolean isReadRedis = true;
            if (isReadRedisInt != null && isReadRedisInt == 0) {
                isReadRedis = false;
            }
            boolean rs = userAnswerService.syncHomeworkIdProductAdaptiveLearning(resIdList, zoneIndex, zoneTotal, isClearRedis, isReadRedis);
            res.setCode(Constants.SUCCESS);
            res.setMsg(rs + "");
        } catch (Exception e) {
            logger.error("{} error! e:{}", entry, e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getPaperAnswerCountByUid(request req) throws BusinessException{
        response res = new response();
        String entry = "sty_getPaperAnswerCountByUid";
        long start = System.currentTimeMillis();
        Integer doneTime = 0;
        enterValidator(entry,start,req);
        try{
            UserAnswerQuery userAnswerQuery = gson.fromJson(req.getMsg(), UserAnswerQuery.class);
            if (userAnswerQuery == null || userAnswerQuery.getPaperId() == null || userAnswerQuery.getUid() == null){
                res.setCode(Constants.PARAM_INVALID);
                res.setErrormsg("getPaperAnswerByUid lose param");
            }
            UserAnswer userAnswer = new UserAnswer();
            userAnswer.setUid(userAnswerQuery.getUid());
            userAnswer.setPaperId(userAnswerQuery.getPaperId());
            doneTime = userAnswerService.countStudyDetailsList(userAnswer);
            res.setMsg(GsonUtil.toJson(doneTime));
            res.setCode(Constants.SUCCESS);

        }catch (Exception e){
            logger.info("getPaperAnswerCountByUid return null");
            res.setCode(Constants.OBJ_NOT_EXISTS);
            res.setErrormsg("getPaperAnswerByUid has no answer");
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 获取作业作答数量（只能查询submithomework时参数为iCount=1的作答）
     */
    public response sty_getHomeworkAnswerCount(request req) throws BusinessException {
        response res = new response();
        String entry = "sty_getHomeworkAnswerCount";
        long start = System.currentTimeMillis();
        long doneTime = 0;
        enterValidator(entry,start,req);
        try{
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserAnswerSumCountQuery>() {}.getType();
            UserAnswerSumCountQuery userAnswerSumCountQuery = gson.fromJson(req.getMsg(), type);
            if (userAnswerSumCountQuery == null || (userAnswerSumCountQuery.getLessonId()==null && userAnswerSumCountQuery.getHomeworkId() == null)){
                res.setCode(Constants.PARAM_INVALID);
                res.setErrormsg("sty_getHomeworkAnswerCount lose param");
            }
            doneTime = userAnswerService.getHomeworkAnswerCount(userAnswerSumCountQuery);
            res.setMsg(GsonUtil.toJson(doneTime));
            res.setCode(Constants.SUCCESS);

        }catch (Exception e){
            logger.info("sty_getHomeworkAnswerCount return null");
            res.setCode(Constants.OBJ_NOT_EXISTS);
            res.setErrormsg("sty_getHomeworkAnswerCount has no answer");
            e.printStackTrace();
        }
        return res;
    }

    public response sty_delHomeWorkRecord(request req) throws BusinessException{
        response res = new response();
        String entry = "sty_delHomeWorkRecord";
        long start = System.currentTimeMillis();
        long doneTime = 0;
        enterValidator(entry,start,req);
        try{
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserHomeWorkAnswer>() {}.getType();
            UserHomeWorkAnswer userHomeWorkAnswer = gson.fromJson(req.getMsg(), type);
            if (userHomeWorkAnswer == null || (userHomeWorkAnswer.getObjId()==null && userHomeWorkAnswer.getHomeworkId() == null)){
                res.setCode(Constants.PARAM_INVALID);
                res.setErrormsg("sty_delHomeWorkRecord lose param");
            }
            userAnswerService.delHomeWorkRecord(userHomeWorkAnswer);
            userDoneRecordService.deleteByParam(userHomeWorkAnswer.getUid(),2,userHomeWorkAnswer.getObjId());
            res.setMsg(GsonUtil.toJson(true));
            res.setCode(Constants.SUCCESS);
            return res;
        }catch (Exception e){
            logger.info("sty_getHomeworkAnswerCount return null");
            res.setCode(Constants.OBJ_NOT_EXISTS);
            res.setErrormsg("sty_getHomeworkAnswerCount has no answer");
            e.printStackTrace();
        }
        return res;
    }

    public response sty_getUserHomeWorkAnswerByHomeworkId(request req) throws BusinessException{
        String entry = "sty_getUserHomeWorkAnswerByHomeworkId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Double> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null || param.get("uid") <= 0
                    || param.get("homeworkId") == null || param.get("homeworkId") <= 0) {
                logger.error( "{} sty_getUserHomeWorkAnswerByHomeworkId parameter lose,uid or homeworkId is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserHomeWorkAnswerByHomeworkId parameter lose,uid or homeworkId is null");
                return res;
            }

            Long homeworkId = MapUtils.getLongValue(param, "homeworkId", 0);
            Long uid = MapUtils.getLongValue(param, "uid", 0);
            int from = MapUtils.getIntValue(param, "from", 0);
            int rows = MapUtils.getIntValue(param, "rows", 10);

            int total = userAnswerService.findUserHomeWorkInfoCount(param);
            if (total == 0) {
                logger.info("{} sty_getUserHomeWorkAnswerByHomeworkId return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserHomeWorkAnswerByHomeworkId return null");
            } else {

                List<UserHomeWorkAnswer> userAnswers = userAnswerService.findUserHomeWorkInfo(param, from, rows);

                if (userAnswers == null || userAnswers.size() == 0) {
                    logger.info("{} sty_getUserHomeWorkAnswerByHomeworkId return null.", entry);
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setErrormsg("sty_getUserHomeWorkAnswerByHomeworkId return null");
                } else {
                    PageModel<UserHomeWorkAnswer> returnUserAnswer = new PageModel<UserHomeWorkAnswer>(total,
                            userAnswers);
                    res.code = Constants.SUCCESS;
                    res.setMsg(GsonUtil.toJson(returnUserAnswer, req.getAppid()));
                }
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_syncLessonIdUserAnswerMedical(request req) throws BusinessException {
        String entry = "sty_syncLessonIdUserAnswerMedical";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            SyncHomeworkDTO param = GsonUtil.getGenericGson().fromJson(req.getMsg(), SyncHomeworkDTO.class);
            List<String> resIdStrList = param.getResIdStrList();
            userAnswerMedicalService.syncLessonIdUserAnswerMedical(resIdStrList);
            res.setCode(Constants.SUCCESS);
            res.setMsg("true");
        } catch (Exception e) {
            logger.error("{} error! e:{}", entry, e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_findUserHomeWorkAnswerInfosGroupByHomeworkId(request req) throws BusinessException{
        String entry = "sty_findUserHomeWorkAnswerInfosGroupByHomeworkId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserHomeworkAnswerQuery query = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserHomeworkAnswerQuery.class);
            if (query == null || query.getUid() == null || query.getUid() <= 0
                    || ((query.getHomeworkId()== null || query.getHomeworkId() <= 0)
                       && CollectionUtils.isEmpty(query.getHomeworkIdList()))) {
                logger.error( "{} sty_findUserHomeWorkAnswerInfosGroupByHomeworkId parameter lose,uid or homeworkId or homeworkIdList is null", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_findUserHomeWorkAnswerInfosGroupByHomeworkId parameter lose,uid or homeworkId or homeworkIdList is null");
                return res;
            }
            List<UserHomeworkAnswerDTO> userHomeworkAnswers= userAnswerService.findUserHomeWorkAnswerInfosGroupByHomeworkId(query);
            if (userHomeworkAnswers == null || userHomeworkAnswers.size() == 0) {
                logger.info("{} findUserHomeWorkAnswerInfosGroupByHomeworkId return null.", entry);
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("findUserHomeWorkAnswerInfosGroupByHomeworkId return null");
            } else {
                res.code = Constants.SUCCESS;
                res.setMsg(GsonUtil.toJson(userHomeworkAnswers, req.getAppid()));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_findAlSubmitQuestionList(request req) throws BusinessException {
        String entry = "sty_findAlSubmitQuestionList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserAnswerSumDTO>() {
            }.getType();
            UserAnswerSumDTO userAnswerSumDTO = gson.fromJson(req.getMsg(), type);
            List<UserAnswerSumDTO> result = userAnswerSumService.findAlSubmitQuestionList(userAnswerSumDTO);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_findAlSubmitQuestionList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_findAlSubmitQuestionListByPaperAndHomework(request req) throws BusinessException {
        String entry = "sty_findAlSubmitQuestionListByPaperAndHomework";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserAnswerSumDTO>() {
            }.getType();
            UserAnswerSumDTO userAnswerSumDTO = gson.fromJson(req.getMsg(), type);
            List<UserAnswerSumDTO> result = userAnswerSumService.findAlSubmitQuestionListByPaperAndHomework(userAnswerSumDTO);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_findAlSubmitQuestionList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_submitEvaluationQuestion(request req) throws BusinessException {
        String entry = "sty_submitEvaluationQuestion";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<EvaluationSubmitDTO>() {
            }.getType();
            EvaluationSubmitDTO evaluationSubmitDTO = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            EvaluationSubmitRetDTO result = evaluationUserAnswerService.submitEvaluationQuestion(evaluationSubmitDTO);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.getGenericGson().toJson(result));
        } catch (Exception e) {
            logger.error("sty_submitEvaluationQuestion error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getEvaluationUserAnswerList(request req) throws BusinessException {
        String entry = "sty_getEvaluationUserAnswerList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<EvaluationUserAnswerQuery>() {
            }.getType();
            EvaluationUserAnswerQuery userAnswerQuery = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            List<EvaluationUserAnswerDTO> result = evaluationUserAnswerService.getEvaluationUserAnswerList(userAnswerQuery);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.getGenericGson().toJson(result));
        } catch (Exception e) {
            logger.error("sty_getEvaluationUserAnswerList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getEvaluationUserAnswerByAnswerId(request req) throws BusinessException {
        String entry = "sty_getEvaluationUserAnswerByAnswerId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<EvaluationUserAnswerQuery>() {
            }.getType();
            EvaluationUserAnswerQuery userAnswerQuery = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            EvaluationUserAnswerDTO result = evaluationUserAnswerService.getEvaluationUserAnswerByAnswerId(userAnswerQuery);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.getGenericGson().toJson(result));
        } catch (Exception e) {
            logger.error("sty_getEvaluationUserAnswerByAnswerId error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_findUserAnswerErrorQuestionInfoList(request req) throws BusinessException {
        String entry = "sty_findUserAnswerErrorQuestionInfoList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserErrorAnswerQuery>() {
            }.getType();
            UserErrorAnswerQuery userAnswerQuery = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            Integer answerType=userAnswerQuery.getAnswerType();
            Long uid=userAnswerQuery.getUid();
            List<Long> answerIdList=userAnswerQuery.getAnswerIdList();
            List<UserAnswerErrorQuestionVo> userAnswerErrorQuestionVoList=null;
            if(answerType==1){//试卷作答
                userAnswerErrorQuestionVoList=userAnswerSumService.findErrorPaperAnswerSumDetailGroupByAnswerId(uid,answerIdList);
            }else{
                //作业作答
                userAnswerErrorQuestionVoList=userAnswerSumService.findErrorHomeworkAnswerSumDetailGroupByAnswerId(uid,answerIdList);
            }
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.getGenericGson().toJson(userAnswerErrorQuestionVoList));
        } catch (Exception e) {
            logger.error("sty_findUserAnswerErrorQuestionInfoList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 获取课前做题试卷答题情况
     */
    public response sty_getUserAnswersByPreClassExercise(request req) throws BusinessException {
        String entry = "sty_getUserAnswersByPreClassExercise";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            List<Long> paperIds = ParamUtils.getLongList(params, "paperIds", false);
            List<UserAnswer> result = userAnswerService.getUserAnswersByPreClassExercise(uid, paperIds);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswersForGoods error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserAnswersByPaperIdsAndObjType(request req) throws BusinessException {
        String entry = "sty_getUserAnswersByPaperIdsAndObjType";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            List<Long> paperIds = ParamUtils.getLongList(params, "paperIds", false);
            Integer objType = ParamUtils.getInt(params,"objType", false);
            List<UserAnswer> result = userAnswerService.getUserAnswersByPaperIdsAndObjType(uid, paperIds, objType);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswersByPaperIdsAndObjType error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_findUserHomeWorkAnswersByUserHomeworkIds(request req) throws BusinessException{
        String entry = "sty_findUserHomeWorkAnswersByUserHomeworkIds";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            List<Long> homeworkIdList = ParamUtils.getLongList(params, "homeworkIdList", false);
            List<UserHomeWorkAnswer> result =userAnswerService.getUserHomeWorkAnswersByUserHomeworkIds(uid, homeworkIdList);
            res.setMsg(GsonUtils.toJson(result));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getSolutionQuestionCount(request req) throws BusinessException {
        String entry = "sty_getSolutionQuestionCount";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long categoryId = ParamUtils.getLong(params, "categoryId", false);
            Long result =solutionQuestionService.getSolutionQuestionCount(uid, categoryId);
            res.setMsg(GsonUtils.toJson(result));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_delCorrectQuestionByCategory(request req) throws BusinessException {
        String entry = "sty_delCorrectQuestionByCategory";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long categoryId = ParamUtils.getLong(params, "categoryId", false);
            Long goodsId = ParamUtils.getLong(params, "goodsId", true);
            Long questionId = ParamUtils.getLong(params, "questionId", true);
            String startDate = ParamUtils.getString(params, "startDate", true);
            String endDate  = ParamUtils.getString(params, "endDate", true);
            Integer sourceType = ParamUtils.getInt(params, "sourceType", true);
            if (Objects.isNull(sourceType)){
                sourceType = 1;
            }
            Boolean deleteResult = userAnswerService.removeCorrectQuestionByCategory(uid, categoryId, goodsId, questionId,startDate,endDate,sourceType);
            res.setCode(Constants.SUCCESS);
            if (deleteResult!=null) {
                res.setMsg(deleteResult.toString());
            }
        } catch (Exception e) {
            logger.error("sty_delErrorQuestionByCategory error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getUserAnswerDetailList(request req) throws BusinessException {
        String entry = "sty_getUserAnswerDetailList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<UserAnswerDetailQuestionQuery>() {
            }.getType();
            List<UserAnswerDetailDto> result = new ArrayList<>();
            UserAnswerDetailQuestionQuery query = GsonUtil.getGenericGson().fromJson(req.getMsg(), type);
            //questionId不能为空
            if (query.getQuestionId() == null) {
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserAnswerDetailList param lose questionId is null");
                return res;
            }
            List<Long> questionList = new ArrayList<>();
            questionList.add(query.getQuestionId());
            List<UserAnswerSum> answerSum = userAnswerSumDao.findAnswerSumAnswerOrHomework(query.getUid(), query.getAnswerId(), questionList);
            if (CollectionUtils.isEmpty(answerSum)) {
                res.setCode(Constants.SUCCESS);
                res.setMsg(JSONUtils.toJsonString(result));
                return res;
            }
            List<Long> sumIdList = answerSum.stream().map(UserAnswerSum::getId).collect(Collectors.toList());
            List<UserAnswerDetailDto> answerDetail = userAnswerDetailDao.getUserAnswerDetailByQuestionAndAnswer(query.getUid(), sumIdList,query.getQuestionId());

            for(UserAnswerDetailDto a:answerDetail){
                for(UserAnswerSum b:answerSum){
                    if(a.getSumId().equals(b.getId())){
                        a.setPaperId(b.getPaperId());
                        result.add(a);
                    }
                }
            }
//            List<UserAnswerDetailDto> result = answerDetail.stream().peek(a -> answerSum.stream().filter(b -> b.getId().equals(a.getSumId()))
//                    .findFirst().ifPresent(b -> a.setPaperId(b.getPaperId()))
//            ).collect(Collectors.toList());
            res.setCode(Constants.SUCCESS);
            res.setMsg(JSONUtils.toJsonString(result));
        } catch (Exception e) {
            logger.error("sty_getUserAnswerDetailList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserCorrectQuestionCount(request req) throws BusinessException{
        String entry = "sty_getUserCorrectQuestionCount";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserErrorAndCorrectQuestionQuery param = GsonUtil.getGenericGson().fromJson(req.getMsg(), UserErrorAndCorrectQuestionQuery.class);
            if (param == null || param.getUid() == null) {
                logger.error("{} sty_getUserCorrectQuestionCount return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserCorrectQuestionCount param lose uid is null");
            } else {
                UserErrorAndCorrectQuestionCountDTO result = userAnswerService.getUserCorrectQuestionCount(param);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
            }
        } catch (Exception e) {
            logger.error("sty_getUserCorrectQuestionCount error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
