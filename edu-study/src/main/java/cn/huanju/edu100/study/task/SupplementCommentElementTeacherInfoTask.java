package cn.huanju.edu100.study.task;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.mapper.homework.comment.CommentElementMapper;
import cn.huanju.edu100.study.model.CommentElementPO;
import cn.huanju.edu100.study.model.Teacher;
import cn.huanju.edu100.study.model.al.StudyPath;
import cn.huanju.edu100.study.model.goods.LessonVideo;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserAnswerService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.stustamp.model.ResourceLive;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hqwx.goods.dto.ProductExternalLesson;
import com.hqwx.goods.dto.ResourceVideo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Created by zhanghong on 2023/6/1.
 */
@Service
public class SupplementCommentElementTeacherInfoTask {

    private static final Logger logger = LoggerFactory.getLogger(SupplementCommentElementTeacherInfoTask.class);

    @Autowired
    private CommentElementMapper commentElementMapper;

    @Resource
    private GoodsResource goodsResource;

    @Resource
    private KnowledgeResource knowledgeResource;

    @Autowired
    private JedisCluster localJedisCluster;

    private static final String LastTeacherTaskId = "SupplementCommentElementTeacherInfoTask";

    @XxlJob("SupplementCommentElementTeacherInfoTask")
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("SupplementCommentElementTeacherInfoTask start");


        new Thread(new Runnable() {
            @Override
            public void run() {
                handleSupplementCommentTeacherInfo();
            }
        }).start();

        return ReturnT.SUCCESS;
    }

    private void handleSupplementCommentTeacherInfo(){

        Long startId = 0L;
        String startIdString = localJedisCluster.get(LastTeacherTaskId);
        if(StringUtils.isNotEmpty(startIdString)){
            startId = Long.valueOf(startIdString);
        }

        Date startDate = new Date(1609467020000L);

        while(true){
            LambdaQueryWrapper<CommentElementPO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.isNull(CommentElementPO::getTeacherId);
            queryWrapper.ge(CommentElementPO::getId,startId);
            queryWrapper.ge(CommentElementPO::getCreateDate,startDate);
            queryWrapper.select(CommentElementPO::getId,CommentElementPO::getObjId,CommentElementPO::getObjType,
                    CommentElementPO::getProductId,CommentElementPO::getCategoryId,
                    CommentElementPO::getUpdateDate);
            queryWrapper.orderByAsc(CommentElementPO::getId);
            queryWrapper.last(" limit 1000");

            List<CommentElementPO> commentElementList = commentElementMapper.selectList(queryWrapper);
            if(CollectionUtils.isEmpty(commentElementList)){
                return;
            }
            int size = commentElementList.size();
            CommentElementPO tempPO = commentElementList.get(size-1);
            startId = tempPO.getId();
            localJedisCluster.set(LastTeacherTaskId,String.valueOf(startId));
            commentElementList.stream().forEach(new Consumer<CommentElementPO>() {
                @Override
                public void accept(CommentElementPO commentElementPO) {
                    Integer objType = commentElementPO.getObjType();
                    if(objType==null || IdUtils.isValid(commentElementPO.getTeacherId())){
                        return;
                    }
                    if(objType == 0){
//                    supplementLessonVideoTeacherInfo(commentElementPO);
                    } else if(objType == 1){
                        supplementProductExternalLessonInfo(commentElementPO);
                    } else if(objType == 3){
                        supplementStudyPathLessonInfo(commentElementPO);
                    }
                }
            });
        }


    }

    private void supplementLessonVideoTeacherInfo(CommentElementPO commentElement){
        List<Long> idList = Lists.newArrayList();
        idList.add(commentElement.getObjId());
        Map<Long, LessonVideo> lessonVideoMap = goodsResource.getLessonVideosByIdList(idList);
        if(MapUtils.isNotEmpty(lessonVideoMap)) {
            LessonVideo lessonVideo = lessonVideoMap.get(commentElement.getObjId());
            if(lessonVideo != null) {
                commentElement.setTeacherId(lessonVideo.getTeacherId());
                commentElement.setTeacherName(lessonVideo.getTeacherName());
            }
        }
        if(!IdUtils.isValid(commentElement.getTeacherId())){
            return;
        }

        commentElementMapper.updateById(commentElement);
    }

    private void supplementProductExternalLessonInfo(CommentElementPO commentElement){
        ProductExternalLesson externalLesson = goodsResource.getProductExternalLessonById(commentElement.getObjId());

        if(externalLesson==null || !IdUtils.isValid(externalLesson.getTeacherId())){
            return;
        }
        Long teacherId = externalLesson.getTeacherId();
        commentElement.setTeacherId(teacherId);
        Teacher teacher = goodsResource.getTeacherById(teacherId);
        if(teacher!=null){
            commentElement.setTeacherName(teacher.getName());
        }
        if(!IdUtils.isValid(commentElement.getTeacherId())){
            return;
        }

        commentElementMapper.updateById(commentElement);
    }


    private void supplementStudyPathLessonInfo(CommentElementPO commentElement){
        Long productId = commentElement.getProductId();
        Long categoryId = commentElement.getCategoryId();
        if(!IdUtils.isValid(productId)){
            return;
        }

        List<StudyPath> studyPathList = goodsResource.getStudyPathForProduct(categoryId,productId);
        if(CollectionUtils.isEmpty(studyPathList)){
            return;
        }

        studyPathList = studyPathList.stream().filter(a->IdUtils.isValid(a.getResourceId())).filter(a->a.getResourceId().equals(commentElement.getObjId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(studyPathList)){
            return;
        }

        StudyPath studyPath = studyPathList.get(0);
        Integer resourceType = studyPath.getResourceType();
        Long resourceId = studyPath.getResourceId();
        if(!IdUtils.isValid(resourceId) || !IdUtils.isValid(resourceType)){
            return;
        }

        if(resourceType == Consts.StudyPathResourceType.RECORD_TYPE){
            Teacher teacher = getResourceVideoTeacher(resourceId);
            if(teacher!=null){
                commentElement.setTeacherId(teacher.getId());
                commentElement.setTeacherName(teacher.getName());
            }
        } else if(resourceType == Consts.StudyPathResourceType.LIVE_TYPE){
            List<ResourceLive> resourceLiveList = knowledgeResource.getResourceLiveByIdList(Lists.newArrayList(resourceId));
            if(CollectionUtils.isEmpty(resourceLiveList)){
                return;
            }
            ResourceLive resourceLive = resourceLiveList.get(0);
            Long teacherId = resourceLive.getTeacherId();
            commentElement.setTeacherId(teacherId);
            Teacher teacher = goodsResource.getTeacherById(teacherId);
            if(teacher!=null){
                commentElement.setTeacherName(teacher.getName());
            }
        }

        if(!IdUtils.isValid(commentElement.getTeacherId())){
            return;
        }

        commentElementMapper.updateById(commentElement);
    }

    public Teacher getResourceVideoTeacher(Long resourceId) {
        ResourceVideo resourceVideo = knowledgeResource.getReturnResourceVideoById(resourceId);
        if(resourceVideo==null){
            return null;
        }
        Long teacherId = resourceVideo.getTeacherId();
        return goodsResource.getTeacherById(teacherId);
    }


}
