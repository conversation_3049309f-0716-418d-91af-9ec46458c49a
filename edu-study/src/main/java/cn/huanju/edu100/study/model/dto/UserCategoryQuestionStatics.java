package cn.huanju.edu100.study.model.dto;

import cn.huanju.edu100.study.model.calculate.TikuLastChapterExercise;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: UserCategoryQuestionStatics
 * @Package cn.huanju.edu100.study.model.dto
 * @Description:
 * @company： 环球
 * @date 2021/8/2 3:21 下午
 */
@Data
public class UserCategoryQuestionStatics
{
    Integer questionTotal;//题目总数
    Integer userDoneQuestionTotal;//做过的题目数
    Long doneRate;//完成
    Integer todayDoneQuestionTotal;//当天做过的题目数
    Long todayMinuteTime;//当天的用时
    Integer todayRightQuestionTotal;//当天做对的题目数
    Long todayRightRate;//当天做对的正确率
    Long lastPracticeChapterId;//最后一次练习的章节id
    String lastPracticeTitle;//最后一次练习的题目
    Integer  lastPracticeQuestionTotal;//最后一次练习的题目总数
    Integer lastPracticeDoneTotal;//最后一次练习的已做题目总数
    Integer lastPracticeRightTotal;//最后一次练习的做题正确总数
    Long lastPracticeUseMinuteTime;//最后一次练习使用的时间
    Long lastPracticeRightRate;//正确率
    TikuLastChapterExercise tikuLastChapterExercise;
    Long bookItemRightRate;  //全本教材做题正确率
    Integer hasNewWrongQuestion;  //是否有新增错题
    Integer userWrongQuestionTotal; //错题总数
}
