package cn.huanju.edu100.study.model.onetoone;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 留学老师Entity
 * <AUTHOR>
 * @version 2016-09-09
 */
public class VTeacher extends DataEntity<VTeacher> {
	
	private static final long serialVersionUID = 1L;
	private Long uid;// 用户uid
	private String name;		// 老师名称
	private String number;		// 老师编号
	private String phone;		// 电话
	private Integer isFullTime;		// 是否全职，0：否，1：是
	private Integer isChinese;		// 中教or外教， 0：中教，1：外教
	private Long minLesson;		// 最小学时
	private String description;		// description
//	private Long schId;		// sch_id
	private String ip;		// ip
	
	public VTeacher() {
		super();
	}

	public VTeacher(Long id){
		super(id);
	}

	public String getName() {
		return name;
	}

	public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public void setName(String name) {
		this.name = name;
	}
	
	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}
	
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public Integer getIsFullTime() {
		return isFullTime;
	}

	public void setIsFullTime(Integer isFullTime) {
		this.isFullTime = isFullTime;
	}
	
	public Integer getIsChinese() {
		return isChinese;
	}

	public void setIsChinese(Integer isChinese) {
		this.isChinese = isChinese;
	}
	
	public Long getMinLesson() {
		return minLesson;
	}

	public void setMinLesson(Long minLesson) {
		this.minLesson = minLesson;
	}
	
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
	
//	public Long getSchId() {
//		return schId;
//	}
//
//	public void setSchId(Long schId) {
//		this.schId = schId;
//	}
	
	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}
	
}