package cn.huanju.edu100.study.entry.grpc;

import cn.huanju.edu100.grpc.annotation.GrpcService;
import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.server.RequestHandler;
import cn.huanju.edu100.grpc.service.StudyNoteServiceGrpc;
import cn.huanju.edu100.study.service.studynote.QuestionNoteService;
import cn.huanju.edu100.study.service.studynote.VideoNoteService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.hqwx.study.dto.query.UserNoteQuestionQuery;
import com.hqwx.study.entity.studynote.*;
import io.grpc.stub.StreamObserver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/21 18:46
 * @description
 */
@GrpcService
@Component
public class StudyNoteGrpcServiceImpl extends StudyNoteServiceGrpc.StudyNoteServiceImplBase {

    @Autowired
    private QuestionNoteService questionNoteService;

    @Autowired
    private VideoNoteService videoNoteService;

    RequestHandler requestHandler = new RequestHandler();

    @Override
    public void getQuestionNoteList(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> questionNoteService.getNoteList(param, QuestionNote.class),
                Query.QuestionQueryParam.class);
    }

    @Override
    public void getQuestionNotePage(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleMapRequest(request, responseObserver, (param) -> {
            var parser = requestHandler.getJsonParser();
            Integer start = Integer.parseInt(param.get("start").toString());
            Integer size = Integer.parseInt(param.get("size").toString());
            Query.QuestionQueryParam queryParam = null;
            try {
                queryParam = parser.parseEntity(
                        parser.toJson(param.get("param")),
                        Query.QuestionQueryParam.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            return questionNoteService.getNotePage(queryParam, start, size, QuestionNote.class);
        });
    }

    @Override
    public void getNotesByQuestionListId(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleMapRequest(request, responseObserver, (param) -> {
            Long uid = Long.parseLong(param.get("uid").toString());
            Integer includeReply= Integer.parseInt(param.get("includeReply").toString());
            List<Long> questionIdList = ((List<Object>) param.get("questionIdList"))
                    .stream().map(o -> Long.parseLong(o.toString())).toList();
            return questionNoteService.getNotesByQuestionListId(questionIdList, uid, includeReply);
        });
    }

    @Override
    public void findyQuestionNotePageByQuestionIdList(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleMapRequest(request, responseObserver, (param) -> {
            List<Long> questionIdList = ((List<Object>) param.get("questionIdList")).stream().map(o -> Long.parseLong(o.toString())).toList();
            Long uid = Long.parseLong(param.get("uid").toString());
            Integer includeReply= Integer.parseInt(param.get("includeReply").toString());
            Integer includeOthers= Integer.parseInt(param.get("includeOthers").toString());
            Integer from= Integer.parseInt(param.get("from").toString());
            Integer rows= Integer.parseInt(param.get("rows").toString());
            return questionNoteService.findyQuestionNotePageByQuestionIdList(questionIdList, uid, includeReply, includeOthers, from, rows);
        });
    }

    /**
     */
    @Override
    public void addQuestionNote(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> questionNoteService.addNote(param), QuestionNote.class);
    }

    /**
     */
    @Override
    public void updateQuestionNote(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> questionNoteService.updateNote(param), QuestionNote.class);
    }


    @Override
    public void getNoteQuestionList(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> questionNoteService.getNoteQuestionList(param), UserNoteQuestionQuery.class);
    }

    /**
     */
    @Override
    public void getVideoNoteList(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> videoNoteService.getNoteList(param, VideoNote.class),
                Query.VideoQueryParam.class);
    }

    @Override
    public void getVideoNotePage(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleMapRequest(request, responseObserver, (param) -> {
            var parser = requestHandler.getJsonParser();
            Integer start = Integer.parseInt(param.get("start").toString());
            Integer size = Integer.parseInt(param.get("size").toString());
            Query.VideoQueryParam queryParam = null;
            try {
                queryParam = parser.parseEntity(
                        parser.toJson(param.get("param")),
                        Query.VideoQueryParam.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            return videoNoteService.getNotePage(queryParam, start, size, VideoNote.class);
        });
    }

    /**
     */
    @Override
    public void addVideoNote(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> videoNoteService.addNote(param), VideoNote.class);
    }

    /**
     */
    @Override
    public void updateVideoNote(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> videoNoteService.updateNote(param), VideoNote.class);
    }

    /**
     */
    @Override
    public void deleteNoteById(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> videoNoteService.deleteNoteById(param), StudyNoteBaseInfo.class);
    }

    /**
     */
    @Override
    public void thumbUpNote(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                (param) -> videoNoteService.thumbUp(param), ThumbUpInfo.class);
    }
}
