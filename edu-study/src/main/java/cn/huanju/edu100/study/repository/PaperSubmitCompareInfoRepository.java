package cn.huanju.edu100.study.repository;

import cn.huanju.edu100.study.mapper.paper.PaperSubmitStatisticInfoMapper;
import cn.huanju.edu100.study.model.PaperSubmitStatisticInfo;
import cn.huanju.edu100.study.util.PaperSubmitStatisticRule;
import cn.huanju.edu100.util.JSONUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import redis.clients.jedis.params.SetParams;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;

import static cn.huanju.edu100.study.util.PaperSubmitStatisticRule.SAVED_KEY_PREFIX;
import static cn.huanju.edu100.study.util.PaperSubmitStatisticRule.SCORE_RANGE_COUNT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/3 14:29
 * @description Redis + MySQL 实现存储功能
 * 1. 读写直接操作 redis
 * 2. 读写时如果 redis 不存在试卷的缓存，则尝试中数据库中加载；数据库中没有的区间redis中也会增加 submitCount 为0的缓存
 * 3. 设置 redis 缓存超时为12个小时
 * 4. 当触发超时时，把 submitCount 大于0而且比较原来记录大的数据更新到数据库中
 */
@Slf4j
@Repository
public class PaperSubmitCompareInfoRepository {
    @Autowired
    JedisPool alMasterPool;

    @Autowired
    private PaperSubmitStatisticInfoMapper paperSubmitStatisticInfoMapper;

    private ExecutorService executor = Executors.newFixedThreadPool(10);

    /**
             * 缓存 24 个小时
             */
    private int EXPIRE_SECONDS = 60 * 60 * 24;

    /**
     * 封装做题数据 redis 的操作
     */
    class PaperRedis {
        Jedis jedis;
        Long paperId;
        Double paperTotalScore;

        PaperRedis(Jedis jedis, Long paperId) {
            this.jedis = jedis;
            this.paperId = paperId;
        }
        PaperRedis(Long paperId) {
            this.paperId = paperId;
        }
        void setPaperTotalScore(Double paperTotalScore) {
            this.paperTotalScore = paperTotalScore;
        }
        void setJedis(Jedis jedis) {
            this.jedis = jedis;
        }

        String submitCountKey(Integer scoreIndex) {
            return PaperSubmitStatisticRule.toKey(PaperSubmitStatisticRule.SUBMITCOUNT_KEY_PREFIX, paperId, scoreIndex);
        }
        String submitCountKeyPrefix(){
            return PaperSubmitStatisticRule.SUBMITCOUNT_KEY_PREFIX;
        }
        String submitCountKeyPattern() {
            return PaperSubmitStatisticRule.toKeyPattern(PaperSubmitStatisticRule.SUBMITCOUNT_KEY_PREFIX, paperId);
        }
        String totalScoreKey(Integer scoreIndex) {
            return PaperSubmitStatisticRule.toKey(PaperSubmitStatisticRule.SCORE_KEY_PREFIX, paperId, scoreIndex);
        }
        String totalScoreKeyPrefix(){
            return PaperSubmitStatisticRule.SCORE_KEY_PREFIX;
        }
        String totalScoreKeyPattern() {
            return PaperSubmitStatisticRule.toKeyPattern(PaperSubmitStatisticRule.SCORE_KEY_PREFIX, paperId);
        }

        String submitAccuracyCountKey(Integer accuracyIndex) {
            return PaperSubmitStatisticRule.toKey(PaperSubmitStatisticRule.SUBMITACCURACYCOUNT_KEY_PREFIX, paperId, accuracyIndex);
        }
        String submitAccuracyCountKeyPrefix(){
            return PaperSubmitStatisticRule.SUBMITACCURACYCOUNT_KEY_PREFIX;
        }
        String submitAccuracyCountKeyPattern() {
            return PaperSubmitStatisticRule.toKeyPattern(PaperSubmitStatisticRule.SUBMITACCURACYCOUNT_KEY_PREFIX, paperId);
        }
        String totalAccuracyKey(Integer accuracyIndex) {
            return PaperSubmitStatisticRule.toKey(PaperSubmitStatisticRule.ACCURACY_KEY_PREFIX, paperId, accuracyIndex);
        }
        String totalAccuracyKeyPrefix(){
            return PaperSubmitStatisticRule.ACCURACY_KEY_PREFIX;
        }
        String totalAccuracyKeyPattern() {
            return PaperSubmitStatisticRule.toKeyPattern(PaperSubmitStatisticRule.ACCURACY_KEY_PREFIX, paperId);
        }

        public void increment(Double score) {
            Integer scoreIndex = PaperSubmitStatisticInfo.score2Index(score, paperTotalScore);
            jedis.incr(submitCountKey(scoreIndex));
            jedis.incrByFloat(totalScoreKey(scoreIndex), score);
            expire(scoreIndex);
            // 数据有更新，放开保存限制
            releaseSave(scoreIndex);
        }

        public void incrementByPaperAccuracy(Double paperAccuracy) {
            Integer paperAccuracyIndex = PaperSubmitStatisticInfo.score2IndexByPaperAccuracy(paperAccuracy);
            jedis.incr(submitAccuracyCountKey(paperAccuracyIndex));
            jedis.incrByFloat(totalAccuracyKey(paperAccuracyIndex), paperAccuracy);
            expireByPaperAccuracy(paperAccuracyIndex);
            // 数据有更新，放开保存限制
            releaseSave(paperAccuracyIndex);
        }

        public void set(Integer scoreIndex, Long submitCount, Double totalScore, Long submitAccuracyCount, Double totalAccuracy) {
            jedis.set(submitCountKey(scoreIndex), submitCount.toString());
            jedis.set(totalScoreKey(scoreIndex), totalScore.toString());
            jedis.set(submitAccuracyCountKey(scoreIndex), submitAccuracyCount.toString());
            jedis.set(totalAccuracyKey(scoreIndex), totalAccuracy.toString());
            expire(scoreIndex);
            expireByPaperAccuracy(scoreIndex);
        }

        /**
         * 对指定分数区间设置超时
         * @param scoreIndex
         */
        public void expire(Integer scoreIndex) {
            jedis.expire(submitCountKey(scoreIndex), EXPIRE_SECONDS);
            jedis.expire(totalScoreKey(scoreIndex), EXPIRE_SECONDS);
        }

        public void expireByPaperAccuracy(Integer scoreIndex) {
            jedis.expire(submitAccuracyCountKey(scoreIndex), EXPIRE_SECONDS);
            jedis.expire(totalAccuracyKey(scoreIndex), EXPIRE_SECONDS);
        }

        public Boolean exists(Double score) {
            //Assert.assertTrue(paperTotalScore != null);
            if(paperTotalScore == null)
                throw new RuntimeException("paperTotalScore is null");
            return exists(PaperSubmitStatisticInfo.score2Index(score, paperTotalScore));
        }

        public Boolean existsByPaperAccuracy(Double paperAccuracy) {
            return existsByPaperAccuracy(PaperSubmitStatisticInfo.score2IndexByPaperAccuracy(paperAccuracy));
        }

        public Boolean exists(Integer scoreIndex) {
            return jedis.exists(submitCountKey(scoreIndex));
        }

        public Boolean existsByPaperAccuracy(Integer scoreIndex) {
            return jedis.exists(submitAccuracyCountKey(scoreIndex));
        }

        /**
         *  锁住保存
         * @param scoreIndex
         * @return
         */
        public Boolean lockSave(Integer scoreIndex) {
            String key = PaperSubmitStatisticRule.toKey(SAVED_KEY_PREFIX, paperId, scoreIndex);
            SetParams setParams = new SetParams();
            setParams.nx().ex(EXPIRE_SECONDS);
            String ok = jedis.set(key, "", setParams);
            if (ok != null) {
                // 创建成功，可以执行保存
                return true;
            } else {
                // 失败，不需要执行保存
                return false;
            }
        }
        public void releaseSave(Integer scoreIndex) {
            String key = PaperSubmitStatisticRule.toKey(SAVED_KEY_PREFIX, paperId, scoreIndex);
            jedis.del(key);
        }
    }

    /**
     * 根据试卷答题分数增加提交统计
     * @param paperId
     * @param score 100分标准化过的分数
     */
    public void increaseSubmitCount(Long paperId, Double score, Double paperTotalScore) {
        if(paperId == null || score == null || paperTotalScore == null){
            log.warn("increaseSubmitCount paperId:{}, score:{}, paperTotalScore:{}", paperId, score, paperTotalScore);
            return;
        }
        try(Jedis jedis = alMasterPool.getResource()) {
            PaperRedis paperRedis = new PaperRedis(jedis, paperId);
            paperRedis.setPaperTotalScore(paperTotalScore);
            if(!paperRedis.exists(score)) {
                cachePaper2Redis(paperId);
            }
            paperRedis.increment(score);
        }
    }

    /**
     * 根据试卷答题正确率增加提交统计
     */
    public void increaseSubmitAccuracyCount(Long paperId, Double paperAccuracy) {
        if(paperId == null || paperAccuracy == null){
            log.warn("increaseSubmitAccuracyCount paperId:{}, paperAccuracy:{}", paperId, paperAccuracy);
            return;
        }
        try(Jedis jedis = alMasterPool.getResource()) {
            PaperRedis paperRedis = new PaperRedis(jedis, paperId);
            if(!paperRedis.existsByPaperAccuracy(paperAccuracy)) {
                cachePaper2Redis(paperId);
            }
            paperRedis.incrementByPaperAccuracy(paperAccuracy);
        }
    }

    /**
     * 查询试卷所有区间的提交统计数据
     * @param paperId
     * @return
     */
    public Map<Integer, PaperSubmitStatisticInfo> getPaperScoreIndex2StatisticInfo(Long paperId) {
        Map<Integer, PaperSubmitStatisticInfo> scoreIndex2StatisticInfo = loadRedisCache(paperId);
        if(scoreIndex2StatisticInfo.size() != SCORE_RANGE_COUNT) {
            cachePaper2Redis(paperId);
            scoreIndex2StatisticInfo = loadRedisCache(paperId);
        }
        return scoreIndex2StatisticInfo;
    }

    public void save(PaperSubmitStatisticInfo paperSubmitStatisticInfo) {
        final String logTag = "SavePaperSubmitStatisticInfo-" + paperSubmitStatisticInfo.getPaperId() + "-" + paperSubmitStatisticInfo.getScoreIndex() + ":";
        log.info("{} data={}", logTag, JSONUtils.toJsonString(paperSubmitStatisticInfo));
        try(Jedis jedis = alMasterPool.getResource()) {
            PaperRedis paperRedis = new PaperRedis(jedis, paperSubmitStatisticInfo.getPaperId());
            if(!paperRedis.lockSave(paperSubmitStatisticInfo.getScoreIndex())) {
                // 上次保存后没再更新，不需要执行保存
                log.info("{} data not changed. ignore save.", logTag);
                return;
            }
        }
        try {
            // 先查询旧数据
            PaperSubmitStatisticInfo existed = null;
            {
                LambdaQueryWrapper<PaperSubmitStatisticInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(PaperSubmitStatisticInfo::getPaperId, paperSubmitStatisticInfo.getPaperId());
                lambdaQueryWrapper.eq(PaperSubmitStatisticInfo::getScoreIndex, paperSubmitStatisticInfo.getScoreIndex());
                existed = paperSubmitStatisticInfoMapper.selectOne(lambdaQueryWrapper);
            }
            if (existed != null){
                // 存在旧数据，并且旧数据的提交数小于缓存提交数，则更新
                if(existed.getSubmitCount() <= paperSubmitStatisticInfo.getSubmitCount() || existed.getSubmitAccuracyCount() <= paperSubmitStatisticInfo.getSubmitAccuracyCount()) {
                    paperSubmitStatisticInfo.setId(existed.getId());
                    paperSubmitStatisticInfoMapper.updateById(paperSubmitStatisticInfo);
                } else {
                    // 不需要更新
                }
            } else {
                // 不存在旧数据，直接插入
                paperSubmitStatisticInfoMapper.insert(paperSubmitStatisticInfo);
            }
        } catch (Exception e) {
            try(Jedis jedis = alMasterPool.getResource()) {
                // 异常，下次再可再保存，同时延迟数据过期时间，避免数据丢失
                PaperRedis paperRedis = new PaperRedis(jedis, paperSubmitStatisticInfo.getPaperId());
                paperRedis.releaseSave(paperSubmitStatisticInfo.getScoreIndex());
                paperRedis.expire(paperSubmitStatisticInfo.getScoreIndex());
            }
            log.error("{} exception: e={}, data={}", logTag, e, JSONUtils.toJsonString(paperSubmitStatisticInfo));
        }
        log.info("{} success!", logTag);
    }

    private Set<String> scanKeys(Jedis jedis, String pattern) {
        Set<String> keys = new HashSet<>();
        String cursor = "0";
        ScanParams params = new ScanParams().match(pattern).count(1000);
        do {
            ScanResult<String> result = jedis.scan(cursor, params);
            keys.addAll(result.getResult());
            cursor = result.getCursor();
        } while (!cursor.equals("0"));
        return keys;
    }

    /**
     * 从 redis 中把指定 key 的数据加载到 scoreIndex2StatisticInfo 中
     * @param scoreIndex2StatisticInfo
     * @param jedis
     * @param keyPattern
     * @param setValueConsumer
     */
    private void loadFromRedisKeyPattern(Map<Integer, PaperSubmitStatisticInfo> scoreIndex2StatisticInfo,
                                         Jedis jedis, String keyPrefix, Long paperId,
                                         BiConsumer<PaperSubmitStatisticInfo, String> setValueConsumer) {
//        Set<String> keySet = scanKeys(jedis,keyPattern);//jedis.keys(keyPattern);
//        if(CollectionUtils.isEmpty(keySet)) {
//            return;
//        }
        List<String> keys = new ArrayList<>();
        for(int scoreIndex = 0; scoreIndex < SCORE_RANGE_COUNT; ++scoreIndex) {
            keys.add(PaperSubmitStatisticRule.toKey(keyPrefix, paperId, scoreIndex));
        }
        List<String> values = jedis.mget(keys.toArray(new String[0]));
        for(int i = 0; i < keys.size(); ++i) {
            String key = keys.get(i);
            String value = values.get(i);
            if (Objects.nonNull(value)) {
                PaperSubmitStatisticInfo paperSubmitStatisticInfo = PaperSubmitStatisticRule.parseKey(key);
                paperSubmitStatisticInfo = scoreIndex2StatisticInfo.getOrDefault(paperSubmitStatisticInfo.getScoreIndex(), paperSubmitStatisticInfo);

                paperSubmitStatisticInfo.ensureNonNullFields();

                setValueConsumer.accept(paperSubmitStatisticInfo, value);

                scoreIndex2StatisticInfo.put(paperSubmitStatisticInfo.getScoreIndex(), paperSubmitStatisticInfo);
            }
        }
    }
    private Map<Integer, PaperSubmitStatisticInfo> loadRedisCache(Long paperId) {
        Map<Integer, PaperSubmitStatisticInfo> scoreIndex2StatisticInfo = new ConcurrentHashMap<>(SCORE_RANGE_COUNT);

        // 加载提交统计数
        CompletableFuture<Void> submitCountFuture = CompletableFuture.runAsync(() -> {
            try (Jedis jedis = alMasterPool.getResource()) {
                PaperRedis paperRedis = new PaperRedis(jedis, paperId);
                loadFromRedisKeyPattern(scoreIndex2StatisticInfo, jedis, paperRedis.submitCountKeyPrefix(), paperId,
                        (paperSubmitStatisticInfo, value) -> paperSubmitStatisticInfo.setSubmitCount(Long.valueOf(value)));
            }
        }, executor);

        // 加载总分
        CompletableFuture<Void> totalScoreFuture = CompletableFuture.runAsync(() -> {
            try (Jedis jedis = alMasterPool.getResource()) {
                PaperRedis paperRedis = new PaperRedis(jedis, paperId);
                loadFromRedisKeyPattern(scoreIndex2StatisticInfo, jedis, paperRedis.totalScoreKeyPrefix(), paperId,
                        (paperSubmitStatisticInfo, value) -> paperSubmitStatisticInfo.setTotalScore(Double.valueOf(value)));
            }
        }, executor);

        // 加载正确率提交统计数
        CompletableFuture<Void> submitAccuracyCountFuture = CompletableFuture.runAsync(() -> {
            try (Jedis jedis = alMasterPool.getResource()) {
                PaperRedis paperRedis = new PaperRedis(jedis, paperId);
                loadFromRedisKeyPattern(scoreIndex2StatisticInfo, jedis, paperRedis.submitAccuracyCountKeyPrefix(), paperId,
                        (paperSubmitStatisticInfo, value) -> paperSubmitStatisticInfo.setSubmitAccuracyCount(Long.valueOf(value)));
            }
        }, executor);

        // 加载总正确率
        CompletableFuture<Void> totalAccuracyFuture = CompletableFuture.runAsync(() -> {
            try (Jedis jedis = alMasterPool.getResource()) {
                PaperRedis paperRedis = new PaperRedis(jedis, paperId);
                loadFromRedisKeyPattern(scoreIndex2StatisticInfo, jedis, paperRedis.totalAccuracyKeyPrefix(), paperId,
                        (paperSubmitStatisticInfo, value) -> paperSubmitStatisticInfo.setTotalAccuracy(Double.valueOf(value)));
            }
        }, executor);

        // 等待所有的异步操作完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(
                submitCountFuture,
                totalScoreFuture,
                submitAccuracyCountFuture,
                totalAccuracyFuture
        );
        allOf.join();
        return scoreIndex2StatisticInfo;
    }

    /**
     * 从数据库中加载后缓存到redis中，只加载redis中缺失的分数区间，不需要加载试卷所有区间的数据
     * 加载时需要注意，如果redis中还存在，redis中的统计的数据库中的统计的数据要更新，不要覆盖了
     * @param paperId
     */
    private void cachePaper2Redis(Long paperId) {
        // 加载 redis 已存在的数据
        Map<Integer, PaperSubmitStatisticInfo> scoreIndex2StatisticInfoFromRedis = loadRedisCache(paperId);
        // 保存数据库或者 redis 中存在的 scoreIndex，用于在缓存到redis后判断需要设置默认值的 scoreIndex
        Set<Integer> existsIndexSet = new HashSet<>(scoreIndex2StatisticInfoFromRedis.keySet());

        List<PaperSubmitStatisticInfo> list = queryFromDb(paperId);
        try(Jedis jedis = alMasterPool.getResource()) {
            // 缓存数据库或者redis存在的数据
            PaperRedis paperRedis = new PaperRedis(jedis, paperId);
            for (var info : list) {
                if (!existsIndexSet.contains(info.getScoreIndex())) {
                    paperRedis.set(info.getScoreIndex(), info.getSubmitCount(), info.getTotalScore(), info.getSubmitAccuracyCount(), info.getTotalAccuracy());
                    existsIndexSet.add(info.getScoreIndex());
                }
            }

            // 缓存数据库和 redis 中都不存在的key
            for(Integer scoreIndex = 0; scoreIndex < SCORE_RANGE_COUNT; ++scoreIndex) {
                if(existsIndexSet.contains(scoreIndex)) {
                    continue;
                }
                paperRedis.set(scoreIndex, 0L, 0.0, 0L, 0.0);
            }
        }
    }

    /**
     * 查询试卷的所有区间统计记录
     * @param paperId
     * @return
     */
    private List<PaperSubmitStatisticInfo> queryFromDb(Long paperId) {
        LambdaQueryWrapper<PaperSubmitStatisticInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(PaperSubmitStatisticInfo::getPaperId, paperId);
        return paperSubmitStatisticInfoMapper.selectList(lambdaQueryWrapper);
    }
}
