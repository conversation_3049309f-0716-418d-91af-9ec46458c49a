package cn.huanju.edu100.study.service.onetoone;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.QueryParam;
import cn.huanju.edu100.study.model.onetoone.VClassesStudent;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 面授学员班级Service
 * <AUTHOR>
 * @version 2016-04-12
 */
public interface VClassesStudentService extends BaseService<VClassesStudent> {

    List<VClassesStudent> findListByQueryParam(QueryParam param) throws DataAccessException;

    List<VClassesStudent> findUnfinishByQueryParam(QueryParam param) throws DataAccessException;

    /**
     * 验证用户是否有权限进入教室
     *
     * @param clsId
     * @param uid
     * @return
     * @throws DataAccessException
     */
    boolean validateEntryByClsIdAndUid(Long clsId, Long uid) throws DataAccessException;

}
