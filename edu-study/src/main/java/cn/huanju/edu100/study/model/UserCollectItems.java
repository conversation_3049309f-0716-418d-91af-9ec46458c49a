package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;


/**
 * 用户收藏的物品Entity
 * 
 * <AUTHOR>
 * @version 2015-09-02
 */
public class UserCollectItems extends DataEntity<UserCollectItems> {

	private static final long serialVersionUID = 1L;
	transient public int tbidx = 0;//分表
	
	private Long uid; // uid
	private Long itemId; // item_id
	private Integer itemType; // item_type 物品类型,(0:题目，1:试卷)
	private Long sourceId; // source_id 归属id（普通收藏，默认归属id为0 ）
	private List<Long> sourceIds;
	private Integer sourceType; // source_type 归属类型(0:普通收藏，1:题库收藏，2:云私塾的收藏)

	private Long productId;	// 产品id
	private Long goodsId;// 商品id

	transient private List<Long> itemIds; // item_id 列表

	public List<Long> getSourceIds() {
		return sourceIds;
	}

	public void setSourceIds(List<Long> sourceIds) {
		this.sourceIds = sourceIds;
	}

	public int getTbidx() {
		return tbidx;
	}
	public void setTbidx(int tbidx) {
		this.tbidx = tbidx;
	}
	public Long getUid() {
		return uid;
	}
	public void setUid(Long uid) {
		this.uid = uid;
	}
	public Long getItemId() {
		return itemId;
	}
	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}
	public Integer getItemType() {
		return itemType;
	}
	public void setItemType(Integer itemType) {
		this.itemType = itemType;
	}
	public Long getSourceId() {
		return sourceId;
	}
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}
	public Integer getSourceType() {
		return sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}
	public Long getProductId() {
		return productId;
	}
	public void setProductId(Long productId) {
		this.productId = productId;
	}
	public Long getGoodsId() {
		return goodsId;
	}
	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}

	public List<Long> getItemIds() {
		return itemIds;
	}

	public void setItemIds(List<Long> itemIds) {
		this.itemIds = itemIds;
	}
}