/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentTask;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 学生任务DAO接口
 *
 * <AUTHOR>
 * @version 2016-01-18
 */
public interface TutorStudentTaskDao extends CrudDao<TutorStudentTask> {

    List<TutorStudentTask> getTutorStudentTaskByTaskList(Long uid, List<Long> taskIdList, Integer source)
            throws DataAccessException;

    boolean updateByUidAndTaskId(TutorStudentTask tutorStudentTask) throws DataAccessException;

    /**
     * 只查询一条记录
     *
     * @param tid
     *            任务id
     * @param uid
     *            用户id
     * @param source
     * @return
     * @throws DataAccessException
     */
    TutorStudentTask findLimitOneByTidAndUid(Long tid, Long uid, Integer source) throws DataAccessException;

    TutorStudentTask getLastTask(Long uid, Long unitId, Long phaseId, Long categoryId, Integer source) throws DataAccessException;

    Map<Long, Integer> getUnitTaskNum(List<Long> unitIdList, Long uid, Integer source) throws  DataAccessException;
}
