package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.VLessonDao;
import cn.huanju.edu100.study.model.onetoone.VLesson;
import cn.huanju.edu100.study.service.onetoone.VLessonService;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 面授课节Service
 *
 * <AUTHOR>
 * @version 2016-04-12
 */
@Service
public class VLessonServiceImpl extends BaseServiceImpl<VLessonDao, VLesson> implements VLessonService {

    @Override
    public List<VLesson> findListByParam(List<Long> clsIds, Long teacherUid, Date startTime, Date endTime, List<Long> schIds) throws DataAccessException {

        if (CollectionUtils.isEmpty(clsIds) && !IdUtils.isValid(teacherUid)) {
            logger.error("findListByParam fail, empty clsIds and teacherUid is null");
            throw new DataAccessException("findListByParam fail, empty clsIds and taecherUid is null");
       }
        return dao.findListByParam(clsIds, teacherUid, startTime, endTime, schIds);
    }

    @Override
    public Double getPeriodCount(Long clsId) throws DataAccessException {

        if (!IdUtils.isValid(clsId)) {
            logger.error("getLessonCount fail, illegal param clsId");
            return 0d;
        }

        VLesson vLesson = new VLesson();
        vLesson.setClsId(clsId);
        return dao.getPeriodCount(vLesson);
    }

    @Override
    public List<VLesson> findListByParam(Map<String, Object> param) throws DataAccessException {
        if (param == null || param.get("roomIdList") == null || param.get("startTime") == null
                || param.get("endTime") == null) {
            logger.error("findListByParam fail, roomIdList or startTime or endTime is null");
            throw new DataAccessException("findListByParam fail, roomIdList or startTime or endTime is null");
       }
        return dao.findListByParam(param);
    }

}
