package cn.huanju.edu100.study.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("exemplary_analogous_question")
@InterceptorIgnore(tenantLine = "1")
public class ExemplaryAnalogousQuestionPO {

    // id
    @TableId(type = IdType.AUTO)
    private Long id;

    // 科目id
    private Long categoryId;

    // 产品id
    private Long productId;

    // 错题id
    private Long errorQuestionId;

    // 推荐题目id集合
    private String recommendedQuestionIds;

    // 抽取题目信息
    private String generateInfo;

    // 创建时间
    private Date createTime;
}