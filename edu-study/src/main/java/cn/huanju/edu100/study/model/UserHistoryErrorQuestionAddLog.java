package cn.huanju.edu100.study.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * CREATE TABLE `hq_study_test`.`user_history_error_question_add_log` (
 *      `id` bigint(20) NOT NULL AUTO_INCREMENT,
 *      `uid` bigint(20) NOT NULL COMMENT '用户id',
 *      `goods_id` bigint(20) DEFAULT NULL COMMENT '商品id',
 *      `category_id` bigint(20) DEFAULT NULL COMMENT '科目id',
 *      `count` int(11) NOT NULL DEFAULT '0' COMMENT '错题数量',
 *      `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态 0-未迁移 1-已迁移',
 *      `create_date` datetime NOT NULL,
 *      `update_date` datetime NOT NULL,
 *      PRIMARY KEY (`id`),
 *      KEY `uid_idx` (`uid`)
 * ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='用户历史错题记录迁移表';
 */
@Data
@TableName("user_history_error_question_add_log")
@InterceptorIgnore(tenantLine = "1")
public class UserHistoryErrorQuestionAddLog {
    @TableId(type= IdType.AUTO)
    private Long id;
    private Long uid;
    private Long goodsId;
    private Long categoryId;
    private Integer count;
    // 0-未迁移 1-已迁移
    private Integer status;
    private String questions;
    private Date createDate;
    private Date updateDate;
}
