package cn.huanju.edu100.study.model.al;


import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version
 * @date 2019-07-25
 */
public class StudyPathQuestion extends DataEntity<StudyPathQuestion> {
    private static final long serialVersionUID = 1L;
    /**
     * 学习路径id
     */
    private Long pathId;

    /**
     * 题目id
     */
    private Long questionId;

    /**
     * 排序
     */
    private Integer sort;

    //查询条件
    private List<Long> pathIdList;

    public Long getPathId() {
        return pathId;
    }

    public void setPathId(Long pathId) {
        this.pathId = pathId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public List<Long> getPathIdList() {
        return pathIdList;
    }

    public void setPathIdList(List<Long> pathIdList) {
        this.pathIdList = pathIdList;
    }
}