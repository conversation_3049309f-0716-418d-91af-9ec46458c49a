package cn.huanju.edu100.study.resource.feigncall;

import cn.huanju.edu100.study.resource.feigncall.dto.AIResponse;
import cn.huanju.edu100.study.resource.feigncall.dto.AssistantExecuteRequestDto;
import cn.huanju.edu100.study.resource.feigncall.dto.InputItems;
import cn.huanju.edu100.study.resource.feigncall.dto.OutputModelDto;
import com.logaritex.ai.api.Data;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

//@FeignClient(name = "chatgpt",url = "http://***********:10049/") //prod
@FeignClient(name = "chatgpt")
public interface AIApiFeign {

    /**
     * 非流式问答接口
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/user/chatgpt", method = RequestMethod.POST,consumes = "application/json")
    AIResponse getAnswerFromChatGPT(@RequestBody InputItems body);
    /**
     * 清除用户历史问题记录缓存
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/user/chatgpt/clear", method = RequestMethod.POST,consumes = "application/json")
    AIResponse clearAnswerHistory(@RequestBody InputItems body);



    /**
     * gpt上传文件
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/file-config/upload-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    OutputModelDto<Data.File> uploadFile(@RequestPart("file") MultipartFile file);

    /**
     * gpt-assistant 执行接口
     *
     * @param assistantRequest
     * @return
     */
    @RequestMapping(value = "/assistants/execute", method = RequestMethod.POST)
    OutputModelDto<Data.DataList<Data.Message>> execute(@RequestBody AssistantExecuteRequestDto assistantRequest);


}
