package cn.huanju.edu100.study.entry.thrift;


import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.homework.comment.UserHomeworkComment;
import cn.huanju.edu100.study.model.homework.task.UserHomeworkTask;
import cn.huanju.edu100.study.service.homework.task.UserHomeworkTaskService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.hqwx.study.dto.UserHomeworkCommentDTO;
import com.hqwx.study.dto.UserHomeworkMaxStateTaskDTO;
import com.hqwx.study.dto.UserHomeworkTaskDTO;
import com.hqwx.study.dto.query.UserHomeworkCommentQuery;
import com.hqwx.study.dto.query.UserHomeworkMaxStateTaskQuery;
import com.hqwx.study.dto.query.UserHomeworkTaskQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class UserHomeworkTaskThriftImpl extends AbstractServiceThrift{

    private static Logger logger = LoggerFactory.getLogger(UserHomeworkTaskThriftImpl.class);

    private static Gson gson = GsonUtil.getGenericGson();


    @Resource
    private UserHomeworkTaskService userHomeworkTaskService;

    public response sty_getUserHomeworkTaskPage(request req) throws BusinessException {
        String entry = "sty_getUserHomeworkTaskPage";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            IPage<UserHomeworkTaskDTO> result = new Page<>();
            UserHomeworkTaskQuery userHomeworkTaskQuery = gson.fromJson(req.getMsg(), UserHomeworkTaskQuery.class);
            if (userHomeworkTaskQuery == null || userHomeworkTaskQuery.getGoodsId() == null) {
                logger.error("{} fail.parameter goodsId is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter  goodsId or lessonId is null.");
            }
            IPage<UserHomeworkTask> userHomeworkTaskPage = userHomeworkTaskService.getUserHomeworkTaskPage(userHomeworkTaskQuery);
            BeanUtils.copyProperties(userHomeworkTaskPage, result);
            List<UserHomeworkTask> userHomeworkTaskList = userHomeworkTaskPage.getRecords();
            result.setRecords(getUserHomeworkTaskDtoList(userHomeworkTaskList));
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getUserHomeworkTaskList(request req) throws BusinessException {
        String entry = "sty_getUserHomeworkTaskList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserHomeworkTaskQuery userHomeworkTaskQuery = gson.fromJson(req.getMsg(), UserHomeworkTaskQuery.class);
            if (userHomeworkTaskQuery == null || userHomeworkTaskQuery.getGoodsId() == null) {
                logger.error("{} fail.parameter goodsId is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter  goodsId or lessonId is null.");
            }
            List<UserHomeworkTask> userHomeworkTaskList = userHomeworkTaskService.getUserHomeworkTaskList(userHomeworkTaskQuery);
            List<UserHomeworkTaskDTO> result =  getUserHomeworkTaskDtoList(userHomeworkTaskList);
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    private List<UserHomeworkTaskDTO> getUserHomeworkTaskDtoList(List<UserHomeworkTask> userHomeworkTaskList) {
        List<UserHomeworkTaskDTO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userHomeworkTaskList)){
            for (UserHomeworkTask userHomeworkTask : userHomeworkTaskList){
                UserHomeworkTaskDTO userHomeworkTaskDTO = new UserHomeworkTaskDTO();
                BeanUtils.copyProperties(userHomeworkTask, userHomeworkTaskDTO);
                if (userHomeworkTask.getCommentTime() != null){
                    userHomeworkTaskDTO.setCommentTime(userHomeworkTask.getCommentTime().getTime());
                }
                if (userHomeworkTask.getAnswerTime() != null){
                    userHomeworkTaskDTO.setAnswerTime(userHomeworkTask.getAnswerTime().getTime());
                }
                if (userHomeworkTask.getStatus() == null){
                    userHomeworkTaskDTO.setStatus(Consts.UserHomeworkTaskStatus.UN_COMMIT);
                }
                result.add(userHomeworkTaskDTO);
            }
        }
        return result;
    }

    public response sty_getMaxStateUserHomeworkTaskList(request req) throws BusinessException{
        String entry = "sty_getMaxStateUserHomeworkTaskList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserHomeworkMaxStateTaskQuery userHomeworkMaxStateTaskQuery = gson.fromJson(req.getMsg(), UserHomeworkMaxStateTaskQuery.class);
            if (userHomeworkMaxStateTaskQuery == null || userHomeworkMaxStateTaskQuery.getUid()==null) {
                logger.error("{} fail.parameter  uid is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter uid  is null.");
            }
            List<UserHomeworkMaxStateTaskDTO> result = userHomeworkTaskService.getMaxStateUserHomeworkTaskList(userHomeworkMaxStateTaskQuery);
            res.code = Constants.SUCCESS;
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getCountSubmitCustomHomework(request req) throws BusinessException {
        String entry = "sty_getCountSubmitCustomHomework";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserHomeworkTaskQuery userHomeworkTaskQuery = gson.fromJson(req.getMsg(), UserHomeworkTaskQuery.class);
            if (userHomeworkTaskQuery == null || userHomeworkTaskQuery.getHomeworkId() == null) {
                logger.error("{} fail.param homeworkId is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "param homeworkId is null.");
            }
            Integer rs = userHomeworkTaskService.getCountSubmitCustomHomework(userHomeworkTaskQuery);
            if (rs == null) {
                rs = 0;
            }
            res.code = Constants.SUCCESS;
            res.setMsg(rs.toString());
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

}
