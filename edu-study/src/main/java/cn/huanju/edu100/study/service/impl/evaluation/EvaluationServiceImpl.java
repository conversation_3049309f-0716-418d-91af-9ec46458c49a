package cn.huanju.edu100.study.service.impl.evaluation;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserAnswerDao;
import cn.huanju.edu100.study.dao.evaluation.EvaluationUserAnswerDao;
import cn.huanju.edu100.study.dao.evaluation.PaperAssessmentScoreDao;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.al.ProductAdaptiveLearning;
import cn.huanju.edu100.study.model.evaluation.EvaluationUserAnswer;
import cn.huanju.edu100.study.model.evaluation.PaperAssessmentScore;
import cn.huanju.edu100.study.model.goods.Goods;
import cn.huanju.edu100.study.model.goods.Product;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.resource.StustampResource;
import cn.huanju.edu100.study.service.UserAnswerSumService;
import cn.huanju.edu100.study.service.evaluation.EvaluationService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.stustamp.dto.UserViewProductUpdateDTO;
import cn.huanju.edu100.thrift.client.dto.KnowledgeQuestionListDTO;
import com.google.common.collect.Lists;
import com.hqwx.study.dto.EvaluationInfoDTO;
import com.hqwx.study.dto.EvaluationReportDTO;
import com.hqwx.study.dto.KnowledgeQuestionReportDTO;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EvaluationServiceImpl implements EvaluationService {

    private static final Logger log = LoggerFactory.getLogger(EvaluationServiceImpl.class);

    private static final int PAGE_SIZE = 60;

    @Autowired
    private GoodsResource goodsResource;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private StustampResource stustampResource;

    @Autowired
    private EvaluationUserAnswerDao evaluationUserAnswerDao;

    @Autowired
    private UserAnswerDao userAnswerDao;

    @Autowired
    private PaperAssessmentScoreDao paperAssessmentScoreDao;

    @Autowired
    private UserAnswerSumService userAnswerSumService;

    @Override
    public EvaluationInfoDTO isShowEvaluationEntryAndResult(Long uid, Long goodsId,List<Long> goodsIds) throws DataAccessException {
        EvaluationInfoDTO evaluationInfoDTO=new EvaluationInfoDTO();
        if(CollectionUtils.isEmpty(goodsIds)){
            return evaluationInfoDTO;
        }
        List<Goods> allGoodsList=new ArrayList<>();
        int pageNum = 1;
        while (true) {
            int pageIndex = pageNum++;
            List<Long> goodsIdList = goodsIds.stream().skip((pageIndex - 1) * PAGE_SIZE).limit(PAGE_SIZE).collect(Collectors.toList());
            if (goodsIdList.size() == 0) {
                break;
            }
            //每次只能查60个商品ID
            List<Goods> goodsList=goodsResource.getGoodsListByIdList(goodsIdList);
            allGoodsList.addAll(goodsList);
        }
        if(CollectionUtils.isEmpty(allGoodsList)){
            return evaluationInfoDTO;
        }
        Goods goods=allGoodsList.stream().filter(g->g.getId().equals(goodsId)).findFirst().orElse(null);
        if(goods==null){
            return evaluationInfoDTO;
        }
        if(goods.getEvaluationState()==null){
            //如果当前商品没有入学测评标识，直接返回
            return evaluationInfoDTO;
        }
        if(goods.getEvaluationState()==0){
            //如果当前商品没有入学测评标识，直接返回
            return evaluationInfoDTO;
        }

        Integer showEntryTab=Consts.ShowEntryTab.noShowEntryAndResult;//入口标识(默认不显示测评入口和结果)
        Integer isPop=Consts.PopType.noPop;//弹窗标识（默认不弹窗）
        evaluationInfoDTO.setSecondCategory(goods.getSecondCategory());
        evaluationInfoDTO.setEvaluationPaperId(goods.getEvaluationPaperId());
        //判断当前商品是否含有私教班产品
        Boolean isPrivateClassGoods=this.isPrivateClassGoods(goods);
        if(isPrivateClassGoods){
            //如果当前商品是包含私教班产品，则判断当前商品最新的入学测评是否作答过
            List<EvaluationUserAnswer> currentGoodsEvaluationUserAnswers=this.getEvaluationUserAnswerList(uid,Lists.newArrayList(goods.getId()));
            if(CollectionUtils.isNotEmpty(currentGoodsEvaluationUserAnswers)){
                showEntryTab=Consts.ShowEntryTab.showResult;//显示入学测评结果入口
            }else{
                showEntryTab=Consts.ShowEntryTab.showEntry;
            }
            Integer type=4;//入学测评弹窗记录保存标识
            isPop=this.getIsPop(uid,goodsId,type,showEntryTab);
            evaluationInfoDTO.setShowEntryTab(showEntryTab);
            evaluationInfoDTO.setIsPop(isPop);
            return evaluationInfoDTO;
        }
        //当前商品所属等级（高端课、中低端课、体验课）
        int currentGoodsLevel=this.getCurrentGoodsLevel(goods);
        /*
         (非私教班商品)
         若同一用户，在同一考试下，任意商品中提交了入学测评，则该商品所属课程类型的其他商品，不再展示入学测评入口；若该课程类型中学员没有提交记录，则所有配置了入学测评的商品展示入口。
         测评课程类型排序依次为：【高端课】>【中低端课】>【体验课】
         若用户再该考试下提交了【高端课】的测评，则向下不需要再填写【中低端课】和【体验课】；向上不适用此规则。
         若用户再该考试下提交了【体验课】的测评，则【体验课】的其他测评无需再次回答，但【高端课】和【中低端课】的测评依然需要填写。
         */
        //高端课对应：商品分类为2高端课
        Integer[] highGoodsTypes={Consts.GOODS_CATEGORY.highGoods};
        List<Integer> highGoodsTypeList=Arrays.asList(highGoodsTypes);
        List<Long> highGoodsIdList=allGoodsList.stream().filter(s->highGoodsTypeList.contains(s.getGoodsCategory()==null?-1:s.getGoodsCategory().intValue())).map(Goods::getId).collect(Collectors.toList());
        if(highGoodsIdList==null){
            highGoodsIdList=new ArrayList<>();
        }
        //获取高端商品的入学测评作答记录
        List<EvaluationUserAnswer> highGoodsEvaluationUserAnswers=this.getEvaluationUserAnswerList(uid,highGoodsIdList);
        if(CollectionUtils.isNotEmpty(highGoodsEvaluationUserAnswers)){
            List<Long> highGoodsIds=highGoodsEvaluationUserAnswers.stream().map(a->a.getGoodsId()).distinct().collect(Collectors.toList());
            if(highGoodsIds.contains(goods.getId())){
                showEntryTab=this.getCurrentGoodsShowEntryTab(uid,goods,currentGoodsLevel);
                evaluationInfoDTO.setShowEntryTab(showEntryTab);
                return evaluationInfoDTO;
            }
        }
        //中低端课对应：商品分类为1标准课、6正式课、8阅读课
        Integer[] mediumLowGoodsTypes={Consts.GOODS_CATEGORY.standardGoods,Consts.GOODS_CATEGORY.formalGoods,Consts.GOODS_CATEGORY.readGoods};
        List<Integer> mediumLowGoodsTypeList=Arrays.asList(mediumLowGoodsTypes);
        List<Long> mediumLowGoodsIdList=allGoodsList.stream().filter(s->mediumLowGoodsTypeList.contains(s.getGoodsCategory()==null?-1:s.getGoodsCategory().intValue())).map(Goods::getId).collect(Collectors.toList());
        if(mediumLowGoodsIdList==null){
            mediumLowGoodsIdList=new ArrayList<>();
        }
        //获取中低端商品的入学测评作答记录
        List<EvaluationUserAnswer> mediumLowGoodsEvaluationUserAnswers=this.getEvaluationUserAnswerList(uid,mediumLowGoodsIdList);

        if(CollectionUtils.isNotEmpty(mediumLowGoodsEvaluationUserAnswers) && !highGoodsIdList.contains(goodsId)){
            List<Long> mediumLowGoodsIds=mediumLowGoodsEvaluationUserAnswers.stream().map(a->a.getGoodsId()).distinct().collect(Collectors.toList());
            if(mediumLowGoodsIds.contains(goods.getId())){
                showEntryTab=this.getCurrentGoodsShowEntryTab(uid,goods,currentGoodsLevel);
                evaluationInfoDTO.setShowEntryTab(showEntryTab);
                return evaluationInfoDTO;
            }
        }
        //体验课对应：商品分类为3体验课、0公开课、7资料包
        Integer[] experienceGoodsTypes={Consts.GOODS_CATEGORY.experienceGoods,Consts.GOODS_CATEGORY.publicGoods,Consts.GOODS_CATEGORY.materialGoods};
        List<Integer> experienceGoodsTypeList=Arrays.asList(experienceGoodsTypes);
        List<Long> experienceGoodsIdList=allGoodsList.stream().filter(s->experienceGoodsTypeList.contains(s.getGoodsCategory()==null?-1:s.getGoodsCategory().intValue())).map(Goods::getId).collect(Collectors.toList());
        if(experienceGoodsIdList==null){
            experienceGoodsIdList=new ArrayList<>();
        }
        //获取体验商品的入学测评作答记录
        List<EvaluationUserAnswer> experienceGoodsEvaluationUserAnswers=this.getEvaluationUserAnswerList(uid,experienceGoodsIdList);
        if(CollectionUtils.isNotEmpty(experienceGoodsEvaluationUserAnswers) && experienceGoodsIdList.contains(goodsId)){
            List<Long> experienceGoodsIds=experienceGoodsEvaluationUserAnswers.stream().map(a->a.getGoodsId()).distinct().collect(Collectors.toList());
            if(experienceGoodsIds.contains(goods.getId())){
                showEntryTab=this.getCurrentGoodsShowEntryTab(uid,goods,currentGoodsLevel);
                evaluationInfoDTO.setShowEntryTab(showEntryTab);
                return evaluationInfoDTO;
            }
        }

        if(goods.getEvaluationState()==Consts.EvaluationState.opened){
            showEntryTab=Consts.ShowEntryTab.showEntry;
        }
        Integer type=4;//入学测评弹窗记录保存标识
        isPop=this.getIsPop(uid,goodsId,type,showEntryTab);
        evaluationInfoDTO.setShowEntryTab(showEntryTab);
        evaluationInfoDTO.setIsPop(isPop);
        return evaluationInfoDTO;
    }


    private int getCurrentGoodsShowEntryTab(Long uid,Goods goods,int currentGoodsLevel) throws DataAccessException{
        int showEntryTab=Consts.ShowEntryTab.noShowEntryAndResult;
        if(currentGoodsLevel==Consts.goodsLevel.high){
            //如果当前商品属于高端商品，则获取当前商品的专业测评作答记录
            List<EvaluationUserAnswer> currentHighGoodsPaperEvaluationUserAnswers=this.getPaperEvaluationUserAnswerList(uid,Lists.newArrayList(goods.getId()));
            if(CollectionUtils.isNotEmpty(currentHighGoodsPaperEvaluationUserAnswers)){
                //如果当前商品在高端商品的专业测评作答记录中存在，则显示入学结果入口
                showEntryTab=Consts.ShowEntryTab.showResult;//显示入学测评结果入口
            }
        }
        if(currentGoodsLevel==Consts.goodsLevel.mediumLow){
            List<EvaluationUserAnswer> currentGoodsEvaluationUserAnswers=this.getPaperEvaluationUserAnswerList(uid,Lists.newArrayList(goods.getId()));
            if(CollectionUtils.isNotEmpty(currentGoodsEvaluationUserAnswers)){
                showEntryTab=Consts.ShowEntryTab.showResult;//显示入学测评结果入口
            }
        }
        if(currentGoodsLevel==Consts.goodsLevel.experience){
            List<EvaluationUserAnswer> currentGoodsEvaluationUserAnswers=this.getPaperEvaluationUserAnswerList(uid,Lists.newArrayList(goods.getId()));
            if(CollectionUtils.isNotEmpty(currentGoodsEvaluationUserAnswers)){
                showEntryTab=Consts.ShowEntryTab.showResult;//显示入学测评结果入口
            }
        }
        return showEntryTab;
    }

    private int getCurrentGoodsLevel(Goods goods){
        int goodsLevel=1;
        //高端课对应：商品分类为2高端课
        Integer[] highGoodsTypes={Consts.GOODS_CATEGORY.highGoods};
        List<Integer> highGoodsTypeList=Arrays.asList(highGoodsTypes);
        if(highGoodsTypeList.contains(goods.getGoodsCategory())){
            goodsLevel=Consts.goodsLevel.high;
            return goodsLevel;
        }
        //中低端课对应：商品分类为1标准课、6正式课、8阅读课
        Integer[] mediumLowGoodsTypes={Consts.GOODS_CATEGORY.standardGoods,Consts.GOODS_CATEGORY.formalGoods,Consts.GOODS_CATEGORY.readGoods};
        List<Integer> mediumLowGoodsTypeList=Arrays.asList(mediumLowGoodsTypes);
        if(mediumLowGoodsTypeList.contains(goods.getGoodsCategory())){
            goodsLevel=Consts.goodsLevel.mediumLow;
            return goodsLevel;
        }
        //体验课对应：商品分类为3体验课、0公开课、7资料包
        Integer[] experienceGoodsTypes={Consts.GOODS_CATEGORY.experienceGoods,Consts.GOODS_CATEGORY.publicGoods,Consts.GOODS_CATEGORY.materialGoods};
        List<Integer> experienceGoodsTypeList=Arrays.asList(experienceGoodsTypes);
        if(experienceGoodsTypeList.contains(goods.getGoodsCategory())){
            goodsLevel=Consts.goodsLevel.experience;
            return goodsLevel;
        }
        return goodsLevel;
    }

    private Integer getIsPop(Long uid,Long goodsId,Integer type,Integer showEntryTab){
        Integer isPop=Consts.PopType.noPop;
        List<UserViewProductUpdateDTO> userViewProductUpdateDTOList=null;
        try{
            userViewProductUpdateDTOList=stustampResource.getUserViewProductUpdateByUid(uid,goodsId,type,null);
        }catch (Exception e) {
            log.error("{} failed: uid:{},goodsId:{},type:{}, e:{} ", "getUserViewProductUpdateByUid", uid,goodsId,type,e);
        }
        //入学弹窗保存记录为空时，才弹窗
        if(showEntryTab==Consts.ShowEntryTab.showEntry && CollectionUtils.isEmpty(userViewProductUpdateDTOList)){
            isPop=Consts.PopType.pop;
        }
        return isPop;
    }

    //该商品是否包含私教班产品
    private Boolean isPrivateClassGoods(Goods goods){
        Boolean isPrivateClassGoods=false;
        //商品创建方式 1-产品排课 2-课程表排课 3-云私塾类型商品
        if(goods.getSourceFlag()!=null && goods.getSourceFlag()==3){
            //如果是云私塾商品，再判断该云私塾商品下的是否有私教班产品
            List<Product> alProductList=goodsResource.getProductByGoodIdAndProdType(goods.getId(),Consts.ProductType.PRODUCT_ADAPTIVE_LEARNING);
            if(CollectionUtils.isNotEmpty(alProductList)){
                List<Long> productIds=alProductList.stream().map(p->p.getId()).distinct().collect(Collectors.toList());
                List<ProductAdaptiveLearning> adaptiveLearningList=goodsResource.getAdaptiveProductByIdList(productIds);
                if(CollectionUtils.isNotEmpty(adaptiveLearningList)){
                    for(ProductAdaptiveLearning productAdaptiveLearning:adaptiveLearningList){
                        boolean isManualStudyOpen = productAdaptiveLearning.getPlanTaskOpenType()!=null && productAdaptiveLearning.getPlanTaskOpenType() == Consts.AL_PRIVATE_TEACH_PROD_TYPE.YES;
                        if(isManualStudyOpen){
                            isPrivateClassGoods=true;
                            break;
                        }
                    }
                }
            }
        }
        return isPrivateClassGoods;
    }

    private List<EvaluationUserAnswer> getEvaluationUserAnswerList(Long uid,List<Long> goodsIdList) throws DataAccessException{
        if(CollectionUtils.isEmpty(goodsIdList)){
            return Lists.newArrayList();
        }
        EvaluationUserAnswer evaluationUserAnswer=new EvaluationUserAnswer();
        evaluationUserAnswer.setUid(uid);
        evaluationUserAnswer.setBusType(Consts.EvaluationBusType.DEFAULT_NEW_TYPE);
        evaluationUserAnswer.setGoodsIds(goodsIdList);
        List<EvaluationUserAnswer> evaluationUserAnswers=evaluationUserAnswerDao.findList(evaluationUserAnswer);
        return evaluationUserAnswers;
    }

    private List<EvaluationUserAnswer> getPaperEvaluationUserAnswerList(Long uid,List<Long> goodsIdList) throws DataAccessException{
        if(CollectionUtils.isEmpty(goodsIdList)){
            return Lists.newArrayList();
        }
        EvaluationUserAnswer evaluationUserAnswer=new EvaluationUserAnswer();
        evaluationUserAnswer.setUid(uid);
        evaluationUserAnswer.setBusType(Consts.EvaluationBusType.DEFAULT_NEW_TYPE);
        evaluationUserAnswer.setEvaluationType(Consts.EvaluationType.MAJOR);
        evaluationUserAnswer.setGoodsIds(goodsIdList);
        List<EvaluationUserAnswer> evaluationUserAnswers=evaluationUserAnswerDao.findList(evaluationUserAnswer);
        return evaluationUserAnswers;
    }

    @Override
    public EvaluationReportDTO getEvaluationReport(Long uid, Long goodsId) throws DataAccessException {
        EvaluationReportDTO evaluationReportDTO=new EvaluationReportDTO();
        EvaluationUserAnswer evaluationUserAnswer=new EvaluationUserAnswer();
        evaluationUserAnswer.setUid(uid);
        evaluationUserAnswer.setBusType(Consts.EvaluationBusType.DEFAULT_NEW_TYPE);
        evaluationUserAnswer.setGoodsId(goodsId);
        //查询学员uid和商品下的入学测评题目作答（包括基础测评和专业测评）
        List<EvaluationUserAnswer> evaluationUserAnswers=evaluationUserAnswerDao.findList(evaluationUserAnswer);
        if(CollectionUtils.isEmpty(evaluationUserAnswers)){
            return evaluationReportDTO;
        }
        //学员的基础测评的作答
        List<EvaluationUserAnswer> baseEvaluationUserAnswers=evaluationUserAnswers.stream().filter(e->e.getEvaluationType()!=null && e.getEvaluationType().intValue()==Consts.EvaluationType.BASE.intValue()).collect(Collectors.toList());
        //学员专业测评作答
        List<EvaluationUserAnswer> paperEvaluationUserAnswers=evaluationUserAnswers.stream().filter(e->e.getEvaluationType()!=null && e.getEvaluationType().intValue()==Consts.EvaluationType.MAJOR.intValue()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(baseEvaluationUserAnswers) && CollectionUtils.isEmpty(paperEvaluationUserAnswers)){
            //如果只有基础测评作答，则标识为true
            evaluationReportDTO.setIsOnlyHaveEvaluationBaseQuestion(true);
            return evaluationReportDTO;
        }
        //按试卷作答ID倒排
        Comparator<EvaluationUserAnswer> orgAnswerIdComparator = Comparator.comparing(EvaluationUserAnswer::getOrgAnswerId).reversed();
        paperEvaluationUserAnswers.sort(orgAnswerIdComparator);
        //获取专业测评最新作答中一条记录
        EvaluationUserAnswer lastPaperEvaluationUserAnswer=paperEvaluationUserAnswers.get(0);
        Long paperId=lastPaperEvaluationUserAnswer.getPaperId();
        //根据试卷ID获取试卷下所有题目列表
        List<Long> questionIds=this.getQuestionListByPaperId(paperId);
        Integer questionNum=questionIds.size();
        Long answerId=lastPaperEvaluationUserAnswer.getOrgAnswerId();
        evaluationReportDTO.setEvaluationPaperId(paperId);
        evaluationReportDTO.setAnswerId(answerId);
        evaluationReportDTO.setAnswerIdStr(answerId+"");
        evaluationReportDTO.setQuestionNum(questionNum);
        Map<String, Object> qryParam = new HashMap<String, Object>();
        qryParam.put("id", answerId);
        qryParam.put("uid", uid);
        UserAnswer userAnswer = userAnswerDao.getShardingById(qryParam);
        evaluationReportDTO.setUsetime(userAnswer.getUsetime());
        evaluationReportDTO.setAnswerNum(userAnswer.getAnswerNum()==null?0:userAnswer.getAnswerNum().intValue());
        List<UserAnswerSum> useAnswerSums = userAnswerSumService.findAnswerSumAndDetail(uid, answerId,
                null);
        //学员入学测评专业测评作答正确率
        this.setEvaluationReportDTOAccuracy(useAnswerSums,evaluationReportDTO);
        //当前学员作答正确率
        Double accuracy=Double.valueOf(evaluationReportDTO.getAccuracy())/100;
        //获取当前学员专业测评试卷作答中的能力比例
        Double levelRate=this.getLevelRate(answerId,paperId,accuracy);
        evaluationReportDTO.setLevelRate(levelRate);
        //获取知识点对应题目的完成情况统计列表（能力详情）
        List<KnowledgeQuestionReportDTO> knowledgeQuestionReportDTOList=this.getKnowledgeQuestionReport(questionIds,useAnswerSums);
        evaluationReportDTO.setKnowledgeQuestionReportVoList(knowledgeQuestionReportDTOList);
        return evaluationReportDTO;
    }

    //获取知识点对应题目的完成情况统计列表
    private List<KnowledgeQuestionReportDTO> getKnowledgeQuestionReport(List<Long> questionIds,List<UserAnswerSum> userAnswerDetailList){
        List<KnowledgeQuestionReportDTO> reportDTOList=Lists.newArrayList();
        List<KnowledgeQuestionListDTO> knowledgeQuestionListDTOList=knowledgeResource.getKnowledgeQuestionListByQuestionIds(questionIds);
        if(CollectionUtils.isEmpty(knowledgeQuestionListDTOList)){
            return reportDTOList;
        }
        for(KnowledgeQuestionListDTO knowledgeQuestionListDTO:knowledgeQuestionListDTOList){
            KnowledgeQuestionReportDTO knowledgeQuestionReportDTO=new KnowledgeQuestionReportDTO();
            knowledgeQuestionReportDTO.setKnowledgeId(knowledgeQuestionListDTO.getKnowledgeId());
            knowledgeQuestionReportDTO.setKnowledgeName(knowledgeQuestionListDTO.getKnowledgeGraphName());
            int questionNum=(knowledgeQuestionListDTO.getQuestionIds()==null?0:knowledgeQuestionListDTO.getQuestionIds().size());
            knowledgeQuestionReportDTO.setQuestionNum(questionNum);
            Integer rightNum=0;
            if(CollectionUtils.isNotEmpty(knowledgeQuestionListDTO.getQuestionIds())){
                for(Long questionId:knowledgeQuestionListDTO.getQuestionIds()){
                    for(UserAnswerSum userAnswerSum:userAnswerDetailList){
                        if(questionId.equals(userAnswerSum.getQuestionId()) && userAnswerSum.getIsRight()== UserAnswerDetail.IsRight.RIGHT){
                            rightNum++;
                        }
                    }
                }
            }
            knowledgeQuestionReportDTO.setRightNum(rightNum);
            Double starRate=0.0;
            if(questionNum!=0){
                starRate=Math.ceil((rightNum.doubleValue()/questionNum)*5);
            }
            knowledgeQuestionReportDTO.setStarRate(starRate);
            reportDTOList.add(knowledgeQuestionReportDTO);
        }
        return reportDTOList;
    }

    //获取当前学员专业测评试卷作答中的能力比例
    private Double getLevelRate(Long answerId,Long paperId,Double accuracy) throws DataAccessException{
        Double levelRate=0.0;
        PaperAssessmentScore userPaperAssessmentScore=new PaperAssessmentScore();
        userPaperAssessmentScore.setUserAnswerId(answerId);
        userPaperAssessmentScore.setPaperId(paperId);
        //所有试卷作答得分表中获取，当前作答的记录
        List<PaperAssessmentScore> userPaperAssessmentScoreList=paperAssessmentScoreDao.findList(userPaperAssessmentScore);
        Integer paperAssessmentAnswerAllPersons=paperAssessmentScoreDao.getAssessmentAnswerCountByPaperId(paperId);//专业测评试卷作答总人数
        Integer underCurrentAnswerRightRatePersons=0;//小于当前学员作答正确率的人数
        if(CollectionUtils.isNotEmpty(userPaperAssessmentScoreList)){
            Double rightRate=userPaperAssessmentScoreList.get(0).getRightRate();
            underCurrentAnswerRightRatePersons=paperAssessmentScoreDao.getAssessmentAnswerCountByPaperIdAndRightRate(paperId,rightRate);
            levelRate=Math.ceil(underCurrentAnswerRightRatePersons.doubleValue()*100/paperAssessmentAnswerAllPersons);
        }else{
            underCurrentAnswerRightRatePersons=paperAssessmentScoreDao.getAssessmentAnswerCountByPaperIdAndRightRate(paperId,accuracy);
            levelRate=Math.ceil(underCurrentAnswerRightRatePersons.doubleValue()*100/(paperAssessmentAnswerAllPersons+1));
        }
        return levelRate;
    }

    private void setEvaluationReportDTOAccuracy(List<UserAnswerSum> userAnswerDetailList, EvaluationReportDTO evaluationReportDTO) {
        int rightNum = 0;//答对数
        for (UserAnswerSum userAnswerSumVo : userAnswerDetailList) {
            if (null != userAnswerSumVo.getIsRight()) {
                if (userAnswerSumVo.getIsRight() != UserAnswerDetail.IsRight.NOT_ANSWER) {
                    if (userAnswerSumVo.getIsRight() == UserAnswerDetail.IsRight.RIGHT) {
                        rightNum++;
                    }
                }
            }
        }
        evaluationReportDTO.setRightNum(rightNum);
        DecimalFormat decimalFormat = new DecimalFormat("0.0");
        String accuracy = decimalFormat.format(Double.valueOf(rightNum) /evaluationReportDTO.getQuestionNum() * 100);
        evaluationReportDTO.setAccuracy(accuracy);
    }

    private List<Long> getQuestionListByPaperId(Long paperId){
        List<QuestionGroup> questionGroupList=knowledgeResource.klg_getQuestionByPaperId(paperId);
        if (CollectionUtils.isEmpty(questionGroupList)) {
            return Lists.newArrayList();
        }
        List<Long> paperQuestionIdList=new ArrayList<>();
        for (QuestionGroup questionGroup : questionGroupList) {
            Collection<Question> questionList = questionGroup.getQuestionList();
            if (!CollectionUtils.isEmpty(questionList)) {
                paperQuestionIdList.addAll(questionList.stream().map(Question::getId).distinct().collect(Collectors.toList()));
            }
        }
        return paperQuestionIdList;
    }
}
