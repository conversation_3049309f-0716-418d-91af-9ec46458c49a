package cn.huanju.edu100.study.service;

import com.hqwx.study.dto.UserAutoRemoveErrorQuestionConfigDTO;

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/9/26.
 */
public interface UserSubErrorQuestionRemoveService {

    void openAutoRemoveErrorQuestion(Long uid, Integer state, Integer rightTimes);

    Long increaseRightTimes(Long uid, Long topicId);

    void decreaseRightTimes(Long uid, Long topicId);

    Boolean isAutoRemoveOpened(Long uid);

    UserAutoRemoveErrorQuestionConfigDTO getAutoRemoveConfig(Long uid);

}
