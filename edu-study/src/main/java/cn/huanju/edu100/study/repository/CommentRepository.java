package cn.huanju.edu100.study.repository;

import cn.huanju.edu100.study.mapper.homework.comment.CommentMapper;
import cn.huanju.edu100.study.mapper.homework.comment.CommentReplyMapper;
import cn.huanju.edu100.study.model.tutor.Comment;
import cn.huanju.edu100.study.model.tutor.CommentElement;
import cn.huanju.edu100.study.model.tutor.ReplyComment;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hqwx.study.dto.query.CommentReplyQuery;
import com.hqwx.study.dto.query.UserNoteQuestionQuery;
import com.hqwx.study.entity.studynote.StudyNoteBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/26 10:08
 * @description
 */
@Slf4j
@Repository
public class CommentRepository {
    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private CommentReplyMapper commentReplyMapper;

    /**
     * 分页查询一级评论信息
     * @param page 分页参数
     * @param commentElement 查询参数
     * @return 评论信息分页列表
     */
    public IPage<Comment> selectCommentPage(IPage<Comment> page, CommentElement commentElement) {

        QueryWrapper<Comment> queryWrapper = new QueryWrapper<>();

        // comment 表条件 -----------------------------------------------------------
        queryWrapper.eq(commentElement.getSource() != null, "a.source", commentElement.getSource())
                .eq("a.level", 1);  // 口查询一级评论
        if(commentElement.getIncludeOthers() != null && commentElement.getIncludeOthers()) {
            // 查询自己和他人的评论
            queryWrapper.and(q -> q.eq(commentElement.getUid() != null, "a.uid", commentElement.getUid())
                            .or().eq("a.status", 2)); // 自己的评论不受审核影响，他人的评论只查询审核通过的
        } else {
            // 只查询自己的评论
            queryWrapper.eq(commentElement.getUid() != null, "a.uid", commentElement.getUid());
        }
        queryWrapper.in(CollectionUtils.isNotEmpty(commentElement.getCommentIdList()),"a.id", commentElement.getCommentIdList());

        // comment_element 表条件 ---------------------------------------------------
        queryWrapper.eq(commentElement.getObjId() != null, "b.obj_id", commentElement.getObjId())
                .in(commentElement.getObjIdList() != null, "b.obj_id", commentElement.getObjIdList())
                .eq(commentElement.getObjType() != null, "b.obj_type", commentElement.getObjType())
                .eq(commentElement.getGoodsId() != null, "b.goods_id", commentElement.getGoodsId())
                .eq(commentElement.getCategoryId() != null, "b.category_id", commentElement.getCategoryId())
                .eq(commentElement.getProductId() != null, "b.product_id", commentElement.getProductId());

        queryWrapper.eq("a.del_flag", "0");

        // 排序 ---------------------------------------------------------------------
        if(commentElement.getIncludeOthers() != null && commentElement.getIncludeOthers()) {
            // 如果包含他人的笔记，则优先按置顶和点赞数倒序排列
            queryWrapper.orderByDesc("a.is_stick")
                    .orderByDesc("a.thumb_up_num");
        }
        queryWrapper.orderByDesc("a.update_date");

        return commentMapper.selectWithPage(page, queryWrapper);
    }

    /**
     * 设置评论的回复信息
     * @param myUid 当前用户id
     * @param commentList 评论列表
     * @return 包含了回复的评论列表
     */
    public List<Comment> setCommentReply(Long myUid, List<Comment> commentList) {
        if(CollectionUtils.isEmpty(commentList)) {
            return commentList;
        }

        List<Long> commentIdList = commentList.stream().map(Comment::getId).collect(Collectors.toList());

        // 查询评论关联的所有回复
        List<ReplyComment> commentReplyList = null;
        QueryWrapper<CommentReplyQuery> queryWrapper = new QueryWrapper<>();

        // 自己的评论不受审核影响，他人的评论只查询审核通过的
//        queryWrapper.eq(myUid != null, "b.uid", myUid)
                // 回复通过（comment_reply 表中的 status），并且内容通过审核（comment表中的 status）
//                .or(q -> q.eq("a.status", 1).eq("b.status", 2));
        queryWrapper.in("a.belong_comment_id", commentIdList);
        queryWrapper.eq("a.del_flag", 0);
        queryWrapper.eq("b.del_flag", 0);
//        queryWrapper.orderByDesc("updateDate");
        commentReplyList = commentReplyMapper.getCommentReplyList(queryWrapper);
        if(CollectionUtils.isNotEmpty(commentReplyList)) {
            //过滤一下
            commentReplyList = commentReplyList.stream().filter(x-> x.getUid().equals(myUid) || x.getStatus()==2).collect(Collectors.toList());
            //排序, null放到最前
            commentReplyList.sort(Comparator.comparing(ReplyComment::getUpdateDate));
            // 将回复按评论id分组
            Map<Long, List<ReplyComment>> commentReplyMap = commentReplyList.stream()
                    .collect(Collectors.groupingBy(ReplyComment::getBelongCommentId));
            // 将回复设置到评论中
            commentList.forEach(comment -> {
                List<ReplyComment> replyCommentList = commentReplyMap.get(comment.getId());
                if(CollectionUtils.isNotEmpty(replyCommentList)) {
                    comment.setReplyCommentList(replyCommentList);
                }
            });
        }
        return commentList;
    }

    public boolean updateById(Long commentId, String text) {
        UpdateWrapper<Comment> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", commentId);
        updateWrapper.set("content", text);
        updateWrapper.set("update_date", new Date());
        return commentMapper.update(null, updateWrapper) > 0;
    }

    public List<Long> getNoteQuestionList(UserNoteQuestionQuery query) {
        if (query == null || query.getGoodsId() == null || query.getCategoryId() == null || query.getUid() == null) {
            log.error("参数错误，uid, goodsId, categoryId不能为空");
            return new ArrayList<>();
        }
        QueryWrapper<Comment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("b.goods_id", query.getGoodsId())
                .eq("b.category_id", query.getCategoryId())
                .eq("a.uid", query.getUid())
                .eq("a.del_flag", 0)
                .eq("b.del_flag", 0)
                .eq("b.obj_type", StudyNoteBaseInfo.Type.TYPE_QUESTION.getValue());

        return commentMapper.getNoteQuestionList(queryWrapper);
    }
}
