package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.service.ExemplaryAnalogousQuestionService;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.ParamUtils;
import com.google.gson.Gson;
import com.hqwx.study.dto.ExemplaryAnalogousQuestionDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

@Component
public class ExemplaryAnalogousQuestionThriftImpl extends AbstractServiceThrift {

    private static Logger logger = LoggerFactory.getLogger(ExemplaryAnalogousQuestionThriftImpl.class);
    static Gson gson = GsonUtil.getGenericGson();

    @Autowired
    private ExemplaryAnalogousQuestionService exemplaryAnalogousQuestionService;

    public response sty_getRecommendedQuestionIdListByErrorQuestionId(request req) throws BusinessException {
        String entry = "sty_getRecommendedQuestionIdListByErrorQuestionId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> param = gson.fromJson(req.getMsg(), Map.class);
            if (param == null || param.get("uid") == null || param.get("errorQuestionIdList") == null) {
                logger.error("{} sty_getRecommendedQuestionIdListByErrorQuestionId return null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getRecommendedQuestionIdListByErrorQuestionId param lose productIds is null or uid is null");
            } else {
                Long uid = ParamUtils.getLong(param, "uid", false);
                Long categoryId = ParamUtils.getLong(param, "categoryId", false);
                Long productId = ParamUtils.getLong(param, "productId", false);
                List<Long> errorQuestionIdList = ParamUtils.getLongList(param, "errorQuestionIdList", false);
                List<Long> excludeQuestionIdList = ParamUtils.getLongList(param, "excludeQuestionIdList", true);
                List<ExemplaryAnalogousQuestionDTO> result = exemplaryAnalogousQuestionService.getRecommendedQuestionIdListByErrorQuestionId(uid, categoryId, productId, errorQuestionIdList, excludeQuestionIdList);
                if (result != null) {
                    res.setCode(Constants.SUCCESS);
                    res.setMsg(GsonUtil.toJson(result));
                } else {
                    res.setCode(Constants.OBJ_NOT_EXISTS);
                    res.setMsg("sty_getRecommendedQuestionIdListByErrorQuestionId return null");
                }
            }
        } catch (Exception e) {
            logger.error("sty_getRecommendedQuestionIdListByErrorQuestionId error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}