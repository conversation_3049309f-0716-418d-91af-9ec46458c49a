package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Collection;

/**
 * 资源相关信息Entity
 *
 * <AUTHOR>
 * @version 2015-05-11
 */
public class Resource extends DataEntity<Resource> {

	private static final long serialVersionUID = 1L;
	private String name; // name
	private String description; // description
	private Long firstCategory; // first_category
	private Long secondCategory; // second_category
	private Long categoryId; // category_id
	private String ip; // ip
	private Integer relateType; // 图片，文件，商品，讲，题目
	private String url; // url
	private Long relateId; // relate_id
	private Collection<ResourceTransData> transData;

	private Integer isVideourl;

	/**
	 * @return the isVideourl
	 */
	public Integer getIsVideourl() {
		return isVideourl;
	}

	/**
	 * @param isVideourl the isVideourl to set
	 */
	public void setIsVideourl(Integer isVideourl) {
		this.isVideourl = isVideourl;
	}

	public Resource() {
		super();
	}

	public Resource(Long id) {
		super(id);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Long getFirstCategory() {
		return firstCategory;
	}

	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Integer getRelateType() {
		return relateType;
	}

	public void setRelateType(Integer relateType) {
		this.relateType = relateType;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Long getRelateId() {
		return relateId;
	}

	public void setRelateId(Long relateId) {
		this.relateId = relateId;
	}

	public Collection<ResourceTransData> getTransData() {
		return transData;
	}

	public void setTransData(Collection<ResourceTransData> transData) {
		this.transData = transData;
	}

}
