package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorStudentAnswerDetailDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail;
import cn.huanju.edu100.study.service.tutor.TutorStudentAnswerDetailService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 做题记录日详情Service
 *
 * <AUTHOR>
 * @version 2016-01-19
 */
@Service
public class TutorStudentAnswerDetailServiceImpl extends
        BaseServiceImpl<TutorStudentAnswerDetailDao, TutorStudentAnswerDetail> implements
        TutorStudentAnswerDetailService {

    @Override
    public List<Long> getDoneQuestionIdByUidAndTaskIdList(Long uid, List<Long> taskIdList) throws DataAccessException {
        return dao.getDoneQuestionIdByUidAndTaskIdList(uid, taskIdList);
    }
}
