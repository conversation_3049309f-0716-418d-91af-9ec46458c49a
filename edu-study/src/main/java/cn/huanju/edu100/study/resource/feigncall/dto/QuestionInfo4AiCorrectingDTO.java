package cn.huanju.edu100.study.resource.feigncall.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @program: resource-common
 * @ClassName QuestionInfo4AiCorrectingDTO
 * @description:
 * @author: gaohaijing
 * @create: 2023-08-02 17:35
 * @Version 1.0
 **/
@Data
@ApiModel(value = "主观题信息", description = "主观题信息")
public class QuestionInfo4AiCorrectingDTO {
    private Long secondCategory;
    private Long categoryId;
    private String questionInfo;
    private String answerInfo;
    private List<Long> topicIdList;

    private boolean hasImage;
}
