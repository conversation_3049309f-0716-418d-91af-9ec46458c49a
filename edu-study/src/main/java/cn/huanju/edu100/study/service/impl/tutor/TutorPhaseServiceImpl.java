package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorPhaseDao;
import cn.huanju.edu100.study.dao.tutor.TutorPlanDao;
import cn.huanju.edu100.study.dao.tutor.TutorStudentOverviewDao;
import cn.huanju.edu100.study.dao.tutor.TutorStudentTaskCountDao;
import cn.huanju.edu100.study.model.tutor.*;
import cn.huanju.edu100.study.service.tutor.TutorPhaseService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 个性化阶段Service
 * <AUTHOR>
 * @version 2016-01-14
 */
@Service
public class TutorPhaseServiceImpl extends BaseServiceImpl<TutorPhaseDao, TutorPhase> implements TutorPhaseService {

    @Autowired
    private TutorPlanDao tutorPlanDao;

    @Autowired
    private TutorStudentTaskCountDao tutorStudentTaskCountDao;

    @Autowired
    private TutorStudentOverviewDao tutorStudentOverviewDao;

    @Override
    public List<TutorPhaseDto> listPhaseByUidAndCategoryList(Map<String, Object> params) throws DataAccessException {
        if (params == null || params.get("uid") == null
                || params.get("classes") == null || params.get("categoryIds") == null) {
            return null;
        }

        String categoryIds = params.get("categoryIds").toString();
        String[] categoryIdArray = categoryIds.split(",");
        if (categoryIdArray == null || categoryIdArray.length == 0) {
            return null;
        }
        List<Long> categoryIdList = new ArrayList<Long>();
        for (String categoryId : categoryIdArray) {
            categoryIdList.add(NumberUtils.toLong(categoryId));
        }

        params.put("categoryIdList", categoryIdList);
        List<TutorPlan> tutorPlans = tutorPlanDao.listPlanes(params);
        if (CollectionUtils.isEmpty(tutorPlans)) {
            return null;
        }

        List<Long> planIds = new ArrayList<Long>();
        for (TutorPlan tutorPlan : tutorPlans) {
            if (tutorPlan.getGroupId() != null) {
                planIds.add(tutorPlan.getId());
            }
        }

        //查询每个科目所有的阶段
        params.put("planIdList", planIds);
        List<TutorPhase> tutorPhases = dao.listPhases(params);
        if (CollectionUtils.isEmpty(tutorPhases)) {
            return null;
        }

        List<TutorPhaseDto> tutorPhaseDtos = new ArrayList<TutorPhaseDto>();
        for (Long categoryId : categoryIdList) {
            TutorPhaseDto tutorPhaseDto = new TutorPhaseDto();
            tutorPhaseDto.setCategoryId(categoryId);
            tutorPhaseDto.setTutorPhases(getTutorPhases(tutorPhases, tutorPlans, categoryId));
            tutorPhaseDtos.add(tutorPhaseDto);
        }

        List<Long> phaseIdList = new ArrayList<Long>();
        for (TutorPhase tutorPhase : tutorPhases) {
            phaseIdList.add(tutorPhase.getId());
        }

        params.put("phaseIdList", phaseIdList);
        //查询阶段任务完成情况
        List<TutorStudentTaskCount> tutorStudentTaskCounts = tutorStudentTaskCountDao.getStudentTaskCountByPhaseIds(params);
        //查询科目的视频和题目的统计情况
        List<TutorStudentOverview> tutorStudentOverviews = tutorStudentOverviewDao.getTutorStudentOverviews(params);
        setCompleteDetail(tutorStudentTaskCounts, tutorStudentOverviews, tutorPhaseDtos);

        return tutorPhaseDtos;
    }

    @Override
    public List<TutorPhase> listPhaseByGroupId(Long groupId) throws DataAccessException {
        Map<String,Object> params = Maps.newHashMap();
        params.put("groupId", groupId);
        List<TutorPlan> plans = tutorPlanDao.listPlanes(params);
        List<Long> planIds = new ArrayList<Long>();
        for (TutorPlan tutorPlan : plans) {
            planIds.add(tutorPlan.getId());
        }

        Map<String,Object> phaseParams = Maps.newHashMap();
        phaseParams.put("planIdList", planIds);
        List<TutorPhase> phases = dao.listPhases(phaseParams);

        return phases;
    }
    private void setCompleteDetail(List<TutorStudentTaskCount> tutorStudentTaskCounts,
            List<TutorStudentOverview> tutorStudentOverviews, List<TutorPhaseDto> tutorPhaseDtos) {

        Map<Long, TutorStudentTaskCount> countMap = new HashMap<Long, TutorStudentTaskCount>();
        for (TutorStudentTaskCount tutorStudentTaskCount : tutorStudentTaskCounts) {
            countMap.put(tutorStudentTaskCount.getPhaseId(), tutorStudentTaskCount);
        }

        Map<Long, TutorStudentOverview> tuMap = new HashMap<Long, TutorStudentOverview>();
        if (!CollectionUtils.isEmpty(tutorStudentOverviews)) {
            for (TutorStudentOverview tutorStudentOverview : tutorStudentOverviews) {
                tuMap.put(tutorStudentOverview.getCategoryId(), tutorStudentOverview);
            }
        }

        for (TutorPhaseDto tutorPhaseDto : tutorPhaseDtos) {
            int complete = 0;
            int all = 0;
            for (TutorPhase tutorPhase : tutorPhaseDto.getTutorPhases()) {
                TutorStudentTaskCount count = countMap.get(tutorPhase.getId());
                if (count != null) {
                    tutorPhase.setTutorStudentTaskCount(countMap.get(tutorPhase.getId()));
                    complete += count.getComplete();
                    all += count.getCount();
                }
            }

            TutorStudentOverview tutorStudentOverview = tuMap.get(tutorPhaseDto.getCategoryId());
            if (tutorStudentOverview != null) {
                tutorPhaseDto.setQuestionCount(tutorStudentOverview.getActualQuestion());
                tutorPhaseDto.setVideoCount(tutorStudentOverview.getActualLength());
            }
            tutorPhaseDto.setCompleteCount(complete);
            tutorPhaseDto.setAllCount(all);

        }
    }

    private List<TutorPhase> getTutorPhases(List<TutorPhase> tutorPhases, List<TutorPlan> tutorPlans, Long categoryId) {

        if (CollectionUtils.isEmpty(tutorPhases) || categoryId == null) {
            return null;
        }

        List<TutorPhase> tuList = new ArrayList<TutorPhase>();
        for (TutorPlan tutorPlan : tutorPlans) {
            if (tutorPlan.getCategoryId().equals(categoryId)) {
                for (TutorPhase tutorPhase : tutorPhases) {
                    if (tutorPhase.getPlanId().equals(tutorPlan.getId())) {
                        tuList.add(tutorPhase);
                    }
                }
            }
        }
        return tuList;
    }

}
