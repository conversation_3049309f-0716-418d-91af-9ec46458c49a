package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.UserCollectItems;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏题目Service
 * <AUTHOR>
 * @version 2015-09-02
 */
public interface UserCollectItemsService extends BaseService<UserCollectItems>{

	boolean removeUserCollectItemsByQuestionIdOrCategoryId(Long uid, Long questionId, Long categoryId) throws DataAccessException;

	boolean delete(Long uid, Long id) throws DataAccessException;

	/**
	 * 收藏某个题目
	 * 里面有用到boxId,如果是其他收藏，到时重构重用

	 * @param item
	 * @return
	 * @throws DataAccessException
	 */
	public boolean collectQustion(final UserCollectItems item)throws DataAccessException;

	/**
	 * 取消收藏某个题目
	 * 里面有用到boxId,如果是其他收藏，到时重构重用
	 * @param item
	 * @return
	 * @throws DataAccessException
	 */
	public boolean cancelCollectQuestion(final UserCollectItems item)throws DataAccessException;

	/**
	 * 获取收藏的id列表
	 * 里面有用到boxId,如果是其他收藏，到时重构重用
	 * @param item
	 * @param from
	 * @param rows
	 * @return
	 * @throws DataAccessException
	 */
	public List<Long> getCollectionQuestionItemIds(final UserCollectItems item, int from ,int rows)throws DataAccessException;

	/**
	 * 获取收藏的题目数目
	 * 里面有用到boxId,如果是其他收藏，到时重构重用
	 * @param item
	 * @return
	 * @throws DataAccessException
	 */
	public Integer getCollectionQuestionItemNum(final UserCollectItems item)throws DataAccessException;

    /**
     * 获取收藏的题目数目,精确到每一个box里有多少收藏题目
     * @param item
     * @return
     * @throws DataAccessException
     */
    Map<Long,Integer> getCollectionQuestionItemNumWithBox(UserCollectItems item) throws DataAccessException;

    /**
	 * 判断题目是否有收藏过
	 * 里面有用到boxId,如果是其他收藏，到时重构重用
	 * @param  item 基本参数 uid BoxId
	 * @param itemIdList
	 * @return
	 * @throws DataAccessException
	 */
	public Map<Long, Boolean> bQuestionCollected(final UserCollectItems item, final List<Long> itemIdList)throws DataAccessException;
	public Integer getCollectionQuestionItemNumByUid(UserCollectItems item) throws DataAccessException ;
	List<UserCollectItems> findListInTbIdxByUid(UserCollectItems entity,
			int from, int rows) throws DataAccessException;
	List<Long> findListInTbIdxByUidAndBoxIds(Long uid,List<Long> boxIds,
			Integer itemType,Integer sourceType) throws DataAccessException;

    List<UserCollectItems> getListInTbIdx(Map<String, Object> params) throws DataAccessException;

    void transferUserCollectItems();

	void transferUserCollectItems(Long uid,Integer tbidx);
}
