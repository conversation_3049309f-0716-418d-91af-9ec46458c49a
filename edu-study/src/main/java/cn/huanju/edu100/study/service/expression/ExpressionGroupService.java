package cn.huanju.edu100.study.service.expression;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.expression.ExpressionGroup;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Set;

/**
 * 规则组Service
 * <AUTHOR>
 * @version 2016-05-23
 */
public interface ExpressionGroupService extends BaseService<ExpressionGroup> {

    List<ExpressionGroup> findListByParam(Set<Long> idSet, Integer type) throws DataAccessException;
}
