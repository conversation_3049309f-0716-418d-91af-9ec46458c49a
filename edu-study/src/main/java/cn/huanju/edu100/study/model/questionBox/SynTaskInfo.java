package cn.huanju.edu100.study.model.questionBox;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 18/11/2
 */
public class SynTaskInfo extends DataEntity<SynTaskInfo> {
    private String name;
    private Date synTime;
    private String synKey;
    private Date lastExeTime;
    private Integer isOn;
    private String filter;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getSynTime() {
        return synTime;
    }

    public void setSynTime(Date synTime) {
        this.synTime = synTime;
    }

    public String getSynKey() {
        return synKey;
    }

    public void setSynKey(String synKey) {
        this.synKey = synKey;
    }

    public Date getLastExeTime() {
        return lastExeTime;
    }

    public void setLastExeTime(Date lastExeTime) {
        this.lastExeTime = lastExeTime;
    }

    public Integer getIsOn() {
        return isOn;
    }

    public void setIsOn(Integer isOn) {
        this.isOn = isOn;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }
}
