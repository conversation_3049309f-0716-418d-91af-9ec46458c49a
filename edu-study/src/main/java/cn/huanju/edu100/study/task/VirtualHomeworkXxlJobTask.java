package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.dao.UserVirtualHomeworkDao;
import cn.huanju.edu100.study.model.questionBox.VirtualHomework;
import cn.huanju.edu100.study.model.questionBox.VirtualHomeworkDetail;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Maps;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gaohaijing
 * create at:
 * @description: 归档虚拟作业表
 */
@Service
public class VirtualHomeworkXxlJobTask {

    private static final Logger logger = LoggerFactory.getLogger(VirtualHomeworkXxlJobTask.class);
    private static final Integer BATCH = 100;

    @Autowired
    @Qualifier("userVirtualHomeworkDao")
    private UserVirtualHomeworkDao userVirtualHomeworkDao;
    @Autowired
    @Qualifier("userVirtualHomeworkHistoryDao")
    private UserVirtualHomeworkDao userVirtualHomeworkHistoryDao;

    @XxlJob("VirtualHomeworkJobHandler")
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("VirtualHomeworkJobHandler start");
        //分片
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);

        for (int i = 0; i < 32; i++) {
            if (i % zoneTotal == zoneIndex) {
                moveHistory(i);
            }
        }

        logger.info("VirtualHomeworkJobHandler end");
        return ReturnT.SUCCESS;
    }

    private void moveHistory(Integer tbidx) {
        Date endDate = DateUtil.addYears(new Date(), -2);
        logger.info("moveHistory tbidx is :{}, endDate:{}", tbidx, DateUtil.formatDate(endDate));
        Map<String, Object> query = Maps.newHashMap();
        query.put("tbidx", tbidx);
        query.put("from", 0);
        query.put("pageSize", BATCH);
        try {
            List<VirtualHomework> homeworkList = userVirtualHomeworkDao.findByParam(query);
            for (VirtualHomework virtualHomework : homeworkList) {
                if (virtualHomework.getCreateDate() != null && virtualHomework.getCreateDate().before(endDate)) {
                    logger.info("moveHistory homework with id:{},tbidx:{} is before endDate", virtualHomework.getId(), tbidx);
                    try {
                        userVirtualHomeworkHistoryDao.insert(virtualHomework);
                    } catch (DataAccessException e) {
                        logger.info("userVirtualHomeworkHistory cat exception with message:{}", e.getMessage());
                        if (StringUtils.indexOf(e.getMessage(), "Duplicate entry") > 0) {
                            logger.info("userVirtualHomeworkHistory have duplicate row with id:{},tbidx:{}", virtualHomework.getId(), tbidx);
                        } else{
                            throw e;
                        }
                    }
                    List<VirtualHomeworkDetail> detailList = userVirtualHomeworkDao.getHomeWorkDetails(
                            virtualHomework.getId(), virtualHomework.getUid(), null);

                    if(CollectionUtils.isNotEmpty(detailList)){
                        try {
                            userVirtualHomeworkHistoryDao.insertDetailBatch(detailList, virtualHomework.getUid());
                        } catch (DataAccessException e) {
                            logger.info("userVirtualHomeworkHistory cat exception with message:{}", e.getMessage());
                            if (StringUtils.indexOf(e.getMessage(), "Duplicate entry") > 0) {
                                logger.info("userVirtualHomeworkHistory insert deatail have duplicate row with id:{},tbidx:{}", virtualHomework.getId(), tbidx);
                                userVirtualHomeworkHistoryDao.deleteDetailByHomeworkId(virtualHomework.getId(), virtualHomework.getUid());
                                userVirtualHomeworkHistoryDao.insertDetailBatch(detailList, virtualHomework.getUid());
                            } else{
                                throw e;
                            }
                        }
                    }
                    userVirtualHomeworkDao.deleteDetailByHomeworkId(virtualHomework.getId(), virtualHomework.getUid());
                    userVirtualHomeworkDao.delete(virtualHomework.getId(), virtualHomework.getUid());
                } else {
                    logger.info("moveHistory homework with id:{},tbidx:{} is after endDate, stop!", virtualHomework.getId(), tbidx);
                    return;
                }
            }
        } catch (Exception e) {
            logger.error("moveHistory catch exception", e);
        }

    }
}
