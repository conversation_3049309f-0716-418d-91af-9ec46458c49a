package cn.huanju.edu100.study.service;

import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.practice.DailyQueryParam;
import cn.huanju.edu100.study.model.questionBox.VirtualHomework;
import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.entity.UserAnswerDetail;

import java.util.*;

public interface UserQuestionBoxService {


	/**
	 * 根据策略随机抽取X道题目
	 * @param num
	 * @param randomType
	 * @param objType
	 * @param objId
	 * @param qTypes (在objType的前提下，增加题型的子过滤项)
	 * @param teachBookId
	 * @param boxId
	 * @param uid
	 * */
	VirtualHomework getRamdonBoxQuestionList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType,
											 List<Integer> qTypes,Integer randomType, Integer num) throws DataAccessException;



	VirtualHomework getPaperDailyPracticeVirtualHomework(Long uid, Long boxId, Long teachBookId, Integer objType, Integer randomType, Integer type, Long objId, DailyQueryParam dailyQueryParam) throws DataAccessException;


	VirtualHomework getRandomDailyVirtualHomework(Long uid, Long boxId, Long teachBookId, Integer objType, Integer randomType, Integer num, Long categoryId, Integer type, Long objId, List<Integer> qTypes) throws DataAccessException;

		/**
         * 题库5.0根据策略随机抽取X道题目
         * @param num
         * @param randomType
         * @param objType
         * @param objId
         * @param qTypes (在objType的前提下，增加题型的子过滤项)
         * @param teachBookId
         * @param boxId
         * @param uid
         * */
	VirtualHomework getNewRandomBoxQuestionList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType,
											 List<Integer> qTypes,Integer randomType, Integer num) throws DataAccessException;

	/**
	 * 题库5.3根据用户章节练习记录抽取每日一练题目
	 * @param num
	 * @param randomType
	 * @param objType
	 * @param objId
	 * @param qTypes (在objType的前提下，增加题型的子过滤项)
	 * @param teachBookId
	 * @param boxId
	 * @param uid
	 * */
	VirtualHomework getDailyPracticeQuestionList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType,
												List<Integer> qTypes,Integer randomType, Integer num) throws DataAccessException;

	Integer getNewRandomBoxQuestionCount(Long uid, Long boxId,
										 Long teachBookId, Long objId, Integer objType, List<Integer> qTypes,Integer randomType,
										 Integer num) throws DataAccessException;

	/**
	 * 获取用户已经做过的题目
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * */
	List<Long> getUserAnswerBoxQuestionInfo(Long uid, Long boxId,
											Long teachBookId, Long objId, Integer objType)throws DataAccessException;

	/**
	 * 获取用户已经做过的题目
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objType
	 * */
	Map<Long, List<Long>> getUserAnswerBoxQuestionInfoBatch(Long uid, Long boxId,
                                                            Long teachBookId, List<Long> objIds, Integer objType)throws DataAccessException;

	/**
	 * 获取周用户已经做过的题目
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * @param reportWeekNum
	 * */
	List<Long> getWeekAnswerBoxQuestionInfo(Long uid, Long boxId,
											Long teachBookId, Long objId, Integer objType, Long reportWeekNum)throws DataAccessException;

	/**
	 * 获取用户做错的题目
	 * @param uid
	 * @param boxId
	 * @param objId
	 * @param objType
	 * */
	List<Long> getUserWrongBoxQuestionInfo(Long uid, Long boxId,
										   Long teachBookId,Long objId,Integer objType)throws DataAccessException;


	/**
	 * 题库5.0 获取用户做错的题目
	 * @param uid
	 * @param boxId
	 * @param objId
	 * @param objType
	 * */
	List<Long> getNewUserWrongBoxQuestionInfo(Long uid, Long boxId,
										   Long teachBookId,Long objId,Integer objType)throws DataAccessException;

	Map<Long, List<Long>> getUserWrongBoxQuestionInfoBatch(Long uid, Long boxId,
                                                           Long teachBookId, List<Long> objIds, Integer objType)throws DataAccessException;

	/**
	 * 获取周用户做错的题目
	 * @param uid
	 * @param boxId
	 * @param objId
	 * @param objType
	 * @param reportWeekNum
	 * */
	List<Long> getWeekWrongBoxQuestionInfo(Long uid, Long boxId,
										   Long teachBookId,Long objId,Integer objType, Long reportWeekNum)throws DataAccessException;

	/**
	 * 获取用户已消灭做错的题目
	 * @param uid
	 * @param boxId
	 * @param objId
	 * @param objType
	 * */
	List<Long> getWipeOutWrongBoxQuestionInfo(Long uid, Long boxId,
											  Long teachBookId,Long objId,Integer objType)throws DataAccessException;

	/**
	 * 用户错题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 * @param topicIdList 答错的子题id列表
	 *
	 * */
	void cacheUserBoxWrongQuestion(Long uid, Long homeworkId, Long oldBookId, Long newBookId,
								   Long boxId, List<Long> questionIds, List<Long> topicIdList) throws DataAccessException;

	/**
	 * 周用户错题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param questionIds 题目id列表
	 * @param reportWeekNum 答题报告周计数
	 * */
	public void cacheWeekWrongQuestion(Long uid, Long homeworkId, Long teachBookId,
									   Long boxId, List<Long> questionIds, Long reportWeekNum) throws DataAccessException;


	/**
	 * 周用户做题情况入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param doneIds 做过题目id列表
	 * @param wrongIds 做错题目id列表
	 *
	 * */
	public void cacheWeekQuestion(Long uid, Long homeworkId, Long oldBookId, Long newBookId,
								  Long boxId, List<Long> doneIds, List<Long> wrongIds) throws DataAccessException;

	/**
	 * 周用户做过的题入redis缓存
	 * @param uid
	 * @param homeworkId 作业id
	 * @param boxId 题库id
	 * @param teachBookId 教材id
	 * @param questionIds 题目id列表
	 * @param reportWeekNum 答题报告周计数
	 * */
	void cacheWeekDoneQuestion(Long uid, Long homeworkId, Long teachBookId,
							   Long boxId, List<Long> questionIds, Long reportWeekNum) throws DataAccessException;

	/**
	 * 刷新用户做过的题目信息（redis：total_count，right_count，last_answer）
	 * @param uid
	 * @param answerDetails 答题详情
	 *
	 * */
	void refreshUserHisQuestionInfo(Long uid,
                                    Collection<UserAnswerDetail> answerDetails, Collection<Question> questions) throws DataAccessException;

	/**
	 * 刷新用户最近一次做过的题目（）
	 * @param uid
	 * @param boxId
	 * @param questions
	 *
	 * */
	void refreshUserLatestAnswerQId(Long uid, Long boxId, Collection<Question> questions) throws DataAccessException;

	/**
	 * 移除用户的错题
	 * @param uid
	 * @param boxId 题库id
	 * @param questionIds 需要移除的题目ids
	 * */
	void removeUserWrongQuestion(Long uid, Long boxId, List<Long> questionIds) throws DataAccessException;

	void removeUserWrongQuestion(Long uid, Long boxId, List<Long> questionIds, boolean updateWrong) throws DataAccessException;
	/**
	 * 移除用户已消灭的错题
	 * @param uid
	 * @param boxId 题库id
	 * @param questionIds 需要移除的题目ids
	 * */
	void removeWipeOutWrongQuestion(Long uid, Long boxId, List<Long> questionIds) throws DataAccessException;

	/**
	 * 题库5.0移除用户已消灭的错题
	 * @param uid
	 * @param boxId 题库id
	 * param questionIds 需要移除的题目ids
	 * */
	void removeNewWipeOutWrongQuestion(Long uid, Long boxId, List<Long> filterQuestions) throws DataAccessException;


	/**
	 * 自动生成练习
	 * */
	Long GenerateBoxExercise(UserGenerateExerciseAnswer userExerciseAnswer) throws DataAccessException;

	/**
	 * 根据策略随机抽取X道题目（游客模式）
	 * @param num
	 * @param randomType
	 * @param objType (0：所有，1：章节，2：知识点)
	 * @param objId
	 * @param qTypes (在objType的前提下，增加题型的子过滤项)
	 * @param teachBookId
	 * @param boxId
	 *
	 * */
	VirtualHomework ramdonBoxQuestion4Tourist(Long boxId, Long teachBookId,Long objId, Integer objType, List<Integer> qTypes,
											  Integer randomType, Integer num) throws DataAccessException;

	/**
	 * TiKu5.0 根据策略随机抽取X道题目（游客模式）
	 *
	 * @param boxId
	 * @param teachBookId
	 * @param objId
	 * @param objType               (0：所有，1：章节，2：知识点)
	 * @param qTypes                (在objType的前提下，增加题型的子过滤项)
	 * @param randomType
	 * @param excludeQuestionIdList
	 * @param num
	 */
	VirtualHomework newRandomBoxQuestion4Tourist(Long boxId, Long teachBookId,Long objId, Integer objType, List<Integer> qTypes,
											  Integer randomType,List<Long> excludeQuestionIdList, Integer num) throws DataAccessException;


    void getOriQuestionIds(Set<Long> oriQuestionIds, Long boxId, Long teachBookId, Long objId, Integer objType, StringBuffer homeworkName) throws DataAccessException;

	/**
	 * 题库5.0 获取题目列表
	 * @param oriQuestionIds
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * @param homeworkName
	 * @throws DataAccessException
	 */
	void getNewOriQuestionIds(Set<Long> oriQuestionIds, Long teachBookId, Long objId, Integer objType, StringBuffer homeworkName) throws DataAccessException;

	/**
	 * 获取书页题目列表
	 * @param oriQuestionIds
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * @param homeworkName
	 * @throws DataAccessException
	 */
	void getShuyeOriQuestionIds(Set<Long> oriQuestionIds, Long teachBookId, Long objId, Integer objType, StringBuffer homeworkName,Integer origin) throws DataAccessException;

    /**
	 * 根据条件获取题库内的题目
	 * @param boxId
	 * @param teachBookId
	 * @param objType (0：所有，1：章节，2：知识点)
	 * @param objId
	 *
	 * */
	List<Long> getBoxQuestionIds(Long boxId, Long teachBookId, Long objId,
								 Integer objType) throws DataAccessException;

	Map<Long, List<Long>> getBoxQuestionIdsBatch(Long boxId, Long teachBookId, List<Long> objIds,
                                                 Integer objType) throws DataAccessException;


	/**
	 * 根据条件取用户做过的题目列表
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * @return
	 * @throws DataAccessException
	 */
	List<Long> getUserDoneQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType) throws DataAccessException;


	/**
	 * 题库5.0获取题目id
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objId
	 * @param objType
	 * @return
	 * @throws DataAccessException
	 */
	List<Long> getNewUserDoneQuestionIdList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType) throws DataAccessException;


	String getUserDoneQuestions(Long uid, Long boxId, String key) throws DataAccessException ;

    Map<String, String> dealUserBoxDoneQuestion4Qtype(Long uid, Long boxId, List<Long> questionIds)throws DataAccessException;

	HashMap<String, List<Long>> dealDoneChapterDataNewReal(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds);

	HashMap<String, String> dealDoneChapterDataNew(ChapterSection chapterSection, Long uid, Long teachBookId, Long boxId, List<Long> questionIds);


	String getUserWipeOutWrongQuestions(Long uid, Long boxId, String key) throws DataAccessException;

    Map<String, String> getUserWipeOutWrongQuestionsAll(Long uid, Long boxId) throws DataAccessException;

    String getUserWrongQuestions(Long uid, Long boxId, String key) throws DataAccessException ;

    /**
    *
    * <AUTHOR> duxiulei
    * @Description :获取用户未分类的错题
    * @Date : 2020/3/25
    *
    */
    List<Long> getUserWrongBoxQuestionUncategorized(Long uid, Long boxId) throws DataAccessException;

    /**
    *
    * <AUTHOR> duxiulei
    * @Description :获取用户未分类已消灭错题
    * @Date : 2020/3/26
    *
    */
    List<Long> getWipeOutWrongBoxQuestionUncategorized(Long uid, Long boxId) throws DataAccessException;

    /**
    *
    * <AUTHOR> duxiulei
    * @Description :获取用户错题列表(按照题型分类)
    * @Date : 2020/3/27
    *
    */
    Map<Integer, List<Long>> getUserWrongBoxQuestionAccordingToQType(Long uid, Long boxId) throws DataAccessException;

    /**
    *
    * <AUTHOR> duxiulei
    * @Description :获取用户已消灭错题列表(按照题型分类)
    * @Date : 2020/3/27
    *
    */
    Map<Integer, List<Long>> getWipeOutWrongBoxQuestionAccordingToQType(Long uid, Long boxId) throws DataAccessException;

    /**
    *
    * <AUTHOR> duxiulei
    * @Description :重置用户章节练习
    * @Date : 2020/3/28
    *
    */
    Boolean resetChapterPractice(Long uid, Long boxId, Long teachBookId) throws DataAccessException;


	VirtualHomework saveVirtualHomework( Long boxId,
										 Long uid,
										 Long objId,
										 Integer objType,
										 Integer randomType,
										 Long teachBookId,
										 String homeworkName,
										 List<Long> resultQuestionIds) throws DataAccessException;





    /**
	 * 5.0获取用户已经做过的题目
	 * @param uid
	 * @param boxId
	 * @param teachBookId
	 * @param objType
	 * */
	Map<Long, List<Long>> getNewUserAnswerBoxQuestionInfoBatch(Long uid, Long boxId,
															Long teachBookId, List<Long> objIds, Integer objType)throws DataAccessException;


	Map<Long, List<Long>> getNewUserWrongBoxQuestionInfoBatch(Long uid, Long boxId,
														   Long teachBookId, List<Long> objIds, Integer objType)throws DataAccessException;


	Map<Long, List<Long>> getNewBoxQuestionIdsBatch(Long boxId, Long teachBookId, List<Long> objIds,
												 Integer objType) throws DataAccessException;

	Map<Long, List<Long>> getShuYeQuestionIdsBatch(Long boxId, Long teachBookId, List<Long> objIds,
													Integer objType) throws DataAccessException;

	/**
	 * 获取用户已消灭做错的题目
	 * @param uid
	 * @param boxId
	 * @param objId
	 * @param objType
	 * */
	List<Long> getNewWipeOutWrongBoxQuestionInfo(Long uid, Long boxId,
											  Long teachBookId,Long objId,Integer objType)throws DataAccessException;


	/**
	 * 根据条件获取题库内的题目
	 * @param boxId
	 * @param teachBookId
	 * @param objType (0：所有，1：章节，2：知识点)
	 * @param objId
	 *
	 * */
	List<Long> getNewBoxQuestionIds(Long boxId, Long teachBookId, Long objId,
								 Integer objType) throws DataAccessException;

	/**
	 * 根据条件获取书页小程序的题目
	 * @param boxId
	 * @param teachBookId
	 * @param objType (0：所有，1：章节，2：知识点)
	 * @param objId
	 *
	 * */
	List<Long> getShuyeQuestionIds(Long boxId, Long teachBookId, Long objId,
									Integer objType,Integer origin) throws DataAccessException;



	/**
	 *
	 * <AUTHOR> zhangqiang
	 * @Description :获取用户错题列表(按照题型分类)
	 * @Date : 2021/08/02
	 *
	 */
	Map<Integer, List<Long>> getNewUserWrongBoxQuestionAccordingToQType(Long uid, Long boxId,Long bookId) throws DataAccessException;


	/**
	 *
	 * <AUTHOR> zhangqiang
	 * @Description :获取用户已消灭错题列表(按照题型分类)
	 * @Date : 2021/08/02
	 *
	 */
	Map<Integer, List<Long>> getNewWipeOutWrongBoxQuestionAccordingToQType(Long uid, Long boxId,Long bookId) throws DataAccessException;

	TeachingBook getTeachBookByBoxId(Long boxId);

	String getNewUserWrongQuestions(Long uid, Long boxId, String key) throws DataAccessException ;

	/**
	 *
	 * <AUTHOR> zhangqiang
	 * @Description :获取用户错题列表(其他错题)
	 * @Date : 2021/09/26
	 *
	 */
	List<Long> getOtherWrongBoxQuestion(Long uid, Long boxId,Long bookId) throws DataAccessException;

	/**
	 * 获取题库中所有题目（含非章节练习题目）
	 */
	List<Long> getAllQBoxQuestions(Long bookId) throws DataAccessException;

	boolean checkUserDoneQuesiotnCount(Long uid,Integer count,List<Long> boxIdList) throws DataAccessException;


	/**
	 *
	 * @param uid 用户yid
	 * @param bookId 教材id
	 * @param boxId 题库盒子id
	 * @param wrongQIds 错题id列表
	 * @param wrongTopicIds 错题小题列表
	 * @throws DataAccessException
	 */
	void cacheWrongQuesitonByHomeWorkId(Long uid,Long homeworkId, Long bookId, Long boxId, List<Long> wrongQIds, List<Long> wrongTopicIds) throws DataAccessException;

	VirtualHomework getRightAndWrongRandomBoxQuestionList(Long uid, Long boxId, Long teachBookId, Long objId, Integer objType,
														  List<Integer> qTypes,Integer randomType, Integer num) throws DataAccessException;

    String getQuestionInfoInStringById(Long questionId);

    String getQuestionInfoInStringByIdNew(Long questionId,String questionTitle,String questionContent);

	/**
	 * 题库5.0根据策略随机抽取X道题目
	 * @param num
	 * @param randomType
	 * @param objType
	 * @param objId
	 * @param qTypes (在objType的前提下，增加题型的子过滤项)
	 * @param teachBookId
	 * @param boxId
	 * */
	List<Long> getChapterQuestionNoToken(Long boxId, Long teachBookId, Long objId, Integer objType,
												List<Integer> qTypes,Integer randomType, Integer num) throws DataAccessException;
}
