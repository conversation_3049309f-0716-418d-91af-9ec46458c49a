/*
 * @(#) UserAgreementThriftImpl.java
 * Copyright(c) 欢聚时代科技有限公司
 */
/*
 * Copyright (C) 多玩游戏 ©2005-2012.
 *
 * @# UserAgreementThriftImpl.java
 *
 */
package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.model.CategoryPrivilege;
import cn.huanju.edu100.study.model.UserAgreement;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserAgreementService;
import cn.huanju.edu100.study.service.impl.GoodsThriftServiceImpl;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.ParamUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 *
 *
 * @version 1.0
 * <AUTHOR>
 * @time 2015年5月11日 下午6:40:01
 */
@Component
public class UserAgreementThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(UserAgreementThriftImpl.class);
    private static String SIGN_AGREEMENT_PRE = "sign_agreement_";
    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;
    @Autowired
    private UserAgreementService userAgreementService;
    @Autowired
    private GoodsThriftServiceImpl goodsThriftServiceImpl;
    @Autowired
    private KnowledgeResource knowledgeResource;
    static ExecutorService exec = Executors.newFixedThreadPool(cn.huanju.edu100.study.util.Constants.DEFAULT_POOL_SIZE, new ThreadFactoryBuilder().setNameFormat("userAgreementThreadPool-%d").setDaemon(true).build());
    private static Gson gson = GsonUtil.getGson();

    public response sty_getUserAgreementsByUid(request req) throws BusinessException {
        String entry = "sty_getUserAgreementsByUid";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            UserAgreement userAgreement = gson.fromJson(req.getMsg(), UserAgreement.class);
            userAgreement.initEncAttr();
            if (userAgreement == null || userAgreement.getUid() == null || userAgreement.getUid() <= 0) {
                logger.error("{} fail.paramerter uid is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is null or emtpty.");
            }

            Collection<UserAgreement> result = new ArrayList<UserAgreement>();
            Map<String, Object> params = new HashMap<>();
            if (userAgreement.getUid() == null || userAgreement.getUid() <= 0L) {
                logger.error("listByUid get error, parameter uid is empty");
                throw new DataAccessException("listByUid get error, parameter uid is empty");
            } else {
                params.put("uid", userAgreement.getUid());
            }
            if (userAgreement.getGoodsId() != null){
                params.put("goodsId", userAgreement.getGoodsId());
            }
            if (userAgreement.getOrderId() != null){
                params.put("orderId", userAgreement.getOrderId());
            }
            if (userAgreement.getStatus() != null){
                params.put("status", userAgreement.getStatus());
            }
            Long schId = req.getSchId();
            Long pschId = req.getPschId();
            if (pschId != null && pschId > 0L) {//这里加上父机构的判断条件
                Set<Long> schIds = new HashSet<Long>();
                schIds.add(pschId);
                List<Long> childSchIds = knowledgeResource.getAllSubSchIds(pschId);
                if (childSchIds != null && !childSchIds.isEmpty()) {
                    schIds.addAll(childSchIds);
                }

                if (schId != null && schId > 0L) {
                    //TODO:晓杰要求去掉：Long schIdzz = schId <= 1 ? 2 : schId;//暂时和原来的逻辑保持一致
                    //schIds.add(schIdzz);
                    schIds.add(schId);
                }
                if (schIds != null && !schIds.isEmpty()) {
                    params.put("schIds", new ArrayList<Long>(schIds));
                }
            }else if (schId != null && schId > 0L) {
                params.put("schId", schId);

            }
            result = userAgreementService.getUserAgreementsByUid(params);
            result = result.stream().map(UserAgreement::initAttr).collect(Collectors.toList());
            if (result != null && !result.isEmpty()) {
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
            } else {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserAgreementsByUid return null");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;

    }

    public response sty_getUserAgreementInfoByIdUid(request req) throws BusinessException {
        String entry = "sty_getUserAgreementInfoByIdUid";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            UserAgreement param = gson.fromJson(req.getMsg(), UserAgreement.class);
            param.initEncAttr();

            if (param == null) {
                logger.error("{} fail.paramerter param is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter id is null or emtpty.");
            }
            if (param.getId() == null || param.getId() <= 0 ) {
                logger.error("{} fail.paramerter id is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter id is null or emtpty.");
            }
            UserAgreement result = userAgreementService.getById(param.getId());
            if(result==null){
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_getUserAgreementInfoByIdUid return null");
            }else{
                if(param.getQryUid()!=null && param.getQryUid() ==1){
                    res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                }else{
                    if ( param.getUid() == null || param.getUid() <= 0) {
                        logger.error("{} fail.paramerter uid is null or emtpty.", entry);
                        throw new BusinessException(Constants.PARAM_INVALID, "paramerter id or uid is null or emtpty.");
                    }
                    if (result != null && result.getUid() != null && result.getUid().equals(param.getUid())) {
                        result.initAttr();
                        res.setMsg(GsonUtil.toJson(result, req.getAppid()));
                    } else {
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("sty_getUserAgreementInfoByIdUid return null");
                    }
                }
            }
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;

    }

    /**
     * 更新UserAgreement记录
     *
     * @param req
     * @return
     */
    public response sty_updateUserAgreementByUAObject(request req) throws BusinessException {
        String entry = "sty_updateUserAgreementByUAObject";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAgreement paramUserAgreement = gson.fromJson(req.getMsg(), UserAgreement.class);
            paramUserAgreement.initEncAttr();
            if (null == paramUserAgreement || null == paramUserAgreement.getId() || paramUserAgreement.getId() <= 0 || null == paramUserAgreement.getUid()) {
                logger.error("{} fail.paramerter userAgreementParam or id or uid is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter userAgreementParam or id or uid is null or emtpty.");
            }
            Long paramId = paramUserAgreement.getId();
            Long paramUid = paramUserAgreement.getUid();
            // 先查詢是否存在此记录,根据id查询，然后比较uid
            UserAgreement userAgreementDb = userAgreementService.getById(paramId);
            if (null == userAgreementDb) {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg("sty_updateUserAgreementByUAObject, not exist userAgreement with id:" + paramId);
                endInfo(entry, res, start);
                return res;
            }
            // 参数传入的uid和数据库记录对应的uid不一致
            if (!paramUid.equals(userAgreementDb.getUid())) {
                String errormsg = String.format("sty_updateUserAgreementByUAObject, param's uid:%d not equal with db record uid:%d", paramUid, userAgreementDb.getUid());
                res.setErrormsg(errormsg);
                res.setMsg(gson.toJson(false));
                endInfo(entry, res, start);
                return res;
            }
            int result = userAgreementService.updateByIdUid(paramUserAgreement, userAgreementDb);
            if (result <= 0) {
                res.setMsg(gson.toJson(false));
            } else {
                res.setMsg(gson.toJson(true));
                // 异步清除redis中是否签协议的缓存
                clearSignAgreementFlag(paramUid);
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;

    }

    public response sty_saveUserAgreementList(request req) throws BusinessException {
        String entry = "sty_saveUserAgreementList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<UserAgreement>>() {
            }.getType();
            Collection<UserAgreement> param = GsonUtil.getGson(req.getAppid()).fromJson(req.getMsg(), type);
            param = param.stream().map(UserAgreement::initEncAttr).collect(Collectors.toList());
            if (param == null || param.isEmpty()) {
                logger.error("{} fail.paramerter uid is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is null or emtpty.");
            }
            if (userAgreementService.batchInsert(param)) {
                res.setMsg("true");
                for (UserAgreement userAgreement : param) {
                    Long uid = userAgreement.getUid();
                    if (null != uid) {
                        compatableRedisClusterClient.del(SIGN_AGREEMENT_PRE + uid);
                    }
                }
            } else {
                res.setCode(Constants.SYS_ERROR);
                res.setErrormsg(entry + " return null");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * @param req
     * @return
     */
    public response sty_getUserAgreementListByUidOrderId(request req) throws BusinessException {
        String entry = "sty_getUserAgreementListByUidOrderId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            UserAgreement userAgreement = gson.fromJson(req.getMsg(), UserAgreement.class);
            userAgreement.initEncAttr();
            if (userAgreement == null || userAgreement.getUid() == null || userAgreement.getUid() < 0) {
                logger.error("{} fail.paramerter uid is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is null or emtpty.");
            }
            if (userAgreement.getOrderId() == null || userAgreement.getOrderId() <= 0) {
                logger.error("{} fail.paramerter OrderId is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter OrderId is null or emtpty.");
            }
            Collection<UserAgreement> result = userAgreementService.listByUidOrderIdFromMaster(userAgreement.getUid(),
                    userAgreement.getOrderId());
            result = result.stream().map(UserAgreement::initAttr).collect(Collectors.toList());
            if (result != null) {
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
            } else {
                res.setCode(Constants.OBJ_NOT_EXISTS);
                res.setErrormsg(entry + " return null");
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;

    }

    public response sty_getUnsignAgreementRelateGoodsIdListByUid(request req) throws BusinessException {
        String entry = "sty_getUnsignAgreementRelateGoodsIdListByUid";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAgreement userAgreement = GsonUtil.getGson(req.getAppid()).fromJson(req.getMsg(), UserAgreement.class);
            userAgreement.initEncAttr();
            if (userAgreement == null || userAgreement.getUid() == null || userAgreement.getUid() <= 0) {
                logger.error("{} fail.paramerter uid is null or emtpty.", entry);
                // throw new BusinessException(Constants.PARAM_INVALID,
                // "paramerter uid is null or emtpty.");
                res.setCode(Constants.PARAM_INVALID);
                res.setErrormsg("paramerter uid is null or emtpty.");
            } else {
                Long uid = userAgreement.getUid();
                long starttime = System.currentTimeMillis();

                Collection<Long> goodsIdList = new ArrayList<Long>();
                Long schId = req.getSchId();
                Long pschId = req.getPschId();
                if (pschId != null && pschId > 0L) {//这里加上父机构的判断条件
                    Set<Long> schIds = new HashSet<Long>();
                    schIds.add(pschId);
                    List<Long> childSchIds = knowledgeResource.getAllSubSchIds(pschId);
                    if (childSchIds != null && !childSchIds.isEmpty()) {
                        schIds.addAll(childSchIds);
                    }

                    if (schId != null && schId > 0L) {
                        //TODO:晓杰要求去掉：Long schIdzz = schId <= 1 ? 2 : schId;//暂时和原来的逻辑保持一致
                        //schIds.add(schIdzz);
                        schIds.add(schId);
                    }
                    goodsIdList = userAgreementService.listGoodsIdByStatusUidSchIds(uid, 0, new ArrayList<Long>(schIds));
                }else if (schId != null && schId > 0L) {
                    goodsIdList = userAgreementService.listGoodsIdByStatusUid(uid, 0,schId);
                    //TODO:晓杰要求去掉：schId <= 1 ? 2 : schId);//暂时和原来的逻辑保持一致
                }

//                Collection<Long> goodsIdList = userAgreementService.listGoodsIdByStatusUid(uid, 0,
//                        req.getSchId() <= 1 ? 2 : req.getSchId());
                logger.info("{} elpased:{}", entry, (System.currentTimeMillis() - starttime));
                if (goodsIdList != null && goodsIdList.size() > 0) {
                    res.setMsg(GsonUtil.toJson(goodsIdList));
                }
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * @param req
     * @return
     */
    public response sty_agreementSignFinishedByUid(request req) throws BusinessException {
        String entry = "sty_agreementSignFinishedByUid";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAgreement userAgreement = GsonUtil.getGson(req.getAppid()).fromJson(req.getMsg(), UserAgreement.class);
            userAgreement.initEncAttr();
            if (userAgreement == null || userAgreement.getUid() == null || userAgreement.getUid() <= 0) {
                logger.error("{} fail.paramerter uid is null or emtpty.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "paramerter uid is null or emtpty.");
            }
            Long uid = userAgreement.getUid();
            // 查询redis，判断用户是否签协议
            Boolean signFlag = isSignAgreement(uid);
            if (null != signFlag) {
                res.setMsg(signFlag.toString());
                endInfo(entry, res, start);
                return res;
            }

            long starttime = System.currentTimeMillis();
            Collection<Long> goodsIdList = userAgreementService.listGoodsIdByStatusUid(uid, 0, null);
            logger.info("{} elpased:{}", entry, (System.currentTimeMillis() - starttime));
            // 设置默认值为true
            signFlag = true;
            // res.setMsg("true");//默认值
            if (goodsIdList != null && goodsIdList.size() > 0) {
                starttime = System.currentTimeMillis();
                response response = goodsThriftServiceImpl.generalThriftMethodInvoke(goodsIdList, 10011, 1212,
                        "gds_listPrivilegesByGoodsIdList");
                logger.info("{} elpased:{}", entry, (System.currentTimeMillis() - starttime));
                java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<List<CategoryPrivilege>>() {
                }.getType();
                Collection<CategoryPrivilege> result = GsonUtil.getGson(req.getAppid()).fromJson(response.getMsg(),
                        type);
                if (result != null) {
                    // for (CategoryPrivilege cp : result) {
                    // if (cp.getType() == 0) {
                    // // res.setMsg("false");
                    // signFlag = false;
                    // break;
                    // }
                    // }
                    signFlag = false;
                }
            }
            res.setMsg(signFlag.toString());
            // 将本次判断结果异步保存到redis
            saveSignAgreementFlag(uid, signFlag);
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    /**
     * 将本次判断结果异步保存到redis
     *
     * @param uid
     * @param signFlag
     */

    /**
     * 查询redis，判断用户是否签协议
     *
     * @param uid
     * @return
     */
    private Boolean isSignAgreement(Long uid) {
        if (ValidateUtils.isEmpty(uid)) {
            return false;
        }
        String signKey = SIGN_AGREEMENT_PRE + uid;
        String value = null;
        try {
            value = compatableRedisClusterClient.get(signKey);
        } catch (Exception e) {
            logger.error("isSignAgreement redis exception", e);
            return false;
        }
        if ("true".equals(value)) {
            return true;
        }
        if ("false".equals(value)) {
            return false;
        }
        return false;
    }

    private void saveSignAgreementFlag(final Long uid, final Boolean signFlag) {
        if (ValidateUtils.isEmpty(uid) || ValidateUtils.isEmpty(signFlag)) {
            return;
        }
        exec.execute(new Runnable() {

            @Override
            public void run() {
                String signKey = SIGN_AGREEMENT_PRE + uid;
                compatableRedisClusterClient.set(signKey, signFlag.toString());
            }
        });

    }

    /**
     * 异步清除redis中是否签协议的缓存
     *
     * @param uid
     */
    private void clearSignAgreementFlag(final Long uid) {
        if (ValidateUtils.isEmpty(uid)) {
            return;
        }
        exec.execute(new Runnable() {

            @Override
            public void run() {
                compatableRedisClusterClient.del(SIGN_AGREEMENT_PRE + uid);
            }
        });
    }

    /**
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_delUserAgreement(request req) throws BusinessException {
        String entry = "sty_delUserAgreement";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = GsonUtil.getGson(req.getAppid()).fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long[] goodsIds = ParamUtils.getLongArray(params, "goodsIds");
            Long orderId = ParamUtils.getLong(params, "orderId", true);

            int result = userAgreementService.deleteByUidAndGoodsId(uid, goodsIds, orderId);
            logger.info("{} result : {} elpased:{}", entry, result, (System.currentTimeMillis() - start));
            res.setMsg("true");

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
