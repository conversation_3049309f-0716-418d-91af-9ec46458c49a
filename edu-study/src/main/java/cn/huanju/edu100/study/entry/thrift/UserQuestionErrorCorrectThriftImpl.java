package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.UserQuestionErrorCorrect;
import cn.huanju.edu100.study.service.UserQuestionErrorCorrectService;
import cn.huanju.edu100.study.service.impl.UserQuestionErrorCorrectServiceImpl.RetCode;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class UserQuestionErrorCorrectThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(UserQuestionErrorCorrectThriftImpl.class);
    static Gson gson = GsonUtil.getGson();
    @Autowired
    private UserQuestionErrorCorrectService userQuestionErrorCorrectService;

	/**
     * 用户上报错题
     * */
	public response sty_reportErrQuestionCorrect(request req) throws BusinessException{
		String entry = "sty_reportErrQuestionCorrect";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
        	UserQuestionErrorCorrect param = gson.fromJson(req.getMsg(),UserQuestionErrorCorrect.class);
            if (param == null || param.getUid() == null || param.getUid() <=0
            		|| param.getQuestionId() == null || param.getQuestionId() <=0
            		|| param.getSource() == null || param.getSource() <= 0
            		|| param.getSourceId() == null // || param.getSourceId() <= 0 //有app上报无法获取sourceId，默认0
//            		|| param.getErrorType() == null || param.getErrorType() <= 0
            		|| param.getFirstCategory() == null || param.getFirstCategory() <= 0
            		|| param.getSecondCategory() == null || param.getSecondCategory() <= 0
            		|| param.getCategoryId() == null || param.getCategoryId() <= 0
            		|| param.getErrorTypeList() == null) {

                logger.error("sty_reportErrQuestionCorrect paramter lose, uid or question_id or source_id or error_type_list "
                		+ "or first_category or second_category or category_id is null.");
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_reportErrQuestionCorrect paramter lose, uid or question_id or source_id or error_type_list "
                		+ "or first_category or second_category or category_id is null.");
            }else {
            	param.setErrorTypes(GsonUtil.toJson(param.getErrorTypeList()));
            	int retCode = userQuestionErrorCorrectService.reportErrorQuestionCorrect(param);

            	if (retCode == RetCode.OK) {
            		res.setCode(Constants.SUCCESS);
					res.setMsg("reportErrQuestionCorrect success!");
				}else if (retCode == RetCode.Fail){
					res.setCode(Constants.SYS_ERROR);
					res.setMsg("reportErrQuestionCorrect faild!");
				}else if (retCode == RetCode.FailByMax) {
					res.setCode(Constants.OBJ_ALREADY_EXISTS);
					res.setMsg("reportErrQuestionCorrect already report bigger than max time(10)!");
				}else {
					res.setCode(Constants.SYS_ERROR);
					res.setMsg("reportErrQuestionCorrect faild! retCode Error.");
				}
			}

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
	}

	/**
	 * 用户上报错题
	 * */
	public response sty_getErrQuestionCorrect(request req) throws BusinessException{
		String entry = "sty_getErrQuestionCorrect";
		long start = System.currentTimeMillis();
		enterValidator(entry, start, req);
		response res = new response();
		try {
			UserQuestionErrorCorrect param = gson.fromJson(req.getMsg(),UserQuestionErrorCorrect.class);
			if (param == null || param.getUid() == null || param.getUid() <=0
					|| param.getQuestionId() == null || param.getQuestionId() <=0
					) {

				logger.error("sty_getErrQuestionCorrect param lose, uid or question_id  is null.");
				res.setCode(Constants.PARAM_LOSE);
				res.setErrormsg("sty_getErrQuestionCorrect param lose, uid or question_id  is null.");
			}else {
				List<UserQuestionErrorCorrect> userQuestionErrorCorrectList = userQuestionErrorCorrectService.getUserQuestionErrorCorrect(param);
				if(CollectionUtils.isNotEmpty(userQuestionErrorCorrectList)){
					if(userQuestionErrorCorrectList.size() > 1){
						List<UserQuestionErrorCorrect> sortedList = userQuestionErrorCorrectList.stream()
								.sorted(Comparator.comparing(UserQuestionErrorCorrect::getCreateDate).reversed())
								.collect(Collectors.toList());
						res.setMsg(GsonUtil.toJson(sortedList.get(0), req.getAppid()));//最新创建的一条记录
					}else{
						res.setMsg(GsonUtil.toJson(userQuestionErrorCorrectList.get(0), req.getAppid()));
					}
				}
				res.setCode(Constants.SUCCESS);
			}
		} catch (DataAccessException e) {
			res = dataAccessException(entry, req, e);
		} catch (Exception e) {
			res = exception(entry, req, e);
		}
		endInfo(entry, res, start);
		return res;
	}


}
