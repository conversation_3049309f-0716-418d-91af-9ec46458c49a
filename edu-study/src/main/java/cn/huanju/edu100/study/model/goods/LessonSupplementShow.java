package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 课件的补充点的展现形式扩展
 * 
 * <AUTHOR>
 * @version 2016-06-12
 */
public class LessonSupplementShow extends DataEntity<LessonSupplementShow> {

	private static final long serialVersionUID = 1L;
	
	private Long suppleId;//补充点id
	private String contentText;//文字内容
	private String iconUrl;//图片url
	private int pullW;//图片拉伸的宽
	private int pullH;//图片拉伸的高
	
	public Long getSuppleId() {
		return suppleId;
	}
	public void setSuppleId(Long suppleId) {
		this.suppleId = suppleId;
	}
	public String getContentText() {
		return contentText;
	}
	public void setContentText(String contentText) {
		this.contentText = contentText;
	}
	public String getIconUrl() {
		return iconUrl;
	}
	public void setIconUrl(String iconUrl) {
		this.iconUrl = iconUrl;
	}
	public int getPullW() {
		return pullW;
	}
	public void setPullW(int pullW) {
		this.pullW = pullW;
	}
	public int getPullH() {
		return pullH;
	}
	public void setPullH(int pullH) {
		this.pullH = pullH;
	}

}