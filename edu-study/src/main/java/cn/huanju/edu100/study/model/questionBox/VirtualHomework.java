package cn.huanju.edu100.study.model.questionBox;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 * 用户练习Entity
 * <AUTHOR>
 * @version 2015-08-17
 */
public class VirtualHomework extends DataEntity<VirtualHomework> {


	private static final long serialVersionUID = 1L;
	transient public int tbidx = 0;//分表

	public Long uid;
	public String name;
	public Integer num;
	public Long homeworkTypeId;
	public Integer homeworkType;
	public Long sourceId;
	public Integer sourceType;
	public Long bookId;
	private Integer randomType;

	public List<VirtualHomeworkDetail> virtualHomeworkDetails;

	public List<VirtualHomeworkDetail> getVirtualHomeworkDetails() {
		return virtualHomeworkDetails;
	}
	public void setVirtualHomeworkDetails(
			List<VirtualHomeworkDetail> virtualHomeworkDetails) {
		this.virtualHomeworkDetails = virtualHomeworkDetails;
	}


	public int getTbidx() {
		return tbidx;
	}
	public void setTbidx(int tbidx) {
		this.tbidx = tbidx;
	}

	public Long getBookId() {
		return bookId;
	}
	public void setBookId(Long bookId) {
		this.bookId = bookId;
	}
	public Long getUid() {
		return uid;
	}
	public void setUid(Long uid) {
		this.uid = uid;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getNum() {
		return num;
	}
	public void setNum(Integer num) {
		this.num = num;
	}
	public Long getHomeworkTypeId() {
		return homeworkTypeId;
	}
	public void setHomeworkTypeId(Long homeworkTypeId) {
		this.homeworkTypeId = homeworkTypeId;
	}
	public Integer getHomeworkType() {
		return homeworkType;
	}
	public void setHomeworkType(Integer homeworkType) {
		this.homeworkType = homeworkType;
	}
	public Long getSourceId() {
		return sourceId;
	}
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}
	public Integer getSourceType() {
		return sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}
    public Integer getRandomType() {
        return randomType;
    }
    public void setRandomType(Integer randomType) {
        this.randomType = randomType;
    }


}
