package cn.huanju.edu100.study.model.al;


import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 * 云私塾产品Entity
 * <AUTHOR>
 * @version 2023-05-08
 */
public class ProductAdaptiveLearning extends DataEntity<ProductAdaptiveLearning> {
	
	private static final long serialVersionUID = 1L;
	private Long productId;
	private Integer year;
	private Long teachbookId;
	private Integer assesmentTag;

	private String name;

	private List<Long> idList;

    private Integer planTaskOpenType; //规划路径开启方式，是否手动开启，0默认自动开启 1表示手动开启'

    private Integer needClock;  //是否需要打卡 0：默认不需要打卡 1：需要打卡'

    private Integer version;
    private Integer clientUseVersion;

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getClientUseVersion() {
        return clientUseVersion;
    }

    public void setClientUseVersion(Integer clientUseVersion) {
        this.clientUseVersion = clientUseVersion;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Long getTeachbookId() {
        return teachbookId;
    }

    public void setTeachbookId(Long teachbookId) {
        this.teachbookId = teachbookId;
    }

    public Integer getAssesmentTag() {
        return assesmentTag;
    }

    public void setAssesmentTag(Integer assesmentTag) {
        this.assesmentTag = assesmentTag;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getPlanTaskOpenType() {
        return planTaskOpenType;
    }

    public void setPlanTaskOpenType(Integer planTaskOpenType) {
        this.planTaskOpenType = planTaskOpenType;
    }

    public Integer getNeedClock() {
        return needClock;
    }

    public void setNeedClock(Integer needClock) {
        this.needClock = needClock;
    }
}