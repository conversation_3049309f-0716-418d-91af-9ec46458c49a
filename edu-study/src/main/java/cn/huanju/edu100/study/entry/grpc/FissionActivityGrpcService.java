package cn.huanju.edu100.study.entry.grpc;

import cn.huanju.edu100.grpc.annotation.GrpcService;
import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.server.RequestHandler;
import cn.huanju.edu100.grpc.service.FissionActivityServiceGrpc;
import cn.huanju.edu100.study.client.FissionActivityService;
import com.hqwx.study.entity.wxapp.FissionActivityQuery;
import com.hqwx.study.entity.wxapp.FissionActivitySetting;
import io.grpc.stub.StreamObserver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/20 11:53
 * @description
 */
@GrpcService
@Component
public class FissionActivityGrpcService extends FissionActivityServiceGrpc.FissionActivityServiceImplBase {
    private final RequestHandler requestHandler = new RequestHandler();

    @Autowired
    private FissionActivityService fissionActivityService;

    @Override
    public void getActivityList(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver,
                fissionActivityService::getActivityList, FissionActivityQuery.class);
    }

    @Override
    public void getPushedActivity(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleObjectRequest(request, responseObserver, (obj) -> {
            Long secondCategoryId = Long.parseLong(obj.toString());
            return fissionActivityService.getPushedActivity(secondCategoryId);
        });
    }

    @Override
    public void getActivitySetting(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleMapRequest(request, responseObserver, (map) -> {
            Long activityId = Long.parseLong(map.get("activityId").toString());
            Integer activityType = Integer.parseInt(map.get("activityType").toString());
            Integer settingType = Integer.parseInt(map.get("settingType").toString());
            return fissionActivityService.getActivitySetting(activityId, activityType, settingType);
        });
    }

    @Override
    public void getActivityAllSettings(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleMapRequest(request, responseObserver, (map) -> {
            Long activityId = Long.parseLong(map.get("activityId").toString());
            Long activityType = Long.parseLong(map.get("activityType").toString());
            return fissionActivityService.getActivityAllSettings(activityId, activityType);
        });
    }

    @Override
    public void getAllActivitySettings(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleObjectRequest(request, responseObserver, (obj) -> {
            Integer type = Integer.parseInt(obj.toString());
            return fissionActivityService.getAllActivitySettings(type);
        });
    }

    @Override
    public void setActivitySetting(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver, (setting) -> {
            return fissionActivityService.setActivitySetting(setting);
        }, FissionActivitySetting.class);
    }

    @Override
    public void getActivityJoinNum(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleMapRequest(request, responseObserver, (map) -> {
            Long activityId = Long.parseLong(map.get("activityId").toString());
            Integer activityType = Integer.parseInt(map.get("activityType").toString());
            return fissionActivityService.getActivityJoinNum(activityId, activityType);
        });
    }
}
