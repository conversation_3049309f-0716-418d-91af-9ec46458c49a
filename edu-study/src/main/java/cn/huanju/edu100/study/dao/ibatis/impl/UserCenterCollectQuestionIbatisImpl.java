package cn.huanju.edu100.study.dao.ibatis.impl;


import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserCenterCollectQuestionDao;
import cn.huanju.edu100.study.model.UserCenterCollectHomeworkLessonQuestionBean;
import cn.huanju.edu100.study.model.UserCenterCollectQuestion;
import cn.huanju.edu100.study.model.UserErrorEpaperQuestion;
import cn.huanju.edu100.study.model.UserQuestionCountVo;
import cn.huanju.edu100.util.GsonUtils;
import cn.huanju.edu100.util.IdWorker;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserCenterCollectQuestionIbatisImpl extends CrudIbatisImpl2<UserCenterCollectQuestion> implements UserCenterCollectQuestionDao {

    final static IdWorker idworker=new IdWorker(0,5);

    public UserCenterCollectQuestionIbatisImpl() {
        super("UserCenterCollectQuestion");
    }


    @SuppressWarnings("unchecked")
    @Override
    public long insertSharding(UserCenterCollectQuestion entity) throws DataAccessException {
        if (entity == null) {
            logger.error("add {} error, parameter is null", namespace);
            throw new DataAccessException("add {} error,param is null", namespace);
        }
        SqlMapClient sqlMap = super.getShardingMaster();
        try {
            Number id = idworker.nextId();
            entity.setId(id.longValue());
            sqlMap.insert(namespace.concat(".insertSharding"), entity);
            return entity.getId();
        } catch (SQLException e) {
            logger.error("insert {} SQLException.content:{}", namespace, (new Gson()).toJson(entity), e);
            throw new DataAccessException("add " + namespace + " SQLException fail.");
        }
    }

    @Override
    public List<UserCenterCollectHomeworkLessonQuestionBean> findUserCenterCollectHomeWorkLessonIds(Map<String, Object> queryParam) throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null || queryParam.get("productIds")==null) {
            logger.error("findUserErrorHomeWorkLessonIds {} error, parameter uid is null,param:{}",
                    namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("findUserCenterCollectHomeWorkLessonIds error,parameter error");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<UserCenterCollectHomeworkLessonQuestionBean>) sqlMap.queryForList(namespace.concat(".findUserCenterCollectHomeWorkLessonIds"),queryParam);
        } catch (SQLException e) {
            logger.error("findUserCenterCollectHomeWorkLessonIds {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
            throw new DataAccessException("findUserCenterCollectHomeWorkLessonIds SQLException error"
                    + e.getMessage());
        }
    }

    @Override
    public List<UserCenterCollectHomeworkLessonQuestionBean> findUserCenterCollectHomeWorkQuestionIds(Map<String, Object> queryParam) throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null) {
            logger.error("findUserCenterCollectHomeWorkQuestionIds {} error, parameter uid is null,param:{}", namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("findUserCenterCollectHomeWorkQuestionIds error,parameter error");
        }
        if ((queryParam.get("productId") == null || queryParam.get("lessonIds") == null) && queryParam.get("goodsId") == null) {
            logger.error("findUserCenterCollectHomeWorkQuestionIds {} error, param productId or goodsId is null,param:{}", namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("findUserCenterCollectHomeWorkQuestionIds error,parameter error");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<UserCenterCollectHomeworkLessonQuestionBean>) sqlMap.queryForList(namespace.concat(".findUserCenterCollectHomeWorkQuestionIds"),queryParam);
        } catch (SQLException e) {
            logger.error("findUserCenterCollectHomeWorkQuestionIds {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
            throw new DataAccessException("findUserCenterCollectHomeWorkQuestionIds SQLException error"
                    + e.getMessage());
        }
    }


    @Override
    public List<UserCenterCollectQuestion> findUserCenterCollectEpaperQuestionIds(Map<String, Object> queryParam) throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null || queryParam.get("productIds") == null) {
            logger.error("findUserCenterCollectEpaperQuestionIds {} error, parameter uid is null,param:{}",
                    namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("findUserCenterCollectEpaperQuestionIds error,parameter error");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<UserCenterCollectQuestion>) sqlMap.queryForList(namespace.concat(".findUserCenterCollectEpaperQuestionIds"), queryParam);
        } catch (SQLException e) {
            logger.error("findUserCenterCollectEpaperQuestionIds {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
            throw new DataAccessException("findUserCenterCollectEpaperQuestionIds SQLException error"
                    + e.getMessage());
        }
    }

    @Override
    public Long getUserCenterCollectQuestionCountByUidAndCategory(Map<String, Object> queryParam) throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null || queryParam.get("categoryId") == null) {
            logger.error("getUserCenterCollectQuestionCountByUidAndCategory {} error, parameter uid or categoryId is null,param:{}",
                    namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("getUserCenterCollectQuestionCountByUidAndCategory error,parameter error");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (Long) sqlMap.queryForObject(
                    namespace+".getUserCenterCollectQuestionCountByUidAndCategory", queryParam);
        } catch (SQLException e) {
            logger.error("getUserCenterCollectQuestionCountByUidAndCategory SQLException.param:{}", GsonUtils.toJson(queryParam), e);
            throw new DataAccessException(
                    "getUserCenterCollectQuestionCountByUidAndCategory SQLException error");
        }
    }

    @Override
    public boolean removeUserCollectQuestion(Map<String, Object> queryParam)  throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null ) {
            logger.error("removeUserCollectQuestion {} error, parameter is null", namespace);
            throw new DataAccessException("removeUserCollectQuestion {} error,param is null", namespace);
        }
        SqlMapClient sqlMap = super.getShardingMaster();
        if (sqlMap == null) {
            logger.error(
                    "removeUserCollectQuestion SQLException sqlMap is null");
            throw new DataAccessException(
                    "removeUserCollectQuestion SQLException error, sqlMap is null");
        }
        try {
            return sqlMap.delete(namespace.concat(".removeUserCollectQuestion"), queryParam) > 0;
        } catch (SQLException e) {
            logger.error("removeUserCollectQuestion SQLException.content:{}",
                    (new Gson()).toJson(queryParam), e);
            throw new DataAccessException("removeUserCollectQuestion SQLException fail.");
        }
    }

    @Override
    public List<Long> findUserCenterCollectIdByQuestionIds(Map<String, Object> queryParam) throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null || queryParam.get("questionIds") == null) {
            logger.error("findUserCenterCollectIdByQuestionIds {} error, parameter uid is null,param:{}",
                    namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("findUserCenterCollectIdByQuestionIds error,parameter error");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<Long>) sqlMap.queryForList(namespace.concat(".findUserCenterCollectIdByQuestionIds"),queryParam);
        } catch (SQLException e) {
            logger.error("findUserCenterCollectIdByQuestionIds {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
            throw new DataAccessException("findUserCenterCollectIdByQuestionIds SQLException error"
                    + e.getMessage());
        }
    }

    @Override
    public List<UserQuestionCountVo> getUserCenterCollectQuestionCountByQtype(Map<String, Object> queryParam) throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null ) {
            logger.error("getUserCenterCollectQuestionCountByQtype {} error, parameter uid is null,param:{}", namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("getUserCenterCollectQuestionCountByQtype error,parameter error");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<UserQuestionCountVo>) sqlMap.queryForList(namespace+".getUserCenterCollectQuestionCountByQtype", queryParam);
        } catch (SQLException e) {
            logger.error("getUserCenterCollectQuestionCountByQtype SQLException.param:{}", GsonUtils.toJson(queryParam), e);
            throw new DataAccessException("getUserCenterCollectQuestionCountByQtype SQLException error");
        }
    }

    @Override
    public List<UserErrorEpaperQuestion> getUserCenterCollectQuestionListGroupByQtype(Map<String, Object> queryParam) throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null ) {
            logger.error("getUserCenterCollectQuestionListGroupByQtype {} error, parameter uid is null,param:{}", namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("getUserCenterCollectQuestionListGroupByQtype error,parameter error");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<UserErrorEpaperQuestion>) sqlMap.queryForList(namespace+".getUserCenterCollectQuestionListGroupByQtype", queryParam);
        } catch (SQLException e) {
            logger.error("getUserCenterCollectQuestionListGroupByQtype SQLException.param:{}", GsonUtils.toJson(queryParam), e);
            throw new DataAccessException("getUserCenterCollectQuestionListGroupByQtype SQLException error");
        }
    }


    @Override
    public List<Long> findUserCenterCollectIdByCategoryAndGoodsId(Map<String, Object> queryParam) throws DataAccessException {
        if (queryParam == null || queryParam.get("uid") == null) {
            logger.error("findUserCenterCollectIdByCategoryAndGoodsId {} error, parameter uid is null,param:{}",
                    namespace, GsonUtils.toJson(queryParam));
            throw new DataAccessException("findUserCenterCollectIdByCategoryAndGoodsId error,parameter error");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<Long>) sqlMap.queryForList(namespace.concat(".findUserCenterCollectIdByCategoryAndGoodsId"),queryParam);
        } catch (SQLException e) {
            logger.error("findUserCenterCollectIdByCategoryAndGoodsId {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
            throw new DataAccessException("findUserCenterCollectIdByCategoryAndGoodsId SQLException error"
                    + e.getMessage());
        }
    }

    @Override
    public List<UserCenterCollectQuestion> getUserCenterCollectQuestionList(Long uid, Long goodsId, List<Long> questionIdList, String delFlag) throws DataAccessException {
        if (uid == null) {
            String errorTip = "getUserCenterCollectQuestionList error, param uid is null";
            logger.error(errorTip);
            throw new DataAccessException(errorTip);
        }
        Map<String, Object> queryParam = Maps.newHashMap();
        queryParam.put("uid", uid);
        queryParam.put("goodsId", goodsId);
        queryParam.put("questionIdList", questionIdList);
        queryParam.put("delFlag", delFlag);
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<UserCenterCollectQuestion>) sqlMap.queryForList(namespace.concat(".getUserCenterCollectQuestionList"), queryParam);
        } catch (SQLException e) {
            logger.error("getUserCenterCollectQuestionList {} SQLException param:{}", namespace, GsonUtils.toJson(queryParam), e);
            throw new DataAccessException("getUserCenterCollectQuestionList SQLException error {}", e.getMessage());
        }
    }

    @Override
    public Boolean insertBatch(List<UserCenterCollectQuestion> userCenterCollectQuestions) throws DataAccessException {
        if (CollectionUtils.isEmpty(userCenterCollectQuestions)) {
            logger.error("insertBatch {} error, param is null", namespace);
            throw new DataAccessException("insertBatch {} error,param is null", namespace);
        }
        SqlMapClient sqlMap = super.getShardingMaster();
        try {
            for (UserCenterCollectQuestion item : userCenterCollectQuestions) {
                Number id = idworker.nextId();
                item.setId(id.longValue());
            }
            HashMap<String, Object> param = new HashMap<String, Object>();
            param.put("list", userCenterCollectQuestions);
            sqlMap.insert(namespace.concat(".insertBatch"), param);
            return true;
        } catch (SQLException e) {
            logger.error("insert {} SQLException.content:{}", namespace, GsonUtils.toJson(userCenterCollectQuestions), e);
            throw new DataAccessException("add " + namespace + " SQLException fail.");
        }
    }
}
