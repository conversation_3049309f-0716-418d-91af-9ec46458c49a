package cn.huanju.edu100.study.model.paper;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


@Data
@TableName(value = "reservation_paper",autoResultMap = true)
public class ReservationPaper {
    @TableId(type= IdType.AUTO)
    private Long id;
    private Long uid;
    private Long paperId;
    private Integer paperType;
    private Integer year;
    private Integer week;
    private Long secondCategory;
    private Long categoryId;
    private Date createDate;
    private Date updateDate;
    private String appid;
}
