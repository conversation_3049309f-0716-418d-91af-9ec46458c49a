package cn.huanju.edu100.study.model.enterschooltest;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 *
 *
 * <AUTHOR>
 * @date 2019-07-05 11:53:01
 */
public class EnterSchoolTestQuestion extends DataEntity<EnterSchoolTestQuestion> {
    private static final long serialVersionUID = 1L;
    /**
     * 分组id
     */
    private  Long groupId;
    /**
     * 试卷id
     */
    private  Long paperId;
    private  Long uid;

    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 题目所属科目id
     */
    private Long categoryId;

    /**
     * 排序
     */
    private Integer sort;

    private String groupName;
    private Integer seq;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public Long getGroupId() {
        return this.groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getQuestionId() {
        return this.questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Integer getSort() {
        return this.sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }
}
