package cn.huanju.edu100.study.model.question;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("question_topic_statistic")
@InterceptorIgnore(tenantLine = "1")
public class QuestionTopicStatistic implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    private Long id;
    private Long topicId;//子题目id
    private String errorProneOptionName;//易错项
    private Long answerRightCount;//作答正确数
    private Long answerCount;//作答总数
    private String rightRate;//正确率
    private Integer timeId;//分区字段（日期）
    private Date createDate;//创建时间
    private Date updateDate;//更新时间

}
