package cn.huanju.edu100.study.entry.grpc;

import cn.huanju.edu100.grpc.annotation.GrpcService;
import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.server.RequestHandler;
import cn.huanju.edu100.grpc.service.UserAnswerServiceGrpc;
import cn.huanju.edu100.study.client.UserAnswerService;
import com.hqwx.study.dto.UserAnswerComment;
import com.hqwx.study.entity.UserAnswer;
import io.grpc.stub.StreamObserver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 17:47
 * @description
 */
@GrpcService
@Component
public class UserAnswerGrpcService extends UserAnswerServiceGrpc.UserAnswerServiceImplBase {
    private final RequestHandler requestHandler = new RequestHandler();

    @Autowired
    private UserAnswerService userAnswerService;

    @Override
    public void updateUserAnswer(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleEntityRequest(request, responseObserver, (param) -> {
            return userAnswerService.updateUserAnswer(param);
        }, UserAnswer.class);
    }

    @Override
    public void getUserAnswerCommentList(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleParamListRequest(request, responseObserver, (list) -> {
            Long uid = Long.parseLong(list.get(0).toString());
            Long userAnswerId = Long.parseLong(list.get(1).toString());
            return userAnswerService.getUserAnswerCommentList(uid, userAnswerId);
        });
    }

    @Override
    public void getPaperUserAnswerCommentList(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleObjectRequest(request, responseObserver, (param) -> {
            return userAnswerService.getPaperUserAnswerCommentList(Long.parseLong(param.toString()));
        });
    }

    @Override
    public void getUserAnswerCommentListByUserAnswerIds(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleObjectRequest(request, responseObserver, (param) -> {
            List<Long> userAnswerIds = new Gson().fromJson(param.toString(), new TypeToken<List<Long>>(){}.getType());
            return userAnswerService.getUserAnswerCommentListByUserAnswerIds(userAnswerIds);
        });
    }

    @Override
    public void saveUserAnswerCommentList(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleListRequest(request, responseObserver, (list) -> {
            return userAnswerService.saveUserAnswerCommentList(list);
        }, UserAnswerComment.class);
    }

    @Override
    public void getUserAnswer(GrpcRequest request, StreamObserver<GrpcResponse> responseObserver) {
        requestHandler.handleParamListRequest(request, responseObserver, (list) -> {
            Long uid = Long.parseLong(list.get(0).toString());
            Long userAnswerId = Long.parseLong(list.get(1).toString());
            boolean withSubjectiveQuestionCorrectScore = Boolean.parseBoolean(list.get(2).toString());
            return userAnswerService.getUserAnswer(uid, userAnswerId, withSubjectiveQuestionCorrectScore);
        });
    }
}
