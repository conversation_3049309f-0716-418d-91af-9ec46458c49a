package cn.huanju.edu100.study.model.onetoone;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 面授课室Entity
 * <AUTHOR>
 * @version 2016-04-12
 */
public class VRoom extends DataEntity<VRoom> {

	private static final long serialVersionUID = 1L;
	private String classes;		// classes
//	private Long schId;		// 机构id
	private String name;		// 课室名称
	private String address;		// 课室地址
	private Integer type;		// 课室类型：0线上，1线下
	private Date startTime;		// 开始时间
	private Date endTime;		// 结束时间
	private String ip;		// ip

	public VRoom() {
		super();
	}

	public VRoom(Long id){
		super(id);
	}

	public String getClasses() {
		return classes;
	}

	public void setClasses(String classes) {
		this.classes = classes;
	}

//	public Long getSchId() {
//		return schId;
//	}
//
//	public void setSchId(Long schId) {
//		this.schId = schId;
//	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

}
