package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorWeikeClassDao;
import cn.huanju.edu100.study.model.tutor.TutorWeikeClass;
import cn.huanju.edu100.study.service.tutor.TutorWeikeClassService;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 微课班Service
 * <AUTHOR>
 * @version 2017-12-28
 */
@Service
public class TutorWeikeClassServiceImpl extends BaseServiceImpl<TutorWeikeClassDao, TutorWeikeClass> implements TutorWeikeClassService {


    @Override
    public List<TutorWeikeClass> listByIds(List<Long> idList) throws BusinessException, DataAccessException {
        if (CollectionUtils.isEmpty(idList)) {
            logger.error("illegal param, idList is empty");
            throw new BusinessException(Constants.PARAM_INVALID, "illegal param, idList is empty");
        }


        return dao.listByIds(idList);
    }
}
