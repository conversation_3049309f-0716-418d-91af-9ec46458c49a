package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.TestTaskDao;
import cn.huanju.edu100.study.model.TestTask;
import cn.huanju.edu100.study.service.TestTaskService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TestTaskServiceImpl extends BaseServiceImpl<TestTaskDao, TestTask> implements TestTaskService {
	@Override
	public List<TestTask> findListByTaskIdList(List<Long> taskIdList)	throws DataAccessException {
		return dao.findListByTaskIdList(taskIdList);
	}
}
