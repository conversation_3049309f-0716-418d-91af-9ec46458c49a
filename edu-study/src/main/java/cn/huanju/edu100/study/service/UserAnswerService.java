package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.adminstudy.HomeworkCompletion;
import cn.huanju.edu100.study.model.adminstudy.StudyCompletionInfoQuery;
import cn.huanju.edu100.study.model.adminstudy.StudyReportQuery;
import cn.huanju.edu100.study.model.adminstudy.UserAnswerCompletion;
import cn.huanju.edu100.study.model.dto.UserAnswerCount;
import cn.huanju.edu100.study.model.dto.UserAnswerHomeworkLastInfo;
import cn.huanju.edu100.study.model.dto.UserAnswerLastInfo;
import com.hqwx.goods.dto.HomeWorkSyncDataDTO;
import com.hqwx.study.dto.*;
import com.hqwx.study.dto.query.UserAnswerSumCountQuery;
import com.hqwx.study.dto.query.UserErrorAndCorrectQuestionQuery;
import com.hqwx.study.dto.query.UserHomeworkAnswerQuery;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import com.hqwx.study.vo.ReadOveredHomeworkAnswer;
import com.hqwx.study.vo.ReadOveredPaperAnswer;
import com.hqwx.study.vo.UserErrorAndCorrectQuestionCountVo;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户答题Service
 *
 * <AUTHOR>
 * @version 2015-05-12
 */
public interface UserAnswerService extends BaseService<UserAnswer> {

    /**
     * 提交试卷
     */
    List<UserAnswerDetail> submit(UserAnswer userAnswer, Integer type) throws DataAccessException;

    /**
     * 提交作业
     */
    List<UserAnswerDetail> submitHomeWork(UserHomeWorkAnswer userHomeWorkAnswer, Integer type)
            throws DataAccessException;

    // source来源，1：错题本中消灭错题提交按钮；0：其他
    List<UserAnswerDetail> submitExercise(UserExerciseAnswer userExerciseAnswer, Integer objType, Integer source) throws DataAccessException;

    List<UserHomeWorkAnswer> findUserHomeWorkInfo(Map<String, Double> param, int from, int rows)
            throws DataAccessException;

    int findUserHomeWorkInfoCount(Map<String, Double> param) throws DataAccessException;

    List<UserAnswerCount> findUserHomeWorkInfoCountBatch(Map<String, Object> param) throws DataAccessException;

    UserHomeWorkAnswer getLatestLessonHomeWork(Long uid, Long objId, Integer objType, Long taskId) throws DataAccessException;

    List<UserHomeWorkAnswer> getLatestLessonHomeWorkByObjIds(Long uid, List<Long> objIdList, Integer objType) throws DataAccessException;

    /**
     * 提交个性化任务记录
     */
    @Deprecated // 旧云私塾里的个人任务学习记录的提交
    void submitPersonalTask(UserTaskAnswer userTaskAnswer, List<UserAnswerDetail> details) throws DataAccessException;

    /**
     * 错题作答service
     */
    @Deprecated
    HashMap<Long, Object> submitErrorQuestion(List<UserErrorAnswer> userErrorAnswerList) throws DataAccessException;

    UserHomeWorkAnswer getUserHomeWorkAnswerById(Long uid, Long user_homework_id) throws DataAccessException;

    List<UserHomeWorkAnswer> getUserHomeWorkAnswersByUserHomeworkIds(Long uid, List<Long> homeworkIdList)throws DataAccessException;

    List<UserHomeWorkAnswer> getUserHomeWorkAnswersByIdList(Long uid, List<Long> user_homework_ids) throws DataAccessException;

    /**
     * 提交新个性化任务记录
     */
    @Deprecated
    void submitTutorPersonalTask(UserTaskAnswer userTaskAnswer, List<UserAnswerDetail> details)
            throws DataAccessException;

    List<UserAnswerDetail> findHomeWorkDetailsByObjType(Map<String, Object> queryPara) throws DataAccessException;

    List<UserAnswer> queryUserFinishedPaperByGoodsIdAndPaperIds(Long uid, Long goodsId, List<Long> paperIds) throws DataAccessException;

    List<UserAnswer> getUserAnswersByPreClassExercise(Long uid, List<Long> paperIds) throws DataAccessException;

    List<UserAnswer> getUserAnswersByPaperIdsAndObjType(Long uid, List<Long> paperIds, Integer objType) throws DataAccessException;

    Long countUserFinishedPaperByGoodsIdAndPaperIds(Long uid, Long goodsId, List<Long> paperIds) throws DataAccessException;

    List<UserHomeWorkAnswer> queryUserSubmitHomeworkQuestionsByGoodsIdAndLessonIds(Long uid, Long goodsId, List<Long> lessonIds) throws DataAccessException;

    UserStudyStatistics queryStudyStatisticsByParam(Long uid, Long objId, Long productId, String date) throws DataAccessException;

    Long countUserSubmitHomeworkQuestionsByGoodsIdAndLessonIds(Long uid, Long goodsId, List<Long> lessonIds) throws DataAccessException;

    List<Map> avgCountList(Map<String, Object> params) throws DataAccessException;

    Integer countByStudyReportQuery(StudyReportQuery params) throws DataAccessException;

    List<UserAnswer> findListByStudyReportQuery(StudyReportQuery params) throws DataAccessException;

    List<UserAnswer> adminstudyfindList(UserAnswer params) throws DataAccessException;

    List<StudyDetails> findStudyDetailsList(UserAnswer userAnswer) throws DataAccessException;

    Integer countStudyDetailsList(UserAnswer userAnswer) throws DataAccessException;

    List<HomeworkDetails> findHomeworkDetailsList(UserHomeWorkAnswer userHomeWorkAnswer) throws DataAccessException;

    List<UserHomeWorkAnswer> getHomeworkDetailsList(UserHomeWorkAnswer userHomeWorkAnswer) throws Exception;

    Integer countHomeworkDetails(UserHomeWorkAnswer userHomeWorkAnswer) throws DataAccessException;

    Integer countByStudyReportQueryHomeWork(StudyReportQuery params) throws DataAccessException;

    List<UserHomeWorkAnswer> findListByStudyReportQueryHomeWork(StudyReportQuery params) throws DataAccessException;

    Integer countHomeWork(UserHomeWorkAnswer userHomeWorkAnswer) throws DataAccessException;

    /**
     * 根据参数查询试卷完成情况
     *
     * @param params
     * @return
     */
    List<UserAnswerCompletion> getUserAnswerCompletionList(StudyCompletionInfoQuery params) throws DataAccessException;

    /**
     * 根据参数查询作业完成情况
     *
     * @param params
     * @return
     */
    List<HomeworkCompletion> getHomeworkCompletionList(StudyCompletionInfoQuery params) throws DataAccessException;

    /**
     * 根据参数count试卷完成情况
     *
     * @param params
     * @return
     * @throws DataAccessException
     */
    Integer countUserAnswerCompletion(StudyCompletionInfoQuery params) throws DataAccessException;

    /**
     * <AUTHOR> Gen
     * @Description //统计试卷的答题时长
     * @Date 14:36 2020/12/3
     **/
    Long getPaperStudyLengthByPaperId(Long paperId, Long uid, Long productId) throws DataAccessException;

    Long getUserUseTime(Long uid) throws DataAccessException;

    UserAnswerStudyReportDTO getUserAnswerStudyReportByGoodsId(Long uid, Long goodsId) throws DataAccessException;

    List<Map<String, Object>> findUserAnswerPaperCompleteCount(Long uid, List<Long> paperIds) throws DataAccessException;

    List<Map<String, Object>> findUserAnswerSumTotalCount(Long uid, List<Long> answerIds) throws DataAccessException;

    Map<String, String> findUserHomeworkCompleteCount(Long uid, List<Long> questionIds, Date startTime, Date endTime, Long goodsId) throws DataAccessException;

    List<Long> findUserHomeworkCompleteQuestionIds(Long uid, List<Long> questionIds, Date startTime, Date endTime, Long goodsId) throws DataAccessException;

    List<UserAnswerDetailDTO> getUserAnswerDetailByQuestions(Long uid, List<Long> questionIdList) throws DataAccessException;

    List<UserAnswerSumDTO> getLastUserAnswerSumByQuestionIds(Long uid, List<Long> questionIdList) throws DataAccessException;

    Long countTikuQuestionNum(Long uid, List<Long> boxIds) throws DataAccessException;

    List<UserAnswerHomeworkLastInfo> getUserHomeworkLastAnswerInfo(Long uid, List<Long> lessonIds, Long productId, Long goodsId) throws DataAccessException;

    List<UserAnswerLastInfo> getUserAnswerLastGroupByPaperId(Long uid, List<Long> paperIds, Long goodsId, List<Integer> objTypeList, Integer paperType) throws DataAccessException;

    List<UserHomeWorkAnswer> getUserAnswerHomeworkList(UserHomeWorkAnswer userHomeWorkAnswer) throws DataAccessException;

    boolean syncHomeworkVideoCourse(List<Long> resIdList, Integer zoneIndex, Integer zoneTotal, Integer isClearRedis, Boolean isReadRedis);

    HomeWorkSyncDataDTO getRedisHomeworkSyncdataZone(int zoneIndex);

    void updateStatusHomeworkSyncdataRedis(int status);

    boolean getIsNoCanContinue();

    void syncHomeworkSyncdataResid();

    boolean syncHomeworkProductSchedule(List<Long> resIdList, Integer zoneIndex, Integer zoneTotal, Integer isClearRedis, Boolean isReadRedis);

    HomeWorkSyncDataDTO getRedisHomeworkSyncdataZoneProductSchedule(int zoneIndex);

    void updateStatusHomeworkSyncdataRedisProductSchedule(int status);

    boolean syncHomeworkProductAdaptiveLearning(List<Long> resIdList, Integer zoneIndex, Integer zoneTotal, Integer isClearRedis, Boolean isReadRedis);

    HomeWorkSyncDataDTO getRedisHomeworkSyncdataZoneProductAdaptiveLearning(int zoneIndex);

    void updateStatusHomeworkSyncdataRedisProductAdaptiveLearning(int status);

    boolean syncHomeworkIdVideoCourse(List<Long> resIdList, Integer zoneIndex, Integer zoneTotal, Integer isClearRedis, Boolean isReadRedis);

    HomeWorkSyncDataDTO getRedisHomeworkIdSyncdataZone(int zoneIndex);

    void updateStatusHomeworkIdSyncdataRedis(int status);

    boolean syncHomeworkIdProductSchedule(List<Long> resIdList, Integer zoneIndex, Integer zoneTotal, Integer isClearRedis, Boolean isReadRedis);

    HomeWorkSyncDataDTO getRedisHomeworkIdSyncdataZoneProductSchedule(int zoneIndex);

    void updateStatusHomeworkIdSyncdataRedisProductSchedule(int status);

    boolean syncHomeworkIdProductAdaptiveLearning(List<Long> resIdList, Integer zoneIndex, Integer zoneTotal, Integer isClearRedis, Boolean isReadRedis);

    HomeWorkSyncDataDTO getRedisHomeworkIdSyncdataZoneProductAdaptiveLearning(int zoneIndex);

    void updateStatusHomeworkIdSyncdataRedisProductAdaptiveLearning(int status);

    Boolean syncHomeworkIncreVideoCourse() throws DataAccessException;

    Boolean syncHomeworkIncreProductSchedule() throws DataAccessException;

    Boolean syncHomeworkIncreProductAdaptiveLearning() throws DataAccessException;

    void setUserAnswerHomeworkMaxIdInRedis() throws DataAccessException;

    String getUserAnswerHomeworkMaxIdInRedis();

    Boolean syncHomeworkIdIncreVideoCourseAndProductSchedule() throws DataAccessException;

    void setAlUserStudyPathRealMaxIdInRedis();

    String getAlUserStudyPathRealMaxIdInRedis();

    Boolean syncHomeworkIdIncreProductAdaptiveLearning() throws DataAccessException;

    long getHomeworkAnswerCount(UserAnswerSumCountQuery userAnswerSumCountQuery) throws DataAccessException;

    void delHomeWorkRecord(UserHomeWorkAnswer userHomeWorkAnswer) throws DataAccessException;

    List<UserHomeworkAnswerDTO> findUserHomeWorkAnswerInfosGroupByHomeworkId(UserHomeworkAnswerQuery query) throws DataAccessException;

    List<ReadOveredPaperAnswer> findLastReadOveredSubjectivePaperAnswers(Long uid, List<Long> paperIds, Long goodsId, Long productId) throws DataAccessException;

    /**
     * 获取用户作答记录
     * @param uid 用户id
     * @param userAnswerId 用户作答记录id
     * @param withSubjectiveQuestionCorrectScore 是否需要计算主观题得分
     * @return 用户作答记录
     * @throws DataAccessException 数据访问异常
     */
    UserAnswer getUserAnswer(Long uid, Long userAnswerId, boolean withSubjectiveQuestionCorrectScore) throws DataAccessException;

    UserErrorAndCorrectQuestionCountVo getUserErrorAndCorrectQuestionCount(UserErrorAndCorrectQuestionQuery param) throws DataAccessException;

    Boolean removeCorrectQuestionByCategory(Long uid, Long categoryId, Long goodsId, Long questionId,String startDate,String endDate, Integer sourceType)throws DataAccessException;

    Page<UserCorrectQuestion> getUserCorrectQuestion(UserErrorAndCorrectQuestionQuery param)throws DataAccessException;

    Page<UserSubErrorQuestion> getUserSubErrorQuestion(UserErrorAndCorrectQuestionQuery param)throws DataAccessException;

    List<ReadOveredHomeworkAnswer> findLastReadOveredSubjectiveHomeworkAnswers(Long uid, List<Long> homeworkIds, Long goodsId, Long productId) throws DataAccessException;

    List<UserAnswerComment> getUserAnswerCommentListByAnswerIds(Long uid, List<Long> userAnswerIdList);

    UserErrorAndCorrectQuestionCountDTO getUserCorrectQuestionCount(UserErrorAndCorrectQuestionQuery param)throws DataAccessException;

    List<UserAnswer> getUserAnswerList(UserAnswer userAnswer) throws DataAccessException;
}
