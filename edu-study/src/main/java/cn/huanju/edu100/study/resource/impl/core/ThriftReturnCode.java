package cn.huanju.edu100.study.resource.impl.core;

/**
 * 
 * <AUTHOR>
 *         接口错误类型枚举
 */
public enum ThriftReturnCode {
    SUCCESS(0, "操作成功"), OBJ_NOT_EXISTS(1, "对象不存在"), PARAM_ERROR(2, "参数错误"), PARAM_TOO_BIG(3, "参数太多或太大"), OBJ_ALREADY_EXISTS(
            4, "对象已经存在"), SYS_ERROR(7, "系统错误");

    private int type;

    private String name;

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getNameByType(int type) {
        for (ThriftReturnCode thriftReturnCode : values()) {
            if (thriftReturnCode.getType() == type) {
                return thriftReturnCode.getName();
            }
        }
        return "";
    }

    private ThriftReturnCode(int type, String name) {
        this.type = type;
        this.name = name;
    }
}
