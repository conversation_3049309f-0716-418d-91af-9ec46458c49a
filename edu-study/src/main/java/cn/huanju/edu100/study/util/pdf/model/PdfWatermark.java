package cn.huanju.edu100.study.util.pdf.model;

import cn.huanju.edu100.study.util.pdf.util.HQFileUtil;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: xiaolong
 * @Date: 2024/7/30 23:16
 * @Version: v1.0.0
 * @Description: 水印
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PdfWatermark {
    public static final Integer TYPE_TEXT = 0;
    public static final Integer TYPE_IMAGE = 1;

    private Integer type;
    private String text;
    private ImageData imageData;

    public void setImageData(String imageFile) {
        if(StringUtils.isBlank(imageFile)) {
            return;
        }
        this.imageData = ImageDataFactory.create(HQFileUtil.getResource(imageFile));
    }

}
