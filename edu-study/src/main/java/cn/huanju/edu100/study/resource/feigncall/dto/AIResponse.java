package cn.huanju.edu100.study.resource.feigncall.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("AI服务接口返回")
@Data
public class AIResponse {
    @ApiModelProperty("问题返回code 200表示成功，其他均为失败")
    private  String code;
    @ApiModelProperty("成功失败消息提示")
    private String message;
    @ApiModelProperty("具体回答消息内容")
    private Object outObj;
    @ApiModelProperty("时间戳")
    private Long timestamp;
}
