package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.HomeworkSyncdataResultDao;
import cn.huanju.edu100.study.model.HomeworkSyncdataResult;
import cn.huanju.edu100.util.GsonUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;

public class HomeworkSyncdataResultIbatisImpl extends CrudIbatisImpl2<HomeworkSyncdataResult> implements HomeworkSyncdataResultDao {

    public HomeworkSyncdataResultIbatisImpl() {
        super("HomeworkSyncdataResult");
    }

    @Override
    public long insert(HomeworkSyncdataResult entity) throws DataAccessException {
        if (entity == null) {
            logger.error("insert HomeworkSyncdataResult {} error, parameter is null", namespace);
            throw new DataAccessException("insert HomeworkSyncdataResult {} error,param is null", namespace);
        }
        SqlMapClient sqlMap = super.getMaster();
        try {
            Object id = sqlMap.insert(namespace + ".insert", entity);
            if (id instanceof Long) {
                return (Long) id;
            } else {
                return 0;
            }
        } catch (SQLException e) {
            logger.error("insert HomeworkSyncdataResult {} SQLException.content:{}", namespace, GsonUtils.toJson(entity), e);
            throw new DataAccessException("insert HomeworkSyncdataResult " + namespace + " SQLException fail." + e.getMessage());
        }
    }

}
