package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserRmBoxErrQuestionDao;
import cn.huanju.edu100.study.model.UserRmBoxErrQuestionLog;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.GsonUtils;
import com.google.gson.Gson;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserRmBoxErrQuestionIbatisImpl extends CrudIbatisImpl2<UserRmBoxErrQuestionLog> implements
		UserRmBoxErrQuestionDao {

	public UserRmBoxErrQuestionIbatisImpl() {
		super("UserRmBoxErrQuestionLog");
	}

	@Override
    public List<UserRmBoxErrQuestionLog> findList(UserRmBoxErrQuestionLog entity) throws DataAccessException {
		if (entity == null || entity.getUid()==null) {
			logger.error("findList {} error, parameter uid is null,param:{}",
					namespace, GsonUtils.toJson(entity));
			throw new DataAccessException("findList error,parameter error");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = transBean2Map(entity);
    		int tbidx = super.getUserProfileTbIdx16(entity.getUid());

    		param.put("tbidx", tbidx);
			return (List<UserRmBoxErrQuestionLog>) sqlMap.queryForList(namespace.concat(".findList"),param);
		} catch (SQLException e) {
			logger.error("findList {} SQLException param:{}", namespace, GsonUtils.toJson(entity), e);
			throw new DataAccessException("findList SQLException error"
					+ e.getMessage());
		}
    }

	@Override
	public int insertlogUserBoxRmQuestionBatch(
			List<UserRmBoxErrQuestionLog> logs, Long uid) throws DataAccessException {
		if (logs==null || logs.size() <=0 || uid == null || uid <= 0) {
            logger.error("param error, insertlogUserBoxRmQuestionBatch parameter is empty,UserRmBoxErrQuestionLogList:{}:uid:{}", namespace, logs,uid);
            throw new DataAccessException("insertlogUserBoxRmQuestionBatch param error,UserRmBoxErrQuestionLogList or uid is empty");
        }

    	try{
    		SqlMapClient sqlMap = super.getMaster();
    		int tbidx = super.getUserProfileTbIdx16(uid);
    		for (UserRmBoxErrQuestionLog log : logs) {
    			log.setTbidx(tbidx);
			}
    		HashMap<String, Object> param = new HashMap<String, Object>();
    		param.put("tbidx", tbidx);
    		param.put("list", logs);
    		return sqlMap.update("UserRmBoxErrQuestionLog.insertLogBatch", param);

    	}catch(SQLException e){
    		logger.error("insertlogUserBoxRmQuestionBatch {} SQLException.content:{}", namespace, (new Gson()).toJson(logs), e);
            throw new DataAccessException("insertlogUserBoxRmQuestionBatch SQLException fail.exception:"+e.getMessage());
    	}catch(Exception e){
    		throw new DataAccessException(e);
    	}
	}

	@Override
	public int updatelogUserBoxRmQuestionBatch(
			List<UserRmBoxErrQuestionLog> logs, Long uid) throws DataAccessException {
		if (logs==null || logs.size() <=0 || uid == null || uid <= 0) {
            logger.error("param error, updatelogUserBoxRmQuestionBatch parameter is empty,UserRmBoxErrQuestionLogList:{}:uid:{}", namespace, logs,uid);
            throw new DataAccessException("updatelogUserBoxRmQuestionBatch param error,UserRmBoxErrQuestionLogList or uid is empty");
        }

		SqlMapClient sqlMap = super.getMaster();
		if (sqlMap == null) {
			logger.error("updatelogUserBoxRmQuestionBatch SQLException sqlMap is null");
			throw new DataAccessException(
					"updatelogUserBoxRmQuestionBatch SQLException error, sqlMap is null");
		}
		int tbidx = super.getUserProfileTbIdx16(uid);
        try {
        	int total=0;
        	sqlMap.startTransaction();
            sqlMap.startBatch();
        	for (UserRmBoxErrQuestionLog log : logs) {
        		if (log.getId()!=null) {
        			log.setTbidx(tbidx);
        			sqlMap.update(namespace.concat(".update"), log);
				}
			}
        	total = sqlMap.executeBatch();
        	sqlMap.commitTransaction();
        	logger.info("removeUserErrorQuestionList list size:{},retSize:{}",logs.size(),total);
        	return total;
        } catch (SQLException e) {
            logger.error("removeUserErrorQuestionList SQLException.content:{}",
                    GsonUtil.getGson().toJson(logs), e);
            throw new DataAccessException("removeUserErrorQuestionList SQLException fail.");
        }finally {
           try {
        	   sqlMap.endTransaction();
           } catch (SQLException e) {
        	   logger.error("removeUserErrorQuestionList endTransaction Exception.content:{}",
                       GsonUtil.getGson().toJson(logs), e);
           }
        }
	}

}
