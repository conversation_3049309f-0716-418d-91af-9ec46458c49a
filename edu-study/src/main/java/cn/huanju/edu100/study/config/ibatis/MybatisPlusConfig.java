package cn.huanju.edu100.study.config.ibatis;

import cn.huanju.edu100.study.config.ibatis.limit.LimitInterceptor;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;

import javax.sql.DataSource;
import java.util.List;


/**
 * 单数据源配置（jeecg.datasource.open = false时生效）
 * <AUTHOR>
 *
 */
@Configuration
@MapperScan(value={"cn.huanju.edu100.study.mapper*"})
public class MybatisPlusConfig {

    @Bean
    @Primary
    public DataSource dataSource(DynamicDataSourceProperties properties, List<DynamicDataSourceProvider> providers){
        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource(providers);
        dataSource.setPrimary(properties.getPrimary());
        dataSource.setStrict(properties.getStrict());
        dataSource.setStrategy(properties.getStrategy());
        dataSource.setP6spy(properties.getP6spy());
        dataSource.setSeata(properties.getSeata());
        return dataSource;
    }

    @Bean
    @Order(2)
    public MybatisPlusInterceptor paginationInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        mybatisPlusInterceptor.addInnerInterceptor(paginationInterceptor);;
        return mybatisPlusInterceptor;
    }
    @Bean
    @Order(1)
    public MasterSlavePlugin masterSlavePlugin(){
        return new MasterSlavePlugin(true,true);
    }

    /**
     * 统一添加limit
     *
     * @return
     */
    @Bean
    @Order(3)
    public LimitInterceptor limitInterceptorPlugin() {
        return new LimitInterceptor();
    }

}
