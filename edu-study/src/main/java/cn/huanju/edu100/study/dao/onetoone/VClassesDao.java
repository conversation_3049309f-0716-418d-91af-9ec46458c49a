/**
 *
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.onetoone.VClasses;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 面授班级DAO接口
 * <AUTHOR>
 * @version 2016-04-12
 */
public interface VClassesDao extends CrudDao<VClasses> {

    List<VClasses> findListByIds(List<Long> ids) throws DataAccessException;

	List<VClasses> findListByIds(List<Long> idList, List<Long> schIds) throws DataAccessException;

	List<VClasses> findListByIds(List<Long> idList, Long schId) throws DataAccessException;

    List<VClasses> findListByTeacherUid(Long teacherUid, Integer from, Integer rows, Integer lessonType,
            Integer classStatus) throws DataAccessException;

    List<VClasses> findListByGoodsAndProduct(Long goodsId, Long productId) throws DataAccessException;
}
