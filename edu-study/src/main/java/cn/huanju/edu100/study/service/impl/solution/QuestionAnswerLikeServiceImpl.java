package cn.huanju.edu100.study.service.impl.solution;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.solution.QuestionAnswerDao;
import cn.huanju.edu100.study.dao.solution.QuestionAnswerLikeDao;
import cn.huanju.edu100.study.model.solution.QuestionAnswer;
import cn.huanju.edu100.study.model.solution.QuestionAnswerLike;
import cn.huanju.edu100.study.service.solution.QuestionAnswerLikeService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.HashMap;

@Service
public class QuestionAnswerLikeServiceImpl extends BaseServiceImpl<QuestionAnswerLikeDao, QuestionAnswerLike> implements QuestionAnswerLikeService {
    @Autowired
    private QuestionAnswerDao questionAnswerDao;

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED)
    public Long addAnswerLike(Long answerId, Long userId) throws DataAccessException {
        if (answerId == null || userId == null) {
            return null;
        }
        QuestionAnswer questionAnswer = questionAnswerDao.get(answerId);
        if (questionAnswer == null) {
            return null;
        }
        // 是否已经点赞过
        HashMap<String, Long> likeMap = new HashMap<String, Long>();
        likeMap.put("answerId", answerId);
        likeMap.put("uid", userId);
        QuestionAnswerLike questionAnswerLikeInfo = dao.findUserAnswerLikeByAnswerIdAndUid(likeMap);
        if (questionAnswerLikeInfo != null) {
            return null;
        }

        Long createdTime = Calendar.getInstance().getTimeInMillis();
        QuestionAnswerLike questionAnswerLike = new QuestionAnswerLike();
        questionAnswerLike.setAnswerId(answerId);
        questionAnswerLike.setUserId(userId);
        questionAnswerLike.setCreatedTime(createdTime/1000);
        Long id = dao.insert(questionAnswerLike);
        if (id > 0L) {
            questionAnswerDao.updateLikeNum(answerId, "+1");
        }
        return id;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED)
    public synchronized boolean deleteAnswerLike(Long answerId, Long userId) throws DataAccessException {
        if (answerId == null || userId == null) {
            return false;
        }

        QuestionAnswer questionAnswer = questionAnswerDao.get(answerId);
        if (questionAnswer == null) {
            return false;
        }

        if ((questionAnswer.getLikeNum().intValue() - 1) < 0 ) {
            return false;
        }

        HashMap<String, Long> paramsMap = new HashMap<String, Long>();
        paramsMap.put("answerId", answerId);
        paramsMap.put("uid", userId);
        boolean ret = dao.deleteAnswerLike(paramsMap);
        if (ret == true) {
           questionAnswerDao.updateLikeNum(answerId, "-1");
        }
        return ret;
    }
}
