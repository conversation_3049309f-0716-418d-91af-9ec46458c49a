package cn.huanju.edu100.study.model.questionBox;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 题库盒子Entity
 * <AUTHOR>
 * @version 2015-08-04
 */
public class Questionbox extends DataEntity<Questionbox> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// name
	private Long firstCategory;		// first_category
	private Long secondCategory;		// second_category
	private Long categoryId;		// category_id
	private Integer state;		// 状态 1：已生效 0 ：未生效
	private Long bak1;		// bak1
	private String bak2;		// bak2
	
	
	public Questionbox() {
		super();
	}

	public Questionbox(Long id){
		super(id);
	}

	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Long getFirstCategory() {
		return firstCategory;
	}
	public void setFirstCategory(Long firstCategory) {
		this.firstCategory = firstCategory;
	}
	public Long getSecondCategory() {
		return secondCategory;
	}
	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}
	public Long getCategoryId() {
		return categoryId;
	}

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
	public Integer getState() {
		return state;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	public Long getBak1() {
		return bak1;
	}
	public void setBak1(Long bak1) {
		this.bak1 = bak1;
	}
	public String getBak2() {
		return bak2;
	}
	public void setBak2(String bak2) {
		this.bak2 = bak2;
	}
}