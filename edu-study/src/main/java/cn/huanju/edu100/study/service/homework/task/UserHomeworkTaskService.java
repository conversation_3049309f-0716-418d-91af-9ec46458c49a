package cn.huanju.edu100.study.service.homework.task;

import cn.huanju.edu100.study.model.homework.task.UserHomeworkTask;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hqwx.study.dto.UserHomeworkMaxStateTaskDTO;
import com.hqwx.study.dto.query.UserHomeworkMaxStateTaskQuery;
import com.hqwx.study.dto.query.UserHomeworkTaskQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserHomeworkTaskService extends IService<UserHomeworkTask> {

    List<UserHomeworkTask> getUserHomeworkTaskList(UserHomeworkTaskQuery query);

    IPage<UserHomeworkTask> getUserHomeworkTaskPage(UserHomeworkTaskQuery query);


    boolean updateUserHomeworkTaskAnswer(Long uid, Long goodsId, Long productId,Long lessonId,  Long homeworkId, Long answerId);


    boolean updateUserHomeworkTaskComment(Long uid, Long goodsId, Long productId, Long homeworkId, Long commentId, Double score);

    Integer getCountSubmitCustomHomework(UserHomeworkTaskQuery query);

    List<UserHomeworkMaxStateTaskDTO> getMaxStateUserHomeworkTaskList(UserHomeworkMaxStateTaskQuery query);
}
