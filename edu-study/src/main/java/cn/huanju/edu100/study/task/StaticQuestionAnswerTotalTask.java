package cn.huanju.edu100.study.task;

import cn.huanju.edu100.redis.IRedisLockHandler;
import cn.huanju.edu100.redis.cluster.lock.CompatableRedisClusterLockHandler;
import cn.huanju.edu100.study.dao.QuestionAnswerStaticsDao;
import cn.huanju.edu100.study.model.QuestionAnswerStaticGroup;
import cn.huanju.edu100.study.service.QuestionAnswerStaticsService;
import cn.huanju.edu100.study.util.RedisConsts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 统计题目记录数据----任务应该已经废弃， 注释掉不执行
 * <AUTHOR>
 *
 */
@Component
public class StaticQuestionAnswerTotalTask {
	Logger logger = LoggerFactory.getLogger(StaticQuestionAnswerTotalTask.class);

	@Autowired
	private QuestionAnswerStaticsService questionAnswerStaticsService;

	@Autowired
	private JedisCluster localJedisCluster;

	@Autowired
	private QuestionAnswerStaticsDao questionAnswerStaticsDao;

//	@Scheduled(cron = "0 0 1 * * ?")
	public void run() {
		try {
			logger.info("StaticQuestionAnswerTotalTask start.");
			//多线程竞争更新总表的分布式锁
			IRedisLockHandler lock = new CompatableRedisClusterLockHandler(localJedisCluster);//直接读单一的主redis
			if(lock.tryLock(RedisConsts.getQuestionStaticTotalLock(),5,TimeUnit.SECONDS)){//得到锁，则执行更新总表操作，获取锁的超时时间15s

				//从详情表中按questionId分组获得每个Question的答题人数
				List<QuestionAnswerStaticGroup> groups = questionAnswerStaticsDao.findGroupByQuestionId();
				if (groups!=null) {
					for (QuestionAnswerStaticGroup questionAnswerStaticGroup : groups) {
						if (questionAnswerStaticGroup.getQuestionId()!=null
								&& questionAnswerStaticGroup.getNum()!=null && questionAnswerStaticGroup.getNum()>0) {
							Integer num = questionAnswerStaticsDao.refashQuestionAnswerTotal(questionAnswerStaticGroup.getQuestionId(),questionAnswerStaticGroup.getNum());
							if (num<=0) {
								//TODO：。。。。。
							}
						}
					}
				}

				//TODO：这里是否还需要把每道题的答题统计情况算出来，放到redis里呢？？？

			}
			lock.unLock(RedisConsts.getQuestionStaticTotalLock());//解锁
			logger.info("StaticQuestionAnswerTotalTask end.");
		} catch (Exception e) {
			logger.error("StaticQuestionAnswerTotalTask error.",e);
		}

	}
}
