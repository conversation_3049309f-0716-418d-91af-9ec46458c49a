package cn.huanju.edu100.study.model.user;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 用户配置实体
 * 
 * */
public class UserConfig extends DataEntity<UserConfig> {
	private static final long serialVersionUID = 1L;

	private Long uid;

	private String configKey;

	private String configVal;

	public String getConfigKey() {
		return configKey;
	}

	public void setConfigKey(String configKey) {
		this.configKey = configKey;
	}

	public String getConfigVal() {
		return configVal;
	}

	public void setConfigVal(String configVal) {
		this.configVal = configVal;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
}
