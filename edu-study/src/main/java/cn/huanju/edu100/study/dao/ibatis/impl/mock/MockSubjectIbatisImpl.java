/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.mock;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.mock.MockSubjectDao;
import cn.huanju.edu100.study.model.mock.MockSubject;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 模考科目DAO接口
 * <AUTHOR>
 * @version 2018-04-08
 */
public class MockSubjectIbatisImpl extends CrudIbatisImpl2<MockSubject> implements
		MockSubjectDao {

	public MockSubjectIbatisImpl() {
		super("MockSubject");
	}

	@Override
	public List<MockSubject> qryByMockExamId(Long mockExamId) throws DataAccessException {
		if (mockExamId == null) {
			logger.error("list {} error, parameter mockExamId is null,mockExamId:{}", namespace, mockExamId);
			throw new DataAccessException("list error,entity is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();

			Map<String, Object> param = new HashMap<String, Object>();
			param.put("mockExamId", mockExamId);
			return (List<MockSubject>) sqlMap.queryForList("MockSubject.qryByMockExamId", param);
		} catch (SQLException e) {
			logger.error("list {} SQLException.mockExamId:{}", namespace, mockExamId, e);
			throw new DataAccessException("list SQLException error" + e.getMessage());
		}
	}

    @Override
    public List<MockSubject> findMockSubjectListByMockId(Long mockExamId) throws DataAccessException {
        if (mockExamId == null) {
            logger.error("list {} error, parameter mockExamId is null,mockExamId:{}", namespace, mockExamId);
            throw new DataAccessException("list error,entity is null");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("mockExamId", mockExamId);
            return (List<MockSubject>) sqlMap.queryForList("MockSubject.findMockSubjectListByMockId", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.mockExamId:{}", namespace, mockExamId, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }
}
