package cn.huanju.edu100.study.model.mock;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 模考科目Entity
 * <AUTHOR>
 * @version 2018-04-08
 */
public class MockSubject extends DataEntity<MockSubject> {

	private static final long serialVersionUID = 1L;
	private Long mockExamId;		// 所属模考活动id
	private Long secondCategory;		// 所属考试id
	private Long categoryId;		// 科目id
	private Long paperId;		// 试卷id
	private Long courseId;		// 课程号
	private String intro;		// 课程简介
	private Date mockStartTime;		// 模考开始时间
	private Date mockEndTime;		// 模考结束时间
//	private Date createDate;		// 创建时间
//	private Date updateDate;		// 更新时间

	private Integer applyStatus = 0;	//报名状态（0未报名，1已报名）
	private String categoryName;	//科目名称
	private String alias;			//科目别名

	private Integer sortNum;	//排序值
	private Long totalApply;    //报名总数

	public MockSubject() {
		super();
	}

	public MockSubject(Long id){
		super(id);
	}

	public Long getMockExamId() {
		return mockExamId;
	}

	public void setMockExamId(Long mockExamId) {
		this.mockExamId = mockExamId;
	}

	public Long getSecondCategory() {
		return secondCategory;
	}

	public void setSecondCategory(Long secondCategory) {
		this.secondCategory = secondCategory;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Long getPaperId() {
		return paperId;
	}

	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}

	public String getIntro() {
		return intro;
	}

	public void setIntro(String intro) {
		this.intro = intro;
	}

	public Date getMockStartTime() {
		return mockStartTime;
	}

	public void setMockStartTime(Date mockStartTime) {
		this.mockStartTime = mockStartTime;
	}

	public Date getMockEndTime() {
		return mockEndTime;
	}

	public void setMockEndTime(Date mockEndTime) {
		this.mockEndTime = mockEndTime;
	}

//	public Date getCreateDate() {
//		return createDate;
//	}
//
//	public void setCreateDate(Date createDate) {
//		this.createDate = createDate;
//	}
//
//	public Date getUpdateDate() {
//		return updateDate;
//	}
//
//	public void setUpdateDate(Date updateDate) {
//		this.updateDate = updateDate;
//	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}

	public Long getTotalApply() {
		return totalApply;
	}

	public void setTotalApply(Long totalApply) {
		this.totalApply = totalApply;
	}
}
