package cn.huanju.edu100.study.dao.ibatis.impl.mock;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.mock.TikuMockLearningAdviceDao;
import cn.huanju.edu100.study.model.mock.TikuMockLearningAdvice;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
*
* <AUTHOR> duxiulei
* @Description :TikuMockLearningAdviceIbatisImpl
* @Date : 2020/8/20
*
*/
public class TikuMockLearningAdviceIbatisImpl extends CrudIbatisImpl2<TikuMockLearningAdvice> implements
        TikuMockLearningAdviceDao {
    public TikuMockLearningAdviceIbatisImpl() {
        super("MockLearningAdvice");
    }

    @Override
    public List<TikuMockLearningAdvice> qryByMockExamIdAndMockSubjectId(Long mockExamId, Long mockSubjectId) throws DataAccessException {
        if (mockExamId == null || mockSubjectId == null) {
            logger.error("list {} error, parameter mockExamId or mockSubjectId is null,mockExamId:{},mockSubjectId:{}", namespace, mockExamId,mockSubjectId);
            throw new DataAccessException("list error,entity is null");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("mockExamId", mockExamId);
            param.put("mockSubjectId", mockSubjectId);
            return (List<TikuMockLearningAdvice>) sqlMap.queryForList("MockLearningAdvice.findListByExamIdAndSubjectId", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.mockExamId:{}", namespace, mockExamId, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }
}
