package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.service.user.UserConfigService;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.GsonUtils;
import cn.huanju.edu100.util.ParamUtils;
import com.google.gson.Gson;
import com.hqwx.study.dto.UserConfigDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *
 *
 * @version 1.0
 * <AUTHOR>
 * @time 2018年11月12日 下午5:30:01
 */
@Component
public class UserConfigThriftImpl extends AbstractServiceThrift {
    private static Logger logger = LoggerFactory.getLogger(UserConfigThriftImpl.class);

    @Resource
    private UserConfigService userConfigService;

    private static Gson gson = GsonUtil.getGson();

    public response sty_saveUserConfig(request req) throws BusinessException {
        String entry = "sty_saveUserConfig";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            String configKey = ParamUtils.getString(params, "configKey", false);
            String configVal = ParamUtils.getString(params, "configVal", false);

            if (ValidateUtils.isEmpty(uid) || ValidateUtils.isEmpty(configKey) || ValidateUtils.isEmpty(configVal)) {
                logger.error("{} fail.parameter uid or configKey or configVal is null or empty.",entry);
                throw new BusinessException(Constants.PARAM_LOSE, "parameter uid or configKey or configVal is null or empty");
            }
            userConfigService.saveUserConfig(uid, configKey, configVal);
            res.setMsg("true");
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserConfigByConfigKey(request req) throws BusinessException {
        String entry = "sty_getUserConfigByConfigKey";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();

        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            String configKey = ParamUtils.getString(params, "configKey", false);

            if (ValidateUtils.isEmpty(uid) || ValidateUtils.isEmpty(configKey)) {
                logger.error("{} fail.parameter uid or configKey is null or empty.",entry);
                throw new BusinessException(Constants.PARAM_LOSE, "parameter uid or configKey is null or empty");
            }
            List<UserConfigDTO> userConfigList = userConfigService.findUserConfigByConfigKey(configKey, uid);
            res.setMsg(GsonUtils.toJson(userConfigList));
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }

        endInfo(entry, res, start);
        return res;
    }
}
