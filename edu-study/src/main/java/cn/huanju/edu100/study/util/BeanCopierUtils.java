package cn.huanju.edu100.study.util;

import cn.huanju.edu100.study.model.UserGenerateExerciseAnswer;
import net.sf.cglib.beans.BeanCopier;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @desc 高性能对象拷贝工具类
 * Created by linyoulong on 2017/9/15.
 */
public class BeanCopierUtils {

    public static void main(String[] argds){
        UserGenerateExerciseAnswer a = new UserGenerateExerciseAnswer();
        UserGenerateExerciseAnswer b = new UserGenerateExerciseAnswer();
        BeanCopierUtils.copyProperties(a,b);
    }

    public static Map<String,BeanCopier> beanCopierMap = new HashMap<String, BeanCopier>();

    public static void copyProperties(Object source, Object target){
        BeanUtils.copyProperties(source,target);
//        String beanKey =  generateKey(source.getClass(), target.getClass());
//        BeanCopier copier =  null;
//        if(!beanCopierMap.containsKey(beanKey)){
//            copier = BeanCopier.create(source.getClass(), target.getClass(), false);
//            beanCopierMap.put(beanKey, copier);
//        }else{
//            copier = beanCopierMap.get(beanKey);
//        }
//        copier.copy(source, target, new DateConverterBeanCopier());
    }
    private static String generateKey(Class<?> class1,Class<?>class2){
        return class1.toString() + class2.toString();
    }
}
