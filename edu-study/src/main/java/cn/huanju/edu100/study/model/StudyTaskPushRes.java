/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 学习任务推荐资源
 * 
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudyTaskPushRes extends DataEntity<StudyTaskPushRes> {

	private static final long serialVersionUID = 1L;
	private Long taskId; // task_id
	private Long resId;		//资源ID
	private String resType; // res_type
	private Long uid;	//用户UID
	private Long knowledgeId;	//知识点ID

	public StudyTaskPushRes() {
		super();
	}

	public StudyTaskPushRes(Long id) {
		super(id);
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public Long getResId() {
		return resId;
	}

	public void setResId(Long resId) {
		this.resId = resId;
	}

	public String getResType() {
		return resType;
	}

	public void setResType(String resType) {
		this.resType = resType;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getKnowledgeId() {
		return knowledgeId;
	}

	public void setKnowledgeId(Long knowledgeId) {
		this.knowledgeId = knowledgeId;
	}

}