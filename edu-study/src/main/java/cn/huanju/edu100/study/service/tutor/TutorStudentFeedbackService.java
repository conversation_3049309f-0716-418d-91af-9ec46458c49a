package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorFeedbackWrap;
import cn.huanju.edu100.study.model.tutor.TutorStudentFeedback;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Map;

/**
 * 老师回访记录Service
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentFeedbackService extends BaseService<TutorStudentFeedback> {

    TutorFeedbackWrap listFeedBackByUid(Map<String, Object> params)
            throws DataAccessException;

}
