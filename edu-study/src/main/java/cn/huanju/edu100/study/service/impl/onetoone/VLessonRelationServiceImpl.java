package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.VLessonRelationDao;
import cn.huanju.edu100.study.model.onetoone.VLessonRelation;
import cn.huanju.edu100.study.service.onetoone.VLessonRelationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 直播课节对应关系表Service
 * <AUTHOR>
 * @version 2017-11-22
 */
@Service
public class VLessonRelationServiceImpl extends BaseServiceImpl<VLessonRelationDao, VLessonRelation> implements VLessonRelationService {

    @Override
    public List<VLessonRelation> findListByLiveLessonIdList(List<Long> liveLessonIdList,Integer type) throws DataAccessException {
        return dao.findListByLiveLessonIdList(liveLessonIdList, type);
    }

    @Override
    public List<VLessonRelation> findListByParam(VLessonRelation param) throws DataAccessException {
        return dao.findList(param);
    }
}
