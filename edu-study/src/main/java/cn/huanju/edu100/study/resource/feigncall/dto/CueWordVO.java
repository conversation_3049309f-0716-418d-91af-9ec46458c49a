package cn.huanju.edu100.study.resource.feigncall.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: com.hqwx.study.assistant.domain.vo.CueWordVO
 * @description:
 * @author: Mr.<PERSON>
 * @create: 2024-01-17 14:58
 */
@Data
@ApiModel("文档知识")
public class CueWordVO {
    @ApiModelProperty("入口id")
    private Long entryId;
    @ApiModelProperty("入口名字")
    private String entryName;

    @ApiModelProperty("场景id")
    private Long sceneId;

    @ApiModelProperty("场景名称")
    private String sceneName;

    @ApiModelProperty("业务id")
    private Long businessSceneId;
    @ApiModelProperty("业务场景名称")
    private String businessSceneName;

    @ApiModelProperty("页面显示名称")
    private String frontName;
    @ApiModelProperty("是否显示")
    private int isShow;
    @ApiModelProperty("排序")
    private int sort;
    @ApiModelProperty("详细描述")
    private String frontRemark;
    @ApiModelProperty("招呼语")
    private String greetingMessage;
    @ApiModelProperty("类型，0未知，1专业学习")
    private Integer type;
}
