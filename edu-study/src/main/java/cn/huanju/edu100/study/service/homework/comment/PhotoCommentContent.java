package cn.huanju.edu100.study.service.homework.comment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class PhotoCommentContent {
    @JsonProperty("imgUrl")
    private String imgUrl;

    @JsonProperty("imgWidth")
    private Integer imgWidth;

    @JsonProperty("imgHeight")
    private Integer imgHeight;
    
    @JsonProperty("relationQuestion")
    private List<RelationQuestion> relationQuestion;
    


    @Data
    public static class RelationQuestion {
        @JsonProperty("imgIndex")
        private String imgIndex;

        @JsonProperty("smallQuestionScoreAnalysis")
        private String smallQuestionScoreAnalysis;

        @JsonProperty("smallQuestionScore")
        private Double smallQuestionScore;

        @JsonProperty("question")
        private String question;

        @JsonProperty("position")
        private String position;

        @JsonProperty("smallQuestionComment")
        private String smallQuestionComment;

        @JsonProperty("smallQuestionTotalScore")
        private Double smallQuestionTotalScore;
    }

}
