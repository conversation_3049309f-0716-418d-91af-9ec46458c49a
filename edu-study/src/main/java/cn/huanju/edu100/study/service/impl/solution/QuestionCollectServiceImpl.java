package cn.huanju.edu100.study.service.impl.solution;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.solution.QuestionCollectDao;
import cn.huanju.edu100.study.dao.solution.SolutionQuestionDao;
import cn.huanju.edu100.study.model.PageModel;
import cn.huanju.edu100.study.model.solution.SolutionQuestion;
import cn.huanju.edu100.study.model.solution.QuestionCollect;
import cn.huanju.edu100.study.service.solution.QuestionCollectService;
import cn.huanju.edu100.study.service.solution.SolutionQuestionService;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class QuestionCollectServiceImpl extends BaseServiceImpl<QuestionCollectDao,QuestionCollect> implements QuestionCollectService  {

    private static Logger logger = LoggerFactory.getLogger(QuestionCollectServiceImpl.class);

    @Autowired
    private SolutionQuestionDao solutionQuestionDao; //问题数据层

    @Autowired
    private SolutionQuestionService solutionQuestionService;

    /**
     * 收藏问题
     * @param uid
     * @param questionId
     * @return
     * @throws DataAccessException
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED)
    public boolean collectQuestion(Long uid, Long questionId,Integer isAl) throws DataAccessException, BusinessException {
        if(ValidateUtils.isEmpty(uid) || ValidateUtils.isEmpty(questionId)){
            logger.error("collectQuestion fail, uid or questionId is null");
            throw new BusinessException(Constants.PARAM_INVALID,"collectQuestion fail, uid or questionId is null");
        }
        SolutionQuestion question = solutionQuestionDao.get(questionId);
        if(question == null){
            String errMsg = String.format("collectQuestion fail, questionId:%d is not exsit", questionId);
            logger.error(errMsg);
            throw new BusinessException(Constants.OBJ_NOT_EXISTS,errMsg);
        }

        QuestionCollect questionCollect = new QuestionCollect();
        questionCollect.setQuestionId(questionId);
        questionCollect.setUserId(uid);
        List<QuestionCollect> questionCollectList =  this.dao.findList(questionCollect);
        if(CollectionUtils.isEmpty(questionCollectList) ==  false){
            String errMsg = String.format("collectQuestion fail, uid:%d,questionId:%d is exsit", uid, questionId);
            logger.error(errMsg);
            throw new BusinessException(Constants.OBJ_ALREADY_EXISTS,errMsg);
        }

        questionCollect.setSecondCategory(question.getSecondCategory());
        questionCollect.setCreateTime((System.currentTimeMillis() / 1000));
        questionCollect.setIsAl(isAl);
        long id =  this.insert(questionCollect);
        if( id <= 0 ){
            String errMsg = String.format("collectQuestion [uid:%d,questionId:%d] insert fail", uid, questionId);
            logger.error(errMsg);
            throw new BusinessException(Constants.SYS_ERROR,errMsg);
        }
        // 更新问题收藏数
        HashMap<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("collectionNum", "+1");
        paramMap.put("id", questionId);
        solutionQuestionDao.updateCollectionNum(paramMap);
        return true;

    }

    /**
     * 取消收藏问题
     * @param uid
     * @param questionId
     * @return
     * @throws DataAccessException
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED)
    public synchronized boolean cancelCollectQuestion(Long uid, Long questionId) throws DataAccessException,BusinessException {
        if(ValidateUtils.isEmpty(uid) || ValidateUtils.isEmpty(questionId)){
            logger.error("cancelCollectQuestion fail, uid or questionId is null");
            throw new BusinessException(Constants.PARAM_INVALID,"cancelCollectQuestion fail, uid or questionId is null");
        }
        SolutionQuestion question = solutionQuestionDao.get(questionId);
        if(question == null){
            String errMsg = String.format("collectQuestion fail, questionId:%d is not exsit", questionId);
            logger.error(errMsg);
            throw new BusinessException(Constants.OBJ_NOT_EXISTS,errMsg);
        }

        if ((question.getCollectionNum() - 1) < 0) {
            String errMsg = String.format("cancelCollectQuestion [uid:%d,questionId:%d, CollectionNum:%d] delete fail", uid, questionId, question.getCollectionNum());
            logger.error(errMsg);
            throw new BusinessException(Constants.SYS_ERROR,errMsg);
        }

        QuestionCollect questionCollect = new QuestionCollect();
        questionCollect.setQuestionId(questionId);
        questionCollect.setUserId(uid);
        List<QuestionCollect> questionCollectList =  this.dao.findList(questionCollect);
        if(CollectionUtils.isEmpty(questionCollectList)){
            String errMsg = String.format("cancelCollectQuestion fail, uid:%d,questionId:%d is not exsit", uid, questionId);
            logger.error(errMsg);
            throw new BusinessException(Constants.OBJ_NOT_EXISTS,errMsg);
        }

        QuestionCollect questionCollectTmp = questionCollectList.get(0);
        boolean rst  = this.delete(questionCollectTmp.getId());

        if( rst == false ){
            String errMsg = String.format("cancelCollectQuestion [uid:%d,questionId:%d] delete fail", uid, questionId);
            logger.error(errMsg);
            throw new BusinessException(Constants.SYS_ERROR,errMsg);
        }
        // 更新问题收藏数
        HashMap<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("collectionNum", "-1");
        paramMap.put("id", questionId);
        solutionQuestionDao.updateCollectionNum(paramMap);
        return true;
    }

    @Override
    public PageModel getUserCollectQuestionList(Long uid, Long secondCategory, Integer from, Integer rows, Integer isAl,Integer sourceType) throws DataAccessException, BusinessException {
        Page<QuestionCollect> page =  new Page<QuestionCollect>();
        QuestionCollect questionCollect = new QuestionCollect();
        questionCollect.setUserId(uid);
        questionCollect.setSecondCategory(secondCategory);
        questionCollect.setIsAl(isAl);
        List<QuestionCollect> questionCollectList = null;
        PageModel pageModel = new PageModel();
        if(sourceType == null || sourceType<= 0){
            Integer total = this.dao.findListCount(page, questionCollect);
            pageModel.setTotal(total);
            page.setFrom(from);
            page.setPageSize(rows);
            page.setOrderBy("createTime desc");
            questionCollectList = this.dao.findList(page,questionCollect);
            if (CollectionUtils.isEmpty(questionCollectList)) {
                return pageModel;
            }
        } else {
            questionCollect.setSourceType(sourceType);
            Integer total = this.dao.findListWithSourceTypeCount(page, questionCollect);
            pageModel.setTotal(total);
            page.setFrom(from);
            page.setPageSize(rows);
            page.setOrderBy("sqc.createTime desc");
            questionCollectList = this.dao.findListWithSourceType(page, questionCollect);
            if (CollectionUtils.isEmpty(questionCollectList)) {
                return pageModel;
            }
        }

       if(CollectionUtils.isEmpty(questionCollectList)){
           return pageModel;
       }

        ArrayList<Long> questionIdsArray = new ArrayList<Long>();
        for (QuestionCollect collect : questionCollectList) {
            questionIdsArray.add(collect.getQuestionId());
        }
        List<SolutionQuestion> questionList = solutionQuestionService.getQuestionListByIdsOrderById(questionIdsArray);
        pageModel.setList(questionList);
        return pageModel;
    }

}
