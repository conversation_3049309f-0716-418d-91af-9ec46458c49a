/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.mock;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.mock.MockExamDao;
import cn.huanju.edu100.study.model.mock.MockExam;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 模考活动DAO接口
 * <AUTHOR>
 * @version 2018-04-08
 */
public class MockExamIbatisImpl extends CrudIbatisImpl2<MockExam> implements
		MockExamDao {

	public MockExamIbatisImpl() {
		super("MockExam");
	}

	@Override
	public List<MockExam> qryBySecCategoryAndStatus(Long secondCategory, Integer status) throws DataAccessException {
		if (secondCategory == null) {
			logger.error("list {} error, parameter secondCategoryis null,secondCategory:{}", namespace, secondCategory);
			throw new DataAccessException("list error,entity is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();

			Map<String, Object> param = new HashMap<String, Object>();
			param.put("secondCategory", secondCategory);
			param.put("status", status);
			return (List<MockExam>) sqlMap.queryForList("MockExam.qryBySecCategoryAndStatus", param);
		} catch (SQLException e) {
			logger.error("list {} SQLException.secondCategory:{}", namespace, secondCategory, e);
			throw new DataAccessException("list SQLException error" + e.getMessage());
		}
	}

    @Override
    public List<MockExam> findMockExamListBySecCategoryAndStatusAndAreaId(Long secondCategory, Integer status, Long areaId) throws DataAccessException {
        if (secondCategory == null) {
            logger.error("list {} error, parameter secondCategoryis null,secondCategory:{}", namespace, secondCategory);
            throw new DataAccessException("list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            if (areaId != null && areaId.intValue() != 0) {
                param.put("areaId", areaId);
            }
            param.put("secondCategory", secondCategory);
            param.put("status", status);
            List<MockExam> mockExamList = new ArrayList<MockExam>();
            mockExamList = (List<MockExam>) sqlMap.queryForList("MockExam.findMockExamListBySecondCategoryAndStatusAndAreaId", param);

            if (areaId == null && mockExamList.size() == 0) {
                mockExamList = (List<MockExam>) sqlMap.queryForList("MockExam.findLastestMockExamListBySecondCategory", param);
            }
            return mockExamList;
        } catch (SQLException e) {
            logger.error("list {} SQLException.secondCategory:{}", namespace, secondCategory, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public List<Long> findMockAreaIdListBySecondCategoryId(Long secondCategory) throws DataAccessException{
        if (secondCategory == null) {
            logger.error("list {} error, parameter secondCategoryis null,secondCategory:{}", namespace, secondCategory);
            throw new DataAccessException("list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();

            param.put("secondCategory", secondCategory);

            List<Long> areaIdList = new ArrayList<Long>();
            areaIdList = (List<Long>) sqlMap.queryForList("MockExam.findAreaIdListBySecondCategory", param);

            return areaIdList;
        } catch (SQLException e) {
            logger.error("list {} SQLException.secondCategory:{}", namespace, secondCategory, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

    @Override
    public List<MockExam> findMyMockList(Long secondCategory, Long uid, int from, int rows) throws DataAccessException {
        if (secondCategory == null || uid == null) {
            logger.error("list {} error, parameter secondCategory or uid is null,secondCategory:{},uid :{}", namespace, secondCategory,uid);
            throw new DataAccessException("list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("secondCategory", secondCategory);
            param.put("uid", uid);
            param.put("from", from);
            param.put("rows", rows);

            List<MockExam> mockExamList = new ArrayList<MockExam>();
            mockExamList = (List<MockExam>) sqlMap.queryForList("MockExam.findMyMockList", param);

            return mockExamList;
        } catch (SQLException e) {
            logger.error("list {} SQLException.secondCategory:{}", namespace, secondCategory, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }
}
