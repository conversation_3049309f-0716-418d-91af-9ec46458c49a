package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * goods_group_contentEntity
 * 
 * <AUTHOR>
 * @version 2015-05-29
 */
public class GoodsGroupContent extends DataEntity<GoodsGroupContent> {

    private static final long serialVersionUID = 1L;
    private Long groupId; // group_id
    private Long goodsId; // goods_id
    private Integer type; // 默认值是0,0表示and,1表示or
    private Integer sort; // 排序

    private Goods goods;

    public GoodsGroupContent() {
        super();
    }

    public GoodsGroupContent(Long id) {
        super(id);
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getSort() {
        return sort;
    }

    public Goods getGoods() {
        return goods;
    }

    public void setGoods(Goods goods) {
        this.goods = goods;
    }

}