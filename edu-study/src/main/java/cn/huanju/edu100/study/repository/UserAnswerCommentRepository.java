package cn.huanju.edu100.study.repository;

import cn.huanju.edu100.study.mapper.paper.UserAnswerCommentMapper;
import cn.huanju.edu100.study.model.paper.UserAnswerCommentPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqwx.study.dto.UserAnswerComment;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/16 14:06
 * @description
 */
@Repository
public class UserAnswerCommentRepository extends ServiceImpl<UserAnswerCommentMapper, UserAnswerCommentPO> {

    public List<UserAnswerComment> selectByAnswerId(Long uid, Long answerId) {
        LambdaQueryWrapper<UserAnswerCommentPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAnswerCommentPO::getAnswerId, answerId);
        queryWrapper.eq(UserAnswerCommentPO::getUid, uid);
        List<UserAnswerCommentPO> poList = getBaseMapper().selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(poList)) {
            return poList.stream().map(po -> { return (UserAnswerComment)po; }).toList();
        }
        return List.of();
    }

    public List<UserAnswerComment> selectByPaperId(Long paperId) {
        LambdaQueryWrapper<UserAnswerCommentPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAnswerCommentPO::getPaperId, paperId);
        queryWrapper.orderByDesc(UserAnswerCommentPO::getAnswerId);
        List<UserAnswerCommentPO> poList = getBaseMapper().selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(poList)) {
            return poList.stream().map(po -> { return (UserAnswerComment)po; }).toList();
        }
        return List.of();
    }

    public List<UserAnswerComment> selectByAnswerIdList(Long uid, List<Long> userAnswerIdList) {
        if (CollectionUtils.isEmpty(userAnswerIdList)) {
            return List.of();
        }
        LambdaQueryWrapper<UserAnswerCommentPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserAnswerCommentPO::getAnswerId, userAnswerIdList);
        queryWrapper.eq(UserAnswerCommentPO::getUid, uid);
        List<UserAnswerCommentPO> poList = getBaseMapper().selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(poList)) {
            return poList.stream().map(po -> { return (UserAnswerComment)po; }).toList();
        }
        return List.of();
    }

    public List<UserAnswerComment> getUserAnswerCommentListByUserAnswerIds(List<Long> userAnswerIds) {
        LambdaQueryWrapper<UserAnswerCommentPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserAnswerCommentPO::getAnswerId, userAnswerIds);
        List<UserAnswerCommentPO> poList = getBaseMapper().selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(poList)) {
            return poList.stream().map(po -> { return (UserAnswerComment)po; }).toList();
        }
        return List.of();
    }
}
