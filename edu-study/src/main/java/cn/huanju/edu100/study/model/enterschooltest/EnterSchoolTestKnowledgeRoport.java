package cn.huanju.edu100.study.model.enterschooltest;

import cn.huanju.edu100.persistence.model.DataEntity;
import com.google.common.collect.Sets;

import java.util.Set;

public class EnterSchoolTestKnowledgeRoport extends DataEntity<EnterSchoolTestKnowledgeRoport> {

    private Integer questionTotal;
    private Integer topicTotal;

    private Integer rightQuestionTotal;
    private Integer answeredQuestionTotal;

    private Integer rightTopicTotal;
    private Integer answeredTopicTotal;

    private String knowledgeName;
    private String chapterName;
    private String childChapterName;

    private Long knowledgeGraphId;
    private Set<Long> questionIds = Sets.newHashSet();
    private Set<Long> topicIds = Sets.newHashSet();


    public Long getKnowledgeGraphId() {
        return knowledgeGraphId;
    }

    public void setKnowledgeGraphId(Long knowledgeGraphId) {
        this.knowledgeGraphId = knowledgeGraphId;
    }

    public Integer getQuestionTotal() {
        return questionIds.size();
    }

    private void setQuestionTotal(Integer questionTotal) {
        this.questionTotal = questionTotal;
    }

    public Integer getRightQuestionTotal() {
        return rightQuestionTotal;
    }

    public void setRightQuestionTotal(Integer rightQuestionTotal) {
        this.rightQuestionTotal = rightQuestionTotal;
    }

    public String getKnowledgeName() {
        return knowledgeName;
    }

    public void setKnowledgeName(String knowledgeName) {
        this.knowledgeName = knowledgeName;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public String getChildChapterName() {
        return childChapterName;
    }

    public void setChildChapterName(String childChapterName) {
        this.childChapterName = childChapterName;
    }

    public Set<Long> getQuestionIds() {
        return questionIds;
    }

    public void setQuestionIds(Set<Long> questionIds) {
        this.questionIds = questionIds;
    }

    public Integer getAnsweredQuestionTotal() {
        return answeredQuestionTotal;
    }

    public void setAnsweredQuestionTotal(Integer answeredQuestionTotal) {
        this.answeredQuestionTotal = answeredQuestionTotal;
    }

    public Integer getRightTopicTotal() {
        return rightTopicTotal;
    }

    public void setRightTopicTotal(Integer rightTopicTotal) {
        this.rightTopicTotal = rightTopicTotal;
    }

    public Integer getAnsweredTopicTotal() {
        return answeredTopicTotal;
    }

    public void setAnsweredTopicTotal(Integer answeredTopicTotal) {
        this.answeredTopicTotal = answeredTopicTotal;
    }

    public Set<Long> getTopicIds() {
        return topicIds;
    }

    public void setTopicIds(Set<Long> topicIds) {
        this.topicIds = topicIds;
    }

    public Integer getTopicTotal() {
        return topicIds.size();
    }

    private void setTopicTotal(Integer topicTotal) {
        this.topicTotal = topicTotal;
    }
}
