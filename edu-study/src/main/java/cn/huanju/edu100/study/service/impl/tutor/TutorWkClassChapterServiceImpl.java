package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorWkClassChapterDao;
import cn.huanju.edu100.study.model.tutor.TutorWkClassChapter;
import cn.huanju.edu100.study.service.tutor.TutorWkClassChapterService;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 微课班章节Service
 * <AUTHOR>
 * @version 2017-12-28
 */
@Service
public class TutorWkClassChapterServiceImpl extends BaseServiceImpl<TutorWkClassChapterDao, TutorWkClassChapter> implements TutorWkClassChapterService {

    @Override
    public List<TutorWkClassChapter> findListByParam(Long wkClassId, List<Long> parentIdList, List<Long> idList) throws BusinessException, DataAccessException {
        if (!IdUtils.isValid(wkClassId) && CollectionUtils.isEmpty(parentIdList) && CollectionUtils.isEmpty(idList)) {
            logger.error("findListByParam fail, all param is illegal, wkClassId, parentIdList, idList");
            throw new BusinessException(Constants.PARAM_INVALID, "findListByParam fail, all param is illegal, wkClassId, parentIdList, idList");
        }


        return dao.findListByParam(wkClassId, parentIdList, idList);
    }
}
