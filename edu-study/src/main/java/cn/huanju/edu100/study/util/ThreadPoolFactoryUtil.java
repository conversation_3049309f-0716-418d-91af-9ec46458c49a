package cn.huanju.edu100.study.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> panxia
 * create at:  2021/4/22  2:02 下午
 * @description: 线程池工厂
 */
public class ThreadPoolFactoryUtil {


	public static ThreadPoolExecutor creatPool(int maxPoolSize, String poolName, BlockingQueue<Runnable> poolQueue){
		TimeUnit unit=TimeUnit.SECONDS;
		Edu100ThreadPoolExecutor threadPoolExecutor = new Edu100ThreadPoolExecutor(
				Runtime.getRuntime().availableProcessors()*2+1, maxPoolSize,60 , unit, poolQueue,
				new ThreadFactoryBuilder().setNameFormat(poolName+"-%d").setDaemon(true).build());
		return threadPoolExecutor;
	}

	public static ThreadPoolExecutor createDefaultPool(String poolName){
		return creatPool(4000,poolName,new SynchronousQueue<>());
	}


}
