/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 试卷答题明细Entity
 * <AUTHOR>
 * @version 2015-06-02
 */
public class PaperAnswerDetail extends DataEntity<PaperAnswerDetail> {

	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long pId;		// p_id
	private Date answerTime;		// answer_time
	private Integer state;		// 状态，0未开始 1进行中 2已交卷 3已评卷
	private Double score;	// 得分

	public PaperAnswerDetail() {
		super();
	}

	public PaperAnswerDetail(Long id){
		super(id);
	}

	public Long getpId() {
		return pId;
	}

	public void setpId(Long pId) {
		this.pId = pId;
	}

	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}

	public Long getUid() {
		return uid;
	}
		public void setUid(Long uid) {
		this.uid = uid;
	}
	public Long getPId() {
		return pId;
	}
	public void setPId(Long pId) {
		this.pId = pId;
	}
	public Date getAnswerTime() {
		return answerTime;
	}
		public void setAnswerTime(Date answerTime) {
		this.answerTime = answerTime;
	}
	public Integer getState() {
		return state;
	}
		public void setState(Integer state) {
		this.state = state;
	}
}
