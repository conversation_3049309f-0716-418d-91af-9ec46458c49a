package cn.huanju.edu100.study.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import cn.huanju.edu100.study.resource.feigncall.dto.ResultBody;
import cn.huanju.edu100.study.resource.feigncall.dto.AiAssistantResponseDTO;
import com.alibaba.fastjson.JSON;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

@Slf4j
public class RetryUtil {
    
    /**
     * 执行带重试的请求并解析响应
     * @param requestSupplier 请求提供者
     * @param maxRetries 最大重试次数
     * @param logTag 日志标签
     * @return 解析后的数据列表
     */
    public static List<Map> executeWithRetry(Supplier<ResponseEntity<ResultBody<AiAssistantResponseDTO>>> requestSupplier, 
                                           int maxRetries, 
                                           String logTag) {
        int retryCount = 0;
        while (retryCount <= maxRetries) {
            try {
                ResponseEntity<ResultBody<AiAssistantResponseDTO>> response = requestSupplier.get();
                if (response == null || response.getStatusCode().value() != 200 || response.getBody() == null || response.getBody().getData() == null) {
                    log.error("{} 请求失败: {}", logTag, response);
                    retryCount++;
                    continue;
                }
                
                List<Map> result = JSON.parseArray(response.getBody().getData().getContent(), Map.class);
                if (result == null) {
                    log.error("{} JSON解析失败: {}", logTag, response.getBody().getData().getContent());
                    retryCount++;
                    continue;
                }
                
                return result;
            } catch (Exception e) {
                log.error("{} 执行异常: {}", logTag, e.getMessage(), e);
                retryCount++;
            }
        }
        
        log.error("{} 重试{}次后仍然失败", logTag, maxRetries);
        return null;
    }

    /**
     * 执行带重试的请求并解析响应为单个对象
     * @param requestSupplier 请求提供者
     * @param maxRetries 最大重试次数
     * @param logTag 日志标签
     * @return 解析后的单个对象
     */
    public static Map executeWithRetryForSingleObject(Supplier<ResponseEntity<ResultBody<AiAssistantResponseDTO>>> requestSupplier,
                                                      int maxRetries,
                                                      String logTag) {
        int retryCount = 0;
        while (retryCount <= maxRetries) {
            try {
                ResponseEntity<ResultBody<AiAssistantResponseDTO>> response = requestSupplier.get();
                if (response == null || response.getStatusCode().value() != 200 || response.getBody() == null || response.getBody().getData() == null) {
                    log.error("{} 请求失败: {}", logTag, response);
                    retryCount++;
                    continue;
                }

                // 尝试解析为单个对象
                Map result = JSON.parseObject(response.getBody().getData().getContent(), Map.class);
                if (result == null) {
                    log.error("{} JSON解析失败: {}", logTag, response.getBody().getData().getContent());
                    retryCount++;
                    continue;
                }

                return result;
            } catch (Exception e) {
                log.error("{} 执行异常: {}", logTag, e.getMessage(), e);
                retryCount++;
            }
        }

        log.error("{} 重试{}次后仍然失败", logTag, maxRetries);
        return null;
    }

    /**
     * 执行带重试的请求并解析响应为String
     * @param requestSupplier 请求提供者
     * @param maxRetries 最大重试次数
     * @param logTag 日志标签
     * @return 解析后的单个对象
     */
    public static String executeWithRetryForString(Supplier<ResponseEntity<ResultBody<AiAssistantResponseDTO>>> requestSupplier,
                                                      int maxRetries,
                                                      String logTag) {
        int retryCount = 0;
        while (retryCount <= maxRetries) {
            try {
                ResponseEntity<ResultBody<AiAssistantResponseDTO>> response = requestSupplier.get();
                if (response == null || response.getStatusCode().value() != 200 || response.getBody() == null || response.getBody().getData() == null) {
                    log.error("{} 请求失败: {}", logTag, response);
                    retryCount++;
                    continue;
                }

                // 尝试解析为单个对象
                String result = response.getBody().getData().getContent();
                if (result == null) {
                    log.error("{} JSON解析失败: {}", logTag, response.getBody().getData().getContent());
                    retryCount++;
                    continue;
                }

                return result;
            } catch (Exception e) {
                log.error("{} 执行异常: {}", logTag, e.getMessage(), e);
                retryCount++;
            }
        }

        log.error("{} 重试{}次后仍然失败", logTag, maxRetries);
        return null;
    }
} 