package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 微课班章节Entity
 * <AUTHOR>
 * @version 2017-12-28
 */
public class TutorWkClassChapter extends DataEntity<TutorWkClassChapter> {
	
	private static final long serialVersionUID = 1L;
	private Long parentId;		// parent_id
	private String parentIds;		// parent_ids
	private Long wkClassId;		// wk_class_id
	private String name;		// name
	private String chapterDesc;		// chapter_desc
	private Integer sort;		// sort
	private Integer klevel;		// klevel
	private String ip;		// ip
	
	public TutorWkClassChapter() {
		super();
	}

	public TutorWkClassChapter(Long id){
		super(id);
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}
	
	public String getParentIds() {
		return parentIds;
	}

	public void setParentIds(String parentIds) {
		this.parentIds = parentIds;
	}
	
	public Long getWkClassId() {
		return wkClassId;
	}

	public void setWkClassId(Long wkClassId) {
		this.wkClassId = wkClassId;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getChapterDesc() {
		return chapterDesc;
	}

	public void setChapterDesc(String chapterDesc) {
		this.chapterDesc = chapterDesc;
	}
	
	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}
	
	public Integer getKlevel() {
		return klevel;
	}

	public void setKlevel(Integer klevel) {
		this.klevel = klevel;
	}
	
	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}
	
}