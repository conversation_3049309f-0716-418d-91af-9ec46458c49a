/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.GroupStudentDao;
import cn.huanju.edu100.study.model.GroupStudent;
import cn.huanju.edu100.study.util.ValidateUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 分组学生DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public class GroupStudentIbatisImpl extends CrudIbatisImpl2<GroupStudent> implements GroupStudentDao {

    public GroupStudentIbatisImpl() {
        super("GroupStudent");
    }

    @Override
    public boolean updateType(Long groupId, Long suid, Integer type) throws DataAccessException {
        if (ValidateUtils.isEmpty(groupId)) {
            logger.error("param error, parameter groupId is invalid,groupId:{}", namespace, groupId);
            throw new DataAccessException("param error,groupId is invalid");
        }
        if (ValidateUtils.isEmpty(suid)) {
            logger.error("param error, parameter suid is invalid,suid:{}", namespace, suid);
            throw new DataAccessException("param error,suid is invalid");
        }
        if (ValidateUtils.isEmpty(type)) {
            logger.error("param error, parameter type is invalid,type:{}", namespace, type);
            throw new DataAccessException("param error,type is invalid");
        }

        try {
            SqlMapClient sqlMap = super.getMaster();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("groupId", groupId);
            param.put("suid", suid);
            param.put("type", type);

            int row = sqlMap.update(namespace + ".updateType", param);
            return row >= 1;
        } catch (SQLException e) {
            logger.error("list {} SQLException.groupId:{} suid:{} type:{}", namespace, groupId, suid, type, e);
            throw new DataAccessException("list SQLException error:" + e.getMessage());
        } catch (Exception e) {
            logger.error("list {} SQLException.groupId:{} suid:{} type:{}", namespace, groupId, suid, type, e);
            throw new DataAccessException("list Exception error:" + e.getMessage());
        }
    }

    @Override
    public GroupStudent qryGroupStudentByGroupIdAndUid(Long groupId, Long uid) throws DataAccessException {
        if (ValidateUtils.isEmpty(groupId)) {
            logger.error("param error, parameter groupId is invalid,groupId:{}", namespace, groupId);
            throw new DataAccessException("param error,groupId is invalid");
        }
        if (ValidateUtils.isEmpty(uid)) {
            logger.error("param error, parameter uid is invalid,uid:{}", namespace, uid);
            throw new DataAccessException("param error,suid is invalid");
        }

        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("groupId", groupId);
            param.put("uid", uid);

            return (GroupStudent) sqlMap.queryForObject(namespace + ".qryGroupStudentByGroupIdAndUid", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.groupId:{} uid:{}", namespace, groupId, uid, e);
            throw new DataAccessException("list SQLException error:" + e.getMessage());
        } catch (Exception e) {
            logger.error("list {} SQLException.groupId:{} uid:{}", namespace, groupId, uid, e);
            throw new DataAccessException("list Exception error:" + e.getMessage());
        }
    }
}
