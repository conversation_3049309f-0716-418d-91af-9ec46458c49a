package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.QuestionAnswerStaticsDao;
import cn.huanju.edu100.study.kafka.question.KafkaMsgProducerService;
import cn.huanju.edu100.study.model.QuestionAnswerCount;
import cn.huanju.edu100.study.model.QuestionAnswerDetail;
import cn.huanju.edu100.study.model.QuestionAnswerStatics;
import cn.huanju.edu100.study.model.QuestionAnswerStaticsCount;
import cn.huanju.edu100.study.service.QuestionAnswerStaticsService;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.GsonUtils;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2015-05-14
 */
@Service
public class QuestionAnswerStaticsServiceImpl extends BaseServiceImpl<QuestionAnswerStaticsDao,QuestionAnswerStatics> implements QuestionAnswerStaticsService {

	private static Logger logger = LoggerFactory.getLogger(QuestionAnswerStaticsServiceImpl.class);

	public final static int cachedQueueMaxSize = 6 * 10000; // 缓存待入库的学生答题记录队列的最大size
	public final static int defaultBatchNum = 1000; // 默认每次批量get的记录条数
	public final static long defaultPeriod = 1000; // 默认每次批量get的最长时间周期(单位为毫秒)
	private final static int redisCacheTime = 60 * 30;	// 30分钟
	public final static Double emptyTotal = 7D;

	public static LinkedBlockingQueue<QuestionAnswerDetail> questionAnswerDetail_Queue = new LinkedBlockingQueue<QuestionAnswerDetail>(
			cachedQueueMaxSize);

	@Autowired
	private CompatableRedisClusterClient compatableRedisClusterClient;
	@Value("${kafka.questionanswerdetail.topic}")
	private String topic;
	@Value("${kafka.questionanswerdetail.ifPartition}")
	private String ifPartition;
	@Value("${kafka.questionanswerdetail.partitionNum}")
	private Integer partitionNum;
	@Value("${kafka.questionanswerdetail.role}")
	String role;//用来生成key

	@Autowired
	@Qualifier("questionStaticProducerService")
	private KafkaMsgProducerService questionStaticProducerService;

	ExecutorService answerCountExecutor = Executors.newFixedThreadPool(20);

	// 题目总答题数缓存
	private LoadingCache<Long, Long> questionTotalCache = CacheBuilder.newBuilder().maximumSize(50000).expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Long>() {
				@Override
				public Long load(Long questionId) throws Exception {
					// 先从redis取
					String num = compatableRedisClusterClient.get(RedisConsts.getQuestionTotal(questionId));
					if(StringUtils.isNotBlank(num)){
						return Long.valueOf(num);
					}
					long answerAmount = dao.countQuestionAnswer(questionId);
					logger.info("questionTotalCache:{},answerAmount:{}", questionId, answerAmount);
					compatableRedisClusterClient.setex(RedisConsts.getQuestionTotal(questionId), redisCacheTime, String.valueOf(answerAmount));
					return answerAmount;
				}
			});

	// 题目答对数缓存
	private LoadingCache<Long, Long> questionRightCache = CacheBuilder.newBuilder().maximumSize(50000).expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Long>() {
                @Override
                public Long load(Long questionId) throws Exception {
					// 先从redis取
					String num = compatableRedisClusterClient.get(RedisConsts.getQuestionRight(questionId));
					if(StringUtils.isNotBlank(num)){
						return Long.valueOf(num);
					}
                    long rightAmount = dao.countRightAnswer(questionId);
                    logger.info("questionRightCache:{},rightAmount:{}",questionId,rightAmount);
					compatableRedisClusterClient.setex(RedisConsts.getQuestionRight(questionId), redisCacheTime, String.valueOf(rightAmount));
                    return rightAmount;
                }
            });

	// 题目答错数缓存
	private LoadingCache<Long, Long> questionWrongCache = CacheBuilder.newBuilder().maximumSize(50000).expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Long>() {
                @Override
                public Long load(Long questionId) throws Exception {
					// 先从redis取
					String num = compatableRedisClusterClient.get(RedisConsts.getQuestionWrong(questionId));
					if(StringUtils.isNotBlank(num)){
						return Long.valueOf(num);
					}
                    long wrongAmount = dao.countWrongAnswer(questionId);
                    logger.info("questionWrongCache:{},wrongAmount:{}",questionId,wrongAmount);
					compatableRedisClusterClient.setex(RedisConsts.getQuestionWrong(questionId), redisCacheTime, String.valueOf(wrongAmount));
                    return wrongAmount;
                }
            });

	// 题目答题缓存
	private LoadingCache<Long, QuestionAnswerCount> questionAnswerCache = CacheBuilder.newBuilder().maximumSize(50000).expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, QuestionAnswerCount>() {
                @Override
                public QuestionAnswerCount load(Long questionId) throws Exception {
					// 先从redis取
					String anserwerNum = compatableRedisClusterClient.get(RedisConsts.getQuestionAnswer(questionId));
					if(StringUtils.isNotBlank(anserwerNum)){
						return (QuestionAnswerCount) GsonUtils.fromJson(anserwerNum, QuestionAnswerCount.class);
					}
					return null;
                }
            });

	/**
     * 统计数据的逻辑
     * 1.若答题数为0，则给一个假数据（7）
     * 2.若答题数不为0，但小于7，则总答题数=【7+真实答题数】，答对数为 =【真实的答对数~总答题数】的之间的随机数
     * 3.若答题数不为0，但答对人数为0，则答对人数为总人数*0.25，答对率再根据答对人数/总人数
     *
     * */
    @Override
	@Cached(name = "studyCache", key = "'QuestionAnswerStaticsServiceImpl.staticQuestionAnswer' + #questionId",
			cacheType = CacheType.LOCAL,localLimit = 10000,expire = 5,timeUnit = TimeUnit.MINUTES
	)
	@CacheRefresh(refresh = 4, stopRefreshAfterLastAccess = 5,timeUnit = TimeUnit.MINUTES)
    public QuestionAnswerStatics staticQuestionAnswer(long questionId) throws DataAccessException {
        QuestionAnswerStatics statics=new QuestionAnswerStatics();

        long answerAmount = 0L;//this.dao.countQuestionAnswer(questionId);
        long rightAmount = 0L;//this.dao.countRightAnswer(questionId);
        long wrongAmount = 0L;//this.dao.countWrongAnswer(questionId);

//        try {
//        	answerAmount = questionTotalCache.getUnchecked(questionId);
//		} catch (Exception e) {
//			logger.error("staticQuestionAnswer questionTotalCache getUnchecked error!! questionId:"+questionId+"!",e);
//		}

        try {
        	rightAmount = questionRightCache.getUnchecked(questionId);
		} catch (Exception e) {
			logger.error("staticQuestionAnswer questionRightCache getUnchecked error!! questionId:"+questionId+"!",e);
		}

        try {
        	wrongAmount = questionWrongCache.getUnchecked(questionId);
		} catch (Exception e) {
			logger.error("staticQuestionAnswer questionWrongCache getUnchecked error!! questionId:"+questionId+"!",e);
		}

		return setQuestionAnswerStatics(statics, rightAmount, wrongAmount);
	}

	@NotNull
	private QuestionAnswerStatics setQuestionAnswerStatics(QuestionAnswerStatics statics, long rightAmount, long wrongAmount) {
		long answerAmount;
		answerAmount = rightAmount + wrongAmount;
		if (0==answerAmount){
			//等于0的时候，给一个假数据
			Double max = emptyTotal;
			Double min = 1D;
			int right = RandomUtils.nextInt(max.intValue())%(max.intValue()-min.intValue()+1) + min.intValue();
			DecimalFormat decimalFormat=new DecimalFormat("0.00%");
			Double rightPer = (double) right / max;

			statics.setAnswerAmount(max.longValue());
			statics.setRightAnswerPercent(decimalFormat.format(rightPer));
			statics.setWrongAnswerPercent(decimalFormat.format(1-rightPer));
			return statics;
		}

		if (answerAmount<emptyTotal){
			//当答题数小于emptyTotal的时候，总数上给一个emptyTotal+answerAmount
			double max = emptyTotal+answerAmount;
			double min = (double) rightAmount;
			int right = RandomUtils.nextInt((int) max)%((int) max - (int) min +1) + (int) min;
			DecimalFormat decimalFormat=new DecimalFormat("0.00%");
			Double rightPer = (double) right / max;
			if (rightPer > 1D) {
				rightPer = 1D;
			}
			statics.setAnswerAmount((long)max);
			statics.setRightAnswerPercent(decimalFormat.format(rightPer));
			statics.setWrongAnswerPercent(decimalFormat.format(1-rightPer));
			return statics;
		}

		DecimalFormat decimalFormat=new DecimalFormat("0.00%");
		statics.setAnswerAmount(answerAmount);
		if (rightAmount ==0) {
			int max = (int)answerAmount;
			int right = BigDecimal.valueOf(max*0.25).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
			Double rightPer = (double) right / max;
			if (rightPer > 1D) {
				rightPer = 1D;
			}

			statics.setRightAnswerPercent(decimalFormat.format(rightPer));
			statics.setWrongAnswerPercent(decimalFormat.format(1-rightPer));
		}else {
			Double rightPer = (double) rightAmount / (double) answerAmount;
			if(rightPer>1D){//兼容总数还没有计算入库的时间差
				rightPer = 1D;
			}
			statics.setRightAnswerPercent(decimalFormat.format(rightPer));
			statics.setWrongAnswerPercent(decimalFormat.format(1-rightPer));
		}

		return statics;
	}

	@Override
    public Map<Long,QuestionAnswerStatics> staticQuestionAnswerBatch(Collection<Long> questionIds) throws DataAccessException {
    	Map<Long,QuestionAnswerStatics> data = new HashMap<Long, QuestionAnswerStatics>();
    	for (Long questionId : questionIds) {
    		QuestionAnswerStatics statics=new QuestionAnswerStatics();
	        statics = staticQuestionAnswer(questionId);
			data.put(questionId, statics);
		}
        return data;
//		return getDataMultiThread(new ArrayList<>(questionIds));
    }

	@Override
    public Map<Long,QuestionAnswerStatics> staticQuestionAnswerBatchNew(Collection<Long> questionIds) throws DataAccessException {
    	Map<Long,QuestionAnswerStatics> data = new HashMap<Long, QuestionAnswerStatics>();
		List<Long> notExistQuestionIds = Lists.newArrayList();
    	for (Long questionId : questionIds) {
    		QuestionAnswerStatics statics=new QuestionAnswerStatics();
	        statics = staticQuestionAnswerNew(questionId);
			if (Objects.nonNull(statics)){
				data.put(questionId, statics);
			} else {
				notExistQuestionIds.add(questionId);
			}
		}

		if (CollectionUtils.isNotEmpty(notExistQuestionIds)){
			List<QuestionAnswerStaticsCount> countList = dao.countRightAnswerByIds(notExistQuestionIds);
			Map<Long, List<QuestionAnswerStaticsCount>> countMap = CollectionUtils.isNotEmpty(countList)
					? countList.stream().collect(Collectors.groupingBy(QuestionAnswerStaticsCount::getId)) : Maps.newHashMap();
			for (Long questionId : notExistQuestionIds) {
				QuestionAnswerStatics statics = new QuestionAnswerStatics();
				long rightAmount = 0l;
				long wrongAmount = 0l;
				if (countMap.containsKey(questionId)){
					List<QuestionAnswerStaticsCount> count = countMap.get(questionId);
					rightAmount = count.stream().filter(questionAnswerStaticsCount -> questionAnswerStaticsCount.getState() == 2).mapToLong(QuestionAnswerStaticsCount::getAnswerAmount).sum();
					wrongAmount = count.stream().filter(questionAnswerStaticsCount -> questionAnswerStaticsCount.getState() != 2).mapToLong(QuestionAnswerStaticsCount::getAnswerAmount).sum();
					statics = setQuestionAnswerStatics(statics, rightAmount, wrongAmount);
				} else {
					statics = setQuestionAnswerStatics(statics, rightAmount, wrongAmount);
				}
				QuestionAnswerCount count = new QuestionAnswerCount();
				count.setRightAnswerAmount(rightAmount);
				count.setWrongAnswerAmount(wrongAmount);
				count.setId(questionId);
				compatableRedisClusterClient.set(RedisConsts.getQuestionAnswer(questionId), GsonUtils.toJson(count));
				data.put(questionId, statics);
			}
		}
        return data;
//		return getDataMultiThread(new ArrayList<>(questionIds));
    }

	private QuestionAnswerStatics staticQuestionAnswerNew(Long questionId) {
		QuestionAnswerCount count = null;
		try {
			count = questionAnswerCache.getUnchecked(questionId);
		} catch (Exception e) {
			logger.error("staticQuestionAnswer questionWrongCache getUnchecked error!! questionId:"+questionId+"!",e);
		}
		if (Objects.isNull(count)){
			return null;
		}

		QuestionAnswerStatics statics = new QuestionAnswerStatics();
		return setQuestionAnswerStatics(statics, count.getRightAnswerAmount(), count.getWrongAnswerAmount());
	}

	public Map<Long, QuestionAnswerStatics> getDataMultiThread(List<Long> questionIds) {
		// 使用 ConcurrentHashMap 保证线程安全
		Map<Long, QuestionAnswerStatics> data = new ConcurrentHashMap<>();
		// 为每个 questionId 创建一个异步任务，收集所有 Future
		List<CompletableFuture<Void>> futures = questionIds.stream()
				.map(questionId ->
						CompletableFuture.runAsync(() -> {
							// 执行耗时操作，并将结果存入 Map
							try {
								QuestionAnswerStatics statics = staticQuestionAnswer(questionId);
								data.put(questionId, statics);
							} catch (DataAccessException e) {
								logger.error("staticQuestionAnswer error!! questionId:"+questionId+"!",e);
							}
						})
				)
				.collect(Collectors.toList());

		// 等待所有任务完成（阻塞直到完成）
		CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
		return new HashMap<>(data); // 转为普通 HashMap 返回（可选）
	}

    /**
     * 记录答题情况到统计表中
     *
     * 记录QuestionAnswerDetail入detail，QuestionAnswer则由定时任务每日更新
     *
     * */
    @Override
    public void syncRecordQuestionAnswerInfo(Long uid,Long questionId,Date answerDate,Integer state) throws DataAccessException{
		logger.debug("questionAnswerDetail_Queue syncRecordQuestionAnswerInfo");
    	QuestionAnswerDetail questionAnswerDetail = new QuestionAnswerDetail();

    	questionAnswerDetail.setUid(uid);
    	questionAnswerDetail.setQuestionId(questionId);
    	questionAnswerDetail.setAnswerTime(answerDate);
    	questionAnswerDetail.setState(state);

//
//    	try {
//			questionAnswerDetail_Queue.put(questionAnswerDetail);
//		} catch (Exception e) {
//			throw new DataAccessException(e);
//		}

		//改用mq
		try {
			questionStaticProducerService.sndMesForTemplate(topic, GsonUtil.toJson(questionAnswerDetail), ifPartition, partitionNum, role);
		} catch (Exception e) {
			throw new DataAccessException(e);
		}
    }

    /**
     * 记录答题情况到统计表中
     *
     * 记录QuestionAnswerDetail入detail，QuestionAnswer则由定时任务每日更新
     *
     * */
    @Override
    public void recordQuestionAnswerInfoBatch(List<QuestionAnswerDetail> questionAnswerDetailList) throws DataAccessException{
//    	QuestionAnswer questionAnswer = new QuestionAnswer();
		logger.debug("questionAnswerDetail_Queue recordQuestionAnswerInfoBatch");
    	for (QuestionAnswerDetail questionAnswerDetail : questionAnswerDetailList) {

//    		try {
//    			questionAnswerDetail_Queue.put(questionAnswerDetail);
//			} catch (Exception e) {
//				throw new DataAccessException(e);
//			}
			//改用mq
    		try {
				questionStaticProducerService.sndMesForTemplate(topic, GsonUtil.toJson(questionAnswerDetail), ifPartition, partitionNum, role);
			} catch (Exception e) {
				throw new DataAccessException(e);
			}
		}
//    	Integer num = this.dao.addQuestionAnswerDetailBatch(questionAnswerDetailList);
    }

    @Override
    public List<QuestionAnswerDetail> getQuestionAnswerDetail2SyncDB(int batchNum, long period)throws DataAccessException{

    	int temBatchNum = (batchNum > 0 ? batchNum : defaultBatchNum); // 判断批量get的参数合理性
		long temPeriod = (period > 0 ? period : defaultPeriod); // 判断批量get的参数合理性
		int times = 0; // get的次数
		long startTime = System.currentTimeMillis(); // 本次批量get的开始时间

		List<QuestionAnswerDetail> list = new ArrayList<QuestionAnswerDetail>(); // 保存get到的记录集合
		logger.debug("questionAnswerDetail_Queue : getQuestionAnswerDetail2SyncDB");
		while (true) {
			QuestionAnswerDetail tem = null;
			try {
				tem = questionAnswerDetail_Queue.poll(10,
						TimeUnit.MILLISECONDS);
			} catch (Exception e) {
				// TODO: handle exception
				throw new DataAccessException(e);
			}

			if (tem != null) {
				list.add(tem);
			}

			long endTime = System.currentTimeMillis();
			if (times >= temBatchNum || (endTime - startTime) >= temPeriod) {
				break;
			}
		}

		return list;
    }

    @Override
    public void drainToAllElement(List<QuestionAnswerDetail> data)throws DataAccessException{
		try {
			int num = questionAnswerDetail_Queue.drainTo(data);
			logger.info("questionAnswerDetail_Queue :drainToAllElement num:{}",num);
		} catch (Exception e) {
			// TODO: handle exception
			throw new DataAccessException(e);
		}

    }

	/**
	 * 考虑到分表，同样question的批量提交插入
	 * @param questionAnswerDetail
	 * @return
	 * @throws DataAccessException
	 */
	@Override
	public Integer addQuestionAnswerDetailBatch(List<QuestionAnswerDetail> questionAnswerDetail) throws DataAccessException {
		if(org.apache.commons.collections4.CollectionUtils.isEmpty(questionAnswerDetail)){
			return 0;
		}
		List<QuestionAnswerDetail> questionList = null;
		Map<Long,List<QuestionAnswerDetail>> questionMap = Maps.newHashMap();
		for (QuestionAnswerDetail answerDetail : questionAnswerDetail) {
			questionList = questionMap.get(answerDetail.getQuestionId());
		if(null == questionList){
			questionList = Lists.newArrayList();
			questionMap.put(answerDetail.getQuestionId(), questionList);
		}
		questionList.add(answerDetail);
	}
		for(Map.Entry<Long, List<QuestionAnswerDetail>> entry : questionMap.entrySet()){
			dao.addQuestionAnswerDetailBatch(entry.getValue());
		}

		return 1;
	}

	public static void main(String[] args) {
    	System.out.println("四舍五入取整:(2.1)=" + new BigDecimal("2.1").setScale(0, BigDecimal.ROUND_HALF_UP));
	}
}
