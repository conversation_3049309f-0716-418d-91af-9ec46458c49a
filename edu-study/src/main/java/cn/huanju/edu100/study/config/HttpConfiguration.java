//package cn.huanju.edu100.study.config;
//
//import org.springframework.context.annotation.Configuration;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.http.client.HttpClient;
//import org.apache.http.client.config.RequestConfig;
//import org.apache.http.conn.HttpClientConnectionManager;
//import org.apache.http.impl.client.CloseableHttpClient;
//import org.slf4j.MDC;
//import org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor;
//import org.springframework.cloud.client.loadbalancer.LoadBalanced;
//import org.springframework.cloud.commons.httpclient.ApacheHttpClientFactory;
//import org.springframework.cloud.openfeign.support.FeignHttpClientProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpRequest;
//import org.springframework.http.client.ClientHttpRequestExecution;
//import org.springframework.http.client.ClientHttpRequestInterceptor;
//import org.springframework.http.client.ClientHttpResponse;
//import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
//import org.springframework.http.converter.StringHttpMessageConverter;
//import org.springframework.web.client.RestTemplate;
//
//import java.io.IOException;
//import java.nio.charset.Charset;
//import java.util.Collections;
//
///**
// * @program: edu-service-hq-parent
// * @description:
// * @author: zhangtong
// * @create: 2022-06-07 14:07
// */
//@Configuration
//public class HttpConfiguration {
//    @Bean
//    @LoadBalanced
//    public RestTemplate restTemplate(HttpComponentsClientHttpRequestFactory factory) {
//        RestTemplate restTemplate = new RestTemplate(factory);
//        restTemplate.getMessageConverters()
//                .add(0, new StringHttpMessageConverter(Charset.forName("UTF-8")));
//        restTemplate.setInterceptors(Collections.singletonList(new ClientHttpRequestInterceptor() {
//            @Override
//            public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
//                HttpHeaders headers = request.getHeaders();
//                String contextId = MDC.get("traceId");
//                if(StringUtils.isNotBlank(contextId)) {
//                    headers.add("traceId", contextId);
//                }
//                return execution.execute(request, body);
//            }
//        }));
//        return restTemplate;
//    }
//
//    @Bean
//    public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory(HttpClient httpClient) {
//        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
//        clientHttpRequestFactory.setHttpClient(httpClient);
//        clientHttpRequestFactory.setConnectionRequestTimeout(5000);
//        clientHttpRequestFactory.setReadTimeout(3000);
//        clientHttpRequestFactory.setConnectTimeout(3000);
//        return clientHttpRequestFactory;
//    }
//
//}
