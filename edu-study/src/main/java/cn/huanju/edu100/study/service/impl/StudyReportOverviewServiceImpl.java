package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.StudyReportOverviewDao;
import cn.huanju.edu100.study.model.StudyReportOverview;
import cn.huanju.edu100.study.service.StudyReportOverviewService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

@Service
public class StudyReportOverviewServiceImpl extends BaseServiceImpl<StudyReportOverviewDao, StudyReportOverview> implements StudyReportOverviewService {

	@Override
	public StudyReportOverview findObjectByUid(Long uid) throws DataAccessException {
		return dao.findObjectByUid(uid);
	}

}
