package cn.huanju.edu100.study.util.pdf.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: PdfHtmlElement
 * @Author: xush<PERSON><PERSON>@hqwx.com
 * @Date: 2024-08-30
 * @Ver: v1.0 -create
 */
@Data
public class PdfHtmlElement implements Serializable {
    public interface Type{
        String Img = "image";
        String Text = "text";
    }

    private String type;
    private String value;
    private long width;
    private long height;

    public PdfHtmlElement() {
    }
    public PdfHtmlElement(String type, String value, long width, long height) {
        this.type = type;
        this.value = value;
        this.width = width;
        this.height = height;
    }
}
