package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 学生学习任务Entity
 * 
 * <AUTHOR>
 * @version 2015-05-15
 */
public class StudyReportDetail extends DataEntity<StudyReportDetail> {

    private static final long serialVersionUID = 1L;
    private Long knowledgeId; // 知识点id
    private Integer answerNum; // 答题数
    private Integer wrongNum; // 错题数
    private String wrongIds; // 错题id
    private String answerIds; // 答题id
    private Long taskId; // 任务id

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public StudyReportDetail() {
        super();
    }

    public StudyReportDetail(Long id) {
        super(id);
    }

    public String getAnswerIds() {
        return answerIds;
    }

    public void setAnswerIds(String answerIds) {
        this.answerIds = answerIds;
    }

    public Long getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(Long knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public Integer getAnswerNum() {
        return answerNum;
    }

    public void setAnswerNum(Integer answerNum) {
        this.answerNum = answerNum;
    }

    public Integer getWrongNum() {
        return wrongNum;
    }

    public void setWrongNum(Integer wrongNum) {
        this.wrongNum = wrongNum;
    }

    public String getWrongIds() {
        return wrongIds;
    }

    public void setWrongIds(String wrongIds) {
        this.wrongIds = wrongIds;
    }
}