package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.MockDao;
import cn.huanju.edu100.study.dao.ibatis.Ibatis;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;

public class MockIbatisImpl extends Ibatis implements MockDao{
    Logger logger = LoggerFactory.getLogger(MockIbatisImpl.class);
    public String getContent(String name) throws DataAccessException {
        try {
            SqlMapClient sqlMap = super.getSlave();
            return (String) sqlMap.queryForObject(
                    "Mock.get", name);
        } catch (SQLException e) {
            logger.error("getContent SQLException:{}", name,
                    e);
            throw new DataAccessException("getContent SQLException error");
        }
    }

}
