package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.PageModel;
import cn.huanju.edu100.study.model.UserDoneRecord;
import cn.huanju.edu100.study.model.UserDoneRecordObjTypeVo;
import cn.huanju.edu100.study.model.UserDoneRecordVo;
import com.hqwx.study.dto.query.UserDoneRecordQuery;

import java.util.List;

public interface UserDoneRecordService extends BaseService<UserDoneRecord> {

    PageModel<UserDoneRecordVo> getUserDoneRecordVoList(UserDoneRecordQuery query) throws DataAccessException;

    List<UserDoneRecordObjTypeVo> getUserDoneRecordObjTypeList(UserDoneRecordQuery query) throws DataAccessException;

    void deleteByParam(Long uid,Integer objType,Long objId) throws DataAccessException;

    public Page<UserDoneRecord> findPage(Page<UserDoneRecord> condition, UserDoneRecord userDoneRecord, String startDate,String endDate)throws DataAccessException;

}
