package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorStudentAnswerDetail;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 做题记录日详情Service
 * 
 * <AUTHOR>
 * @version 2016-01-19
 */
public interface TutorStudentAnswerDetailService extends BaseService<TutorStudentAnswerDetail> {

    List<Long> getDoneQuestionIdByUidAndTaskIdList(Long uid, List<Long> taskIdList) throws DataAccessException;
}