/**
 *
 */
package cn.huanju.edu100.study.dao.tutor;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.tutor.TutorCategory;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 个性化考试DAO接口
 * <AUTHOR>
 * @version 2016-01-12
 */
public interface TutorCategoryDao extends CrudDao<TutorCategory> {

    List<TutorCategory> getCategoryByIdList(List<Long> categoryIdList)
            throws DataAccessException;

}
