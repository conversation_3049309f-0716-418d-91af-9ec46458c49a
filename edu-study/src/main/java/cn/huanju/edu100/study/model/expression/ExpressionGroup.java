package cn.huanju.edu100.study.model.expression;


import cn.huanju.edu100.persistence.model.DataEntity;
import cn.huanju.edu100.persistence.model.HqFiledAnnotation;

import java.util.List;

/**
 * 规则组Entity
 * <AUTHOR>
 * @version 2016-05-23
 */
public class ExpressionGroup extends DataEntity<ExpressionGroup> {

	private static final long serialVersionUID = 1L;
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String name;		// 规则组
	@HqFiledAnnotation(filedGrpLevel = 3)
	private String description;		// 描述
	private Long status;		// status
	private Integer type;		// 规则类型
	private Integer compareType;		// 比较类型
    private Integer isOpposite;     // 是否取反，0:否，1：是

	List<ExpressionRule> expressionRules; // 规则列表，为叶子节点

	List<ExpressionGroup> expressionGroups; // 规则组列表

	@HqFiledAnnotation(filedGrpLevel = 3)
	List<ExpressionMember> expressionMembers; // 规则组成员

	public ExpressionGroup() {
		super();
	}

	public ExpressionGroup(Long id){
		super(id);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getCompareType() {
		return compareType;
	}

	public void setCompareType(Integer compareType) {
		this.compareType = compareType;
	}

    public List<ExpressionRule> getExpressionRules() {
        return expressionRules;
    }

    public void setExpressionRules(List<ExpressionRule> expressionRules) {
        this.expressionRules = expressionRules;
    }

    public List<ExpressionGroup> getExpressionGroups() {
        return expressionGroups;
    }

    public void setExpressionGroups(List<ExpressionGroup> expressionGroups) {
        this.expressionGroups = expressionGroups;
    }

    public List<ExpressionMember> getExpressionMembers() {
        return expressionMembers;
    }

    public void setExpressionMembers(List<ExpressionMember> expressionMembers) {
        this.expressionMembers = expressionMembers;
    }

    public Integer getIsOpposite() {
        return isOpposite;
    }

    public void setIsOpposite(Integer isOpposite) {
        this.isOpposite = isOpposite;
    }

}
