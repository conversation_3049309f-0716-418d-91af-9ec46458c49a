package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.UserAnswerMedical;
import cn.huanju.edu100.exception.DataAccessException;

public interface UserAnswerMedicalDao extends <PERSON>rudDao<UserAnswerMedical> {

    Integer getUidCountByEPaperId(Long ePaperId, Long paperId, Long lessonId) throws DataAccessException;

    Integer updateLessonId(Long ePaperId, Long paperId, Long lessonId) throws DataAccessException;
}
