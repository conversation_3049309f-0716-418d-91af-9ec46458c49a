package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 * 学生任务Entity
 * <AUTHOR>
 * @version 2016-01-18
 */
public class TutorStudentTask extends DataEntity<TutorStudentTask> {

	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Integer status;		// status
	private Long planId;		// planId
	private Long phaseId;		// phaseId
	private Long unitId;		// unitId
	private Long taskId;		// task_id
	private String type;		// type
	private double score;		// score
	private Double total;		// 试卷任务总分
	private int qCompleteFlag;		// 录播任务段落作业完成标志
	private Integer source;// 0:任务 1：微课
	private String ip;		// ip
	private String title;// 任务标题
	private TutorPlan plan;

	private List<Long> taskIdList;

	public TutorStudentTask() {
		super();
	}

	public TutorStudentTask(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public List<Long> getTaskIdList() {
        return taskIdList;
    }

    public void setTaskIdList(List<Long> taskIdList) {
        this.taskIdList = taskIdList;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public int getqCompleteFlag() {
        return qCompleteFlag;
    }

    public void setqCompleteFlag(int qCompleteFlag) {
        this.qCompleteFlag = qCompleteFlag;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public TutorPlan getPlan() {
		return plan;
	}

	public void setPlan(TutorPlan plan) {
		this.plan = plan;
	}

	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}
}
