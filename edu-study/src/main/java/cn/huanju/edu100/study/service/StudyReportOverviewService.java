/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.StudyReportOverview;
import cn.huanju.edu100.exception.DataAccessException;


/**
 * 作业学习Service
 * <AUTHOR>
 * @version 2015-05-15
 */
public interface StudyReportOverviewService extends BaseService<StudyReportOverview> {

	public StudyReportOverview findObjectByUid(Long uid) throws DataAccessException;
}
