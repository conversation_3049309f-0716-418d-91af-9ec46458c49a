/**
 * 
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.questionBox.VirtualHomework;
import cn.huanju.edu100.study.model.questionBox.VirtualHomeworkDetail;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 用户练习作业DAO接口
 * <AUTHOR>
 * @version 2015-08-12
 */
public interface UserVirtualHomeworkDao extends CrudDao<VirtualHomework> {

	Integer insertDetailBatch(List<VirtualHomeworkDetail> details,Long uid) throws DataAccessException;

	VirtualHomework get(long id, long uid) throws DataAccessException;

	List<VirtualHomeworkDetail> getHomeWorkDetails(Long homeworkId, Long uid, Integer elementType) throws DataAccessException;

	List<VirtualHomework> findList(Map<String, Object> param) throws DataAccessException;

    List<VirtualHomework> findByParam(Map<String, Object> param) throws DataAccessException;

    boolean deleteDetailByHomeworkId(Long homeWorkId, Long uid) throws DataAccessException;

    boolean delete(long id, long uid) throws DataAccessException;
}