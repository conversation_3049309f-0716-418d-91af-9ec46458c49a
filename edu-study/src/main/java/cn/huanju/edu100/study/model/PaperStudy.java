/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 作业学习Entity
 * <AUTHOR>
 * @version 2015-05-15
 */
public class PaperStudy extends DataEntity<PaperStudy> {
	
	private static final long serialVersionUID = 1L;
	private Long taskId;		// task_id
	private Long paperId;		// paper_id
	private String name;		// homework_name
	
	public PaperStudy() {
		super();
	}

	public PaperStudy(Long id){
		super(id);
	}

	public Long getTaskId() {
		return taskId;
	}
		public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}
	
		public Long getPaperId() {
			return paperId;
		}

		public void setPaperId(Long paperId) {
			this.paperId = paperId;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

}