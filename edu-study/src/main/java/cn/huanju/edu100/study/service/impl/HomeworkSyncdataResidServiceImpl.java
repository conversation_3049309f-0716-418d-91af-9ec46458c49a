package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.HomeworkSyncdataResidDao;
import cn.huanju.edu100.study.model.HomeworkSyncdataResid;
import cn.huanju.edu100.study.service.HomeworkSyncdataResidService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HomeworkSyncdataResidServiceImpl extends BaseServiceImpl<HomeworkSyncdataResidDao, HomeworkSyncdataResid> implements HomeworkSyncdataResidService {

    @Override
    public Boolean saveHomeworkSyncdataResid(HomeworkSyncdataResid homeworkSyncdataResid) throws DataAccessException {
        HomeworkSyncdataResid homeworkSyncdataResidQuery = new HomeworkSyncdataResid();
        homeworkSyncdataResidQuery.setResId(homeworkSyncdataResid.getResId());
        homeworkSyncdataResidQuery.setType(homeworkSyncdataResid.getType());
        List<HomeworkSyncdataResid> rsList = dao.findList(homeworkSyncdataResidQuery);
        if (CollectionUtils.isEmpty(rsList)) {
            homeworkSyncdataResid.setCreateDate(new Date());
            homeworkSyncdataResid.setUpdateDate(new Date());
            dao.insert(homeworkSyncdataResid);
        }/* else {//更新
            homeworkSyncdataResid.setId(rsList.get(0).getId());
            dao.update(homeworkSyncdataResid);
        }*/
        return true;
    }

    @Override
    public List<HomeworkSyncdataResid> findListByType(int videoCourse) throws DataAccessException {
        HomeworkSyncdataResid homeworkSyncdataResidQuery = new HomeworkSyncdataResid();
        homeworkSyncdataResidQuery.setType(videoCourse);
        List<HomeworkSyncdataResid> rsList = dao.findList(homeworkSyncdataResidQuery);
        return rsList;
    }

    @Override
    public List<HomeworkSyncdataResid> getDifferenceResid(Integer productType) throws DataAccessException {
        List<HomeworkSyncdataResid> rsList = dao.getDifferenceResid(productType);
        return rsList;
    }

}
