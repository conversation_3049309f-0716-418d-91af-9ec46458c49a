package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.HomeworkSyncdataResultDao;
import cn.huanju.edu100.study.model.HomeworkSyncdataResult;
import cn.huanju.edu100.study.service.HomeworkSyncdataResultService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HomeworkSyncdataResultServiceImpl extends BaseServiceImpl<HomeworkSyncdataResultDao, HomeworkSyncdataResult> implements HomeworkSyncdataResultService {

    @Override
    public Boolean saveHomeworkSyncdataResult(HomeworkSyncdataResult homeworkSyncdataResult) throws DataAccessException {
        HomeworkSyncdataResult homeworkSyncdataResultQuery = new HomeworkSyncdataResult();
        homeworkSyncdataResultQuery.setResId(homeworkSyncdataResult.getResId());
        homeworkSyncdataResultQuery.setProductType(homeworkSyncdataResult.getProductType());
        homeworkSyncdataResultQuery.setType(homeworkSyncdataResult.getType());
        List<HomeworkSyncdataResult> rsList = dao.findList(homeworkSyncdataResultQuery);
        if (CollectionUtils.isNotEmpty(rsList)) {//更新
            homeworkSyncdataResult.setId(rsList.get(0).getId());
            dao.update(homeworkSyncdataResult);
        } else {
            homeworkSyncdataResult.setCreateDate(new Date());
            homeworkSyncdataResult.setUpdateDate(new Date());
            dao.insert(homeworkSyncdataResult);
        }
        return true;
    }

}
