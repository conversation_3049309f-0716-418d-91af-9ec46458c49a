/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.WhiteListDao;
import cn.huanju.edu100.study.model.WhiteList;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;

/**
 * 白名单列表DAO接口
 * <AUTHOR>
 * @version 2015-10-27
 */
public class WhiteListIbatisImpl extends CrudIbatisImpl2<WhiteList> implements
		WhiteListDao {

	public WhiteListIbatisImpl() {
		super("WhiteList");
	}

    @Override
    public void insertBatch(List<WhiteList> insertList) throws DataAccessException {
        try {
            SqlMapClient sqlMap = super.getMaster();
            sqlMap.insert("WhiteList.insertBatch", insertList);
        } catch (SQLException e) {
            logger.error("getBulletinList SQLException.", e);
            throw new DataAccessException("getBulletinList SQLException error");
        }
    }

}
