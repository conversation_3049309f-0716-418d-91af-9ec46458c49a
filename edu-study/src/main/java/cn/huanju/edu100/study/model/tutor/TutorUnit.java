package cn.huanju.edu100.study.model.tutor;


import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;
import java.util.List;

/**
 * 云私塾单元Entity
 * <AUTHOR>
 * @version 2017-05-10
 */
public class TutorUnit extends DataEntity<TutorUnit> {

	private static final long serialVersionUID = 1L;
	private String classes;		// classes
	private Long sort;		// sort
	private Long planId;		// plan_id
	private Long phaseId;		// phase_id
	private String name;		// name
	private String shortName;		// 短标题
	private String ip;		// ip
	private Integer type;// 类型 1:任务 2:微课
	private Integer isLock;// 是否加锁 0：否  1：是
	private Date openTime;// 开放时间
	private Long wkClassId;//微课班ID
	private List<Long> idList;
	private int needTask;// 是否需要返回任务 0：需要 1：不需要

	List<TutorTaskDto> tutorTaskDtos;
	public TutorUnit() {
		super();
	}

	public TutorUnit(Long id){
		super(id);
	}

	public String getClasses() {
		return classes;
	}

	public void setClasses(String classes) {
		this.classes = classes;
	}

	public Long getSort() {
		return sort;
	}

	public void setSort(Long sort) {
		this.sort = sort;
	}

	public Long getPlanId() {
		return planId;
	}

	public void setPlanId(Long planId) {
		this.planId = planId;
	}

	public Long getPhaseId() {
		return phaseId;
	}

	public void setPhaseId(Long phaseId) {
		this.phaseId = phaseId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

    public List<TutorTaskDto> getTutorTaskDtos() {
        return tutorTaskDtos;
    }

    public void setTutorTaskDtos(List<TutorTaskDto> tutorTaskDtos) {
        this.tutorTaskDtos = tutorTaskDtos;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getIsLock() {
		return isLock;
	}

	public void setIsLock(Integer isLock) {
		this.isLock = isLock;
	}

	public Date getOpenTime() {
		return openTime;
	}

	public void setOpenTime(Date openTime) {
		this.openTime = openTime;
	}

	public Long getWkClassId() {
		return wkClassId;
	}

	public void setWkClassId(Long wkClassId) {
		this.wkClassId = wkClassId;
	}

	public List<Long> getIdList() {
		return idList;
	}

	public void setIdList(List<Long> idList) {
		this.idList = idList;
	}

	public int getNeedTask() {
		return needTask;
	}

	public void setNeedTask(int needTask) {
		this.needTask = needTask;
	}
}
