package cn.huanju.edu100.study.config.ibatis;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;

@Configuration
public class StudyDataSource {


    @Bean("defaultDs")
    public DataSource defaultDs(DataSource dataSource){
        return ((DynamicRoutingDataSource)dataSource).getDataSource("default-ds");
    }


    @Bean("his_masterDS")
    public DataSource historyMasterDs(DataSource dataSource){
        return ((DynamicRoutingDataSource)dataSource).getDataSource("his_masterDS");
    }

    @Bean("his_slave1DS")
    public DataSource historySlave1Ds(DataSource dataSource){
        return ((DynamicRoutingDataSource)dataSource).getDataSource("his_slave1DS");
    }

    @Bean("his_slave2DS")
    public DataSource historySlave2Ds(DataSource dataSource){
        return ((DynamicRoutingDataSource)dataSource).getDataSource("his_slave2DS");
    }


    @Bean("qboxDs")
    public DataSource qboxDs(DataSource dataSource){
        return ((DynamicRoutingDataSource)dataSource).getDataSource("qbox-ds");
    }


    @Bean("tutorDs")
    public DataSource tutorDs(DataSource dataSource){
        return ((DynamicRoutingDataSource)dataSource).getDataSource("tutor-ds");
    }


    @Bean("userAnswerDs")
    public DataSource userAnswerDs(DataSource dataSource){
        return ((DynamicRoutingDataSource)dataSource).getDataSource("ua-ds");
    }

    @Bean("uaEvenDs")
    public DataSource uaEvenDs(DataSource dataSource) {
        return ((DynamicRoutingDataSource) dataSource).getDataSource("ua-even-ds");
    }

    @Bean("uaOddDs")
    public DataSource uaOddDs(DataSource dataSource) {
        return ((DynamicRoutingDataSource) dataSource).getDataSource("ua-odd-ds");
    }

    @Bean("virtualHomeworkDs")
    public DataSource virtualHomeworkDs(DataSource dataSource) {
        return ((DynamicRoutingDataSource) dataSource).getDataSource("virtual-homework-ds");
    }

    @Bean("alDefaultDs")
    public DataSource alDefaultDs(DataSource dataSource){
        return ((DynamicRoutingDataSource)dataSource).getDataSource("al-default-ds");
    }
}
