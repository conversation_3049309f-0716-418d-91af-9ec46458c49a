package cn.huanju.edu100.study.model.adminstudy;


import com.hqwx.study.entity.UserAnswer;

import java.util.Date;

/**
 * 学员学习情况
 */
public class UserAnswerCompletion extends UserAnswer {

    private Long questionNum;//关联题目个数
    private Long knowledgeNum;//关联知识点个数
    private String knowledgeType;//知识点类型
    private Long paperCompletionNum;//试卷完成次数
    private String avgAccuracy;//平均准确率
    private Double avgScore;//平均得分
    private Date lastAnswerTime;//最后一次作答时间

    public Long getQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(Long questionNum) {
        this.questionNum = questionNum;
    }

    public Long getKnowledgeNum() {
        return knowledgeNum;
    }

    public void setKnowledgeNum(Long knowledgeNum) {
        this.knowledgeNum = knowledgeNum;
    }

    public String getKnowledgeType() {
        return knowledgeType;
    }

    public void setKnowledgeType(String knowledgeType) {
        this.knowledgeType = knowledgeType;
    }

    public Long getPaperCompletionNum() {
        return paperCompletionNum;
    }

    public void setPaperCompletionNum(Long paperCompletionNum) {
        this.paperCompletionNum = paperCompletionNum;
    }

    public String getAvgAccuracy() {
        return avgAccuracy;
    }

    public void setAvgAccuracy(String avgAccuracy) {
        this.avgAccuracy = avgAccuracy;
    }

    public Double getAvgScore() {
        return avgScore;
    }

    public void setAvgScore(Double avgScore) {
        this.avgScore = avgScore;
    }

    public Date getLastAnswerTime() {
        return lastAnswerTime;
    }

    public void setLastAnswerTime(Date lastAnswerTime) {
        this.lastAnswerTime = lastAnswerTime;
    }
}
