package cn.huanju.edu100.study.model.util;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Set;

public class AnswerSummaryVo {

    //("总题目数")
    private Integer totalQuestionNum;
    //("正确题目数")
    private Integer totalRightNum;
    //("错误题目数")
    private Integer totalWrongNum;
    //("未答题目数")
    private Integer noAnswerNum;
    //("正确率")
    private Integer rightRate;
    @JsonIgnore
    private Set<Long> allQuestionIds;
    @JsonIgnore
    private Set<Long> rightQuestionIds;
    @JsonIgnore
    private Set<Long> wrongQuestionIds;
    @JsonIgnore
    private Set<Long> noAnswerQuestionIds;

    public static AnswerSummaryVo buildDefaultSummary() {
        AnswerSummaryVo summaryVo = new AnswerSummaryVo();
        summaryVo.setTotalQuestionNum(0);
        summaryVo.setTotalRightNum(0);
        summaryVo.setTotalWrongNum(0);
        summaryVo.setNoAnswerNum(0);
        summaryVo.setRightRate(0);
        return summaryVo;
    }

    public void formatSummary() {
        if (CollectionUtils.isNotEmpty(this.allQuestionIds)) {
            setTotalQuestionNum(allQuestionIds.size());
        }
        if (CollectionUtils.isNotEmpty(this.rightQuestionIds)) {
            setTotalRightNum(rightQuestionIds.size());
        }
        if (CollectionUtils.isNotEmpty(this.wrongQuestionIds)) {
            setTotalWrongNum(wrongQuestionIds.size());
        }
        if (CollectionUtils.isNotEmpty(this.noAnswerQuestionIds)) {
            setNoAnswerNum(noAnswerQuestionIds.size());
        }
        BigDecimal rate = totalQuestionNum == 0 ? BigDecimal.ZERO : new BigDecimal(totalRightNum).divide(new BigDecimal(totalQuestionNum), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        setRightRate(rate.intValue());
    }

    public Integer getTotalQuestionNum() {
        return totalQuestionNum;
    }

    public void setTotalQuestionNum(Integer totalQuestionNum) {
        this.totalQuestionNum = totalQuestionNum;
    }

    public Integer getTotalRightNum() {
        return totalRightNum;
    }

    public void setTotalRightNum(Integer totalRightNum) {
        this.totalRightNum = totalRightNum;
    }

    public Integer getTotalWrongNum() {
        return totalWrongNum;
    }

    public void setTotalWrongNum(Integer totalWrongNum) {
        this.totalWrongNum = totalWrongNum;
    }

    public Integer getRightRate() {
        return rightRate;
    }

    public void setRightRate(Integer rightRate) {
        this.rightRate = rightRate;
    }

    public Integer getNoAnswerNum() {
        return noAnswerNum;
    }

    public void setNoAnswerNum(Integer noAnswerNum) {
        this.noAnswerNum = noAnswerNum;
    }

    public Set<Long> getRightQuestionIds() {
        return rightQuestionIds;
    }

    public void setRightQuestionIds(Set<Long> rightQuestionIds) {
        this.rightQuestionIds = rightQuestionIds;
    }

    public Set<Long> getWrongQuestionIds() {
        return wrongQuestionIds;
    }

    public void setWrongQuestionIds(Set<Long> wrongQuestionIds) {
        this.wrongQuestionIds = wrongQuestionIds;
    }

    public Set<Long> getNoAnswerQuestionIds() {
        return noAnswerQuestionIds;
    }

    public void setNoAnswerQuestionIds(Set<Long> noAnswerQuestionIds) {
        this.noAnswerQuestionIds = noAnswerQuestionIds;
    }

    public Set<Long> getAllQuestionIds() {
        return allQuestionIds;
    }

    public void setAllQuestionIds(Set<Long> allQuestionIds) {
        this.allQuestionIds = allQuestionIds;
    }
}
