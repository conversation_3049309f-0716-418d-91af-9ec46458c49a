/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorTeacherDao;
import cn.huanju.edu100.study.model.tutor.TutorTeacher;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 班主任DAO接口
 * <AUTHOR>
 * @version 2016-01-12
 */
public class TutorTeacherIbatisImpl extends CrudIbatisImpl2<TutorTeacher> implements
		TutorTeacherDao {

	public TutorTeacherIbatisImpl() {
		super("TutorTeacher");
	}

    @Override
    public List<TutorTeacher> findListByTuids(List<Long> tuids) throws DataAccessException {

        if (CollectionUtils.isEmpty(tuids)) {
            logger.error("findListByTuids {} error, parameter tuids is empty, entity:{}", namespace, tuids);
            throw new DataAccessException(String.format("findListByTuids %s error, parameter tuids is empty, entity: %s", namespace, tuids));
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = Maps.newHashMap();
            param.put("tuids", tuids);
            return sqlMap.queryForList(namespace.concat(".findListByTuids"), param);
        } catch (SQLException e) {
            logger.error("findListByTuids {} SQLException. tuids:{}", namespace, tuids, e);
            throw new DataAccessException(String.format("findListByTuids SQLException error :%s", e.getMessage()));
        }
    }

}
