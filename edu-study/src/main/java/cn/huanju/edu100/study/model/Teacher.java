package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 名师信息Entity
 * <AUTHOR>
 * @version 2015-05-20
 */
public class Teacher extends DataEntity<Teacher> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// name
	private String username;		// username
	private String passWord;		// pass_word
	private String powerType;		// power_type
	private String country;		// country
	private String list;		// list
	private String master;		// master
	private String lessonTop;		// lesson_top
	private String lesson;		// lesson
	private String pic;		// pic
	private String pic2;		// pic2
	private String pic3;		// pic3
	private String pic4;		// pic4
	private String livePic;     //直播商城老师头像
	private String image1;		// image1
	private String image2;		// image2
	private String image3;		// image3
	private String notice1;		// notice1
	private String notice2;		// notice2
	private String notice3;		// notice3
	private Date addTime;		// add_time
	private Integer classType;		// class_type
	private Integer topshow;		// topshow
	private Integer isShow;		// is_show
	private Integer shownum;		// shownum
	private Integer sex;		// sex
	private String education;		// education
	private String protitle;		// protitle
	private String college;		// college
	private Integer cataID;		// cataID
	private String pageshow;		// pageshow
	private Integer isblog;		// isblog
	private String bloglink;		// bloglink
	private String video;		// video
	private Long hits;		// hits
	private String quotation;		// quotation
	private String nameInitial;		// nameInitial
	private Integer star;		// star
	private Integer typeNum;		// typeNum
	private String sinaUid;		// sina_uid
	private String qq;		// sina_uid
	private String email;		// sina_uid
	private String label1;// 标签1
    private String label2;// 标签2
    private String label3;// 标签3

    private String backgroundPic;// 名师主页背景图片
    private String microblogHomeUrl;// 新浪微博主页
    private String wechatPublicAccount;// 微信公众号 
    private boolean isFamousTeacher;//是否是名师堂老师
	private String transparentBackgroundPic;//透明背景图片
	
	public Teacher() {
		super();
	}

	public Teacher(Long id){
		super(id);
	}

	public String getLabel1() {
		return label1;
	}

	public void setLabel1(String label1) {
		this.label1 = label1;
	}

	public String getLabel2() {
		return label2;
	}

	public void setLabel2(String label2) {
		this.label2 = label2;
	}

	public String getLabel3() {
		return label3;
	}

	public void setLabel3(String label3) {
		this.label3 = label3;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}
	
	public String getPassWord() {
		return passWord;
	}

	public void setPassWord(String passWord) {
		this.passWord = passWord;
	}
	
	public String getPowerType() {
		return powerType;
	}

	public void setPowerType(String powerType) {
		this.powerType = powerType;
	}
	
	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}
	
	public String getList() {
		return list;
	}

	public void setList(String list) {
		this.list = list;
	}
	
	public String getMaster() {
		return master;
	}

	public void setMaster(String master) {
		this.master = master;
	}
	
	public String getLessonTop() {
		return lessonTop;
	}

	public void setLessonTop(String lessonTop) {
		this.lessonTop = lessonTop;
	}
	
	public String getLesson() {
		return lesson;
	}

	public void setLesson(String lesson) {
		this.lesson = lesson;
	}
	
	public String getPic() {
		return pic;
	}

	public void setPic(String pic) {
		this.pic = pic;
	}
	
	public String getPic2() {
		return pic2;
	}

	public void setPic2(String pic2) {
		this.pic2 = pic2;
	}
	
	public String getPic3() {
		return pic3;
	}

	public void setPic3(String pic3) {
		this.pic3 = pic3;
	}
	
	public String getPic4() {
		return pic4;
	}

	public void setPic4(String pic4) {
		this.pic4 = pic4;
	}
	
	public String getImage1() {
		return image1;
	}

	public void setImage1(String image1) {
		this.image1 = image1;
	}
	
	public String getImage2() {
		return image2;
	}

	public void setImage2(String image2) {
		this.image2 = image2;
	}
	
	public String getImage3() {
		return image3;
	}

	public void setImage3(String image3) {
		this.image3 = image3;
	}
	
	public String getNotice1() {
		return notice1;
	}

	public void setNotice1(String notice1) {
		this.notice1 = notice1;
	}
	
	public String getNotice2() {
		return notice2;
	}

	public void setNotice2(String notice2) {
		this.notice2 = notice2;
	}
	
	public String getNotice3() {
		return notice3;
	}

	public void setNotice3(String notice3) {
		this.notice3 = notice3;
	}
	
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}
	
	public Integer getClassType() {
		return classType;
	}

	public void setClassType(Integer classType) {
		this.classType = classType;
	}
	
	public Integer getTopshow() {
		return topshow;
	}

	public void setTopshow(Integer topshow) {
		this.topshow = topshow;
	}
	
	public Integer getIsShow() {
		return isShow;
	}

	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}
	
	public Integer getShownum() {
		return shownum;
	}

	public void setShownum(Integer shownum) {
		this.shownum = shownum;
	}
	
	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}
	
	public String getEducation() {
		return education;
	}

	public void setEducation(String education) {
		this.education = education;
	}
	
	public String getProtitle() {
		return protitle;
	}

	public void setProtitle(String protitle) {
		this.protitle = protitle;
	}
	
	public String getCollege() {
		return college;
	}

	public void setCollege(String college) {
		this.college = college;
	}
	
	public Integer getCataID() {
		return cataID;
	}

	public void setCataID(Integer cataID) {
		this.cataID = cataID;
	}
	
	public String getPageshow() {
		return pageshow;
	}

	public void setPageshow(String pageshow) {
		this.pageshow = pageshow;
	}
	
	public Integer getIsblog() {
		return isblog;
	}

	public void setIsblog(Integer isblog) {
		this.isblog = isblog;
	}
	
	public String getBloglink() {
		return bloglink;
	}

	public void setBloglink(String bloglink) {
		this.bloglink = bloglink;
	}
	
	public String getVideo() {
		return video;
	}

	public void setVideo(String video) {
		this.video = video;
	}
	
	public Long getHits() {
		return hits;
	}

	public void setHits(Long hits) {
		this.hits = hits;
	}
	
	public String getQuotation() {
		return quotation;
	}

	public void setQuotation(String quotation) {
		this.quotation = quotation;
	}
	
	public String getNameInitial() {
		return nameInitial;
	}

	public void setNameInitial(String nameInitial) {
		this.nameInitial = nameInitial;
	}
	
	public Integer getStar() {
		return star;
	}

	public void setStar(Integer star) {
		this.star = star;
	}
	
	public Integer getTypeNum() {
		return typeNum;
	}

	public void setTypeNum(Integer typeNum) {
		this.typeNum = typeNum;
	}
	
	public String getSinaUid() {
		return sinaUid;
	}

	public void setSinaUid(String sinaUid) {
		this.sinaUid = sinaUid;
	}

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }



	public String getBackgroundPic() {
		return backgroundPic;
	}

	public void setBackgroundPic(String backgroundPic) {
		this.backgroundPic = backgroundPic;
	}

	public String getMicroblogHomeUrl() {
		return microblogHomeUrl;
	}

	public void setMicroblogHomeUrl(String microblogHomeUrl) {
		this.microblogHomeUrl = microblogHomeUrl;
	}

	public String getWechatPublicAccount() {
		return wechatPublicAccount;
	}

	public void setWechatPublicAccount(String wechatPublicAccount) {
		this.wechatPublicAccount = wechatPublicAccount;
	}

	public boolean getIsFamousTeacher() {
		return isFamousTeacher;
	}

	public void setIsFamousTeacher(boolean isFamousTeacher) {
		this.isFamousTeacher = isFamousTeacher;
	}

	public String getLivePic() {
		return livePic;
	}

	public void setLivePic(String livePic) {
		this.livePic = livePic;
	}

	public String getTransparentBackgroundPic() {
		return transparentBackgroundPic;
	}

	public void setTransparentBackgroundPic(String transparentBackgroundPic) {
		this.transparentBackgroundPic = transparentBackgroundPic;
	}
}