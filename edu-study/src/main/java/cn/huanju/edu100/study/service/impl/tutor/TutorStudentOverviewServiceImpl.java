package cn.huanju.edu100.study.service.impl.tutor;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.tutor.TutorStudentOverviewDao;
import cn.huanju.edu100.study.dao.tutor.TutorTaskDao;
import cn.huanju.edu100.study.model.tutor.TutorStudentOverview;
import cn.huanju.edu100.study.model.tutor.TutorStudentOverviewDto;
import cn.huanju.edu100.study.service.tutor.TutorStudentOverviewService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生学习报告Service
 * <AUTHOR>
 * @version 2016-01-27
 */
@Service
public class TutorStudentOverviewServiceImpl extends BaseServiceImpl<TutorStudentOverviewDao, TutorStudentOverview> implements TutorStudentOverviewService {

    @Autowired
    private TutorTaskDao tutorTaskDao;

    @Override
    public TutorStudentOverviewDto getTutorStudentOverview(TutorStudentOverview params) throws DataAccessException {

        if (params == null || params.getUid() == null || StringUtils.isBlank(params.getClasses())
                    || (params.getCategoryId() == null && params.getSecondCategory() == null)) {
            return null;
        }

        //获取用户某个考试或者科目的学习报告情况
        List<TutorStudentOverview> list = dao.findList(params);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        TutorStudentOverviewDto tutorStudentOverviewDto = new TutorStudentOverviewDto();
        if (params.getCategoryId() != null) {
            try {
                PropertyUtils.copyProperties(tutorStudentOverviewDto, list.get(0));
            } catch (Exception e) {
                logger.error("TutorStudentOverviewServiceImpl.getTutorStudentOverview failed:", e);
            }
        }else {
            setSecondCategroyOverview(list, tutorStudentOverviewDto);
        }

        //获取某个考试或者科目当前应该完成的任务数
        Map<String, Object> paramMap = new HashMap<String, Object>();
        if (params.getCategoryId() != null) {
            paramMap.put("categoryId", params.getCategoryId());
        }
        if (params.getSecondCategory() != null) {
            List<Long> categoryIdList = Lists.newArrayList();
            for (TutorStudentOverview tutorStudentOverview : list) {
                categoryIdList.add(tutorStudentOverview.getCategoryId());
            }
            paramMap.put("secondCategory", params.getSecondCategory());
            paramMap.put("categoryIdList", categoryIdList);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        paramMap.put("offTime", calendar.getTime());
        paramMap.put("classes", params.getClasses());
        tutorStudentOverviewDto.setStandardCount(tutorTaskDao.getTutorTaskCount(paramMap));

        return tutorStudentOverviewDto;
    }

    public static void main(String[] args){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        System.out.println(calendar.getTime());
    }

    private void setSecondCategroyOverview(List<TutorStudentOverview> list,
            TutorStudentOverviewDto tutorStudentOverviewDto) {
        for (TutorStudentOverview tutorStudentOverview : list) {
            tutorStudentOverviewDto.setActualLength(tutorStudentOverviewDto.getActualLength()
                    + tutorStudentOverview.getActualLength());
            tutorStudentOverviewDto.setActualQuestion(tutorStudentOverviewDto.getActualQuestion()
                    + tutorStudentOverview.getActualQuestion());
            tutorStudentOverviewDto.setActualVideo(tutorStudentOverviewDto.getActualVideo()
                    + tutorStudentOverview.getActualVideo());
            tutorStudentOverviewDto.setCompleteKnowledge(tutorStudentOverviewDto.getCompleteKnowledge()
                    + tutorStudentOverview.getCompleteKnowledge());
            tutorStudentOverviewDto.setCompleteTask(tutorStudentOverviewDto.getCompleteTask()
                    + tutorStudentOverview.getCompleteTask());
            tutorStudentOverviewDto.setNewsCount(tutorStudentOverviewDto.getNewsCount()
                    + tutorStudentOverview.getNewsCount());
            tutorStudentOverviewDto.setPlanKnowledge(tutorStudentOverviewDto.getPlanKnowledge()
                    + tutorStudentOverview.getPlanKnowledge());
            tutorStudentOverviewDto.setPlanLength(tutorStudentOverviewDto.getPlanLength()
                    + tutorStudentOverview.getPlanLength());
            tutorStudentOverviewDto.setPlanQuestion(tutorStudentOverviewDto.getPlanQuestion()
                    + tutorStudentOverview.getPlanQuestion());
            tutorStudentOverviewDto.setPlanTask(tutorStudentOverviewDto.getPlanTask()
                    + tutorStudentOverview.getPlanTask());
            tutorStudentOverviewDto.setPlanVideo(tutorStudentOverviewDto.getPlanVideo()
                    + tutorStudentOverview.getPlanVideo());
            tutorStudentOverviewDto.setPushCount(tutorStudentOverviewDto.getPushCount()
                    + tutorStudentOverview.getPushCount());
            tutorStudentOverviewDto.setWrongKnowledge(tutorStudentOverviewDto.getWrongKnowledge()
                    + tutorStudentOverview.getWrongKnowledge());
            tutorStudentOverviewDto.setWrongQuestion(tutorStudentOverviewDto.getWrongQuestion()
                    + tutorStudentOverview.getWrongQuestion());
        }
    }

}
