package cn.huanju.edu100.study.model.homework.trainbrush;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "user_train_brush_task_result",autoResultMap = true)
public class UserTrainBrushTaskResult {

    public enum TaskTypeEnum {
        QUESTION(0, "题目"),
        PAPER(7, "试卷"),
        ;

        private final Integer value;
        private final String desc;

        TaskTypeEnum(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public Integer getValue() {
            return value;
        }
    }

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 活动id
     */
    private Long groupId;

    /**
     * 任务类型：0-题目；1-记忆卡；2-资料；3-视频；4-直播
     */
    private Integer taskType;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 学习时长（秒）
     */
    private Integer studyDuration;

    /**
     * 平均正确率
     */
    private Double accuracy;

    /**
     * 已完成资源ids
     */
    private String completeIds;

    /**
     * 已学习资源ids
     */
    private String submitIds;

    //学习完成状态：0-未完成 1-直播出勤完成 2-回放完成 3-两者都有
    private Integer completeType;

    //最后学习时间
    private Date lastStudyTime;

    private Date createDate;

    private Date updateDate;

    private Integer completeNum;

    private Integer submitNum;

}
