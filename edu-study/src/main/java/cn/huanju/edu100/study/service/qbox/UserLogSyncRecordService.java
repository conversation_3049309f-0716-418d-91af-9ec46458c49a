package cn.huanju.edu100.study.service.qbox;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.questionBox.UserLogSyncRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 */
public interface UserLogSyncRecordService {
    List<UserLogSyncRecord> findList(UserLogSyncRecord entity) throws DataAccessException;

    Long insert(UserLogSyncRecord entity);

    Integer update(UserLogSyncRecord entity);
}
