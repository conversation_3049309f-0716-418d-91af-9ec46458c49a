/**
 *
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.QueryParam;
import cn.huanju.edu100.study.model.common.ResultCount;
import cn.huanju.edu100.study.model.onetoone.VClassesStudent;
import cn.huanju.edu100.study.model.onetoone.VClassesStudentOrder;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 面授学员班级DAO接口
 * <AUTHOR>
 * @version 2016-04-12
 */
public interface VClassesStudentDao extends CrudDao<VClassesStudent> {

    List<VClassesStudent> findListByQueryParam(QueryParam param, Integer unFinish) throws DataAccessException;

    List<ResultCount> getStudentCountByClsIds(List<Long> clsIds) throws DataAccessException;

    int addLessonCount(VClassesStudentOrder vClassesStudentOrder) throws DataAccessException;

    int updateStatus(VClassesStudent vClassesStudent) throws DataAccessException;

}
