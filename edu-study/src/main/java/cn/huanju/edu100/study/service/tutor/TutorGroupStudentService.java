package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorGroupStudent;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 学员跟分组关联Service
 * 
 * <AUTHOR>
 * @version 2016-01-26
 */
public interface TutorGroupStudentService extends BaseService<TutorGroupStudent> {

    List<TutorGroupStudent> findByGroupIdAndUid(Long groupId,Long uid) throws DataAccessException;
}