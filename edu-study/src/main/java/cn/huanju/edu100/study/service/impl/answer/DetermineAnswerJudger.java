package cn.huanju.edu100.study.service.impl.answer;

import cn.huanju.edu100.study.model.QuestionGroupRelation;
import cn.huanju.edu100.study.model.QuestionTopic;
import com.hqwx.study.entity.UserAnswerDetail;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/5/15.
 */
public class DetermineAnswerJudger extends SingleChoiceAnswerJudger {

    @Override
    public double calculateScore(QuestionTopic topic, String[] answer,Long paperId) {
    	Double score = getScore(topic,paperId);
        return judge(topic,answer) == UserAnswerDetail.IsRight.RIGHT ? score :
                (topic.getScoreRule() == QuestionTopic.ScoreRule.DEDUCTION ? - score : 0);
    }
    @Override
    public double calculateScores(QuestionTopic topic, String[] answer,Long paperId,Map<Long,QuestionGroupRelation> map) {
        Double score = getScores(topic,paperId,map);
        return judge(topic,answer) == UserAnswerDetail.IsRight.RIGHT ? score :
                (topic.getScoreRule() == QuestionTopic.ScoreRule.DEDUCTION ? - score : 0);
    }
}
