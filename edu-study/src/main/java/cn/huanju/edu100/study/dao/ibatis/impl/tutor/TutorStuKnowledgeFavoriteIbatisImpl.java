/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorStuKnowledgeFavoriteDao;
import cn.huanju.edu100.study.model.tutor.TutorSectionTask;
import cn.huanju.edu100.study.model.tutor.TutorStuKnowledgeFavorite;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.IdUtils;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生微课班知识点收藏DAO接口
 * <AUTHOR>
 * @version 2017-12-28
 */
public class TutorStuKnowledgeFavoriteIbatisImpl extends CrudIbatisImpl2<TutorStuKnowledgeFavorite> implements
		TutorStuKnowledgeFavoriteDao {

	public TutorStuKnowledgeFavoriteIbatisImpl() {
		super("TutorStuKnowledgeFavorite");
	}


	@Override
	public List<TutorStuKnowledgeFavorite> getByUidAndKIds(Long uid, Long weikeId, List<Long> kIds) throws DataAccessException {
		if (!IdUtils.isValid(uid) || !IdUtils.isValid(weikeId) || CollectionUtils.isEmpty(kIds)) {
			logger.error("param illegal,  uid or weikeId or kIds is null");
			throw new DataAccessException("param illegal,  uid or weikeId or kIds is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("weikeId", weikeId);
			param.put("knowledgeIds", kIds);
			return sqlMap.queryForList(namespace.concat(".getByUidAndKIds"), param);
		} catch (SQLException e) {
			logger.error("getByUidAndKIds SQLException.uid:{}, weikeId :{}, kIds :{}", uid, weikeId, kIds, e);
			throw new DataAccessException("getByUidAndKIds SQLException error" + e.getMessage());
		}
	}

	@Override
	public boolean updateStatus(Long id, Integer status) throws DataAccessException {
		if (!IdUtils.isValid(id) || null == status) {
			logger.error("param illegal, id or status is null");
			throw new DataAccessException("param illegal, id or status is null");
		}

		try {
			SqlMapClient sqlMap = super.getMaster();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("id", id);
			if (Consts.Tutor_Wk_Know_Status.COLLECT == status) {
				param.put("delFlag", "0");
			} else {
				param.put("delFlag", "1");
			}
			int row = sqlMap.delete(namespace.concat(".updateStatus"), param);
			return row >= 1;
		} catch (SQLException e) {
			logger.error("updateStatus {} SQLException.id:{}, status :{}", namespace, id, status, e);
			throw new DataAccessException("updateStatus " + namespace + " SQLException fail.");
		}
	}

	@Override
	public List<TutorSectionTask> listCollectWkTask(Long uid, Long weikeId, Integer type) throws DataAccessException {
		if (!IdUtils.isValid(uid) || !IdUtils.isValid(weikeId)) {
			logger.error("param illegal, uid or weikeId is null");
			throw new DataAccessException("param illegal, uid or weikeId is null");
		}


		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = Maps.newHashMap();
			param.put("uid", uid);
			param.put("weikeId", weikeId);
			param.put("type", type);
			List<TutorSectionTask>  resutlLit = sqlMap.queryForList(namespace.concat(".listCollectWkTask"), param);
			return resutlLit;
		} catch (SQLException e) {
			logger.error("listCollectWkTask {} SQLException.uid:{}, weikeId:{}, type:{}", namespace, uid, weikeId, type, e);
			throw new DataAccessException("listCollectWkTask SQLException error" + e.getMessage());
		}
	}

	@Override
	public boolean insertBatch(Collection<TutorStuKnowledgeFavorite> favorites) throws DataAccessException {

		if (CollectionUtils.isEmpty(favorites)) {
			logger.error("batchInsert get error, parameter is empty");
			throw new DataAccessException("insertBatch get error, parameter is empty");
		}
		try {
			SqlMapClient sqlMap = super.getMaster();
			sqlMap.insert(namespace.concat(".insertBatch"), favorites);
			return true;
		} catch (SQLException e) {
			logger.error("insertBatch SQLException.", e);
			throw new DataAccessException("insertBatch get SQLException error" + e.getMessage());
		} catch (Exception e) {
			logger.error("insertBatch SException.", e);
			throw new DataAccessException("insertBatch get Exception error" + e.getMessage());
		}
	}
}
