package cn.huanju.edu100.study.service.impl.onetoone;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.onetoone.VResourceDao;
import cn.huanju.edu100.study.model.onetoone.VResource;
import cn.huanju.edu100.study.service.onetoone.VResourceService;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 留学资源Service
 * <AUTHOR>
 * @version 2016-12-12
 */
@Service
public class VResourceServiceImpl extends BaseServiceImpl<VResourceDao, VResource> implements VResourceService {

    @Override
    public Map<Long, VResource> getResourceMapByIds(Set<Long> ids) throws DataAccessException {

        if (CollectionUtils.isEmpty(ids)) {
            logger.error("getResourceMapByIds fail, empty ids");
            return Collections.emptyMap();
        }

        Map<String, Object> param = Maps.newHashMap();
        List<Long> idList = new ArrayList<Long>();
        param.put("ids", idList);
//        param.put("status", Consts.VResource_Status.USERABLE);
        List<VResource> resources = findListByParam(param);
        if (CollectionUtils.isNotEmpty(resources)) {
            Map<Long, VResource> resourceMap = Maps.newHashMap();
            for (VResource vResource : resources) {
                resourceMap.put(vResource.getId(), vResource);
            }

            return resourceMap;
        }

        return Collections.emptyMap();
    }

    @Override
    public List<VResource> findListByParam(Map<String, Object> params) throws DataAccessException {

        if (MapUtils.isEmpty(params)) {
            logger.error("findListByParam fail, emtpy params");
            throw new DataAccessException("empty param map");
        }

        return dao.findListByParam(params);
    }

    @Override
    public Integer findCountByParam(Map<String, Object> params) throws DataAccessException {

        if (MapUtils.isEmpty(params)) {
            logger.error("findCountByParam fail, emtpy params");
            throw new DataAccessException("empty param map");
        }

        return dao.findCountByParam(params);
    }

}
