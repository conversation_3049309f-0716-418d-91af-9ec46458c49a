package cn.huanju.edu100.study.resource.feigncall;


import cn.huanju.edu100.study.config.FeignConfig;
import cn.huanju.edu100.study.resource.feigncall.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;

@FeignClient(value = "hq-ai-agent" , url = "${hq-ai-agent.url}", configuration = FeignConfig.class)
public interface EduStudyAssistantFeign {

    //根据入口获取场景撇只列表
    @PostMapping("/api/v2/business/cueWordList")
    ResponseEntity<ResultBody<List<CueWordVO>>> cueWordList(@Validated @RequestBody EntryExamCategoryParameter entryExamCategoryParameter);

    //非流聊天接口
    @PostMapping("/api/v2/assistant/question")
    ResponseEntity<ResultBody<AiAssistantResponseDTO>> question(@RequestBody AiAsstantRequest request);
}
