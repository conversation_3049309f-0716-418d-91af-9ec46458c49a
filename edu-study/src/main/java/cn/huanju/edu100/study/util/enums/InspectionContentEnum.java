package cn.huanju.edu100.study.util.enums;

/**
 * 考察内容（第一级）
 */
public enum InspectionContentEnum {

    society_work_info(1, "用户社会和职业信息"),
    exam_info(2, "用户考试意向信息"),
    study_info(3, "用户学习信息"),
    market_info(4, "营销信息"),
    work_develop_info(5, "职业发展"),
    other_info(9, "其他"),
    ;

    private Integer val;

    private String desc;

    InspectionContentEnum(Integer val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public Integer getVal() {
        return val;
    }

    public String getDesc() {
        return desc;
    }
}
