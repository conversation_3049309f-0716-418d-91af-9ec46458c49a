package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.service.UserQBoxRmErrQuestionService;
import cn.huanju.edu100.study.service.UserQuestionBoxService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
@Component
public class NewUserQuestionBoxThriftImpl  extends AbstractServiceThrift{
    private static Logger logger = LoggerFactory.getLogger(NewUserQuestionBoxThriftImpl.class);
    static Gson gson = GsonUtil.getGson();
    @Autowired
    private UserQuestionBoxService userQuestionBoxService;
    @Autowired
    private UserQBoxRmErrQuestionService userQBoxRmErrQuestionService;
    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    /**
     * 获取用户已做错的题目情况
     * param uid
     * param boxId
     * param objType(所有/章节/知识点/题型)
     * param objId(对应的业务id)
     * param isTotal（是否只查总数）
     * param from
     * param row
     *
     * */
    public response sty_getNewUserWrongBoxQuestionInfo(request req) throws BusinessException {
        String entry = "sty_getNewUserWrongBoxQuestionInfo";
        long start = System.currentTimeMillis();
        enterValidatorWithLessLog(entry, start, req);
        response res = new response();
        try {
            Map<String,Double> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || param.get("uid")<=0
                    || param.get("box_id") == null || param.get("box_id")<=0
                    || param.get("obj_type") == null || param.get("obj_type")<0
                    || (param.get("obj_type").intValue() != 0 && param.get("obj_type").intValue() != 3 && (param.get("teach_book_id") == null || param.get("teach_book_id") <= 0) )
                    || param.get("is_total") == null || param.get("is_total")<0
                    || (param.get("obj_type").intValue() != 0 && param.get("obj_id") == null )) {

                logger.error("{} sty_getNewUserWrongBoxQuestionInfo parameter lose, uid or box_id or obj_id or teach_book_id or is_total is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getNewUserWrongBoxQuestionInfo parameter lose, uid or box_id or obj_id or teach_book_id or is_total is null.");
            }else {

                Long uid = param.get("uid").longValue() ;
                Long boxId = param.get("box_id").longValue() ;
//                Long teachBookId = null;
                Long bookId = param.get("teach_book_id").longValue();

                Integer objType = param.get("obj_type").intValue();
                Long objId = null;

                if (objType.intValue() != 0) {
                    objId = param.get("obj_id").longValue();
                }
                Integer isTotal = param.get("is_total").intValue();
                //（0：所有，1：章节，2：知识点，3：题型）
                if (param.get("obj_type").intValue() != 0 && param.get("obj_type").intValue() != 3) {
                    bookId = param.get("teach_book_id").longValue();
                }

                //如果不是单单获取总数的话，需判断是否传了from和rows
                if (isTotal.intValue() == Consts.IsTotal.NO && (param.get("from") == null || param.get("from").intValue() <0 || param.get("rows")==null || param.get("rows").intValue()<0)) {
                    res.setCode(Constants.PARAM_LOSE);
                    res.setErrormsg("sty_getNewUserWrongBoxQuestionInfo parameter lose, from or rows is null.");
                }else {
                    //此key标识有错题本新增错题，进入到错题本，消除此key
                    String hasNewWrongKey = "has_new_wrong_quesiton_uid"+uid+"_teachBookId:"+bookId;
                    compatableRedisClusterClient.del(hasNewWrongKey);
                    //List<Long> userWrongQuestionList = userQuestionBoxService.getUserWrongBoxQuestionInfo(uid, boxId, teachBookId, objId, objType);
                    List<Long> userWrongQuestionList = userQuestionBoxService.getNewUserWrongBoxQuestionInfo(uid, boxId, bookId, objId, objType);

                    Integer objTypeOri = objType == 3 ? 0 : objType;//这里如果是按照题型来取的，就直接拿所有id去过滤就行了
                    List<Long> boxQuestionList = new ArrayList<>();
                    if (param.get("origin") != null && param.get("origin").intValue() == 2){
                        Integer origin = param.get("origin").intValue();
                        boxQuestionList = userQuestionBoxService.getShuyeQuestionIds(boxId, bookId, objId, objTypeOri, origin);
                    }else{
                        boxQuestionList = userQuestionBoxService.getNewBoxQuestionIds(boxId, bookId, objId, objTypeOri);
                    }


                    //将当前总集合和用户的错题集合进去差集过滤，过滤掉当前总集合中不再存在的题目。（mark：产品要求这么做，以此来解决总题目数小于当前用户错题数的问题）
                    List<Long> oriUserWrongList = new ArrayList<Long>();
                    if (boxQuestionList != null && !boxQuestionList.isEmpty()) {//这里加下判断，极限情况下，题库中当前数据全部被清空了，就不要过滤了。
                        oriUserWrongList = (List<Long>) CollectionUtils.intersection(boxQuestionList, userWrongQuestionList);
                    }else {
                        oriUserWrongList.addAll(userWrongQuestionList);
                    }

                    if (oriUserWrongList != null && oriUserWrongList.size()>0) {

                        Map<String, Object> result = new HashMap<String, Object>();
                        List<Long> questionIds = new ArrayList<Long>();

                        if (isTotal.intValue() == Consts.IsTotal.NO) {
                            int from = param.get("from").intValue();
                            int rows = param.get("rows").intValue();

                            for (int i = from; (i < from + rows) && i < oriUserWrongList.size(); i++) {
                                questionIds.add(oriUserWrongList.get(i));
                            }
                            result.put("question_ids", questionIds);
                        }

                        result.put("total", oriUserWrongList.size());
                        res.setCode(Constants.SUCCESS);
                        res.setMsg(GsonUtil.toJson(result, req.getAppid()));

                    }else {
                        res.setCode(Constants.OBJ_NOT_EXISTS);
                        res.setErrormsg("sty_getNewUserWrongBoxQuestionInfo return null");
                    }
                }
            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfoWithLessLog(entry, res, start);
        return res;
    }


    /**
     * 题库5.0 用户从错题集中主动移除错题(批量question_id+单个obj_id)
     * param uid
     * param box_id
     * param teach_book_id
     * param obj_id (obj_type为0时，obj_id可不传)
     * param obj_type (1：章节，2：知识点)--->不管是从章节还是从知识点上移除，都需要移除掉对应题目类型上的错题。
     * param question_ids (需要移除的题目Id集合，集合中的题目Id若不存在于当前的obj_id中的话，则不进行移除。)
     * */
    public response sty_userRemoveErrQuestionInBoxNew(request req) throws BusinessException{
        String entry = "sty_userRemoveErrQuestionInBoxNew";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || (Double)param.get("uid")<=0
                    || param.get("box_id") == null || (Double)param.get("box_id")<=0
                    || param.get("teach_book_id") == null || (Double)param.get("teach_book_id")<=0
                    || param.get("obj_type") == null || (Double)param.get("obj_type")<=0 || param.get("question_ids") == null
                    || param.get("obj_id") == null || (Double)param.get("obj_id")<=0 ) {

                logger.error("{} sty_userRemoveErrQuestionInBoxNew parameter lose, uid or box_id or book_id or obj_id or obj_type or question_ids is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_userRemoveErrQuestionInBoxNew parameter lose, uid or box_id or book_id or obj_id or obj_type or question_ids is null.");
            }else {
                Long uid = ((Double)param.get("uid")).longValue() ;
                Long boxId = ((Double)param.get("box_id")).longValue() ;
                Long bookId = ((Double)param.get("teach_book_id")).longValue() ;
                Integer objType = ((Double)param.get("obj_type")).intValue();
                Long objId = null;

                if (objType.intValue() != 0) {
                    objId = ((Double)param.get("obj_id")).longValue();
                }

                List<Long> questionIds = new ArrayList<Long>();
                if (param.get("question_ids") != null) {
                    questionIds = gson.fromJson(param.get("question_ids").toString(), new TypeToken<List<Long>>(){}.getType());
                }
                if (questionIds.isEmpty()) {
                    res.setCode(Constants.PARAM_LOSE);
                    res.setErrormsg("sty_userRemoveErrQuestionInBoxNew parameter lose, question_ids is null.");
                }else if (questionIds.size() > 30) {//这里需要限制一下每次移除的题目Id数量
                    res.setCode(Constants.PARAM_INVALID);
                    res.setErrormsg("sty_userRemoveErrQuestionInBoxNew parameter invalid, question_ids is too big. max size is 30!");
                }else {
                    userQBoxRmErrQuestionService.updateUserRmErrQuestionLog(uid,boxId,bookId,objId,objType,questionIds);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg("sty_userRemoveErrQuestionInBoxNew success!");
                }

            }

        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 题库5.0 用户从错题集中主动移除错题(批量obj_id+单个question_id)
     * param uid
     * param box_id
     * param teach_book_id
     * param obj_ids (obj_type为0时，obj_id可不传)
     * param obj_type (1：章节，2：知识点)--->不管是从章节还是从知识点上移除，都需要移除掉对应题目类型上的错题。
     * param question_id (需要移除的题目Id集合，集合中的题目Id若不存在于当前的obj_id中的话，则不进行移除。)
     * */
    public response sty_userRmErrQuestionInBoxBatchObjNew(request req) throws BusinessException{
        String entry = "sty_userRmErrQuestionInBoxBatchObjNew";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || (Double)param.get("uid")<=0
                    || param.get("box_id") == null || (Double)param.get("box_id")<=0
                    || param.get("teach_book_id") == null || (Double)param.get("teach_book_id")<=0
                    || param.get("obj_type") == null || (Double)param.get("obj_type")<=0 || param.get("obj_ids") == null
                    || param.get("question_id") == null || (Double)param.get("question_id")<=0 ) {
                logger.error("{} sty_userRmErrQuestionInBoxBatchObjNew parameter lose, uid or box_id or book_id or obj_ids or obj_type or question_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_userRmErrQuestionInBoxBatchObjNew parameter lose, uid or box_id or book_id or obj_ids or obj_type or question_id is null.");
            }else {
                Long uid = ((Double)param.get("uid")).longValue() ;
                Long boxId = ((Double)param.get("box_id")).longValue() ;
                Long bookId = ((Double)param.get("teach_book_id")).longValue() ;
                Integer objType = ((Double)param.get("obj_type")).intValue();
                Long questionId = ((Double)param.get("question_id")).longValue();

                List<Long> objIds = new ArrayList<Long>();
                if (param.get("obj_ids") != null) {
                    objIds = gson.fromJson(param.get("obj_ids").toString(), new TypeToken<List<Long>>(){}.getType());
                }
                if (objIds.isEmpty()) {
                    res.setCode(Constants.PARAM_LOSE);
                    res.setErrormsg("sty_userRmErrQuestionInBoxBatchObjNew parameter lose, obj_ids is null.");
                }else if (objIds.size() > 30) {//这里需要限制一下每次移除的obj_id数量
                    res.setCode(Constants.PARAM_INVALID);
                    res.setErrormsg("sty_userRmErrQuestionInBoxBatchObjNew parameter invalid, obj_ids is too big. max size is 30!");
                }else {
                    userQBoxRmErrQuestionService.updateUserRmErrQuestionLogBatchObj(uid, boxId, bookId, objIds, objType, questionId);
                    res.setCode(Constants.SUCCESS);
                    res.setMsg("userRemoveErrQuestionInBox success!");
                }
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    /**
     * 题库5.0 获取用户已做题目数量和该科目下题目总数
     * @param req
     * @return
     * @throws BusinessException
     */
    public response sty_getUserQuestionCount(request req) throws BusinessException{
        String entry = "sty_getUserQuestionCount";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String,Object> param = gson.fromJson(req.getMsg(),Map.class);
            if (param == null || param.get("uid") == null || (Double)param.get("uid")<=0
                    || param.get("box_id") == null || (Double)param.get("box_id")<=0
                    || param.get("teach_book_id") == null || (Double)param.get("teach_book_id")<=0 ) {
                logger.error("{} sty_getUserQuestionCount parameter lose, uid or book_id is null.", entry);
                res.setCode(Constants.PARAM_LOSE);
                res.setErrormsg("sty_getUserQuestionCount parameter lose, uid or book_id is null.");
            }else {
                Long uid = ((Double)param.get("uid")).longValue() ;
                Long boxId = ((Double)param.get("box_id")).longValue() ;
                Long bookId = ((Double)param.get("teach_book_id")).longValue() ;
                Long objId = ((Double)param.get("obj_id")).longValue();
                Integer objType = ((Double)param.get("obj_type")).intValue();

                Map<String, Object> result = new HashMap<>();
                List<Long> totalIdList = userQuestionBoxService.getNewBoxQuestionIds(boxId, bookId, objId, objType);
                List<Long> doneIdList  = userQuestionBoxService.getNewUserDoneQuestionIdList(uid, boxId, bookId, objId, objType);
                if (CollectionUtils.isNotEmpty(totalIdList) && CollectionUtils.isNotEmpty(doneIdList)){
                    Collection<Long> doneIdCollect = CollectionUtils.intersection(totalIdList, doneIdList);
                    result.put("totalCount", totalIdList.size());
                    result.put("doneCount", doneIdCollect.size());
                } else {
                    result.put("doneCount", 0);
                }
                if(CollectionUtils.isNotEmpty(totalIdList)){
                    result.put("totalCount", totalIdList.size());
                } else {
                    result.put("totalCount", 0);
                }
                res.setCode(Constants.SUCCESS);
                res.setMsg(gson.toJson(result));
            }
        } catch (DataAccessException e) {
            res = dataAccessException(entry, req, e);
        } catch (Exception e) {
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
