package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.service.UserAnswerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 增量-（产品排课、新课程表排课）补充作业id
 */
@Service
public class SyncHomeworkIdIncreProductAdaptiveLearningJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SyncHomeworkIdIncreProductAdaptiveLearningJobHandler.class);
    @Autowired
    private UserAnswerService userAnswerService;

    @XxlJob("SyncHomeworkIdIncreProductAdaptiveLearningJobHandler")
    public ReturnT<String> execute(String param) throws Exception {
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("SyncHomeworkIdIncreProductAdaptiveLearningJobHandler 分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);
        if (zoneIndex == 0) {
            logger.info("------SyncHomeworkIdIncreProductAdaptiveLearningJobHandler start------");
            Boolean rs = userAnswerService.syncHomeworkIdIncreProductAdaptiveLearning();
            userAnswerService.setAlUserStudyPathRealMaxIdInRedis();
            logger.info("------SyncHomeworkIdIncreProductAdaptiveLearningJobHandler end------rs:{}", rs);
        }
        return ReturnT.SUCCESS;
    }

}
