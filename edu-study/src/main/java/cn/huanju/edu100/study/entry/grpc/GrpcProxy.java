package cn.huanju.edu100.study.entry.grpc;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.grpc.metadata.GrpcRequest;
import cn.huanju.edu100.grpc.metadata.GrpcResponse;
import cn.huanju.edu100.grpc.server.ServerCons;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import co.elastic.apm.api.ElasticApm;
import co.elastic.apm.api.Transaction;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.protobuf.util.JsonFormat;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.net.InetSocketAddress;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class GrpcProxy<T> implements InvocationHandler, org.springframework.cglib.proxy.InvocationHandler {
    private Object proxy;
    private T obj;
    private static Pattern linePattern = Pattern.compile("_(\\w)");
    private static Pattern humpPattern = Pattern.compile("[A-Z]");
    private static Map<String,String> functionMap = new ConcurrentHashMap<>();
    public GrpcProxy(Object proxy, T obj){
        this.proxy = proxy;
        this.obj = obj;
        initAllMethod(obj.getClass());
    }

    protected static String toLowerCaseFirstOne(String s){
        if(Character.isLowerCase(s.charAt(0))) {
            return s;
        }else {
            return (new StringBuilder()).append(Character.toLowerCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }

    protected static String lineToHump(String str) {
        //do not lower string
//        str = str.toLowerCase();
        Matcher matcher = linePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    protected static String humpToLine(String str) {
        Matcher matcher = humpPattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    protected void initAllMethod(Class clazz){
        Method[] methods = clazz.getMethods();
        for (Method method: methods){
            functionMap.put(lineToHump(method.getName()),method.getName());
        }
    }

    protected Method getMethod(Class clazz,String methodName,Class args){
        try {
            if(functionMap.containsKey(methodName)) {
                String orgMethodName = functionMap.get(methodName);
                Method invokeMethod = clazz.getMethod(orgMethodName, args);
                return invokeMethod;
            }else{
                return null;
            }
        } catch (NoSuchMethodException e) {
            return null;
        }
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        response res = null;
        String methodName = method.getName();
        request req = new request();
        GrpcRequest grpcRequest = (GrpcRequest) args[0];
        Object traceId = ServerCons.C_TRACE_ID.get();
        MDC.remove("unionId");
        MDC.remove("uids");
        if(traceId!= null && StringUtils.isNotBlank(traceId.toString())){
            MDC.put(ServerCons.TRACE_ID, traceId.toString());
        }else {
            if (StringUtils.isNotBlank(grpcRequest.getTraceId())) {
                MDC.put(ServerCons.TRACE_ID, grpcRequest.getTraceId());
            } else {
                MDC.put(ServerCons.TRACE_ID, UUID.randomUUID().toString().replace("-", ""));
            }
        }

        if(ServerCons.C_CLIENTIP.get() != null){
            MDC.remove(ServerCons.REQUEST_IP);
            Object TRANSPORT_ATTR_REMOTE_ADDR = ServerCons.C_CLIENTIP.get();
            if(TRANSPORT_ATTR_REMOTE_ADDR != null){
                if(TRANSPORT_ATTR_REMOTE_ADDR instanceof InetSocketAddress){
                    MDC.put(ServerCons.REQUEST_IP,((InetSocketAddress)TRANSPORT_ATTR_REMOTE_ADDR).getHostName());
                }else {
                    MDC.put(ServerCons.REQUEST_IP, (String) TRANSPORT_ATTR_REMOTE_ADDR);
                }
            }
        }

        if(!method.getName().toLowerCase().contains("ping")) {
            log.info("Processed GRPC info >>>: " + method.getName() +" param:"+ JsonFormat.printer().omittingInsignificantWhitespace().print(grpcRequest));
        }
        if(ServerCons.C_CALLTIME.get() != null){
            Object calltimeObj = ServerCons.C_CALLTIME.get();
            long client_calltime = 0L;
            if(calltimeObj instanceof Long){
                client_calltime = (long) calltimeObj;
            }else if(calltimeObj instanceof String){
                try {
                    client_calltime = Long.valueOf((String)calltimeObj);
                }catch (Exception e){}
            }
            if(client_calltime > 0) {
                Long timeDiff = System.currentTimeMillis() - client_calltime;
                if (timeDiff > 500) {
                    log.warn("study >>>{} 调用时间间隔({}) > 500ms", method.getName(), timeDiff);
                }
            }
        }
        StreamObserver<GrpcResponse> responseObserver = (StreamObserver<GrpcResponse>) args[1];
        req.setTraceId(grpcRequest.getTraceId());
        req.setClient_ip(grpcRequest.getClientIp());
//        req.setTimestamp(grpcRequest.gett)
        req.setMsg(grpcRequest.getMsg());
        req.setAppid(grpcRequest.getAppid());
        req.setPschId(grpcRequest.getPschId());
        req.setSchId(grpcRequest.getSchId());
        req.setContextId(grpcRequest.getContextId());
        req.setCodetype(grpcRequest.getCodetype());
        GrpcResponse.Builder resp = GrpcResponse.newBuilder();
        try {
            if("grpcPing".equalsIgnoreCase(methodName) || "grpc_ping".equalsIgnoreCase(methodName)){
                resp.setCode(1);
                resp.setCodetype(1);
                responseObserver.onNext(resp.build());
                responseObserver.onCompleted();
                return null;
            }
            Method invokeMethod = getMethod(obj.getClass(),methodName,request.class);//ProductThriftImpl.class.getMethod(methodName, request.class);
            if(invokeMethod != null) {
                res = (response) invokeMethod.invoke(obj, req);
            }else{
                throw new Exception("unkown function"+methodName);
            }
        }catch (Throwable e){
            catchException(e);
            resp.setCode(-1);
            resp.setCodetype(200);
            if(null != e.getMessage()) {
                resp.setMsg(e.getMessage());
            }
            if(null != e.getMessage()) {
                resp.setErrormsg(e.getMessage());
            }
            responseObserver.onNext(resp.build());
            responseObserver.onCompleted();
            return null;
        } finally {
            MDC.remove("unionId");
            MDC.remove("uids");
        }
        resp.setCode(res.getCode());
        resp.setCodetype(res.getCodetype());
        if(null != res.getMsg()) {
            resp.setMsg(res.getMsg());
        }
        if(null != res.getErrormsg()) {
            resp.setErrormsg(res.getErrormsg());
        }
        responseObserver.onNext(resp.build());
        responseObserver.onCompleted();
        return null;

    }
    private static void catchException(Throwable e){
        if(e != null && !(e instanceof BusinessException) && !(e instanceof DataAccessException)) {
            Transaction transaction = ElasticApm.currentTransaction();
            transaction.captureException(e);
            log.error("",e);
        }
    }
}
