/**
 *
 */
package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.persistence.dao.CrudDao;
import com.hqwx.study.entity.UserAnswer;
import cn.huanju.edu100.study.model.adminstudy.StudyCompletionInfoQuery;
import cn.huanju.edu100.study.model.adminstudy.StudyReportQuery;
import cn.huanju.edu100.study.model.adminstudy.UserAnswerCompletion;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 用户答题DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-12
 */
public interface UserAnswerDao extends CrudDao<UserAnswer> {

    List<UserAnswer> queryUserFinishedPaperByGoodsIdAndPaperIds(Long uid, Long goodsId, List<Long> paperIds) throws DataAccessException;

    List<UserAnswer> getUserAnswersByPreClassExercise(Long uid, List<Long> paperIds) throws DataAccessException;

    List<UserAnswer> getUserAnswersByPaperIdsAndObjType(Long uid, List<Long> paperIds, Integer objType) throws DataAccessException;
    
    Long countUserFinishedPaperByGoodsIdAndPaperIds(Long uid, Long goodsId, List<Long> paperIds) throws DataAccessException;

    Long queryStudyStatisticsByParam(Long uid, Long objId, Long productId, String startTime, String endTime) throws DataAccessException;

    List<Map> avgCountList(Map<String, Object> params) throws DataAccessException;

    Integer countByStudyReportQuery(StudyReportQuery params) throws DataAccessException;

    List<UserAnswer> findListByStudyReportQuery(StudyReportQuery params) throws DataAccessException;

    List<UserAnswerCompletion> getUserAnswerCompletionList(StudyCompletionInfoQuery params) throws DataAccessException;

    Integer countUserAnswerCompletion(StudyCompletionInfoQuery params) throws DataAccessException;

    Long getPaperUseTimeByPaperId(Long uid, Long paperId, Long productId) throws DataAccessException;

    Long getUserPaperUseTime(Long uid) throws DataAccessException;

    List<UserAnswer> findAnswerGroupByPaperId(Long uid, List<Long> paperIds, Long goodsId, List<Integer> objTypeList, Integer paperType) throws DataAccessException;

    List<UserAnswer> findLastReadOveredSubjectivePaperAnswers(Long uid, List<Long> paperIds, Long goodsId, Long productId, List<Integer> stateList) throws DataAccessException;
}