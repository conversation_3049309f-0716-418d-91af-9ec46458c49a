package cn.huanju.edu100.study.model;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 分组学生Entity
 * <AUTHOR>
 * @version 2015-05-14
 */
public class GroupStudent extends DataEntity<GroupStudent> {
	
	private static final long serialVersionUID = 1L;
	private Long groupId;		// group_id
	private Long suid;			// 学生ID
	private Integer type;		// 学生类型：0：非问题用户，1:问题用户
	private String memo;		// 备注
	
	public GroupStudent() {
		super();
	}

	public GroupStudent(Long id){
		super(id);
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public Long getSuid() {
		return suid;
	}

	public void setSuid(Long suid) {
		this.suid = suid;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
	
}