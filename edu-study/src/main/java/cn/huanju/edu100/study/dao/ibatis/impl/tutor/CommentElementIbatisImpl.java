/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.CommentElementDao;
import cn.huanju.edu100.study.model.tutor.CommentElement;
import cn.huanju.edu100.util.GsonUtil;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.Map;

/**
 * 评价元素DAO接口
 * <AUTHOR>
 * @version 2017-12-06
 */
public class CommentElementIbatisImpl extends CrudIbatisImpl2<CommentElement> implements
		CommentElementDao {

	public CommentElementIbatisImpl() {
		super("CommentElement");
	}

	@Override
	public CommentElement getByParam(CommentElement commentElement) throws DataAccessException {

		if (null == commentElement) {
			logger.error("getByParam error, parameter commentElement is null");
			throw new DataAccessException("getByParam error,commentElement is null");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> params = transBean2Map(commentElement);
			return (CommentElement) sqlMap.queryForObject(namespace.concat(".getByParam"), params);
		} catch (SQLException e) {
			logger.error("getByParam SQLException.commentElement:{}", GsonUtil.toJson(commentElement), e);
			throw new DataAccessException("getByParam SQLException error" + e.getMessage());
		}
	}
}
