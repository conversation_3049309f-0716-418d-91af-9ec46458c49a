package cn.huanju.edu100.study.config;

import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "wx.miniapp")
public class WxMaProperties {
    private List<Config> configs;

    private Map<String, Config> hqAppIdConfigMap;


    public Map<String,Config> getHqAppIdConfigMap() {
        this.hqAppIdConfigMap = Maps.newHashMap();
        for (Config config : configs) {
            hqAppIdConfigMap.put(config.getHqAppid(), config);
        }
        return hqAppIdConfigMap;
    }
    @Data
    public static class Config{
        /**
         * 设置微信小程序的appid
         */
        private String appid;

        /**
         * 设置微信小程序的Secret
         */
        private String secret;

        /**
         * 设置微信小程序消息服务器配置的token
         */
        private String token;

        /**
         * 设置微信小程序消息服务器配置的EncodingAESKey
         */
        private String aesKey;

        /**
         * 消息格式，XML或者JSON
         */
        private String msgDataFormat;

        /**
         * 用户中心appid
         */
        private String hqAppid;

        /**
         *小程序名称
         * */
        private String name;
    }
}
