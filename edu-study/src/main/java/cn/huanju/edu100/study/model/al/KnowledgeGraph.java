package cn.huanju.edu100.study.model.al;

import cn.huanju.edu100.persistence.model.DataEntity;
import com.google.common.collect.Lists;

import java.util.List;


/**
 * @description
 * <AUTHOR>
 * @version
 * @date 2019-07-19   
 */
public class KnowledgeGraph extends DataEntity<KnowledgeGraph> {

    private static final long serialVersionUID = 1L;

    private String name;

    private Long parentId;

    private String parentIds;

    /**
     * 富文本上传bs2的js
     */
    private String extension;

    private Long firstCategory;

    private Long secondCategory;

    private Long categoryId;

    /**
     * 难度等级
     */
    private Byte klevel;

    /**
     * 状态 1：已生效 0 ：未发布
     */
    private Byte state;

    private String remark;

    private String content;

    /**
     * 相似知识点id
     */
    private String relateIds;

    /**
     * 依赖知识点id
     */
    private String dependIds;

    private Integer estimateValue;

    /**
     * 预估视频时长
     */
    private Integer estimateTime;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 掌握要求
     */
    private Byte kmaster;

    /**
     * 知识点属性
     */
    private Byte ktype;

    private String parentName;

    /**
     * 知识点所属阶段逗号分割
     */
    private String stage;

    /**
     * 知识点类型：1基础知识点，2组合知识点
     */
    private Byte type;

    /**
     * 组合知识点关联的基础知识点
     */
    private String knowledgeGraphIds;
    /**
     * 关联基础知识点的组合知识点
     */
    private List<Long> idList = Lists.newArrayList();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentIds() {
        return parentIds;
    }

    public void setParentIds(String parentIds) {
        this.parentIds = parentIds;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Byte getKlevel() {
        return klevel;
    }

    public void setKlevel(Byte klevel) {
        this.klevel = klevel;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRelateIds() {
        return relateIds;
    }

    public void setRelateIds(String relateIds) {
        this.relateIds = relateIds;
    }

    public String getDependIds() {
        return dependIds;
    }

    public void setDependIds(String dependIds) {
        this.dependIds = dependIds;
    }

    public Integer getEstimateValue() {
        return estimateValue;
    }

    public void setEstimateValue(Integer estimateValue) {
        this.estimateValue = estimateValue;
    }

    public Integer getEstimateTime() {
        return estimateTime;
    }

    public void setEstimateTime(Integer estimateTime) {
        this.estimateTime = estimateTime;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Byte getKmaster() {
        return kmaster;
    }

    public void setKmaster(Byte kmaster) {
        this.kmaster = kmaster;
    }

    public Byte getKtype() {
        return ktype;
    }

    public void setKtype(Byte ktype) {
        this.ktype = ktype;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getKnowledgeGraphIds() {
        return knowledgeGraphIds;
    }

    public void setKnowledgeGraphIds(String knowledgeGraphIds) {
        this.knowledgeGraphIds = knowledgeGraphIds;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }
}