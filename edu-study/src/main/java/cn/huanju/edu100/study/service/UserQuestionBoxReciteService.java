package cn.huanju.edu100.study.service;

import cn.huanju.edu100.exception.DataAccessException;

import java.util.HashMap;
import java.util.List;

public interface UserQuestionBoxReciteService {


	/**
     * (背题模式)根据策略随机抽取X道题目
     * 首先按照用户未看过的维度随机，当用户不存在看过的题目的时候，则随机出题。
     * @param uid
     * @param box_id
     * @param teach_book_id
     * @param obj_id (obj_type为0时，obj_id可不传)
     * @param obj_type (0：所有，1：章节，2：知识点)
     * @param qTypes 题型子过滤项（在处理完所有的随机逻辑后，再进一步过滤）
     * @param num 题目数量(默认值，默认15道)
     * @param random_type 随机模式（0：未做试题，1：错误试题，2：全部试题，3：未做或者错误试题）【可暂时不用这个参数，但代码的扩展性需要写好点。】
     * */
	List<Long> ramdonBoxQuestions4Recite(Long uid, Long boxId,Long teachBookId,
			Long objId, Integer objType, List<Integer> qTypes, Integer randomType, Integer num) throws DataAccessException;

	/**
     * (背题模式)记录用户本次背的题目
     * @param uid
     * @param box_id
     * @param teach_book_id
     * @param obj_id (obj_type为0时，obj_id可不传)
     * @param obj_type (0：所有，1：章节，2：知识点)
     * @param questionIds
     * */
	void recordUserBoxReciteQIds(Long uid, Long boxId, Long teachBookId,
			Long objId, Integer objType, List<Long> questionIds) throws DataAccessException;

	void saveUserReciteQuestions(Long uid, Long boxId, HashMap<String, String> questionIdMap) throws DataAccessException ;
	String getUserReciteQuestions(Long uid, Long boxId, String key) throws DataAccessException ;
}
