package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.service.UserAnswerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 增量-作业（云私塾任务）信息同步至作业管理里
 */
@Service
public class SyncHomeworkIncreProductAdaptiveLearningJobHandler{

    private static final Logger logger = LoggerFactory.getLogger(SyncHomeworkIncreProductAdaptiveLearningJobHandler.class);
    @Autowired
    private UserAnswerService userAnswerService;

    @XxlJob("SyncHomeworkIncreProductAdaptiveLearningJobHandler")
    public ReturnT<String> execute() throws Exception {
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("SyncHomeworkIncreProductAdaptiveLearningJobHandler 分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);
        int curZoneIndex = 0;
        if (zoneTotal > 2) {
            curZoneIndex = 2;
        }
        if (zoneIndex == curZoneIndex) {
            logger.info("------SyncHomeworkIncreProductAdaptiveLearningJobHandler start------");
            Boolean rs = userAnswerService.syncHomeworkIncreProductAdaptiveLearning();
            logger.info("------SyncHomeworkIncreProductAdaptiveLearningJobHandler end------rs:{}", rs);
        }
        return ReturnT.SUCCESS;
    }

}
