package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.async.BatchProcess;
import cn.huanju.edu100.study.dao.UserVideoLogDao;
import cn.huanju.edu100.study.model.UserVideoLog;
import cn.huanju.edu100.study.service.UserVideoLogService;
import cn.huanju.edu100.study.util.Constants;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.util.DateUtils;
import cn.huanju.edu100.exception.DataAccessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 记录学生在学习录播课程过程中的信息Service
 *
 * <AUTHOR>
 * @version 2015-05-18
 */
@Service
public class UserVideoLogHqServiceImpl extends
		BaseServiceImpl<UserVideoLogDao, UserVideoLog> implements
		UserVideoLogService {
	private static Logger logger = LoggerFactory
			.getLogger(UserVideoLogHqServiceImpl.class);
	final static boolean isDebug = true;       // 是否打印日志
	@Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;
	@Autowired
	private BatchProcess<UserVideoLog> batchProcess;

	private int EXPIRE_TIME = 60 * 60 * 25;		// 24小时
//	private int EXPIRE_TIME = 60 * 3;		// 1小时


//	@Override
	public Long getLengthFromCache(Long uid, Long courseId, Long clsId, Long lessonId, String strDt) throws DataAccessException, cn.huanju.edu100.exception.DataAccessException {
		if(null == uid || null == courseId || null == clsId || null == lessonId || StringUtils.isBlank(strDt)) {
			return null;
		}
		String strLength = compatableRedisClusterClient.hget(getUserLogSetKey(uid), getUserLogFieldKey(courseId, clsId, lessonId, strDt));
		if(StringUtils.isNotBlank(strLength)){
			return Long.valueOf(strLength);
		}
		return null;
	}

	/**
	 * 更新到数据库
	 * @param userVideoLog
	 * @return
	 * @throws DataAccessException
	 */
    @Override
	public Long addUserVideoLog(UserVideoLog userVideoLog)
			throws DataAccessException,BusinessException {
		userVideoLogValidator(userVideoLog);
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("uid", userVideoLog.getUid());
		param.put("courseId", userVideoLog.getCourseId());
		param.put("clsId", userVideoLog.getClsId());
		param.put("type", userVideoLog.getType());
		param.put("lessonId", userVideoLog.getLessonId());
		//查询条件加个日期,一天只有一条;查出,时长累加
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		param.put("startTime", sdf.format(new Date()));

		UserVideoLog qryUserVideoLog = dao.findOne(param);

		long result = 0l;
		if (qryUserVideoLog == null) { // 没有，直接入库
			// 数据库中无, 则新增
			result = dao.insertUserVideoLog(userVideoLog);
		} else {
			if(null != userVideoLog.getType() && userVideoLog.getType().equals(Consts.USER_VIDEO_TYPE.DOWN)){	//下载的不再处理
				return qryUserVideoLog.getId();
			}
		    if (userVideoLog.getStatus() != null &&  userVideoLog.getStatus() == 1) {//如果学习记录状态为1标识已完成后面同步不在更新status
		        userVideoLog.setId(qryUserVideoLog.getId());
                result = dao.updateUserVideoLog(userVideoLog);
		    } else {
    			// 有数据，则更新
		        if (qryUserVideoLog.getStatus() != null && qryUserVideoLog.getStatus() > 0) {
	                userVideoLog.setStatus(qryUserVideoLog.getStatus());
		        }
    		    userVideoLog.setId(qryUserVideoLog.getId());
    			result = dao.updateUserVideoLog(userVideoLog);
		    }
		}
		return result;

	}

    @Override
	public void addUserVideoLogCache(UserVideoLog userVideoLog)
            throws DataAccessException,BusinessException {
        userVideoLogValidator(userVideoLog);
        if ((userVideoLog.getStatus() != null &&  userVideoLog.getStatus() == 1) || (userVideoLog.getType() != null && userVideoLog.getType() == Consts.USER_VIDEO_TYPE.DOWN)) {//如果学习记录状态为1标识已完成后面同步不在更新status
//			this.updateLogCache(userVideoLog);		//这里不累加length?
            addUserVideoLog(userVideoLog);
        }
        batchProcess.offer(userVideoLog);
    }
	//记录当期缓存里有哪些用户
    private String getKeyHqUserLog(){
		return Constants.USER_VIDEO_LOG_KeyHqUserLog;
	}
//    private String getUserLogSetKey(){
//        return "UserVideoLogHq_";
//    }

    private String getUserLogSetKey(final Long uid){
        return "UserVideoLogHq_" + uid;
    }


	private String getUserLogRecordKey(final Long uid){
		return "UserVideoLogHq_Re_" + uid;
	}
    private String getDelLogKey() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return "DelUserVideoLogHq_Key_"+sdf.format(new Date());
    }

	public String getUserLogUidFieldKey(UserVideoLog param) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(param.getUid()).append("_").append(getUserLogFieldKey(param.getCourseId(), param.getClsId(), param.getLessonId(), new Date()));
		return stringBuilder.toString();
	}
	private String getUserLogFieldKey(UserVideoLog param) {
		return getUserLogFieldKey(param.getCourseId(), param.getClsId(), param.getLessonId(), new Date());
    }
	private String getUserLogFieldKey(final Long courseId, final Long clsId, final Long lessonId, final String strDt){
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(courseId).append("_").append(clsId).append("_").append(lessonId).append("_").append(strDt);
		return stringBuilder.toString();
	}
	private String getUserLogFieldKey(final Long courseId, final Long clsId, final Long lessonId, final Date dt){
		return getUserLogFieldKey(courseId, clsId, lessonId, DateUtils.getYearToDay(dt));
	}

	@Override
	public Collection<UserVideoLog> queryUserVideoLogsByUidCourseId(Long uid,
			Long courseId) throws DataAccessException,BusinessException {
		if (uid == null || uid <= 0) {
			logger.error("userVideoLog uid is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE, "paramerter uid is null or emtpty.");
		}
		if (courseId == null || courseId <= 0) {
			logger.error("paramerter CourseId is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,
					"paramerter CourseId is null or emtpty.");
		}
		Collection<UserVideoLog> userVideoLogs = dao.queryByUidCourseId(uid,
				courseId);
		return userVideoLogs;
	}

	@Override
	public Collection<UserVideoLog> queryUserVideoLogsByUidCourseIdList(Long uid,
			Collection<Long> courseIds) throws DataAccessException,BusinessException {
		if (uid == null || uid <= 0) {
			logger.error("userVideoLog uid is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,"paramerter uid is null or emtpty.");
		}
		if (courseIds == null || courseIds.isEmpty()) {
			logger.error("paramerter CourseId is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,
					"paramerter CourseId is null or emtpty.");
		}
		Collection<UserVideoLog> userVideoLogs = dao.queryByUidCourseIdList(uid,
				courseIds);
		return userVideoLogs;
	}

	protected static void userVideoLogValidator(UserVideoLog userVideoLog)
			throws BusinessException {

		if (userVideoLog == null || userVideoLog.getUid() == null
				|| userVideoLog.getUid() <= 0) {
			logger.error("userVideoLog uid is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,"paramerter uid is null or emtpty.");
		}
		if (userVideoLog.getCourseId() == null
				|| userVideoLog.getCourseId() <= 0) {
			logger.error("paramerter CourseId is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,
					"paramerter CourseId is null or emtpty.");
		}
		if (null != userVideoLog.getType() && !userVideoLog.getType().equals(Consts.USER_VIDEO_TYPE.DOWN) && (userVideoLog.getClsId() == null || userVideoLog.getClsId() <= 0)) {
			logger.error("paramerter ClsId is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,"paramerter ClsId is null or emtpty.");
		}
		if (userVideoLog.getLessonId() == null
				|| userVideoLog.getLessonId() <= 0) {
			logger.error("paramerter LessonId is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,
					"paramerter LessonId is null or emtpty.");
		}
		if (null != userVideoLog.getType() && !userVideoLog.getType().equals(Consts.USER_VIDEO_TYPE.DOWN) && (userVideoLog.getLength() == null || userVideoLog.getLength() <= 0)) {
			logger.error("paramerter Length is null or emtpty.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,
					"paramerter Length is null or emtpty.");
		}

	}

	/**
	 * 根据用户uid和课程id获取用户最好一次观看的视频
	 */
	@Override
	public UserVideoLog getLastUserVideoLog(long uid, long courseId)
			throws DataAccessException, BusinessException {
		if (uid <= 0 || courseId <= 0) {
			logger.error("paramerter courseId  or uid is not right.");
			throw new BusinessException(cn.huanju.edu100.util.Constants.PARAM_LOSE,
					"  courseId  or uid paramerter  is not right");
		}
		UserVideoLog userVideoLog = dao.getLastUserVideoLog(uid,
				courseId);
		if(null == userVideoLog){
			return null;
		}
		Long logCacheLength = null;

		try {
			logCacheLength = getLengthFromCache(userVideoLog.getUid(), userVideoLog.getCourseId(), userVideoLog.getClsId(), userVideoLog.getLessonId(), DateUtils.formatDateSimple(userVideoLog.getStartTime()));
		} catch (cn.huanju.edu100.exception.DataAccessException e) {
			e.printStackTrace();
		}
		if(null != logCacheLength){
			userVideoLog.setLength(logCacheLength);
		}

		return userVideoLog;
	}

	/**
	 * 添加UserVideoLog 到cache,并记录添加的 UserVideoLog 的key到缓存
	 * @param list
	 * @return
	 * @throws DataAccessException
	 */
	@Override
	public boolean addBatch(Collection<UserVideoLog> list)
			throws DataAccessException {
		UserVideoLog userVideoLog = null;
		Long lengValue = null;
	  	HashMap<String,Long> keyValue = new HashMap<String,Long>();
	  	for (UserVideoLog videoLog : list) {
			if(null == videoLog.getLength()){
				videoLog.setLength(0l);
			}
			videoLog.setUpdateDate(new Date());
	      	String uidField = getUserLogUidFieldKey(videoLog);
			lengValue = keyValue.get(uidField);
	      	if (lengValue != null) {
				lengValue = lengValue + videoLog.getLength();
	          keyValue.put(uidField, lengValue);
	      	} else {
	          keyValue.put(uidField, videoLog.getLength());
	      	}
	  	}
		String key = null;
		String recordKey = null;
		Long uid = null;
		UserVideoLog cachedUserVideoLog = null;
		String field = null;
	  	try {
          	for (String uidField : keyValue.keySet()) {

				key = getUserLogSetKey(Long.valueOf(uidField.substring(0, uidField.indexOf("_"))));

				field = uidField.substring(uidField.indexOf("_") + 1);

				compatableRedisClusterClient.hincrBy(key, field, keyValue.get(uidField));

				compatableRedisClusterClient.expire(key, EXPIRE_TIME); // 这里expire时间没有用
				compatableRedisClusterClient.sadd(getDelLogKey(), uidField);
				compatableRedisClusterClient.expire(getDelLogKey(), EXPIRE_TIME* 3);	//，每日的总的hash3天后失效
				compatableRedisClusterClient.sadd(getKeyHqUserLog(),"hq_uf_" + uidField);
          	}
			for (UserVideoLog videoLog : list) {
				recordKey = "data_update_" + getUserLogUidFieldKey(videoLog);
				compatableRedisClusterClient.setex(recordKey, EXPIRE_TIME, String.valueOf(videoLog.getUpdateDate().getTime()));	//记录数据更新时间
			}
      	} catch (Exception ex) {
          logger.error("addBatch fail", ex);
          throw new DataAccessException("");
      	} finally {
     	 }
	  	return true;
	}
}
