package cn.huanju.edu100.study.entry.thrift;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.adminstudy.HomeworkCompletion;
import cn.huanju.edu100.study.model.adminstudy.StudyCompletionInfoQuery;
import cn.huanju.edu100.study.model.adminstudy.StudyReportQuery;
import cn.huanju.edu100.study.model.adminstudy.UserAnswerCompletion;
import cn.huanju.edu100.study.service.UserAnswerService;
import cn.huanju.edu100.study.service.UserAnswerSumService;
import cn.huanju.edu100.thrift.request;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.DateUtil;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.ParamUtils;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 替换admin100服务里面的study数据数操作
 * @author: wusiyue
 * @create: 2019-05-13 17:23
 **/
@Component
public class AdminStudyThriftImpl extends AbstractServiceThrift{
    private static Logger logger = LoggerFactory.getLogger(AdminStudyThriftImpl.class);
    static Gson gson = GsonUtil.getGson();
    @Autowired
    private UserAnswerService userAnswerService;
    @Autowired
    private UserAnswerSumService userAnswerSumService;

    /**
     * 获取完成的试卷数量
     */
    public response sty_getStudyAvgCountList(request req) throws BusinessException {
        String entry = "sty_getStudyAvgCountList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            List<Map> result = userAnswerService.avgCountList(params);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getStudyAvgCountList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_findListByStudyReportQuery(request req) throws BusinessException {
        String entry = "sty_findListByStudyReportQuery";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            StudyReportQuery params = gson.fromJson(req.getMsg(), StudyReportQuery.class);
            if (params == null) {
                logger.error("{} fail.parameter is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter is null.");
            }
            if (Objects.equal(params.getStudyLogType(), StudyReportQuery.StudyLogType.HOME_WORK)) {
                Integer count = userAnswerService.countByStudyReportQueryHomeWork(params);
                List<UserHomeWorkAnswer> list = Lists.newArrayList();
                if (count > 0) {
                    list = userAnswerService.findListByStudyReportQueryHomeWork(params);
                }
                PageModel<UserHomeWorkAnswer> result = new PageModel<UserHomeWorkAnswer>(count, list);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
            } else {
                Integer count = userAnswerService.countByStudyReportQuery(params);
                List<UserAnswer> list = Lists.newArrayList();
                if (count > 0) {
                    list = userAnswerService.findListByStudyReportQuery(params);
                }
                PageModel<UserAnswer> result = new PageModel<UserAnswer>(count, list);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
            }
        } catch (Exception e) {
            logger.error("sty_findListByStudyReportQuery error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_findHomeworkDetailsList(request req) throws BusinessException {
        String entry = "sty_findHomeworkDetailsList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {

            UserHomeWorkAnswer params = gson.fromJson(req.getMsg(), UserHomeWorkAnswer.class);

            Integer count = userAnswerService.countHomeworkDetails(params);
            List<HomeworkDetails> list=Lists.newArrayList();
            if (count > 0) {
                list = userAnswerService.findHomeworkDetailsList(params);
            }
            PageModel<HomeworkDetails> result = new PageModel<HomeworkDetails>(count, list);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));

        } catch (Exception e) {
            logger.error("sty_findHomeworkDetailsList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getHomeworkDetailsList(request req) throws BusinessException {
        String entry = "sty_getHomeworkDetailsList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserHomeWorkAnswer params = gson.fromJson(req.getMsg(), UserHomeWorkAnswer.class);

            List<UserHomeWorkAnswer> homeworkDetailsList = userAnswerService.getHomeworkDetailsList(params);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(homeworkDetailsList, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getHomeworkDetailsList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_findStudyDetailsList(request req) throws BusinessException {
        String entry = "sty_findStudyDetailsList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAnswer params = gson.fromJson(req.getMsg(), UserAnswer.class);

            Integer count = userAnswerService.countStudyDetailsList(params);
            List<StudyDetails> list=Lists.newArrayList();
            if(count>0){
                list = userAnswerService.findStudyDetailsList(params);
            }
            PageModel<StudyDetails> result = new PageModel<StudyDetails>(count, list);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_findStudyDetailsList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
    public response sty_getUserPaperCompleteDistinct(request req) throws BusinessException {
        String entry = "sty_getUserPaperCompleteDistinct";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String,String> params = gson.fromJson(req.getMsg(), Map.class);
            List<Long> paperIds = new ArrayList<>();
            if(params.get("paperIds") != null){
                String[] split = params.get("paperIds").split(",");
                for (String s : split) {
                    paperIds.add(Long.valueOf(s));
                }
            }
            List<Map<String, Object>> userAnswer = userAnswerService.findUserAnswerPaperCompleteCount(Long.valueOf(params.get("uid")), paperIds);
            Long finishPaperCount = 0L;
            if(!CollectionUtils.isEmpty(userAnswer)){
                List<Long> answerIds = userAnswer.stream().map(m -> Long.valueOf(m.get("answerId").toString())).collect(Collectors.toList());
                List<Map<String, Object>> userAnswerSum = userAnswerService.findUserAnswerSumTotalCount(Long.valueOf(params.get("uid")),answerIds);
                // 行转列
                Map<String,Object> userAnswerSumMap = new HashMap<>();
                for (Map<String, Object> map : userAnswerSum) {
                    userAnswerSumMap.put(map.get("answerId").toString(),map.get("answerTotalNum"));
                }
                // 试卷中答题（非空）数量>=80%且提交则判定为完成，其余情况为未完成
                // 处理过的paperId不再处理
                Set<String> processedPaperId = new HashSet<>();
                for (Map<String, Object> map : userAnswer) {
                    String paperId = map.get("paperId").toString();
                    if(processedPaperId.contains(paperId)){
                        continue;
                    }
                    String answerId = map.get("answerId").toString();
                    Double answerNum = Double.valueOf(map.get("answerNum").toString());
                    Double totalCount = Double.valueOf(userAnswerSumMap.getOrDefault(answerId, 0).toString());

                    if(!Objects.equal(totalCount,0L) && answerNum/totalCount > 0.8){
                        finishPaperCount++;
                        processedPaperId.add(paperId);
                    }
                }

            }
            Map<String,String> result = new HashMap<>();
            result.put("finishPaperCount",finishPaperCount.toString());
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserPaperCompleteDistinct error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
    public response sty_getUserHomeworkCompleteDistinct(request req) throws BusinessException {
        String entry = "sty_getUserHomeworkCompleteDistinct";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        Map<String,Object> result = new HashMap<>();
        try {
            Map<String,String> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long goodsId = ParamUtils.getLong(params,"goodsId",true);
            String questionIdsStr = ParamUtils.getString(params, "questionIds", true);
            String startDate = ParamUtils.getString(params, "startDate", true);
            String endDate = ParamUtils.getString(params, "endDate", true);

            Date startTime1 = null;
            Date endTime1 = null;
            if(StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)){
                try {
                    startTime1 = DateUtil.stringToDate(startDate, DateUtil.FORMAT_YMDHMS);
                    endTime1 = DateUtil.stringToDate(endDate,DateUtil.FORMAT_YMDHMS);
                } catch (Exception e) {
                    logger.error("sty_getUserHomeworkCompleteDistinct error parse date-->" ,e);
                }
            }
            List<Long> questionIds = new ArrayList<>();
            if(questionIdsStr != null){
                String[] split = questionIdsStr.split(",");
                for (String s : split) {
                    questionIds.add(Long.valueOf(s));
                }
            }
            Map<String,String> completeCountResult = userAnswerService.findUserHomeworkCompleteCount(uid, questionIds,startTime1,endTime1,goodsId);
            if(completeCountResult!=null){
                result.put("finishHomeworkCount",completeCountResult.get("finishHomeworkCount"));
            }

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserPaperCompleteDistinct error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getUserHomeworkCompleteQustionIds(request req) throws BusinessException {
        String entry = "sty_getUserHomeworkCompleteQustionIds";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        Map<String,Object> result = new HashMap<>();
        try {
            Map<String,String> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long goodsId = ParamUtils.getLong(params,"goodsId",true);
            String questionIdsStr = ParamUtils.getString(params, "questionIds", true);
            String startDate = ParamUtils.getString(params, "startDate", true);
            String endDate = ParamUtils.getString(params, "endDate", true);

            Date startTime1 = null;
            Date endTime1 = null;
            if(StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)){
                try {
                    startTime1 = DateUtil.stringToDate(startDate, DateUtil.FORMAT_YMDHMS);
                    endTime1 = DateUtil.stringToDate(endDate,DateUtil.FORMAT_YMDHMS);
                } catch (Exception e) {
                    logger.error("sty_getUserHomeworkCompleteQustionIds error parse date-->" ,e);
                }
            }
            List<Long> questionIds = new ArrayList<>();
            if(questionIdsStr != null){
                String[] split = questionIdsStr.split(",");
                for (String s : split) {
                    questionIds.add(Long.valueOf(s));
                }
            }
            List<Long> completeQuestionIds=userAnswerService.findUserHomeworkCompleteQuestionIds(uid, questionIds, startTime1, endTime1, goodsId);
            result.put("completeQuestionIds",completeQuestionIds);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserHomeworkCompleteQustionIds error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }


    public response sty_getUserAnswerCompletionList(request req) throws BusinessException {
        String entry = "sty_getUserAnswerCompletionList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            StudyCompletionInfoQuery params = gson.fromJson(req.getMsg(), StudyCompletionInfoQuery.class);
            if (params == null) {
                logger.error("{} fail.parameter is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter is null.");
            }
                Integer count = userAnswerService.countUserAnswerCompletion(params);
                List<UserAnswerCompletion> list = Lists.newArrayList();
                if (count > 0) {
                    list = userAnswerService.getUserAnswerCompletionList(params);
                }
                PageModel<UserAnswerCompletion> result = new PageModel<UserAnswerCompletion>(count, list);
                res.setCode(Constants.SUCCESS);
                res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getUserAnswerCompletionList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getHomeworkCompletionList(request req) throws BusinessException {
        String entry = "sty_getHomeworkCompletionList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            StudyCompletionInfoQuery params = gson.fromJson(req.getMsg(), StudyCompletionInfoQuery.class);
            if (params == null) {
                logger.error("{} fail.parameter is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter is null.");
            }

            List<HomeworkCompletion> result = userAnswerService.getHomeworkCompletionList(params);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getHomeworkCompletionList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_getAnswerDetailList(request req) throws BusinessException {
        String entry = "sty_getAnswerDetailList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAnswerDetail params = gson.fromJson(req.getMsg(), UserAnswerDetail.class);
            if (params == null) {
                logger.error("{} fail.parameter is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "parameter is null.");
            }
            if (null == params.getUid()) {
                logger.error("{} fail.parameter is null.", entry);
                throw new BusinessException(Constants.PARAM_INVALID, "uid is null.");
            }

            List<UserAnswerDetail> result = userAnswerSumService.getAnswerDetailList(params);
            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_getAnswerDetailList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_adminstudyfindList(request req) throws BusinessException {
        String entry = "sty_adminstudyfindList";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            UserAnswer params = gson.fromJson(req.getMsg(), UserAnswer.class);
            List<UserAnswer> result = userAnswerService.adminstudyfindList(params);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_adminstudyfindList error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_adminstudyfindAnswerSumQuestion(request req) throws BusinessException {
        String entry = "sty_adminstudyfindAnswerSumQuestion";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);

            Long uid = ParamUtils.getLong(params, "uid", false);
            List<Long> list = ParamUtils.getLongList(params, "uAnswerIdList",false);

            List<UserAnswerDetail> result = userAnswerSumService.findAnswerSumQuestion(params);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_adminstudyfindAnswerSumQuestion error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_adminstudygetQuestionListByAnswerId(request req) throws BusinessException {
        String entry = "sty_adminstudygetQuestionListByAnswerId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long answerId = ParamUtils.getLong(params, "answerId", false);

            List<Long> questionIdList = ParamUtils.getLongList(params, "questionIdList",true);
            Long questionId = ParamUtils.getLong(params, "questionId", true);
            Date startTime = ParamUtils.getDateFromLong(params, "startTime", true);
            Date endTime = ParamUtils.getDateFromLong(params, "endTime", true);


            params.put("startTime",startTime);
            params.put("endTime",endTime);

//            Integer count=userAnswerSumService.countQuestionListByAnswerId(params);
            List<UserAnswerDetail> result=userAnswerSumService.getQuestionListByAnswerId(params);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_adminstudygetQuestionListByAnswerId error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }

    public response sty_adminstudygetQuestionListByHomeworkAnswerId(request req) throws BusinessException {
        String entry = "sty_adminstudygetQuestionListByHomeworkAnswerId";
        long start = System.currentTimeMillis();
        enterValidator(entry, start, req);
        response res = new response();
        try {
            Map<String, Object> params = gson.fromJson(req.getMsg(), Map.class);
            Long uid = ParamUtils.getLong(params, "uid", false);
            Long homeworkAnswerId = ParamUtils.getLong(params, "homeworkAnswerId", false);

            List<Long> questionIdList = ParamUtils.getLongList(params, "questionIdList",true);
            Long questionId = ParamUtils.getLong(params, "questionId", true);
            Date startTime = ParamUtils.getDateFromLong(params, "startTime", true);
            Date endTime = ParamUtils.getDateFromLong(params, "endTime", true);


            params.put("startTime",startTime);
            params.put("endTime",endTime);

//            Integer count=userAnswerSumService.countQuestionListByHomeworkAnswerId(params);
            List<UserAnswerDetail> result = userAnswerSumService.getQuestionListByHomeworkAnswerId(params);

            res.setCode(Constants.SUCCESS);
            res.setMsg(GsonUtil.toJson(result, req.getAppid()));
        } catch (Exception e) {
            logger.error("sty_adminstudygetQuestionListByHomeworkAnswerId error !", e);
            res = exception(entry, req, e);
        }
        endInfo(entry, res, start);
        return res;
    }
}
