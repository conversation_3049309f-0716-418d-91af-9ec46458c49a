package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;

/**
 * 用户移除题库错题日志Entity
 *
 * <AUTHOR>
 * @version 2016-05-26
 */
public class UserRmBoxErrQuestionLog extends DataEntity<UserRmBoxErrQuestionLog> {

    private static final long serialVersionUID = 1L;
    transient public int tbidx = 0;//分表

    private Long uid;
    private Long boxId;
    private Long bookId;
    private Long objId;
    private Integer objType;
    private Long questionId;
    private Integer removeType;
    private Long userAnswerHomeworkId;
    private Date removeDate;

    public int getTbidx() {
		return tbidx;
	}
	public void setTbidx(int tbidx) {
		this.tbidx = tbidx;
	}

	public Integer getRemoveType() {
		return removeType;
	}
	public void setRemoveType(Integer removeType) {
		this.removeType = removeType;
	}
	public Long getUid() {
		return uid;
	}
	public void setUid(Long uid) {
		this.uid = uid;
	}
	public Long getBoxId() {
		return boxId;
	}
	public void setBoxId(Long boxId) {
		this.boxId = boxId;
	}
	public Long getBookId() {
		return bookId;
	}
	public void setBookId(Long bookId) {
		this.bookId = bookId;
	}
	public Long getObjId() {
		return objId;
	}
	public void setObjId(Long objId) {
		this.objId = objId;
	}
	public Integer getObjType() {
		return objType;
	}
	public void setObjType(Integer objType) {
		this.objType = objType;
	}
	public Long getQuestionId() {
		return questionId;
	}
	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}
	public Date getRemoveDate() {
		return removeDate;
	}
	public void setRemoveDate(Date removeDate) {
		this.removeDate = removeDate;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Long getUserAnswerHomeworkId() {
		return userAnswerHomeworkId;
	}
	public void setUserAnswerHomeworkId(Long userAnswerHomeworkId) {
		this.userAnswerHomeworkId = userAnswerHomeworkId;
	}

}
