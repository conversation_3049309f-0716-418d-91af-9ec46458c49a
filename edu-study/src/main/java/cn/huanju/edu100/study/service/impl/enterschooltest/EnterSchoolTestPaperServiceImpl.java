package cn.huanju.edu100.study.service.impl.enterschooltest;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.enterschooltest.EnterSchoolTestPaperDao;
import cn.huanju.edu100.study.model.QuestionGroup;
import cn.huanju.edu100.study.model.enterschooltest.EnterSchoolTestPaper;
import cn.huanju.edu100.study.model.enterschooltest.EnterSchoolTestQuestion;
import cn.huanju.edu100.study.model.enterschooltest.EnterSchoolTestQuestionGroup;
import cn.huanju.edu100.study.model.enterschooltest.EnterSchoolTestQuestionKnowledgeGraphRelation;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestPaperService;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestQuestionGroupService;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestQuestionKnowledgeGraphRelationService;
import cn.huanju.edu100.study.service.enterschooltest.EnterSchoolTestQuestionService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 入学测评试卷 Service
 * <AUTHOR>
 * @version 2021-04-27
 */
@Service
public class EnterSchoolTestPaperServiceImpl extends BaseServiceImpl<EnterSchoolTestPaperDao, EnterSchoolTestPaper> implements EnterSchoolTestPaperService {
    private static Logger logger = LoggerFactory.getLogger(EnterSchoolTestPaperServiceImpl.class);

    @Autowired
    private EnterSchoolTestQuestionService enterSchoolTestQuestionService;
    @Autowired
    private EnterSchoolTestQuestionGroupService enterSchoolTestQuestionGroupService;
    @Autowired
    private EnterSchoolTestQuestionKnowledgeGraphRelationService enterSchoolTestQuestionKnowledgeGraphRelationService;

    @Override
    public List<EnterSchoolTestPaper> findEnterSchoolTestPaperList(EnterSchoolTestPaper paper) throws DataAccessException {
        return dao.findList(paper.getPage(),paper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED,value = "transactionManager")
    public EnterSchoolTestPaper insertRelationEnterSchoolTest(Long uid, Long relationPaperId, Long categoryId, Map<String,List<EnterSchoolTestQuestion>> groupMap,
                                                            Map<String, QuestionGroup> groupSourceMap, Map<Long, List<Long>> questionKnowledgeMap) throws DataAccessException {
        //生成试卷
        EnterSchoolTestPaper cpaper = saveUserEnterSchoolTestPaper(uid,categoryId,relationPaperId, 1);
        if (cpaper == null) {
            return null;
        }
        //试题 集合
        List<EnterSchoolTestQuestion> assembleEnterSchoolTestQuestions = Lists.newArrayList();
        //题目排序
        int sort = 0;
        for (String groupMapKey : groupMap.keySet()){
            List<EnterSchoolTestQuestion> groupQuestions = groupMap.get(groupMapKey);
            //生成题目组
            EnterSchoolTestQuestionGroup cgroup = saveRelationUserEnterSchoolTestQuestionGroup(cpaper.getId(),groupSourceMap.get(groupMapKey));
            for (EnterSchoolTestQuestion groupQuestion : groupQuestions){
                groupQuestion.setPaperId(cpaper.getId());
                groupQuestion.setGroupId(cgroup.getId());
                groupQuestion.setCategoryId(categoryId);
                groupQuestion.setSort(++sort);
                assembleEnterSchoolTestQuestions.add(groupQuestion);
            }
        }
        //生成题目
        cpaper.setEnterSchoolTestQuestions(assembleEnterSchoolTestQuestions);
        batchSaveQuestion(assembleEnterSchoolTestQuestions);
        List<EnterSchoolTestQuestionKnowledgeGraphRelation> relations = Lists.newArrayList();
        if (questionKnowledgeMap!=null && questionKnowledgeMap.keySet().size()>0) {
            for (Long questionId : questionKnowledgeMap.keySet()){
                List<Long> questionKnowledges = questionKnowledgeMap.get(questionId);
                if (questionKnowledges == null || questionKnowledges.size()<=0){
                    continue;
                }
                for(Long knowledgeGraphId : questionKnowledges){
                    EnterSchoolTestQuestionKnowledgeGraphRelation ralation = new EnterSchoolTestQuestionKnowledgeGraphRelation();
                    ralation.setPaperId(cpaper.getId());
                    ralation.setQuestionId(questionId);
                    ralation.setKnowledgeGraphId(knowledgeGraphId);
                    relations.add(ralation);
                }
            }
        }
        //生成题目和知识点关联
        batchSaveQuestionKnowledgeGraphRelaion(relations);
        return cpaper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED,value = "transactionManager")
    public EnterSchoolTestPaper insertEnterSchoolTest(Long uid, Long categoryId, Map<String,List<EnterSchoolTestQuestion>> groupMap, Map<Long, List<Long>> questionKnowledgeMap) throws DataAccessException {
        //生成试卷
        EnterSchoolTestPaper cpaper = saveUserEnterSchoolTestPaper(uid,categoryId,null, 0);
        //试题 集合
        List<EnterSchoolTestQuestion> assembleEnterSchoolTestQuestions = Lists.newArrayList();
        //试题组排序
        int seq = 0;
        //题目排序
        int sort = 0;
        for (String groupMapKey : groupMap.keySet()){
            List<EnterSchoolTestQuestion> groupQuestions = groupMap.get(groupMapKey);
            //生成题目组
            EnterSchoolTestQuestionGroup cgroup = saveUserEnterSchoolTestQuestionGroup(cpaper.getId(),groupMapKey,++seq,groupQuestions.size());
            for (EnterSchoolTestQuestion groupQuestion : groupQuestions){
                groupQuestion.setPaperId(cpaper.getId());
                groupQuestion.setGroupId(cgroup.getId());
                groupQuestion.setCategoryId(categoryId);
                groupQuestion.setSort(++sort);
                assembleEnterSchoolTestQuestions.add(groupQuestion);
            }
        }
        //生成题目
        cpaper.setEnterSchoolTestQuestions(assembleEnterSchoolTestQuestions);
        batchSaveQuestion(assembleEnterSchoolTestQuestions);
        List<EnterSchoolTestQuestionKnowledgeGraphRelation> relations = Lists.newArrayList();
        if (questionKnowledgeMap!=null && questionKnowledgeMap.keySet().size()>0) {
            for (Long questionId : questionKnowledgeMap.keySet()){
                List<Long> questionKnowledges = questionKnowledgeMap.get(questionId);
                if (questionKnowledges == null || questionKnowledges.size()<=0){
                    continue;
                }
                for(Long knowledgeGraphId : questionKnowledges){
                    EnterSchoolTestQuestionKnowledgeGraphRelation ralation = new EnterSchoolTestQuestionKnowledgeGraphRelation();
                    ralation.setPaperId(cpaper.getId());
                    ralation.setQuestionId(questionId);
                    ralation.setKnowledgeGraphId(knowledgeGraphId);
                    relations.add(ralation);
                }
            }
        }
        //生成题目和知识点关联
        batchSaveQuestionKnowledgeGraphRelaion(relations);
        return cpaper;
    }

    public void batchSaveQuestionKnowledgeGraphRelaion(List<EnterSchoolTestQuestionKnowledgeGraphRelation> questionKnowledgeGraphRelations) throws DataAccessException {
        if (questionKnowledgeGraphRelations!=null && questionKnowledgeGraphRelations.size()>0){
            enterSchoolTestQuestionKnowledgeGraphRelationService.insertBatch(questionKnowledgeGraphRelations);
        }
    }
    public void batchSaveQuestion(List<EnterSchoolTestQuestion> questions) throws DataAccessException {
        enterSchoolTestQuestionService.insertBatch(questions);
    }

    public EnterSchoolTestPaper saveUserEnterSchoolTestPaper(Long uid, Long categoryId, Long relationPaperId, Integer relationFlag) throws DataAccessException {
        EnterSchoolTestPaper paper = buildUserEnterSchoolTestPaper(uid,categoryId,relationPaperId,relationFlag);
        long id = dao.insert(paper);
        paper.setId(id);
        return paper;
    }

    public EnterSchoolTestQuestionGroup saveRelationUserEnterSchoolTestQuestionGroup(Long paperId, QuestionGroup sourceGroup) throws DataAccessException {
        EnterSchoolTestQuestionGroup group = new EnterSchoolTestQuestionGroup();
        group.setPaperId(paperId);
        group.setGroupName(sourceGroup.getGroupName());
        group.setQuestionTotal(sourceGroup.getQuestionTotal());
        group.setQuestionType(sourceGroup.getGroupType());
        group.setSeq(sourceGroup.getSeq());
        long id = enterSchoolTestQuestionGroupService.insert(group);
        group.setId(id);
        return group;
    }
    public EnterSchoolTestQuestionGroup saveUserEnterSchoolTestQuestionGroup(Long paperId, String groupMapKey, Integer seq, Integer questionTotal) throws DataAccessException {
        EnterSchoolTestQuestionGroup group = new EnterSchoolTestQuestionGroup();
        group.setPaperId(paperId);
        String groupName = "";
        try {
            groupName = Consts.QType.valueOf("QType"+groupMapKey).getDes();
        }catch (Exception e){
            logger.error("获取题组名称异常qtype:"+groupMapKey,e);
            groupName = "入学测评题组";
        }
        group.setGroupName(groupName);
        group.setQuestionTotal(questionTotal);
        group.setQuestionType(Integer.valueOf(groupMapKey));
        group.setSeq(seq);
        long id = enterSchoolTestQuestionGroupService.insert(group);
        group.setId(id);
        return group;
    }


    public EnterSchoolTestPaper buildUserEnterSchoolTestPaper(Long uid, Long categoryId, Long relationPaperId, Integer relationFlag) {
        EnterSchoolTestPaper paper = new EnterSchoolTestPaper();
        paper.setUid(uid);
        paper.setCategoryId(categoryId);
        paper.setRelationPaperId(relationPaperId);
        paper.setRelationFlag(relationFlag);
        Date date = new Date();
        paper.setCreateDate(date);
        paper.setUpdateDate(date);
        return paper;
    }

}
