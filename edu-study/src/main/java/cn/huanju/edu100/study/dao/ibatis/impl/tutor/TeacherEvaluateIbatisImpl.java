package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TeacherEvaluateDao;
import cn.huanju.edu100.study.model.tutor.TeacherEvaluate;
import cn.huanju.edu100.study.util.ValidateUtils;
import com.google.gson.Gson;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.springframework.stereotype.Service;
import java.sql.SQLException;
import java.util.List;


@Service
public class TeacherEvaluateIbatisImpl  extends CrudIbatisImpl2<TeacherEvaluate> implements
        TeacherEvaluateDao {

    public TeacherEvaluateIbatisImpl() {
        super("TeacherEvaluate");
    }

    @Override
    public long insertBatch(List<TeacherEvaluate> teacherEvaluateList) throws DataAccessException {
        if (ValidateUtils.isEmpty(teacherEvaluateList)) {
            logger.error("param error, parameter teacherEvaluateList is empty,studyLogQuestionList:{}", namespace,
                    teacherEvaluateList);
            throw new DataAccessException("param error,teacherEvaluateList is empty");
        }

        try {
            SqlMapClient sqlMap = super.getMaster();
            sqlMap.insert(namespace + ".insertBatch", teacherEvaluateList);
            return 0l;
        } catch (SQLException e) {
            logger.error("insertBatch {} SQLException.content:{}", namespace,
                    (new Gson()).toJson(teacherEvaluateList), e);
            throw new DataAccessException("add " + namespace + " SQLException fail.exception:" + e.getMessage());
        } catch (Exception e) {
            throw new DataAccessException(e);
        }
    }
}
