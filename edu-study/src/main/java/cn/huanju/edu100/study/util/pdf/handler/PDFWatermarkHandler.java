package cn.huanju.edu100.study.util.pdf.handler;

import cn.huanju.edu100.study.util.pdf.model.PdfContant;
import cn.huanju.edu100.study.util.pdf.model.PdfWatermark;
import cn.huanju.edu100.study.util.pdf.util.HQFileUtil;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.AffineTransform;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.extgstate.PdfExtGState;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;

import java.util.Objects;

/**
 * @Author: xiaolong
 * @Date: 2024/7/31 15:04
 * @Version: v1.0.0
 * @Description: 水印
 **/
public class PDFWatermarkHandler implements IEventHandler {
    private PdfWatermark watermark;

    public PDFWatermarkHandler(PdfWatermark watermark) {
        this.watermark = watermark;
    }

    @Override
    public void handleEvent(Event event) {
        try {
            final PdfDocumentEvent docEvent = (PdfDocumentEvent) event;
            final PdfDocument pdfDoc = docEvent.getDocument();
            final Document doc = new Document(pdfDoc);
            final PdfPage page = docEvent.getPage();
            final int pageNumber = pdfDoc.getPageNumber(page);
            final Rectangle pageSize = page.getPageSize();
            final float pdfWidth = pageSize.getWidth();
            final float pdfHeight = pageSize.getHeight();


            //设置水印透明度
            PdfExtGState egState = new PdfExtGState()
                    .setFillOpacity(0.1f)
                    .setStrokeOpacity(0.1f);
            page.setIgnorePageRotationForContent(true);
            PdfCanvas pdfCanvas = new PdfCanvas(page);
            pdfCanvas.saveState();
            pdfCanvas.setExtGState(egState);
            if (Objects.equals(PdfWatermark.TYPE_TEXT, this.watermark.getType())) {
                PdfFont pdfFont = HQFileUtil.getSysFont(PdfContant.Font.NormalFile);
                Integer fontSize = 40;
                DeviceRgb color = new DeviceRgb(200, 200, 200); // 浅灰色
                float centerX = pdfWidth / 2;
                float[] yPositions = new float[]{
                        pdfHeight * 1 / 4 + 50,
                        pdfHeight * 2 / 4 + 50,
                        pdfHeight * 3 / 4 + 50
                };

                // 计算旋转弧度
                double radians = Math.toRadians(45);
                float cos = (float) Math.cos(radians);
                float sin = (float) Math.sin(radians);

                for (float yPosition : yPositions) {
                    pdfCanvas.saveState();
                    pdfCanvas.concatMatrix(cos,
                            sin,
                            -sin,
                            cos,
                            centerX - cos * centerX + sin * yPosition,
                            yPosition - sin * centerX - cos * yPosition);
                    Canvas canvas = new Canvas(pdfCanvas, pdfCanvas.getDocument().getDefaultPageSize());
                    canvas.setFont(pdfFont)
                            .setFontSize(fontSize)
                            .setFontColor(color)
                            .showTextAligned(
                                    new Paragraph(this.watermark.getText()),
                                    0, yPosition,
                                    TextAlignment.LEFT, // 左对齐，确保从x开始
                                    VerticalAlignment.TOP // 顶部对齐，确保y为基线
                            );
                    canvas.close();
                    pdfCanvas.restoreState();
                }
            }
            else if (Objects.equals(PdfWatermark.TYPE_IMAGE, this.watermark.getType())) {
                AffineTransform transform = new AffineTransform();
                transform.scale(0.75, 0.75);//设置缩放比例
                transform.rotate(Math.toRadians(45));//设置旋转角度
                pdfCanvas.concatMatrix(transform);
                ImageData img = this.watermark.getImageData();
                pdfCanvas.addImage(img, (pdfWidth)/2+120, (pdfHeight)/2-380, pdfWidth, true);
                pdfCanvas.restoreState();
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
