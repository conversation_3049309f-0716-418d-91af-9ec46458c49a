package cn.huanju.edu100.study.mapper.homework.trainbrush;

import cn.huanju.edu100.study.model.homework.comment.UserHomeworkComment;
import cn.huanju.edu100.study.model.homework.trainbrush.UserTrainBrushQuestionResult;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * Created by z<PERSON><PERSON> on 2023/2/2.
 */
@Repository
@Mapper
@DS("al-default-ds")
public interface UserTrainBrushQuestionResultMapper extends BaseMapper<UserTrainBrushQuestionResult> {
}
