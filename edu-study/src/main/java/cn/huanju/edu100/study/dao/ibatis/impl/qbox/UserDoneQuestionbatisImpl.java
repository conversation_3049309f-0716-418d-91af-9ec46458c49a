/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.qbox;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.qbox.UserDoneQuestionDao;
import cn.huanju.edu100.study.model.questionBox.UserDoneQuestion;
import cn.huanju.edu100.util.GsonUtil;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网校公告DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
public class UserDoneQuestionbatisImpl extends CrudIbatisImpl2<UserDoneQuestion> implements
		UserDoneQuestionDao {
	public UserDoneQuestionbatisImpl() {
		super("UserDoneQuestion");
	}

	@Override
	public UserDoneQuestion getFromMasterDbByUidQboxIdAndKey(Long uid, Long questionBoxId, String key) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("getFromMasterDbByUidQboxIdAndKey error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("getFromMasterDbByUidQboxIdAndKey error,parameter uid is empty or less than 0");
		}
		if (questionBoxId == null || questionBoxId <= 0 ) {
			logger.error("getFromMasterDbByUidQboxIdAndKey error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("getFromMasterDbByUidQboxIdAndKey error,parameter questionBoxId is empty or less than 0");
		}
		if (StringUtils.isBlank(key)) {
			logger.error("getFromMasterDbByUidQboxIdAndKey error, parameter key is empty,key:{}", key);
			throw new DataAccessException("getFromMasterDbByUidQboxIdAndKey error,parameter uid is empty ");
		}
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("uid", uid);
		param.put("questionBoxId", questionBoxId);
		param.put("key", key);
		return (UserDoneQuestion) queryForObjectFromMaster(namespace.concat(".getByUidQboxIdAndKey"), param);
	}

	@Override
	public UserDoneQuestion getByUidQboxIdAndKey(Long uid, Long questionBoxId, String key) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("getByUidQboxIdAndKey  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("getByUidQboxIdAndKey error,parameter uid is empty or less than 0");
		}
		if (questionBoxId == null || questionBoxId <= 0 ) {
			logger.error("getByUidQboxIdAndKey  error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("getByUidQboxIdAndKey error,parameter questionBoxId is empty or less than 0");
		}
		if (StringUtils.isBlank(key)) {
			logger.error("getByUidQboxIdAndKey  error, parameter key is empty,key:{}", key);
			throw new DataAccessException("getByUidQboxIdAndKey error,parameter uid is empty ");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("questionBoxId", questionBoxId);
			param.put("key", key);
//			logger.info("sqlmapclient is {}",sqlMap);
			return (UserDoneQuestion) sqlMap.queryForObject(namespace.concat(".getByUidQboxIdAndKey"), param);
		} catch (SQLException e) {
			logger.error("getByUidQboxIdAndKey SQLException.uid:{},questionBoxId:{}", uid, questionBoxId, e);
			throw new DataAccessException("getByUidQboxIdAndKey SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<UserDoneQuestion> getByUidQboxId(Long uid, Long questionBoxId) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("getByUidQboxId  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("getByUidQboxId error,parameter uid is empty or less than 0");
		}
		if (questionBoxId == null || questionBoxId <= 0 ) {
			logger.error("getByUidQboxId  error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("getByUidQboxId error,parameter questionBoxId is empty or less than 0");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("questionBoxId", questionBoxId);
//			logger.info("sqlmapclient is {}",sqlMap);
			return sqlMap.queryForList(namespace.concat(".getByUidQboxId"), param);
		} catch (SQLException e) {
			logger.error("getByUidQboxId SQLException.uid:{},questionBoxId:{}", uid, questionBoxId, e);
			throw new DataAccessException("getByUidQboxId SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<UserDoneQuestion> getByUidAndQboxId(Long uid, Long questionBoxId) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("getByUidAndQboxId  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("getByUidQboxIdAndKey error,parameter uid is empty or less than 0");
		}
		if (questionBoxId == null || questionBoxId <= 0 ) {
			logger.error("getByUidAndQboxId  error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("getByUidQboxIdAndKey error,parameter questionBoxId is empty or less than 0");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("questionBoxId", questionBoxId);
			return (List<UserDoneQuestion>) sqlMap.queryForList(namespace.concat(".getByUidAndQboxId"), param);
		} catch (SQLException e) {
			logger.error("getByUidAndQboxId SQLException.uid:{},questionBoxId:{}", uid, questionBoxId, e);
			throw new DataAccessException("getByUidAndQboxId SQLException error" + e.getMessage());
		}
	}

    @Override
    public List<UserDoneQuestion> getByUidAndBoxIds(Long uid, List<Long> boxIds) throws DataAccessException {
        if (uid == null || uid <= 0 ) {
            logger.error("getByUidAndBoxIds  error, parameter uid is empty or less than 0,uid:{}", uid);
            throw new DataAccessException("getByUidAndBoxIds error,parameter uid is empty or less than 0");
        }
        if (CollectionUtils.isEmpty(boxIds)) {
            logger.error("getByUidAndBoxIds  error, parameter boxIds is empty ,boxIds:{}", GsonUtil.toJson(boxIds));
            throw new DataAccessException("getByUidAndBoxIds error,parameter boxIds is empty ");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", uid);
            param.put("boxIds", boxIds);
            return (List<UserDoneQuestion>) sqlMap.queryForList(namespace.concat(".getByUidAndBoxIds"), param);
        } catch (SQLException e) {
            logger.error("getByUidAndBoxIds SQLException.uid:{},", uid, e);
            throw new DataAccessException("getByUidAndBoxIds SQLException error" + e.getMessage());
        }
    }

	@Override
	public List<UserDoneQuestion> getByUidQboxIdAndKeyLike(Long uid, Long questionBoxId, String key) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("getByUidQboxIdAndKeyLike  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("getByUidQboxIdAndKeyLike error,parameter uid is empty or less than 0");
		}
		if (questionBoxId == null || questionBoxId <= 0 ) {
			logger.error("getByUidQboxIdAndKeyLike  error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("getByUidQboxIdAndKeyLike error,parameter questionBoxId is empty or less than 0");
		}
		if (StringUtils.isBlank(key)) {
			logger.error("getByUidQboxIdAndKeyLike  error, parameter key is empty,key:{}", key);
			throw new DataAccessException("getByUidQboxIdAndKeyLike error,parameter uid is empty ");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("questionBoxId", questionBoxId);
			param.put("key", key);
			return (List<UserDoneQuestion>) sqlMap.queryForList(namespace.concat(".getByUidQboxIdAndKeyLike"), param);
		} catch (SQLException e) {
			logger.error("getByUidQboxIdAndKeyLike SQLException.uid:{},questionBoxId:{}", uid, questionBoxId, e);
			throw new DataAccessException("getByUidQboxIdAndKeyLike SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<UserDoneQuestion> getByUidQboxIdListAndKeyLike(Long uid, List<Long> boxIdList, String key) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("getByUidQboxIdListAndKeyLike  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("getByUidQboxIdAndKeyLike error,parameter uid is empty or less than 0");
		}
		if (boxIdList == null || boxIdList.size() <= 0 ) {
			logger.error("getByUidQboxIdListAndKeyLike  error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", boxIdList);
			throw new DataAccessException("getByUidQboxIdAndKeyLike error,parameter questionBoxId is empty or less than 0");
		}
		if (StringUtils.isBlank(key)) {
			logger.error("getByUidQboxIdListAndKeyLike  error, parameter key is empty,key:{}", key);
			throw new DataAccessException("getByUidQboxIdListAndKeyLike error,parameter uid is empty ");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("boxIdList", boxIdList);
			param.put("key", key);
			return (List<UserDoneQuestion>) sqlMap.queryForList(namespace.concat(".getByUidQboxIdListAndKeyLike"), param);
		} catch (SQLException e) {
			logger.error("getByUidQboxIdListAndKeyLike SQLException.uid:{},questionBoxId:{}", uid, boxIdList, e);
			throw new DataAccessException("getByUidQboxIdListAndKeyLike SQLException error" + e.getMessage());
		}
	}

	@Override
	public List<String> getKeysByUidAndQboxId(Long uid, Long questionBoxId) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("getKeysByUidAndQboxId  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("getKeysByUidAndQboxId error,parameter uid is empty or less than 0");
		}
		if (questionBoxId == null || questionBoxId <= 0 ) {
			logger.error("getKeysByUidAndQboxId  error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("getKeysByUidAndQboxId error,parameter questionBoxId is empty or less than 0");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("questionBoxId", questionBoxId);
			return (List<String>) sqlMap.queryForList(namespace.concat(".getKeysByUidAndQboxId"), param);
		} catch (SQLException e) {
			logger.error("getKeysByUidAndQboxId SQLException.uid:{},questionBoxId:{}", uid, questionBoxId, e);
			throw new DataAccessException("getKeysByUidAndQboxId SQLException error" + e.getMessage());
		}
	}

	@Override
	public void deleteByUidAndQboxId(Long uid, Long questionBoxId) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("deleteByUidAndQboxId  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("deleteByUidAndQboxId error,parameter uid is empty or less than 0");
		}
		if (questionBoxId == null || questionBoxId <= 0 ) {
			logger.error("deleteByUidAndQboxId  error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("deleteByUidAndQboxId error,parameter questionBoxId is empty or less than 0");
		}
		try {
			SqlMapClient sqlMap = super.getMaster();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("questionBoxId", questionBoxId);
			sqlMap.delete(namespace.concat(".deleteByUidAndQboxId"), param);
		} catch (SQLException e) {
			logger.error("deleteByUidAndQboxId SQLException.uid:{},questionBoxId:{}", uid, questionBoxId, e);
			throw new DataAccessException("deleteByUidAndQboxId SQLException error" + e.getMessage());
		}
	}

	@Override
	public void deleteByUidAndQboxIdAndIdList(Long uid, Long questionBoxId, List<Long> idList) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("deleteByUidAndQboxIdAndIdList  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("deleteByUidAndQboxIdAndIdList error,parameter uid is empty or less than 0");
		}
		if (questionBoxId == null || questionBoxId <= 0 ) {
			logger.error("deleteByUidAndQboxIdAndIdList  error, parameter questionBoxId is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("deleteByUidAndQboxIdAndIdList error,parameter questionBoxId is empty or less than 0");
		}
		if (CollectionUtils.isEmpty(idList) ) {
			logger.error("deleteByUidAndQboxIdAndIdList  error, parameter idList is empty or less than 0,questionBoxId:{}", questionBoxId);
			throw new DataAccessException("deleteByUidAndQboxIdAndIdList error,parameter idList is empty or less than 0");
		}
		try {
			SqlMapClient sqlMap = super.getMaster();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("questionBoxId", questionBoxId);
			param.put("idList", idList);
			sqlMap.delete(namespace.concat(".deleteByUidAndQboxIdAndIdList"), param);
		} catch (SQLException e) {
			logger.error("deleteByUidAndQboxIdAndIdList SQLException.uid:{},questionBoxId:{}", uid, questionBoxId, e);
			throw new DataAccessException("deleteByUidAndQboxIdAndIdList SQLException error" + e.getMessage());
		}
	}

	@Override
	public void insertBatch(List<UserDoneQuestion> userDoneQuestionList) throws DataAccessException {
		if (CollectionUtils.isEmpty(userDoneQuestionList)) {
			logger.error("batchInsert get error, parameter is empty");
			throw new DataAccessException("insertBatch get error, parameter is empty");
		}
		try {
			SqlMapClient sqlMap = super.getMaster();
			sqlMap.insert(namespace.concat(".insertBatch"), userDoneQuestionList);
		} catch (SQLException e) {
			logger.error("batchInsert SQLException.", e);
			throw new DataAccessException("insertBatch get SQLException error" + e.getMessage());
		} catch (Exception e) {
			logger.error("get SException.", e);
			throw new DataAccessException("insertBatch get Exception error" + e.getMessage());
		}
	}

	@Override
	public void insertBatchNew(List<UserDoneQuestion> userDoneQuestionList) throws DataAccessException {
		if (CollectionUtils.isEmpty(userDoneQuestionList)) {
			logger.error("insertBatchNew get error, parameter is empty");
			throw new DataAccessException("insertBatchNew get error, parameter is empty");
		}
		try {
			SqlMapClient sqlMap = super.getMaster();
			sqlMap.insert(namespace.concat(".insertBatchNew"), userDoneQuestionList);
		} catch (SQLException e) {
			logger.error("insertBatchNew SQLException.", e);
			throw new DataAccessException("insertBatchNew get SQLException error" + e.getMessage());
		} catch (Exception e) {
			logger.error("get SException.", e);
			throw new DataAccessException("insertBatchNew get Exception error" + e.getMessage());
		}
	}

	@Override
	public List<UserDoneQuestion> getAllBoxIdByUid(Long uid) throws DataAccessException {
		if (uid == null || uid <= 0 ) {
			logger.error("getAllBoxIdByUid  error, parameter uid is empty or less than 0,uid:{}", uid);
			throw new DataAccessException("getAllBoxIdByUid error,parameter uid is empty or less than 0");
		}
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			return (List<UserDoneQuestion>) sqlMap.queryForList(namespace.concat(".getAllBoxIdByUid"), param);
		} catch (SQLException e) {
			logger.error("getAllBoxIdByUid SQLException.uid:{}", uid, e);
			throw new DataAccessException("getAllBoxIdByUid SQLException error" + e.getMessage());
		}
	}
}
