package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;


public class Question extends DataEntity<Question> {

    private static final long serialVersionUID = 1L;
    //	private Long id;
    private String title;
    private Integer qtype;
    private Long firstCategory;
    private Long secondCategory;
    private Long categoryId;
    private Integer isMulti;
    private Integer qlevel;
    private Double score;
    private Integer limitTime;
    private String content;
    private Integer state;
//	private Long createBy;
//	private Long updateBy;
//	private Date createDate;
//	private Date updateDate;

    /*非数据库对应属性*/
    private List<QuestionTopic> topicList;

    private String proTagRelation;


    private String aiAssistantAppid;
//	public Long getId() {
//		return id;
//	}
//
//	public void setId(Long id) {
//		this.id = id;
//	}

    public String getTitle() {
        return title;
    }

    public Integer getIsMulti() {
		return isMulti;
	}

	public void setIsMulti(Integer isMulti) {
		this.isMulti = isMulti;
	}

	public void setTitle(String title) {
        this.title = title;
    }

    public Integer getQtype() {
        return qtype;
    }

    public void setQtype(Integer qtype) {
        this.qtype = qtype;
    }

    public Long getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(Long firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getSecondCategory() {
        return secondCategory;
    }

    public void setSecondCategory(Long secondCategory) {
        this.secondCategory = secondCategory;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getQlevel() {
        return qlevel;
    }

    public void setQlevel(Integer qlevel) {
        this.qlevel = qlevel;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Integer getLimitTime() {
        return limitTime;
    }

    public void setLimitTime(Integer limitTime) {
        this.limitTime = limitTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getProTagRelation() {
        return proTagRelation;
    }

    public void setProTagRelation(String proTagRelation) {
        this.proTagRelation = proTagRelation;
    }

    //	public Long getCreateBy() {
//		return createBy;
//	}
//
//	public void setCreateBy(Long createBy) {
//		this.createBy = createBy;
//	}
//
//	public Long getUpdateBy() {
//		return updateBy;
//	}
//
//	public void setUpdateBy(Long updateBy) {
//		this.updateBy = updateBy;
//	}
//
//	public Date getCreateDate() {
//		return createDate;
//	}
//
//	public void setCreateDate(Date createDate) {
//		this.createDate = createDate;
//	}
//
//	public Date getUpdateDate() {
//		return updateDate;
//	}
//
//	public void setUpdateDate(Date updateDate) {
//		this.updateDate = updateDate;
//	}

    public List<QuestionTopic> getTopicList() {
        return topicList;
    }

    public void setTopicList(List<QuestionTopic> topicList) {
        this.topicList = topicList;
    }


    public String getAiAssistantAppid() {
        return aiAssistantAppid;
    }

    public void setAiAssistantAppid(String aiAssistantAppid) {
        this.aiAssistantAppid = aiAssistantAppid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Question)) return false;
        if (!super.equals(o)) return false;

        Question question = (Question) o;

        return id.equals(question.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
