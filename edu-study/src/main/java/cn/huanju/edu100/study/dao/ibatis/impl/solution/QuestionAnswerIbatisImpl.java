package cn.huanju.edu100.study.dao.ibatis.impl.solution;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.solution.QuestionAnswerDao;
import cn.huanju.edu100.study.model.solution.QuestionAnswer;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.util.GsonUtils;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;


public class QuestionAnswerIbatisImpl extends CrudIbatisImpl2<QuestionAnswer> implements QuestionAnswerDao {
    public QuestionAnswerIbatisImpl() {
        super("QuestionAnswer");
    }

    @Override
    public List<QuestionAnswer> findUserAnswerListByQids(String qids) throws DataAccessException {
        if (qids == null) {
            logger.error("findUserAnswerListByQids {} error, parameter qids is null,qids:{}", namespace, qids);
            throw new DataAccessException("findUserAnswerListByQids error,qids is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();

            return (List<QuestionAnswer>) sqlMap.queryForList(namespace+".findUserAnswerListByQids", qids);
        } catch (SQLException e) {
            logger.error("findUserAnswerListByQids {} SQLException.qids:{}", namespace, qids, e);
            throw new DataAccessException("findUserAnswerListByQids SQLException error" + e.getMessage());
        }
    }

    @Override
    public List<QuestionAnswer> getQuestionAnswerByQidWriteDb(Long qid) throws DataAccessException {
        if (qid == null) {
            logger.error("getQuestionAnswerByQidWriteDb {} error, parameter qid is null,qid:{}", namespace, qid);
            throw new DataAccessException("getQuestionAnswerByQidWriteDb error,qid is null");
        }
        try {
            SqlMapClient sqlMap = super.getMaster();

            HashMap<String, Object> paramsAnswerMap = new HashMap<String, Object>();
            paramsAnswerMap.put("questionId",qid);

            return (List<QuestionAnswer>) sqlMap.queryForList(namespace+".findList", paramsAnswerMap);
        } catch (SQLException e) {
            logger.error("getQuestionAnswerByQidWriteDb {} SQLException.qid:{}", namespace, qid, e);
            throw new DataAccessException("getQuestionAnswerByQidWriteDb SQLException error" + e.getMessage());
        }
    }

    @Override
    public List<QuestionAnswer> findUserAnswerListByQidListAndStartTime(List<Long> qidList, Date startTime) throws DataAccessException {
        HashMap<String, Object> paramsAnswerMap = new HashMap<String, Object>();
        paramsAnswerMap.put("qidList",qidList);
        if(String.valueOf(startTime.getTime()).length()==10){
            paramsAnswerMap.put("startTime",startTime.getTime());
        }else{
            paramsAnswerMap.put("startTime",startTime.getTime()/1000);
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            return (List<QuestionAnswer>) sqlMap.queryForList(namespace+".findUserAnswerListByQidListAndStartTime", paramsAnswerMap);
        } catch (SQLException e) {
            logger.error("findUserAnswerListByQidListAndStartTime {} SQLException.paramsAnswerMap:{}", namespace, GsonUtils.toJson(paramsAnswerMap), e);
            throw new DataAccessException("findUserAnswerListByQidListAndStartTime SQLException error" + e.getMessage());
        }
    }

    @Override
    public boolean updateLikeNum(Long answerId, String likeNumStr) throws DataAccessException {
        if (ValidateUtils.isEmpty(answerId)) {
            logger.error("updateLikeNum {} error, answerId  is null:{}", namespace, answerId);
            throw new DataAccessException("updateLikeNum error,answerId is null");
        }
        if (ValidateUtils.isEmpty(likeNumStr)) {
            logger.error("updateLikeNum {} error, likeNumStr is null:{}", namespace, likeNumStr);
            throw new DataAccessException("updateLikeNum error,likeNumStr is null");
        }
        HashMap<String, Object> paramsAnswerMap = new HashMap<String, Object>();
        SqlMapClient sqlMap = super.getMaster();
        try {
            Long updatedTime = Calendar.getInstance().getTimeInMillis();
            paramsAnswerMap.put("updatedTime", updatedTime/1000);
            paramsAnswerMap.put("id", answerId);
            paramsAnswerMap.put("likeNum", likeNumStr);
            int row = sqlMap.update(namespace+".updateLikeNum", paramsAnswerMap);
            return row >= 1;
        } catch (SQLException e) {
            logger.error("updateLikeNum {} SQLException.paramsAnswerMap:{}", namespace, GsonUtils.toJson(paramsAnswerMap), e);
            throw new DataAccessException("add " + namespace + " SQLException fail.");
        }catch (Exception e) {
            logger.error("updateLikeNum {} SQLException.paramsAnswerMap:{}", namespace, GsonUtils.toJson(paramsAnswerMap), e);
            throw new DataAccessException("updateLikeNum Exception error:" + e.getMessage());
        }
    }

    @Override
    public Long countUnReadAnswer(HashMap<String, Long> paramMap) throws DataAccessException {
        if (paramMap == null) {
            logger.error("countUnReadAnswer error, parameter error,paperId:{}", GsonUtils.toJson(paramMap));
            throw new DataAccessException("countUnReadAnswer error,parameter error");
        }
        SqlMapClient sqlMapClient = super.getSlave();
        try {
            return (Long)sqlMapClient.queryForObject(namespace + ".countUnReadAnswer", paramMap);
        } catch (SQLException e) {
            logger.error("countUnReadAnswer SQLException paperId:{} ", GsonUtils.toJson(paramMap), e);
            throw new DataAccessException("countUnReadAnswer SQLException error" + e.getMessage());
        }
    }
}
