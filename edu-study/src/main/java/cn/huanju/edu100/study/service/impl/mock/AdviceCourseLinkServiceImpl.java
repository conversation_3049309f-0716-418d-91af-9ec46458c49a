package cn.huanju.edu100.study.service.impl.mock;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.mock.AdviceCourseLinkDao;
import cn.huanju.edu100.study.model.mock.AdviceCourseLink;
import cn.huanju.edu100.study.service.mock.AdviceCourseLinkService;
import cn.huanju.edu100.exception.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class AdviceCourseLinkServiceImpl extends BaseServiceImpl<AdviceCourseLinkDao, AdviceCourseLink> implements AdviceCourseLinkService {



    @Override
    public List<AdviceCourseLink> getAdviceCourseLinkList(Long adviceId)throws DataAccessException {
        return dao.getAdviceCourseLinkList(adviceId);
    }
}
