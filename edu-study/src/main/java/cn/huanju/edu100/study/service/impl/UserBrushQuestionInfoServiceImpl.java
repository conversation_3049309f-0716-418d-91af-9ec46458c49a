package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.UserAnswerDetailDao;
import cn.huanju.edu100.study.dao.UserAnswerSumDao;
import cn.huanju.edu100.study.dao.UserBrushQuestionInfoDao;
import cn.huanju.edu100.study.dao.UserHomeWorkAnswerDao;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.questionBox.UserBrushQuestionInfo;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserBrushQuestionInfoService;
import cn.huanju.edu100.study.service.impl.answer.*;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.study.util.ThreadPoolFactoryUtil;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.DateUtil;
import cn.huanju.edu100.util.DateUtils;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;

/**
 * 用户刷题挑战记录Service
 *
 * <AUTHOR>
 * @version 2017-09-11
 */
@Service
public class UserBrushQuestionInfoServiceImpl extends BaseServiceImpl<UserBrushQuestionInfoDao, UserBrushQuestionInfo> implements UserBrushQuestionInfoService {

    private static Logger logger = LoggerFactory.getLogger(UserBrushQuestionInfoServiceImpl.class);

    @Autowired
    private UserHomeWorkAnswerDao userHomeWorkAnswerDao;

    @Autowired
    private UserAnswerSumDao userAnswerSumDao;
    @Autowired
    private UserAnswerDetailDao userAnswerDetailDao;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private CompatableRedisClusterClient answerCompatableRedisClusterClient;

    @Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

    private static ExecutorService qBoxBrushSubmitEs = ThreadPoolFactoryUtil.createDefaultPool("userQBoxBrushThreadPool");

    @Override
    public UserBrushQuestionInfo getBrushInfoByUid(long uid, Long boxId) throws DataAccessException {
        return dao.getBrushInfoByUid(uid, boxId);
    }

    private static AbstractAnswerJudger[] judgers = new AbstractAnswerJudger[] {
            new SingleChoiceAnswerJudger(),//0单选
            new MultiChoiceAnswerJudger(),//1多选
            new UncertainChoiceAnswerJudger(),//2不定项选择
            new DetermineAnswerJudger(),//3判断题
            new FillAnswerJudger(),//4填空题
            new SubjectiveAnswerJudger(),//5简答题
    };
    private AbstractAnswerJudger getAnswerJudger(Integer questionType) {
        if (questionType == null || questionType >= 5) {
            questionType = 5;
        }
        return judgers[questionType];
    }

    private void calUserExerciseAnswer(UserExerciseAnswer userExerciseAnswer) throws DataAccessException {
        List<UserAnswerDetail> answerDetails = userExerciseAnswer.getAnswerDetail();
        List<Long> idList = new ArrayList<Long>();
        for (UserAnswerDetail answerDetail : answerDetails) {
            answerDetail.setUserHomeworkId(userExerciseAnswer.getId());
            if (!idList.contains(answerDetail.getQuestionId())) {
                idList.add(answerDetail.getQuestionId());
            }
        }
        List<Question> questionList = knowledgeResource.getQuestionByIds(idList);

        if (questionList.size()<=0) {
            throw new DataAccessException(""+ Constants.SYS_ERROR,"getQuestionByIds return null ,param:"+GsonUtil.toJson(idList));
        }

        //这里先循环判断出正确与否，然后返回。
        for (Question question : questionList) {
            Collection<QuestionTopic> topics = question.getTopicList();
            if (CollectionUtils.isNotEmpty(topics)) {
                for (QuestionTopic topic : topics) {
                    for (UserAnswerDetail answerDetailItem : answerDetails) {
                        if (topic.getId().equals(answerDetailItem.getTopicId())) {
                            AbstractAnswerJudger answerJudger = getAnswerJudger(topic.getQtype());
                            int isRight = answerJudger.judge(topic, answerDetailItem.getAnswer());
                            Double score = answerJudger.calculateScore(topic, answerDetailItem.getAnswer(),null);
                            answerDetailItem.setIsRight(isRight);
                            answerDetailItem.setScore(score);
                            break;
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<UserAnswerDetail> submitBrushExercise(UserExerciseAnswer userExerciseAnswer) throws DataAccessException {

        userExerciseAnswer.setScore(0d);
        long useTime = 0;
        if (userExerciseAnswer.getUsetime() != null) {
            useTime = userExerciseAnswer.getUsetime();
        } else if (userExerciseAnswer.getStartTime() != null && userExerciseAnswer.getEndTime() != null) {
            useTime = DateUtil.timeDiffSecond(userExerciseAnswer.getStartTime(), userExerciseAnswer.getEndTime());
        }

        userExerciseAnswer.setUsetime(useTime);
        userExerciseAnswer.setAnswerNum(((long) userExerciseAnswer.getAnswerDetail().size()));
        userExerciseAnswer.setState(userExerciseAnswer.getIsSubmit() == 0 ? UserAnswer.State.DOING
                : UserAnswer.State.SUBMITTED);

        Set<Long> isDoList = new HashSet<Long>();
        for (UserAnswerDetail userAnswerDetail : userExerciseAnswer.getAnswerDetail()) {
            isDoList.add(userAnswerDetail.getQuestionId());
        }
        userExerciseAnswer.setAnswerNum(Long.valueOf("" + isDoList.size()));

        // 刷题作答只有新增，没有更新的场景。
        Long userHomeWorkAnswerId = userHomeWorkAnswerDao.insertSharding(userExerciseAnswer);
        userExerciseAnswer.setId(userHomeWorkAnswerId);

        List<UserAnswerDetail> details = submitBoxExercise(userExerciseAnswer);
        // 处理排行榜逻辑
        // 获取当天的排行榜key
//        String toDay = DateUtils.getYearToDayUnLine(new Date());
//        String rankingKey = "qbRank_"+userExerciseAnswer.getBoxId().longValue()+"_"+toDay;

        // 获取当前所属周的排行榜的key
//        org.joda.time.DateTime dateTime = new org.joda.time.DateTime();
//        int week = dateTime.weekOfWeekyear().get();
//        int weekYear = dateTime.getWeekyear();
//
//        String rankingKey = "qbRank_"+userExerciseAnswer.getBoxId().longValue()+"_"+ weekYear+"_"+week;
//        logger.info("rankingKey:{},uid:{}",rankingKey, userExerciseAnswer.getUid());
//
//        Double score = compatableRedisClusterClient.zscore(rankingKey, userExerciseAnswer.getUid().toString());
//        当日首次刷题作答或者最新作答数大于当前榜单作答数，则直接入榜
//        if (score == null || (score != null && score.longValue() < userExerciseAnswer.getAnswerNum())){
//            compatableRedisClusterClient.zadd(rankingKey, (double) userExerciseAnswer.getAnswerNum(),userExerciseAnswer.getUid().toString());
//        }

        // 处理历史连续挑战时间和历史最佳挑战数的逻辑
        UserBrushQuestionInfo userBrushQuestionInfo = dao.getBrushInfoByUid(userExerciseAnswer.getUid(), userExerciseAnswer.getBoxId());
        if (userBrushQuestionInfo == null){
            userBrushQuestionInfo = new UserBrushQuestionInfo();
            userBrushQuestionInfo.setUid(userExerciseAnswer.getUid());
            userBrushQuestionInfo.setBoxId(userExerciseAnswer.getBoxId());
            userBrushQuestionInfo.setBestAnswerId(userHomeWorkAnswerId);
            userBrushQuestionInfo.setBestHomeworkId(userExerciseAnswer.getObjId());
            userBrushQuestionInfo.setBestCnt(isDoList.size());
            userBrushQuestionInfo.setLastSerialDay(new Date());
            userBrushQuestionInfo.setSerialCnt(1);
            dao.insertSharding(userBrushQuestionInfo);
        }else{
            if (userBrushQuestionInfo.getBestCnt() <= isDoList.size()){
                userBrushQuestionInfo.setBestAnswerId(userHomeWorkAnswerId);
                userBrushQuestionInfo.setBestHomeworkId(userExerciseAnswer.getObjId());
                userBrushQuestionInfo.setBestCnt(isDoList.size());
            }
            int difDate = DateUtils.diffDate(new Date(), userBrushQuestionInfo.getLastSerialDay());
            if(difDate == 1){
                userBrushQuestionInfo.setLastSerialDay(new Date());
                userBrushQuestionInfo.setSerialCnt(userBrushQuestionInfo.getSerialCnt() + 1);
            } if(difDate > 1) {// 表示本次作答和上次作答天数大于1天，则要重新计算连续
                userBrushQuestionInfo.setLastSerialDay(new Date());
                userBrushQuestionInfo.setSerialCnt(1);
            }else {// 小于1的话，则表示这是同一天的作答，则只需要更新上次作答的时间即可
                userBrushQuestionInfo.setLastSerialDay(new Date());
            }

            dao.updateSharding(userBrushQuestionInfo);
        }
        return details;
    }

    private List<UserAnswerDetail> submitBoxExercise(UserExerciseAnswer userExerciseAnswer) throws DataAccessException {
        logger.info("sty_userBrushQuestionBoxExercise start submitBoxExercise.compatableRedisClusterClient.");
        List<UserAnswerDetail> answerDetails = userExerciseAnswer.getAnswerDetail();

        List<Long> idList = new ArrayList<Long>();
        for (UserAnswerDetail answerDetail : answerDetails) {
            answerDetail.setUserHomeworkId(userExerciseAnswer.getId());
            if (!idList.contains(answerDetail.getQuestionId())) {
                idList.add(answerDetail.getQuestionId());
            }
        }

        /*        for (UserAnswerDetail answerDetailItem : answerDetails) {
            answerDetailItem.setIsRight(UserAnswerDetail.IsRight.RIGHT);
            answerDetailItem.setScore(0D);
        }*/
        calUserExerciseAnswer(userExerciseAnswer);

        //先把结果同步写redis,设置缓存有效期为2分钟
        answerCompatableRedisClusterClient.setex(RedisConsts.getUserHomeWorkAnswerKey(userExerciseAnswer.getUid(), userExerciseAnswer.getId()), 120, GsonUtil.getGenericGson().toJson(userExerciseAnswer));
        logger.info("sty_userBrushQuestionBoxExercise end set to redis");
        qBoxBrushSubmitEs.submit(new UserBoxBrushSubmitCachedBean(userExerciseAnswer));
        logger.info("sty_userBrushQuestionBoxExercise end submit");
        return answerDetails;

    }

    class UserBoxBrushSubmitCachedBean implements Callable<Void> {

        private UserExerciseAnswer userExerciseAnswer;

        public UserBoxBrushSubmitCachedBean(UserExerciseAnswer userExerciseAnswer) {
            this.userExerciseAnswer = userExerciseAnswer;
        }

        @Override
        public Void call() throws Exception {
            Map<Long, Long> qIdFlagMap = new HashMap<Long, Long>();// questionId-->sumId

            Collection<UserAnswerDetail> answerDetails = userExerciseAnswer.getAnswerDetail();
            for(UserAnswerDetail userAnswerDetail : answerDetails){
                Long sumId = null;
                if (qIdFlagMap.get(userAnswerDetail.getQuestionId()) == null){
                    UserAnswerSum answerSum = new UserAnswerSum();
                    answerSum.setUid(userExerciseAnswer.getUid());
                    answerSum.setUserHomeworkId(userExerciseAnswer.getId());
                    answerSum.setQuestionId(userAnswerDetail.getQuestionId());
                    answerSum.setScore(0d);
                    answerSum.setState(UserAnswerSum.State.DONE);

                    sumId = userAnswerSumDao.insertSharding(answerSum, answerSum.getClass());
                    qIdFlagMap.put(userAnswerDetail.getQuestionId(), sumId);
                }else{
                    sumId = qIdFlagMap.get(userAnswerDetail.getQuestionId());
                }
                userAnswerDetail.setUid(userExerciseAnswer.getUid());
                userAnswerDetail.setSumId(sumId);
                userAnswerDetail.setUserHomeworkId(userExerciseAnswer.getId());
                userAnswerDetail.setIsRight(userAnswerDetail.getIsRight());
                userAnswerDetail.setAnswerStr(GsonUtil.toJson(userAnswerDetail.getAnswer())); //存答案到DB

                userAnswerDetailDao.insertSharding(userAnswerDetail, userAnswerDetail.getClass());
            }
            return null;
        }
    }

}
