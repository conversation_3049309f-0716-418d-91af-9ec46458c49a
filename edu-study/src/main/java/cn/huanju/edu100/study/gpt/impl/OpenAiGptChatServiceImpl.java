package cn.huanju.edu100.study.gpt.impl;

import cn.huanju.edu100.study.gpt.GptChatService;
import cn.huanju.edu100.study.gpt.param.LlmAiRequestV2;
import cn.huanju.edu100.util.JSONUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/7 18:24
 * @description
 */
@Service
public class OpenAiGptChatServiceImpl implements GptChatService {

    @Data
    static class StreamMessage {

        private String role;

        private String content;
        public StreamMessage() {
        }

        public StreamMessage(String role, String content) {
            this.role = role;
            this.content = content;
        }
    }

    @Data
    static class AiParams {
        private String modelName;
        private Double temperature;
        private Boolean enableAsks=Boolean.TRUE;
        private Long maxToken;
    }

    @Data
    @Accessors(chain = true)
    static class ChatGptStreamQuery {

        @ApiModelProperty("是否开启追问功能 默认开启追问功能")
        private boolean enableAsks;

        @ApiModelProperty("用户唯一标识")
        private String user;

        private List<StreamMessage> message;

        @ApiModelProperty("是否使用微软openAi 默认不开启false")
        private Boolean  useAuzreOpenAi=Boolean.FALSE;

        @ApiModelProperty("问题回答严谨度0-2的小数越小越严谨")
        private Double temperature;

        @ApiModelProperty("application 项目名称")
        private String application;

        @ApiModelProperty("modelName 模型名称 gpt-3.5-turbo gpt-4 ")
        private String  modelName="gpt-3.5-turbo";

        @ApiModelProperty("maxToken 请求生成文本最大长度，不设置走默认")
        private Long maxToken;

        @ApiModelProperty("会话id")
        private String conversationId;
    }

    @Resource
    private WebClient.Builder webClientBuilder;

    @Value("${gpt.chatUrl.openai}")
    private String chatGptStreamUrl;

    private static final String DONE = "[DONE]";

    @Override
    public void streamChat(ChatParam param, StreamObserver streamObserver) {
        LlmAiRequestV2 request = new LlmAiRequestV2();

        request.setApplication("inner-subjective_question_ai");
        request.setUser(param.getUserKey());
        request.setConversationId(param.getUserKey());
        if(StringUtils.isNotBlank(param.getGptConfigJson())){
            AiParams aiParams = JSONUtils.parseObject(param.getGptConfigJson(), AiParams.class);
            request.setTemperature(aiParams.getTemperature());
            request.setModelName(aiParams.getModelName());
            if (Objects.nonNull(aiParams.getMaxToken())) {
                request.setMaxToken(aiParams.getMaxToken().intValue());
            }
        } else {
            request.setTemperature(0.5);
            request.setModelName("gpt-4");
            request.setMaxToken(1000);
        }
        List<LlmAiRequestV2.MessageV2> messageList = new ArrayList<>(2);
        if(StringUtils.isNotBlank(param.getSystemPrompt())) {
            LlmAiRequestV2.MessageV2 sysMessage = new LlmAiRequestV2.MessageV2();
            sysMessage.setRole("system");
            LlmAiRequestV2.MessageV2.MessageContent sysContent = new LlmAiRequestV2.MessageV2.MessageContent();
            sysContent.setValue(param.getSystemPrompt());
            sysMessage.setContent(sysContent);
            messageList.add(sysMessage);
        }
        LlmAiRequestV2.MessageV2 userMessage = new LlmAiRequestV2.MessageV2();
        userMessage.setRole("user");
        LlmAiRequestV2.MessageV2.MessageContent userContent = new LlmAiRequestV2.MessageV2.MessageContent();
        userContent.setValue(param.getUserPrompt());
        userMessage.setContent(userContent);
        messageList.add(userMessage);

        request.setMessages(messageList);

        webClientBuilder.build().post()
                .uri(chatGptStreamUrl)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class)
                .onErrorResume(WebClientResponseException.class, ex -> {
                    if (ex.getStatusCode() == HttpStatus.BAD_REQUEST) {
                        return Flux.just(ex.getResponseBodyAsString());
                    } else {
                        return Flux.error(ex);
                    }
                })
                .doOnError(streamObserver::onError)
                .subscribe(data -> {
                    if(DONE.equals(data)) {
                        streamObserver.onCompleted();
                    } else {
                        // 内容格式：
                        // {
                        //    "role": "assistant",
                        //    "content": ""
                        //}
                        Map<String, String> content = JSONUtils.parseObject(data, Map.class);
                        String s = content.get("content");
                        if(s != null) {
                            s = s.replaceAll("\n", "<br/>");
                            streamObserver.onNext(s);
                        }
                    }
                });
    }
}
