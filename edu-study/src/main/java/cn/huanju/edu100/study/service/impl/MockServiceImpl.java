/**
 * Copyright (c) 2011 duowan.com.
 * All Rights Reserved.
 * This program is the confidential and proprietary information of
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.MockDao;
import cn.huanju.edu100.study.service.MockService;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Service
public class MockServiceImpl implements MockService {

	Logger logger = LoggerFactory.getLogger(MockServiceImpl.class);

	@Autowired
	MockDao mockDao;
	@Autowired
	CompatableRedisClusterClient compatableRedisClusterClient;

	public String getContent(String name) throws DataAccessException {
		return mockDao.getContent(name);
	}

	@Override
	public void testRedis() {
	}

	@Override
	public String sty_userCollectQuestion() throws DataAccessException {

		return "";
	}

	@Override
	public String sty_getUserQuestionCollectByBoxId()
			throws DataAccessException {
		HashMap<String, Object> data = new HashMap<String, Object>();
		List<Long> qIds = new ArrayList<Long>();
		qIds.add(200l);
		qIds.add(201l);
		qIds.add(202l);
		qIds.add(203l);
		qIds.add(204l);
		data.put("total", 5);
		data.put("question_ids", GsonUtil.toJson(qIds,6));
		return GsonUtil.toJson(data);
	}

	@Override
	public String sty_userIsCollectQuestion() throws DataAccessException {
		HashMap<Long, Object> data = new HashMap<Long, Object>();
		data.put(200l, true);
		data.put(201l, false);
		return null;
	}

	@Override
	public String sty_getUserWrongBoxQuestionInfo() throws DataAccessException {
		HashMap<String, Object> data = new HashMap<String, Object>();
		List<Long> qIds = new ArrayList<Long>();
		qIds.add(200l);
		qIds.add(201l);
		qIds.add(202l);
		qIds.add(203l);
		qIds.add(204l);
		data.put("total", 5);
		data.put("question_ids", qIds);
		return GsonUtil.toJson(data,6);
	}

	@Override
	public String sty_getUserAnswerBoxQuestionInfo() throws DataAccessException {
		HashMap<String, Object> data = new HashMap<String, Object>();
		List<Long> qIds = new ArrayList<Long>();
		qIds.add(200l);
		qIds.add(201l);
		qIds.add(202l);
		qIds.add(203l);
		qIds.add(204l);
		data.put("total", 5);
		data.put("question_ids", qIds);
		return GsonUtil.toJson(data,6);
	}

	@Override
	public String sty_getRamdonBoxQuestionList() throws DataAccessException {
		HashMap<String, Object> detail1 = new HashMap<String, Object>();
		detail1.put("id", 1);
		detail1.put("homework_id", 5);
		detail1.put("element_id", 200);
		detail1.put("element_type", 0);

		HashMap<String, Object> detail2 = new HashMap<String, Object>();
		detail2.put("id", 2);
		detail2.put("homework_id", 5);
		detail2.put("element_id", 201);
		detail2.put("element_type", 0);

		List<Object> details = new ArrayList<Object>();
		details.add(detail1);
		details.add(detail2);

		HashMap<String, Object> data = new HashMap<String, Object>();
		data.put("id", 5);
		data.put("name", "建设工程计价《第一章 建设工程造价构成》");
		data.put("num", 2);
		data.put("source_id", 3);
		data.put("source_type", 0);
		data.put("homework_type_id", 11);
		data.put("homework_type", 0);
		data.put("homework_details", details);

		return GsonUtil.toJson(data,6);
	}

	@Override
	public String sty_getUserBoxExerciseList() throws DataAccessException {
		HashMap<String, Object> data1 = new HashMap<String, Object>();
		data1.put("user_homework_id", 5);
		data1.put("homework_id", 3);
		data1.put("name", "建设工程计价《第一章 建设工程造价构成》");
		data1.put("num", 3);
		data1.put("right_count", 3);
		data1.put("state", 1);
		data1.put("create_date", new Date());
		data1.put("update_date", new Date());

		HashMap<String, Object> data2 = new HashMap<String, Object>();
		data2.put("user_homework_id", 6);
		data1.put("homework_id", 4);
		data2.put("name", "建设工程计价《第二章 建设工程造价构成1221》");
		data2.put("num", 3);
		data2.put("right_count", 3);
		data2.put("state", 2);
		data2.put("create_date", new Date());
		data2.put("update_date", new Date());

		List<Object> details = new ArrayList<Object>();
		details.add(data1);
		details.add(data2);

		HashMap<String, Object> data = new HashMap<String, Object>();
		data.put("exerciseList", details);
		data.put("total", 2);
		return GsonUtil.toJson(details,6);
	}

	@Override
	public String sty_getUserIsDoExerciseById() throws DataAccessException {
		List<Object> details = new ArrayList<Object>();

		HashMap<String, Object> data1 = new HashMap<String, Object>();
		data1.put("homework_id", 5);
		data1.put("user_homeword_id", 1);
		data1.put("state", 2);
		data1.put("right_count", 3);

		HashMap<String, Object> data2 = new HashMap<String, Object>();
		data2.put("homework_id", 6);
		data2.put("user_homeword_id", 2);
		data2.put("state", 0);
		data2.put("right_count", 0);

		details.add(data1);
		details.add(data2);
		return GsonUtil.toJson(details,6);
	}

	@Override
	public String sty_getUserAnswerHis() throws DataAccessException {
		List<Object> details = new ArrayList<Object>();

		HashMap<String, Object> data1 = new HashMap<String, Object>();
		data1.put("question_id", 200);
		data1.put("answer_num", 5);
		data1.put("right_num", 2);
		data1.put("last_answer", "A");

		HashMap<String, Object> data2 = new HashMap<String, Object>();
		data2.put("question_id", 201);
		data2.put("answer_num", 0);
		data2.put("right_num", 0);
		data2.put("last_answer", "");

		details.add(data1);
		details.add(data2);
		return GsonUtil.toJson(details,6);
	}

	@Override
	public String sty_getUserBoxPaperList() throws DataAccessException {
		List<Object> details = new ArrayList<Object>();

		HashMap<String, Object> data1 = new HashMap<String, Object>();
		data1.put("id", 200);
		data1.put("uid", 5001145);
		data1.put("paper_id", 2);
		data1.put("title", "试卷名称啊啊啊");
		data1.put("obj_id", 5);
		data1.put("obj_type", 2);
		data1.put("paper_type", 2);
		data1.put("source", 2);
		data1.put("score", 100);
		data1.put("usetime", 60);
		data1.put("answer_num", 100);
		data1.put("state", 2);
		data1.put("create_date", "2015-08-10 12:12:12");
		data1.put("update_date", "2015-08-10 12:12:12");

		HashMap<String, Object> data2 = new HashMap<String, Object>();
		data2.put("id", 201);
		data2.put("uid", 5001145);
		data2.put("paper_id", 3);
		data2.put("title", "试卷名称123");
		data2.put("obj_id", 5);
		data2.put("obj_type", 2);
		data2.put("paper_type", 2);
		data2.put("source", 2);
		data2.put("score", 80);
		data2.put("usetime", 60);
		data2.put("answer_num", 80);
		data2.put("state", 1);
		data2.put("create_date", "2015-08-10 12:12:12");
		data2.put("update_date", "2015-08-10 12:12:12");

		details.add(data1);
		details.add(data2);
		return GsonUtil.toJson(details,6);
	}

	@Override
	public String sty_getUserIsDoPaperById() throws DataAccessException {
		HashMap<Long, Integer> data1 = new HashMap<Long, Integer>();
		data1.put(200l, 0);
		data1.put(201l, 1);
		data1.put(202l, 2);
		return GsonUtil.toJson(data1,6);
	}

	@Override
	public String sty_isAuthPaperByUid() throws DataAccessException {
		HashMap<Long, Boolean> data1 = new HashMap<Long, Boolean>();
		data1.put(200l, true);
		data1.put(201l, false);
		return GsonUtil.toJson(data1,6);
	}

	@Override
	public String sty_getUserLastPractice() throws DataAccessException {
		HashMap<String, Object> data1 = new HashMap<String, Object>();
		data1.put("uid", 5001145L);
		data1.put("type", 1);
		data1.put("last_practice_id", 200L);
		return GsonUtil.toJson(data1,6);
	}

}
