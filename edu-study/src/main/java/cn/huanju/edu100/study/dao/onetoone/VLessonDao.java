/**
 *
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.onetoone.VLesson;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 面授课节DAO接口
 * <AUTHOR>
 * @version 2016-04-12
 */
public interface VLessonDao extends CrudDao<VLesson> {

    List<VLesson> findListByParam(List<Long> clsIds, Long teacherUid, Date startTime, Date endTime, List<Long> schIds) throws DataAccessException;

    Integer getCompleteCountByClsId(Long clsId) throws DataAccessException;

    List<VLesson> findListByIds(List<Long> ids) throws DataAccessException;

    Double getPeriodCount(VLesson entity) throws DataAccessException;

    List<VLesson> findListByParam(Map<String, Object> param) throws DataAccessException;

}
