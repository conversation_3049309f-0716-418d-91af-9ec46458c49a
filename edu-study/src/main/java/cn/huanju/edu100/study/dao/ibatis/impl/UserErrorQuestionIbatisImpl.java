/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserErrorQuestionDao;
import cn.huanju.edu100.study.model.UserErrorQuestion;
import cn.huanju.edu100.util.GsonUtil;
import com.google.gson.Gson;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 用户错题集DAO接口
 * <AUTHOR>
 * @version 2015-05-17
 */
public class UserErrorQuestionIbatisImpl extends CrudIbatisImpl2<UserErrorQuestion> implements
	UserErrorQuestionDao {

	public UserErrorQuestionIbatisImpl() {
		super("UserErrorQuestion");
	}

	/**
	 * 移除在lesson或paragraph上的错题
	 * */
	@Override
	public boolean removeErrQuestionWithlesson(UserErrorQuestion errorQuestion)
			throws DataAccessException {
		if (errorQuestion == null || errorQuestion.getUid() == null || errorQuestion.getUid() <= 0L ||
				errorQuestion.getQuestionId() == null || errorQuestion.getQuestionId() <=0L) {
			logger.error("removeErrQuestionWithlesson {} error, parameter is null", namespace);
			throw new DataAccessException("removeErrQuestionWithlesson {} error,param is null",
					namespace);
		}
		SqlMapClient sqlMap = super.getMaster();
		if (sqlMap == null) {
			logger.error(
					"removeErrQuestionWithlesson SQLException sqlMap is null");
			throw new DataAccessException(
					"removeErrQuestionWithlesson SQLException error, sqlMap is null");
		}
		try {
			return sqlMap.delete(namespace.concat(".removeErrQuestionWithlesson"), errorQuestion) > 0;
		} catch (SQLException e) {
			logger.error("removeErrQuestionWithlesson SQLException.content:{}",
					(new Gson()).toJson(errorQuestion), e);
			throw new DataAccessException("removeErrQuestionWithlesson SQLException fail.");
		}
	}

	/**
	 * 移除不在lesson和paragraph上的错题
	 * */
	@Override
	public boolean removeErrQuestion(UserErrorQuestion errorQuestion)
			throws DataAccessException {
		if (errorQuestion == null || errorQuestion.getUid() == null || errorQuestion.getUid() <= 0L ||
				errorQuestion.getQuestionId() == null || errorQuestion.getQuestionId() <=0L) {
			logger.error("removeErrQuestion {} error, parameter is null", namespace);
			throw new DataAccessException("removeErrQuestion {} error,param is null",
					namespace);
		}
		SqlMapClient sqlMap = super.getMaster();
		if (sqlMap == null) {
			logger.error(
					"removeErrQuestion SQLException sqlMap is null");
			throw new DataAccessException(
					"removeErrQuestion SQLException error, sqlMap is null");
		}
		try {
			return sqlMap.delete(namespace.concat(".removeErrQuestion"), errorQuestion) > 0;
		} catch (SQLException e) {
			logger.error("removeErrQuestion SQLException.content:{}",
					(new Gson()).toJson(errorQuestion), e);
			throw new DataAccessException("removeErrQuestion SQLException fail.");
		}
	}

	@Override
	public Integer removeUserErrorQuestionList(List<UserErrorQuestion> errorList)
			throws DataAccessException {
		SqlMapClient sqlMap = super.getMaster();
		if (sqlMap == null) {
			logger.error("removeUserErrorQuestionList SQLException sqlMap is null");
			throw new DataAccessException(
					"removeUserErrorQuestionList SQLException error, sqlMap is null");
		}
        try {
        	int total=0;
        	sqlMap.startTransaction();
            sqlMap.startBatch();
        	for (UserErrorQuestion userErrorQuestion : errorList) {
        		if (userErrorQuestion.getId()!=null) {
        			sqlMap.update(namespace.concat(".delete"), userErrorQuestion);
				}
			}
        	total = sqlMap.executeBatch();
        	sqlMap.commitTransaction();
        	logger.info("removeUserErrorQuestionList list size:{},retSize:{}",errorList.size(),total);
        	return total;
        } catch (SQLException e) {
            logger.error("removeUserErrorQuestionList SQLException.content:{}",
                    GsonUtil.getGson().toJson(errorList), e);
            throw new DataAccessException("removeUserErrorQuestionList SQLException fail.");
        }finally {
            try {
            	sqlMap.endTransaction();
           } catch (SQLException e) {
        	   e.printStackTrace();
           }
        }
	}


	@Override
	public List<UserErrorQuestion> findHomeworkErrorQuestionList(Map<String, Object> params) throws DataAccessException {
		if (params == null) {
			logger.error("findHomeworkErrorQuestionList {} error, parameter is null", namespace);
			throw new DataAccessException("findHomeworkErrorQuestionList error,parameter error");
		}

		try {
			SqlMapClient sqlMap = super.getSlave();
			return (List<UserErrorQuestion>) sqlMap.queryForList(namespace.concat(".findHomeworkErrorQuestionList"), params);
		} catch (SQLException e) {
			logger.error("findHomeworkErrorQuestionList {} SQLException params:{}", namespace, params, e);
			throw new DataAccessException("get SQLException error" + e.getMessage());
		}
	}
}
