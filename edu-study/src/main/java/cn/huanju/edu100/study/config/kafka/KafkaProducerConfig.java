package cn.huanju.edu100.study.config.kafka;

import cn.huanju.edu100.study.kafka.KafkaProducerListener;
import cn.huanju.edu100.study.kafka.question.KafkaMsgProducerService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.HashMap;

@Configuration
public class KafkaProducerConfig {
    @Value("${kafka.servers}")
    private String kafkaServers;
    @Value("${kafka.secret.servers}")
    private String kafkaSecretServers;
    @Value("${security.protocol}")
    private String securityProtocol;
    @Value("${sasl.mechanism}")
    private String saslMechanism;
    @Value("${sasl.jaas.config}")
    private String saslJaasConfig;

    @Bean("questionStaticProducerService")
    public KafkaMsgProducerService questionStaticProducerService(){
        KafkaMsgProducerService service = new KafkaMsgProducerService();
        service.setKafkaTemplate(kafkaTemplate());
        return service;
    }

    @Bean("fundProducerService")
    public KafkaMsgProducerService fundProducerService(){
        KafkaMsgProducerService service = new KafkaMsgProducerService();
        service.setKafkaTemplate(kafkaTemplate());
        return service;
    }

    @Bean("KafkaTemplate")
    public KafkaTemplate kafkaTemplate(){
        KafkaTemplate kafkaTemplate = new KafkaTemplate(producerFactory(), true);
        kafkaTemplate.setDefaultTopic("replicated-topic");
        kafkaTemplate.setProducerListener(producerListener());
        return kafkaTemplate;
    }

    @Bean("producerFactory")
    public DefaultKafkaProducerFactory producerFactory(){
        return new DefaultKafkaProducerFactory(producerProperties());
    }

    @Bean("producerProperties")
    public HashMap producerProperties(){
        HashMap<String, String> hashMap = new HashMap();
        hashMap.put("bootstrap.servers", kafkaServers);
        hashMap.put("group.id", "0");
        hashMap.put("retries", "1");
        hashMap.put("batch.size", "16384");
        hashMap.put("linger.ms", "1");
        hashMap.put("buffer.memory", "33554432");
        hashMap.put("max.request.size","2097152");
//        hashMap.put("enable.idempotence","false");
        hashMap.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        hashMap.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        return hashMap;
    }

    @Bean("producerListener")
    public KafkaProducerListener producerListener(){
        return new KafkaProducerListener();
    }

    @Bean("secretStaticProducerService")
    public KafkaMsgProducerService secretStaticProducerService(){
        KafkaMsgProducerService service = new KafkaMsgProducerService();
        service.setKafkaTemplate(secretKafkaTemplate());
        return service;
    }

    @Bean("secretKafkaTemplate")
    public KafkaTemplate secretKafkaTemplate(){
        KafkaTemplate template = new KafkaTemplate(secretProducerFactory(), true);
        template.setDefaultTopic("replicated-topic");
        template.setProducerListener(secretProducerListener());
        return template;
    }

    @Bean("secretProducerFactory")
    public DefaultKafkaProducerFactory secretProducerFactory(){
        return new DefaultKafkaProducerFactory(secretProducerProperties());
    }

    @Bean("secretProducerProperties")
    public HashMap secretProducerProperties(){
        HashMap hashMap = new HashMap();
        hashMap.put("bootstrap.servers", kafkaSecretServers);
        hashMap.put("group.id", "0");
        hashMap.put("retries", "1");
        hashMap.put("batch.size", "16384");
        hashMap.put("linger.ms", "1");
        hashMap.put("buffer.memory", "33554432");
        hashMap.put("max.request.size","2097152");
        hashMap.put("enable.idempotence","false"); //加密集群需要追加这个
        hashMap.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        hashMap.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        hashMap.put("security.protocol", securityProtocol);
        hashMap.put("sasl.mechanism", saslMechanism);
        hashMap.put("sasl.jaas.config", saslJaasConfig);
        return hashMap;
    }

    @Bean("secretProducerListener")
    public KafkaProducerListener secretProducerListener(){
        return new KafkaProducerListener();
    }
}
