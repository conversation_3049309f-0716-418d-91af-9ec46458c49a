package cn.huanju.edu100.study.service.impl.syncAnswer;

import cn.huanju.edu100.study.dao.GroupStudentDao;
import cn.huanju.edu100.study.dao.tutor.*;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.tutor.*;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.tutor.TutorStudentPushResService;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.ThreadPoolFactoryUtil;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import com.hqwx.study.entity.UserHomeWorkAnswer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 云私塾用户个性化任务proxy
 *
 * <AUTHOR>
 *
 * */
@Component()
public class TutorAnswerSubmitProxy {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static int numOfThreads = 2;// 默认的线程数
    private static int cpuNums = Runtime.getRuntime().availableProcessors();
    private static ExecutorService executor = ThreadPoolFactoryUtil.createDefaultPool("tutorAnswerSubmitProxyThreadPool");

    public final static int cachedQueueMaxSize = 6 * 10000; // 缓存待入库的学生答题队列的最大size

    public int batchNum = 1000; // 默认每次批量get的记录条数
    public long period = 1000; // 默认每次批量get的最长时间周期(单位为毫秒)

    public static LinkedBlockingQueue<UserPersonalTaskCachedBean> userPersonalTask_Queue = new LinkedBlockingQueue<UserPersonalTaskCachedBean>(
            cachedQueueMaxSize);

    @Autowired
    private TutorStudentTaskDao tutorStudentTaskDao;

    @Autowired
    private TutorStudentAnswerDao tutorStudentAnswerDao;

    @Autowired
    private TutorStudentAnswerDetailDao tutorStudentAnswerDetailDao;

    @Autowired
    private GroupStudentDao groupStudentDao;

    @Autowired
    private TutorTaskDao tutorTaskDao;

    @Autowired
    private TutorWeikeClassDao tutorWeikeClassDao;

    @Autowired
    private TutorUnitDao tutorUnitDao;

    @Autowired
    private TutorStudentEventLogDao tutorStudentEventLogDao;

    @Autowired
    private TutorStudentPushResService tutorStudentPushResService;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Scheduled(cron = "0/3 * * * * ?")
    public void process() {// 每3s钟把队列里的batchNum条提交数据取出来执行
        if (!userPersonalTask_Queue.isEmpty()) {
            long startTime = System.currentTimeMillis(); // 本次批量get的开始时间

            while (true) {
                List<UserPersonalTaskCachedBean> list = new ArrayList<TutorAnswerSubmitProxy.UserPersonalTaskCachedBean>();

                userPersonalTask_Queue.drainTo(list, batchNum);
                for (UserPersonalTaskCachedBean userHomeWorkCachedBean : list) {
                    executor.submit(userHomeWorkCachedBean);
                }

                long endTime = System.currentTimeMillis();
                if (userPersonalTask_Queue.isEmpty() || (endTime - startTime) >= period) {
                    break;
                }
            }
        }
    }

    public void submitPersonalTask(UserTaskAnswer userTaskAnswer, List<UserAnswerDetail> details) {
        try {
            userPersonalTask_Queue.put(new UserPersonalTaskCachedBean(userTaskAnswer, details));
        } catch (InterruptedException e) {
            logger.error("submitPersonalTask syn2Queue InterruptedException .", e);
        }
    }

    class UserPersonalTaskCachedBean implements Callable<Void> {
        private UserTaskAnswer userTaskAnswer;
        private List<UserAnswerDetail> details;

        public UserPersonalTaskCachedBean(UserTaskAnswer userTaskAnswer, List<UserAnswerDetail> details) {
            this.userTaskAnswer = userTaskAnswer;
            this.details = details;
        }

        public UserTaskAnswer getUserTaskAnswer() {
            return userTaskAnswer;
        }

        public void setUserTaskAnswer(UserTaskAnswer userTaskAnswer) {
            this.userTaskAnswer = userTaskAnswer;
        }

        public List<UserAnswerDetail> getDetails() {
            return details;
        }

        public void setDetails(List<UserAnswerDetail> details) {
            this.details = details;
        }

        @Override
        public Void call() throws Exception {

            try {
                Long useTime = 0L;
                if (userTaskAnswer.getUsetime() != null) {
                    useTime = userTaskAnswer.getUsetime();
                } else if (userTaskAnswer.getStartTime() != null && userTaskAnswer.getEndTime() != null) {
                    useTime = (userTaskAnswer.getEndTime().getTime() - userTaskAnswer.getStartTime().getTime());
                }
                userTaskAnswer.setStudyDuration(useTime.intValue());

                // 1、将根据taskId和uid查询student_task表，有记录则更新状态，没有则insert并返回自增的主键值
                Long taskId = userTaskAnswer.getTaskId();
                Long uid = userTaskAnswer.getUid();
                Long wkSectionId = userTaskAnswer.getWkSectionId();
                Integer objType = userTaskAnswer.getObjType();

                logger.info("UserPersonalTaskCachedBean call start, uid:{}, taskId:{}, wkSectionId:{}, objType:{}", uid, taskId, wkSectionId, objType);
                Date now = new Date();
                TutorTask tutorTask = null;
                TutorWeikeClass wkClass = null;
                if (!objType.equals(UserAnswer.ObjType.weikeClassPaper)) {
                    tutorTask = tutorTaskDao.get(taskId);
                    if (tutorTask == null) {
                        logger.error("[UserPersonalTaskCachedBean] not found tutorTask, uid:{}, taskId:{}", uid, taskId);
                        return null;
                    }
                } else {
                    wkClass = tutorWeikeClassDao.getBySectionId(wkSectionId);
                    if (null == wkClass) {
                        logger.error("[UserPersonalTaskCachedBean] not found weikeClass, uid:{}, sectionId:{}", uid, wkSectionId);
                        return null;
                    }
                    TutorUnit unit = tutorUnitDao.getByWkId(wkClass.getId());
                    if (null == unit) {
                        logger.error("[UserPersonalTaskCachedBean] not found unit, uid:{}, sectionId:{}", uid, wkSectionId);
                        return null;
                    }
                    wkClass.setUnit(unit);
                }
                Integer isSubmit = userTaskAnswer.getIsSubmit() == null ? 0 : userTaskAnswer.getIsSubmit();
                int taskStatus = 0;
                // 做段落作业为进行中或者提前做测评作业
                if (1 != isSubmit || userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.RESOURCE_VIDEO) {
                    taskStatus = Consts.STUDENT_TASK_STATUS.DONING;
                } else {
                    taskStatus = Consts.STUDENT_TASK_STATUS.COMPLETE;
                }
                Integer tutorStuTaskSource = null;
                if (!objType.equals(UserAnswer.ObjType.weikeClassPaper)) {
                    tutorStuTaskSource = Consts.Tutor_Student_Task_Source.TASK;
                } else {
                    tutorStuTaskSource = Consts.Tutor_Student_Task_Source.WEIKE_CLASS;
                }
                TutorStudentTask oldStudentTask = tutorStudentTaskDao.findLimitOneByTidAndUid(objType.equals(UserAnswer.ObjType.weikeClassPaper) ? wkSectionId : taskId, uid, tutorStuTaskSource);

                // TODO 保存的时候是否要进做题记录表?
                // 2、保存本次做题记录
                Integer answerType = convertAnswerType();
                TutorStudentAnswer tutorStudentAnswer = new TutorStudentAnswer();
                tutorStudentAnswer.setGroupId(userTaskAnswer.getGroupId());
                tutorStudentAnswer.setObjId(userTaskAnswer.getObjId());
                tutorStudentAnswer.setParentObjId(userTaskAnswer.getParentObjId());
                tutorStudentAnswer.setUid(uid);
                tutorStudentAnswer.setStudyDuration(useTime);
                tutorStudentAnswer.setObjType(answerType);
                tutorStudentAnswer.setSource(tutorStuTaskSource);
                tutorStudentAnswer.setPlatForm(userTaskAnswer.getPlatForm());
                tutorStudentAnswer.setAppid(userTaskAnswer.getAppid());
                if (!objType.equals(UserAnswer.ObjType.weikeClassPaper)) {
                    // 任务
                    tutorStudentAnswer.setClasses(tutorTask.getClasses());
                    tutorStudentAnswer.setTaskId(taskId);
                    tutorStudentAnswer.setPlaneId(tutorTask.getPlanId());
                    tutorStudentAnswer.setPhaseId(tutorTask.getPhaseId());
                    tutorStudentAnswer.setUnitId(tutorTask.getUnitId());
                    tutorStudentAnswer.setFirstCategory(tutorTask.getFirstCategory());
                    tutorStudentAnswer.setSecondCategory(tutorTask.getSecondCategory());
                    tutorStudentAnswer.setCategoryId(tutorTask.getCategoryId());
                } else {
                    // 微课班试卷
                    tutorStudentAnswer.setClasses(wkClass.getClasses());
                    tutorStudentAnswer.setTaskId(wkSectionId);
                    tutorStudentAnswer.setPlaneId(wkClass.getUnit().getPlanId());
                    tutorStudentAnswer.setPhaseId(wkClass.getUnit().getPhaseId());
                    tutorStudentAnswer.setUnitId(wkClass.getUnit().getId());
                    tutorStudentAnswer.setFirstCategory(wkClass.getFirstCategory());
                    tutorStudentAnswer.setSecondCategory(wkClass.getSecondCategory());
                    tutorStudentAnswer.setCategoryId(wkClass.getCategoryId());
                }
                // key：questionId
                Map<Long, Boolean> questionResultMap = new HashMap<Long, Boolean>();// 为了去除重复
                Map<Long, UserAnswerDetail> quadMap = new HashMap<Long, UserAnswerDetail>();
                boolean questionIsRight = false;
                long wrongQuesNum = 0;
                long rightQuesNum = 0;
                List<Long> wrongQuesIdList = Lists.newArrayList();
                List<Long> questionIdList = Lists.newArrayList();
                for (UserAnswerDetail userAnswerDetail : details) {
                    quadMap.put(userAnswerDetail.getQuestionId(), userAnswerDetail);
                    questionIsRight = false;
                    if (userAnswerDetail.getIsRight() == null
                            || userAnswerDetail.getIsRight() == UserAnswerDetail.IsRight.WRONG
                            || userAnswerDetail.getIsRight() == UserAnswerDetail.IsRight.HALF_RIGHT) {
                        wrongQuesNum++;
                        wrongQuesIdList.add(userAnswerDetail.getQuestionId());
                    } else if (userAnswerDetail.getIsRight() != null
                            && userAnswerDetail.getIsRight() == UserAnswerDetail.IsRight.RIGHT) {
                        rightQuesNum++;
                        questionIsRight = true;
                    }
                    Boolean qValue = questionResultMap.get(userAnswerDetail.getQuestionId());
                    if (qValue != null) {
                        questionResultMap.put(userAnswerDetail.getQuestionId(), questionIsRight && qValue);
                    } else {
                        questionIdList.add(userAnswerDetail.getQuestionId());
                        questionResultMap.put(userAnswerDetail.getQuestionId(), questionIsRight);
                    }
                }

                // 重复做任务状态不同则更新学生任务关系
                Paper paper = null;
                if (userTaskAnswer.getPaperId() != null) {
                    paper = knowledgeResource.getPaperInfoById(userTaskAnswer.getPaperId());
                }
                BigDecimal bigDecimal = BigDecimal.valueOf(rightQuesNum * 100d / details.size()); // 题目作对百分比
                if (oldStudentTask != null) {
                    if (oldStudentTask.getStatus() == null
                            || (oldStudentTask.getStatus() != null && taskStatus > oldStudentTask.getStatus())) {
                        oldStudentTask.setStatus(taskStatus);
                    }
                    if (!objType.equals(UserAnswer.ObjType.weikeClassPaper)) {
                        oldStudentTask.setUnitId(tutorTask.getUnitId());
                    } else {
                        oldStudentTask.setUnitId(wkClass.getUnit().getId());
                    }
                    setScore(oldStudentTask, paper, bigDecimal);
                    boolean updateResult = tutorStudentTaskDao.update(oldStudentTask); // 状态修改为2（已提交）
                    if (false == updateResult) {
                        throw new DataAccessException(String.valueOf(cn.huanju.edu100.util.Constants.SYS_ERROR),
                                "updata StudentTask state fail.");
                    }
                } else if (oldStudentTask == null) {
                    TutorStudentTask temStudentTask = new TutorStudentTask();
                    temStudentTask.setType(String.valueOf(null == userTaskAnswer.getType() ? 1 : userTaskAnswer
                            .getType()));
                    setScore(temStudentTask, paper, bigDecimal);
                    temStudentTask.setUid(uid);
                    temStudentTask.setStatus(taskStatus);
                    temStudentTask.setCreateBy(0l);
                    temStudentTask.setCreateDate(now);
                    temStudentTask.setUpdateDate(now);
                    temStudentTask.setSource(tutorStuTaskSource);
                    if (!objType.equals(UserAnswer.ObjType.weikeClassPaper)) {
                        temStudentTask.setTaskId(taskId);
                        temStudentTask.setPlanId(tutorTask.getPlanId());
                        temStudentTask.setPhaseId(tutorTask.getPhaseId());
                        temStudentTask.setUnitId(tutorTask.getUnitId());
                    } else {
                        temStudentTask.setTaskId(wkSectionId);
                        temStudentTask.setPlanId(wkClass.getUnit().getPlanId());
                        temStudentTask.setPhaseId(wkClass.getUnit().getPhaseId());
                        temStudentTask.setUnitId(wkClass.getUnit().getId());
                    }
                    tutorStudentTaskDao.insert(temStudentTask);
                }

                // for (Map.Entry<Long, Boolean> data :
                // questionResultMap.entrySet()) {
                // if (!data.getValue()) {
                // wrongQuesNum++;
                // wrongQuesIdList.add(data.getKey());
                // }
                // }
                //未作答的不计入
                tutorStudentAnswer.setAnswerNum(wrongQuesNum+rightQuesNum);
                tutorStudentAnswer.setWrongNum(wrongQuesNum);
                tutorStudentAnswer.setCreateDate(new Date());
                int pushType = UserHomeWorkAnswer.HomeWorkType.M_CLASS == userTaskAnswer.getObjType() ? Consts.Tutor_Student_Answer_Type.PUSH
                        : Consts.Tutor_Student_Answer_Type.ACTIVELY;
                tutorStudentAnswer.setType(pushType); // 主动学习填1,微课是推送学习
                Long studentAnswerLogId = tutorStudentAnswerDao.insert(tutorStudentAnswer);

                // 3、插入到做题明细表中
                // TODO 案例题先放大题
                TutorStudentAnswerDetail studentAnswerDetail = null;
                // for (UserAnswerDetail userAnswerDetail : details) {
                for (Long questionId : questionIdList) {
                    UserAnswerDetail userAnswerDetail = quadMap.get(questionId);
                    if (userAnswerDetail == null) {
                        continue;
                    }
                    studentAnswerDetail = new TutorStudentAnswerDetail();
                    studentAnswerDetail.setLogId(studentAnswerLogId);
                    studentAnswerDetail.setQuestionId(userAnswerDetail.getQuestionId());
                    studentAnswerDetail.setUid(uid);
                    if (!objType.equals(UserAnswer.ObjType.weikeClassPaper)) {
                        studentAnswerDetail.setClasses(tutorTask.getClasses());
                    } else {
                        studentAnswerDetail.setClasses(wkClass.getClasses());
                    }
                    // studentAnswerDetail.setResult(userAnswerDetail.getIsRight());

                    //默认未作答
                    Integer result = UserAnswerDetail.IsRight.NOT_ANSWER;
                    Boolean isRight = questionResultMap.get(questionId);
                    if (isRight == null ||
                            (userAnswerDetail.getIsRight()!=null
                                    && userAnswerDetail.getIsRight() == UserAnswerDetail.IsRight.NOT_ANSWER)) {
                        result = UserAnswerDetail.IsRight.NOT_ANSWER;
                    } else {
                        result = isRight ? UserAnswerDetail.IsRight.RIGHT : UserAnswerDetail.IsRight.WRONG;
                    }
                    studentAnswerDetail.setResult(result);
                    now = new Date();
                    studentAnswerDetail.setCreateDate(now);
                    tutorStudentAnswerDetailDao.insert(studentAnswerDetail);
                }

                // 4、更新用戶最新一次错题记录,答错就更新,全部答对就删除上次错题记录 注：微课作业不用进最新记录表
                logger.info(
                        "UserPersonalTaskCachedBean TutorStudentEventLog begin, uid:{}, taskId:{},wkSectionId:{}, objTyp:{}, taskstatus:{}",
                        uid, taskId, wkSectionId, userTaskAnswer.getObjType(), taskStatus);
                /*
                 * if (UserHomeWorkAnswer.HomeWorkType.M_CLASS !=
                 * userTaskAnswer.getObjType()
                 * && UserHomeWorkAnswer.HomeWorkType.RESOURCE_VIDEO !=
                 * userTaskAnswer.getObjType()) {
                 */if (UserHomeWorkAnswer.HomeWorkType.M_CLASS != userTaskAnswer.getObjType()
                        && UserHomeWorkAnswer.HomeWorkType.RESOURCE_VIDEO != userTaskAnswer.getObjType()) {
                    TutorStudentEventLog eventLogEntity = new TutorStudentEventLog();
                    eventLogEntity.setUid(uid);
                    if (!objType.equals(UserAnswer.ObjType.weikeClassPaper)) {
                        eventLogEntity.setTaskId(taskId);
                    } else {
                        eventLogEntity.setTaskId(wkSectionId);
                    }
                    eventLogEntity.setSource(tutorStuTaskSource);
                    eventLogEntity.setObjType(answerType);
                    eventLogEntity.setObjId(userTaskAnswer.getObjId());
                    eventLogEntity.setParentObjId(userTaskAnswer.getParentObjId());
                    List<TutorStudentEventLog> eventLogList = tutorStudentEventLogDao.findList(eventLogEntity);
                    logger.info(
                            "UserPersonalTaskCachedBean TutorStudentEventLog going, eventLogList:{}, wrongQuesNum:{}",
                            GsonUtil.toJson(eventLogList), wrongQuesNum);
                    if (CollectionUtils.isEmpty(eventLogList) && wrongQuesNum > 0) {
                        eventLogEntity = new TutorStudentEventLog();
                        BeanUtils.copyProperties(tutorStudentAnswer, eventLogEntity);
                        now = new Date();
                        eventLogEntity.setForeignId(studentAnswerLogId);
                        eventLogEntity.setCreateDate(now);
                        eventLogEntity.setUpdateDate(now);
                        if (!objType.equals(UserAnswer.ObjType.weikeClassPaper)) {
                            eventLogEntity.setUnitId(tutorTask.getUnitId());
                        } else {
                            eventLogEntity.setUnitId(wkClass.getUnit().getId());
                        }
                        eventLogEntity.setType(Consts.TUTOR_STUDENT_EVENT_TYPE.WRONG_QUESTION);
                        tutorStudentEventLogDao.insert(eventLogEntity);
                    } else if (!CollectionUtils.isEmpty(eventLogList) && wrongQuesNum <= 0) {
                        eventLogEntity = eventLogList.get(0);
                        tutorStudentEventLogDao.delete(eventLogEntity.getId());
                    } else if (!CollectionUtils.isEmpty(eventLogList) && wrongQuesNum > 0) {
                        eventLogEntity = eventLogList.get(0);
                        eventLogEntity.setForeignId(studentAnswerLogId); // 需要重新设记录id
                        eventLogEntity.setAnswerNum((long) details.size());
                        eventLogEntity.setWrongNum(wrongQuesNum);
                        eventLogEntity.setUpdateDate(now);
                        tutorStudentEventLogDao.update(eventLogEntity);
                    }
                }

                // TODO 推微课，用户通知？
                if (!CollectionUtils.isEmpty(wrongQuesIdList) && pushType != Consts.Tutor_Student_Answer_Type.PUSH) {
                    // 录播课测评不推送微课
                    if (userTaskAnswer.getObjType() == null
                            || userTaskAnswer.getObjType() != UserHomeWorkAnswer.HomeWorkType.RESOURCE_VIDEO) {
                        tutorStudentPushResService.pushLessonVideo(uid, wrongQuesIdList, tutorTask, studentAnswerLogId,
                                Consts.Tutor_Student_Push_Resource.WEI_KE);
                    }
                }

                logger.info("UserPersonalTaskCachedBean call end, uid:{}, taskId:{},wkSectionId:{}, studentAnswerLogId:{}", uid,
                        taskId, wkSectionId, studentAnswerLogId);
            } catch (Exception e) {
                logger.error("UserPersonalTaskCachedBean call error, uid:{}, taskId:{}, wkSectionId:{}", userTaskAnswer.getUid(),
                        userTaskAnswer.getTaskId(), userTaskAnswer.getWkSectionId(), e);
            }

            return null;
        }

        private void setScore(TutorStudentTask oldStudentTask, Paper paper, BigDecimal bigDecimal) {
            if (userTaskAnswer.getPaperId() != null) { // 录播课测评保存题目正确率百分比，试卷保存分数
                if (paper != null && paper.getPaperScore() != null) {
                    oldStudentTask.setTotal(paper.getPaperScore());
                }
                if (userTaskAnswer.getScore() == null) {
                    oldStudentTask.setScore(0.0d);
                } else {
                    oldStudentTask
                            .setScore(Math.max(userTaskAnswer.getScore(), oldStudentTask.getScore()));
                }
            } else {
                if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.RESOURCE_VIDEO) {
                    if (bigDecimal.doubleValue() > oldStudentTask.getScore()) {
                        oldStudentTask.setScore(bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                    }
                }
            }
        }

        /**
         * 转换做题对象类型
         *
         * @return
         */
        private Integer convertAnswerType() {
            Integer answerType = null;
            if (userTaskAnswer.getPaperId() == null || userTaskAnswer.getPaperId() <= 0) {
                if (userTaskAnswer.getmClassId() != null) {
                    answerType = Consts.answerType.MCLASS_HOMEWORK;
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.LESSON) {
                    answerType = Consts.answerType.LESSON_HOMEWORK;
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.PARAGRAPH) {
                    answerType = Consts.answerType.PARAGRAPH_HOMEWORK;
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.COURSE_PARAGRAPH) {
                    answerType = Consts.answerType.NEW_PARAGRAPH_HOMEWORK;// TODO:新录播课作业
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.RESOURCE_VIDEO) {
                    answerType = Consts.answerType.RESOURCE_VIDEO;// 讲测评作业
                } else if (userTaskAnswer.getObjType() != null
                        && userTaskAnswer.getObjType() == UserHomeWorkAnswer.HomeWorkType.WEIKE_CLASS) {
                    answerType = Consts.answerType.WEIKE_CLASS;// 微课班
                } else {
                    answerType = Consts.answerType.PESONAL_TEST;
                }
            } else {
                answerType = Consts.answerType.PAPER;
            }
            return answerType;
        }
    }

    // @Override
    public void init() {
        logger.info("do init....");
    }

    // @Override
    public void destroy() {
        logger.info("do destroy....");
    }
}
