package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.UserQuestionErrorCorrect;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

public interface UserQuestionErrorCorrectService extends BaseService<UserQuestionErrorCorrect> {

	int reportErrorQuestionCorrect(UserQuestionErrorCorrect param) throws DataAccessException;

	List<UserQuestionErrorCorrect> getUserQuestionErrorCorrect(UserQuestionErrorCorrect userQuestionErrorCorrect)throws DataAccessException;

}
