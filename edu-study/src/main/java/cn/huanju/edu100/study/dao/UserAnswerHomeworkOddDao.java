package cn.huanju.edu100.study.dao;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.dao.CrudDao;
import com.hqwx.study.entity.UserHomeWorkAnswer;

import java.util.List;
import java.util.Map;

public interface UserAnswerHomeworkOddDao extends CrudDao<UserHomeWorkAnswer> {

	int updateByParam(Map<String, Object> param) throws DataAccessException;

	List<UserHomeWorkAnswer> selectByParam(Map<String, Object> param) throws DataAccessException;

	int updateHomeworkIdByUid(Map<String, Object> param) throws DataAccessException;

	UserHomeWorkAnswer getMaxIdByTbIndex(Integer tbIndex) throws DataAccessException;

	Integer selectCountByMaxId(Integer tbIndex, Long id) throws DataAccessException;

	List<UserHomeWorkAnswer> selectListByMaxId(Integer tbIndex, Long id, int from, int rows) throws DataAccessException;

}