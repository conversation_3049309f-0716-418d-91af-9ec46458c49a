package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorStudentTaskCount;
import cn.huanju.edu100.exception.DataAccessException;

/**
 * 任务统计Service
 * <AUTHOR>
 * @version 2016-01-18
 */
public interface TutorStudentTaskCountService extends BaseService<TutorStudentTaskCount> {

    Double getStudyProgressByUid(TutorStudentTaskCount params) throws DataAccessException;

}
