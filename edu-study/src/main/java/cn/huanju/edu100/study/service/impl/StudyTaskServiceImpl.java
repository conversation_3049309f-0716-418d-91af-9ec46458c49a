package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.BusinessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.*;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.*;
import cn.huanju.edu100.study.util.ValidateUtils;
import cn.huanju.edu100.util.Constants;
import cn.huanju.edu100.util.EduStringUtils;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.util.ListUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 学习任务Service
 *
 * <AUTHOR>
 * @version 2015-05-15
 */
@Service
public class StudyTaskServiceImpl extends BaseServiceImpl<StudyTaskDao, StudyTask> implements StudyTaskService {
    private static Logger logger = LoggerFactory.getLogger(StudyTaskServiceImpl.class);
    private static Gson gson = GsonUtil.getGson();

    @Autowired
    private StudentTaskDao studentTaskDao;

    @Autowired
    private StudyLogDao studyLogDao;

    @Autowired
    private GroupStudentDao groupStudentDao;

    @Autowired
    private StudyTaskPushResDao studyTaskPushResDao;

    @Autowired
    private StudyLogQuestionDao studyLogQuestionDao;

    @Autowired
    private KnowledgeResource knowledgeResource;

    @Autowired
    private UserGroupService userGroupService;
    @Autowired
    private StudyPlanService studyPlanService;
    @Autowired
    private StudyLogService studyLogService;
    @Autowired
    private StudentTaskService studentTaskService;

    @Override
    public Collection<StudyTask> getStudyTasksByPid(Long pid, Long uid) throws DataAccessException {
        Collection<StudyTask> taskList = getStudyTasksByPidFromCache(pid);
        if (CollectionUtils.isEmpty(taskList)) {
            return null;
        }
        List<Long> tidList = new ArrayList<Long>();
        for (StudyTask task : taskList) {
            tidList.add(task.getId());
        }
        List<StudyTaskPushRes> pushList = studyTaskPushResDao.findListByTaskIdList(tidList, uid);
        Map<Long, Integer> weicourseMap = buildWeicourseMapByPushList(pushList);
        String tidsStr = EduStringUtils.getUniqueIds(tidList);
        Collection<StudentTask> sTaskList = dao.qryStudentTasksByTidsAndUid(tidsStr, uid);
        Map<Long, StudentTask> sTaskMap = buildTaskMapByList(sTaskList);
        for (StudyTask task : taskList) {
            StudentTask studentTask = sTaskMap.get(task.getId());
            if (studentTask != null) {
                task.setFinishState(studentTask.getState());
            } else {
                task.setFinishState(0);
            }
            Integer weiCount = weicourseMap.get(task.getId());
            if (weiCount != null) {
                task.setWeicosCount(weiCount);
            } else {
                task.setWeicosCount(0);
            }
        }
        return taskList;
    }

    /**
     * @param pushList
     * @return
     */
    private Map<Long, Integer> buildWeicourseMapByPushList(List<StudyTaskPushRes> pushList) {
        Map<Long, Integer> weicourseMap = new HashMap<Long, Integer>();
        for (StudyTaskPushRes pushRes : pushList) {
            Integer weiCount = weicourseMap.get(pushRes.getTaskId());
            if (weiCount == null) {
                weiCount = 0;
            }
            weicourseMap.put(pushRes.getTaskId(), weiCount + 1);
        }
        return weicourseMap;
    }

    private Map<Long, StudentTask> buildTaskMapByList(Collection<StudentTask> sTaskList) {
        Map<Long, StudentTask> sTaskMap = new HashMap<Long, StudentTask>();
        for (StudentTask sTask : sTaskList) {
            sTaskMap.put(sTask.getTaskId(), sTask);
        }
        return sTaskMap;
    }

    @Override
    public void studentAnswerSubmit(Long groupId, Long taskId, Long suid, Integer type, Integer answerType,
            Integer studyDuration, List<StudyReportDetail> answerDetail) throws DataAccessException,BusinessException {
        // 1、将根据taskId和uid查询student_task表，是否有记录，有则更新状态为2，没有则insert并返回自增的主键值
        StudentTask oldStudentTask = studentTaskDao.findLimitOneByTidAndUid(taskId, suid);
        Long studentTaskId = null; // 学生任务的id值
        if (oldStudentTask != null) {
            studentTaskId = oldStudentTask.getId();
            boolean updateResult = studentTaskDao.updateState(oldStudentTask.getId(), 2); // 状态修改为2（已提交）
            if (false == updateResult) {
                throw new DataAccessException(String.valueOf(Constants.SYS_ERROR), "updata state fail.");
            }
        } else {
            StudentTask temStudentTask = new StudentTask();
            temStudentTask.setTaskId(taskId);
            temStudentTask.setUid(suid);
            temStudentTask.setState(2);
            temStudentTask.setCreator(0l);
            temStudentTask.setCreateTime(new Date());
            temStudentTask.setModifyTime(new Date());
            studentTaskId = studentTaskDao.insertAndGetGenerateId(temStudentTask);
        }

        if (type == null) {
            type = 1;
        }
        // 2、将List循环插入到study_log表中
        int wrongNumAll = 0; // 记录总错题数，供第3步骤用
        List<StudyLog> studyLogList = new ArrayList<StudyLog>();
        List<Long> knowledgeIdList = new ArrayList<Long>();
        List<StudyTaskPushRes> studyTaskPushResList = new ArrayList<StudyTaskPushRes>();
        StudyTask studyTask = getStudyTaskById(taskId);
        UserGroup userGroup = userGroupService.getGroupById(groupId);
        for (StudyReportDetail studyReportDetail : answerDetail) {
            StudyLog tem = new StudyLog();
            tem.groupId = groupId;
            tem.planId = studyTask.getPlanId();
            tem.phaseId = studyTask.getPhaseId();
            tem.taskId = studyTask.getId();
            tem.categoryId = userGroup.getCategoryId();
            tem.stuTaskId = studentTaskId; // 学生任务id
            tem.uid = suid; // 学生ID
            tem.type = type; // 类型状态 0：主动学习 1：推送学习
            Long knowledgeId = studyReportDetail.getKnowledgeId();
            tem.knowledgeId = knowledgeId; //
            tem.answerNum = studyReportDetail.getAnswerNum(); //
            tem.answerIds = studyReportDetail.getAnswerIds();
            tem.wrongNum = studyReportDetail.getWrongNum(); //
            String wrongIds = studyReportDetail.getWrongIds();
            tem.wrongIds = wrongIds; //
            tem.studyDuration = studyDuration; // 学习时长
            tem.create_time = new Date(); // 创建时间
            tem.answerType = answerType;
            studyLogList.add(tem);

            wrongNumAll += studyReportDetail.getWrongNum();

            // 判断是否含有错题
            if (ValidateUtils.isEmpty(wrongIds) || studyReportDetail.getWrongNum() == 0) {
                continue;
            }
            StudyTaskPushRes studyTaskPushRes = new StudyTaskPushRes();
            studyTaskPushRes.setKnowledgeId(knowledgeId);
            studyTaskPushRes.setUid(suid);
            studyTaskPushRes.setTaskId(taskId);
            studyTaskPushResList.add(studyTaskPushRes);

            knowledgeIdList.add(knowledgeId);
        }
        // 保存日志记录具体问题
        saveStudyLogQuestion(studyLogList);

        // 微课学习不推送微课, 只有讲作业和段落作业才推微课
        if (type == 0 && answerType != null && (answerType == 0 || answerType == 1)) {
            // 插入微课相关信息
            saveWeiCourseInfo(studyTaskPushResList, knowledgeIdList);
        }

        // 3、如果type为（1：推送学习），且错题数累计大于0，则修改group_student的type为（1：问题用户，即题目考核不达标）
        if (1 == type && wrongNumAll > 0) {
            boolean updateSign = groupStudentDao.updateType(groupId, suid, 1);
            // 如果成功更新问题用户，发起问题用户消息通知
            if (updateSign && userGroup != null && userGroup.getCategoryId() != null && userGroup.getCategoryId() > 0) {
                String title = "新问题用户提醒";
                String content = "新问题用户提醒";
                boolean notifySign = knowledgeResource.addOaNotifyRecord(userGroup.getCategoryId(), 1l, title, content,
                        suid);
                logger.info("studentAnswerSubmit notifySign categoryId {} result {}", userGroup.getCategoryId(),
                        notifySign);
            }
        }
    }

    /**
     * @param studyLogList
     * @throws DataAccessException
     */
    private void saveStudyLogQuestion(List<StudyLog> studyLogList) throws DataAccessException {
        List<StudyLogQuestion> studyLogQuestionList = new ArrayList<StudyLogQuestion>();
        for (StudyLog slog : studyLogList) {
            studyLogDao.insert(slog);
            if (slog.getId() != null && slog.getId() > 0 && slog.getAnswerNum() > 0) {
                String[] wrongIdArray = null;
                if (slog.getWrongNum() > 0) {
                    wrongIdArray = gson.fromJson(slog.getWrongIds(), String[].class);
                    for (String wrongId : wrongIdArray) {
                        StudyLogQuestion logQuestion = new StudyLogQuestion(slog.getId(), slog.getUid(),
                                slog.getKnowledgeId(), Long.valueOf(wrongId));
                        logQuestion.setState(0);
                        studyLogQuestionList.add(logQuestion);
                    }
                }
                List<String> rightIdArray = filterRightQuestionId(slog.getAnswerIds(), wrongIdArray);
                for (String rightId : rightIdArray) {
                    StudyLogQuestion logQuestion = new StudyLogQuestion(slog.getId(), slog.getUid(),
                            slog.getKnowledgeId(), Long.valueOf(rightId));
                    logQuestion.setState(1);
                    studyLogQuestionList.add(logQuestion);
                }
            }
        }

        if (!CollectionUtils.isEmpty(studyLogQuestionList)) {
            studyLogQuestionDao.insertBatch(studyLogQuestionList);
        }
    }

    /**
     * @param answerIds
     * @return
     */
    private List<String> filterRightQuestionId(String answerIds, String[] wrongIdArray) {
        List<String> answerIdList = new ArrayList<String>(Arrays.asList(gson.fromJson(answerIds, String[].class)));
        if (wrongIdArray == null) {
            return answerIdList;
        }
        List<String> wrongIdList = new ArrayList<String>(Arrays.asList(wrongIdArray));
        List<String> rightIdList = new ArrayList<String>();
        for (String answerId : answerIdList) {
            if (!wrongIdList.contains(answerId)) {
                rightIdList.add(answerId);
            }
        }
        return rightIdList;
    }

    /**
     * 保存微课相关信息
     *
     * @param studyTaskPushResList
     * @param knowledgeIdList
     * @throws DataAccessException
     */
    @Override
    public void saveWeiCourseInfo(List<StudyTaskPushRes> studyTaskPushResList, List<Long> knowledgeIdList)
            throws DataAccessException {
        if (CollectionUtils.isEmpty(knowledgeIdList)) {
            return;
        }

        // 查询微课信息
        List<Knowledge> knowledgeList = null;
        if (knowledgeIdList.size() > 30) {
            List<List<Long>> spList = ListUtil.splitList(knowledgeIdList, 50);
            List<Knowledge> subKnowledgeList = new ArrayList<Knowledge>();
            for (List<Long> idList : spList) {
                subKnowledgeList = knowledgeResource.getWeiCoursesByKnowledgeIdList(idList);
                if (!CollectionUtils.isEmpty(subKnowledgeList)) {
                    if (knowledgeList == null) {
                        knowledgeList = new ArrayList<Knowledge>();
                    }
                    knowledgeList.addAll(subKnowledgeList);
                }
            }
        } else {
            knowledgeList = knowledgeResource.getWeiCoursesByKnowledgeIdList(knowledgeIdList);
        }
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        // 设置微课ID
        buildResInfo(studyTaskPushResList, knowledgeList);

        // 过滤没有resId的studyTaskPushRes
        List<StudyTaskPushRes> savedList = new ArrayList<StudyTaskPushRes>();
        for (StudyTaskPushRes studyTaskPushRes : studyTaskPushResList) {
            if (null == studyTaskPushRes.getResId()) {
                continue;
            }
            savedList.add(studyTaskPushRes);
        }
        if (CollectionUtils.isEmpty(savedList)) {
            return;
        }
        studyTaskPushResDao.insertPushResList(savedList);
    }

    /**
     * 设置微课ID
     *
     * @param studyTaskPushResList
     * @param knowledgeList
     */
    private void buildResInfo(List<StudyTaskPushRes> studyTaskPushResList, List<Knowledge> knowledgeList) {
        for (Knowledge knowledge : knowledgeList) {
            Collection<Resource> resources = knowledge.getResources();
            if (CollectionUtils.isEmpty(resources)) {
                continue;
            }
            Resource[] resourceArr = new Resource[resources.size()];
            resources.toArray(resourceArr);
            Resource firstResource = resourceArr[0];
            if (firstResource == null) {
                continue;
            }
            Long firstResourceId = firstResource.getId();
            for (StudyTaskPushRes studyTaskPushRes : studyTaskPushResList) {
                Long knowledgeId = studyTaskPushRes.getKnowledgeId();
                if (!knowledge.getId().equals(knowledgeId)) {
                    continue;
                }
                studyTaskPushRes.setResId(firstResourceId);
                break;
            }
        }
    }

    private final LoadingCache<Long, Collection<StudyTask>> studyTasksCache = CacheBuilder.newBuilder()
            .maximumSize(1000).expireAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Collection<StudyTask>>() {
                @Override
                public Collection<StudyTask> load(Long planId) throws Exception {
                    return getStudyTasksByPidFromDB(planId);
                }
            });

    private final LoadingCache<Long, StudyTask> studyTaskCache = CacheBuilder.newBuilder().maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES).build(new CacheLoader<Long, StudyTask>() {
                @Override
                public StudyTask load(Long id) throws Exception {
                    return dao.get(id);
                }
            });

    @Override
    public Collection<StudyTask> getStudyTasksByPidFromCache(Long planId) throws DataAccessException {
        if (planId == null || planId <= 0) {
            logger.error("getStudyTasksByPidFromCache error, parameter id:{}", planId);
            throw new DataAccessException("getStudyTasksByPidFromCache parameter id is null.");
        }
        Collection<StudyTask> result = null;
        try {
            result = studyTasksCache.getUnchecked(planId);
            if (result != null && !result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // ignore exception
        }
        return result;
    }

    private Collection<StudyTask> getStudyTasksByPidFromDB(Long planId) throws DataAccessException {
        return dao.qryStudyTasksByPid(planId);
    }

    @Override
    public StudyTask getStudyTaskById(Long id) throws DataAccessException {
        if (id == null || id <= 0) {
            logger.error("getStudyTaskById error, parameter id:{}", id);
            throw new DataAccessException("getStudyTaskById parameter id is null.");
        }
        StudyTask result = null;
        try {
            result = studyTaskCache.getUnchecked(id);
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            // ignore exception
        }
        return result;
    }

    @Override
    public void studyLogDataTransferAction() {
        try {
            Page<StudyLog> studyLogPage = studyLogService.find(new Page<StudyLog>(), new StudyLog());
            List<StudyLog> studyLogList = studyLogPage.getList();
            logger.info("studyLogList size = " + studyLogList.size());
//            System.out.println("studyLogList size = " + studyLogList.size());

            for (StudyLog log : studyLogList) {
                StudentTask studentTask = studentTaskService.get(log.getStuTaskId());
                if (studentTask != null && studentTask.getTaskId().longValue() > 0) {
                    log.taskId = studentTask.getTaskId();
                    StudyTask studyTask = getStudyTaskById(log.taskId);
                    if (studyTask != null && studyTask.getPlanId() != null && studyTask.getPlanId().longValue() > 0) {
                        log.planId = studyTask.getPlanId();
                        log.phaseId = studyTask.getPhaseId();
                        StudyPlan studyPlan = studyPlanService.getStudyPlanById(log.planId);
                        if (studyPlan != null && studyPlan.getGroupId() != null
                                && studyPlan.getGroupId().longValue() > 0) {
                            log.groupId = studyPlan.getGroupId();
                            log.categoryId = studyPlan.getCategoryId();
                        }
                    }
                    studyLogService.update(log);
                    List<StudyLogQuestion> studyLogQuestionList = new ArrayList<StudyLogQuestion>();
                    if (log.getId() != null && log.getId().longValue() > 0 && log.getAnswerNum() > 0) {
                        String[] wrongIdArray = null;
                        if (log.getWrongNum() > 0) {
                            wrongIdArray = gson.fromJson(log.getWrongIds(), String[].class);
                            for (String wrongId : wrongIdArray) {
                                StudyLogQuestion logQuestion = new StudyLogQuestion(log.getId(), log.getUid(),
                                        log.getKnowledgeId(), Long.valueOf(wrongId));
                                logQuestion.setState(0);
                                studyLogQuestionList.add(logQuestion);
                            }
                        }
                        List<String> rightIdArray = filterRightQuestionId(log.getAnswerIds(), wrongIdArray);
                        for (String rightId : rightIdArray) {
                            StudyLogQuestion logQuestion = new StudyLogQuestion(log.getId(), log.getUid(),
                                    log.getKnowledgeId(), Long.valueOf(rightId));
                            logQuestion.setState(1);
                            studyLogQuestionList.add(logQuestion);
                        }
                        if (!CollectionUtils.isEmpty(studyLogQuestionList)) {
                            studyLogQuestionDao.insertBatch(studyLogQuestionList);
                        }
                    }
                }
            }
        } catch (DataAccessException e) {
            logger.error("studyLogDataTransferAction error {}", e.getMessage());
        }
    }

    @Override
    public Boolean hasRightToViewResource(List<Long> taskIdList, Long resId) throws DataAccessException {
        Boolean rt = null;
        if (resId == null || resId.longValue() <= 0) {
            return rt;
        }
        Resource resource = knowledgeResource.getResourceById(resId);
        if (resource == null) {
            return rt;
        }
        rt = false;
        for (Long taskId : taskIdList) {
            StudyTask studyTask = getStudyTaskById(taskId);
            if (studyTask == null || studyTask.getCategoryId() == null) {
                continue;
            }
            if (resource.getCategoryId().equals(studyTask.getCategoryId())) {
                rt = true;
                break;
            }
        }
        return rt;
    }
}
