/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserBrushQuestionInfoDao;
import cn.huanju.edu100.study.model.questionBox.UserBrushQuestionInfo;
import cn.huanju.edu100.util.IdWorker;
import com.google.gson.Gson;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户刷题挑战记录DAO接口
 *
 * <AUTHOR>
 * @version 2017-09-11
 */
public class UserBrushQuestionInfoIbatisImpl extends CrudIbatisImpl2<UserBrushQuestionInfo> implements UserBrushQuestionInfoDao {

    final static IdWorker idworker=new IdWorker(0,5);
    public UserBrushQuestionInfoIbatisImpl() {
        super("UserBrushQuestionInfo");
    }

    @SuppressWarnings("unchecked")
    @Override
    public long insertSharding(UserBrushQuestionInfo entity) throws DataAccessException {
        if (entity == null) {
            logger.error("add {} error, parameter is null", namespace);
            throw new DataAccessException("add {} error,param is null", namespace);
        }
        SqlMapClient sqlMap = super.getShardingMaster();
        try {
            //CommonSelfIdGenerator keyGenerator = new CommonSelfIdGenerator();
            Number id = idworker.nextId();
            entity.setId(id.longValue());
            sqlMap.insert(namespace.concat(".insert"), entity);
            return entity.getId();
        } catch (SQLException e) {
            logger.error("insert {} SQLException.content:{}", namespace, (new Gson()).toJson(entity), e);
            throw new DataAccessException("add " + namespace + " SQLException fail.");
        }
//        SqlMapClient sqlMap = super.getMaster();
//        try {
//            int tbidx = (int) (entity.getUid() % 64);
//            entity.setTbidx(tbidx);
//            sqlMap.insert(namespace.concat(".insertOld"), entity);
//            return entity.getId();
//        } catch (SQLException e) {
//            logger.error("insert {} SQLException.content:{}", namespace, (new Gson()).toJson(entity), e);
//            throw new DataAccessException("add " + namespace + " SQLException fail.");
//        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateSharding(UserBrushQuestionInfo entity) throws DataAccessException {
        if (entity == null) {
            logger.error("update {} error, parameter is null.", namespace);
            return false;
        }
//        try {
//            SqlMapClient sqlMap = super.getMaster();
//            int tbidx = (int) (entity.getUid() % 64);
//            entity.setTbidx(tbidx);
//            int row = sqlMap.update(namespace.concat(".updateOld"), entity);
//            return row >= 1;
//        } catch (SQLException e) {
//            logger.error("update {} SQLException.content:{}", namespace, (new Gson()).toJson(entity), e);
//            throw new DataAccessException("update " + namespace + " SQLException fail.");
//        }

        try {
            SqlMapClient sqlMap = super.getShardingMaster();
            int row = sqlMap.update(namespace.concat(".update"), entity);
            return row >= 1;
        } catch (SQLException e) {
            logger.error("update {} SQLException.content:{}", namespace, (new Gson()).toJson(entity), e);
            throw new DataAccessException("update " + namespace + " SQLException fail.");
        }
    }
    @Override
    public UserBrushQuestionInfo getBrushInfoByUid(Long uid, Long boxId) throws DataAccessException {
        if (uid == null) {
            logger.error("list {} error, parameter uid is null,uid:{}", namespace, uid);
            throw new DataAccessException("list error,entity is null");
        }
        try {
            SqlMapClient sqlMap = super.getSlave();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("uid", uid);
            param.put("boxId",boxId);
            return (UserBrushQuestionInfo) sqlMap.queryForObject("UserBrushQuestionInfo.getBrushInfoByUid", param);
        } catch (SQLException e) {
            logger.error("list {} SQLException.uid:{}", namespace, uid, e);
            throw new DataAccessException("list SQLException error" + e.getMessage());
        }
    }

}
