package cn.huanju.edu100.study.model.onetoone;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 留学老师课酬标准Entity
 * <AUTHOR>
 * @version 2016-09-09
 */
public class VTeacherPay extends DataEntity<VTeacherPay> {
	
	private static final long serialVersionUID = 1L;
	private Long teacherId;		// 老师id
	private Long payId;		// 薪酬标准
	
	public VTeacherPay() {
		super();
	}

	public VTeacherPay(Long id){
		super(id);
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	
	public Long getPayId() {
		return payId;
	}

	public void setPayId(Long payId) {
		this.payId = payId;
	}
	
}