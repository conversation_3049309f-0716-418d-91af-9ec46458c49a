package cn.huanju.edu100.study.model;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 用户分表收藏记录Entity
 * <AUTHOR>
 * @version 2021-09-27
 */
public class UserCenterCollectQuestion extends DataEntity<UserCenterCollectQuestion> {
	
	private static final long serialVersionUID = 1L;
	private Long uid;		// 用户id
	private Long productId;		// 产品id
	private Long categoryId;		// 科目id
	private Long goodsId;		// 商品id
	private Long lessonId;		// 讲节id
	private Long questionId;		// 题目id
	private Long topicId;		// 子题id
	private Integer qtype;		// 题目类型 0：单选 1：多选(多个答案，全部选中得分)  2不定向选择(不少于1个答案，按选中的比例得分) 3、主观题
	private Integer sourceType;		// 题目来源 0:随堂练习 1:错题集 2:收藏夹
	private Integer productType;		// 产品类型
	private Integer searchQuestionModel; //刷题模式/题目来源：0-全部；1-课程配置；2-快题库
	private Integer filterDate;

	public Integer getFilterDate() {
		return filterDate;
	}

	public void setFilterDate(Integer filterDate) {
		this.filterDate = filterDate;
	}

	public Integer getSearchQuestionModel() {
		return searchQuestionModel;
	}

	public void setSearchQuestionModel(Integer searchQuestionModel) {
		this.searchQuestionModel = searchQuestionModel;
	}

	public UserCenterCollectQuestion() {
		super();
	}

	public UserCenterCollectQuestion(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}
	
	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}
	
	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}
	
	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
	
	public Long getLessonId() {
		return lessonId;
	}

	public void setLessonId(Long lessonId) {
		this.lessonId = lessonId;
	}
	
	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}
	
	public Long getTopicId() {
		return topicId;
	}

	public void setTopicId(Long topicId) {
		this.topicId = topicId;
	}
	
	public Integer getQtype() {
		return qtype;
	}

	public void setQtype(Integer qtype) {
		this.qtype = qtype;
	}
	
	public Integer getSourceType() {
		return sourceType;
	}

	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}
	
	public Integer getProductType() {
		return productType;
	}

	public void setProductType(Integer productType) {
		this.productType = productType;
	}
	
}