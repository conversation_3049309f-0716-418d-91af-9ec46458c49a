/**
 *
 */
package cn.huanju.edu100.study.dao.onetoone;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.onetoone.VRoom;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;

/**
 * 面授课室DAO接口
 * <AUTHOR>
 * @version 2016-04-12
 */
public interface VRoomDao extends CrudDao<VRoom> {

    List<VRoom> findListByIds(List<Long> ids) throws DataAccessException;

}
