package cn.huanju.edu100.study.model;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Date;
import java.util.List;

/**
 * 用户错题集Entity
 * <AUTHOR>
 * @version 2015-05-13
 */
public class UserErrorPaper extends DataEntity<UserErrorPaper> {

	private static final long serialVersionUID = 1L;
	private Long uid;		// uid
	private Long paperId;		// 试卷id
	private Double num;		// 用户得分
	private Date lastErrorTime;		// last_error_time

	private List<UserErrorPaperDetail> details;

	public UserErrorPaper() {
		super();
	}

	public UserErrorPaper(Long uid, Long paperId, Double num, Date lastErrorTime, List<UserErrorPaperDetail> details) {
		this.uid = uid;
		this.paperId = paperId;
		this.num = num;
		this.lastErrorTime = lastErrorTime;
		this.details = details;
	}

	public UserErrorPaper(Long id){
		super(id);
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getPaperId() {
		return paperId;
	}

	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}

	public Double getNum() {
		return num;
	}

	public void setNum(Double num) {
		this.num = num;
	}

	public Date getLastErrorTime() {
		return lastErrorTime;
	}

	public void setLastErrorTime(Date lastErrorTime) {
		this.lastErrorTime = lastErrorTime;
	}

	public List<UserErrorPaperDetail> getDetails() {
		return details;
	}

	public void setDetails(List<UserErrorPaperDetail> details) {
		this.details = details;
	}
}
