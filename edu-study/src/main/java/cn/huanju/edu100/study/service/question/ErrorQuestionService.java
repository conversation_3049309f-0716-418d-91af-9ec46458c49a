package cn.huanju.edu100.study.service.question;

import com.hqwx.study.dto.UserAutoRemoveErrorQuestionConfigDTO;
import com.hqwx.study.dto.UserCategoryHistoryErrorQuestionInfo;
import com.hqwx.study.dto.UserErrorQuestionToPdfTaskDTO;
import com.hqwx.study.dto.command.UserErrorQuestionToPdfTaskCommand;
import com.hqwx.study.dto.query.OpenAutoRemoveQuery;
import com.hqwx.study.dto.query.UserErrorQuestionToPdfTaskQuery;

import java.util.List;

public interface ErrorQuestionService {

    @Deprecated
    void openAutoRemoveErrorQuestion(OpenAutoRemoveQuery query);

    @Deprecated
    Long increaseRightTimes(Long uid, Long categoryId, Long topicId);

    @Deprecated
    void decreaseRightTimes(Long uid, Long categoryId, Long topicId);

    @Deprecated
    Boolean isAutoRemoveOpened(Long uid, Long categoryId);

    List<UserCategoryHistoryErrorQuestionInfo> haveHistoryErrorQuestion(Long uid, Long goodsId, List<Long> categoryIds);

    Boolean moveHistoryErrorQuestion(Long uid, Long goodsId, Long categoryId);

    List<Long> getHistoryErrorQuestion(Long uid, Long goodsId, Long categoryId);

    // 创建用户错题pdf生成任务
    UserErrorQuestionToPdfTaskDTO createUserErrorQuestionToPdfTask(UserErrorQuestionToPdfTaskCommand cmd) ;
    // 查询用户错题pdf生成任务的状态
    UserErrorQuestionToPdfTaskDTO getUserErrorQuestionToPdfTaskResult(UserErrorQuestionToPdfTaskQuery query);

    UserAutoRemoveErrorQuestionConfigDTO getUserAutoRemoveErrorQuestionConfig(Long uid);
}
