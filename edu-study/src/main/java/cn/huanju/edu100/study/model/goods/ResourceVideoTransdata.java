package cn.huanju.edu100.study.model.goods;

import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 视频转码数据Entity
 * 
 * <AUTHOR>
 * @version 2015-07-29
 */
public class ResourceVideoTransdata extends DataEntity<ResourceVideoTransdata> {

	private static final long serialVersionUID = 1L;
	private Long resId; // res_id
	private String resType; // res_type
	private String resClarity; // res_clarity
	private String tBucket; // t_bucket
	private String transData; // trans_data
	private Integer status; // status
	private String bak1; // bak1
	private String bak2; // bak2
	private String ip; // ip

	public ResourceVideoTransdata() {
		super();
	}

	public ResourceVideoTransdata(Long id) {
		super(id);
	}

	public Long getResId() {
		return resId;
	}

	public void setResId(Long resId) {
		this.resId = resId;
	}

	public String getResType() {
		return resType;
	}

	public void setResType(String resType) {
		this.resType = resType;
	}

	public String getResClarity() {
		return resClarity;
	}

	public void setResClarity(String resClarity) {
		this.resClarity = resClarity;
	}

	public String getTBucket() {
		return tBucket;
	}

	public void setTBucket(String tBucket) {
		this.tBucket = tBucket;
	}

	public String getTransData() {
		return transData;
	}

	public void setTransData(String transData) {
		this.transData = transData;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getBak1() {
		return bak1;
	}

	public void setBak1(String bak1) {
		this.bak1 = bak1;
	}

	public String getBak2() {
		return bak2;
	}

	public void setBak2(String bak2) {
		this.bak2 = bak2;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

}