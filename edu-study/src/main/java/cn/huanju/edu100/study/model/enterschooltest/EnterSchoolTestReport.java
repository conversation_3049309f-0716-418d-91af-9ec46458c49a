package cn.huanju.edu100.study.model.enterschooltest;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.List;

/**
 *
 * 入学测评报告 接口响应dto
 * <AUTHOR>
 * @date 2021-04-27 11:53:01
 */
public class EnterSchoolTestReport extends DataEntity<EnterSchoolTestReport> {
    private static final long serialVersionUID = 1L;
    /**
     * uid
     */
    private Long uid;
    private Long paperId;
    private Long categoryId;
    private List<EnterSchoolTestKnowledgeRoport> knowledgeRoports;


    /**
     * 用户答题id
     */
    private Long userAnswerId;

    private Integer questionTotal;
    private Integer rightQuestionTotal;
    private Integer totalTimes;
    private String studyAdvice;
    private Long goodsId;

    private List<Long> allQuestionIds;
    private List<Long> wrongQuestionIds;
    private List<Long> rightQuestionIds;
    private List<Long> noQuestionIds;
    private List<Long> allTopicIds;
    private List<Long> wrongTopicIds;
    private List<Long> rightTopicIds;
    private List<Long> noTopicIds;

    public List<Long> getNoQuestionIds() {
        return noQuestionIds;
    }

    public void setNoQuestionIds(List<Long> noQuestionIds) {
        this.noQuestionIds = noQuestionIds;
    }

    public List<Long> getRightQuestionIds() {
        return rightQuestionIds;
    }

    public void setRightQuestionIds(List<Long> rightQuestionIds) {
        this.rightQuestionIds = rightQuestionIds;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public List<Long> getAllQuestionIds() {
        return allQuestionIds;
    }

    public void setAllQuestionIds(List<Long> allQuestionIds) {
        this.allQuestionIds = allQuestionIds;
    }

    public List<Long> getWrongQuestionIds() {
        return wrongQuestionIds;
    }

    public void setWrongQuestionIds(List<Long> wrongQuestionIds) {
        this.wrongQuestionIds = wrongQuestionIds;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public List<EnterSchoolTestKnowledgeRoport> getKnowledgeRoports() {
        return knowledgeRoports;
    }

    public void setKnowledgeRoports(List<EnterSchoolTestKnowledgeRoport> knowledgeRoports) {
        this.knowledgeRoports = knowledgeRoports;
    }

    public Long getUserAnswerId() {
        return userAnswerId;
    }

    public void setUserAnswerId(Long userAnswerId) {
        this.userAnswerId = userAnswerId;
    }

    public Integer getQuestionTotal() {
        return questionTotal;
    }

    public void setQuestionTotal(Integer questionTotal) {
        this.questionTotal = questionTotal;
    }

    public Integer getRightQuestionTotal() {
        return rightQuestionTotal;
    }

    public void setRightQuestionTotal(Integer rightQuestionTotal) {
        this.rightQuestionTotal = rightQuestionTotal;
    }

    public Integer getTotalTimes() {
        return totalTimes;
    }

    public void setTotalTimes(Integer totalTimes) {
        this.totalTimes = totalTimes;
    }

    public String getStudyAdvice() {
        return studyAdvice;
    }

    public void setStudyAdvice(String studyAdvice) {
        this.studyAdvice = studyAdvice;
    }

    public List<Long> getAllTopicIds() {
        return allTopicIds;
    }

    public void setAllTopicIds(List<Long> allTopicIds) {
        this.allTopicIds = allTopicIds;
    }

    public List<Long> getWrongTopicIds() {
        return wrongTopicIds;
    }

    public void setWrongTopicIds(List<Long> wrongTopicIds) {
        this.wrongTopicIds = wrongTopicIds;
    }

    public List<Long> getRightTopicIds() {
        return rightTopicIds;
    }

    public void setRightTopicIds(List<Long> rightTopicIds) {
        this.rightTopicIds = rightTopicIds;
    }

    public List<Long> getNoTopicIds() {
        return noTopicIds;
    }

    public void setNoTopicIds(List<Long> noTopicIds) {
        this.noTopicIds = noTopicIds;
    }
}
