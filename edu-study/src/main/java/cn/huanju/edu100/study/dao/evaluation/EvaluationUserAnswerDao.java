package cn.huanju.edu100.study.dao.evaluation;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.study.model.evaluation.EvaluationUserAnswer;
import com.hqwx.study.dto.EvaluationUserAnswerDTO;
import com.hqwx.study.dto.query.EvaluationUserAnswerQuery;

import java.util.List;

public interface EvaluationUserAnswerDao extends CrudDao<EvaluationUserAnswer> {


    void insertBatch(List<EvaluationUserAnswer> list) throws DataAccessException;

    Integer getEvaluationUserAnswerCount(EvaluationUserAnswerQuery query) throws DataAccessException;

    List<EvaluationUserAnswerDTO> getEvaluationUserAnswerList(EvaluationUserAnswerQuery query) throws DataAccessException;
}