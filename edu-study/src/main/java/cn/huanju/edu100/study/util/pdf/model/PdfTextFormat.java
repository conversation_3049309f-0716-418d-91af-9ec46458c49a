package cn.huanju.edu100.study.util.pdf.model;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: PaperTextFormat
 * @Author: xush<PERSON><EMAIL>
 * @Date: 2024-08-16
 * @Ver: v1.0 -create
 */
public enum PdfTextFormat {
    //试卷中，各中文本的格式
    Plain(1, "纯文本"),
    RichHtml(2, "html富文本"),
    HttpJs(3, "js"),
    ;
    
    public static PdfTextFormat getByText(String text) {
        if (StringUtils.isBlank(text)) {
            return Plain;
        }

        final String JsSuffix = ".js";
        if (text.startsWith("http") && text.endsWith(JsSuffix)) {
            return HttpJs;
        }
        if (isRichText(text)) {
            return RichHtml;
        }

        return Plain;
    }
    private static boolean isRichText(String text) {
        String regex = "<[^>]*>";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        return matcher.find();
    }

    public Integer Type() {
        return type;
    }
    public String Desc() {
        return desc;
    }

    private final Integer type;
    private final String desc;

    PdfTextFormat(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
