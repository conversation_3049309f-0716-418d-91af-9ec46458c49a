/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.tutor;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.tutor.TutorTaskLiveDao;
import cn.huanju.edu100.study.model.tutor.TutorTaskLive;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 直播任务DAO接口
 * <AUTHOR>
 * @version 2018-01-16
 */
public class TutorTaskLiveIbatisImpl extends CrudIbatisImpl2<TutorTaskLive> implements
		TutorTaskLiveDao {

	public TutorTaskLiveIbatisImpl() {
		super("TutorTaskLive");
	}

	@Override
	public List<TutorTaskLive> getByIdList(List<Long> liveTaskIdList) throws DataAccessException {
		if (CollectionUtils.isEmpty(liveTaskIdList)) {
			return null;
		}

		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("taskIdList", liveTaskIdList);

			SqlMapClient sqlMap = super.getSlave();
			return (List<TutorTaskLive>) sqlMap
					.queryForList("TutorTaskLive.findList", params);
		} catch (SQLException e) {
			logger.error("getByIdList SQLException.idList:{}", liveTaskIdList, e);
			throw new DataAccessException("getByIdList SQLException error");
		}
	}
}
