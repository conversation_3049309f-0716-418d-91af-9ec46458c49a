package cn.huanju.edu100.study.task;

import cn.huanju.edu100.study.service.UserAnswerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 增量-（产品排课、新课程表排课）补充作业id
 */
@Service
public class SyncHomeworkIdIncreVideoCourseAndProductScheduleJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SyncHomeworkIdIncreVideoCourseAndProductScheduleJobHandler.class);
    @Autowired
    private UserAnswerService userAnswerService;

    @XxlJob("SyncHomeworkIdIncreVideoCourseAndProductScheduleJobHandler")
    public ReturnT<String> execute(String param) throws Exception {
        int zoneIndex = XxlJobHelper.getShardIndex();//分片
        int zoneTotal = XxlJobHelper.getShardTotal();
        logger.info("SyncHomeworkIdIncreVideoCourseAndProductScheduleJobHandler 分片参数：当前分片序号 = {}, 总分片数 = {}", zoneIndex, zoneTotal);
        if (zoneIndex == 0) {
            logger.info("------SyncHomeworkIdIncreVideoCourseAndProductScheduleJobHandler start------");
            Boolean rs = userAnswerService.syncHomeworkIdIncreVideoCourseAndProductSchedule();
            userAnswerService.setUserAnswerHomeworkMaxIdInRedis();
            logger.info("------SyncHomeworkIdIncreVideoCourseAndProductScheduleJobHandler end------rs:{}", rs);
        }
        return ReturnT.SUCCESS;
    }

}
