package cn.huanju.edu100.study.service.onetoone;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.onetoone.VResource;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 留学资源Service
 *
 * <AUTHOR>
 * @version 2016-12-12
 */
public interface VResourceService extends BaseService<VResource> {


    List<VResource> findListByParam(Map<String, Object> params) throws DataAccessException;

    Integer findCountByParam(Map<String, Object> params) throws DataAccessException;

    Map<Long, VResource> getResourceMapByIds(Set<Long> ids) throws DataAccessException;
}
