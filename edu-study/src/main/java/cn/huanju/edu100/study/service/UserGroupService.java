package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.GroupTeacher;
import cn.huanju.edu100.study.model.UserGroup;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.Collection;

/**
 * 用户分组Service
 *
 * <AUTHOR>
 * @version 2015-05-12
 */
public interface UserGroupService extends BaseService<UserGroup> {

    Collection<UserGroup> getGroupsByUid(long uid) throws DataAccessException;

    UserGroup getGroupById(Long id) throws DataAccessException;

    Collection<GroupTeacher> getGroupTeachers(Long groupId) throws DataAccessException;

}
