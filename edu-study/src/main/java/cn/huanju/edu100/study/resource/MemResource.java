/*
 * Copyright (c) 2011 duowan.com.
 * All Rights Reserved.
 * This program is the confidential and proprietary information of
 * duowan. ("Confidential Information").  You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with duowan.com.
 */
package cn.huanju.edu100.study.resource;

import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.thrift.response;
import cn.huanju.edu100.util.GsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Service
public class MemResource {

    @Autowired
    private IGenericThriftResource genericThriftResource;
    private static final Logger LOG = LoggerFactory.getLogger(MemResource.class);


    /**
     * 积分上报
     *
     * @param uid
     * @param taskId
     * @return
     */
    public Integer notifyCreditTask(Long uid, Integer taskId, Map<String, Object> param,Long schId) {
        if (uid == null || uid <= 0 || taskId == null || taskId <= 0) {
            return null;
        }
        if(param == null) {
            param = new HashMap<String, Object>();
        }
        param.put("uid", uid);
        param.put("taskId", taskId);
        try {
            LOG.info("[notifyCreditTask] start, param is:{}", GsonUtil.toJson(param));
            response response = genericThriftResource.generalMemThriftMethodInvoke(GsonUtil.toJson(param), 1,
                    Consts.Code.CLIENT_IP, "mem_notifyCreditTask",schId);
            LOG.info("[notifyCreditTask] response:{}", response.getMsg());
            if (response != null) {
                return response.getCode();
            }
        } catch (Exception e) {
            LOG.error("[notifyCreditTask] error param:{},", GsonUtil.toJson(param), e);
        }
        return null;
    }

}
