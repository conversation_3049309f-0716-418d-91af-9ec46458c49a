package cn.huanju.edu100.study.model;


import cn.huanju.edu100.persistence.model.DataEntity;

/**
 * 用户中心任务完成情况Entity
 * <AUTHOR>
 * @version 2017-03-20
 */
public class CTaskResult extends DataEntity<CTaskResult> {

	private static final long serialVersionUID = 1L;
	private Long modelId;		// model_id
	private Long groupId;		// group_id
	private Long phaseId;		// phase_id
	private Long sectionId;		// section_id
	private Long productId;		// product_id
	private Long taskId;		// 产品对应的下一级
	private String name;		// 任务名称
	private Long uid;		// uid
	private Integer type;		// type
	private Integer complete;		// 0：未完成， 1：进行中， 2：已完成

	private String taskJson; //任务实体json字符串

	public CTaskResult() {
		super();
	}

	public CTaskResult(Long modelId, Long groupId, Long phaseId, Long sectionId, Long productId, Long taskId, Long uid,
            Integer type, Integer complete) {
        super();
        this.modelId = modelId;
        this.groupId = groupId;
        this.phaseId = phaseId;
        this.sectionId = sectionId;
        this.productId = productId;
        this.taskId = taskId;
        this.uid = uid;
        this.type = type;
        this.complete = complete;
    }

    public CTaskResult(Long id){
		super(id);
	}

	public Long getModelId() {
		return modelId;
	}

	public void setModelId(Long modelId) {
		this.modelId = modelId;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public Long getPhaseId() {
		return phaseId;
	}

	public void setPhaseId(Long phaseId) {
		this.phaseId = phaseId;
	}

	public Long getSectionId() {
		return sectionId;
	}

	public void setSectionId(Long sectionId) {
		this.sectionId = sectionId;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getComplete() {
		return complete;
	}

	public void setComplete(Integer complete) {
		this.complete = complete;
	}

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTaskJson() {
        return taskJson;
    }

    public void setTaskJson(String taskJson) {
        this.taskJson = taskJson;
    }

	/**
	 * 目前是状态不一致才保存
	 * @param cTaskResult
	 * @return
	 */
	public boolean bNeedSave(final CTaskResult cTaskResult){
		return !this.getComplete().equals(cTaskResult.getComplete());
	}
}
