package cn.huanju.edu100.study.service;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.UserErrorQuestion;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Map;

/**
 * 用户试卷题目相关Service
 * <AUTHOR>
 * @version 2015-05-17
 */
public interface UserQuestionService extends BaseService<UserErrorQuestion> {
    void saveErrorQuestion(UserErrorQuestion errorQuestion) throws DataAccessException;

	void removeErrorQuestion(UserErrorQuestion errorQuestion)
			throws DataAccessException;

	Integer removeUserErrorQuestionList(List<UserErrorQuestion> errorList) throws DataAccessException;

	List<UserErrorQuestion> findHomeworkErrorQuestionList(Map<String,Object> params) throws DataAccessException;

	void transferUserErrorQuestion();
}