package cn.huanju.edu100.study.service.tutor;

import cn.huanju.edu100.persistence.service.BaseService;
import cn.huanju.edu100.study.model.tutor.TutorTeacher;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.List;
import java.util.Set;

/**
 * 班主任Service
 * <AUTHOR>
 * @version 2016-01-12
 */
public interface TutorTeacherService extends BaseService<TutorTeacher> {

    TutorTeacher getTeacherByUid(Long uid, String classes) throws DataAccessException;

    List<TutorTeacher> findListByTuids(Set<Long> tuidSet) throws DataAccessException;

}
