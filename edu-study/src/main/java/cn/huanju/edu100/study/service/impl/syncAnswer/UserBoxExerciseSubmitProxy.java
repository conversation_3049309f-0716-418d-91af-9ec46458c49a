package cn.huanju.edu100.study.service.impl.syncAnswer;

import cn.huanju.edu100.study.dao.UserAnswerDetailDao;
import cn.huanju.edu100.study.dao.UserAnswerSumDao;
import cn.huanju.edu100.study.dao.UserHomeWorkAnswerDao;
import cn.huanju.edu100.study.model.*;
import cn.huanju.edu100.study.model.goods.Product;
import cn.huanju.edu100.study.resource.GoodsResource;
import cn.huanju.edu100.study.service.*;
import cn.huanju.edu100.study.util.Consts;
import cn.huanju.edu100.study.util.IdUtils;
import cn.huanju.edu100.study.util.ThreadPoolFactoryUtil;
import cn.huanju.edu100.util.Collections3;
import cn.huanju.edu100.util.GsonUtil;
import cn.huanju.edu100.exception.DataAccessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hqwx.study.entity.UserAnswer;
import com.hqwx.study.entity.UserAnswerDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.*;

/**
 * 用户题库练习提交proxy
 *
 * */
@Component
public class UserBoxExerciseSubmitProxy{
	private Logger logger = LoggerFactory.getLogger(getClass());

	private static int numOfThreads = 2;// 默认的线程数
	private static int cpuNums = Runtime.getRuntime().availableProcessors();
	private static ExecutorService executor = ThreadPoolFactoryUtil.createDefaultPool( "userBoxExerciseSubmitProxyThreadPool");
	public final static int cachedQueueMaxSize = 6 * 10000; // 缓存待入库的学生答题队列的最大size

	public int batchNum = 1000; // 默认每次批量get的记录条数
	public long period = 1000; // 默认每次批量get的最长时间周期(单位为毫秒)

	public static LinkedBlockingQueue<UserExerciseCachedBean> userExercise_Queue = new LinkedBlockingQueue<UserExerciseCachedBean>(
			cachedQueueMaxSize);

	@Autowired
	private UserAnswerSumDao userAnswerSumDao;
    @Autowired
    private UserHomeWorkAnswerDao userHomeWorkAnswerDao;
	@Autowired
	private UserAnswerDetailDao userAnswerDetailDao;
	@Autowired
	private UserQuestionService userQuestionService;
	@Autowired
	private QuestionAnswerStaticsService questionAnswerStaticsService;
	@Autowired
	private UserQuestionBoxService userQuestionBoxService;
	@Autowired
	private UserQuestionBoxSubmitService userQuestionBoxSubmitService;
	@Autowired
	private UserSubErrorQuestionService userSubErrorQuestionService;
	@Resource
	private GoodsResource goodsResource;

	private static ExecutorService es = ThreadPoolFactoryUtil.createDefaultPool("syncQuestionLogThreadPool");


//	@Override
	@Scheduled(cron = "0/3 * * * * ?")
	public void process() {//每3s钟把队列里的batchNum条提交数据取出来执行
		if (!userExercise_Queue.isEmpty()) {
			long startTime = System.currentTimeMillis(); // 本次批量get的开始时间

			while(true){
				List<UserExerciseCachedBean> list = new ArrayList<UserBoxExerciseSubmitProxy.UserExerciseCachedBean>();
				List<Future<?>> futureList = new ArrayList<>();

				userExercise_Queue.drainTo(list, batchNum);
				for (UserExerciseCachedBean userExerciseCachedBean : list) {
                    logger.info("userExerciseCachedBean process, uid:{},userHomeWorkId:{},boxId:{},bookId:{},questionIds:{}",
							userExerciseCachedBean.getUid(),userExerciseCachedBean.getUserHomeWorkId(),userExerciseCachedBean.getUserExerciseAnswer().getBoxId(),
							userExerciseCachedBean.getUserExerciseAnswer().getTeachBookId(),
                            GsonUtil.toJson(Collections3.extractToList(userExerciseCachedBean.getQuestions(),"id")));
					Future<?> result=  executor.submit(userExerciseCachedBean);
					futureList.add(result);
				}
				int sucCount = 0;
				int failCount = 0;
				for(Future<?> result : futureList){
					try {
						result.get(60, TimeUnit.SECONDS);
						sucCount++;
					}catch (Exception e){
						logger.error("submitExercise userExerciseCachedBean process submit exception:",e);
						failCount++;
					}
				}
				logger.info("submitExercise userExerciseCachedBean process submit success:{} failed:{}", sucCount, failCount);

				long endTime = System.currentTimeMillis();
				if (userExercise_Queue.isEmpty() || (endTime - startTime) >= period) {
					break;
				}
			}
			logger.info("userExercise_Queue after process size:{}",userExercise_Queue.size());
		}
	}

	public void submitExercise(UserExerciseAnswer userExerciseAnswer, List<Question> questions, Integer source) {
		try {
			userExercise_Queue.put(new UserExerciseCachedBean(userExerciseAnswer.getUid(),userExerciseAnswer.getId(),userExerciseAnswer,questions,source));
		} catch (InterruptedException e) {
			logger.error("submitExercise syn2Queue InterruptedException .",e);
		}
	}

	class UserExerciseCachedBean implements Callable<Void>{
		private Long uid;
		private Long userHomeWorkId;

		private UserExerciseAnswer userExerciseAnswer;
		private List<Question> questions;
		private Integer source;

		public UserExerciseCachedBean(Long uid,Long userHomeWorkId,UserExerciseAnswer userExerciseAnswer,List<Question> questions,Integer source) {
			this.uid = uid;
			this.userHomeWorkId = userHomeWorkId;
			this.userExerciseAnswer = userExerciseAnswer;
			this.questions = questions;
			this.source = source;
		}

		public Long getUid() {
			return uid;
		}
		public void setUid(Long uid) {
			this.uid = uid;
		}
		public Long getUserHomeWorkId() {
			return userHomeWorkId;
		}
		public void setUserHomeWorkId(Long userHomeWorkId) {
			this.userHomeWorkId = userHomeWorkId;
		}
		public UserExerciseAnswer getUserExerciseAnswer() {
			return userExerciseAnswer;
		}

		public void setUserExerciseAnswer(UserExerciseAnswer userExerciseAnswer) {
			this.userExerciseAnswer = userExerciseAnswer;
		}
		public List<Question> getQuestions() {
			return questions;
		}
		public void setQuestions(List<Question> questions) {
			this.questions = questions;
		}

		/**
		 * 题库做题的判断正误规则：
		 * 设题目的小题数为T，未作答数为E，做错数为W，做对数为R。
			当 E==0，W==0，T==R，全作答且全对，该题进已做，不进错题
			当 T==E时，此时W=0，R=0，不进已做&&不进错题
			当 E > 0，T != E，R != 0 ，存在已做对和未做答，则该题进已做&&进错题
			当 E > 0，T != E，W != 0，存在已做错和未作答，则该题进已做&&进错题
		 * */
		@Override
		public Void call() throws Exception {
		    logger.info("start execute userExerciseCachedBean uid:{} userHomeWorkId:{}", uid, userHomeWorkId);
			try {
				executeUserExerciseCachedBean();
			}catch (Exception e){
				logger.error("execute userExerciseCachedBean uid:{} userHomeWorkId:{} error", uid, userHomeWorkId, e);
			}

			logger.info("end execute userExerciseCachedBean uid:{} userHomeWorkId:{}", uid, userHomeWorkId);
			return null;
		}

		private void executeUserExerciseCachedBean() throws DataAccessException {
			List<QuestionAnswerDetail> questionAnswerDetailList = new ArrayList<QuestionAnswerDetail>();
			Collection<UserAnswerDetail> answerDetails = userExerciseAnswer.getAnswerDetail();
			Long boxId = userExerciseAnswer.getBoxId();
			Long teachBookId = userExerciseAnswer.getTeachBookId();

			HashSet<Long> doneQIds = new HashSet<Long>();//已做题集合(当且仅当存在一道小题是已作答)
			HashSet<Long> wrongQIds = new HashSet<Long>();//已做做错的题集合（但凡有一道小题做错或未做，并且若有未做的小题，未做的小题数 "小于" 总小题数）
			HashSet<Long> wrongTopicIds = new HashSet<Long>();
			HashSet<Long> rightQIds = new HashSet<Long>();//已做做对的题集合

			for (Question question : questions) {
				//初始化答题记录Bean
				QuestionAnswerDetail questionAnswerDetail = new QuestionAnswerDetail();
				questionAnswerDetail.setUid(userExerciseAnswer.getUid());
				questionAnswerDetail.setQuestionId(question.getId());
				questionAnswerDetail.setSchId(1L);
				questionAnswerDetail.setAnswerTime(new Date());
				Integer isQuestionRight = UserAnswerDetail.IsRight.RIGHT;//初始状态为2-正确
				int errCount = 0;

//	        	Integer isMulti = question.getIsMulti();

				List<UserAnswerSum> oldAnswerSums = findByUserHomeworkIdWithDetails(uid, userExerciseAnswer.getId(), question.getId());
				UserAnswerSum answerSum;
				if (oldAnswerSums.size() == 0) {
					answerSum = new UserAnswerSum();
					answerSum.setUid(userExerciseAnswer.getUid());
					answerSum.setUserHomeworkId(userExerciseAnswer.getId());
					answerSum.setQuestionId(question.getId());
					answerSum.setScore(0d);
					answerSum.setState(UserAnswerSum.State.DONE);

//                    Long id = userAnswerSumDao.insert(answerSum);
//                    answerSum.setId(id);// TODO:灰度期间两边都插入
					userAnswerSumDao.insertSharding(answerSum, answerSum.getClass());
				} else {
					answerSum = oldAnswerSums.get(0);
				}
				Collection<QuestionTopic> topics = question.getTopicList();
				if (CollectionUtils.isNotEmpty(topics)) {
					double sumTotalScore = 0;
					int emptyCount = 0;//记录一道大题里未作答的小题数量
					List<Long> emptyTopicList = new ArrayList<Long>();

					for (QuestionTopic topic : topics) {
						UserAnswerDetail answerDetail = null;
						for (UserAnswerDetail answerDetailItem : answerDetails) {
							if (topic.getId().equals(answerDetailItem.getTopicId())) {
								answerDetail = answerDetailItem;
								break;
							}
						}
						if (answerDetail == null) {
							continue;
						}

						if (answerDetail.getAnswer() == null || answerDetail.getAnswer().length ==0
								|| answerDetail.getIsRight().intValue() == UserAnswerDetail.IsRight.NOT_ANSWER) {
							emptyCount++;
						}

						answerDetail.setAnswerStr(GsonUtil.toJson(answerDetail.getAnswer()));
						int isRight = answerDetail.getIsRight();
						Double score = answerDetail.getScore();
						answerDetail.setUid(userExerciseAnswer.getUid());
						answerDetail.setSumId(answerSum.getId());
						sumTotalScore += score;

						if (answerDetail.getId() != null && answerDetail.getId()!=0) {
							userAnswerDetailDao.updateSharding(answerDetail);
						} else {
							answerDetail.setId(null);
//                            Long id = userAnswerDetailDao.insert(answerDetail);
//                            answerDetail.setId(id);// TODO:灰度期间两边都插入
							userAnswerDetailDao.insertSharding(answerDetail, answerDetail.getClass());
						}

						answerDetail.setUserHomeworkId(userExerciseAnswer.getId());

						//update by linyl 20160301
						//update by linyl 20160819,未作答的题目，既不进已做集合也不进错题集合。
						if ((isRight != UserAnswerDetail.IsRight.RIGHT)) {//只要有其中一个小题答错则为半对,并且答错数累加
							if (isRight != UserAnswerDetail.IsRight.NOT_ANSWER) {
								wrongTopicIds.add(topic.getId());
								errCount++;
								isQuestionRight = UserAnswerDetail.IsRight.HALF_RIGHT;
								try{
									//记录用户做错的题目
									addUserSubErrorQuestion(answerDetail,topic.getQtype(), userExerciseAnswer);
								}catch (Exception e){
									logger.error("addUserSubErrorQuestion error.uid:"+uid+",userHomeWorkId:"+userHomeWorkId,e);
								}
							}else if (isRight == UserAnswerDetail.IsRight.NOT_ANSWER) {
								emptyTopicList.add(topic.getId());
							}
						}
					}

					if (emptyCount == 0 && errCount == 0) {//全作答且全作对
						rightQIds.add(question.getId());
						doneQIds.add(question.getId());
					}else if (emptyCount == topics.size() && errCount == 0) {//全部未作答
						// 不做处理
						isQuestionRight = UserAnswerDetail.IsRight.NOT_ANSWER;
					}else if (errCount > 0 || (emptyCount > 0 && emptyCount < topics.size())) {//有未作答，也有已作答
						if (errCount == 0) {//没有做错的，但有未作答和已做对，则认为该题作答错误
							wrongTopicIds.addAll(emptyTopicList);
						}
						wrongQIds.add(question.getId());
						doneQIds.add(question.getId());

						if (errCount == topics.size()) {
							isQuestionRight = UserAnswerDetail.IsRight.WRONG;
						}
					}

					//未作答的小题数为0，或者 未作答的小题数小于总的小题数===>存在已作答的小题
//	                if(emptyCount == 0 || emptyCount < topics.size()){
//	                	//满足以上条件，均判定为已作答
//	                	doneQIds.add(question.getId());
//	                }
//
//	                //有错题 && 未作答的题目数!=子题数
////	                if (errCount > 0 && emptyCount!= topics.size()) {
//	                //update by linyl 20160301
//	                if (errCount > 0 || (emptyCount > 0 && emptyCount!= topics.size())) {//有错题 || 有未作答的小题，且未作答的小题数不等于总小题数
//	                	wrongQIds.add(question.getId());
//					}else if(errCount==0 && emptyCount ==0){//记录做对的题，既没有做错的小题，也没有未作答的小题
//						rightQIds.add(question.getId());
//					}

					if (isQuestionRight != UserAnswerDetail.IsRight.NOT_ANSWER) {
						questionAnswerDetail.setState(isQuestionRight);
						questionAnswerDetailList.add(questionAnswerDetail);
					}

					if (answerSum.getScore().doubleValue() != sumTotalScore /**|| (emptyCount > 0)*/ ) {
						//重新算分，每次提交都假定前台把每个小题的id都传过来了
						answerSum.setScore(sumTotalScore);

//	                    if (emptyCount > 0) {
//	                    	answerSum.setState(UserAnswerSum.State.DOING);
//						}
						userAnswerSumDao.updateSharding(answerSum);
					}
				}
			}
			Long oldBookId = null;
			Long newBookId = null;
			if (userExerciseAnswer.getIsNewBook() == 1) {
				newBookId = teachBookId;
				TeachingBook oldBook = userQuestionBoxService.getTeachBookByBoxId(boxId);
				if (oldBook != null) {
					oldBookId = oldBook.getId();
				}
			} else {
				oldBookId = teachBookId;
			}
			try{
				// 首先更新用户做过的题目历史记录
				userQuestionBoxService.refreshUserHisQuestionInfo(uid,answerDetails,questions);
			}catch (Exception e){
				logger.error("refreshUserHisQuestionInfo error.uid:"+uid+",userHomeWorkId:"+userHomeWorkId,e);
			}
			try{
				// 然后将本次作答的题目更新到用户最近一次作答的缓存中
				userQuestionBoxService.refreshUserLatestAnswerQId(uid, boxId, questions);
			}catch (Exception e){
				logger.error("refreshUserLatestAnswerQId error.uid:"+uid+",userHomeWorkId:"+userHomeWorkId,e);
			}
			try{
				// 其次记录用户做过的题目
				userQuestionBoxSubmitService.cachePaperDoneQuestion(uid, oldBookId, boxId, new ArrayList<Long>(doneQIds));
			}catch (Exception e){
				logger.error("cacheUserBoxDoneQuestion error.uid:"+uid+",userHomeWorkId:"+userHomeWorkId,e);
			}
			try{
				// 最后记录用户做错的题目
				userQuestionBoxSubmitService.cachePaperWrongQuestion(uid,  oldBookId, boxId, new ArrayList<Long>(wrongQIds), new ArrayList<Long>(wrongTopicIds));
			}catch (Exception e){
				logger.error("cacheUserBoxWrongQuestion error.uid:"+uid+",userHomeWorkId:"+userHomeWorkId,e);
			}
			//tiku4.6 错题本出入规则修改
			try{
				if (source == 1) {
					// 根据用户本次做对的题目，移除掉用户曾经做错的错题。
					userQuestionBoxService.removeUserWrongQuestion(uid, boxId, new ArrayList<Long>(rightQIds), true);
				}

			}catch (Exception e){
				logger.error("removeUserWrongQuestion error.uid:"+uid+",userHomeWorkId:"+userHomeWorkId,e);
			}

			try{
				// 记录周用户做题目情况，用于做题报告
				userQuestionBoxService.cacheWeekQuestion(uid, userExerciseAnswer.getObjId(), oldBookId, newBookId, boxId, new ArrayList<Long>(doneQIds), new ArrayList<Long>(wrongQIds));
			}catch (Exception e){
				logger.error("cacheWeekQuestion error.uid:"+uid+",userHomeWorkId:"+userHomeWorkId,e);
			}
			//用户提交的时候才计算正确率
			if (userExerciseAnswer.getState() == UserAnswer.State.SUBMITTED) {
				//添加正确率计算,保留三位小数
				DecimalFormat decimalFormat = new DecimalFormat("0.000");
				long rightCount = questionAnswerDetailList.stream().filter(x -> x.getState() == UserAnswerDetail.IsRight.RIGHT).count();
				String accuracy = decimalFormat.format(Double.valueOf(rightCount) / questions.size());
				Map<String, String> map = Maps.newHashMap();
				map.put("accuracy", accuracy);
				userExerciseAnswer.setComment(GsonUtil.toJson(map));
				long doCount = questionAnswerDetailList.size();
				userExerciseAnswer.setAnswerNum(doCount);
				userHomeWorkAnswerDao.updateSharding(userExerciseAnswer);
			}
			syncQuestionLog(questionAnswerDetailList);
		}

		private void addUserSubErrorQuestion(UserAnswerDetail answerDetail, Integer topicQuestionType, UserExerciseAnswer userExerciseAnswer) throws Exception{
			Date nowDate = new Date();
			UserSubErrorQuestion userSubErrorQuestion = new UserSubErrorQuestion();
			userSubErrorQuestion.setUid(uid);
			Long productId = userExerciseAnswer.getProductId();
			userSubErrorQuestion.setProductId(productId);
			if (IdUtils.isValid(productId)) {
				List<Long> prodIdList = Lists.newArrayList();
				prodIdList.add(productId);
				Map<Long, Product> productIdMap = goodsResource.getProductsByIdList(prodIdList);
				if (productIdMap != null && productIdMap.get(productId) != null) {
					userSubErrorQuestion.setProductType(productIdMap.get(productId).getType());
				}
			}
			userSubErrorQuestion.setCategoryId(userExerciseAnswer.getCategoryId());
			userSubErrorQuestion.setGoodsId(userExerciseAnswer.getGoodsId());
			userSubErrorQuestion.setLessonId(userExerciseAnswer.getObjId());
			userSubErrorQuestion.setQuestionId(answerDetail.getQuestionId());
			userSubErrorQuestion.setTopicId(answerDetail.getTopicId());
			userSubErrorQuestion.setAnswerId(answerDetail.getUserHomeworkId());
			userSubErrorQuestion.setLastErrorTime(nowDate);
			userSubErrorQuestion.setLastErrorAnswer(Arrays.toString(answerDetail.getAnswer()));
			userSubErrorQuestion.setQtype(topicQuestionType);
			userSubErrorQuestion.setCreateDate(nowDate);
			userSubErrorQuestion.setSourceType(Consts.UserSubErrorQuestionSourceType.QUESTION_BANK);
			userSubErrorQuestionService.saveSubErrorQuestion(userSubErrorQuestion);
		}

		public List<UserAnswerSum> findByUserHomeworkIdWithDetails(Long uid, Long userHomeWorkId, Long questionId) throws DataAccessException {
	        UserAnswerSum answerSum = new UserAnswerSum();
	        answerSum.setUserHomeworkId(userHomeWorkId);
	        answerSum.setQuestionId(questionId);
			answerSum.setUid(uid);
	        List<UserAnswerSum> answerSums = userAnswerSumDao.findAllList(answerSum);
	        answerSums = setDetails(uid, answerSums);
	        return answerSums;
	    }

		private List<UserAnswerSum> setDetails(Long uid,List<UserAnswerSum> answerSums) throws DataAccessException {
	        int i = 0;
	        for (UserAnswerSum userAnswer : answerSums) {
	            UserAnswerDetail detail = new UserAnswerDetail();
	            detail.setSumId(userAnswer.getId());
				detail.setUid(uid);
	            userAnswer.setAnswerDetail(userAnswerDetailDao.findAllList(detail));
	            answerSums.set(i++, userAnswer);
	        }
	        return answerSums;
	    }

		private void syncQuestionLog(List<QuestionAnswerDetail> questionAnswerDetailList) {
            try {
                questionAnswerStaticsService.recordQuestionAnswerInfoBatch(questionAnswerDetailList);
            } catch (DataAccessException e) {
                logger.error(e.getMessage());
            }
        }
	}

//	@Override
	public void init() {
		logger.info("do UserBoxExerciseSubmitProxy init....");
	}
//	@Override
	public void destroy() {
		logger.info("do UserBoxExerciseSubmitProxy destroy....");
	}
}
