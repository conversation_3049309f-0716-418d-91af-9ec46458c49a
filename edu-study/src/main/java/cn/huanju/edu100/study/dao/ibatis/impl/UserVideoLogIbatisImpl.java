/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.UserVideoLogDao;
import cn.huanju.edu100.study.model.UserVideoLog;
import cn.huanju.edu100.util.IdWorker;
import com.ibatis.sqlmap.client.SqlMapClient;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 记录学生在学习录播课程过程中的信息DAO接口
 *
 * <AUTHOR>
 * @version 2015-05-18
 */
public class UserVideoLogIbatisImpl extends CrudIbatisImpl2<UserVideoLog>
		implements UserVideoLogDao {

	final static IdWorker idworker=new IdWorker(0,4);
	public UserVideoLogIbatisImpl() {
		super("UserVideoLog");
	}

	@Override
	public UserVideoLog query(final Long uid, final Long courseId, final Long clsId, final Long lessonId, final String strDt) throws DataAccessException{
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("uid", uid);
		param.put("courseId", courseId);
		param.put("clsId", clsId);
		param.put("lessonId", lessonId);
		param.put("strDt", strDt);
		try {
			SqlMapClient sqlMap = super.getSlave();
			return (UserVideoLog) sqlMap.queryForObject(
					"UserVideoLog.queryOne", param);
		} catch (SQLException e) {
			logger.error("findOne SQLException.param:{}", param, e);
			throw new DataAccessException("findOne SQLException error");
		}
	}
	@Override
	public Collection<UserVideoLog> queryUserVideoLog(Map<String, Object> param)
			throws DataAccessException {

		try {
			SqlMapClient sqlMap = super.getSlave();
			return (Collection<UserVideoLog>) sqlMap.queryForList(
					"UserVideoLog.findList", param);
		} catch (SQLException e) {
			logger.error("findList SQLException.uid:{}", param, e);
			throw new DataAccessException("findList SQLException error");
		}
	}
	@Override
	public UserVideoLog findOne(Map<String, Object> param)
			throws DataAccessException {

		try {
			SqlMapClient sqlMap = super.getSlave();
			return (UserVideoLog) sqlMap.queryForObject(
					"UserVideoLog.findOne", param);
		} catch (SQLException e) {
			logger.error("findOne SQLException.param:{}", param, e);
			throw new DataAccessException("findOne SQLException error");
		}
	}
	@Override
	public long insertUserVideoLog(UserVideoLog userVideoLog)
			throws DataAccessException {

		try {
			SqlMapClient sqlMap = super.getShardingMaster();
			userVideoLog.setStartTime(new Date());
			if (userVideoLog.getStatus() == null) {
				userVideoLog.setStatus(0);
			}
//			SqlMapClient mainSqlMap = super.getMaster();
//			Long newId =  (Long) mainSqlMap.insert("UserVideoLog.insert", userVideoLog);

			Long newId = idworker.nextId();

			userVideoLog.setId(newId);
			sqlMap.insert("UserVideoLog.insertSharding", userVideoLog);

			return newId;
		} catch (SQLException e) {
			logger.error("insert SQLException.uid:{}", userVideoLog, e);
			throw new DataAccessException("insert SQLException error");
		}
	}

	@Override
	public int updateUserVideoLog(UserVideoLog userVideoLog)
			throws DataAccessException {
		if(null == userVideoLog.getUid()){
			logger.error("updateLastRec SQLException.uid null", userVideoLog);
			return -1;
		}
		try {


			SqlMapClient sqlMap = super.getShardingMaster();
			userVideoLog.setStartTime(new Date());
			if (userVideoLog.getStatus() == null) {
				userVideoLog.setStatus(0);
			}
//			SqlMapClient mainSqlMap = super.getMaster();
//			mainSqlMap.update("UserVideoLog.updateLastRec", userVideoLog);

			return sqlMap.update("UserVideoLog.updateLastRec", userVideoLog);
		} catch (SQLException e) {
			logger.error("updateLastRec SQLException.uid:{}", userVideoLog, e);
			throw new DataAccessException("updateLastRec SQLException error");
		}
	}

	@Override
	public Collection<UserVideoLog> queryByUidCourseId(Long uid, Long courseId)
			throws DataAccessException {
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			param.put("courseId", courseId);
			return (Collection<UserVideoLog>) sqlMap.queryForList(
					"UserVideoLog.queryByUidCourseId", param);
		} catch (SQLException e) {
			logger.error("queryByUidCourseId SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"queryByUidCourseId SQLException error");
		}
	}

	@Override
	public Collection<UserVideoLog> queryByUidCourseIdList(Long uid,
			Collection<Long> courseIds) throws DataAccessException {
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("uid", uid);
			boolean comma = false;
			StringBuffer sb = new StringBuffer();
			for (Long id : courseIds) {
				if (comma) {
					sb.append(",");
				}
				sb.append(id.toString());
				comma = true;
			}
			param.put("courseIds", sb.toString());
			return (Collection<UserVideoLog>) sqlMap.queryForList(
					"UserVideoLog.queryByUidCourseIdList", param);
		} catch (SQLException e) {
			logger.error("queryByUidCourseId SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"queryByUidCourseId SQLException error");
		}
	}

	@Override
	public UserVideoLog getLastUserVideoLog(long uid, long courseId)
			throws DataAccessException {
		try {
			SqlMapClient sqlMap = super.getSlave();
			Map<String, Object> param = new HashMap<String, Object>();

			param.put("uid", uid);
			param.put("courseId", courseId);

			return (UserVideoLog) sqlMap.queryForObject(
					"UserVideoLog.queryLastByUidCourseId", param);

		} catch (SQLException e) {
			logger.error("getLastUserVideoLog SQLException.uid:{}", uid, e);
			throw new DataAccessException(
					"getLastUserVideoLog SQLException error");
		}
	}

}
