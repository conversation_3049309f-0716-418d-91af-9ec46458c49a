package cn.huanju.edu100.study.dao.solution;

import cn.huanju.edu100.persistence.dao.CrudDao;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.study.model.solution.QuestionCollect;
import cn.huanju.edu100.exception.DataAccessException;

import java.util.HashMap;
import java.util.List;

public interface QuestionCollectDao extends CrudDao<QuestionCollect>{
    QuestionCollect findUserCollectByQuestionIdAndUid(HashMap<String, Long> paramsMap) throws DataAccessException;

    Integer findListWithSourceTypeCount(Page<QuestionCollect> page, QuestionCollect questionCollect) throws DataAccessException;

    List<QuestionCollect> findListWithSourceType(Page<QuestionCollect> page, QuestionCollect questionCollect) throws DataAccessException;
}
