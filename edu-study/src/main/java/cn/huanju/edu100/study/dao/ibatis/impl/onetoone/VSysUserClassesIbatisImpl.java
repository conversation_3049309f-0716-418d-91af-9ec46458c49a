/**
 *
 */
package cn.huanju.edu100.study.dao.ibatis.impl.onetoone;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.study.dao.ibatis.impl.CrudIbatisImpl2;
import cn.huanju.edu100.study.dao.onetoone.VSysUserClassesDao;
import cn.huanju.edu100.study.model.onetoone.VSysUserClasses;
import cn.huanju.edu100.util.GsonUtil;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.client.SqlMapClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 班级督导DAO接口
 * <AUTHOR>
 * @version 2016-12-16
 */
public class VSysUserClassesIbatisImpl extends CrudIbatisImpl2<VSysUserClasses> implements
		VSysUserClassesDao {

	public VSysUserClassesIbatisImpl() {
		super("VSysUserClasses");
	}

    @Override
    public List<VSysUserClasses> findListByParam(Map<String, Object> param) throws DataAccessException {

        if (MapUtils.isEmpty(param)) {
            logger.error("findListByParam {} error, param is empty, entity:{}", namespace, GsonUtil.toJson(param));
            throw new DataAccessException(String.format("findListByParam %s error, param is empty, entity:%s",
                    namespace, GsonUtil.toJson(param)));
        }

        try {
            SqlMapClient sqlMap = super.getSlave();
            return sqlMap.queryForList(namespace.concat(".findList"), param);
        } catch (SQLException e) {
            logger.error("findListByParam {} SQLException.entity:{}", namespace, GsonUtil.toJson(param), e);
            throw new DataAccessException("findListByParam SQLException error" + e.getMessage());
        }
    }

    @Override
    public Map<Long, VSysUserClasses> getClsId2VSysUser(List<Long> clsIds) throws DataAccessException {

        if (CollectionUtils.isEmpty(clsIds)) {
            return Collections.emptyMap();
        }

        Map<String, Object> param = Maps.newHashMap();
        param.put("clsIds", clsIds);
        List<VSysUserClasses> userClassesList = findListByParam(param);
        if (CollectionUtils.isNotEmpty(userClassesList)) {
            Map<Long, VSysUserClasses> resultMap = Maps.newHashMap();
            for (VSysUserClasses vSysUserClasses : userClassesList) {
                resultMap.put(vSysUserClasses.getvClsId(), vSysUserClasses);
            }

            return resultMap;
        } else {
            return Collections.emptyMap();
        }
    }

}
