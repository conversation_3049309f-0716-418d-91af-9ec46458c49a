package cn.huanju.edu100.study.model.goods;


import cn.huanju.edu100.persistence.model.DataEntity;

import java.math.BigDecimal;

/**
 * 商品内容Entity
 * <AUTHOR>
 * @version 2015-06-18
 */
public class GoodsContent extends DataEntity<GoodsContent> {
	
	private static final long serialVersionUID = 1L;
	private Long goodsId;		// goods_id
	private Long relateId;		// relate_id
	private Integer relateType;		// 0表示服务，1表示商品，2表示套餐
	private Integer type;		// 1表示必选，0表示多选一
	private Double ratio;
	private Double realCostPrice;		// 商品的成本价格
	private Double defaultRealCostPrice;		// 不包含服务、第三方支付的成本价
	
	public GoodsContent() {
		super();
	}

	public GoodsContent(Long id){
		super(id);
	}

	public Long getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
	
	public Long getRelateId() {
		return relateId;
	}

	public void setRelateId(Long relateId) {
		this.relateId = relateId;
	}
	
	public Integer getRelateType() {
		return relateType;
	}

	public void setRelateType(Integer relateType) {
		this.relateType = relateType;
	}
	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Double getRealCostPrice() {
		return realCostPrice;
	}

	public void setRealCostPrice(Double realCostPrice) {
		this.realCostPrice = realCostPrice;
	}

	public Double getDefaultRealCostPrice() {
		return defaultRealCostPrice;
	}

	public Double fetchDefaultRealCostPrice() {
		if(null == defaultRealCostPrice){
			return 0d;
		}
		return defaultRealCostPrice;
	}

	public void setDefaultRealCostPrice(Double defaultRealCostPrice) {
		this.defaultRealCostPrice = defaultRealCostPrice;
	}

	public Double getRatio() {
		return ratio;
	}

	public void setRatio(Double ratio) {
		this.ratio = ratio;
	}

	public BigDecimal fetchRatio(){
		if(null == ratio){
			return BigDecimal.ZERO;
		}

		return BigDecimal.valueOf(ratio);
	}
}