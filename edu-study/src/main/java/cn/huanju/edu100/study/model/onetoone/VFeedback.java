package cn.huanju.edu100.study.model.onetoone;

import cn.huanju.edu100.persistence.model.DataEntity;

import java.util.Collection;
import java.util.Date;
import java.util.Map;

/**
 * 留学反馈/报告Entity
 *
 * <AUTHOR>
 * @version 2016-12-12
 */
public class VFeedback extends DataEntity<VFeedback> {

    private static final long serialVersionUID = 1L;
    private Long vClsId; // 班级id
    private Long vLessonId; // 课节id
    private Long uid; // 学员uid
    private String title; // 标题
    private Integer type; // 类型 0：老师反馈 1: 课时报告 2：督导报告
    private Long vResId; // 留学资源id
    private String content; // 内容
    private Integer userType; // 0: 老师 1: 督导
    private int isSend;// 是否发送 0：发送 1：待督导检查
    private Collection<Long> uidList;
    private Map<Long, String> uidContentMap;
    private VLesson vLesson;
    private String productName;
    private String vLessonName;
    private String teacherName;
    private String creatorName;

    private VResource vResource;

    private Date lessonStartTime;
    private Date lessonEndTime;

    public VFeedback() {
        super();
    }

    public VFeedback(Long id) {
        super(id);
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Long getvClsId() {
        return vClsId;
    }

    public void setvClsId(Long vClsId) {
        this.vClsId = vClsId;
    }

    public Long getvLessonId() {
        return vLessonId;
    }

    public void setvLessonId(Long vLessonId) {
        this.vLessonId = vLessonId;
    }

    public Long getvResId() {
        return vResId;
    }

    public void setvResId(Long vResId) {
        this.vResId = vResId;
    }

    public VResource getvResource() {
        return vResource;
    }

    public void setvResource(VResource vResource) {
        this.vResource = vResource;
    }

    public Collection<Long> getUidList() {
        return uidList;
    }

    public void setUidList(Collection<Long> uidList) {
        this.uidList = uidList;
    }

    public VLesson getvLesson() {
        return vLesson;
    }

    public void setvLesson(VLesson vLesson) {
        this.vLesson = vLesson;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getvLessonName() {
        return vLessonName;
    }

    public void setvLessonName(String vLessonName) {
        this.vLessonName = vLessonName;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public Map<Long, String> getUidContentMap() {
        return uidContentMap;
    }

    public void setUidContentMap(Map<Long, String> uidContentMap) {
        this.uidContentMap = uidContentMap;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public int getIsSend() {
        return isSend;
    }

    public void setIsSend(int isSend) {
        this.isSend = isSend;
    }

    public Date getLessonStartTime() {
        return lessonStartTime;
    }

    public void setLessonStartTime(Date lessonStartTime) {
        this.lessonStartTime = lessonStartTime;
    }

    public Date getLessonEndTime() {
        return lessonEndTime;
    }

    public void setLessonEndTime(Date lessonEndTime) {
        this.lessonEndTime = lessonEndTime;
    }
}
