package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.exception.DataAccessException;
import cn.huanju.edu100.persistence.model.Page;
import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.study.dao.UserCorrectQuestionDao;
import cn.huanju.edu100.study.dao.UserSubErrorQuestionDao;
import cn.huanju.edu100.study.model.UserCorrectQuestion;
import cn.huanju.edu100.study.model.UserSubErrorQuestion;
import cn.huanju.edu100.study.service.UserCorrectQuestionService;
import cn.huanju.edu100.study.util.IdUtils;
import com.hqwx.study.dto.UserErrorAndCorrectQuestionCountDTO;
import com.hqwx.study.dto.query.UserErrorAndCorrectQuestionQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserCorrectQuestionServiceImpl extends BaseServiceImpl<UserCorrectQuestionDao, UserCorrectQuestion> implements UserCorrectQuestionService {

    @Resource
    private UserSubErrorQuestionDao userSubErrorQuestionDao;
    @Override
    public void saveUserCorrectQuestion(UserCorrectQuestion userCorrectQuestion) throws DataAccessException {
        if(!IdUtils.isValid(userCorrectQuestion.getUid())
            || !IdUtils.isValid(userCorrectQuestion.getQuestionId())
            || !IdUtils.isValid(userCorrectQuestion.getCategoryId())
            || !IdUtils.isValid(userCorrectQuestion.getGoodsId())){
            throw new DataAccessException("saveUserCorrectQuestion error, parameter uid or questionId or taskId is empty");
        }
//        UserCorrectQuestion dbCorrectQuestion = dao.get(userCorrectQuestion);
//        if (dbCorrectQuestion != null) {
//            userCorrectQuestion.setId(dbCorrectQuestion.getId());
//        }
        dao.insertSharding(userCorrectQuestion);
    }

    @Override
    public Integer getUserCorrectQuestionCount(UserErrorAndCorrectQuestionQuery query) throws DataAccessException {
        return (int)dao.groupByCount(query);
    }

    @Override
    public Boolean removeCorrectQuestionByCategory(Map<String, Object> params) throws DataAccessException {
        return dao.removeCorrectQuestionByCategory(params);
    }

    @Override
    public Page<UserCorrectQuestion> findGroypByPage(UserErrorAndCorrectQuestionQuery query) throws DataAccessException {
        Page<UserCorrectQuestion> page = new Page();
        page.setFrom(query.getFrom());
        page.setPageSize(query.getRows());
        page.setOrderBy(" createDate ");
        List<UserCorrectQuestion> results = dao.findGroupByList(page, query);

        if (CollectionUtils.isNotEmpty(results)) {
            //题目id列表
            List<Long> questionIdList = results.stream().map(UserCorrectQuestion::getQuestionId).collect(Collectors.toList());
            //当前条件下，题目的做错次数
            var list = userSubErrorQuestionDao.getUserQuestionErrorTimes(query.getUid(), query.getGoodsId(), query.getCategoryId(), query.getStartTime(), query.getEndTime(), query.getSourceType(), questionIdList);
            if (CollectionUtils.isNotEmpty(list)) {
                Map<Long, Integer> questionErrorTimesMap = list.stream().collect(Collectors.toMap(UserSubErrorQuestion::getQuestionId, UserSubErrorQuestion::getErrorTimes));
                results.forEach(item -> item.setErrorTime(questionErrorTimesMap.getOrDefault(item.getQuestionId(), 0)));
            }
        }

        Page<UserCorrectQuestion> result = new Page<>();
        result.setList(results);
        result.setCount(dao.groupByCount(query));
        result.setPageSize(page.getPageSize());
        result.setPageNo(page.getFrom() / page.getPageSize() + 1);
        return result;
    }

    @Override
    public List<Long> getUserCorrectQuestionIdList(UserErrorAndCorrectQuestionQuery query) throws DataAccessException {
        return dao.getUserCorrectQuestionIdList(query);
    }

    @Override
    public UserErrorAndCorrectQuestionCountDTO countForCategory(UserErrorAndCorrectQuestionQuery param) throws DataAccessException {
        UserErrorAndCorrectQuestionCountDTO result = new UserErrorAndCorrectQuestionCountDTO();
        result.setUid(param.getUid());
        result.setGoodsId(param.getGoodsId());
        result.setType(param.getType());
        result.setCategoryCountList(dao.countForCategory(param));
        return result;
    }
}
