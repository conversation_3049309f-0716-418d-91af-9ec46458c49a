package cn.huanju.edu100.study.service.impl;

import cn.huanju.edu100.persistence.service.impl.BaseServiceImpl;
import cn.huanju.edu100.redis.cluster.client.CompatableRedisClusterClient;
import cn.huanju.edu100.study.dao.UserQuestionErrorCorrectDao;
import cn.huanju.edu100.study.model.UserQuestionErrorCorrect;
import cn.huanju.edu100.study.resource.KnowledgeResource;
import cn.huanju.edu100.study.service.UserQuestionErrorCorrectService;
import cn.huanju.edu100.study.util.RedisConsts;
import cn.huanju.edu100.exception.DataAccessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class UserQuestionErrorCorrectServiceImpl extends BaseServiceImpl<UserQuestionErrorCorrectDao, UserQuestionErrorCorrect> implements
		UserQuestionErrorCorrectService {

	private static Logger logger = LoggerFactory.getLogger(UserQuestionErrorCorrectServiceImpl.class);

	@Autowired
    private CompatableRedisClusterClient compatableRedisClusterClient;

	@Autowired
    private KnowledgeResource knowledgeResource;

	private static int reportMax1Day = 10;

	public interface RetCode {
		/**
		 * 上报成功
		 */
		int OK = 0;
		/**
		 * 上报失败
		 */
		int Fail = 1;
		/**
		 * 超过每天最大上报次数
		 */
		int FailByMax = 2;

	}

	@Override
	public int reportErrorQuestionCorrect(UserQuestionErrorCorrect correct)
			throws DataAccessException {
		Map<String, String> data = new HashMap<String, String>();

		// 查詢用戶本次上報該題目的糾錯次數
		Map<String, String> questionMaps = new HashMap<String, String>();
		questionMaps = compatableRedisClusterClient.hgetAll(RedisConsts.getUserRpErrQuseNumKey(correct.getUid()));

		// 判斷當前上報限制的剩餘的緩存時間
		Long ttl = compatableRedisClusterClient.ttl(RedisConsts.getUserRpErrQuseNumKey(correct.getUid()));
		logger.info("reportErrorQuestionCorrect uid:{},questionId:{},TTL:{}",correct.getUid(),correct.getQuestionId(),ttl);

		if (ttl < 0) { //-2:沒有該Key，-1:沒有設置過期時間，
			data.put(RedisConsts.getUserRpErrQuseNumField(correct.getQuestionId()), "1");
		}else {
			if (questionMaps != null && !questionMaps.isEmpty()) {
				Iterator questionMapsIterator = questionMaps.entrySet().iterator();
				while (questionMapsIterator.hasNext()) {
		            Map.Entry questionEntry = (Map.Entry)questionMapsIterator.next();
		            String qidStr = questionEntry.getKey().toString();
		            String cntStr = questionEntry.getValue().toString();

		            if (qidStr.equals(RedisConsts.getUserRpErrQuseNumField(correct.getQuestionId()))) {//如果今日已經有上報過了
		            	// 判斷是否超過每日的最大上報次數
		            	if (Integer.valueOf(cntStr) >= reportMax1Day) {
							return RetCode.FailByMax;
						}
		            	data.put(qidStr, ""+(Integer.valueOf(cntStr)+1));
					}else {
						data.put(qidStr, cntStr);
					}
		        }
			}else {
				data.put(RedisConsts.getUserRpErrQuseNumField(correct.getQuestionId()), "1");
			}
		}
		if (!data.isEmpty()) {
			// 獲取當前晚上12點的UNIX時間戳
			long epoch = getTimeNight();
			compatableRedisClusterClient.hmset(RedisConsts.getUserRpErrQuseNumKey(correct.getUid()), data);
			// 設置當前緩存為當日的晚上12點
			compatableRedisClusterClient.expireAt(RedisConsts.getUserRpErrQuseNumKey(correct.getUid()), epoch);
			// 上報次數記錄成功，正式寫DB了
			this.dao.insert(correct);
		}
		return RetCode.OK;
	}

	@Override
	public List<UserQuestionErrorCorrect> getUserQuestionErrorCorrect(UserQuestionErrorCorrect userQuestionErrorCorrect) throws DataAccessException {
		return this.dao.findList(userQuestionErrorCorrect);
	}

	private int getTimeNight(){
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 24);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return (int) (cal.getTimeInMillis()/1000);
	}

}
