package cn.huanju.edu100.study.model;

import com.hqwx.study.entity.UserAnswerDetail;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

public class UserAnswerReport {

    private int rightCount;
    private int errorCount;
    private String rightRate;
    private int totalCount;
    private int totalTopicCount;

    public static UserAnswerReport formatReport(List<UserAnswerSum> list) {
        UserAnswerReport userAnswerReport = new UserAnswerReport();
        userAnswerReport.setTotalCount(list.size());

        List<UserAnswerDetail> answerDetails = list.stream().map(UserAnswerSum::getAnswerDetail).flatMap(List::stream).collect(Collectors.toList());
        userAnswerReport.setTotalTopicCount(answerDetails.size());

        int right = 0;
        int error = 0;
        for (UserAnswerSum sum:list) {
            if (sum.getIsRight() == UserAnswerDetail.IsRight.RIGHT) {
                right++;
            } else {
                error++;
            }
        }
        userAnswerReport.setRightCount(right);
        userAnswerReport.setErrorCount(error);

        BigDecimal rightDecimal = new BigDecimal(userAnswerReport.getRightCount());
        BigDecimal totalDecimal = new BigDecimal(userAnswerReport.getTotalCount());
        int rightRate = rightDecimal.divide(totalDecimal,2, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(100)).intValue();

        userAnswerReport.setRightRate(String.valueOf(rightRate));

        return userAnswerReport;
    }


    public int getRightCount() {
        return rightCount;
    }

    public void setRightCount(int rightCount) {
        this.rightCount = rightCount;
    }

    public int getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(int errorCount) {
        this.errorCount = errorCount;
    }

    public String getRightRate() {
        return rightRate;
    }

    public void setRightRate(String rightRate) {
        this.rightRate = rightRate;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getTotalTopicCount() {
        return totalTopicCount;
    }

    public void setTotalTopicCount(int totalTopicCount) {
        this.totalTopicCount = totalTopicCount;
    }
}
